.boq-table .ant-table {
  border: none !important;
}

.boq-table {
  border-radius: 8px;
  overflow: hidden;
  // Table header styling
  .ant-table-thead > tr > th {
    background-color: var(--color-primary);
    color: var(--color-text-selected);
    font-weight: 600;
    border-bottom: 1px solid var(--color-neutral-n3);
    border-inline-end: none !important;

    &::before {
      content: none !important;
    }
  }

  // Table body row styling - alternating colors
  // .ant-table-tbody > tr:nth-child(odd) {
  //   background-color: var(--color-neutral-n1);
  // }

  .ant-table-tbody > tr:nth-child(even):not(.ant-table-placeholder) {
    background-color: var(--color-neutral-n0);
  }

  // Hover state for table rows - chỉ apply cho rows có data
  .ant-table-tbody > tr:not(.ant-table-placeholder):hover > td {
    background-color: var(--color-neutral-n1) !important;
  }

  // Table cell styling
  .ant-table-tbody > tr > td {
    color: var(--color-neutral-n8);
    border-bottom: 1px solid var(--color-neutral-n2);
    border-top: none !important;
    border-left: none !important;
    border-right: none !important;
  }

  // Empty state styling - chỉ apply cho placeholder row
  .ant-table-tbody > tr.ant-table-placeholder > td {
    background-color: white !important;

    &:hover {
      background-color: white !important;
    }
  }

  // Table border styling
  .ant-table {
    margin-block: 0px -1px !important;
    margin-inline: 0px !important;
    border: 1px solid var(--color-neutral-n3);

    .ant-table-container {
      .ant-table-content {
        colgroup {
          .ant-table-expand-icon-col {
            width: 20px;
          }
        }

        .ant-table-thead {
          .ant-table-cell {
            &.ant-table-row-expand-icon-cell {
              // padding: 4px 0px !important;
              // width: 50px !important;
            }
          }
        }

        .ant-table-tbody {
          .ant-table-expanded-row.ant-table-expanded-row-level-1 {
            .ant-table-row:nth-child(even) {
              background-color: var(--table-even-row-2);

              .ant-table-cell-fix-right {
                background-color: var(--table-even-row-2);
              }
            }
          }
          .ant-table-row:nth-child(even) {
            background-color: var(--table-even-row);

            .ant-table-cell-fix-right {
              background-color: var(--table-even-row);
            }
          }
          .ant-table-row:nth-child(odd) {
            background-color: var(--table-even-row-2);

            .ant-table-cell-fix-right {
              background-color: var(--table-even-row-2);
            }
          }
          .ant-table-row {
            &:has(.ant-table-row-expand-icon-cell) {
              &:has(:only-child) {
                // background-color: var(--color-neutral-n2);
              }

              & > .ant-table-cell {
                font-weight: 700;
              }
            }

            .ant-table-row-expand-icon-cell {
              div {
                display: flex;
              }
            }
          }
          .ant-table-expanded-row {
            & > .ant-table-cell {
              padding: 0 !important;
            }
          }
        }
      }
    }
  }

  // Selection column styling (if using row selection)
  .ant-table-selection-column {
    .ant-checkbox-wrapper {
      .ant-checkbox {
        border-color: var(--color-neutral-n4);

        &.ant-checkbox-checked {
          background-color: var(--color-primary);
          border-color: var(--color-primary);
        }
      }
    }
  }

  .ant-btn {
    .ant-btn-icon {
      width: 20px;
      aspect-ratio: 1;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}


.icon-only-button {
  padding: 0 !important; /* Remove all padding */
  margin: 0; /* Remove any margin */
  width: 16px; /* Match icon size */
  height: 16px; /* Match icon size */
  line-height: 1; /* Remove extra line height */
  background: none !important; /* Remove background */
  border: none !important; /* Remove border */
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-only-button .anticon,
.icon-only-button svg {
  font-size: 16px; /* Ensure consistent icon size */
  margin: 0; /* Remove any icon margin */
}

/* Optional: Hover effect to indicate clickability */
.icon-only-button:hover .anticon,
.icon-only-button:hover svg {
  color: #1890ff; /* Ant Design primary color for hover */
}

/* Ensure danger button (delete) has a distinct hover color */
.icon-only-button.ant-btn-dangerous:hover .anticon {
  color: #ff4d4f; /* Ant Design danger color for hover */
}

/* UnitSelector in table styling */
.boq-table .ant-table-cell .ant-select {
  width: 100px;
  min-width: 100px;
}