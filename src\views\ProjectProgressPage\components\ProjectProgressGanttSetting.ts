import { TimelineViewMode } from "@syncfusion/ej2-react-gantt";

export const undoRedoActions: any = [
  "Sorting",
  "Add",
  "ColumnReorder",
  "ColumnResize",
  "ColumnState",
  "Delete",
  "Edit",
  "Filtering",
  "Indent",
  "Outdent",
  "NextTimeSpan",
  "PreviousTimeSpan",
  "RowDragAndDrop",
  "Search",
];

export const toolBarSettings = [
  "ExpandAll",
  "CollapseAll",
  "Add",
  "Edit",
  "Update",
  "Delete",
  "Cancel",
  // {
  //   id: "InsertTask",
  //   prefixIcon: "e-icons e-plus", // Use plus icon for inserting task
  //   text: "Insert Task",
  // },
  "ZoomIn",
  "ZoomOut",
  "ZoomToFit",
  "Indent",
  "Outdent",
  "Undo",
  "Redo",
  "CriticalPath",
  "ExcelExport",
  "CsvExport",
  "PdfExport",
];

export const taskFields = {
  id: "TaskID",
  // bdId: "id",
  name: "TaskName",
  startDate: "StartDate", //Date
  endDate: "EndDate", //Date
  duration: "Duration", //Thời hạn task
  work: "work", //Số giờ thực hiện task
  progress: "Progress",
  dependency: "Predecessor", //Quan hệ với task cha (2FF, 3FS)
  indicators: "Indicators",
  money: "Money",
  notes: "info",
  resourceInfo: "resources", //Danh sách nhân viên phụ trách task
  child: "subtasks", //Các task con
};

export const resourceFields: any = {
  id: "resourceId",
  name: "resourceName",
  // unit: "resourceUnit",
  // group: "resourceGroup",
};

export const timelineSettings = {
  topTier: {
    unit: "Week" as TimelineViewMode,
    format: "dd MMM, y",
  },
  bottomTier: {
    unit: "Day" as TimelineViewMode,
  },
};

export const editSettings = {
  allowAdding: true,
  allowEditing: true,
  allowDeleting: true,
  allowTaskbarEditing: true,
  showDeleteConfirmDialog: true,
};

export const labelSettings = {
  taskLabel: "Progress",
  rightLabel: "TaskName",
};

export const splitterSettings = { position: "45%" };
