import {
  CheckOutlined,
  CloseOutlined,
  DeleteOutlined,
  DownloadOutlined,
  InboxOutlined,
} from "@ant-design/icons";
import {
  Button,
  Checkbox,
  Input,
  message,
  Modal,
  Table,
  Upload,
  UploadProps,
} from "antd";
import TextArea from "antd/es/input/TextArea";
import { UploadFile } from "antd/lib";
import { fileAttachApi } from "api/fileAttach.api";
import { FileAttachQuery } from "hooks/useFileAttach";
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
} from "react";
import { MdOutlineDelete } from "react-icons/md";
import {
  FileAttach,
  FileAttachType,
  FileAttachUpload,
  FileAttachUpload2,
} from "types/fileAttach";
import { $url } from "utils/url";
import "./imageManagement.css";
import { downloadFile } from "utils/downloadFile";
import Column from "antd/es/table/Column";
const { Dragger } = Upload;

interface CreateFolderModalProps {
  onCreateSuccess?: () => void;
  query: FileAttachQuery;
}
interface selectedFolderProps {
  id?: number;
  path: string;
  title: string;
}
export interface UploadImageRef {
  open: (selectedFolder: selectedFolderProps) => void;
  close: () => void;
}
const UploadImageModal = forwardRef(
  ({ onCreateSuccess, query }: CreateFolderModalProps, ref) => {
    const [visible, setVisible] = useState(false);
    const [description, setDescription] = useState<string>("");
    const [selectedFolder, setSelectedFolder] = useState<selectedFolderProps>();
    const [previewImages, setPreviewImages] = useState<FileAttachUpload2[]>([]);
    const [fileList, setFileList] = useState<UploadFile[]>([]);
    const [uploadedFiles, setUploadedFiles] = useState<FileAttachUpload2[]>([]);
    const [succeedUploadedFiles, setSucceedUploadedFiles] = useState<number>(0);
    const [failureUploadedFiles, setFailureUploadedFiles] = useState<
      FileAttachUpload2[]
    >([]);
    const [totalUploadedFiles, setTotalUploadedFiles] = useState<
      FileAttachUpload[]
    >([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [isUploaded, setIsUploaded] = useState<boolean>(false);
    const [isReplaceAll, setIsReplaceAll] = useState<boolean>(false);
    const [uploadedFailedFile, setUploadedFailedFile] = useState<
      FileAttachUpload2[]
    >([]);
    const vietnameseLocale = {
      uploading: "Đang tải lên...",
      removeFile: "Xóa tệp",
      downloadFile: "Tải xuống",
      uploadError: "Lỗi tải lên",
      previewFile: "Xem trước",
    };
    useImperativeHandle(ref, () => ({
      open: (selectedFolder: selectedFolderProps) => {
        setVisible(true);
        setSelectedFolder(selectedFolder);
      },
      close: () => setVisible(false),
    }));
    const handleUploadFiles = async (file: any) => {
      const formData = new FormData();
      formData.append("file", file);
      const dataUploaded = await fileAttachApi.getFileUpload(formData);
      const dataForUpload = dataUploaded.data || {};

      const isDuplicate = uploadedFiles.some(
        (f) => f.originalname === dataForUpload.originalname
      );

      if (isDuplicate) {
        message.warning("File đã tồn tại, không thêm lại", file.originalname);
        return null; // hoặc bạn có thể return existing file tùy logic
      }

      if (dataForUpload.size === 0) {
        console.log("Có vào size = 0 không", dataUploaded);
        setUploadedFailedFile((prev) => [...(prev || []), dataForUpload]);
        setFailureUploadedFiles((prev) => [...(prev || []), dataForUpload]);
      }

      setIsUploaded(false);

      if (isUploaded) {
        setSucceedUploadedFiles(0);
        setFailureUploadedFiles([]);
        setUploadedFiles((prev) => [...(prev || []), dataForUpload]);
      } else {
        setUploadedFiles((prev) => [...(prev || []), dataForUpload]);
      }

      return dataForUpload;
    };
    console.log("Failed uploaded file until now", failureUploadedFiles);
    useEffect(() => {
      if (uploadedFiles && uploadedFiles.length > 0) {
        setSucceedUploadedFiles(0);
      }
    }, [uploadedFiles]);
    const props: UploadProps = {
      name: "file",
      multiple: true,
      fileList,
      customRequest: async (options: any) => {
        const { file, onSuccess, onError } = options;
        try {
          handleUploadFiles(file);
        } catch (error) {
          console.error("Upload failed:", error);
          message.error(`${file.name} thất bại.`);
          onError?.(error);
        }
      },
      onChange(info) {
        setFileList(info.fileList);
      },
      onDrop(e) {
        console.log("Dropped files", e.dataTransfer.files);
      },
      showUploadList: false,
      itemRender: (originNode, file, fileList, actions) => {
        console.log("File and list file", fileList, file);
        console.log("And what is uploaded files", uploadedFiles);
        const isImage = file.type?.startsWith("image/");
        const currentFile = uploadedFiles?.find(
          (f) => f.originalname == file.name
        );

        console.log("What is current file", currentFile);
        return succeedUploadedFiles ? (
          <div className="h-[50px]"></div>
        ) : (
          <div className="flex items-center justify-between gap-2 border p-2 rounded mb-2">
            <div className="flex items-center gap-2 w-full overflow-hidden">
              {isImage ? (
                <img
                  src={$url(currentFile?.path)}
                  alt={file.name}
                  className="w-16 h-16 object-cover object-center rounded"
                />
              ) : (
                <div className="w-16 h-16 flex items-center justify-center bg-gray-100 text-xs text-gray-500 rounded">
                  {file.name.split(".").pop()?.toUpperCase()}
                </div>
              )}
              <span className=" overflow-hidden w-[70%] truncate whitespace-nowrap">
                {file.name}
              </span>
            </div>
            <div className="flex gap-2">
              <Button
                type="link"
                onClick={actions.download}
                className="text-blue-500 text-sm flex justify-center items-center"
              >
                <DownloadOutlined /> Tải file
              </Button>
              <Button onClick={actions.remove} className="text-red-500 text-sm">
                <MdOutlineDelete />
              </Button>
            </div>
          </div>
        );
      },
      onRemove: (file) => {
        // Xóa khỏi previewImages theo tên hoặc URL
        setUploadedFiles((prev) =>
          (prev || []).filter((f) => f.originalname !== file.name)
        );
        return true; // cho phép Upload tự xử lý xóa tiếp
      },
      onDownload(file) {
        const fileForDownload = uploadedFiles?.find(
          (item) => item.originalname == file.originFileObj?.name
        );
        downloadFile(
          $url(fileForDownload?.path),
          fileForDownload?.originalname
        );
      },
    };
    function getFileAttachType(mimetype: string): FileAttachType {
      let type = mimetype.split("/")[0].toUpperCase();
      if (type == "APPLICATION") {
        type = "PDF";
      }
      return Object.values(FileAttachType).includes(type as FileAttachType)
        ? (type as FileAttachType)
        : FileAttachType.Other;
    }
    const handleClose = () => {
      setVisible(false);
      setFileList([]);
      setDescription("");
      setUploadedFiles([]);
      setSucceedUploadedFiles(0);
      setFailureUploadedFiles([]);
      setIsUploaded(false);
      setIsReplaceAll(false);
      setUploadedFailedFile([]);
    };
    console.log(
      "Uploaded files & uploaded fail file",
      uploadedFiles,
      uploadedFailedFile
    );
    const handleOkModal = async () => {
      try {
        setLoading(true);
        const requests = uploadedFiles?.map((file) => {
          const type = getFileAttachType(file.mimetype);
          const fileAttach: Partial<FileAttach> = {
            name: file.originalname,
            type: type,
            path: selectedFolder?.path?.endsWith("/")
              ? selectedFolder?.title!
              : `/${selectedFolder?.title!}`,
            size: file.size,
            url: file.path, // hoặc file.url nếu bạn lưu như vậy
            desc: description,
          };
          return fileAttach;
        });
        const handleMany = await fileAttachApi.createMany({
          fileAttaches: requests,
          isReplace: isReplaceAll,
        });
        console.log("sau khi tải nhiều ảnh lên", handleMany);
        const numberOfFailureFile = handleMany.data?.filter(
          (item: FileAttach) => item.isError == true
        );
        const numberOfSuccess = handleMany.data?.filter(
          (item: FileAttach) => item.isError == false
        );

        if (handleMany.status) {
          setSucceedUploadedFiles(numberOfSuccess?.length);
          setFailureUploadedFiles(numberOfFailureFile);
          setTotalUploadedFiles(handleMany.data);
          // setUploadedFiles([]);
        }

        console.log(
          "Số thành công và thất bại",
          numberOfFailureFile?.length,
          numberOfSuccess?.length
        );
        // if (requests) {
        //   await Promise.all(handleMany); // đợi tất cả API chạy song song
        // }
        query.path = selectedFolder?.title?.includes("/")
          ? selectedFolder?.title
          : `/${selectedFolder?.title}`;
        // handleClose();
        if (
          !(numberOfSuccess?.length === 0 && numberOfFailureFile?.length > 0)
        ) {
          message.success("Tệp tin đã được tải lên");
        }
        setIsUploaded(true);
        onCreateSuccess?.();
      } catch (error) {
        console.log(error);
      } finally {
        // handleClose();
        // setSucceedUploadedFiles(0);
        // alert("không đóng nữa");
        setFileList([]);
        setUploadedFiles([]);
        setDescription("");
        setLoading(false);
        setIsReplaceAll(false);
        setFailureUploadedFiles([]);
        // setSucceedUploadedFiles(0);
      }
    };
    const handleFindFailedFileInUploadedFile = (name: string) => {
      const failedFile = uploadedFiles.find(
        (item) => item.originalname === name && item.size === 0
      );
      return !!failedFile;
    };
    // console.log("Data for upload", uploadedFiles);
    return (
      <Modal
        width={800}
        open={visible}
        okButtonProps={{
          disabled:
            (uploadedFiles?.length ?? 0) === 0 ||
            (uploadedFailedFile?.length ?? 0) > 0,
          loading: loading,
        }}
        title="Tải tập tin lên"
        onOk={() => {
          handleOkModal();
        }}
        onCancel={handleClose}
      >
        <div className="flex flex-col gap-5">
          <div className="hidden">
            <label htmlFor="">Mô tả</label>
            <Input
              value={description}
              onChange={(e) => {
                setDescription(e.target.value);
              }}
            />
          </div>
          <Dragger {...props} locale={vietnameseLocale}>
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">
              Nhấn hoặc kéo thả tệp vào khu vực này để tải lên
            </p>
            <p className="ant-upload-hint">
              Hỗ trợ tải lên một hoặc nhiều tệp. Nghiêm cấm tải lên dữ liệu công
              ty hoặc các tệp bị cấm.
            </p>
          </Dragger>
        </div>
        <div
          className={`mt-3 flex flex-col gap-2 px-[8px] ${
            uploadedFiles?.length > 0 ? "block" : "hidden"
          }`}
        >
          <Checkbox
            checked={isReplaceAll}
            onChange={(e) => setIsReplaceAll(e.target.checked)}
          >
            Áp dụng thay thế cho tất cả tệp
          </Checkbox>
        </div>
        <div
          className={`px-[8px] ${
            uploadedFailedFile?.length > 0 ? "block" : "hidden"
          }`}
        >
          Số lượng tệp lỗi không thể tải lên được được:{" "}
          {uploadedFailedFile?.length}
        </div>
        <div className="mt-3 flex flex-col gap-2">
          {uploadedFiles.map((file) => {
            const isImage = file.mimetype?.startsWith("image/");
            return (
              <div
                key={file.originalname}
                className="flex items-center justify-between gap-2 border p-2 rounded"
              >
                <div
                  className={`flex items-center gap-2 w-full overflow-hidden`}
                >
                  {isImage ? (
                    <img
                      src={$url(file.path)}
                      alt={file.originalname}
                      className="w-16 h-16 object-cover object-center rounded"
                    />
                  ) : (
                    <div className="w-16 h-16 flex items-center justify-center bg-gray-100 text-xs text-gray-500 rounded">
                      {file.originalname.split(".").pop()?.toUpperCase()}
                    </div>
                  )}
                  <span className={`truncate w-[70%]`}>
                    {file.originalname}
                  </span>
                  <span>
                    {handleFindFailedFileInUploadedFile(file.originalname) ? (
                      <CloseOutlined className="text-red-400" />
                    ) : (
                      <CheckOutlined className="text-green-400" />
                    )}
                  </span>
                </div>
                <div className="flex gap-2">
                  <Button
                    type="link"
                    className="text-blue-500 text-sm flex items-center"
                    onClick={() =>
                      downloadFile($url(file.path), file.originalname)
                    }
                  >
                    <DownloadOutlined /> Tải file
                  </Button>
                  <Button
                    className="text-red-500 text-sm"
                    onClick={() => {
                      console.log("file hiện tại là gì", file);
                      setUploadedFiles((prev) =>
                        prev.filter((f) => f.originalname !== file.originalname)
                      );
                      setUploadedFailedFile((prev) =>
                        prev.filter((f) => f.originalname !== file.originalname)
                      );
                    }}
                  >
                    <MdOutlineDelete />
                  </Button>
                </div>
              </div>
            );
          })}
        </div>
        {uploadedFiles.length == 0 && isUploaded ? (
          <div className="mt-4 text-sm font-medium">
            <div className="text-blue-500">
              Tổng số tệp tin đã tải lên: {totalUploadedFiles?.length || 0}
            </div>
          </div>
        ) : (
          ""
        )}
        {uploadedFiles.length == 0 && isUploaded ? (
          <div className="text-sm font-medium">
            <div className="text-green-500">
              Số tệp tin đã tải lên thành công: {succeedUploadedFiles || 0}
            </div>
          </div>
        ) : (
          ""
        )}
        {uploadedFiles.length == 0 && isUploaded ? (
          <div className="font-medium">
            <div className="text-red-500">
              Số tệp tin đã tải lên thất bại: {failureUploadedFiles?.length}
            </div>
            <>
              <div className="">Danh sách tệp tin thất bại</div>
              <div>
                <Table
                  dataSource={failureUploadedFiles}
                  scroll={{ x: "max-content" }}
                >
                  <Column title="Tên file" dataIndex="name" key="name" />
                  <Column title="Lỗi" dataIndex="message" key="message" />
                </Table>
              </div>
            </>
          </div>
        ) : (
          ""
        )}
      </Modal>
    );
  }
);

export default UploadImageModal;
