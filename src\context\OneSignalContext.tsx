import { createContext, useContext, useRef, useState } from "react";
import ROneSignal from "react-onesignal";
import { getToken } from "../utils/auth";
import { Button, Space, notification } from "antd";
import { oneSignalApi } from "api/oneSignal";
import { OneSignal } from "utils/oneSignal";
import { OneSignalType } from "types/onesignal";

interface IOneSignalContext {
  isInitialized: boolean;
  isSubscribed: boolean;
  loading: boolean;
  isBlocked: boolean;
  subscribe?: (sub: boolean, notification?: boolean) => void;
  runOneSignal?: () => void;
  setNotificationLoading?: (notification: boolean) => void;
  getListCallback?: () => Function[];
  addListCallback?: (cb: Function) => number;
  removeListCallback?: (index: number | undefined) => void;
}

interface Props {
  children: React.ReactNode;
}
const OneSignalContext = createContext<IOneSignalContext>({
  isInitialized: false,
  isSubscribed: false,
  loading: false,
  isBlocked: false,
});

export const useOneSignalContext = () => useContext(OneSignalContext);

const OneSignalProvider = ({ children }: Props) => {
  const [isInit, setIsInit] = useState<boolean>(false);
  const [isSubscribed, setIsSubscribed] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [isBlocked, setIsBlocked] = useState<boolean>(false);

  const listCallback = useRef<Function[]>([]);

  const addListCallback = (cb: Function) => {
    const index = listCallback.current.push(cb);
    return index - 1;
  };

  const getListCallback = () => {
    return listCallback.current;
  };

  const removeListCallback = (index: number | undefined) => {
    if (index !== undefined) listCallback.current.splice(index, 1);
  };

  const runOneSignal = () => {
    if (!isInit) {
      OneSignal.init(() => {
        ROneSignal.on("subscriptionChange", () => {
          const token = getToken();
          if (!token) return;
          OneSignal.addEventSubChange(async (isSub) => {
            // const oneSignalId = localStorage.getItem("oneSignalId");
            try {
              setLoading(true);
              const oneSignalId = await ROneSignal.getUserId();
              if (isSub) {
                await oneSignalApi.sub({
                  oneSignalId: oneSignalId,
                });
              } else {
                await oneSignalApi.unSub({ oneSignalId });
              }
              setIsBlocked(false);
              setIsSubscribed(isSub);
              OneSignal.getId();
            } catch (error) {
            } finally {
              setLoading(false);
            }
          });
        });
        ROneSignal.getSubscription(async (sub) => {
          try {
            await OneSignal.notifyPermission(false);
            if (sub) {
              const oneSignalId = await ROneSignal.getUserId();
              await oneSignalApi.sub({
                oneSignalId: oneSignalId,
              });
            }
            setIsSubscribed(sub);
          } catch (error) {
            setIsBlocked(true);
          }
        });
        ROneSignal.on(
          "notificationPermissionChange",
          async function (permissionChange) {
            const currentPermission = permissionChange.to;

            //Gọi api xóa userId nếu reset quyền thông báo hoặc tắt quyền
            if (currentPermission == "granted") {
              window.location.reload();
            }
          }
        );
        // addListCallback(() =>
        //   notification.info({
        //     message: "Đã tạo xong mã",
        //     btn: (
        //       <Space>
        //         <Button
        //           type="primary"
        //           onClick={() => (window.location.href = "/bep/orderFood")}
        //         >
        //           Đến trang đơn nước
        //         </Button>
        //       </Space>
        //     ),
        //   })
        // );

        OneSignal.notificationDisplay((event) => {
          console.log("onesignal event: ", event);
          const id = (event?.heading as string).slice(
            (event?.heading as string).indexOf("#")
          );
          if (event?.data?.type === OneSignalType.EndQrCode) {
            notification.info({
              message: (
                <>
                  ({id}) Đã tạo xong mã QR.
                  <br />
                  Vui lòng làm mới trang lịch sử tạo QR Code.
                </>
              ),
              btn: (
                <Space>
                  <Button
                    type="primary"
                    onClick={() =>
                      (window.location.href = "/admin/qr-code/history")
                    }
                  >
                    Đến trang Lịch sử tạo QR
                  </Button>
                </Space>
              ),
            });
          } else if (event?.data?.type === OneSignalType.StartQrCode) {
            notification.info({
              message: (
                <>
                  ({id}) Hệ thống đang tạo mã.
                  <br />
                  Bạn có thể kiểm tra tại trang Lịch sử tạo QR Code.
                </>
              ),
              btn: (
                <Space>
                  <Button
                    type="primary"
                    onClick={() =>
                      (window.location.href = "/admin/qr-code/history")
                    }
                  >
                    Đến trang Lịch sử tạo QR
                  </Button>
                </Space>
              ),
            });
          } else {
            notification.info({
              message: `${event?.heading}`,
            });
          }
        });
        setIsInit(true);
      });
    } else {
      //Case user logout rồi login lại
      ROneSignal.getSubscription(async (sub) => {
        try {
          const oneSignalId = await ROneSignal.getUserId();
          if (sub) {
            await oneSignalApi.sub({
              oneSignalId: oneSignalId,
            });
          }
          await OneSignal.notifyPermission(false);
          setIsSubscribed(sub);
        } catch (error) {
          setIsBlocked(true);
        }
      });
    }
  };

  return (
    <OneSignalContext.Provider
      value={{
        isInitialized: isInit,
        runOneSignal,
        isSubscribed,
        loading,
        isBlocked,
        setNotificationLoading: setLoading,
        getListCallback,
        addListCallback,
        removeListCallback,
      }}
    >
      {children}
    </OneSignalContext.Provider>
  );
};

export default OneSignalProvider;
