import { companyApi } from "api/company.api";
import { useMemo, useState } from "react";
import { Company } from "types/company";
import { QueryParam } from "types/query";

export interface CompanyQuery extends QueryParam { }

interface UseCompanyProps {
  initQuery: CompanyQuery;
}

export const useCompany = ({ initQuery }: UseCompanyProps) => {
  const [data, setData] = useState<Company[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<CompanyQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const isEmptyQuery = useMemo(
    () =>
      Object.keys(query).filter(
        (k) =>
          (query[k] != undefined || query[k] != null) &&
          !["limit", "page", "queryObject", "type"].includes(k)
      ).length == 0,
    [query]
  );


  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await companyApi.findAll(query);

      setData(data.companies);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return { companies: data, total, fetchData, loading, setQuery, query, isEmptyQuery };
};
