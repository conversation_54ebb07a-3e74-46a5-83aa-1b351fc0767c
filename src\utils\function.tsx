import { Modal } from "antd";
import CustomButton from "components/Button/CustomButton";

// Hoi truoc khi import excel
export const handleConfirmImportExcel = async (props?: {
  modalContent?: React.ReactNode;
}): Promise<void> => {
  const { modalContent } = props || {};
  return new Promise((resolve, reject) => {
    Modal.confirm({
      title: "Xác nhận nhập excel dữ liệu",
      getContainer: () => {
        return document.getElementById("App") as HTMLElement;
      },
      icon: null,
      content: modalContent || (
        <>
          <p>Bạn có chắc chắn muốn nhập dữ liệu này không?</p>
        </>
      ),
      footer: (_, { OkBtn, CancelBtn }) => (
        <>
          <CustomButton
            variant="outline"
            className="cta-button"
            onClick={() => {
              reject();
              Modal.destroyAll();
            }}
          >
            Không
          </CustomButton>
          <CustomButton
            onClick={() => {
              resolve();
              Modal.destroyAll();
            }}
            className="cta-button"
          >
            Có
          </CustomButton>
        </>
      ),
    });
  });
};
