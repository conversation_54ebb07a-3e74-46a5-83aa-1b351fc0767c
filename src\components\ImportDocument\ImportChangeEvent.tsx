import {
  DownloadOutlined,
  InboxOutlined,
} from "@ant-design/icons";
import {
  Alert,
  Modal,
  Space,
  Spin,
  Table,
  Upload,
  message,
} from "antd";
import { Rule } from "antd/es/form";
import { serviceApi } from "api/service.api";
import { readerData } from "utils/excel2";
import CustomButton from "components/Button/CustomButton";
import { forwardRef, useImperativeHandle, useState, useEffect } from "react";
import { ChangeEvent } from "types/changeEvent";

const { Dragger } = Upload;

const rules: Rule[] = [{ required: true }];

export interface ImportChangeEventModal {
  open: () => void;
  close: () => void;
}

export interface IValidate {
  index: number;
  message: string;
}

interface DataImportReturn {
  msg: string;
  rowNum: number;
  status: string;
}

export interface ChangeEventImport extends ChangeEvent {
  rowNum: number;
}

interface IProps {
  onSuccess?: () => void;
  createApi?: (data: any) => any;
  onUploaded?: (excelData: any, setData: (data: any[]) => void) => void;
  demoExcel?: string;
  guide?: React.ReactNode[];
  uploadText?: string;
  okText?: string;
  onClose?: () => void;
  titleText?: string;
  validateMessage?: IValidate[];
  onDownloadDemoExcel?: () => void;
  loadingDownloadDemo?: boolean;
}

const ImportChangeEvent = forwardRef((props: IProps, ref) => {
  const {
    onSuccess,
    createApi,
    onUploaded,
    onClose,
    validateMessage,
    guide,
    demoExcel,
    uploadText = "Kéo thả hoặc click vào đây để upload file",
    okText = "Nhập dữ liệu ngay",
    titleText = "Nhập excel dữ liệu",
    onDownloadDemoExcel,
    loadingDownloadDemo,
  } = props;

  const [visible, setVisible] = useState(false);
  const [dataPosts, setDataPosts] = useState<ChangeEventImport[]>([]);
  const [dataReturn, setDataReturn] = useState<{
    data: DataImportReturn[];
    successCount: number;
    errorCount: number;
  }>();
  const [loading, setLoading] = useState(false);

  useImperativeHandle(ref, () => ({
    open: () => setVisible(true),
    close: () => setVisible(false),
  }));

  useEffect(() => {
    if (validateMessage?.length) {
      setDataReturn(undefined);
    }
  }, [validateMessage]);

  const handleImport = async () => {
    if (!dataPosts.length) return;

    try {
      setLoading(true);
      const { data } = await serviceApi.import({ services: dataPosts });
      const successCount = data.filter((d: any) => d.status === "ok").length;
      const errorCount = data.filter((d: any) => d.status === "error").length;

      setDataReturn({ data, successCount, errorCount });
      setDataPosts([]);
      onSuccess?.();
    } catch (err) {
      console.error("Import error", err);
      message.error("Đã xảy ra lỗi trong quá trình nhập dữ liệu");
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = async (file: File) => {
    if (!file.name.includes("xlsx")) {
      message.error("Chỉ chấp nhận file Excel (.xlsx)");
      return Upload.LIST_IGNORE;
    }

    const excelData = await readerData(file, 0);
    setDataReturn(undefined);
    onUploaded?.(excelData, setDataPosts);
    return false;
  };

  const renderUploadBlock = () => (
    <Dragger
      style={{ marginTop: "0.5em" }}
      maxCount={1}
      multiple={false}
      beforeUpload={handleFileUpload}
      onChange={(info) => {
        if (info.fileList.length === 0) {
          setDataPosts([]);
          setDataReturn(undefined);
        }
      }}
    >
      <p className="ant-upload-drag-icon">
        <InboxOutlined />
      </p>
      <p className="ant-upload-text">{uploadText}</p>
    </Dragger>
  );

  const renderAlert = () => {
    if (!dataReturn) return null;

    const errors = dataReturn.data.filter(d => d.status === "error");

    return (
      <Alert
        className="p-3 mt-2"
        type="warning"
        description={
          <div>
            <div className="text-blue-600 font-bold">Tổng dòng nhập: {dataReturn.data.length}</div>
            <div className="text-green-500">Tổng dòng thành công: {dataReturn.successCount}</div>
            <div className="text-red-500">Tổng dòng thất bại: {dataReturn.errorCount}</div>
            <div className="font-bold mt-2">Danh sách dòng thất bại</div>
            <Table
              className="mt-1"
              size="small"
              columns={[
                { title: "Dòng", dataIndex: "rowNum" },
                { title: "Lỗi", dataIndex: "msg" },
              ]}
              dataSource={errors}
              pagination={false}
              rowKey="rowNum"
            />
          </div>
        }
      />
    );
  };

  const handleClose = () => {
    setVisible(false);
    onClose?.();
    setDataPosts([]);
    setDataReturn(undefined);
  };

  return (
    <Modal
      open={visible}
      title={titleText}
      maskClosable={false}
      width={1000}
      destroyOnClose
      onCancel={handleClose}
      afterClose={handleClose}
      footer={[
        <CustomButton
          key="import"
          variant="primary"
          loading={loading}
          disabled={!dataPosts.length}
          onClick={handleImport}
        >
          {okText}
        </CustomButton>,
        <CustomButton
          key="close"
          variant="outline"
          onClick={handleClose}
        >
          Đóng
        </CustomButton>,
      ]}
    >
      <Spin spinning={false}>
        {guide && (
          <Alert
            className="mb-3"
            type="warning"
            message={<strong>Lưu ý</strong>}
            description={
              <ul className="list-disc pl-4">
                {guide.map((item, idx) => (
                  <li key={idx}>{item}</li>
                ))}
              </ul>
            }
          />
        )}

        {(demoExcel || onDownloadDemoExcel) && (
          <Space className="mb-2 cursor-pointer" onClick={onDownloadDemoExcel}>
            <DownloadOutlined />
            {loadingDownloadDemo ? <Spin size="small" /> : "Tải file import mẫu"}
          </Space>
        )}

        {renderUploadBlock()}
        {renderAlert()}
      </Spin>
    </Modal>
  );
});

export default ImportChangeEvent;
