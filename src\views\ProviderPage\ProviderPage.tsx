import {
  DeleteOutlined,
  EditOutlined,
  ImportOutlined,
  LockOutlined,
  PlusOutlined,
  SearchOutlined,
  UnlockOutlined,
} from "@ant-design/icons";
import {
  Button,
  Card,
  Divider,
  Image,
  Input,
  message,
  Modal,
  Popconfirm,
  Select,
  Space,
  Spin,
  Table,
  Tag,
  Tooltip,
} from "antd";
import { providerApi } from "api/provider.api";
import { Pagination } from "components/Pagination";
import React, { useEffect, useState, useRef, useMemo } from "react";
import { ModalStatus } from "types/modal";
import { QueryParam } from "types/query";
import { Provider, ProviderModule, ProviderTypeTrans } from "types/provider";
import { getTitle } from "utils";
import { $url } from "utils/url";
import { useProvider } from "hooks/useProvider";
import { Link, useNavigate } from "react-router-dom";
import { unixToDate } from "utils/dateFormat";
import {
  checkRole,
  checkRoles,
  filterActionColumnIfNoPermission,
} from "utils/auth";
import { PermissionNames } from "types/PermissionNames";
import { permissionStore } from "store/permissionStore";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import PencilIcon from "assets/svgs/PencilIcon";
import DeleteIcon from "assets/svgs/DeleteIcon";
import CustomButton from "components/Button/CustomButton";
import PageTitle from "components/PageTitle/PageTitle";
import CustomInput from "components/Input/CustomInput";
import { handleExport, MyExcelColumn } from "utils/MyExcel";
import { CountryTrans, CurrencyTrans } from "types/address";
import ImportProvider, {
  ImportProviderModal,
} from "components/ImportDocument/ImportProvider";
import { removeSubstringFromKeys } from "utils/common";
import { getListByKey } from "utils/data";
import { getListNameByTypeDictionary } from "hooks/useDictionary";
import { DictionaryType } from "types/dictionary";
import { exportTemplateWithValidation } from "utils/TemplateExcel";
import logoImage from "assets/images/logo.png";
import { DictionarySelector } from "components/Selector/DictionarySelector";
import { TableProps } from "antd/lib";
import { BMDImage } from "components/Image/BMDImage";
import { excelDateToDayjs } from "utils/date";
import { settings } from "settings";
import dayjs from "dayjs";
import { observer } from "mobx-react";
import LockButton from "components/Button/LockButton";
import EditButton from "components/Button/EditButton";

const { ColumnGroup, Column } = Table;

export const ProviderPage = observer(
  ({ title = "", module = ProviderModule.Supplier }) => {
    const isProvider = module === ProviderModule.Supplier; // la nha cung cap

    const {
      haveAddPermission,
      haveBlockPermission,
      haveEditPermission,
      haveViewAllPermission,
    } = checkRoles(
      {
        add: isProvider
          ? PermissionNames.providerAdd
          : PermissionNames.subcontractorAdd,
        edit: isProvider
          ? PermissionNames.providerEdit
          : PermissionNames.subcontractorEdit,
        block: isProvider
          ? PermissionNames.providerBlock
          : PermissionNames.subcontractorBlock,
        viewAll: isProvider
          ? PermissionNames.providerViewAll
          : PermissionNames.subcontractorViewAll,
      },
      permissionStore.permissions
    );

    const importModal = useRef<ImportProviderModal>();

    const [loadingDownloadDemo, setLoadingDownloadDemo] = useState(false);

    const exportColumnsSupplier: MyExcelColumn<Provider>[] = [
      {
        header: "Nhóm tài khoản",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "accountGroup",
        columnKey: "accountGroup",
        render: (record: Provider) => record.accountGroup?.name,
      },
      {
        header: "Nhóm sản phẩm",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "materialGroups",
        columnKey: "materialGroups",
        render: (record: Provider) =>
          record.materialGroups?.map((it) => it.name)?.join(", "),
      },
      {
        header: "Mã vùng",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "areaCode",
        columnKey: "areaCode",
        render: (record: Provider) => record.areaCode,
      },
    ];

    const exportColumnsSubContractor: MyExcelColumn<Provider>[] = [
      {
        header: "Lĩnh vực chuyên môn",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "specialization",
        columnKey: "specialization",
      },
      {
        header: "Số lượng nhân sự",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "staffCount",
        columnKey: "staffCount",
      },
      {
        header: "Thời gian hoàn thành trung bình",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "averageCompletionTime",
        columnKey: "averageCompletionTime",
      },
      {
        header: "Ngay bắt đầu hợp tác",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "startDate",
        columnKey: "startDate",
        render: (record) =>
          record.startDate
            ? dayjs(record.startDate).format(settings.dateFormat)
            : "",
      },
      {
        header: "Loại công tác",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "workTypes",
        columnKey: "workTypes",
        render: (record: Provider) =>
          record.workTypes?.map((it) => it.name)?.join(", "),
      },
      {
        header: "Điện thoại 1",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "phone1",
        columnKey: "phone1",
      },
      {
        header: "Điện thoại 2",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "phone2",
        columnKey: "phone2",
      },
      {
        header: "Email",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "email",
        columnKey: "email",
      },
      {
        header: "Fax",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "fax",
        columnKey: "fax",
      },
    ];

    const exportColumns: MyExcelColumn<Provider>[] = [
      {
        header: isProvider ? "Mã nhà cung cấp" : "Mã thầu phụ",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "code",
        columnKey: "code",
      },
      {
        header: isProvider ? "Tên nhà cung cấp" : "Tên thầu phụ",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "name",
        columnKey: "name",
      },
      {
        header: isProvider ? "Loại nhà cung cấp" : "Loại thầu phụ",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "type",
        columnKey: "type",
        render: (record: Provider) => record.providerCategory?.name,
      },
      {
        header: "Tên khác",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "aliasName",
        columnKey: "aliasName",
        render: (record: Provider) => record.aliasName,
      },
      ...(isProvider ? exportColumnsSupplier : exportColumnsSubContractor),
      {
        header: "Website",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "website",
        columnKey: "website",
        render: (record: Provider) => record.website,
      },
      {
        header: "Quốc gia",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "country",
        columnKey: "country",
        render: (record: Provider) => record.country?.name,
      },
      {
        header: "Xã",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "ward",
        columnKey: "ward",
        render: (record: Provider) => record.ward?.name,
      },
      {
        header: "Huyện",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "district",
        columnKey: "district",
        render: (record: Provider) => record.district?.name,
      },
      {
        header: "Thành phố",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "city",
        columnKey: "city",
        render: (record: Provider) => record.city?.name,
      },
      {
        header: "Địa chỉ",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "address",
        columnKey: "address",
        render: (record: Provider) => record.address,
      },
      {
        header: "Loại tiền tệ",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "currency",
        columnKey: "currency",
        render: (record: Provider) => CurrencyTrans[record.currency]?.label,
      },
      {
        header: "Mã số thuế",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "taxNumber",
        columnKey: "taxNumber",
        render: (record: Provider) => record.taxNumber,
      },
      {
        header: "Tên ngân hàng 1",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "bankName1",
        columnKey: "bankName1",
        render: (record: Provider) => record.bankName1,
      },
      {
        header: "Số tài khoản 1",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "bankAccount1",
        columnKey: "bankAccount1",
        render: (record: Provider) => record.bankAccount1,
      },
      {
        header: "Tên ngân hàng 2",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "bankName2",
        columnKey: "bankName2",
        render: (record: Provider) => record.bankName2,
      },
      {
        header: "Số tài khoản 2",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "bankAccount2",
        columnKey: "bankAccount2",
        render: (record: Provider) => record.bankAccount2,
      },
      {
        header: "Danh xưng",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "salutation",
        columnKey: "salutation",
        render: (record: Provider) => record.salutation,
      },
      {
        header: "Tên người liên hệ",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "contactName",
        columnKey: "contactName",
        render: (record: Provider) => record.contactName,
      },
      {
        header: "Điện thoại",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "contactPhone",
        columnKey: "contactPhone",
        render: (record: Provider) => record.contactPhone,
      },
      {
        header: "Điện thoại di động",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "contactMobile",
        columnKey: "contactMobile",
        render: (record: Provider) => record.contactMobile,
      },
      {
        header: "Email",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "contactEmail",
        columnKey: "contactEmail",
        render: (record: Provider) => record.contactEmail,
      },
      {
        header: "Địa chỉ",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "contactAddress",
        columnKey: "contactAddress",
        render: (record: Provider) => record.contactAddress,
      },
      {
        header: "Trạng thái",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "isActive",
        columnKey: "isActive",
        render: (record: Provider) =>
          record.isActive ? "Hoạt động" : "Bị khóa",
      },
    ];

    const handleTableChange: TableProps<any>["onChange"] = (
      pagination,
      filters,
      sorter
    ) => {
      if (!Array.isArray(sorter)) {
        const fieldMap: Record<string, string> = {
          providerCategory: "providerCategory.name",
          code: "provider.code",
          address: "provider.address",
          createdAt: "provider.createdAt",
          name: "provider.name",
          aliasName: "provider.aliasName",
          email: "provider.email",
          website: "provider.website",
          phone: "provider.phone1",
        };
        const columnKey = sorter.field || sorter.column?.key;

        if (!sorter.order) {
          // setSortField(null);
          // setSortOrder(null);
          query.queryObject = undefined;
          setQuery({ ...query });
        } else {
          const order = sorter.order === "ascend" ? "ASC" : "DESC";
          // setSortField("jobCategory.name");
          // setSortOrder(order);
          const field = fieldMap[columnKey as string];

          const newQueryObject = JSON.stringify([
            {
              type: "sort",
              field,
              value: order,
            },
          ]);
          query.queryObject = newQueryObject;
          setQuery({ ...query });
        }
        fetchData();
      } else {
        query.queryObject = undefined;
        setQuery({ ...query });
        fetchData();
      }
    };

    const [loadingDelete, setLoadingDelete] = useState(false);
    const {
      providers,
      fetchData,
      loading,
      query,
      setQuery,
      total,
      isEmptyQuery,
    } = useProvider({
      initQuery: {
        limit: 10,
        page: 1,
        module,
        isAdmin: haveViewAllPermission ? true : undefined,
      },
    });
    const navigate = useNavigate();
    useEffect(() => {
      document.title = getTitle(title);
      fetchData();
    }, []);

    const handleDeleteProvider = async (id: number) => {
      try {
        setLoadingDelete(false);
        await providerApi.delete(id);
        message.success("Xóa thành công");
        fetchData();
      } catch (error) {
      } finally {
        setLoadingDelete(true);
      }
    };

    const handleActiveProvider = async (id: number, value: boolean) => {
      try {
        setLoadingDelete(false);
        await providerApi.update(id, { provider: { isActive: !value } });
        message.success(value ? "Khóa thành công" : "Mở khóa thành công");
        fetchData();
      } catch (error) {
      } finally {
        setLoadingDelete(true);
      }
    };

    const handleRowClick = (record: Provider) => {
      if (isProvider) {
        navigate(
          `/master-data/${PermissionNames.providerEdit.replace(
            ":id",
            record!.id + ""
          )}`
        );
      } else {
        navigate(
          `/master-data/${PermissionNames.subcontractorEdit.replace(
            ":id",
            record!.id + ""
          )}`
        );
      }
    };

    const columns: CustomizableColumn<Provider>[] = [
      {
        key: "code",
        title: isProvider ? "Mã" : "Mã",
        dataIndex: "code",
        width: isProvider ? 100 : 120,
        // align: "right",
        defaultVisible: true,
        alwaysVisible: true,
        sorter: true,
        render: (_, record) => {
          return (
            <div
              className="text-[#1677ff] cursor-pointer"
              onClick={() => handleRowClick(record)}
            >
              {record.code}
            </div>
          );
        },
        indexColumn: 0,
      },
      {
        key: "name",
        title: isProvider ? "Nhà cung cấp" : "Thầu phụ",
        width: 300,
        minWidth: !isProvider ? 300 : undefined,
        dataIndex: "name",

        render: (_, record) => (
          <div className="flex items-center gap-2">
            <BMDImage
              src={
                record.logo && record.logo.trim()
                  ? $url(record.logo)
                  : logoImage
              }
              className="!w-[34px] object-cover"
              width={34}
              height={34}
              fallback={logoImage}
            />
            <label htmlFor="" className="text-bold">
              {record.name}
            </label>
          </div>
        ),
        defaultVisible: true,
        sorter: true,
        indexColumn: 1,
      },
      {
        key: "aliasName",
        title: "Tên khác",
        width: 150,
        dataIndex: "aliasName",
        sorter: true,
        defaultVisible: true,
        showColumn: isProvider,
        indexColumn: 2,
      },
      {
        key: "email",
        title: "Email",
        dataIndex: "email",
        sorter: true,
        defaultVisible: true,
        showColumn: !isProvider,
        indexColumn: 3,
        width: 150,
      },
      {
        key: "phone",
        title: "Điện thoại",
        dataIndex: "phone",
        sorter: true,
        defaultVisible: true,
        showColumn: !isProvider,
        width: 140,
        indexColumn: 3,
        render: (_, record: any) => (
          <div className="service-cell">
            <div className="service-info">
              <div className="service-name">
                {record.phone1 || record.phone2}
              </div>
            </div>
          </div>
        ),
      },
      {
        key: "providerCategory",
        title: "Loại",
        width: 150,
        dataIndex: "providerCategory",
        sorter: true,
        render: (_, record: any) => (
          <div className="service-cell">
            <div className="service-info">
              <div className="service-name">
                {record.providerCategory?.name}
              </div>
            </div>
          </div>
        ),
        defaultVisible: true,
        indexColumn: 4,
      },
      {
        key: "createdAt",
        title: "Ngày bắt đầu hợp tác",
        width: 200,
        dataIndex: "createdAt",
        sorter: true,
        align: "right",
        render: (createdAt) =>
          createdAt
            ? new Date(createdAt * 1000).toLocaleDateString("vi-VN")
            : "-",
        defaultVisible: true,
        showColumn: isProvider,
        indexColumn: 5,
      },
      {
        key: "address",
        title: "Địa chỉ",
        dataIndex: "address",
        width: 350,
        minWidth: isProvider ? undefined : 350,
        sorter: true,
        render: (_, record) => (
          <div className="service-cell">
            <div className="service-info">
              <div className="service-name">{record.address}</div>
            </div>
          </div>
        ),
        defaultVisible: true,
        indexColumn: isProvider ? 6 : 2,
      },
      {
        key: "website",
        title: "Website",
        dataIndex: "website",
        sorter: true,
        defaultVisible: true,
        showColumn: !isProvider,
        indexColumn: 7,
        width: 150,
      },
      {
        key: "status",
        title: "Trạng thái",
        align: "center",
        width: 150,
        // sorter: true,
        render: (_, record) => (
          <div className="flex justify-center">
            {record.isActive ? (
              <Tag color="green" className="status-tag !mr-0">
                Hoạt động
              </Tag>
            ) : (
              <Tag color="red" className="status-tag !mr-0">
                Bị khóa
              </Tag>
            )}
          </div>
        ),
        defaultVisible: true,
        indexColumn: 8,
      },
      {
        key: "actions",
        title: "Xử lý",
        align: "center",
        width: 100,
        fixed: "right",
        indexColumn: 9,
        render: (_, record) => {
          const nameModule = isProvider ? "nhà cung cấp" : "thầu phụ";
          return (
            <Space size="small">
              {haveEditPermission && (
                <EditButton
                  onClick={(e) => {
                    e.stopPropagation();
                    if (isProvider) {
                      navigate(
                        `/master-data/${PermissionNames.providerEdit.replace(
                          ":id",
                          record!.id + ""
                        )}?update=1`
                      );
                    } else {
                      navigate(
                        `/master-data/${PermissionNames.subcontractorEdit.replace(
                          ":id",
                          record!.id + ""
                        )}?update=1`
                      );
                    }
                  }}
                />
              )}

              {haveBlockPermission && (
                <LockButton
                  isActive={record.isActive}
                  onAccept={() =>
                    handleActiveProvider(record.id, record.isActive)
                  }
                  modalTitle={`${
                    record.isActive ? "Khóa" : "Mở khóa"
                  } ${nameModule} ${record.name}`}
                  modalContent={
                    <>
                      <div>
                        Khi {record.isActive ? "khóa" : "mở khóa"} {nameModule}{" "}
                        các thông tin của {nameModule} này cũng sẽ được{" "}
                        {record.isActive ? "khóa" : "mở khóa"}.
                      </div>
                      <div>
                        Bạn có chắc chắn muốn{" "}
                        {record.isActive ? "khóa" : "mở khóa"} {nameModule} này?
                      </div>
                    </>
                  }
                />
              )}
            </Space>
          );
        },
        defaultVisible: true,
        alwaysVisible: true,
      },
    ];

    const handleOnUploadedFile = async (excelData: any, setData: any) => {
      const { results } = excelData;

      console.log("results", results);

      const importData = results?.map((item: any) => {
        const refineRow = removeSubstringFromKeys(item, " *");

        const code = refineRow[isProvider ? "Mã NCC" : "Mã thầu phụ"];
        const name = refineRow[isProvider ? "Tên NCC" : "Tên thầu phụ"];
        const providerCategoryName =
          refineRow[isProvider ? "Loại NCC" : "Loại thầu phụ"];
        const aliasName = refineRow["Tên khác"];
        const accountGroupName = refineRow["Nhóm tài khoản"];
        const materialGroupNames = refineRow["Nhóm sản phẩm"];
        const website = refineRow["Website"];
        const countryName = refineRow["Quốc gia"];
        const cityName = refineRow["Tỉnh/Thành phố"];
        const districtName = refineRow["Quận/Huyện"];
        const wardName = refineRow["Xã/Phường"];
        const address = refineRow["Địa chỉ"];
        const areaCode = refineRow["Mã vùng"];
        const bankName1 = refineRow["Tên ngân hàng 1"];
        const bankAccount1 = refineRow["Số tài khoản 1"];
        const bankName2 = refineRow["Tên ngân hàng 2"];
        const bankAccount2 = refineRow["Số tài khoản 2"];
        const currency = Object.values(CurrencyTrans).find(
          (item) => item.label == refineRow["Loại tiền tệ"]
        )?.value;
        const taxNumber = refineRow["Mã số thuế"];
        const salutation = refineRow["Danh xưng"];
        const contactName = refineRow["Tên người liên hệ"];
        const contactPhone = refineRow["Điện thoại"];
        const contactMobile = refineRow["Điện thoại di động"];
        const contactEmail = refineRow["Email"];
        const contactAddress = refineRow["Địa chỉ người liên hệ"];
        const statusName = refineRow["Trạng thái"];
        const specialization = refineRow["Lĩnh vực chuyên môn"];
        const staffCount = refineRow["Số lượng nhân sự"];
        const averageCompletionTime =
          refineRow["Thời gian hoàn thành trung bình"];
        const workTypeNames = refineRow["Loại công tác"];
        const startDate = refineRow["Ngày bắt đầu hợp tác"]
          ? excelDateToDayjs(refineRow["Ngày bắt đầu hợp tác"])
          : "";
        const phone1 = refineRow["Điện thoại 1"];
        const phone2 = refineRow["Điện thoại 2"];
        const email = refineRow["Email thầu phụ"];
        const fax = refineRow["Fax"];
        let isActive = undefined;
        if (statusName) {
          isActive = statusName == "Hoạt động" ? true : false;
        }

        return {
          module: module,
          name,
          code,
          providerCategoryName,
          aliasName,
          accountGroupName,
          materialGroupNames,
          website,
          countryName,
          cityName,
          districtName,
          wardName,
          address,
          areaCode,
          bankName1,
          bankAccount1,
          bankName2,
          bankAccount2,
          currency,
          taxNumber,
          salutation,
          contactName,
          contactPhone,
          contactMobile,
          contactEmail,
          contactAddress,
          isActive,
          specialization,
          staffCount,
          averageCompletionTime,
          workTypeNames,
          startDate,
          phone1,
          phone2,
          email,
          fax,
          rowNum: item.__rowNum__,
        };
      });
      console.log("importData", importData);

      setData(importData);
    };

    const handleDownloadDemoExcel = async () => {
      if (isProvider) {
        onDownloadDemoSupplierExcel();
      } else {
        onDownloadDemoSubContractorExcel();
      }
    };

    const onDownloadDemoSupplierExcel = async () => {
      try {
        setLoadingDownloadDemo(true);
        const [
          accountGroupNames,
          maritalGroupNames,
          countryNames,
          providerCategoryNames,
          workTypeNames,
        ] = await Promise.all([
          getListNameByTypeDictionary(DictionaryType.AccountGroup),
          getListNameByTypeDictionary(DictionaryType.ProductGroup),
          getListNameByTypeDictionary(DictionaryType.Country),
          getListNameByTypeDictionary(DictionaryType.ProviderCategory),
          getListNameByTypeDictionary(DictionaryType.WorkType),
        ]);

        const currencyOptions = Object.values(CurrencyTrans)
          .map((item) => item.label)
          .join(",");

        const result = await exportTemplateWithValidation({
          templatePath: "/exportFile/file_mau_nhap_ncc.xlsx",
          outputFileName: "file_mau_nhap_ncc.xlsx",
          sheetsToAdd: [
            { name: "Nhóm tài khoản", data: accountGroupNames },
            { name: "Nhóm sản phẩm", data: maritalGroupNames },
            { name: "Quốc gia", data: countryNames },
            { name: "Loại nhà cung cấp", data: providerCategoryNames },
            { name: "Loại công tác", data: workTypeNames },
          ],
          validations: [
            {
              headerName: "Loại NCC",
              type: "list",
              formulae: [
                `'Loại nhà cung cấp'!$A$1:$A$${providerCategoryNames.length}`,
              ],
            },
            {
              headerName: "Quốc gia",
              type: "list",
              formulae: [`'Quốc gia'!$A$1:$A$${countryNames.length}`],
            },
            {
              headerName: "Nhóm tài khoản",
              type: "list",
              formulae: [
                `'Nhóm tài khoản'!$A$1:$A$${accountGroupNames.length}`,
              ],
            },
            {
              headerName: "Nhóm sản phẩm",
              type: "list",
              formulae: [`'Nhóm sản phẩm'!$A$1:$A$${maritalGroupNames.length}`],
            },
            {
              headerName: "Loại tiền tệ",
              type: "list",
              formulae: [`"${currencyOptions}"`],
            },
            {
              headerName: "Tỉnh/Thành phố",
              type: "list",
              allowBlank: true,
              formulae: [
                "=OFFSET(State_list!$A$2,0,0,COUNTA(State_list!$A:$A),1)",
              ],
            },
            {
              headerName: "Quận/Huyện",
              type: "list",
              allowBlank: true,
              formulae: [
                "=OFFSET(City_list!$B$1,MATCH(J2,City_list!$A:$A,0)-1,0,COUNTIF(City_list!$A:$A,J2),1)",
              ],
            },
            {
              headerName: "Xã/Phường",
              type: "list",
              allowBlank: true,
              formulae: [
                "=OFFSET(District_list!$B$1,MATCH(K2,District_list!$A:$A,0)-1,0,COUNTIF(District_list!$A:$A,K2),1)",
              ],
            },
            {
              headerName: "Trạng thái",
              type: "list",
              allowBlank: true,
              formulae: [`'Status'!$A$1:$A$2`],
            },
            {
              headerName: "Loại công tác",
              type: "list",
              allowBlank: true,
              formulae: [`'Loại công tác'!$A$1:$A$${workTypeNames.length}`],
            },
          ],
        });
      } catch (error) {
        message.error(
          `Có lỗi xảy ra: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      } finally {
        setLoadingDownloadDemo(false);
      }
    };

    const onDownloadDemoSubContractorExcel = async () => {
      try {
        setLoadingDownloadDemo(true);
        const [countryNames, subContractorCategoryNames, workTypeNames] =
          await Promise.all([
            getListNameByTypeDictionary(DictionaryType.Country),
            getListNameByTypeDictionary(DictionaryType.SubContractorCategory),
            getListNameByTypeDictionary(DictionaryType.WorkType),
          ]);

        const currencyOptions = Object.values(CurrencyTrans)
          .map((item) => item.label)
          .join(",");

        const result = await exportTemplateWithValidation({
          templatePath: "/exportFile/file_mau_nhap_thau_phu.xlsx",
          outputFileName: "file_mau_nhap_thau_phu.xlsx",
          sheetsToAdd: [
            { name: "Quốc gia", data: countryNames },
            { name: "Loại thầu phụ", data: subContractorCategoryNames },
            { name: "Loại công tác", data: workTypeNames },
          ],
          validations: [
            {
              headerName: "Loại thầu phụ",
              type: "list",
              formulae: [
                `'Loại thầu phụ'!$A$1:$A$${subContractorCategoryNames.length}`,
              ],
            },
            {
              headerName: "Quốc gia",
              type: "list",
              formulae: [`'Quốc gia'!$A$1:$A$${countryNames.length}`],
            },
            {
              headerName: "Loại tiền tệ",
              type: "list",
              formulae: [`"${currencyOptions}"`],
            },
            {
              headerName: "Tỉnh/Thành phố",
              type: "list",
              allowBlank: true,
              formulae: [
                "=OFFSET(State_list!$A$2,0,0,COUNTA(State_list!$A:$A),1)",
              ],
            },
            {
              headerName: "Quận/Huyện",
              type: "list",
              allowBlank: true,
              formulae: [
                `=OFFSET(City_list!$B$1,MATCH(INDIRECT("I"&ROW()),City_list!$A:$A,0)-1,0,COUNTIF(City_list!$A:$A,INDIRECT("I"&ROW())),1)`,
              ],
            },
            {
              headerName: "Xã/Phường",
              type: "list",
              allowBlank: true,
              formulae: [
                `=OFFSET(District_list!$B$1,MATCH(INDIRECT("J"&ROW()),District_list!$A:$A,0)-1,0,COUNTIF(District_list!$A:$A,INDIRECT("J"&ROW())),1)`,
              ],
            },
            {
              headerName: "Trạng thái",
              type: "list",
              allowBlank: true,
              formulae: [`'Status'!$A$1:$A$2`],
            },
            {
              headerName: "Loại công tác",
              type: "list",
              allowBlank: true,
              formulae: [`'Loại công tác'!$A$1:$A$${workTypeNames.length}`],
            },
          ],
        });
      } catch (error) {
        message.error(
          `Có lỗi xảy ra: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      } finally {
        setLoadingDownloadDemo(false);
      }
    };

    return (
      <div className="app-container">
        <PageTitle
          title={title}
          breadcrumbs={["Dữ liệu nguồn", title]}
          extra={
            <Space>
              {haveAddPermission && (
                <>
                  <CustomButton
                    size="small"
                    showPlusIcon
                    onClick={() => {
                      if (isProvider) {
                        navigate(`/master-data/${PermissionNames.providerAdd}`);
                      } else {
                        navigate(
                          `/master-data/${PermissionNames.subcontractorAdd}`
                        );
                      }
                    }}
                  >
                    {isProvider ? "Tạo nhà cung cấp" : "Tạo thầu phụ"}
                  </CustomButton>
                  <CustomButton
                    size="small"
                    icon={<ImportOutlined />}
                    onClick={() => {
                      importModal.current?.open();
                    }}
                  >
                    Nhập excel
                  </CustomButton>
                </>
              )}
            </Space>
          }
        />
        <Card>
          <div className="flex gap-[16px] items-end pb-[12px] justify-between">
            <div className="flex gap-[16px] items-end flex-wrap">
              <div className="w-[200px]">
                <CustomInput
                  tooltipContent="Tìm kiếm theo tên, mã"
                  label="Tìm kiếm"
                  placeholder="Tìm kiếm"
                  value={query.search}
                  onChange={(value) => {
                    query.search = value;
                    setQuery({ ...query });

                    if (!value) {
                      fetchData({ ...query });
                    }
                  }}
                  onPressEnter={() => {
                    // Pass all parameters including filters to API
                    query.page = 1;
                    fetchData({ ...query });
                  }}
                  allowClear

                  // error="Có lỗi xãy ra"
                />
              </div>
              <div className="flex flex-col">
                <b>Loại</b>
                <DictionarySelector
                  label="Loại"
                  initQuery={{
                    type: isProvider
                      ? DictionaryType.ProviderCategory
                      : DictionaryType.SubContractorCategory,
                  }}
                  allowClear={true}
                  addonOptions={[
                    {
                      id: "",
                      name: "Tất cả các loại",
                    },
                  ]}
                  value={query.providerCategoryId ?? ""}
                  onChange={(value) => {
                    query.providerCategoryId = value || "";
                    setQuery({ ...query });
                  }}
                />
              </div>
              <div className="flex flex-col">
                <b>Trạng thái</b>
                <Select
                  value={query.isActive ?? ""}
                  allowClear
                  options={[
                    {
                      value: "",
                      label: "Tất cả trạng thái",
                    },
                    {
                      value: true,
                      label: "Hoạt động",
                    },
                    {
                      value: false,
                      label: "Bị khóa",
                    },
                  ]}
                  onChange={(e) => {
                    if (e === "") {
                      delete query.isActive;
                      setQuery({ ...query });
                    } else {
                      setQuery({ ...query, isActive: e });
                    }
                  }}
                ></Select>
              </div>
              <CustomButton
                onClick={() => {
                  setQuery({ ...query, page: 1 });
                  fetchData({ ...query, page: 1 });
                }}
              >
                Áp dụng
              </CustomButton>
              {!isEmptyQuery && (
                <CustomButton
                  variant="outline"
                  onClick={() => {
                    delete query.isActive;
                    delete query.providerCategoryId;
                    delete query.search;
                    setQuery({ ...query });
                    fetchData();
                  }}
                >
                  Bỏ lọc
                </CustomButton>
              )}
            </div>
            <CustomButton
              onClick={() => {
                Modal.confirm({
                  title: `Bạn có muốn xuất file excel?`,
                  getContainer: () => {
                    return document.getElementById("App") as HTMLElement;
                  },
                  icon: null,

                  footer: (_, { OkBtn, CancelBtn }) => (
                    <>
                      <CustomButton
                        variant="outline"
                        className="cta-button"
                        onClick={() => {
                          handleExport({
                            onProgress(percent) {
                              console.log("What is percent", percent);
                            },
                            exportColumns,
                            fileType: "xlsx",
                            dataField: "providers",
                            query: query,
                            api: providerApi.findAll,
                            fileName: isProvider
                              ? "Danh sách nhà cung cấp"
                              : "Danh sách thầu phụ",
                            sheetName: isProvider
                              ? "Danh sách nhà cung cấp"
                              : "Danh sách thầu phụ",
                          });
                          Modal.destroyAll();
                        }}
                      >
                        Có
                      </CustomButton>
                      <CustomButton
                        onClick={() => {
                          Modal.destroyAll();
                        }}
                        className="cta-button"
                      >
                        Không
                      </CustomButton>

                      {/* <OkBtn />
                      <CancelBtn /> */}
                    </>
                  ),
                });
              }}
            >
              Xuất excel
            </CustomButton>
          </div>
          <CustomizableTable
            columns={filterActionColumnIfNoPermission(columns, [
              haveEditPermission,
              haveBlockPermission,
            ])}
            dataSource={providers}
            rowKey="id"
            loading={loading}
            pagination={false}
            scroll={{ x: 1200 }}
            bordered
            displayOptions
            //@ts-ignore
            onChange={handleTableChange}
            onRowClick={handleRowClick}
          />

          <Pagination
            currentPage={query.page}
            defaultPageSize={query.limit}
            total={total}
            onChange={({ limit, page }) => {
              query.page = page;
              query.limit = limit;
              setQuery({ ...query });
              fetchData();
            }}
          />

          {useMemo(
            () => (
              <ImportProvider
                guide={[
                  "Vui lòng tải và sử dụng file mẫu để import dữ liệu đúng cách",
                  "Không được thay đổi tiêu đề trong file csv/excel mẫu để tránh import thiếu dữ liệu",
                  "Chi tiết xem tại sheet Hướng dẫn của file import mẫu",
                ]}
                onSuccess={() => {
                  query.page = 1;
                  fetchData();
                }}
                ref={importModal}
                createApi={providerApi.create}
                onUploaded={(excelData, setData) => {
                  console.log("up gì lên vậy", excelData);
                  handleOnUploadedFile(excelData, setData);
                }}
                okText={isProvider ? `Nhập NCC ngay` : `Nhập thầu phụ ngay`}
                providerModule={module}
                // demoExcel="/exportFile/file_mau_nhap_service.xlsx"
                onDownloadDemoExcel={handleDownloadDemoExcel}
                loadingDownloadDemo={loadingDownloadDemo}
              />
            ),
            [loadingDownloadDemo]
          )}
        </Card>
      </div>
    );
  }
);
