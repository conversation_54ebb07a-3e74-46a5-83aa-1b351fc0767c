import { Col, Form, Input, message, Modal, Row } from "antd";
import { Rule } from "antd/lib/form";
import { boqApi } from "api/boq.api";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { ModalStatus } from "types/modal";
import { BOQ } from "types/boq";

const rules: Rule[] = [{ required: true }];

export interface boqModal {
  handleCreate: () => void;
  handleUpdate: (boq: BOQ) => void;
}
interface boqModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
}

export const boqModal = React.forwardRef(
  ({ onClose, onSubmitOk }: boqModalProps, ref) => {
    const [form] = Form.useForm<BOQ>();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");
    const [selectedboq, setSelectedboq] = useState<BOQ>();

    useImperativeHandle<any, boqModal>(
      ref,
      () => ({
        handleCreate() {
          form.resetFields();
          setVisible(true);
          setStatus("create");
          setSelectedboq(undefined);
        },
        handleUpdate(boq: BOQ) {
          // form.setFieldsValue({ ...boq });
          setVisible(true);
          setStatus("update");
          setSelectedboq(boq);
        },
      }),
      []
    );

    const getPayload = () => {
      const { ...rest } = form.getFieldsValue();
      return { boq: rest };
    };

    const submitForm = async () => {
      try {
        setLoading(true);
        const valid = await form.validateFields();
        const data = getPayload();
        let res: any = undefined;
        switch (status) {
          case "create":
            res = await boqApi.create(data);
            message.success("Create boq successfully!");
            break;
          case "update":
            res = await boqApi.update(selectedboq?.id || 0, data);
            message.success("Update boq successfully!");
            break;
        }
        onSubmitOk();
        handleClose();
      } finally {
        setLoading(false);
      }
    };

    const handleClose = () => {
      onClose?.();
      setVisible(false);
      setSelectedboq(undefined);
    };

    return (
      <Modal
        onCancel={() => {
          handleClose();
        }}
        visible={visible}
        title={status == "create" ? "Create boq" : "Update boq"}
        style={{ top: 20 }}
        width={700}
        confirmLoading={loading}
        onOk={submitForm}
      >
        <Form layout="vertical" form={form}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="Username" name="username" rules={rules}>
                <Input placeholder="" />
              </Form.Item>
            </Col>

            {status == "create" && (
              <Col span={12}>
                <Form.Item label="Password" name="password" rules={rules}>
                  <Input placeholder="" />
                </Form.Item>
              </Col>
            )}

            <Col span={12}>
              <Form.Item label="Name" name="name" rules={rules}>
                <Input placeholder="" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item label="Phone" name="phone" rules={rules}>
                <Input placeholder="" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item label="Email" name="email">
                <Input placeholder="" />
              </Form.Item>
            </Col>
          </Row>

          {/* <Form.Item shouldUpdate={true}>
            {() => {
              return (
                <Form.Item label="Avatar" name="avatar">
                  <SingleImageUpload
                    onUploadOk={(path: string) => {
                      console.log("onUploadOk:", path);
                      form.setFieldsValue({
                        avatar: path,
                      });
                    }}
                    imageUrl={form.getFieldValue("avatar")}
                  />
                </Form.Item>
              );
            }}
          </Form.Item> */}
        </Form>
      </Modal>
    );
  }
);
