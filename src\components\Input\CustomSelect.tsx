import React from "react";
import { Select } from "antd";
import { DownOutlined } from "@ant-design/icons";
import { useTheme } from "context/ThemeContext";
import "./CustomInput.scss";
import clsx from "clsx";

export type CustomSelectProps = {
  label?: string;
  required?: boolean;
  value?: string | number | string[] | number[];
  onChange?: (value: any, option?: any) => void;
  placeholder?: string;
  error?: string;
  disabled?: boolean;
  options?: { label: string; value: string | number | boolean }[];
  className?: string;
  status?: "normal" | "error" | "disabled";
  classNameContainer?: string;
  mode?: "multiple" | "tags";
  allowClear?: boolean;
  onSearch?: (value: string) => void;
  loading?: boolean;
  showSearch?: boolean;
  filterOption?: ((input: string, option?: any) => boolean) | boolean;
  inputStyle?: React.CSSProperties;
  maxTagCount?: number;
};

const CustomSelect: React.FC<CustomSelectProps> = ({
  label = "",
  required = false,
  value,
  onChange,
  placeholder = "Chọn...",
  error,
  disabled = false,
  options = [],
  className = "",
  status: propStatus,
  classNameContainer,
  mode,
  allowClear = true,
  loading,
  onSearch,
  showSearch,
  filterOption,
  inputStyle,
  maxTagCount,
}) => {
  const { darkMode } = useTheme();

  const getInputStatus = () => {
    if (propStatus) return propStatus;
    if (error) return "error";
    if (disabled) return "disabled";
    return "normal";
  };

  const status = getInputStatus();
  const inputClassName = `custom-input custom-input-${status} ${
    darkMode ? "dark" : ""
  } ${className}`;

  const handleChange = (selectedValue: any, option: any) => {
    if (onChange) {
      onChange(selectedValue, option);
    }
  };

  return (
    <div className={clsx("custom-input-container", classNameContainer)}>
      {label && (
        <div className="custom-input-label">
          {label} {required && <span className="custom-input-required">*</span>}
        </div>
      )}
      <Select
        getPopupContainer={() => document.getElementById("App") as HTMLElement}
        size="large"
        value={value}
        onChange={handleChange}
        placeholder={placeholder}
        disabled={disabled}
        className={inputClassName}
        suffixIcon={<DownOutlined />}
        options={options}
        status={error ? "error" : undefined}
        mode={mode}
        allowClear={allowClear}
        onSearch={onSearch}
        loading={loading}
        showSearch={showSearch}
        filterOption={filterOption}
        style={inputStyle}
        maxTagCount={maxTagCount}
      />
      {error && <div className="custom-input-error-message">{error}</div>}
    </div>
  );
};

export default CustomSelect;
