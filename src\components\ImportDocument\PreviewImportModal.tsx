import { DownloadOutlined } from "@ant-design/icons";
import { <PERSON>ton, Modal, Table, Tabs } from "antd";
import React, { useImperativeHandle, useMemo, useState } from "react";

export interface PreviewImportModal {
  open: (
    data: any[],
    isImportedData?: boolean,
    isChangeTabError?: boolean
  ) => void;
  close: () => void;
}

interface PreviewImportModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
  columns: any[];
  title?: string;
  totalData?: number;
}

export const PreviewImportModal = React.forwardRef<
  PreviewImportModal,
  PreviewImportModalProps
>(
  (
    {
      onClose,
      onSubmitOk,
      columns,
      title = "Xem danh sách dữ liệu",
      totalData = 0,
    },
    ref
  ) => {
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState(false);
    const [isImportedData, setIsImportedData] = useState(false);
    const [data, setData] = useState<any[]>();
    const [activeKey, setActiveKey] = useState("valid");

    const handleClose = () => {
      setVisible(false);
      onClose();
    };

    const handleOpen = () => {
      setVisible(true);
    };

    useImperativeHandle<any, PreviewImportModal>(
      ref,
      () => ({
        open(newData, isShowFull = false, isChangeTabError = false) {
          setIsImportedData(isShowFull);
          setData(newData);
          setActiveKey(isChangeTabError ? "invalid" : "valid");
          handleOpen();
        },
        close() {
          handleClose();
        },
      }),
      []
    );

    const successData = useMemo(
      () => (data || []).filter((item) => !item.errorMessage),
      [data]
    );

    const errorData = useMemo(
      () => (data || []).filter((item) => item.errorMessage),
      [data]
    );

    return (
      <Modal
        afterClose={() => {
          setData(undefined);
        }}
        // footer={null}
        okButtonProps={{
          className: "hidden",
        }}
        cancelText="Đóng"
        onCancel={handleClose}
        open={visible}
        title={title + ` (Tổng số dòng dữ liệu: ${totalData})`}
        width={1200}
        confirmLoading={loading}
      >
        <Tabs activeKey={activeKey} onChange={setActiveKey}>
          <Tabs.TabPane
            tabKey="valid"
            key="valid"
            tab={`${isImportedData ? "Thành công" : "Hợp lệ"} (${
              successData.length
            })`}
          >
            <Table
              bordered
              scroll={{
                x: 1200,
                y: 500,
              }}
              onRow={(item) => ({
                className: !!item.errorMessage ? "bg-red-50" : "bg-green-50",
              })}
              pagination={false}
              size="small"
              dataSource={successData}
              columns={columns.filter((e) => !!e)}
              rowKey={(record, index) => `valid-${index}`}
            />
          </Tabs.TabPane>
          <Tabs.TabPane
            tabKey="invalid"
            key="invalid"
            tab={`${isImportedData ? "Thất bại" : "Không hợp lệ"} (${
              errorData.length
            })`}
          >
            <Table
              bordered
              scroll={{
                x: 1200,
                y: 500,
              }}
              onRow={(item) => ({
                className: !!item.errorMessage ? "bg-red-50" : "bg-green-50",
              })}
              pagination={false}
              size="small"
              dataSource={errorData}
              columns={columns.filter((e) => !!e)}
              rowKey={(record, index) => `invalid-${index}`}
            />
          </Tabs.TabPane>
        </Tabs>
      </Modal>
    );
  }
);

PreviewImportModal.displayName = "PreviewImportModal";
