import React, { useEffect, useMemo, useState } from "react";
import { Modal } from "antd";
import { usePermission } from "hooks/usePermission";
import { CustomizableColumn } from "components/Table/CustomizableTable";
import { PermissionType, PermissionTypeTrans } from "types/permission";
import { uniq } from "lodash";
import CustomButton from "components/Button/CustomButton";
import { Checkbox } from "antd";
import { permissionStore } from "store/permissionStore";
import RoleTable, { RoleColumn } from "views/Role/components/RoleTable";

interface PermissionSelectionModalProps {
  visible: boolean;
  onCancel: () => void;
  onSave: (
    selectedPermissionIds: number[],
    selectedPermissionNames: string[]
  ) => void;
  initialPermissionNames?: string[];
  title?: string;
  permissionList: any[];
}

export const PermissionSelectionModal: React.FC<
  PermissionSelectionModalProps
> = ({
  visible,
  onCancel,
  onSave,
  initialPermissionNames = [],
  title = "Chọn quyền bổ sung",
  permissionList,
}) => {
  const [checkedNames, setCheckedNames] = useState<string[]>(
    initialPermissionNames
  );
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      setCheckedNames(initialPermissionNames);
    }
  }, [visible, initialPermissionNames]);

  const columns: CustomizableColumn<RoleColumn>[] = useMemo(() => {
    const cols: CustomizableColumn<RoleColumn>[] = [
      {
        title: "Chức năng",
        key: "feature",
        dataIndex: "feature",
      },
    ];

    Object.values(PermissionTypeTrans).forEach((it) => {
      cols.push({
        title: it.label,
        key: it.value,
        dataIndex: it.value,
        align: "center",
        width: 100,
        render: (_, record) => {
          return record.routeChildren ? null : (
            <Checkbox
              className="py-[7.25px]"
              disabled={it.value != PermissionType.List}
              checked={
                checkedNames.includes(record.name as any) &&
                it.value == PermissionType.List
              }
              onChange={(e) => {
                const checked = e.target.checked;
                const checkedName = record.name || "";
                if (checked) {
                  setCheckedNames(uniq([...checkedNames, checkedName]));
                } else {
                  setCheckedNames(
                    checkedNames.filter((k) => k !== checkedName)
                  );
                }
              }}
            />
          );
        },
      });
    });

    return cols;
  }, [checkedNames]);

  const handleSave = () => {
    // Map permission names to IDs using permissionList from props
    const selectedIds = checkedNames
      .map((permissionName) => {
        const permission = permissionList.find(
          (p) => p.name === permissionName
        );
        console.log(
          `PermissionModal: Looking for "${permissionName}", found:`,
          permission
        );
        return permission?.id;
      })
      .filter(Boolean) as number[];

    console.log("=== MODAL SAVE DEBUGGING ===");
    console.log("Checked names count:", checkedNames.length);
    console.log("Permission list count:", permissionList.length);
    console.log("Successfully mapped IDs:", selectedIds.length);
    console.log(
      "Failed to map count:",
      checkedNames.length - selectedIds.length
    );

    // Tìm những permission names không map được ID
    const failedToMap = checkedNames.filter((name) => {
      const permission = permissionList.find((p) => p.name === name);
      return !permission?.id;
    });
    console.log("Failed to map permissions:", failedToMap);

    console.log("PermissionModal: Final selectedIds:", selectedIds);
    console.log("PermissionModal: Final checkedNames:", checkedNames);

    onSave(selectedIds, checkedNames);
    onCancel();
  };

  const handleCancel = () => {
    setCheckedNames(initialPermissionNames);
    onCancel();
  };

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={handleCancel}
      width={1000}
      footer={[
        <CustomButton
          key="cancel"
          variant="outline"
          onClick={handleCancel}
          className="cta-button mr-4"
        >
          Hủy
        </CustomButton>,
        <CustomButton
          key="save"
          onClick={handleSave}
          loading={loading}
          className="cta-button"
        >
          Lưu
        </CustomButton>,
      ]}
    >
      <div className="py-4">
        <RoleTable
          loading={loading}
          checkedNames={checkedNames}
          setCheckedNames={setCheckedNames}
          // columns={columns}
        />
      </div>
    </Modal>
  );
};
