import Cookies from "js-cookie";
import md5 from "md5";
import dayjs from "dayjs";
import { userStore } from "store/userStore";
import { Permission, PermissionType } from "types/permission";
import { settings } from "settings";
import { CustomizableColumn } from "components/Table/CustomizableTable";
export const getToken = () => {
  const token = Cookies.get("token");

  return token || "";
};

export const setToken = (token: string) => {
  return Cookies.set("token", token, {
    path: "/", // Cookie will be available on all paths
    expires: 7,
    sameSite: "Lax",
    secure: true,
  });
};

export const checkRole = (role: string, permissions?: Permission[]) => {
  const find = permissions?.find((e) => e.name.includes(role));
  if (find || !settings.checkPermission) return true;
  return false;
};

export interface roleAction {
  create?: string;
  update?: string;
  detail?: string;
  delete?: string;
  export?: string;
  import?: string;
  showHide?: string;
}

interface RoleCheck {
  add?: string;
  edit?: string;
  list?: string;
  block?: string;
  delete?: string;
  reset?: string;
  viewAll?: string;
}

export const checkRoles = (roles: RoleCheck, permissions: Permission[]) => {
  let haveAddPermission = !!permissions.find((e) =>
    e.name.includes(roles.add || "")
  );
  let haveEditPermission = !!permissions.find((e) =>
    e.name.includes(roles.edit || "")
  );
  let haveListPermission = !!permissions.find((e) =>
    e.name.includes(roles.list || "")
  );
  let haveBlockPermission = !!permissions.find((e) =>
    e.name.includes(roles.block || "")
  );
  let haveDeletePermission = !!permissions.find((e) =>
    e.name.includes(roles.delete || "")
  );
  let haveResetPermission = !!permissions.find((e) =>
    e.name.includes(roles.reset || "")
  );
  let haveViewAllPermission = !!permissions.find((e) =>
    e.name.includes(roles.viewAll || "")
  );

  if (!settings.checkPermission) {
    return {
      haveAddPermission: true,
      haveEditPermission: true,
      haveListPermission: true,
      haveBlockPermission: true,
      haveDeletePermission: true,
      haveResetPermission: true,
      haveViewAllPermission: true,
    };
  }

  return {
    haveAddPermission,
    haveEditPermission,
    haveListPermission,
    haveBlockPermission,
    haveDeletePermission,
    haveResetPermission,
    haveViewAllPermission,
  };
};

export const filterActionColumnIfNoPermission = <T>(
  columns: CustomizableColumn<T>[],
  permissions: boolean[]
) => {
  return columns.filter(
    (column) => column.key !== "actions" || permissions.some((p) => p == true)
  );
};

export const generateHash = () => {
  const time = dayjs().unix();
  const hash = md5(`${import.meta.env.VITE_PUBLIC_HASHKEY}.${time}`);
  return { time, hash };
};

/**
 * Kiểm tra quyền edit record dựa trên người tạo và quyền viewAll
 * @param userStoreId - ID của user hiện tại từ userStore
 * @param recordCreatorId - ID của người tạo record
 * @param hasEditPermission - Có quyền edit hay không
 * @param hasViewAllPermission - Có quyền viewAll hay không
 * @returns boolean - Có được phép edit hay không
 */
export const checkEditPermissionByCreator = (
  userStoreId: number | undefined,
  recordCreatorId: number | undefined,
  hasEditPermission: boolean,
  hasViewAllPermission: boolean
): boolean => {
  // Nếu không có quyền edit cơ bản thì không được edit
  if (!hasEditPermission) {
    return false;
  }

  // Nếu có quyền viewAll thì được edit tất cả
  if (hasViewAllPermission) {
    return true;
  }

  // Nếu là người tạo thì được edit
  if (userStoreId === recordCreatorId) {
    return true;
  }

  // Các trường hợp khác không được edit
  return false;
};

/**
 * Kiểm tra quyền delete record dựa trên người tạo và quyền viewAll
 * @param userStoreId - ID của user hiện tại từ userStore
 * @param recordCreatorId - ID của người tạo record
 * @param hasDeletePermission - Có quyền delete hay không
 * @param hasViewAllPermission - Có quyền viewAll hay không
 * @returns boolean - Có được phép delete hay không
 */
export const checkDeletePermissionByCreator = (
  userStoreId: number | undefined,
  recordCreatorId: number | undefined,
  hasDeletePermission: boolean,
  hasViewAllPermission: boolean
): boolean => {
  // Nếu không có quyền delete cơ bản thì không được delete
  if (!hasDeletePermission) {
    return false;
  }

  // Nếu có quyền viewAll thì được delete tất cả
  if (hasViewAllPermission) {
    return true;
  }

  // Nếu là người tạo thì được delete
  if (userStoreId === recordCreatorId) {
    return true;
  }

  // Các trường hợp khác không được delete
  return false;
};
