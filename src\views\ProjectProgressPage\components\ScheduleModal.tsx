import { Col, Form, Input, message, Modal, Row } from "antd";
import { Rule } from "antd/lib/form";
import { scheduleApi } from "api/schedule.api";
import CustomInput from "components/Input/CustomInput";
import { TextInput } from "components/Input/TextInput";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { ModalStatus } from "types/modal";
import { Schedule } from "types/schedule";
import dayjs from "dayjs";
import { formatDate, formatDateTime } from "utils/date";
import { BMDTextArea } from "components/TextArea/BMDTextArea";
import { MembershipSelector } from "components/Selector/MembershipSelector";
import { InputNumber } from "components/Input/InputNumber";

const rules: Rule[] = [{ required: true }];

export interface ScheduleModalRef {
  handleCreate: (start: Date, end: Date) => void;
  handleUpdate: (schedule: Schedule) => void;
}

interface ScheduleModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
}

export const ScheduleModal = React.forwardRef(
  ({ onClose, onSubmitOk }: ScheduleModalProps, ref) => {
    const [form] = Form.useForm<any>();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");
    const [selectedSchedule, setSelectedSchedule] = useState<Schedule>();
    const [timeSlot, setTimeSlot] = useState<{ start: Date; end: Date }>();

    useImperativeHandle<any, ScheduleModalRef>(
      ref,
      () => ({
        handleCreate(start: Date, end: Date) {
          form.resetFields();

          const workingDays = getWorkingDays(
            start,
            dayjs(end).subtract(1, "day").toDate()
          );

          form.setFieldsValue({
            Duration: workingDays,
          });

          setTimeSlot({ start, end });
          setVisible(true);
          setStatus("create");
        },

        handleUpdate(schedule: Schedule) {
          setSelectedSchedule(schedule);
          form.setFieldsValue({
            TaskName: schedule.TaskName,
            info: schedule.info,
            Duration: schedule.Duration,
            work: schedule.work,
            resourceIds: schedule.resources?.map((r) => r.id) || [],
          });
          setVisible(true);
          setStatus("update");
        },
      }),
      []
    );

    const createData = async () => {
      const valid = await form.validateFields();
      const formData = form.getFieldsValue();

      if (!timeSlot) return;

      const payload = {
        resourceInfoIds: formData.resourceIds || [],
        schedule: {
          code: "",
          TaskID: 0,
          TaskName: formData.TaskName,
          StartDate: dayjs(timeSlot.start).format("YYYY-MM-DD"),
          EndDate: dayjs(timeSlot.end).format("YYYY-MM-DD"),
          Duration: formData.Duration || 0,
          work: formData.work || "",
          Progress: 0,
          Predecessor: "",
          Money: 0,
          info: formData.info,
          position: 0,
        },
      };

      setLoading(true);
      try {
        const res = await scheduleApi.createScheduleSelect(payload);
        message.success("Tạo schedule thành công!");
        onClose();
        onSubmitOk();
        setVisible(false);
      } catch (error) {
        console.error("Error creating schedule:", error);
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const valid = await form.validateFields();
      const formData = form.getFieldsValue();

      const payload = {
        resourceInfoIds: formData.resourceIds || [],
        schedule: {
          code: "",
          TaskID: selectedSchedule?.id || 0,
          TaskName: formData.TaskName,
          StartDate: selectedSchedule?.StartDate || "",
          EndDate: selectedSchedule?.EndDate || "",
          Duration: formData.Duration || 0,
          work: formData.work || "",
          Progress: 0,
          Predecessor: "",
          Money: 0,
          info: formData.info,
          position: 0,
        },
      };

      setLoading(true);
      try {
        const res = await scheduleApi.update(
          selectedSchedule?.id || 0,
          payload
        );
        message.success("Cập nhật schedule thành công!");
        onClose();
        onSubmitOk();
        setVisible(false);
      } catch (error) {
        console.error("Error updating schedule:", error);
      } finally {
        setLoading(false);
      }
    };

    function getWorkingDays(start: Date, end: Date): number {
      let count = 0;
      let current = dayjs(start);

      while (current.isBefore(end, "day") || current.isSame(end, "day")) {
        const day = current.day(); // 0 = Sunday, 6 = Saturday
        if (day !== 0 && day !== 6) {
          count++;
        }
        current = current.add(1, "day");
      }

      return count;
    }

    return (
      <Modal
        className="footer-full"
        onCancel={() => {
          onClose?.();
          setVisible(false);
        }}
        centered
        open={visible}
        title={status == "create" ? "Tạo schedule mới" : "Chỉnh sửa schedule"}
        style={{ top: 20 }}
        width={550}
        confirmLoading={loading}
        cancelButtonProps={{ style: { display: "none" } }}
        okText={status == "create" ? "Tạo" : "Lưu"}
        onOk={() => {
          status == "create" ? createData() : updateData();
        }}
        getContainer={() => {
          return document.getElementById("App") as HTMLElement;
        }}
      >
        <Form layout="vertical" form={form}>
          <Row gutter={16}>
            {status === "create" && timeSlot && (
              <Col span={24}>
                <div className="mb-4 p-3 bg-gray-50 rounded">
                  <p>
                    <strong>Thông tin hiện tại:</strong>
                  </p>
                  <p>
                    <strong>Thời gian bắt đầu:</strong>{" "}
                    {dayjs(timeSlot.start).format("YYYY/MM/DD")}
                  </p>
                  <p>
                    <strong>Thời gian kết thúc:</strong>{" "}
                    {dayjs(timeSlot.end)
                      .subtract(1, "day")
                      .format("YYYY/MM/DD")}
                  </p>
                </div>
              </Col>
            )}

            {status === "update" && selectedSchedule && (
              <Col span={24}>
                <div className="mb-4 p-3 bg-gray-50 rounded">
                  <p>
                    <strong>Thông tin hiện tại:</strong>
                  </p>
                  <p>
                    <strong>Thời gian bắt đầu:</strong>{" "}
                    {selectedSchedule.StartDate}
                  </p>
                  <p>
                    <strong>Thời gian kết thúc:</strong>{" "}
                    {selectedSchedule.EndDate}
                  </p>
                </div>
              </Col>
            )}

            <Col span={24}>
              <Form.Item name="TaskName" label="Tên task" rules={rules}>
                <Input placeholder="Nhập tên task" />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item name="info" label="Mô tả">
                <BMDTextArea placeholder="Nhập mô tả task" />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item name="resourceIds" label="Người phụ trách">
                <MembershipSelector
                  allowClear={true}
                  placeholder="Chọn người phụ trách"
                  multiple={true}
                />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item name="Duration" label="Thời lượng (ngày)">
                <InputNumber placeholder="Nhập thời lượng" disabled />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item name="work" label="Số giờ làm">
                <Input placeholder="VD: 8h" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
);
