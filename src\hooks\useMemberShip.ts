import { memberShipApi } from "api/memberShip.api";
import { useMemo, useState } from "react";
import { MemberShip } from "types/memberShip";
import { QueryParams2 } from "types/query";

export interface MemberShipQuery extends QueryParams2 {
  projectId?: number;
  companyId?: number;
  memberShipCategoryId?: number;
  queryObject?: string;
  isActive?: boolean;
}

interface UseMemberShipProps {
  initQuery: MemberShipQuery;
}

export const useMemberShip = ({ initQuery }: UseMemberShipProps) => {
  const [data, setData] = useState<MemberShip[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<MemberShipQuery>(initQuery);
  const [loading, setLoading] = useState(false);
  const [isFetched, setIsFetched] = useState(false);

  const isEmptyQuery = useMemo(
    () =>
      Object.keys(query).filter(
        (k) =>
          query[k] && !["limit", "page", "queryObject", "projectId"].includes(k)
      ).length == 0,
    [query]
  );

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await memberShipApi.findAll(query);

      setData(data.memberShips);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return {
    memberShips: data,
    total,
    fetchData,
    loading,
    setQuery,
    query,
    isEmptyQuery,
    isFetched,
    setData
  };
};
