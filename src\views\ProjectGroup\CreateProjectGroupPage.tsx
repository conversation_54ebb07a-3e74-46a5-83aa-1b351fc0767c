import {
  <PERSON><PERSON>,
  <PERSON>,
  Col,
  Divider,
  Form,
  Input,
  message,
  Modal,
  Row,
  Select,
} from "antd";
import TextArea from "antd/es/input/TextArea";
import { Rule } from "antd/lib/form";
import { providerApi } from "api/provider.api";
import { projectGroupApi } from "api/projectGroup.api";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { useNavigate } from "react-router-dom";
import { FileAttach } from "types/fileAttach";
import { ModalStatus } from "types/modal";
import { Provider, ProviderTypeTrans } from "types/provider";
import { getTitle } from "utils";
import { ProjectCategorySelector } from "components/Selector/ProjectCategorySelector";

const rules: Rule[] = [{ required: true }];

export const CreateProjectGroupPage = ({ title = "" }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    document.title = getTitle(title);
  }, []);

  const createData = async () => {
    const valid = await form.validateFields();
    const { projectCategoryId, ...data } = form.getFieldsValue();
    const payload = {
      projectGroup: {
        ...data,
      },
      projectCategoryId,
    };
    setLoading(true);
    try {
      const res = await projectGroupApi.create(payload);
      message.success("Tạo nhóm dự án thành công!");
      navigate("/project-group/project-group-list");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <div className="font-bold text-2xl mb-[20px]">Tạo nhóm dự án</div>
      <Card>
        <Form layout="vertical" form={form}>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label="Mã nhóm dự án" name="code">
                <Input placeholder="Nếu không điền hệ thống sẽ tự sinh mã" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="Tên nhóm dự án" name="name" rules={rules}>
                <Input placeholder="" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="Loại nhóm dự án"
                name="projectCategoryId"
                rules={rules}
              >
                <ProjectCategorySelector />
              </Form.Item>
            </Col>
          </Row>
        </Form>
        <div className="flex gap-[16px] justify-end mt-2">
          <Button
            size="large"
            className="w-[120px]"
            onClick={() => {
              navigate("/project-group/project-group-list");
            }}
          >
            Hủy
          </Button>
          <Button
            loading={loading}
            type="primary"
            size="large"
            className="w-[140px]"
            onClick={() => {
              createData();
            }}
          >
            Tạo nhóm dự án
          </Button>
        </div>
      </Card>
    </div>
  );
};
