import { Suspense, useCallback, useEffect, useState } from "react";
import { Layout, message, Modal, Spin } from "antd";
import "./styles/AdminLayout.scss";
import { Outlet, NavLink, useLocation, useNavigate } from "react-router-dom";
import { AuthProvider } from "provider/AuthProvider";
import { Navbar } from "./components/Navbar";
import { Sidebar } from "./components/Sidebar";
const { Header, Sider, Content } = Layout;
import { isMobile } from "react-device-detect";
import { SIDEBAR_WIDTH } from "utils/theme";
import { TransitionGroup, CSSTransition } from "react-transition-group";
import { appStore } from "store/appStore";
import { permissionStore } from "store/permissionStore";
import { useTheme } from "context/ThemeContext";
import { settings } from "settings";
import CustomButton from "components/Button/CustomButton";
import { PermissionNames } from "types/PermissionNames";
import { useOneSignalContext } from "context/OneSignalContext";
import { OneSignal } from "utils/oneSignal";
import { iOS } from "utils/devide";

export const AdminLayout = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { darkMode, toggleDarkMode } = useTheme();

  const [collapsed, setCollapsed] = useState(
    window.innerWidth <= 1024 ? true : false
  );
  const [siteLayoutMarginLeft, setSiteLayoutMarginLeft] =
    useState(SIDEBAR_WIDTH);
  const [openChooseProjectModal, setOpenChooseProjectModal] = useState(false);

  const toggle = useCallback(() => {
    setCollapsed((prev) => !prev);
  }, []);

  const errorMessage = iOS()
    ? "Không hỗ trợ trên ios"
    : "Vui lòng cấp quyền cho phép nhận thông báo trên trình duyệt bạn đang sử dụng";

  const {
    isSubscribed,
    loading: oneSignalLoading,
    setNotificationLoading,
  } = useOneSignalContext();

  useEffect(() => {
    if (window.innerWidth > 1024) {
      if (collapsed) {
        setSiteLayoutMarginLeft(0);
      } else {
        setSiteLayoutMarginLeft(SIDEBAR_WIDTH);
      }
    } else {
      setSiteLayoutMarginLeft(0);
    }
  }, [collapsed]);

  useEffect(() => {
    if (window.innerWidth <= 1024) {
      setCollapsed(true);
    }
  }, [location.pathname]);

  useEffect(() => {
    if (permissionStore.accessRoutes.length) {
      const route = permissionStore.accessRoutes.find((r) =>
        location.pathname.startsWith(r.path || "")
      );
      if (route && !route.hidden && (route.isPublic || route.isAccess)) {
        if (route.needProject && !appStore.currentProject) {
          setOpenChooseProjectModal(true);
        } else {
          setOpenChooseProjectModal(false);
        }
      }
    }
  }, [
    location.pathname,
    appStore.currentProject,
    permissionStore.accessRoutes,
  ]);


  const handleChangePushNotification = useCallback(async (checked: boolean) => {

    try {
      setNotificationLoading?.(true);
      await OneSignal.subscribe(checked);
    } catch (error) {
      message.error(errorMessage);
    } finally {
      setNotificationLoading?.(false);
    }
  }, []);


  return (
    <div>
      <AuthProvider>
        <Layout>
          <Sidebar collapsed={collapsed} toggle={toggle} />
          <Layout
            className="site-layout"
            style={{ marginLeft: siteLayoutMarginLeft }}
          >
            <Navbar collapsed={collapsed}
              toggle={toggle}
              handleChangePushNotification={handleChangePushNotification}
              isSubscribed={isSubscribed}
              oneSignalLoading={oneSignalLoading}
            />
            <Content
              className={`site-layout-background`}
              style={{
                margin: "0px",
                padding: 24,
              }}
            >
              {/* <Outlet /> */}
              {/* <TransitionGroup component={null}>
                <CSSTransition
                  key={location.pathname}
                  classNames="page"
                  timeout={300}
                  unmountOnExit
                > */}
              <Suspense
                fallback={
                  <div
                    style={{
                      minHeight: "calc(100vh - 64px)",
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    <Spin />
                  </div>
                }
              >
                <Outlet />
              </Suspense>
              {/* </CSSTransition> */}
              {/* </TransitionGroup> */}
            </Content>
          </Layout>
        </Layout>
      </AuthProvider>
      <Modal
        open={openChooseProjectModal}
        centered
        onCancel={() => {
          setOpenChooseProjectModal(false);
        }}
        footer={null}
        closeIcon={false}
        maskClosable={false}
      >
        <img
          src={darkMode ? settings.logoWhite : settings.logo}
          alt=""
          className="block w-fit mx-auto h-[52px] object-contain cursor-pointer"
        />
        <div className="text-center font-bold text-[24px] my-4">
          Bạn cần chọn dự án để tiếp tục
        </div>
        <CustomButton
          block
          onClick={() => {
            appStore.lastUrl = location.pathname;
            navigate(`/project/${PermissionNames.projectList}`);
            setOpenChooseProjectModal(false);
          }}
        >
          Chọn dự án
        </CustomButton>
      </Modal>
    </div>
  );
};
