import { Dictionary } from "./dictionary";
import { Project } from "./project";
import { ProjectItemDetail } from "./projectItemDetail";

export interface ProjectItem {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  code: string;
  name: string;
  area: string; // diện tích
  isActive: boolean;
  note: string;
  floors: number; // số tầng
  project: Project;
  projectItemDetails: ProjectItemDetail[];
}
