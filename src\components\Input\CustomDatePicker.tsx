import React from "react";
import { DatePicker } from "antd";
import { useTheme } from "context/ThemeContext";
import "./CustomInput.scss";
import clsx from "clsx";
import type { Dayjs } from "dayjs";

export type CustomDatePickerProps = {
  label?: string;
  required?: boolean;
  value?: Dayjs;
  onChange?: (date: Dayjs | null, dateString: string | string[]) => void;
  placeholder?: string;
  error?: string;
  disabled?: boolean;
  className?: string;
  status?: "normal" | "error" | "disabled";
  classNameContainer?: string;
  allowClear?: boolean;
  format?: string;
  showTime?: boolean;
  height?: string | number;
  size?: "small" | "middle" | "large";
};

const CustomDatePicker: React.FC<CustomDatePickerProps> = ({
  label = "",
  required = false,
  value,
  onChange,
  placeholder = "Chọn ngày...",
  error,
  disabled = false,
  className = "",
  status: propStatus,
  classNameContainer,
  allowClear = true,
  format = "DD/MM/YYYY",
  showTime = false,
  height,
  size = "large",
}) => {
  const { darkMode } = useTheme();

  const getInputStatus = () => {
    if (propStatus) return propStatus;
    if (error) return "error";
    if (disabled) return "disabled";
    return "normal";
  };

  const status = getInputStatus();
  const inputClassName = `custom-input custom-input-${status} ${
    darkMode ? "dark" : ""
  } ${className}`;

  const handleChange = (date: Dayjs | null, dateString: string | string[]) => {
    if (onChange) {
      onChange(date, dateString);
    }
  };

  const customStyle: React.CSSProperties = {
    width: "100%",
    ...(height && { height: typeof height === 'number' ? `${height}px` : height }),
  };

  return (
    <div className={clsx("custom-input-container", classNameContainer)}>
      {label && (
        <div className="custom-input-label">
          {label} {required && <span className="custom-input-required">*</span>}
        </div>
      )}
      <DatePicker
        getPopupContainer={() =>
          document.getElementById("App") as HTMLElement
        }
        size={size}
        value={value}
        onChange={handleChange}
        placeholder={placeholder}
        disabled={disabled}
        className={inputClassName}
        status={error ? "error" : undefined}
        allowClear={allowClear}
        format={format}
        showTime={showTime}
        style={customStyle}
      />
      {error && <div className="custom-input-error-message">{error}</div>}
    </div>
  );
};

export default CustomDatePicker; 