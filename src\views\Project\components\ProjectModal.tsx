import {
  Col,
  Form,
  message,
  Modal,
  <PERSON>,
  <PERSON><PERSON>,
  Select,
  DatePicker,
} from "antd";
import { Rule } from "antd/lib/form";
import { projectApi } from "api/project.api";
import { companyApi } from "api/company.api";
import { staffApi } from "api/staff.api";
import CustomInput from "components/Input/CustomInput";
import CustomSelect from "components/Input/CustomSelect";
import CustomDatePicker from "components/Input/CustomDatePicker";
import React, { useImperativeHandle, useState, useEffect } from "react";
import { ModalStatus } from "types/modal";
import { Project } from "types/project";
import { Company } from "types/company";
import { Staff } from "types/staff";
import dayjs from "dayjs";

const rules: Rule[] = [{ required: true }];

export interface projectModal {
  handleCreate: () => void;
  handleUpdate: (project: Project) => void;
}
interface projectModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
}

export const projectModal = React.forwardRef(
  ({ onClose, onSubmitOk }: projectModalProps, ref) => {
    const [form] = Form.useForm<Project>();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");
    const [selectedProject, setSelectedProject] = useState<Project>();
    const [companies, setCompanies] = useState<Company[]>([]);
    const [staffs, setStaffs] = useState<Staff[]>([]);
    const [gettingLocation, setGettingLocation] = useState(false);
    const [selectedInvestor, setSelectedInvestor] = useState<Company | null>(null);

    // Fetch companies for investor dropdown
    const fetchCompanies = async () => {
      try {
        const { data } = await companyApi.findAll({ limit: 100 });
        setCompanies(data.companies || data);
      } catch (error) {
        console.error("Failed to fetch companies:", error);
      }
    };

    // Fetch staffs for engineer dropdown
    const fetchStaffs = async () => {
      try {
        const { data } = await staffApi.findAll({ limit: 100 });
        setStaffs(data.staffs || data);
      } catch (error) {
        console.error("Failed to fetch staffs:", error);
      }
    };

    // Get current location using GPS
    const getCurrentLocation = async () => {
      if (!navigator.geolocation) {
        message.error("Trình duyệt không hỗ trợ định vị GPS");
        return;
      }

      setGettingLocation(true);
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          const { latitude, longitude } = position.coords;
          try {
            // You can integrate with a reverse geocoding service here
            // For now, just set coordinates as location
            const locationText = `Tọa độ: ${latitude.toFixed(
              6
            )}, ${longitude.toFixed(6)}`;
            const currentValues = form.getFieldsValue();
            form.setFieldsValue({ ...currentValues, location: locationText });
            message.success("Đã lấy vị trí hiện tại thành công");
          } catch (error) {
            message.error("Không thể lấy thông tin địa chỉ từ tọa độ");
          } finally {
            setGettingLocation(false);
          }
        },
        (error) => {
          setGettingLocation(false);
          message.error("Không thể lấy vị trí hiện tại");
        },
        { enableHighAccuracy: true, timeout: 10000, maximumAge: 60000 }
      );
    };

    // Handle investor selection
    const handleInvestorChange = (companyId: number) => {
      const selectedCompany = companies.find(company => company.id === companyId);
      setSelectedInvestor(selectedCompany || null);
    };

    useEffect(() => {
      if (visible) {
        fetchCompanies();
        fetchStaffs();
      }
    }, [visible]);

    useImperativeHandle<any, projectModal>(
      ref,
      () => ({
        handleCreate() {
          form.resetFields();
          setSelectedInvestor(null);
          setVisible(true);
          setStatus("create");
        },
        handleUpdate(project: Project) {
          setSelectedProject(project);
          const formData = {
            ...project,
            startAt: project.startAt ? dayjs.unix(project.startAt) : undefined,
            endAt: project.endAt ? dayjs.unix(project.endAt) : undefined,
          };
          form.setFieldsValue(formData as any);
          
          // Set selected investor based on existing data
          if (project.companyIds && project.companyIds.length > 0) {
            const investorId = project.companyIds[0];
            const investor = companies.find(company => company.id === investorId);
            setSelectedInvestor(investor || null);
          }
          
          setVisible(true);
          setStatus("update");
        },
      }),
      [companies]
    );

    const createData = async () => {
      const valid = await form.validateFields();
      const formValues = form.getFieldsValue() as any;

      // Prepare payload according to API structure
      const payload = {
        projectCategoryId: 0,
        parentId: 0,
        taskTemplateId: 0,
        companyIds: formValues.investorId ? [formValues.investorId] : [],
        memberShip: [],
        engineerIds: formValues.engineerIds || [],
        project: {
          name: formValues.name,
          code: formValues.code,
          description: formValues.description,
          startAt: formValues.startAt ? dayjs(formValues.startAt).unix() : 0,
          endAt: formValues.endAt ? dayjs(formValues.endAt).unix() : 0,
          isActive: true,
          location: formValues.location,
          budget: formValues.budget ? Number(formValues.budget) : 0,
          investor: selectedInvestor?.name || "",
        },
      };

      setLoading(true);
      try {
        const res: any = await projectApi.create(payload);
        console.log("Response:", res);

        if (res.status === true) {
          message.success("Tạo dự án thành công!");
          onClose();
          onSubmitOk();
          setVisible(false);
        } else {
          message.error(res.message || "Có lỗi xảy ra khi tạo dự án");
        }
      } catch (error: any) {
        message.error(
          error?.response?.data?.message || "Có lỗi xảy ra khi tạo dự án"
        );
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const valid = await form.validateFields();
      const formValues = form.getFieldsValue() as any;

      // Prepare payload according to API structure
      const payload = {
        projectCategoryId: 0,
        parentId: 0,
        taskTemplateId: 0,
        companyIds: formValues.investorId ? [formValues.investorId] : [],
        memberShip: [],
        engineerIds: formValues.engineerIds || [],
        project: {
          name: formValues.name,
          code: formValues.code,
          description: formValues.description,
          startAt: formValues.startAt ? dayjs(formValues.startAt).unix() : 0,
          endAt: formValues.endAt ? dayjs(formValues.endAt).unix() : 0,
          isActive: true,
          location: formValues.location,
          budget: formValues.budget ? Number(formValues.budget) : 0,
          investor: selectedInvestor?.name || "",
        },
      };

      setLoading(true);
      try {
        const res: any = await projectApi.update(
          selectedProject?.id || 0,
          payload
        );
        console.log("Update Response:", res);

        if (res.status === true) {
          message.success("Cập nhật dự án thành công!");
          onClose();
          onSubmitOk();
          setVisible(false);
        } else {
          message.error(res.message || "Có lỗi xảy ra khi cập nhật dự án");
        }
      } catch (error: any) {
        message.error(
          error?.response?.data?.message || "Có lỗi xảy ra khi cập nhật dự án"
        );
      } finally {
        setLoading(false);
      }
    };

    // Prepare company options for investor dropdown
    const companyOptions = companies.map((company) => ({
      label: company.name,
      value: company.id,
    }));

    // Prepare staff options
    const staffOptions = staffs.map((staff) => ({
      label: staff.fullName,
      value: staff.id,
    }));

    return (
      <Modal
        className="footer-full"
        onCancel={() => {
          onClose?.();
          setVisible(false);
        }}
        centered
        visible={visible}
        title={status == "create" ? "Tạo dự án mới" : "Cập nhật dự án"}
        style={{ top: 20 }}
        width={700}
        confirmLoading={loading}
        cancelButtonProps={{ style: { display: "none" } }}
        okText={status == "create" ? "Tạo" : "Cập nhật"}
        onOk={() => {
          status == "create" ? createData() : updateData();
        }}
        getContainer={() => {
          return document.getElementById("App") as HTMLElement;
        }}
      >
        <Form layout="vertical" form={form}>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item name="name" label="Tên dự án" rules={rules}>
                <CustomInput placeholder="Nhập tên dự án" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="code" label="Mã dự án" rules={rules}>
                <CustomInput placeholder="Mã dự án nếu không nhập hệ thống sẽ tự tạo" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item name="budget" label="Ngân sách dự án" rules={rules}>
                <CustomInput placeholder="Nhập ngân sách dự án" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item label="Ngày khởi công" name="startAt" rules={rules}>
                <CustomDatePicker 
                  placeholder="Chọn ngày khởi công"
                />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                label="Ngày dự kiến hoàn thành"
                name="endAt"
                rules={rules}
              >
                <CustomDatePicker 
                  placeholder="Chọn ngày dự kiến hoàn thành"
                />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item
                label="Địa điểm thi công"
                name="location"
                rules={rules}
              >
                <div style={{ display: "flex", gap: "8px" }}>
                  <div style={{ flex: 1 }}>
                    <CustomInput placeholder="Nhập địa điểm thi công hoặc sử dụng GPS" />
                  </div>
                  {/* <Button 
                    type="default" 
                    loading={gettingLocation}
                    onClick={getCurrentLocation}
                    style={{ minWidth: '100px' }}
                  >
                    {gettingLocation ? "Đang lấy..." : "Lấy GPS"}
                  </Button> */}
                </div>
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item
                label="Chủ đầu tư"
                name="investorId"
                rules={rules}
              >
                <CustomSelect
                  placeholder="Chọn chủ đầu tư"
                  options={companyOptions}
                  onChange={handleInvestorChange}
                />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item
                label="Kỹ sư tham gia"
                name="engineerIds"
                rules={rules}
              >
                <CustomSelect
                  placeholder="Chọn kỹ sư tham gia (có thể chọn nhiều)"
                  mode="multiple"
                  options={staffOptions}
                />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item label="Mô tả" name="description" rules={rules}>
                <CustomInput
                  type="textarea"
                  rows={3}
                  placeholder="Nhập mô tả dự án"
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
);
