import { Select } from "antd";
import { DefaultOptionType } from "antd/es/select";
import { SelectProps } from "antd/lib";
import { useColor } from "hooks/useColor";
import { useMaterial } from "hooks/useMaterial";
import { useMaterialGroup } from "hooks/useMaterialGroup";
import { debounce, uniqBy } from "lodash";
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
} from "react";
import { Material } from "types/material";
import { MaterialGroup } from "types/materialGroup";
import { QueryParams2 } from "types/query";

type CustomFormItemProps = {
  value?: number;
  initQuery?: QueryParams2;
  disabled?: boolean;
  selectedColor?: Material[];
  multiple?: boolean;
  onChange?: (value: any) => void;
  selectProps?: SelectProps;
  initOptionItem?: Material | Material[];
  valueIsOption?: boolean;
  allowClear?: boolean;
  placeholder?: string;
  forFilterMaterial?: string;
  isGetMaterialCode?: boolean;
};

export interface MaterialSelector {
  refresh(): void;
}

/**
 * A demo that changes it's number value on click.
 * @param value initialValue passed by parent Form.Item.
 * @param onChange a callback for Form.Item to read its child component's value.
 * @constructor
 */
export const MaterialSelector = forwardRef(
  (
    {
      value,
      onChange,
      initQuery,
      disabled,
      multiple = false,
      selectedColor,
      initOptionItem,
      valueIsOption,
      selectProps,
      allowClear = true,
      placeholder = "Chọn NVL",
      forFilterMaterial,
      isGetMaterialCode = false,
    }: CustomFormItemProps,
    ref
  ) => {
    // console.log("material nhận đc là", forFilterMaterial);
    const { materials, total, loading, fetchData, query } = useMaterial({
      initQuery: {
        page: 1,
        limit: 90,
        type: forFilterMaterial,
        ...initQuery,
      },
    });

    useImperativeHandle<any, MaterialSelector>(
      ref,
      () => ({
        refresh() {
          fetchData();
        },
      }),
      []
    );

    useEffect(() => {
      if (forFilterMaterial !== undefined) {
        query.type = forFilterMaterial;
      }
      fetchData();
    }, [forFilterMaterial]);

    const debounceSearch = useCallback(
      debounce((keyword) => {
        query.search = keyword;
        fetchData();
      }, 300),
      [query]
    );

    const options = useMemo(() => {
      let data = [...materials];
      if (initOptionItem) {
        if ((initOptionItem as Material[])?.length) {
          data = data.concat(initOptionItem);
        } else {
          data.push(initOptionItem as Material);
        }
      }

      return uniqBy(data, (data) => data.id);
    }, [materials, initOptionItem]);

    return (
      <Select<any, DefaultOptionType>
        mode={multiple ? "multiple" : undefined}
        disabled={disabled}
        loading={loading}
        style={{ width: "100%", minWidth: 200 }}
        allowClear={allowClear}
        onSearch={debounceSearch}
        showSearch
        placeholder={placeholder}
        filterOption={false}
        value={value}
        onChange={(v, opts) => {
          if (v === undefined || v.length == 0) {
            debounceSearch(v);
          }
          if (valueIsOption) {
            if (opts instanceof Array) {
              onChange?.(opts?.map((v) => v.item));
            } else {
              onChange?.(opts?.item);
            }
          } else {
            onChange?.(v);
          }
        }}
        {...selectProps}
      >
        {options?.map((item) => (
          <Select.Option item={item} value={item.id} key={item.id}>
            <div className="flex items-center gap-2">
              <span>{isGetMaterialCode ? item.code : item.name}</span>
            </div>
          </Select.Option>
        ))}
      </Select>
    );
  }
);
