@mixin status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.approve-template-card {
  .content-card-header {
    padding: 0;
  }

  .card-body {
    min-height: 400px;
    max-height: calc(100dvh - 550px);
    padding: 0;
    overflow-y: auto;
    padding-top: 16px;

    .approve-process-container {
      display: flex;
      margin-bottom: 20px;
      position: relative;
      align-items: flex-start;

      .process-icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        border: 2px solid #bfc7d1;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 18px;
        background: #fff;
        color: #333;
        margin-right: 16px;

        .approve {
          background-color: var(--color-project-done);
          @include status-icon;
        }

        .reject {
          background-color: var(--color-accent);
          opacity: 0.2;
          @include status-icon;
        }

        .pending {
          background-color: var(--color-neutral-n0);
          @include status-icon;
        }

        .current {
          background-color: var(--color-logo);
          border: 2px solid var(--color-neutral-n0);
          z-index: 999;
          @include status-icon;
          aspect-ratio: 1;

          span {
            color: var(--color-neutral-n0);
            font-weight: 700;
            font-size: 12px;
            text-align: center;
          }
        }

        .icon {
          position: absolute;

          span {
            color: var(--color-neutral-n7);
            font-weight: 700;
            font-size: 12px;
            text-align: center;
          }
        }
      }

      .current-background {
        position: absolute;
        top: -6px;
        left: -6px;
        width: calc(100% + 12px);
        height: calc(100% + 12px);
        opacity: 0.1;
        border-radius: 50%;
        background-color: var(--color-logo);
      }

      .process-content {
        flex: 1;
        min-width: 0;
        margin-left: 8px;

        .process-content-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 4px;
          gap: 8px;
          min-height: 32px;

          .title {
            font-weight: 700;
            font-size: 13px;
            color: var(--color-logo);
            white-space: nowrap;
            flex-shrink: 0;
          }

          // .ant-form-item {
          //   margin-bottom: 16px !important;
          // }
        }

        .time {
          font-size: 12px;
          color: var(--color-neutral-n5);
          white-space: nowrap;
          flex-shrink: 0;
        }

        .process-content-staff {
          display: flex;
          margin-bottom: 4px;

          .staff-name {
            font-size: 13px;
            color: var(--color-logo);
            margin-left: 4px;
          }
        }

        .note {
          font-size: 13px;
          color: var(--color-logo);
          margin-top: 4px;
        }

        .ant-form-item {
          flex: 1;
        }
      }
    }

    .line {
      position: absolute;
      left: 16px;
      top: 42px;
      width: 1px;
      height: calc(100% - 28px);
      background-color: var(--color-neutral-n3);
    }
  }
}

.modal-approve-process {
  .ant-modal-content {
    padding: 24px;
  }
  .modal-header {
    padding: 8px 20px;
    background: transparent !important;

    h3 {
      color: var(--color-logo);
      font-weight: 700;
      margin: 0;
    }
  }
  .modal-select {
    display: flex;
    column-gap: 8px;
    padding: 8px 20px;
    border-bottom: none !important;

    .ant-select {
      flex: 1;
    }
  }

  .approve-process-card {
    .content-card-header {
      padding-bottom: 0px;
      padding-top: 8px;
    }

    .line {
      left: 18px;
    }
  }
}

.approve-process-form-row {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 4px;
  width: 100%;
}

.approve-process-staff-row {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  gap: 8px;
}

.approve-process-staff-row + .approve-process-staff-row {
  margin-top: 8px;
}

.approve-process-staff-row {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 8px;
  overflow: hidden; // Thêm dòng này
}

.approve-process-staff2 {
  flex: 1;
  min-width: 0;
  max-width: 100%;
  margin-bottom: 0;
}

.approve-process-staff2 .ant-select,
.approve-process-staff1 .ant-select {
  width: 100% !important;
  min-width: 0 !important;
}

.approve-process-placeholder {
  width: 32px;
  display: inline-block;
}

.approve-process-staff2 .ant-select-selector,
.approve-process-staff1 .ant-select-selector {
  width: 100% !important;
  min-width: 0 !important;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.modal-approve-process .cta-button,
.modal-approve-process .ant-btn {
  border-radius: 8px !important;
  height: 36px !important;
  min-width: 80px !important;
  font-size: 14px !important;
}
.approve-process-main-box {
  margin: 12px 0;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  // padding: 32px 32px 24px 32px;
  padding: 16px 32px;
  background: #fff !important;
}
.approval-template-selector-box {
  border-radius: 8px;
  margin-bottom: 24px;
  background: #fff !important;
}
