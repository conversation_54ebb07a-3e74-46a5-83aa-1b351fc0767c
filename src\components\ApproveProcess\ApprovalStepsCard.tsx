// components/ApprovalSteps/ApprovalStepsCard.tsx
import React, { useEffect, useMemo, useRef, useState } from "react";
import {
  Card,
  Avatar,
  Tooltip,
  Button,
  Modal,
  Input,
  Space,
  Form,
  FormListFieldData,
  Spin,
  UploadFile,
  Dropdown,
  message,
  Tag,
  Select,
} from "antd";
import PencilIcon from "assets/svgs/PencilIcon";
import { useTheme } from "context/ThemeContext";
import "./ApproveProcess.scss";
import { PlusIcon } from "assets/svgs/PlusIcon";
import { $url } from "utils/url";
import CustomButton from "components/Button/CustomButton";
import { ApprovalListStatus } from "types/approvalList";
import { rules } from "utils/validateRule";
import DeleteIcon from "assets/svgs/DeleteIcon";
import { approvalTemplateApi } from "api/approvalTemplate.api";
import {
  ApprovalTemplate,
  ApprovalTemplateMode,
  ApprovalTemplateName,
  ApprovalTemplateType,
} from "types/approvalTemplate";
import TextArea from "antd/es/input/TextArea";
import { CheckIcon } from "assets/svgs/CheckIcon";
import { CloseIcon } from "assets/svgs/CloseIcon";
import { ColorThemes } from "utils/theme";
import { userStore } from "store/userStore";
import { CommentUploadMultiple } from "components/Upload/CommentUploadFile/CommentUploadMultiple";
import { StepType, StepTypeTrans } from "types/approvalStep";
import { MembershipSelector } from "components/Selector/MembershipSelector";
import { MemberShip } from "types/memberShip";
import { Staff } from "types/staff";
import { BMDTextArea } from "components/TextArea/BMDTextArea";
import { ArrowDownIcon } from "assets/svgs/ArrowDownIcon";
import { MemberShipApproval } from "types/memberShipApproval";
import { ApprovalListDetail } from "types/approvalListDetail";
import { Role } from "types/role";
import ApprovalTemplateStepItem from "views/ApproveProcessPage/components/ApprovalTemplateStepItem";
import { observer } from "mobx-react";
import { checkRoles } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { PermissionNames } from "types/PermissionNames";
import clsx from "clsx";
import { useWatch } from "antd/es/form/Form";
import { CheckCircleFilled, CloseCircleFilled } from "@ant-design/icons";
import { findCurrentApprovalStep } from "./approveUtil";
import { unixToFullDate } from "utils/dateFormat";

const initSteps = () => [
  {
    note: "",
    name: StepType.Create,
    status: ApprovalListStatus.Pending,
  },
  {
    // memberShipId: undefined,
    // memberShip2Id: undefined,
    note: "",
    name: "",
    // status: ApprovalListStatus.Pending,
    approvers: [{}],
    isAllApproval: false, // Mặc định là một người duyệt
  },
  // {
  //   memberShipId: undefined,
  //   note: "",
  //   name: StepType.Publish,
  //   status: ApprovalListStatus.Pending,
  // },
];

export interface ApproveData {
  file: string;
  note: string;
  approvalListId: number;
  approvalListDetailId?: number;
  staffId?: number;
}

interface ApprovalStepsCardProps {
  steps: StepItem[];
  onSelectStep?: (step: StepItem[]) => void;
  onRemove?: (data: number[]) => void;
  onApprove?: (data: ApproveData) => Promise<void>;
  onReject?: (data: ApproveData) => Promise<void>;
  loading?: boolean;
  editable?: boolean;
  templateName?: string;
  templateDescription?: string;
  templateType?: ApprovalTemplateType;
  processMode?: "simple" | "complex";
  isShowActionButton?: boolean;
  onChangeTemplateInfo?: (info: {
    name: string;
    description: string;
    type: string;
  }) => void;
  onSubmit?: (data: {
    name: string;
    description: string;
    type: string;
    steps: StepItem[];
    removeList: number[];
  }) => void;
}

export interface StepItem {
  id?: number;
  staffId?: number;
  staff2Id?: number;
  note: string;
  position: number;
  name: string;
  actionText?: string;
  statusText?: string;
  statusColor?: string;
  isAllApproval?: boolean; // true: tất cả người duyệt phải duyệt, false: chỉ cần một người duyệt
  time?: string;
  staff?: Staff;
  staff2?: Staff;
  memberShipId?: number;
  memberShip2Id?: number;
  memberShip?: MemberShip;
  memberShip2?: MemberShip;
  status?: ApprovalListStatus;
  type?: string;
  approvers: {
    id?: number;
    roleId?: number;
    memberShipId?: number;
    role?: Role;
    memberShip?: MemberShip;
    status?: ApprovalListStatus;
    approveAt?: number;
    rejectAt?: number;
    note?: string;
  }[];
}

export const ApprovalStepsCard = observer(
  ({
    steps,
    loading = false,
    onSelectStep,
    onRemove,
    onApprove,
    onReject,
    editable = false,
    templateName,
    templateDescription,
    templateType,
    isShowActionButton,
    onChangeTemplateInfo,
    onSubmit,
  }: ApprovalStepsCardProps) => {
    // console.log({ steps });

    const { haveViewAllPermission } = checkRoles(
      {
        viewAll: PermissionNames.indicativeViewAll,
      },
      permissionStore.permissions
    );

    const { darkMode } = useTheme();
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [formSteps] = Form.useForm<{
      steps: StepItem[];
      mode: ApprovalTemplateMode;
    }>();
    const [formTemplate] = Form.useForm<{
      name: string;
      description: string;
      type: string;
    }>();
    const [formReject] = Form.useForm<{
      note: string;
      fileList: UploadFile[];
      revertApprovalListId?: number;
    }>();

    const [removeList, setRemoveList] = useState<number[]>([]);
    const approvalTemplate = useRef({ name: "", description: "", type: "" });
    const [selectedApprovalTemplate, setSelectedApprovalTemplate] =
      useState<ApprovalTemplate | null>(null);
    const [showSaveTemplate, setShowSaveTemplate] = useState(false);
    const [templateInfo, setTemplateInfo] = useState<{
      name: string;
      description: string;
      type?: string;
    }>({ name: "", description: "", type: "" });

    const [currentStep, setCurrentStep] = useState<
      StepItem & { isLastStep: boolean }
    >();

    const getCurrentProjectId = () => {
      try {
        const appStoreStr = localStorage.getItem("AppStore");
        if (!appStoreStr) return undefined;
        const appStore = JSON.parse(appStoreStr);
        return appStore?.currentProject?.id;
      } catch {
        return undefined;
      }
    };

    useEffect(() => {
      if (isModalVisible && selectedApprovalTemplate?.id) {
        approvalTemplateApi.findOne(selectedApprovalTemplate.id).then((res) => {
          setTemplateInfo({
            name: res.data.name,
            description: res.data.description,
            type: res.data.type,
          });
          formTemplate.setFieldsValue({
            name: res.data.name,
            description: res.data.description,
            type: res.data.type,
          });
        });
      }
    }, [isModalVisible, selectedApprovalTemplate?.id]);

    useEffect(() => {
      if (steps.length) {
        let { curr, isLastStep } = findCurrentApprovalStep(steps);
        if (curr) {
          setCurrentStep({ ...curr, isLastStep });
        }
      }
    }, [steps]);

    const showEditButton = useMemo(() => {
      return steps.some(
        (step) =>
          step.status == ApprovalListStatus.Pending &&
          step.name.includes(StepType.Publish)
      );
    }, [steps]);

    const handleEdit = () => {
      setIsModalVisible(true);
      if (steps.length) {
        formSteps.setFieldsValue({
          mode: ApprovalTemplateMode.Complex,
          steps: steps.map((item) => ({
            ...item,
            // memberShipId: item.memberShip?.id,
            // memberShip: item.memberShip, // Đảm bảo là object memberShip
            // memberShip2Id: item.memberShip2?.id,
            // memberShip2: item.memberShip2,
          })),
        });
      } else {
        formSteps.setFieldsValue({ steps: initSteps() });
      }

      formTemplate.setFieldsValue({
        name: templateName || templateInfo.name || "",
        description: templateDescription || templateInfo.description || "",
        type: templateType || "",
      });
      setRemoveList([]);
    };

    const handleCheckApprover = (
      approvalTemplateDetails: {
        name: string;
        approvalListDetails: { roleId?: number; memberShipId?: number }[];
      }[]
    ) => {
      let errorSteps = [];
      for (let i = 0; i < approvalTemplateDetails.length; i++) {
        let isValid = true;
        const step = approvalTemplateDetails[i];

        if (step.name == StepType.Create) continue;

        const approvers = step.approvalListDetails || [];
        if (approvers.length === 0) {
          isValid = false;
        } else {
          let haveNullMemberShip = false;
          approvers.forEach((approver) => {
            if (!approver.memberShipId) {
              haveNullMemberShip = true;
            }
          });
          if (haveNullMemberShip) {
            isValid = false;
          }
        }
        if (!isValid) {
          errorSteps.push(i + 1);
        }
      }

      if (errorSteps.length > 0) {
        message.error(
          `Vui lòng chọn người duyệt cho bước ${errorSteps.join(", ")}`
        );
        return false;
      }
      return true;
    };

    const handleSubmit = async () => {
      await formSteps.validateFields();
      const { name, description, type } = formTemplate.getFieldsValue();

      approvalTemplate.current = { name, description, type };

      onChangeTemplateInfo?.({ name, description, type });

      const { steps: newSteps } = formSteps.getFieldsValue();

      // Không cần convert gì nữa, chỉ lấy memberShipId/memberShip2Id
      const stepsForSubmit = (newSteps || []).map((step, idx) => ({
        ...step,
        position: idx,
        approvalListDetails: step.approvers || [],
      }));

      if (handleCheckApprover(stepsForSubmit)) {
        onSubmit?.({
          name,
          description,
          type,
          steps: stepsForSubmit,
          removeList,
        });

        // Nếu có quy trình và không có quy trình cũ và không có mẫu thi hiện luu quy trình mẫu
        if (
          newSteps.length &&
          !showSaveTemplate &&
          !steps.length &&
          !selectedApprovalTemplate?.id
        ) {
          setShowSaveTemplate(true);
        } else if (!!selectedApprovalTemplate?.id) {
          setShowSaveTemplate(false);
        }

        const dataSubmit = stepsForSubmit;

        onSelectStep?.(stepsForSubmit);
        onRemove?.(removeList);
        setIsModalVisible(false);
        // check neu chon quy trinh mau va co thay doi quy trinh thi hoi cap nhat lai
        if (!!selectedApprovalTemplate?.id && checkDiffStepFromTemplate()) {
          await handleUpdateApprovalTemplate();
        }
        setSelectedApprovalTemplate(null);
      }
    };

    const checkDiffStepFromTemplate = () => {
      const { steps } = formSteps.getFieldsValue();
      const templateSteps = selectedApprovalTemplate?.approvalTemplateDetails;
      // Nếu có thay đổi trong quy trình bao gồm thay đổi người duyệt, ghi chú, xóa quy trình thì trả về true
      if (templateSteps?.length !== steps.length) return true;
      return steps.some(
        (step) =>
          step.memberShipId !==
            templateSteps?.find((s) => s.id === step.id)?.memberShip?.id ||
          step.note !== templateSteps?.find((s) => s.id === step.id)?.note
      );
    };

    const handleClose = () => {
      setIsModalVisible(false);
      setRemoveList([]);
    };

    const getDataSubmit = () => {
      const { steps } = formSteps.getFieldsValue();
      const payload = {
        approvalTemplateDetails: steps.map((item, i) => ({
          id: item.id,
          name: item.name,
          type: item.type,
          position: i,
          note: item.note,
          memberShipId: item.memberShipId,
          memberShip2Id: item.memberShip2Id,
        })),
        approvalTemplate: {
          name: approvalTemplate.current.name,
          description: approvalTemplate.current.description,
          type: approvalTemplate.current.type,
        },
        projectId: getCurrentProjectId(),
      };

      return payload;
    };

    const handleUpdateApprovalTemplate = async (): Promise<void> => {
      return new Promise((resolve, reject) => {
        Modal.confirm({
          title: `Cập nhật quy trình mẫu`,
          getContainer: () => {
            return document.getElementById("App") as HTMLElement;
          },
          icon: null,
          content: (
            <>
              <div>
                Bạn có muốn cập nhật quy trình mới này vào quy trình mẫu{" "}
                {selectedApprovalTemplate?.name} không?
                <br />
                Nếu có, quy trình mới này sẽ thay thế quy trình mẫu cũ.
              </div>
            </>
          ),
          footer: (_, { OkBtn, CancelBtn }) => (
            <>
              <CustomButton
                variant="outline"
                className="cta-button"
                onClick={async () => {
                  await formTemplate.validateFields();
                  const { name, description, type } =
                    formTemplate.getFieldsValue(true);
                  approvalTemplate.current = {
                    name: name || "",
                    description: description || "",
                    type: type || "",
                  };

                  await approvalTemplateApi.update(
                    selectedApprovalTemplate!?.id,
                    {
                      ...getDataSubmit(),
                      projectId: getCurrentProjectId(),
                    }
                  );
                  setShowSaveTemplate(false);
                  resolve();
                  Modal.destroyAll();
                }}
              >
                Cập nhật
              </CustomButton>
              <CustomButton
                onClick={() => {
                  resolve();
                  Modal.destroyAll();
                }}
                className="cta-button"
              >
                Không
              </CustomButton>
            </>
          ),
        });
      });
    };

    const handleShowModalConfirm = ({
      title,
      content,
      approvalListId,
      isReject,
      onSubmit,
    }: {
      title: string;
      content: React.ReactNode;
      approvalListId: number;
      isReject?: boolean;
      onSubmit?: (data: ApproveData) => void;
    }) => {
      let previousStep: StepItem[] = [];
      if (isReject) {
        const currentIndex = steps.findIndex((st) => st.id == currentStep?.id);
        if (currentIndex > 0) {
          previousStep = steps.slice(1, currentIndex);
        }
      }
      if (previousStep.length) {
        formReject.setFieldValue(
          "revertApprovalListId",
          previousStep[previousStep.length - 1]?.id
        );
      }

      Modal.confirm({
        title,
        getContainer: () => {
          return document.getElementById("App") as HTMLElement;
        },
        width: 800,
        icon: null,
        content: (
          <>
            <div>{content}</div>
            {_renderFormNote({ isReject, previousStep })}
          </>
        ),
        footer: (_, { OkBtn, CancelBtn }) => (
          <>
            <CustomButton
              variant="outline"
              className="cta-button"
              onClick={() => {
                Modal.destroyAll();
              }}
            >
              Hủy
            </CustomButton>
            <CustomButton
              onClick={async () => {
                await formReject.validateFields();
                const { note, fileList, revertApprovalListId } =
                  formReject.getFieldsValue(true);
                const approvalListDetails = currentStep?.approvers || [];
                const dataSubmit = {
                  approvalListId,
                  approvalListDetailId: approvalListDetails.find(
                    (it) => it.memberShip?.staff?.id == userStore.info?.id
                  )?.id,
                  revertApprovalListId: isReject
                    ? revertApprovalListId
                    : undefined,
                  note,
                  staffId: userStore.info?.id,
                  file: !!fileList?.length ? JSON.stringify(fileList) : "",
                };
                onSubmit?.(dataSubmit);
                Modal.destroyAll();
              }}
              className="cta-button"
            >
              Xác nhận
            </CustomButton>
          </>
        ),
      });
    };

    const handleApproveProcess = (approvalListId: number) => {
      handleShowModalConfirm({
        title: "Duyệt quy trình",
        content:
          "Bạn có muốn duyệt quy trình này? Vui lòng ghi chú lý do duyệt",
        onSubmit: (data) => {
          onApprove?.(data).then(() => {
            formReject.resetFields();
          });
        },
        approvalListId,
      });
    };

    const handleRejectProcess = (approvalListId: number) => {
      // formReject.resetFields();
      handleShowModalConfirm({
        title: "Từ chối quy trình",
        content:
          "Bạn muốn từ chối quy trình này? Vui lòng ghi chú lý do từ chối",
        onSubmit: (data) => {
          onReject?.(data).then(() => {
            formReject.resetFields();
          });
        },
        isReject: true,
        approvalListId,
      });
    };

    const _renderFormNote = ({
      isReject = false,
      previousStep,
    }: {
      isReject?: boolean;
      previousStep?: StepItem[];
    }) => {
      return (
        <Form form={formReject} layout="vertical">
          {isReject && (
            <>
              <Form.Item label="Bước quay lại" name="revertApprovalListId">
                <Select
                  className="!w-[200px]"
                  options={previousStep?.map((step) => ({
                    label: step.name,
                    value: step.id,
                  }))}
                />
              </Form.Item>
            </>
          )}
          <Form.Item name="note" label="Ghi chú" rules={rules}>
            <BMDTextArea placeholder="Nhập ghi chú" />
          </Form.Item>

          <Form.Item noStyle shouldUpdate={true}>
            {({ getFieldValue, setFieldValue }) => (
              <CommentUploadMultiple
                fileList={getFieldValue("fileList") || []}
                onUploadOk={function (fileAttaches: UploadFile[]): void {
                  setFieldValue("fileList", fileAttaches);
                }}
              />
            )}
          </Form.Item>
        </Form>
      );
    };

    const _renderStepStatus = (
      status: ApprovalListStatus,
      index: number,
      indexPending?: number
    ) => {
      switch (status) {
        case ApprovalListStatus.Approved:
          return (
            <div className="process-icon">
              <div className="approve" />
              <div className="icon">
                <CheckIcon />
              </div>
            </div>
          );
        case ApprovalListStatus.Rejected:
          return (
            <div className="process-icon">
              <div className="reject" />
              <div className="icon">
                <CloseIcon />
              </div>
            </div>
          );
        case ApprovalListStatus.Pending:
          if (index == indexPending) {
            return (
              <div className="process-icon">
                <div className="current-background" />
                <div className="current">
                  <span>{index + 1}</span>
                </div>
              </div>
            );
          }
          return (
            <div
              className="process-icon"
              style={{ borderColor: "var(--color-neutral-n3)" }}
            >
              <div className="pending" />
              <div className="icon">
                <span>{index + 1}</span>
              </div>
            </div>
          );
        default:
          return;
      }
    };

    const _renderStepRow = (step: StepItem, index: number, arr: StepItem[]) => {
      // console.log("_renderStepRow:", step);

      // if (step.id == 314) {
      //   debugger;
      // }

      // const findIndexPending = arr.findIndex(
      //   (item) =>
      //     item.status == ApprovalListStatus.Pending &&
      //     item.name != ApprovalTemplateName.Create
      // );
      const findIndexPending = arr.findIndex(
        (item) => item.status == ApprovalListStatus.Pending
      );
      const isReject = arr.some(
        (item) =>
          item.status == ApprovalListStatus.Rejected &&
          item.name != ApprovalTemplateName.Create
      );

      // Chỉ cho phép duyệt khi là người duyệt và chưa có bước nào bị từ chối
      let showAction = true;
      if (step.status == ApprovalListStatus.Approved) {
        // Nếu đã duyệt
        showAction = false;
      } else if (step.name == ApprovalTemplateName.Create) {
        showAction = false;
      } else if (step.status != ApprovalListStatus.Rejected && isReject) {
        // Nếu có bước nào bị từ chối
        showAction = false;
      } else if (
        step.status == ApprovalListStatus.Pending &&
        index != findIndexPending
      ) {
        // Nếu không phải bước đang duyệt
        showAction = false;
      } else {
        //case tạo từ admin
        if (step.staffId && !step.memberShip) {
          if (step.staffId != userStore.info.id) {
            showAction = false;
          }
        } else {
          const ids = [step.memberShipId, step.memberShip2Id].filter(Boolean);
          if (!ids.includes(userStore.info.memberShip?.id)) {
            // Nếu không phải người duyệt
            showAction = false;
          }
        }
      }

      const stepNameApprove =
        step.name == StepType.Approve ? ` lần ${index}` : "";

      // Lấy user hiện tại nếu là bước tạo mới mà không có staff
      const isCreateStep = step.name === StepType.Create;
      const memberShipData = isCreateStep
        ? step.memberShip || step.staff
        : step.memberShip;

      return (
        <div key={index} className="approve-process-container">
          {_renderStepStatus(step.status!, index, findIndexPending)}

          <div className="process-content">
            <div className="process-content-header">
              <div className="title">
                {step.name === StepType.Create
                  ? StepTypeTrans[StepType.Create]
                  : step.name === StepType.Publish
                  ? StepTypeTrans[StepType.Publish]
                  : step.name || ""}

                {/* {stepNameApprove} */}
              </div>
              {step.status == ApprovalListStatus.Approved && (
                <div className="time">{step.time}</div>
              )}
              {/* {showAction && step.id && (
              <Space>
                <Tooltip title="Duyệt">
                  <Button
                    disabled={false}
                    type="text"
                    icon={
                      <CheckIcon
                        size={14}
                        fill={
                          darkMode
                            ? ColorThemes.dark.status.done
                            : ColorThemes.light.status.done
                        }
                      />
                    }
                    onClick={(e) => {
                      e.stopPropagation();
                      handleApproveProcess(step.id!);
                    }}
                  />
                </Tooltip>
                {step.status == ApprovalListStatus.Pending && (
                  <Tooltip title="Từ chối">
                    <Button
                      disabled={false}
                      type="text"
                      icon={
                        <CloseIcon
                          size={14}
                          fill={
                            darkMode
                              ? ColorThemes.dark.accent
                              : ColorThemes.light.accent
                          }
                        />
                      }
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRejectProcess(step.id!);
                      }}
                    />
                  </Tooltip>
                )}
              </Space>
            )} */}
            </div>

            <div
              className="process-content-staff"
              style={{ flexDirection: "column", display: "flex", gap: 4 }}
            >
              {isCreateStep ? (
                <div>
                  <Avatar
                    size={20}
                    style={{ backgroundColor: "#1890ff", flexShrink: 0 }}
                    src={$url(step.staff?.avatar)}
                    // src={
                    //   "staff" in memberShipData &&
                    //   typeof memberShipData.staff?.avatar === "string" &&
                    //   memberShipData.staff.avatar.trim() !== ""
                    //     ? $url(memberShipData.staff.avatar)
                    //     : !("staff" in memberShipData) &&
                    //       typeof memberShipData.avatar === "string" &&
                    //       memberShipData.avatar.trim() !== ""
                    //     ? $url(memberShipData.avatar)
                    //     : undefined
                    // }
                  >
                    {(step.staff?.fullName || "").charAt(0)}
                  </Avatar>
                  <span className="staff-name">
                    {/* {("staff" in memberShipData
                    ? memberShipData.staff?.fullName || memberShipData.name
                    : //@ts-ignore
                      memberShipData.fullName || memberShipData.name) || ""}

                  {memberShipData?.jobTitle?.name
                    ? ` - ${memberShipData.jobTitle.name}`
                    : ""} */}
                    {/* {step.staff?.fullName} - {step.staff?.jobTitle?.name} */}
                    {step.staff?.fullName}
                  </span>
                </div>
              ) : (
                <div className="flex flex-col gap-1">
                  {step.approvers.map((approver, i) => {
                    console.log({ approver });
                    return (
                      <div>
                        <div key={i} className="flex items-center gap-1">
                          <span className="staff-name">
                            {approver.memberShip?.name} -{" "}
                            {approver.memberShip?.phone}
                          </span>
                          {approver.status &&
                          [
                            ApprovalListStatus.Approved,
                            ApprovalListStatus.Rejected,
                          ].includes(approver.status) ? (
                            <>
                              {approver.status ==
                              ApprovalListStatus.Approved ? (
                                <CheckCircleFilled className="text-green-600" />
                              ) : (
                                <CloseCircleFilled className="text-red-600" />
                              )}
                            </>
                          ) : (
                            <></>
                          )}
                        </div>
                        <div className="staff-name !text-gray-400 !text-[11px] leading-5">
                          {approver.approveAt ? (
                            <div>{unixToFullDate(approver.approveAt)}</div>
                          ) : (
                            <></>
                          )}
                          {approver.rejectAt ? (
                            <div>{unixToFullDate(approver.rejectAt)}</div>
                          ) : (
                            <></>
                          )}
                          {approver.note ? <div>{approver.note}</div> : <></>}
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
              {/* {!!step.memberShip2?.id && (
              <div>
                <Avatar
                  size={20}
                  style={{ backgroundColor: "#1890ff", flexShrink: 0 }}
                  src={
                    step.memberShip2?.staff?.avatar &&
                    step.memberShip2.staff.avatar.trim() !== ""
                      ? $url(step.memberShip2.staff.avatar)
                      : undefined
                  }
                >
                  {(
                    step.memberShip2?.staff?.fullName ||
                    step.memberShip2?.name ||
                    ""
                  ).charAt(0)}
                </Avatar>
                <span className="staff-name">
                  {step.memberShip2?.staff?.fullName || step.memberShip2?.name}
                  {step.memberShip2?.jobTitle?.name
                    ? ` - ${step.memberShip2.jobTitle.name}`
                    : ""}
                </span>
              </div>
            )} */}
            </div>

            {!!step.note && <div className="note">{step.note}</div>}
            {step.status == ApprovalListStatus.Rejected && (
              <div className="time">{step.time}</div>
            )}
          </div>

          <div className="line" />
        </div>
      );
    };

    const _renderFormStepRow = (
      record: FormListFieldData,
      index: number,
      length: number,
      remove: (name: number | number[]) => void
    ) => {
      const step = formSteps.getFieldValue("steps")[index] as StepItem;
      console.log(
        `_renderFormStepRow record - step name ${step?.name} - membership ${step?.memberShip?.name} - membership ${step?.memberShip2?.name} - staff ${step?.staff?.fullName}`
      );

      const status = formSteps.getFieldValue(["steps", record.name, "status"]);
      const stepName = formSteps.getFieldValue([
        "steps",
        record.name,
        "name",
      ]) as StepType;
      const isCreateOrPublish =
        stepName === StepType.Create || stepName === StepType.Publish;

      // console.log("_renderFormStepRow", record);
      // console.log("_renderFormStepRow stepName", stepName);

      return (
        <div key={index} className="approve-process-container">
          <Form.Item noStyle shouldUpdate={true}>
            {({ getFieldValue }) =>
              _renderStepStatus(
                getFieldValue(["steps", record.name, "status"]),
                index
              )
            }
          </Form.Item>

          <div className="process-content">
            <div
              className="process-content-header"
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
              }}
            >
              <Form.Item noStyle shouldUpdate={true}>
                {() =>
                  isCreateOrPublish ? (
                    <div className="title">
                      {StepTypeTrans[stepName as StepType]}
                    </div>
                  ) : (
                    <Form.Item
                      name={[record.name, "name"]}
                      rules={[
                        { required: true, message: "Nhập tên bước duyệt" },
                      ]}
                      style={{ marginBottom: 0 }}
                    >
                      <Input
                        placeholder="Nhập tên bước duyệt"
                        disabled={status !== ApprovalListStatus.Pending}
                      />
                    </Form.Item>
                  )
                }
              </Form.Item>

              <div style={{ display: "flex", gap: 8 }}>
                {index > 1 &&
                  index < length - 1 &&
                  status === ApprovalListStatus.Pending && (
                    <Tooltip title="Xóa bước này">
                      <Button
                        type="text"
                        icon={<DeleteIcon />}
                        onClick={() => {
                          const id = formSteps.getFieldValue([
                            "steps",
                            record.name,
                            "id",
                          ]);
                          remove(record.name);
                          if (id) {
                            setRemoveList((prev) => [...prev, id]);
                          }
                        }}
                      />
                    </Tooltip>
                  )}
              </div>
            </div>

            <div
              style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "flex-start",
                marginBottom: "4px",
                width: "100%",
              }}
            >
              <div className="approve-process-form-row">
                {/* Dòng 1: Input nhân viên 1 + nút + */}
                <div className="approve-process-staff-row">
                  <Form.Item
                    name={[record.name, "memberShipId"]}
                    rules={stepName === StepType.Create ? [] : rules}
                    className="approve-process-staff1"
                  >
                    {stepName === StepType.Create ? (
                      <span
                        style={{
                          display: "inline-block",
                          minHeight: 32,
                          minWidth: 200,
                          lineHeight: "32px",
                          padding: "0 12px",
                          background: "#f5f5f5",
                          borderRadius: 6,
                          border: "1px solid #d9d9d9",
                          color: "#222",
                        }}
                      >
                        {step?.staff?.fullName ||
                          step.staff?.fullName ||
                          "Người tạo"}
                      </span>
                    ) : (
                      <MembershipSelector
                        disabled={status != ApprovalListStatus.Pending}
                        value={formSteps.getFieldValue([
                          "steps",
                          record.name,
                          "memberShipId",
                        ])}
                        onChange={(val, memberShips) => {
                          formSteps.setFieldValue(
                            ["steps", record.name, "memberShipId"],
                            val
                          );
                          // Nếu muốn lưu object để hiển thị avatar, có thể lưu thêm vào memberShip (không ảnh hưởng validate)
                          const obj = memberShips.find((m) => m.id === val);
                          // console.log("change value obj:", obj);

                          formSteps.setFieldValue(
                            ["steps", record.name, "memberShip"],
                            obj
                          );
                          formSteps.setFieldsValue({
                            steps: [...formSteps.getFieldValue("steps")],
                          });
                        }}
                        // KHÔNG cần valueIsOption, KHÔNG cần initOptionItem
                      />
                    )}
                  </Form.Item>
                  {formSteps.getFieldValue(["steps", record.name, "name"]) !==
                    StepType.Create &&
                  formSteps.getFieldValue([
                    "steps",
                    record.name,
                    "memberShip2Id",
                  ]) === undefined ? (
                    <Tooltip title="Thêm nhân viên 2">
                      <Button
                        type="text"
                        className="add-button"
                        icon={<PlusIcon size={24} fill="#1677ff" />}
                        onClick={() => {
                          formSteps.setFieldValue(
                            ["steps", record.name, "memberShip2Id"],
                            null
                          );
                          formSteps.setFieldsValue({
                            steps: [...formSteps.getFieldValue("steps")],
                          });
                        }}
                      />
                    </Tooltip>
                  ) : (
                    // Giữ chỗ cho nút + khi không hiển thị
                    <span className="approve-process-placeholder" />
                  )}
                </div>

                {/* Dòng 2: Input nhân viên 2 + nút xóa (nếu đã có memberShip2Id) */}
                {formSteps.getFieldValue([
                  "steps",
                  record.name,
                  "memberShip2Id",
                ]) !== undefined && (
                  <div className="approve-process-staff-row">
                    <Form.Item
                      name={[record.name, "memberShip2Id"]}
                      className="approve-process-staff2"
                    >
                      <MembershipSelector
                        disabled={status != ApprovalListStatus.Pending}
                        value={formSteps.getFieldValue([
                          "steps",
                          record.name,
                          "memberShip2Id",
                        ])}
                        onChange={(val, memberShips) => {
                          formSteps.setFieldValue(
                            ["steps", record.name, "memberShip2Id"],
                            val
                          );
                          const obj = memberShips.find((m) => m.id === val);
                          formSteps.setFieldValue(
                            ["steps", record.name, "memberShip2"],
                            obj
                          );
                        }}
                        placeholder="Chọn nhân viên 2"
                      />
                    </Form.Item>
                    <Tooltip title="Xóa nhân viên 2">
                      <Button
                        type="text"
                        danger
                        icon={<DeleteIcon width={16} />}
                        onClick={() => {
                          formSteps.setFieldValue(
                            ["steps", record.name, "memberShip2Id"],
                            undefined
                          );
                          formSteps.setFieldValue(
                            ["steps", record.name, "memberShip2"],
                            undefined
                          );
                          formSteps.setFieldsValue({
                            steps: [...formSteps.getFieldValue("steps")],
                          });
                        }}
                      />
                    </Tooltip>
                  </div>
                )}
              </div>

              {/* <Form.Item name={[record.name, "note"]}>
            <Input placeholder="Nhập ghi chú" />
          </Form.Item> */}
            </div>
          </div>

          <div className="line" />
        </div>
      );
    };

    const typeValue = (templateType || templateInfo.type || "").toLowerCase();
    const isHideAddStep = typeValue === "simple";

    const isHavePermissionToChangeStep = useMemo(() => {
      if (haveViewAllPermission) {
        return true;
      } else {
        const approvers =
          currentStep?.approvers.map((it) => it.memberShip?.staff?.id) || [];
        return approvers.includes(userStore.info.id);
      }
    }, [currentStep, haveViewAllPermission, userStore.info.id]);

    // console.log("[approval step card] steps", steps);

    return (
      <>
        <Spin spinning={loading}>
          <div
            className={clsx(
              "mt-[-64px] flex justify-end mb-[27px] gap-2 items-center",
              !isShowActionButton && "invisible"
            )}
          >
            {/* <Tag>{currentStep?.statusText}</Tag> */}
            <Dropdown
              trigger={["click"]}
              menu={{
                items: [
                  {
                    label: currentStep?.actionText,
                    key: "0",
                    onClick: () => {
                      handleApproveProcess(currentStep?.id!);
                    },
                  },
                  {
                    label: "Từ chối",
                    key: "1",
                    className: "!text-red-500",
                    onClick: () => {
                      handleRejectProcess(currentStep?.id!);
                    },
                  },
                ],
              }}
            >
              <Button
                iconPosition="end"
                icon={<ArrowDownIcon />}
                className="!h-[37px]"
                disabled={!isHavePermissionToChangeStep}
              >
                Thao tác
              </Button>
            </Dropdown>
          </div>
          <Card
            className="content-card approve-process-card z-[1]"
            bodyStyle={{ padding: 0 }}
          >
            <div style={{}}>
              <div className="content-card-header">
                <div className="text-[20px] font-bold">Quy trình duyệt</div>

                {/* {!editable ? (
                <CustomButton
                  variant="primary"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleEdit();
                  }}
                >
                  Tạo
                </CustomButton>
              ) : ( */}
                <Tooltip title="Chỉnh sửa">
                  <Button
                    type="text"
                    icon={
                      <PencilIcon
                        size={18}
                        fill={darkMode ? "#ffffff" : "#050505"}
                      />
                    }
                    onClick={(e) => {
                      e.stopPropagation();
                      handleEdit();
                    }}
                  />
                </Tooltip>
              </div>
              <div className="card-body ">
                {[...steps].map(_renderStepRow)}{" "}
                {!steps.length && (
                  <div className="flex items-center justify-center flex-1">
                    Không có bước duyệt nào
                  </div>
                )}
              </div>
            </div>

            {/* {showSaveTemplate && (
            <div className="flex items-center justify-between p-[20px]">
              <div>Bạn có muốn lưu mẫu quy trình này không?</div>
              <CustomButton
                variant="outline"
                onClick={async () => {
                  if (selectedApprovalTemplate?.id) {
                    // Đảm bảo formTemplate cập nhật giá trị mới nhất
                    await formTemplate.validateFields();
                    const { name, description, type } =
                      formTemplate.getFieldsValue();
                    approvalTemplate.current = { name, description, type };
                    await approvalTemplateApi.update(
                      selectedApprovalTemplate.id,
                      {
                        ...getDataSubmit(),
                        projectId: getCurrentProjectId(),
                      }
                    );
                    message.success("Cập nhật mẫu quy trình thành công");
                  } else {
                    // handleCreateApprovalTemplate();
                  }
                  setShowSaveTemplate(false);
                  handleClose();
                }}
              >
                Lưu
              </CustomButton>
            </div>
          )} */}
          </Card>
        </Spin>

        <Modal
          open={isModalVisible}
          onCancel={() => {
            setIsModalVisible(false);
          }}
          width={1200}
          style={{ top: 10 }}
          footer={
            <Space>
              <Button
                onClick={() => {
                  setIsModalVisible(false);
                }}
              >
                Đóng
              </Button>
              <CustomButton onClick={handleSubmit}>Lưu</CustomButton>
            </Space>
          }
        >
          <Form form={formSteps} layout="vertical">
            <Form.Item hidden name="mode" />
            <div className="approve-process-card">
              <Form.List name="steps">
                {(fields, { remove }) => {
                  // console.log({ fields });
                  return (
                    <div className="card-body">
                      {fields.map((field, index) => {
                        // const stepName = form.getFieldValue([
                        //   "steps",
                        //   field.name,
                        //   "name",
                        // ]);

                        return (
                          <ApprovalTemplateStepItem
                            field={field}
                            fields={fields}
                            index={index}
                            isEditMode={true}
                            // localType={localType}
                            key={field.key}
                            onRemove={(name) => remove(name)}
                          />
                        );
                      })}
                    </div>
                  );
                }}
              </Form.List>
            </div>
          </Form>
        </Modal>

        {/* <Modal
        closable={false}
        // open={isModalVisible}
        onCancel={handleClose}
        onOk={() => {
          setIsModalVisible(false);
        }}
        width={400}
        footer={
          <Space className="p-[20px]">
            <CustomButton variant="outline" onClick={handleClose}>
              Hủy
            </CustomButton>
            <CustomButton onClick={handleSubmit}>Lưu</CustomButton>
          </Space>
        }
        className="modal-approve-process"
      >
        <Form
          form={formSteps}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{ steps: initSteps }}
        >
          <Form.List name={"steps"}>
            {(fields, { add, remove }) => {
              const sortedFields = [...fields];

              return (
                <div className="content-card approve-process-card">
                  <div style={{}}>
                    <div className="content-card-header">
                      <h2 className="header-text">Quy trình duyệt</h2>

                      {!isHideAddStep && (
                        <Tooltip title="Thêm bước">
                          <Button
                            type="text"
                            className="add-button"
                            icon={
                              <PlusIcon
                                size={24}
                                fill={darkMode ? "#ffffff" : "#050505"}
                              />
                            }
                            onClick={(e) => {
                              const listSteps = formSteps.getFieldValue(
                                "steps"
                              ) as StepItem[];

                              // const indexInsert = listSteps
                              //   ? listSteps.length - 1
                              //   : 0;

                              // listSteps.splice(indexInsert, 0, {
                              //   memberShipId: undefined,
                              //   note: "",
                              //   name: "",
                              //   status: ApprovalListStatus.Pending,
                              //   position: indexInsert,
                              // });

                              // for (let i = 0; i < listSteps.length; i++) {
                              //   const element = listSteps[i];
                              //   element.position = i;
                              // }

                              // form.setFieldsValue({
                              //   steps: listSteps,
                              // });

                              e.stopPropagation();
                            }}
                          />
                        </Tooltip>
                      )}
                    </div>
                    <div className="card-body">
                      {sortedFields.map((field, index) =>
                        _renderFormStepRow(
                          field,
                          index,
                          sortedFields.length,
                          remove
                        )
                      )}
                    </div>
                  </div>
                </div>
              );
            }}
          </Form.List>
        </Form>
      </Modal> */}
      </>
    );
  }
);
