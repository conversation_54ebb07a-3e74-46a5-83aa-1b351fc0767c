import {
  DeleteOutlined,
  EditOutlined,
  FileTextOutlined,
  PlusOutlined,
  SearchOutlined,
  LockOutlined,
  UnlockOutlined,
  ImportOutlined,
} from "@ant-design/icons";
import {
  Button,
  Input,
  Space,
  Spin,
  Table,
  Tag,
  Tooltip,
  Divider,
  Modal,
  message,
  Card,
  Select,
} from "antd";
import { materialApi } from "api/material.api";
import { useNavigate } from "react-router-dom";
import { Pagination } from "components/Pagination";
import React, { useEffect, useState, useRef, useMemo } from "react";
import { ModalStatus } from "types/modal";
import { QueryParam } from "types/query";
// import { goods } from "types/goods";
import { EMaterialType, Material } from "types/material";
import { useMaterial } from "hooks/useMaterial";
import { getTitle } from "utils";
import { $url } from "utils/url";
import { GoodsModal } from "./components/GoodsModal";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import CustomButton from "components/Button/CustomButton";
import CustomInput from "components/Input/CustomInput";
import PencilIcon from "assets/svgs/PencilIcon";
import DeleteIcon from "assets/svgs/DeleteIcon";
import PageTitle from "components/PageTitle/PageTitle";
import {
  checkRole,
  checkRoles,
  filterActionColumnIfNoPermission,
} from "utils/auth";
import { PermissionNames } from "types/PermissionNames";
import { permissionStore } from "store/permissionStore";
import { useTheme } from "context/ThemeContext";
import { handleExport, MyExcelColumn } from "utils/MyExcel";
import { MaterialUsageTrans } from "types/materialUsage";
import { GoodsOriginTrans } from "types/productOrigin";
import ImportMaterial, {
  ImportMaterialModal,
} from "components/ImportDocument/ImportMaterial";
import { exportTemplateWithValidation } from "utils/TemplateExcel";
import {
  getListNameByApi,
  getListNameByTypeDictionary,
} from "hooks/useDictionary";
import { unitApi } from "api/unit.api";
import { providerApi } from "api/provider.api";
import { brandApi } from "api/brand.api";
import { materialGroupApi } from "api/materialGroup.api";
import { removeSubstringFromKeys } from "utils/common";
import { DictionarySelector } from "components/Selector/DictionarySelector";
import { DictionaryType } from "types/dictionary";
import { useTransition } from "hooks/useTransition";
import QueryLabel from "components/QueryLabel/QueryLabel";
import { TableProps } from "antd/lib";
import { observer } from "mobx-react";
import LockButton from "components/Button/LockButton";
import EditButton from "components/Button/EditButton";
import { BMDImage } from "components/Image/BMDImage";
import logoImage from "assets/images/logo.png";

const { ColumnGroup, Column } = Table;

export const GoodsPage = observer(
  ({ title = "", type = EMaterialType.Product }) => {
    const isProduct = type === EMaterialType.Product;

    const {
      haveAddPermission,
      haveBlockPermission,
      haveEditPermission,
      haveViewAllPermission,
    } = checkRoles(
      {
        add: isProduct ? PermissionNames.goodsAdd : PermissionNames.materialAdd,
        edit: isProduct
          ? PermissionNames.goodsEdit
          : PermissionNames.materialEdit,
        block: isProduct
          ? PermissionNames.goodsBlock
          : PermissionNames.materialBlock,
        viewAll: isProduct
          ? PermissionNames.goodsViewAll
          : PermissionNames.materialViewAll,
      },
      permissionStore.permissions
    );
    const { darkMode } = useTheme();
    const navigate = useNavigate();
    const { isLoaded } = useTransition();

    // Initialize useMaterial hook
    const {
      materials,
      total,
      fetchData,
      loading,
      setQuery,
      query,
      isEmptyQuery,
    } = useMaterial({
      initQuery: {
        page: 1,
        limit: 50,
        isAdmin: haveViewAllPermission ? true : undefined,
        search: "",
        type: type,
      },
    });

    const [selectedgoods, setSelectedgoods] = useState<Partial<Material>>({});
    const modalRef = useRef<GoodsModal>(null);
    const importModal = useRef<ImportMaterialModal>();
    const [deleteModalVisible, setDeleteModalVisible] = useState(false);
    const [itemToDelete, setItemToDelete] = useState<Material | null>(null);
    const [loadingDelete, setLoadingDelete] = useState(false);
    const [loadingDownloadDemo, setLoadingDownloadDemo] = useState(false);

    useEffect(() => {
      document.title = getTitle(title);
    }, []);

    useEffect(() => {
      if (isLoaded) {
        fetchData();
      }
    }, [isLoaded]);

    const handleActiveMaterial = async (id: number, value: boolean) => {
      try {
        setLoadingDelete(true);
        await materialApi.update(id, { material: { isActive: !value } });
        message.success(value ? "Khóa thành công" : "Mở khóa thành công");
        fetchData();
      } catch (error) {
        console.error("Error updating material:", error);
      } finally {
        setLoadingDelete(false);
      }
    };

    const exportColumns: MyExcelColumn<Material>[] = [
      {
        header: isProduct ? "Mã hàng hóa" : "Mã nguyên vật liệu",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "code",
        columnKey: "code",
      },
      {
        header: isProduct ? "Tên hàng hóa" : "Tên nguyên vật liệu",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "name",
        columnKey: "name",
      },

      {
        header: isProduct ? "Nhóm hàng hóa" : "Nhóm hàng",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "materialGroup",
        columnKey: "materialGroup",
        render: (record) => record.materialGroup?.name,
      },
      {
        header: "Đơn vị tính",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "unit",
        columnKey: "unit",
        render: (record) => record.unit?.name,
      },
      {
        header: "Mã SP NCC",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "supplierProductCode",
        columnKey: "supplierProductCode",
      },
      {
        header: "Hình thức sử dụng",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "usageForm",
        columnKey: "usageForm",
        render: (record: Material) =>
          MaterialUsageTrans[record.usageForm]?.label,
      },
      {
        header: "Nguồn gốc sản phẩm",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "productOrigin",
        columnKey: "productOrigin",
        render: (record: Material) =>
          GoodsOriginTrans[record.productOrigin]?.label,
      },
      {
        header: "Thương hiệu",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "brand",
        columnKey: "brand",
        render: (record: Material) => record.brand?.name,
      },
      {
        header: isProduct ? "Mô tả hàng hóa" : "Mô tả nguyên vật liệu",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "description",
        columnKey: "description",
      },
      {
        header: "Dài (mm)",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "lengthMm",
        columnKey: "lengthMm",
      },
      {
        header: "Rộng (mm)",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "widthMm",
        columnKey: "widthMm",
      },
      {
        header: "Cao (mm)",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "heightMm",
        columnKey: "heightMm",
      },
      {
        header: "Dày (mm)",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "thicknessMm",
        columnKey: "thicknessMm",
      },
      {
        header: "Đường kính",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "diameterMm",
        columnKey: "diameterMm",
      },
      {
        header: "Kích thước khác",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "otherDimensions",
        columnKey: "otherDimensions",
      },
      {
        header: "Giá mua",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "purchasePrice",
        columnKey: "purchasePrice",
        style: { numFmt: "###,##" },
      },
      {
        header: "Giá bán",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "salePrice",
        columnKey: "salePrice",
        style: { numFmt: "###,##" },
      },
      {
        header: "Thuế",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "taxPercent",
        columnKey: "taxPercent",
      },
      {
        header: "Số lượng tối thiểu",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "minQuantity",
        columnKey: "minQuantity",
        style: { numFmt: "###,##" },
      },
      {
        header: "Số lượng tối đa",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "maxQuantity",
        columnKey: "maxQuantity",
        style: { numFmt: "###,##" },
      },
      {
        header: "Thời gian tồn",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "shelfLifeDays",
        columnKey: "shelfLifeDays",
      },
      {
        header: "Nhà cung cấp",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "provider",
        columnKey: "provider",
        render: (record: Material) => record.provider?.name,
      },
      {
        header: "Trạng thái",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "isActive",
        columnKey: "isActive",
        render: (record: Material) =>
          record.isActive ? "Hoạt động" : "Bị khóa",
      },
    ];

    // Handle delete modal
    const handleDeleteClick = (record: Material) => {
      setItemToDelete(record);
      setDeleteModalVisible(true);
    };

    const handleDeleteConfirm = async () => {
      if (itemToDelete) {
        try {
          await materialApi.block(itemToDelete.id, { isActive: false });
          setDeleteModalVisible(false);
          setItemToDelete(null);
          fetchData(); // Refresh data
        } catch (error) {
          console.error("Error blocking item:", error);
        }
      }
    };

    const handleDeleteCancel = () => {
      setDeleteModalVisible(false);
      setItemToDelete(null);
    };

    // Handle upload file processing
    const handleOnUploadedFile = async (excelData: any, setData: any) => {
      const { results } = excelData;

      console.log("results", results);

      const importData = results?.map((item: any) => {
        const refineRow = removeSubstringFromKeys(item, " *");

        const code = isProduct
          ? refineRow["Mã hàng hóa"]
          : refineRow["Mã nguyên vật liệu"];
        const name = isProduct
          ? refineRow["Tên hàng hóa"]
          : refineRow["Tên nguyên vật liệu"];
        const materialGroupName = isProduct
          ? refineRow["Nhóm hàng hóa"]
          : refineRow["Nhóm hàng"];
        const unitName = refineRow["Đơn vị tính"];
        const supplierProductCode = refineRow["Mã SP NCC"];
        const usageForm = Object.values(MaterialUsageTrans).find(
          (item) => item.label === refineRow["Hình thức sử dụng"]
        )?.value;
        const productOrigin = Object.values(GoodsOriginTrans).find(
          (item) => item.label === refineRow["Nguồn gốc sản phẩm"]
        )?.value;
        const brandName = refineRow["Thương hiệu"];
        const description = refineRow["Mô tả hàng hóa"];
        const lengthMm = refineRow["Dài (mm)"];
        const widthMm = refineRow["Rộng (mm)"];
        const heightMm = refineRow["Cao (mm)"];
        const thicknessMm = refineRow["Dày (mm)"];
        const diameterMm = refineRow["Đường kính"];
        const otherDimensions = refineRow["Kích thước khác"];
        const purchasePrice = refineRow["Giá mua"];
        const salePrice = refineRow["Giá bán"];
        const taxPercent = refineRow["Thuế (%)"];
        const minQuantity = refineRow["Số lượng tối thiểu"];
        const maxQuantity = refineRow["Số lượng tối đa"];
        const shelfLifeDays = refineRow["Thời gian tồn (ngày)"];
        const statusName = refineRow["Trạng thái"];
        const oldSystemCode = refineRow["Mã hệ thống cũ"];
        const providerName = refineRow["Nhà cung cấp"];

        let isActive = undefined;
        if (statusName) {
          isActive = statusName === "Hoạt động" ? true : false;
        }

        return {
          type: type,
          oldSystemCode,
          providerName,
          code,
          name,
          materialGroupName,
          unitName,
          supplierProductCode,
          usageForm,
          productOrigin,
          brandName,
          description,
          lengthMm: lengthMm ? Number(lengthMm) : undefined,
          widthMm: widthMm ? Number(widthMm) : undefined,
          heightMm: heightMm ? Number(heightMm) : undefined,
          thicknessMm: thicknessMm ? Number(thicknessMm) : undefined,
          diameterMm: diameterMm ? Number(diameterMm) : undefined,
          otherDimensions,
          purchasePrice: purchasePrice ? Number(purchasePrice) : undefined,
          salePrice: salePrice ? Number(salePrice) : undefined,
          taxPercent: taxPercent ? Number(taxPercent) : undefined,
          minQuantity: minQuantity ? Number(minQuantity) : undefined,
          maxQuantity: maxQuantity ? Number(maxQuantity) : undefined,
          shelfLifeDays: shelfLifeDays ? Number(shelfLifeDays) : undefined,
          isActive,
          rowNum: item.__rowNum__,
        };
      });
      console.log("importData", importData);

      setData(importData);
    };

    const dataSource: Material[] = materials;

    const getServiceTypeColor = (type: string): string => {
      switch (type) {
        case "Dịch vụ tư vấn":
          return "green";
        case "Bảo trì":
          return "orange";
        case "Thuê thiết bị":
          return "blue";
        default:
          return "default";
      }
    };

    const handleTableChange: TableProps<any>["onChange"] = (
      pagination,
      filters,
      sorter
    ) => {
      if (!Array.isArray(sorter)) {
        const fieldMap: Record<string, string> = {
          name: "material.name",
          code: "material.code",
          materialGroup: "materialGroup.name",
          createdAt: "material.createdAt",
          unit: "unit.name",
          productOrigin: "material.productOrigin",
          supplierProductCode: "material.supplierProductCode",
        };
        const columnKey = sorter.field || sorter.column?.key;

        if (!sorter.order) {
          // setSortField(null);
          // setSortOrder(null);
          query.queryObject = undefined;
          setQuery({ ...query });
        } else {
          const order = sorter.order === "ascend" ? "ASC" : "DESC";
          // setSortField("jobCategory.name");
          // setSortOrder(order);
          const field = fieldMap[columnKey as string];

          const newQueryObject = JSON.stringify([
            {
              type: "sort",
              field,
              value: order,
            },
          ]);
          query.queryObject = newQueryObject;
          setQuery({ ...query });
        }
        fetchData();
      } else {
        query.queryObject = undefined;
        setQuery({ ...query });
        fetchData();
      }
    };

    const handleRowClick = (record: Material) => {
      if (isProduct) {
        navigate(
          `/master-data/${PermissionNames.goodsEdit.replace(
            ":id",
            record!.id + ""
          )}`
        );
      } else {
        navigate(
          `/master-data/${PermissionNames.materialEdit.replace(
            ":id",
            record!.id + ""
          )}`
        );
      }
    };

    const columns: CustomizableColumn<Material>[] = [
      {
        key: "code",
        title: "Mã",
        dataIndex: "code",
        width: 100,
        render: (_, record) => (
          <div
            className="text-[#1677ff] cursor-pointer"
            onClick={() => handleRowClick(record)}
          >
            {record?.code || "-"}
          </div>
        ),
        defaultVisible: true,
        alwaysVisible: true,
        sorter: true,
      },
      {
        key: "name",
        title: isProduct ? "Hàng hóa" : "Tên nguyên vật liệu",
        dataIndex: "name",
        width: 180,
        render: (_, record) => (
          <div className="flex items-center gap-2">
            <BMDImage
              src={
                record.avatar && record.avatar.trim()
                  ? $url(record.avatar)
                  : logoImage
              }
              className="size-[34px] object-cover"
              width={34}
              height={34}
              fallback={logoImage}
            />
            <label htmlFor="" className="text-bold">
              {record.name}
            </label>
          </div>
        ),
        defaultVisible: true,
        sorter: true,
      },
      {
        key: "materialGroup",
        title: isProduct ? "Nhóm hàng hóa" : "Nhóm hàng",
        width: 150,
        render: (_, record) =>
          record.materialGroup?.name || "Chưa có nhóm hàng hóa",
        defaultVisible: true,
        sorter: true,
      },
      {
        key: "unit",
        title: "Đơn vị tính",
        width: 100,
        render: (_, record) => record.unit?.name || "Chưa có đơn vị tính",
        defaultVisible: true,
        sorter: true,
      },
      {
        key: "createdAt",
        title: "Ngày nhập kho đầu tiên",
        dataIndex: "createdAt",
        width: 150,
        render: (timestamp) =>
          timestamp
            ? new Date(timestamp * 1000).toLocaleDateString("vi-VN")
            : "-",
        defaultVisible: true,
        showColumn: isProduct,
        sorter: true,
      },
      {
        key: "productOrigin",
        title: "Nguồn gốc sản phẩm",
        width: 150,
        render: (_, record) => GoodsOriginTrans?.[record.productOrigin]?.label,
        defaultVisible: true,
        showColumn: !isProduct,
        sorter: true,
      },
      {
        key: "supplierProductCode",
        title: "Mã SP NCC",
        width: 100,
        render: (_, record) => record.supplierProductCode,
        defaultVisible: true,
        showColumn: !isProduct,
        sorter: true,
      },
      {
        key: "status",
        title: "Trạng thái",
        align: "center",
        width: 150,
        render: (_, record) => (
          <div className="flex justify-center">
            <Tag
              className="status-tag !mr-0"
              color={record.isActive ? "green" : "red"}
            >
              {record.isActive ? "Hoạt động" : "Bị khóa"}
            </Tag>
          </div>
        ),
        defaultVisible: true,
      },
      {
        key: "actions",
        title: "Xử lý",
        align: "center",
        width: 100,
        fixed: "right",
        render: (_, record) => {
          const typeName = isProduct ? "hàng hóa" : "nguyên vật liệu";
          return (
            <Space size="small">
              {haveEditPermission && (
                <EditButton
                  onClick={(e) => {
                    e.stopPropagation();
                    if (isProduct) {
                      navigate(
                        `/master-data/${PermissionNames.goodsEdit.replace(
                          ":id",
                          record.id + ""
                        )}?update=1`
                      );
                    } else {
                      navigate(
                        `/master-data/${PermissionNames.materialEdit.replace(
                          ":id",
                          record.id + ""
                        )}?update=1`
                      );
                    }
                  }}
                />
              )}

              {haveBlockPermission && (
                <LockButton
                  isActive={record.isActive}
                  onAccept={() =>
                    handleActiveMaterial(record.id, record.isActive)
                  }
                  modalTitle={`${
                    record.isActive ? "Khóa" : "Mở khóa"
                  } ${typeName} ${record.name}`}
                  modalContent={
                    <>
                      <div>
                        Khi {record.isActive ? "khóa" : "mở khóa"} {typeName}{" "}
                        các thông tin của {typeName} này cũng sẽ được{" "}
                        {record.isActive ? "khóa" : "mở khóa"}.
                      </div>
                      <div>
                        Bạn có chắc chắn muốn{" "}
                        {record.isActive ? "khóa" : "mở khóa"} {typeName} này?
                      </div>
                    </>
                  }
                />
              )}
            </Space>
          );
        },
        defaultVisible: true,
        alwaysVisible: true,
      },
    ];

    const pagination = {
      current: query.page,
      pageSize: query.limit,
      total: total,
      showSizeChanger: true,
    };

    const handleDownloadDemoExcel = async () => {
      try {
        setLoadingDownloadDemo(true);
        const [unitNames, providerNames, brandNames, materialGroupNames] =
          await Promise.all([
            getListNameByApi({ api: unitApi.findAll, dataKey: "units" }),
            getListNameByApi({
              api: providerApi.findAll,
              dataKey: "providers",
            }),
            getListNameByTypeDictionary(DictionaryType.Brand),
            getListNameByTypeDictionary(
              isProduct
                ? DictionaryType.ProductGroup
                : DictionaryType.MaterialGroup
            ),
          ]);

        const materialUsage = Object.values(MaterialUsageTrans)
          .map((item) => item.label)
          .join(",");

        const productOrigin = Object.values(GoodsOriginTrans)
          .map((item) => item.label)
          .join(",");

        const result = await exportTemplateWithValidation({
          templatePath: isProduct
            ? "/exportFile/file_mau_nhap_hang_hoa.xlsx"
            : "/exportFile/file_mau_nhap_nguyen_vat_lieu.xlsx",
          outputFileName: isProduct
            ? "file_mau_nhap_hang_hoa.xlsx"
            : "file_mau_nhap_nguyen_vat_lieu.xlsx",
          sheetsToAdd: [
            { name: "Đơn vị", data: unitNames },
            { name: "Thương hiệu", data: brandNames },
            { name: "Nhà cung cấp", data: providerNames },
            { name: "Nhóm hàng hóa", data: materialGroupNames },
          ],
          validations: [
            {
              column: "E",
              type: "list",
              formulae: unitNames.length
                ? [`'Đơn vị'!$A$1:$A$${unitNames.length}`]
                : [],
            },
            {
              column: "G",
              type: "list",
              formulae: providerNames.length
                ? [`'Nhà cung cấp'!$A$1:$A$${providerNames.length}`]
                : [],
            },
            {
              column: "F",
              type: "list",
              formulae: brandNames.length
                ? [`'Thương hiệu'!$A$1:$A$${brandNames.length}`]
                : [],
            },
            {
              column: "C",
              type: "list",
              formulae: materialGroupNames.length
                ? [`'Nhóm hàng hóa'!$A$1:$A$${materialGroupNames.length}`]
                : [],
            },
            {
              column: "I",
              type: "list",
              formulae: [`"${materialUsage}"`],
            },
            {
              column: "J",
              type: "list",
              formulae: [`"${productOrigin}"`],
            },
            {
              column: "X",
              type: "list",
              allowBlank: true,
              formulae: ['"Hoạt động,Bị khóa"'],
            },
          ],
        });
      } catch (error) {
        message.error(
          `Có lỗi xảy ra: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      } finally {
        setLoadingDownloadDemo(false);
      }
    };

    return (
      <div>
        <PageTitle
          title={title}
          breadcrumbs={["Dữ liệu nguồn", title]}
          extra={
            haveAddPermission && (
              <Space>
                <CustomButton
                  size="small"
                  showPlusIcon
                  onClick={() => {
                    isProduct
                      ? navigate(`/master-data/${PermissionNames.goodsAdd}`)
                      : navigate(`/master-data/${PermissionNames.materialAdd}`);
                  }}
                >
                  {isProduct ? "Tạo hàng hóa" : "Tạo nguyên vật liệu"}
                </CustomButton>
                <CustomButton
                  size="small"
                  icon={<ImportOutlined />}
                  onClick={() => {
                    importModal.current?.open();
                  }}
                >
                  Nhập excel
                </CustomButton>
              </Space>
            )
          }
        />

        <div className="app-container">
          <Card>
            <div className="flex gap-[16px] items-end pb-[12px] justify-between">
              <div className="flex gap-[16px] items-end flex-wrap">
                <div className="w-[300px]">
                  <CustomInput
                    tooltipContent={
                      isProduct
                        ? "Tìm theo mã, tên hàng hóa"
                        : "Tìm theo mã, tên nguyên vật liệu"
                    }
                    label="Tìm kiếm"
                    placeholder="Tìm kiếm"
                    onPressEnter={() => {
                      console.log("onPressEnter:");
                      query.page = 1;
                      setQuery({ ...query });
                      fetchData();
                    }}
                    value={query.search}
                    onChange={(value) => {
                      console.log("change search value:", value);
                      query.search = value;
                      setQuery({ ...query });

                      if (!value) {
                        fetchData();
                      }
                    }}
                    allowClear
                  />
                </div>
                <div>
                  <QueryLabel>
                    {isProduct ? "Nhóm hàng hóa" : "Nhóm hàng"}
                  </QueryLabel>
                  <DictionarySelector
                    label={isProduct ? "Nhóm hàng hóa" : "Nhóm hàng"}
                    initQuery={{
                      type: isProduct
                        ? DictionaryType.ProductGroup
                        : DictionaryType.MaterialGroup,
                    }}
                    allowClear={true}
                    addonOptions={[
                      {
                        id: "",
                        name: isProduct
                          ? "Tất cả nhóm hàng hóa"
                          : "Tất cả nhóm hàng",
                      },
                    ]}
                    value={query.materialGroupId ?? ""}
                    onChange={(value) => {
                      query.materialGroupId = value || "";
                      setQuery({ ...query });
                    }}
                  />
                </div>
                <div>
                  <QueryLabel>Trạng thái</QueryLabel>
                  <Select
                    placeholder="Chọn trạng thái"
                    value={
                      query.isActive !== undefined ? String(query.isActive) : ""
                    }
                    allowClear
                    onChange={(value) => {
                      if (value === undefined || value === "") {
                        delete query.isActive;
                      } else {
                        query.isActive = value === "true";
                      }
                      setQuery({ ...query });
                    }}
                    options={[
                      {
                        value: "",
                        label: "Tất cả trạng thái",
                      },
                      {
                        value: "true",
                        label: "Hoạt động",
                      },
                      {
                        value: "false",
                        label: "Bị khóa",
                      },
                    ]}
                    style={{ minWidth: 160 }}
                  />
                </div>
                <CustomButton
                  onClick={() => {
                    query.page = 1;
                    setQuery({ ...query });
                    fetchData();
                  }}
                >
                  Áp dụng
                </CustomButton>

                {(query.search ||
                  query.materialGroupId ||
                  query.isActive !== undefined) && (
                  <CustomButton
                    variant="outline"
                    onClick={() => {
                      delete query.materialGroupId;
                      delete query.isActive;
                      delete query.search;
                      setQuery({ ...query });
                      fetchData();
                    }}
                  >
                    Bỏ lọc
                  </CustomButton>
                )}
              </div>

              <CustomButton
                onClick={() => {
                  Modal.confirm({
                    title: `Bạn có muốn xuất file excel?`,
                    getContainer: () => {
                      return document.getElementById("App") as HTMLElement;
                    },
                    icon: null,
                    footer: (_, { OkBtn, CancelBtn }) => (
                      <>
                        <CustomButton
                          variant="outline"
                          className="cta-button"
                          onClick={() => {
                            handleExport({
                              onProgress(percent) {
                                console.log("What is percent", percent);
                              },
                              exportColumns,
                              fileType: "xlsx",
                              dataField: "materials",
                              query: query,
                              api: materialApi.findAll,
                              fileName: isProduct
                                ? "Danh sách hàng hóa"
                                : "Danh sách nguyên vật liệu",
                              sheetName: isProduct
                                ? "Danh sách hàng hóa"
                                : "Danh sách nguyên vật liệu",
                            });
                            Modal.destroyAll();
                          }}
                        >
                          Có
                        </CustomButton>
                        <CustomButton
                          onClick={() => {
                            Modal.destroyAll();
                          }}
                          className="cta-button"
                        >
                          Không
                        </CustomButton>
                      </>
                    ),
                  });
                }}
              >
                Xuất excel
              </CustomButton>
            </div>
            <CustomizableTable
              columns={filterActionColumnIfNoPermission(columns, [
                haveEditPermission,
                haveBlockPermission,
              ])}
              dataSource={dataSource}
              rowKey="id"
              loading={loading}
              pagination={false}
              scroll={{ x: 1200 }}
              bordered
              displayOptions
              tableId="goods-page"
              //@ts-ignore
              onChange={handleTableChange}
              onRowClick={handleRowClick}
            />

            <Pagination
              currentPage={query.page}
              defaultPageSize={query.limit}
              total={total}
              onChange={({ limit, page }) => {
                query.page = page;
                query.limit = limit;
                setQuery({ ...query });
                fetchData();
              }}
            />

            {useMemo(
              () => (
                <ImportMaterial
                  materialType={type}
                  guide={[
                    "Vui lòng tải và sử dụng file mẫu để import dữ liệu đúng cách",
                    "Không được thay đổi tiêu đề trong file csv/excel mẫu để tránh import thiếu dữ liệu",
                    "Chi tiết xem tại sheet Hướng dẫn của file import mẫu",
                    isProduct
                      ? "Các trường bắt buộc: Tên hàng hóa, Nhóm hàng hóa, Đơn vị tính, Dài, Thuế"
                      : "Các trường bắt buộc: Tên nguyên vật liệu, Nhóm hàng, Đơn vị tính, Dài, Thuế",
                  ]}
                  onSuccess={() => {
                    query.page = 1;
                    fetchData();
                  }}
                  ref={importModal}
                  createApi={materialApi.create}
                  onUploaded={(excelData, setData) => {
                    console.log("up gì lên vậy", excelData);
                    handleOnUploadedFile(excelData, setData);
                  }}
                  okText={
                    isProduct
                      ? `Nhập hàng hóa ngay`
                      : `Nhập nguyên vật liệu ngay`
                  }
                  onDownloadDemoExcel={handleDownloadDemoExcel}
                />
              ),
              [type, isProduct]
            )}
          </Card>
        </div>

        <GoodsModal onSubmitOk={fetchData} onClose={() => {}} ref={modalRef} />
      </div>
    );
  }
);
