import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const reportApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/report",
      params,
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/report/${id}`,
      method: "get",
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/report",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/report/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/report/${id}`,
      method: "delete",
    }),
  approve: (id: number, data?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/report/${id}/approve`,
      method: "patch",
      data,
    }),
  reject: (id: number, data?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/report/${id}/reject`,
      method: "patch",
      data,
    }),
};
