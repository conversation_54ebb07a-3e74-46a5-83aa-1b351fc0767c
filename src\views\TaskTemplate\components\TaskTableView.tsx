import React, { useRef, useState } from "react";
import { Button, Input, Card, Checkbox, Col, Row, Tooltip } from "antd";
import { useTheme } from "context/ThemeContext";
import { TaskTemplateForm } from "types/taskTemplate";
import { FormInstance, useWatch } from "antd/es/form/Form";
import { TaskModal } from "./TaskModal";
import CustomButton from "components/Button/CustomButton";
import { PlusIcon } from "assets/svgs/PlusIcon";
import { Todo } from "types/todo";
import { ColorThemes } from "utils/theme";
import DeleteIcon from "assets/svgs/DeleteIcon";
import { TaskForm } from "types/task";

// Interface cho form có todos
interface FormWithTodos {
  todos?: Todo[];
}

export type TaskTableViewProps = {
  form: FormInstance<FormWithTodos>;
  readonly?: boolean;
};

interface EditTodo extends Todo {
  isEdit?: boolean;
  oldContent?: string;
}

const TaskTableView: React.FC<TaskTableViewProps> = (props) => {
  const { form, readonly } = props;
  const todos = useWatch("todos", form);
  const { darkMode } = useTheme();
  const taskRef = useRef<TaskModal>(null);
  const [visibleAddTask, setVisibleAddTask] = useState(false);
  const [newTaskContent, setNewTaskContent] = useState("");

  const handleCreateTask = () => {
    setVisibleAddTask(true);
  };

  const handleSubmitTask = (todo: EditTodo) => {
    const newTodos = todos || [];
    const findIndexTodo = newTodos.findIndex((item) => item.id === todo.id);
    const minId = newTodos.length
      ? Math.min(...newTodos.map((item) => item.id))
      : 0;
    if (findIndexTodo > -1) {
      newTodos[findIndexTodo] = todo;
    } else {
      newTodos.push({ ...todo, id: minId - 1 });
    }
    form.setFieldsValue({ todos: [...newTodos] });
  };

  const handleDeleteTask = (todo: EditTodo) => {
    const newTodos = todos || [];
    const findIndexTodo = newTodos.findIndex((item) => item.id === todo.id);
    if (findIndexTodo > -1) {
      newTodos.splice(findIndexTodo, 1);
    }
    form.setFieldsValue({ todos: [...newTodos] });
  };

  const handleAddNewContent = () => {
    if (!!newTaskContent.length) {
      handleSubmitTask({ content: newTaskContent } as EditTodo);
    }
    setNewTaskContent("");
  };

  const _renderTaskItem = (item: EditTodo, index: number) => {
    return (
      <Col span={24} className="gutter-row" key={index}>
        <Row className="gap-x-[10px] items-center">
          <Checkbox
            checked={item.isActive}
            onClick={() =>
              handleSubmitTask({ ...item, isActive: !item.isActive })
            }
          />

          {item.isEdit ? (
            <Input
              value={item.content}
              autoFocus
              onChange={(e) =>
                handleSubmitTask({ ...item, content: e.target.value })
              }
              onBlur={() => {
                handleSubmitTask({
                  ...item,
                  isEdit: !item.isEdit,
                  content: item.oldContent || "",
                });
              }}
              onPressEnter={() => {
                handleSubmitTask({
                  ...item,
                  isEdit: !item.isEdit,
                });
              }}
              className="flex-1"
            />
          ) : (
            <b
              className={"flex-1 " + (!readonly ? "cursor-pointer" : "")}
              style={{
                color: darkMode
                  ? ColorThemes.dark.neutral.n8
                  : ColorThemes.light.neutral.n8,
              }}
              onClick={() => {
                if (readonly) return;
                handleSubmitTask({
                  ...item,
                  isEdit: !item.isEdit,
                  oldContent: item.content,
                });
              }}
            >
              {item.content}
            </b>
          )}

          <Tooltip title="Xóa">
            <Button
              type="text"
              icon={<DeleteIcon />}
              onClick={() => handleDeleteTask({ ...item })}
            />
          </Tooltip>
        </Row>
      </Col>
    );
  };

  return (
    <>
      <div className="absolute top-[-25px] right-0">
        <CustomButton
          size="small"
          icon={<PlusIcon />}
          onClick={handleCreateTask}
          disabled={readonly}
        />
      </div>
      <Card className="mt-[12px] py-[8px]">
        <Row gutter={[16, 16]}>
          {todos?.map(_renderTaskItem)}
          {!todos?.length && !visibleAddTask && (
            <div className="flex justify-center w-full">
              <span className="text-center">
                Chưa có danh sách việc cần làm
              </span>
            </div>
          )}
          {visibleAddTask && (
            <Input
              value={newTaskContent}
              autoFocus
              onChange={(e) => setNewTaskContent(e.target.value)}
              onBlur={() => {
                handleAddNewContent();
                setVisibleAddTask(false);
              }}
              onPressEnter={() => {
                handleAddNewContent();
              }}
              placeholder="Thêm một việc cần làm"
            />
          )}
        </Row>
      </Card>
    </>
  );
};

export default TaskTableView;
