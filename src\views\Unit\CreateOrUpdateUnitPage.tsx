import {
  Card,
  Col,
  Form,
  Input,
  message,
  Row,
  Select,
  Space,
  Spin,
} from "antd";
import { Rule } from "antd/lib/form";
import { useEffect, useMemo, useRef, useState } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { getTitle } from "utils";
import { Unit, UnitTypeTrans } from "types/unit";
import CustomButton from "components/Button/CustomButton";
import PageTitle from "components/PageTitle/PageTitle";
import ActiveStatusTagSelect from "components/ActiveStatus/ActiveStatusTagSelect";
import { ModalStatus } from "types/modal";
import { isEmpty } from "lodash";
import { PermissionNames } from "types/PermissionNames";
import { unitApi } from "api/unit.api";
import clsx from "clsx";
import { TextInput } from "components/Input/TextInput";
import TextArea from "antd/es/input/TextArea";
import { observer } from "mobx-react";
import { checkRoles } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { BMDTextArea } from "components/TextArea/BMDTextArea";
import { BMDCKEditor } from "components/Editor";

const rules: Rule[] = [{ required: true }];
const descriptionRules: Rule[] = [{ required: false }];

interface EditUnitPageProps {
  title: string;
  status: ModalStatus;
}

interface UnitForm extends Unit {}

export const CreateOrUpdateUnitPage = observer(
  ({ title = "", status }: EditUnitPageProps) => {
    const { haveEditPermission } = checkRoles(
      {
        edit: PermissionNames.unitEdit,
      },
      permissionStore.permissions
    );

    const [form] = Form.useForm<UnitForm>();
    const [loading, setLoading] = useState(false);
    const navigate = useNavigate();
    useEffect(() => {
      document.title = getTitle(title);
    }, []);
    const [searchParams, setSearchParams] = useSearchParams();
    const [selectedUnit, setSelectedUnit] = useState<Unit>();
    const [readonly, setReadonly] = useState(true);
    const [loadingFetch, setLoadingFetch] = useState(false);

    const params = useParams();

    const setDataToForm = (data: Unit) => {
      form.setFieldsValue({
        ...data,
        isActive: data.isActive,
      });
    };

    const getOneUnit = async (id: number) => {
      try {
        setLoadingFetch(true);
        const { data } = await unitApi.findOne(id);

        if (isEmpty(data)) {
          navigate("/404");

          return;
        }

        setSelectedUnit(data);
        setDataToForm(data);

        return data as Unit;
      } catch (e: any) {
      } finally {
        setLoadingFetch(false);
      }
    };

    useEffect(() => {
      document.title = getTitle(title);

      if (status == "create") {
        setReadonly(false);
      } else {
        const unitId = params.id;
        if (unitId) {
          getOneUnit(+unitId);
          setReadonly(searchParams.get("update") != "1");
        } else {
          navigate("/404");
        }
      }
    }, [status, params.id, title]);

    const getDataSubmit = () => {
      const { ...data } = form.getFieldsValue();

      const payload = {
        unit: {
          ...data,
          isActive: selectedUnit?.isActive,
        },
      };

      return payload;
    };

    const createData = async () => {
      const valid = await form.validateFields();

      setLoading(true);
      try {
        const res = await unitApi.create(getDataSubmit());
        message.success("Tạo đơn vị tính thành công!");
        navigate(`/master-data/${PermissionNames.unitList}`);
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const valid = await form.validateFields();

      setLoading(true);
      try {
        const res = await unitApi.update(
          selectedUnit!?.id || 0,
          getDataSubmit()
        );
        message.success("Chỉnh sửa đơn vị tính thành công!");
      } finally {
        setLoading(false);
      }
    };

    const handleSubmit = () => {
      if (status == "create") {
        createData();
      } else {
        updateData();
      }
    };

    const pageTitle = useMemo(
      () => (status == "create" ? "Tạo đơn vị tính" : "Chỉnh sửa đơn vị tính"),
      [status]
    );

    return (
      <div className="app-container">
        <PageTitle
          back
          breadcrumbs={[
            { label: "Dữ liệu nguồn" },
            {
              label: "Danh mục đơn vị tính",
              href: `/master-data/${PermissionNames.unitList}`,
            },
            { label: pageTitle },
          ]}
          title={pageTitle}
          extra={
            selectedUnit &&
            status == "update" && (
              <Space>
                <ActiveStatusTagSelect
                  disabled={readonly}
                  isActive={selectedUnit?.isActive}
                  onChange={(value) => {
                    setSelectedUnit({
                      ...selectedUnit,
                      isActive: value,
                    } as Unit);
                    form.setFieldsValue({
                      isActive: value,
                    });
                  }}
                />
              </Space>
            )
          }
        />
        <Card>
          <Spin spinning={loadingFetch}>
            <Form
              layout="vertical"
              form={form}
              className={clsx(readonly ? "readonly" : "")}
              disabled={readonly}
            >
              <Form.Item name="isActive" hidden />
              <Row gutter={16}>
                {/* <Col span={6}>
                  <Form.Item label="Mã đơn vị" name="code">
                    <TextInput
                      disabled={status == "update"}
                      placeholder={
                        status == "create"
                          ? "Nếu không điền hệ thống sẽ tự sinh mã"
                          : ""
                      }
                    />
                  </Form.Item>
                </Col> */}
                <Col span={8}>
                  <Form.Item label="Tên đơn vị" name="name" rules={rules}>
                    <Input placeholder="Nhập tên đơn vị" />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label="Loại đơn vị" name="type" rules={rules}>
                    <Select
                      placeholder="Chọn loại đơn vị"
                      options={Object.values(UnitTypeTrans).map((item) => ({
                        label: item.label,
                        value: item.value,
                      }))}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label="Ký hiệu" name="symbol">
                    <Input placeholder="Nhập ký hiệu" />
                  </Form.Item>
                </Col>

                <Col span={24}>
                  <Form.Item
                    label="Mô tả"
                    name="description"
                    rules={descriptionRules}
                  >
                    <BMDCKEditor
                      placeholder="Nhập mô tả"
                      disabled={readonly}
                      inputHeight={300}
                      onChange={(content) => {
                        form.setFieldsValue({ description: content });
                      }}
                      value={selectedUnit?.description}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Form>
            <div className="flex gap-[16px] justify-end mt-2">
              {!readonly && (
                <CustomButton
                  variant="outline"
                  className="cta-button"
                  onClick={() => {
                    if (status == "create") {
                      navigate(`/master-data/${PermissionNames.unitList}`);
                    } else {
                      setDataToForm(selectedUnit!);
                      setReadonly(true);
                    }
                  }}
                >
                  Hủy
                </CustomButton>
              )}

              <CustomButton
                className="cta-button"
                loading={loading}
                onClick={() => {
                  if (!readonly) {
                    handleSubmit();
                  } else {
                    setReadonly(false);
                  }
                }}
                disabled={status == "update" && !haveEditPermission}
              >
                {status == "create"
                  ? "Tạo đơn vị tính"
                  : readonly
                  ? "Chỉnh sửa"
                  : "Lưu chỉnh sửa"}
              </CustomButton>
            </div>
          </Spin>
        </Card>
      </div>
    );
  }
);
