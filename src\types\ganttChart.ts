export interface GanttChartItem {
  TaskID: number | string; // Unique ID for each task
  TaskName: string; // Name of the task
  StartDate?: Date; // Start date of the task
  EndDate?: Date; // End date of the task
  Duration?: number; // Duration of the task (in days or other units, optional)
  Progress?: number; // Progress of the task, ranging from 0 to 100 (optional)
  ParentID?: number; // The ID of the parent task for a hierarchical structure (optional)
  Dependency?: string; // Dependency between tasks (optional)
  Predecessor?: string; // Predecessor task ID (optional)
  AssignedTo?: string; // Person assigned to the task (optional)
  ResourceID?: number; // Resource ID assigned to the task (optional)
  IsMilestone?: boolean; // Whether the task is a milestone (optional)
  Notes?: string; // Any additional notes (optional)
  subtasks?: GanttChartItem[];
}
