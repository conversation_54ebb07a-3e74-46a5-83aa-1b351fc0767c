import { FileOutlined, PlusOutlined } from "@ant-design/icons";
import {
  Button,
  Image,
  message,
  Progress,
  Spin,
  Tooltip,
  Upload,
  UploadFile,
} from "antd";
import React, { useEffect, useState } from "react";
import { getToken } from "utils/auth";
import { UploadChangeParam } from "antd/es/upload/interface";
import { $url } from "utils/url";
import FileItem from "./FileItem";
import { GrAttachment } from "react-icons/gr";
import noImg from "assets/images/No-Image.png";
import <PERSON>agger from "antd/es/upload/Dragger";
const mimeTypeMap: { [key: string]: string } = {
  jpg: "image/jpeg",
  jpeg: "image/jpeg",
  png: "image/png",
  pdf: "application/pdf",
  doc: "application/msword",
  docx: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  zip: "application/zip",
  rar: "application/x-rar-compressed",
  // Thêm các lo<PERSON>i MI<PERSON> kh<PERSON>c n<PERSON> cần
};
import { v4 as uuidv4 } from "uuid";

export const CommentUploadMultiple = ({
  onUploadOk,
  onBefore,
  fileTypes = [
    "image/jpeg",
    "image/png",
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/x-compressed", // Add RAR file type
    "application/x-zip-compressed", // Add ZIP file type
    "application/vnd.rar",
    "application/x-rar-compressed",
    "application/octet-stream",
    "application/zip",
    "application/octet-stream",
    "multipart/x-zip",
  ],
  fileList,
  fileTypeText = "JPG, PNG, PDF, DOC, DOCX, RAR, ZIP",
  placeholder,
  acceptType,
  readOnly = false,
  uploadUrl = import.meta.env.VITE_API_URL + "/v1/admin/fileAttach/upload",
  isCompact = false,
}: {
  fileTypeText?: string;
  fileTypes?: string[];
  placeholder?: string;
  uploadUrl?: string;
  acceptType?: string;
  readOnly?: boolean;
  fileList: UploadFile[];
  isCompact?: boolean;
  onUploadOk: (fileAttaches: UploadFile[]) => void;
  onBefore?: () => Promise<boolean>;
}) => {
  const [loading, setLoading] = useState(false);
  const [fileListOrigin, setFileListOrigin] = useState<UploadFile[]>(fileList);
  const [fileProgress, setFileProgress] = useState<{ [key: string]: number }>(
    {}
  );

  useEffect(() => {
    setFileListOrigin(() => fileList.map((e) => ({ ...e })));
  }, [fileList]);

  const getFileMimeType = (filename: string) => {
    const extension = filename.split(".").pop()?.toLowerCase();
    return extension ? mimeTypeMap[extension] : "";
  };

  const checkFileType = async (file: any) => {
    const mimeType = file.type || getFileMimeType(file.name);
    const isValidType = mimeType ? fileTypes.includes(mimeType) : false;
    if (!isValidType) {
      message.error(`Chỉ chấp nhận định dạng ${fileTypeText}`, 3);
      return Upload.LIST_IGNORE;
    }

    let isValid = true;
    if (onBefore) {
      isValid = await onBefore();
    }

    return isValid ? true : Upload.LIST_IGNORE;
  };

  const handleChange = (info: UploadChangeParam<any>) => {
    const { fileList, file } = info;
    const { status } = info.file;
    setFileListOrigin([...fileList]);

    console.log("fileList nè", fileList);
    console.log("file nè", file);

    if (status === "uploading") {
      setFileProgress((prevProgress) => ({
        ...prevProgress,
        [info.file.uid]: info.file.percent, // Update the progress
      }));
    }

    if (status === "done") {
      const formattedFileList = fileList.map((file) => ({
        name: file.name,
        type: file.type,
        url: file.url || $url(file.response?.data.path),
        size: file.size,
        uid: file.uid || uuidv4(),
      })) as UploadFile[];
      setTimeout(() => {
        onUploadOk(formattedFileList);
        setFileListOrigin(formattedFileList);
        setFileProgress((prevProgress) => {
          const { [info.file.uid]: _, ...remaining } = prevProgress;
          return remaining; // Remove the progress of the uploaded file
        });
      }, 100);
    }

    if (info.file.status === "error") {
      setFileProgress((prevProgress) => {
        const { [info.file.uid]: _, ...remaining } = prevProgress;
        return remaining; // Remove the progress of the failed file
      });
    }
  };

  const handleDelete = (uid: string) => {
    console.log(uid);
    const updatedFileList =
      fileListOrigin?.filter((file) => file.uid !== uid) || [];
    setFileListOrigin(updatedFileList);

    // Clear the progress for the deleted file
    setFileProgress((prevProgress) => {
      const newProgress = { ...prevProgress };
      delete newProgress[uid];
      return newProgress;
    });

    const formattedFileList = updatedFileList.map((file) => ({
      name: file.name,
      type: file.type,
      url: file.url || $url(file.response?.data.path),
      size: file.size,
    })) as UploadFile[];

    onUploadOk(formattedFileList);
  };

  return (
    <div className="">
      <div>
        <Upload
          name="file"
          showUploadList={false}
          fileList={fileListOrigin}
          // beforeUpload={checkFileType}
          action={uploadUrl}
          headers={{ token: getToken() || "" }}
          accept={acceptType}
          onChange={handleChange}
          disabled={readOnly}
          multiple
        >
          <div className="flex items-center gap-2 text-primary font-medium my-2 cursor-pointer">
            <GrAttachment />
            <span>Tải file lên</span>
          </div>
        </Upload>
      </div>
      {!isCompact && (
        <div className="grid grid-cols-4 gap-2">
          {fileListOrigin &&
            fileListOrigin.map((file) => {
              console.log(file);
              return (
                <div key={file.uid}>
                  {/* Always show progress bar for all files */}
                  <div className="w-full px-3">
                    {fileProgress[file.uid] &&
                    Math.round(fileProgress[file.uid]) > 0 ? (
                      <Progress
                        showInfo={false}
                        size="small"
                        percent={fileProgress[file.uid]}
                        status="active"
                        strokeColor="#1fa6aa"
                      />
                    ) : null}
                  </div>

                  <FileItem
                    loading={fileProgress[file.uid]}
                    file={{
                      fileType: file.type,
                      fileName: file.name,
                      fileSize: file.size || 0,
                      fileUrl: $url(file.url) || noImg,
                    }}
                    onDelete={() => handleDelete(file.uid)}
                  />
                </div>
              );
            })}
        </div>
      )}
    </div>
  );
};
