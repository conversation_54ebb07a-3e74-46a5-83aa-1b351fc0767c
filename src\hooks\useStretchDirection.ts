import { stretchDirectionApi } from "api/stretchDirection.api";
import { useState } from "react";
import { StretchDirection } from "types/stretchDirection";
import { QueryParam } from "types/query";

export interface StretchDirectionQuery extends QueryParam {}

interface UseStretchDirectionProps {
  initQuery: StretchDirectionQuery;
}

export const useStretchDirection = ({
  initQuery,
}: UseStretchDirectionProps) => {
  const [data, setData] = useState<StretchDirection[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<StretchDirectionQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await stretchDirectionApi.findAll(query);

      setData(data.stretchDirections);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return {
    stretchDirections: data,
    total,
    fetchData,
    loading,
    setQuery,
    query,
  };
};
