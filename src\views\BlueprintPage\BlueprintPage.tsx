import {
  Card,
  Spin,
  Button,
  Space,
  Tooltip,
  Modal,
  Tag,
  Avatar,
  message,
  Select,
} from "antd";
import { AppstoreOutlined, UnorderedListOutlined } from "@ant-design/icons";
import PencilIcon from "assets/svgs/PencilIcon";
import CustomButton from "components/Button/CustomButton";
import CustomInput from "components/Input/CustomInput";
import PageTitle from "components/PageTitle/PageTitle";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import { Pagination } from "components/Pagination";
import { useTheme } from "context/ThemeContext";
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { PermissionNames } from "types/PermissionNames";
import dayjs from "dayjs";
import { checkRoles, filterActionColumnIfNoPermission } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { observer } from "mobx-react";
import { $url } from "utils/url";
import logoImage from "assets/images/logo.png";
import { Staff } from "types/staff";
import { unixToDate } from "utils/dateFormat";
import DeleteIcon from "assets/svgs/DeleteIcon";
import QueryLabel from "components/QueryLabel/QueryLabel";
import { DictionarySelector } from "components/Selector/DictionarySelector";
import { DictionaryType } from "types/dictionary";
import { use } from "echarts";
import { useDraw } from "hooks/useDraw";
import { Draw } from "types/draw";
import { drawApi } from "api/draw.api";
import GridView from "./components/GridViewModal";
import EditButton from "components/Button/EditButton";
import DeleteButton from "components/Button/DeleteButton";
import { getTitle } from "utils";
import ProgressLegend from "components/ProgressLegend/ProgressLegend";
import {
  getOverallApprovalStatus,
  ProgressInstructionStatus,
} from "types/instruction";
import { StepItem } from "components/ApproveProcess/ApprovalStepsCard";
import {
  findLastApprovalStep,
  transformApproveData,
} from "components/ApproveProcess/approveUtil";

function BlueprintPage({ title }: { title: string }) {
  const {
    haveAddPermission,
    haveDeletePermission,
    haveEditPermission,
    haveViewAllPermission,
  } = checkRoles(
    {
      add: PermissionNames.blueprintAdd,
      edit: PermissionNames.blueprintEdit,
      delete: PermissionNames.blueprintDelete,
      viewAll: PermissionNames.blueprintViewAll,
    },
    permissionStore.permissions
  );

  const [searchValue, setSearchValue] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [loadingDelete, setLoadingDelete] = useState(false);
  const [viewMode, setViewMode] = useState<"table" | "grid">("table");
  const [page, setPage] = React.useState(1);
  const [limit, setLimit] = React.useState(20);

  const {
    fetchDraw,
    draws,
    loadingDraw,
    queryDraw,
    setQueryDraw,
    totalDraw,
    isEmptyQueryDraw,
  } = useDraw({
    initQuery: {
      limit: 10,
      page: 1,
      isAdmin: haveViewAllPermission ? true : undefined,
    },
  });

  useEffect(() => {
    document.title = getTitle(title);
    fetchDraw();
  }, []);

  // Refresh data when component comes into focus (e.g., when navigating back from create/edit page)
  useEffect(() => {
    const handleFocus = () => {
      fetchDraw();
    };

    window.addEventListener("focus", handleFocus);
    return () => {
      window.removeEventListener("focus", handleFocus);
    };
  }, [fetchDraw]);

  const { darkMode } = useTheme();
  const navigate = useNavigate();

  const handleSearch = (value: string) => {
    setSearchValue(value);
  };

  const handleStatusChange = (value: string) => {
    setStatusFilter(value);
    const newQuery = {
      ...queryDraw,
      page: 1,
      status: value || undefined,
    };
    setQueryDraw(newQuery);

    setTimeout(() => {
      fetchDraw();
    }, 100);
  };

  const handleCreateBlueprint = () => {
    navigate(`/doc-management/${PermissionNames.blueprintAdd}`);
  };

  const handleDeleteDraw = async (id: number) => {
    try {
      await drawApi.delete(id);
      message.success("Xóa bản vẽ thành công");
      fetchDraw();
    } catch (e) {
      console.log({ e });
    } finally {
    }
  };

  const handleRowClick = (record: Draw) => {
    navigate(
      `/doc-management/${PermissionNames.blueprintEdit.replace(
        ":id",
        record!.id + ""
      )}`
    );
  };

  const columns: CustomizableColumn<any>[] = [
    {
      key: "code",
      title: "Mã",
      dataIndex: "code",
      width: 100,
      defaultVisible: true,
      alwaysVisible: false,
      align: "center",

      render: (_, record) => {
        return (
          <div
            className="text-[#1677ff] cursor-pointer"
            onClick={() => handleRowClick(record)}
          >
            {record.code}
          </div>
        );
      },
    },
    {
      key: "title",
      title: "Tên bản vẽ",
      dataIndex: "title",
      width: 450,
      defaultVisible: true,
      alwaysVisible: false,
    },
    {
      key: "section",
      title: "Hạng mục",
      dataIndex: "section",
      width: 150,
      defaultVisible: true,
      alwaysVisible: false,
    },
    {
      key: "drawCategory",
      title: "Loại bản vẽ",
      dataIndex: "drawCategory",
      width: 150,
      defaultVisible: true,
      alwaysVisible: false,
      render: (_, record) => {
        return <div>{record.drawCategory?.name || "Chưa có loại bản vẽ"}</div>;
      },
    },
    {
      key: "createdAt",
      title: "Ngày upload",
      width: 130,
      dataIndex: "createdAt",
      render: (_, record: Draw) => (
        <div className="service-cell">
          <div className="service-info">
            <div className="service-name">
              {record.createdAt && record?.createdAt > 0
                ? unixToDate(record.createdAt || 0)
                : ""}
            </div>
          </div>
        </div>
      ),
      defaultVisible: true,
    },
    {
      key: "status",
      title: "Trạng thái",
      width: 100,
      align: "center",
      dataIndex: "status",
      render: (status, record) => {
        // <div className="justify-center flex">
        //   {record.isActive ? (
        //     <Tag color="green" className="status-tag !mr-0">
        //       Hoạt động
        //     </Tag>
        //   ) : (
        //     <Tag color="red" className="status-tag !mr-0">
        //       Bị khóa
        //     </Tag>
        //   )}
        // </div>
        // const sortedApprovalLists = record.approvalLists
        //   ? record.approvalLists
        //       .slice()
        //       .sort(
        //         (a: { position: number }, b: { position: number }) =>
        //           a.position - b.position
        //       )
        //   : [];
        const lastApprovalStep: StepItem | undefined = findLastApprovalStep(
          transformApproveData(record.approvalLists, record.createdBy)
        );
        return (
          <ProgressLegend
            // status={
            //   ProgressInstructionStatus[
            //     getOverallApprovalStatus(sortedApprovalLists)
            //   ]
            // }
            statusColor={lastApprovalStep?.statusColor}
            statusText={lastApprovalStep?.statusText}
            steps={record.approvalLists}
          />
        );
      },
      defaultVisible: true,
    },
    {
      key: "actions",
      title: "Xử lý",
      width: 100,
      align: "center",
      fixed: "right",
      defaultVisible: true,
      alwaysVisible: true,
      render: (_, record) => (
        <Space size="small">
          {haveEditPermission && (
            <EditButton
              onClick={(e) => {
                e.stopPropagation();
                navigate(
                  `/doc-management/${PermissionNames.blueprintEdit.replace(
                    ":id",
                    record!.id + ""
                  )}?update=1`
                );
              }}
            />
          )}

          {haveDeletePermission && (
            <DeleteButton
              onClick={(e) => {
                e.stopPropagation();
                Modal.confirm({
                  title: `Xóa bản vẽ "${record.title}"`,
                  getContainer: () => {
                    return document.getElementById("App") as HTMLElement;
                  },
                  icon: null,
                  content: (
                    <>
                      <div>
                        Bạn sẽ không thể khôi phục dữ liệu này sau khi xóa.
                        <br />
                        Bạn có chắc chắn muốn xóa dữ liệu này?
                      </div>
                    </>
                  ),
                  footer: (_, { OkBtn, CancelBtn }) => (
                    <>
                      <CustomButton
                        variant="outline"
                        className="cta-button"
                        onClick={() => {
                          handleDeleteDraw(record.id);
                          Modal.destroyAll();
                        }}
                      >
                        Có
                      </CustomButton>
                      <CustomButton
                        onClick={() => {
                          Modal.destroyAll();
                        }}
                        className="cta-button"
                      >
                        Không
                      </CustomButton>
                    </>
                  ),
                });
              }}
            />
          )}
        </Space>
      ),
    },
  ];

  return (
    <div className="app-container">
      <PageTitle
        title="Bản vẽ"
        breadcrumbs={["Quản lý tài liệu & bản vẽ", title]}
        extra={
          <Space>
            {haveAddPermission && (
              <CustomButton
                size="small"
                showPlusIcon
                onClick={handleCreateBlueprint}
              >
                Tạo bản vẽ
              </CustomButton>
            )}
          </Space>
        }
      />

      <Card>
        <div className="pb-[16px]">
          <div className="flex justify-between items-end mb-4">
            {/* Filter section */}
            <div className="flex gap-4">
              <div className="flex gap-[16px] items-center w-[300px]">
                <CustomInput
                  tooltipContent={"Tìm theo mã, tên bản vẽ"}
                  label="Tìm kiếm"
                  placeholder="Tìm kiếm"
                  onPressEnter={() => {
                    queryDraw.page = 1;
                    setQueryDraw({ ...queryDraw });
                    fetchDraw();
                  }}
                  value={queryDraw.search}
                  onChange={(value) => {
                    queryDraw.search = value;
                    setQueryDraw({ ...queryDraw });

                    if (!value) {
                      fetchDraw();
                    }
                  }}
                  allowClear
                />
              </div>

              <div className="flex flex-col" style={{ width: 220 }}>
                <QueryLabel>Loại bản vẽ</QueryLabel>
                <DictionarySelector
                  placeholder="Chọn loại bản vẽ"
                  initQuery={{
                    type: DictionaryType.DrawCategory,
                    isActive: true,
                  }}
                  showSearch
                  allowClear={true}
                  addonOptions={[
                    {
                      id: 0,
                      name: "Tất cả loại bản vẽ",
                    },
                  ]}
                  value={queryDraw.drawCategoryId || undefined}
                  onChange={(value) => {
                    queryDraw.drawCategoryId = value || undefined;
                    setQueryDraw({ ...queryDraw });
                  }}
                />
              </div>

              <div className="flex flex-col">
                <QueryLabel>Trạng thái</QueryLabel>
                <Select
                  placeholder="Chọn trạng thái"
                  allowClear={true}
                  value={queryDraw.isActive}
                  onChange={(value) => {
                    queryDraw.isActive = value;
                    setQueryDraw({ ...queryDraw });
                  }}
                  style={{
                    minWidth: 200,
                  }}
                  showSearch
                  filterOption={(input, option) =>
                    (typeof option?.label === "string"
                      ? option.label
                      : String(option?.label ?? "")
                    )
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  options={[
                    { value: true, label: "Hoạt động" },
                    { value: false, label: "Bị khóa" },
                  ]}
                />
              </div>

              {/* Thêm div wrapper cho buttons với style align */}
              <div className="flex flex-col justify-end">
                <div className="flex gap-2">
                  <CustomButton
                    onClick={() => {
                      if (!queryDraw.drawCategoryId)
                        delete queryDraw.drawCategoryId;
                      if (queryDraw.isActive === undefined)
                        delete queryDraw.isActive;
                      queryDraw.page = 1;
                      setQueryDraw({ ...queryDraw });
                      fetchDraw();
                    }}
                  >
                    Áp dụng
                  </CustomButton>

                  {!isEmptyQueryDraw && (
                    <CustomButton
                      variant="outline"
                      onClick={() => {
                        delete queryDraw.drawCategoryId;
                        delete queryDraw.isActive;
                        delete queryDraw.search;
                        delete queryDraw.queryObject;
                        queryDraw.page = 1;
                        setQueryDraw({ ...queryDraw });
                        fetchDraw();
                      }}
                    >
                      Bỏ lọc
                    </CustomButton>
                  )}
                </div>
              </div>
            </div>

            {/* View Mode Toggle */}
            <div className="flex items-center">
              <div className="flex border rounded-md overflow-hidden">
                <Button
                  type={viewMode === "grid" ? "primary" : "default"}
                  size="small"
                  icon={<AppstoreOutlined />}
                  onClick={() => setViewMode("grid")}
                  style={{
                    borderRadius: 0,
                  }}
                />
                <Button
                  type={viewMode === "table" ? "primary" : "default"}
                  size="small"
                  icon={<UnorderedListOutlined />}
                  onClick={() => setViewMode("table")}
                  style={{
                    borderRadius: 0,
                    borderRight: "1px solid #d9d9d9",
                  }}
                />
              </div>
            </div>
          </div>

          <Spin spinning={loadingDraw}>
            {viewMode === "table" ? (
              <CustomizableTable
                columns={filterActionColumnIfNoPermission(columns, [
                  haveEditPermission,
                  haveDeletePermission,
                ])}
                dataSource={draws}
                rowKey="id"
                loading={loadingDraw}
                pagination={false}
                scroll={{ x: 1200 }}
                bordered
                displayOptions
                onRowClick={handleRowClick}
              />
            ) : (
              <GridView
                data={draws}
                onEdit={(record) => {
                  navigate(
                    `/doc-management/${PermissionNames.blueprintEdit.replace(
                      ":id",
                      record!.id + ""
                    )}?update=1`
                  );
                }}
                onDelete={handleDeleteDraw}
                onView={(record) => {
                  navigate(
                    `/doc-management/${PermissionNames.blueprintEdit.replace(
                      ":id",
                      record!.id + ""
                    )}`
                  );
                }}
              />
            )}
          </Spin>
        </div>

        <Pagination
          currentPage={queryDraw.page}
          total={totalDraw}
          defaultPageSize={queryDraw.limit}
          onChange={({ limit, page }) => {
            queryDraw.page = page;
            queryDraw.limit = limit;
            setQueryDraw({ ...queryDraw });
            setPage(page);
            setLimit(limit);
            fetchDraw();
          }}
        />
      </Card>
    </div>
  );
}

export default observer(BlueprintPage);
