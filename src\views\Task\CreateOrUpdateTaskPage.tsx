// This file combines Create and Edit Indicative Page into a single component
import {
  Card,
  Col,
  Row,
  Form,
  Button,
  Spin,
  Tabs,
  message,
  Input,
  Tag,
  DatePicker,
  Checkbox,
  Select,
} from "antd";
import PageTitle from "components/PageTitle/PageTitle";
import React, { useEffect, useMemo, useState } from "react";
import { FormInstance, Rule } from "antd/lib/form";
import CustomButton from "components/Button/CustomButton";
import { ModalStatus } from "types/modal";
import { PermissionNames } from "types/PermissionNames";
import {
  useNavigate,
  useParams,
  useSearchParams,
  useLocation,
} from "react-router-dom";
import { FileAttach } from "types/fileAttach";
import { isEmpty } from "lodash";
import { getTitle } from "utils";
import { FileUploadMultiple2 } from "components/Upload/FileUploadMultiple2";
import { ProjectSelector } from "components/Selector/ProjectSelector";
import { useWatch } from "antd/es/form/Form";
import { StaffSelector } from "components/Selector/StaffSelector";
import { DictionarySelector } from "components/Selector/DictionarySelector";
import { DictionaryType } from "types/dictionary";
import { fileAttachApi } from "api/fileAttach.api";
import { $url } from "utils/url";
import dayjs, { Dayjs } from "dayjs";
import { Staff } from "types/staff";
import { CustomizableColumn } from "components/Table/CustomizableTable";
import { StopOutlined } from "@ant-design/icons";
import { WorkStatusTrans } from "types/workStatus";
import { useStaff2 } from "hooks/useStaff2";
import { FollowerSelector } from "../../components/Follower/FollowerSelector";
import {
  ApprovalStepsCard,
  StepItem,
} from "../../components/ApproveProcess/ApprovalStepsCard";
import clsx from "clsx";
import { observer } from "mobx-react";
import { checkEditPermissionByCreator, checkRoles } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { approvalListApi } from "api/approvalList.api";
import { ApprovalListStatus, ApprovalListType } from "types/approvalList";
import { transformApproveData } from "components/ApproveProcess/approveUtil";
import { CommentView } from "components/Comment/CommentView";
import { settings } from "settings";
import {
  Task,
  TaskForm,
  TaskPriority,
  TaskPriorityTrans,
  TaskModule,
} from "types/task";
import { rfiApi } from "api/rfi.api";
import { instructionApi } from "api/instruction.api";
import { drawApi } from "api/draw.api";
import { taskApi } from "api/task.api";
import { TaskTemplateSelector } from "components/Selector/TaskTemplateSelector";
import { ProjectItemSelector } from "components/Selector/ProjectItemSelector";
import { ApprovalTemplateType } from "types/approvalTemplate";
import { appStore } from "store/appStore";
import { approvalTemplateApi } from "api/approvalTemplate.api";
import { MembershipSelector } from "components/Selector/MembershipSelector";
import { InputNumber } from "components/Input/InputNumber";
import TextArea from "antd/es/input/TextArea";
import { TaskSelector } from "components/Selector/TaskSelector";
import { useApprovalStep } from "hooks/useAppovalStep";
import { toJS } from "mobx";
import { userStore } from "store/userStore";
import { TaskType } from "types/task";
import TaskTableView from "views/TaskTemplate/components/TaskTableView";
import { Todo } from "types/todo";
import { BMDTextArea } from "components/TextArea/BMDTextArea";
import { TaskDependencyTable } from "./components/TaskDependencyTable";
import { BMDCKEditor } from "components/Editor";

const rules: Rule[] = [{ required: true, message: "Trường này là bắt buộc" }];
const descriptionRules: Rule[] = [{ required: false }];

interface EditTaskPageProps {
  title: string;
  status: ModalStatus;
  parentId?: number;
}

const APPROVAL_TEMPLATE_TYPE = ApprovalTemplateType.Task;

function CreateOrUpdateTaskPage({
  title = "",
  status,
  parentId,
}: EditTaskPageProps) {
  const { haveEditPermission, haveViewAllPermission } = checkRoles(
    {
      edit: PermissionNames.indicativeEdit,
      viewAll: PermissionNames.taskViewAll,
    },
    permissionStore.permissions
  );

  const [form] = Form.useForm<TaskForm>();
  const [loading, setLoading] = useState(false);
  const [selectedTask, setSelectedTask] = useState<Task>();
  const [fileList, setFileList] = useState<FileAttach[]>([]);
  const navigate = useNavigate();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedStaffs, setSelectedStaffs] = useState<Staff[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);

  const {
    followers,
    setFollowers,
    approvalSteps,
    setApprovalSteps,
    fetchApprovalTemplate,
  } = useApprovalStep();

  const [removeApprovalList, setRemoveApprovalList] = useState<number[]>([]);
  const [loadingApprove, setLoadingApprove] = useState(false);
  const [commentRefreshTrigger, setCommentRefreshTrigger] = useState(0);
  const [isLoaded, setIsLoaded] = useState(false);

  // State cho module liên kết
  const [selectedModule, setSelectedModule] = useState<string | undefined>();
  const [moduleOptions] = useState([
    { label: "RFI", value: TaskModule.RFI },
    { label: "Instruction", value: TaskModule.Instruction },
    { label: "Draw", value: TaskModule.Draw },
    { label: "Task", value: TaskModule.Task },
  ]);
  const [moduleData, setModuleData] = useState<any[]>([]);
  const [moduleDataLoading, setModuleDataLoading] = useState(false);
  const [selectedModuleId, setSelectedModuleId] = useState<
    number | undefined
  >();

  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys: React.Key[], selectedRows: Staff[]) => {
      setSelectedRowKeys(selectedKeys as number[]);
      setSelectedStaffs(selectedRows);
    },
  };

  const { fetchData, staffs, query, setQuery, total } = useStaff2({
    initQuery: { limit: 10, page: 1 },
  });

  const handleAddFollowers = () => {
    setIsModalVisible(true);
  };

  const [readonly, setReadonly] = useState(true);
  const [loadingFetch, setLoadingFetch] = useState(false);
  const params = useParams();
  const [searchParams, setSearchParams] = useSearchParams();
  const type = useWatch("type", form);

  const location = useLocation();

  // Cập nhật setDataToForm để set module/moduleId
  const setDataToForm = (data: Task) => {
    form.setFieldsValue({
      ...data,
      projectId: data.project?.id,
      dueAtDate: data.dueAt ? dayjs.unix(data.dueAt) : undefined,
      projectItemId: data.projectItem?.id,
      parentId: data.parent?.id,
      assigneeMemberShipId: data.assigneeMemberShip?.id,
      coordinatorIds: (data as any).coordinators?.map((e: any) => e.id) || [],
      departmentId: data.department?.id,
      workTypeIds: data.workTypes?.map((e) => e.id) || [],
      module: data.module || undefined,
      moduleId: data.moduleId || undefined,
    });
    setSelectedModule(data.module || undefined);
    setSelectedModuleId(data.moduleId || undefined);

    // setFileList(data.fileAttaches ? [...data.fileAttaches] : []);
    const transformedApproveData = transformApproveData(
      data.approvalLists,
      data.createdBy
    );
    setApprovalSteps(transformedApproveData);
    setFollowers(data.followMemberShips || []);
  };

  const getOneTask = async (id: number) => {
    try {
      setLoadingFetch(true);
      const { data } = await taskApi.findOne(id);

      if (isEmpty(data)) {
        navigate("/404");

        return;
      }

      setSelectedTask(data);
      setDataToForm(data);

      //   if (data.serviceType) {
      //     setServiceTypes([data.serviceType]);
      //   }

      return data as Task;
    } catch (e: any) {
    } finally {
      setLoadingFetch(false);
    }
  };

  // Fetch data khi chọn module
  useEffect(() => {
    if (!selectedModule) {
      setModuleData([]);
      setSelectedModuleId(undefined);
      return;
    }
    setModuleDataLoading(true);
    let apiPromise: Promise<any> | null = null;
    if (selectedModule === TaskModule.RFI)
      apiPromise = rfiApi.findAll({ limit: 50, page: 1 });
    else if (selectedModule === TaskModule.Instruction)
      apiPromise = instructionApi.findAll({ limit: 50, page: 1 });
    else if (selectedModule === TaskModule.Draw)
      apiPromise = drawApi.findAll({ limit: 50, page: 1 });
    else if (selectedModule === TaskModule.Task)
      apiPromise = taskApi.findAll({ limit: 50, page: 1 });
    else apiPromise = null;
    if (apiPromise) {
      apiPromise
        .then((res) => {
          let list: any[] = [];
          if (selectedModule === TaskModule.RFI) list = res.data?.rfis || [];
          else if (selectedModule === TaskModule.Instruction)
            list = res.data?.instructions || [];
          else if (selectedModule === TaskModule.Draw)
            list = res.data?.draws || [];
          else if (selectedModule === TaskModule.Task)
            list = res.data?.tasks || [];
          console.log("API data for module", selectedModule, list);
          setModuleData(list);
        })
        .finally(() => setModuleDataLoading(false));
    } else {
      setModuleData([]);
      setModuleDataLoading(false);
    }
  }, [selectedModule]);

  useEffect(() => {
    document.title = getTitle(title);

    if (status == "update") {
      const taskId = params.id;

      if (taskId) {
        getOneTask(+taskId);

        setReadonly(searchParams.get("update") != "1");

        // searchParams.delete("serviceId");
        // setSearchParams(searchParams);
      }
    } else {
      setReadonly(false);

      if (!appStore.currentProject) {
        return;
      }

      const projectId = appStore.currentProject.id;

      fetchApprovalTemplate({
        projectId: appStore.currentProject.id,
        createdStaff: toJS(userStore.info) as Staff,
        type: ApprovalTemplateType.Task,
      });

      // Không tự động set projectId từ currentProject nữa
      // form.setFieldsValue({
      //   projectId,
      // });
    }

    fetchData();

    setIsLoaded(true);
  }, []);

  useEffect(() => {
    // Xử lý copyData khi tạo mới
    if (
      status === "create" &&
      location.state &&
      typeof location.state === "object" &&
      "copyData" in location.state
    ) {
      const locationState = location.state as { copyData: any };
      const copyData = locationState.copyData;
      if (copyData.id) {
        // Nếu có id thì gọi API lấy chi tiết
        getOneTask(Number(copyData.id)).then((data) => {
          if (data) {
            // Xóa id, code để tránh trùng lặp
            setDataToForm({ ...data, code: "" });
          }
        });
      } else {
        setDataToForm(copyData);
      }
    }
  }, [status, location.state]);

  // Cập nhật getDataSubmit để lấy module/moduleId
  const getDataSubmit = async () => {
    const {
      projectId,
      createdById,
      parentId: formParentId, // lấy parentId từ form nếu có
      projectItemId,
      dueAtDate,
      createdDate,
      assigneeMemberShipId,
      coordinatorIds,
      departmentId,
      workTypeIds,
      todos,
      module,
      moduleId,
      ...data
    } = form.getFieldsValue();

    const fileAttachIds: number[] = [];

    for (const file of fileList) {
      if (file.id) {
        fileAttachIds.push(file.id);
      } else if (file.originFile) {
        const { data } = await fileAttachApi.upload(file.originFile);

        const resFileAttach = await fileAttachApi.create({
          fileAttach: {
            ...file,
            url: $url(data.path),
          },
        });

        fileAttachIds.push(resFileAttach.data.id);
      }
    }

    const approvalLists = approvalSteps.map((e, i) => ({
      id: e.id,
      name: e.name,
      type: ApprovalListType.Task,
      position: e.position,
      note: e.note,
      memberShipId: e.memberShipId,
      memberShip2Id: e.memberShip2Id,
      staffId: e.staffId,
    }));

    // Nếu có prop parentId thì ưu tiên dùng, còn không thì lấy từ form
    const parentIdToUse = parentId !== undefined ? parentId : formParentId;

    const payload: any = {
      task: {
        ...data,
        type: TaskType.Task,
        startDate: createdDate ? createdDate.format("YYYY-MM-DD") : "",
        endDate: dueAtDate ? dueAtDate.format("YYYY-MM-DD") : "",
        dueAt: dueAtDate ? dueAtDate.unix() : 0,
        module: selectedModule || undefined,
        moduleId: selectedModuleId || undefined,
      },
      fileAttachIds: fileAttachIds || [],
      projectId,
      departmentId: departmentId || 0,
      followMemberShipIds: followers?.map((it) => it.id),
      coordinatorIds: coordinatorIds || [],
      todos: todos?.map((item) => ({
        ...item,
        id: item.id > 0 ? item.id : undefined,
        isActive: item.isActive || false,
      })),
      projectItemId,
      workTypeIds,
      assigneeMemberShipId,
      approvalLists,
    };

    // Nếu có parentId thì truyền ra ngoài object task
    if (parentIdToUse !== undefined) {
      payload.parentId = parentIdToUse;
    }

    console.log(payload);

    return payload;
  };

  const createData = async () => {
    const valid = await form.validateFields();

    setLoading(true);
    try {
      const res = await taskApi.create(await getDataSubmit());

      message.success("Tạo công việc thành công!");
      navigate(`/${PermissionNames.taskList}`);
      setFileList([]);
    } finally {
      setLoading(false);
    }
  };

  const updateData = async () => {
    const valid = await form.validateFields();

    setLoading(true);
    try {
      const res = await taskApi.update(
        selectedTask!?.id || 0,
        await getDataSubmit()
      );

      setSelectedTask({ ...selectedTask, ...res.data });
      setRemoveApprovalList([]);

      message.success("Chỉnh sửa công việc thành công!");
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = () => {
    if (status == "create") {
      createData();
    } else {
      updateData();
    }
  };

  // const handleApproveProcess = async (data: ApproveData) => {
  //   console.log("Approve process");
  //   try {
  //     setLoadingApprove(true);
  //     await taskApi.approve(selectedTask!.id || 0, data);
  //     message.success("Duyệt chỉ thị công trường thành công!");
  //     await getOneTask(selectedTask!.id || 0);
  //     // Trigger refresh CommentView
  //     setCommentRefreshTrigger((prev) => prev + 1);
  //   } finally {
  //     setLoadingApprove(false);
  //   }
  // };

  // const handleRejectProcess = async (data: ApproveData) => {
  //   console.log("Reject process");
  //   try {
  //     setLoadingApprove(true);
  //     await instructionApi.reject(selectedTask!.id || 0, data);
  //     message.success("Từ chối chỉ thị công trường thành công!");
  //     await getOneInstruction(selectedTask!.id || 0);
  //     // Trigger refresh CommentView
  //     setCommentRefreshTrigger((prev) => prev + 1);
  //   } finally {
  //     setLoadingApprove(false);
  //   }
  // };

  const pageTitle = useMemo(
    () => (status == "create" ? "Tạo công việc" : "Chỉnh sửa công việc"),
    [status]
  );

  const canEditRecord = (record: Task) => {
    if (!record) return false;

    return checkEditPermissionByCreator(
      userStore.info.id,
      record.createdBy?.id,
      haveEditPermission,
      haveViewAllPermission
    );
  };

  if (!isLoaded) {
    return null;
  }

  return (
    <div className="app-container">
      <PageTitle
        back
        breadcrumbs={[
          { label: "Báo cáo" },
          {
            label: "Công việc",
            href: `/progress-management/${PermissionNames.taskList}`,
          },
          { label: pageTitle },
        ]}
        title={pageTitle}
      />
      <Spin spinning={loadingFetch}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            createdDate: dayjs(),
            type: TaskType.Task,
          }}
          disabled={readonly}
          className={clsx(readonly ? "readonly" : "")}
        >
          <Row gutter={24}>
            <Col span={18}>
              <Card className="content-card ">
                {/* Chọn từ công việc mẫu */}
                {!readonly && (
                  <Card
                    title="Chọn từ công việc mẫu"
                    className="mb-0 form-card"
                  >
                    <Row gutter={8}>
                      <Col flex="auto">
                        <Form.Item
                          name="taskTemplateId"
                          style={{ marginBottom: 0 }}
                        >
                          <TaskTemplateSelector placeholder="Chọn từ công việc mẫu" />
                        </Form.Item>
                      </Col>
                      <Col>
                        <Button
                          type="primary"
                          onClick={() => {
                            // Xử lý khi bấm nút Chọn ở đây
                            message.warning("Tính năng đang phát triển");
                          }}
                          style={{ height: 35 }}
                        >
                          Chọn
                        </Button>
                      </Col>
                    </Row>
                  </Card>
                )}

                <Row gutter={16}>
                  {/* First Row */}
                  <Col span={6}>
                    <Form.Item name="code" label="Mã công việc">
                      <Input
                        disabled={status != "create"}
                        placeholder="Mã công việc"
                      />
                    </Form.Item>
                  </Col>

                  <Col span={12}>
                    <Form.Item name="title" label="Tiêu đề" rules={rules}>
                      <Input placeholder="Tiêu đề" />
                    </Form.Item>
                  </Col>

                  <Col span={6}>
                    <Form.Item
                      label="Loại công tác"
                      name="workTypeIds"
                      rules={rules}
                    >
                      <DictionarySelector
                        multiple
                        placeholder="Loại công tác"
                        initQuery={{
                          type: DictionaryType.WorkType,
                          isActive: true,
                        }}
                      />
                    </Form.Item>
                  </Col>

                  {/* Second Row */}
                  <Col span={6}>
                    <Form.Item name="projectId" label="Dự án" rules={rules}>
                      <ProjectSelector placeholder="Dự án" />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      name="projectItemId"
                      label="Hạng mục"
                      rules={rules}
                    >
                      <ProjectItemSelector placeholder="Hạng mục" />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      name="createdDate"
                      label="Ngày tạo"
                      rules={rules}
                    >
                      <DatePicker
                        className="w-full"
                        format={settings.dateFormat}
                        disabled={readonly}
                        placeholder="Ngày tạo"
                      />
                    </Form.Item>
                  </Col>
                  {/* Ẩn trường Công việc cha nếu có parentId (tức là tạo công việc phụ thuộc) hoặc đang chỉnh sửa */}
                  {parentId === undefined && status !== "update" && (
                    <Col span={6}>
                      <Form.Item name="parentId" label="Công việc cha">
                        <TaskSelector
                          placeholder="Công việc cha"
                          initQuery={{
                            ignoreId: params.id,
                            projectId: form.getFieldValue("projectId"),
                          }}
                        />
                      </Form.Item>
                    </Col>
                  )}

                  <Col span={6}>
                    <Form.Item
                      name="assigneeMemberShipId"
                      label="Người phụ trách"
                      rules={rules}
                    >
                      <MembershipSelector
                        onChange={(value, options) => {
                          const findMemberShip = options.find(
                            (e) => e.id == value
                          );

                          console.log("findMemberShip:", findMemberShip);

                          form.setFieldsValue({
                            departmentId: findMemberShip?.staff?.department?.id,
                          });
                        }}
                        placeholder="Chọn người phụ trách"
                      />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item name="coordinatorIds" label="Người phối hợp">
                      <MembershipSelector
                        multiple
                        placeholder="Chọn người phối hợp"
                      />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      name="dueAtDate"
                      label="Ngày đến hạn"
                      rules={rules}
                    >
                      <DatePicker
                        className="w-full"
                        format={settings.dateFormat}
                        disabled={readonly}
                        placeholder="Ngày đến hạn"
                      />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      name="duration"
                      label="Thời gian làm (Giờ)"
                      rules={rules}
                    >
                      <InputNumber placeholder="Thời gian làm" required />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      name="priority"
                      label="Mức độ ưu tiên"
                      rules={rules}
                    >
                      <Select
                        options={Object.keys(TaskPriorityTrans).map((k) => ({
                          label: TaskPriorityTrans[k as TaskPriority],
                          value: k,
                        }))}
                        style={{ width: "100%" }}
                      ></Select>
                    </Form.Item>
                  </Col>

                  {/* Second Row */}
                  <Col span={6}>
                    <Form.Item
                      label="Phòng ban"
                      name="departmentId"
                      required
                      rules={rules}
                    >
                      <DictionarySelector
                        placeholder="Chọn phòng ban"
                        initQuery={{
                          type: DictionaryType.Department,
                          isActive: true,
                        }}
                        showSearch
                      />
                    </Form.Item>
                  </Col>

                  {/* Fourth Row */}

                  <Col span={24}>
                    <Form.Item
                      name="description"
                      label="Mô tả"
                      rules={descriptionRules}
                    >
                      <BMDCKEditor
                        placeholder="Nhập mô tả"
                        value={selectedTask?.description}
                        disabled={readonly}
                        inputHeight={300}
                        onChange={(content) => {
                          form.setFieldsValue({ description: content });
                        }}
                      />
                    </Form.Item>
                  </Col>

                  <Col span={24}>
                    <Form.Item name="isPrivate" valuePropName="checked">
                      <Checkbox
                        style={{
                          fontWeight: 500,
                          fontSize: 15,
                        }}
                        className="private-checkbox"
                        disabled={readonly}
                      >
                        Riêng tư
                      </Checkbox>
                    </Form.Item>
                  </Col>
                  <Col span={24}>
                    <Form.Item label="Danh sách việc cần làm" name="todos">
                      <TaskTableView form={form as any} readonly={readonly} />
                    </Form.Item>
                  </Col>
                </Row>

                {/* Thông tin liên kết */}
                <Card title="Thông tin liên kết" className="mb-0 form-card">
                  <Row gutter={24}>
                    <Col span={12}>
                      <Form.Item name="module" label="Module liên kết">
                        <Select
                          placeholder="Chọn module liên kết"
                          options={moduleOptions}
                          value={selectedModule}
                          onChange={(val) => {
                            setSelectedModule(val);
                            setSelectedModuleId(undefined);
                            form.setFieldsValue({
                              module: val,
                              moduleId: undefined,
                            });
                          }}
                          disabled={readonly}
                          allowClear
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item name="moduleId" label="Dữ liệu liên kết">
                        <Select
                          placeholder="Chọn dữ liệu liên kết"
                          loading={moduleDataLoading}
                          value={selectedModuleId}
                          onChange={(val) => {
                            setSelectedModuleId(val as number);
                            form.setFieldsValue({ moduleId: val });
                          }}
                          disabled={readonly || !selectedModule}
                          options={(() => {
                            const opts = moduleData.map((item) => ({
                              label: item.code
                                ? item.title
                                  ? `${item.code} - ${item.title}`
                                  : item.code
                                : item.id,
                              value: item.id,
                            }));
                            console.log("Select options:", opts);
                            return opts;
                          })()}
                          showSearch
                          filterOption={(input, option) =>
                            (option?.label ?? "")
                              .toLowerCase()
                              .includes(input.toLowerCase())
                          }
                          allowClear
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </Card>

                <Tabs defaultActiveKey="0" type="line" className="mt-[16px]">
                  <Tabs.TabPane tab="Tệp đính kèm" key="0">
                    <Form.Item
                      shouldUpdate={true}
                      style={{ marginBottom: 0, height: "100%" }}
                      className="form-height-full"
                    >
                      {() => {
                        return (
                          <Form.Item
                            label={""}
                            noStyle
                            style={{ marginBottom: 0 }}
                            name="files"
                            className="h-full "
                          >
                            <FileUploadMultiple2
                              hideUploadButton={readonly}
                              showSearch
                              className="h-full"
                              fileList={fileList}
                              onUploadOk={(file) => {
                                fileList.push(file);
                                setFileList([...fileList]);
                              }}
                              onDelete={(file) => {
                                const findIndex = fileList.findIndex(
                                  (e) => e.uid == file.uid
                                );

                                if (findIndex > -1) {
                                  fileList.splice(findIndex, 1);
                                  setFileList([...fileList]);
                                }
                              }}
                            />
                          </Form.Item>
                        );
                      }}
                    </Form.Item>
                  </Tabs.TabPane>
                  {selectedTask && (
                    <Tabs.TabPane tab="Công việc phụ thuộc" key="1">
                      <TaskDependencyTable parentId={selectedTask.id} />
                    </Tabs.TabPane>
                  )}
                  {selectedTask && (
                    <Tabs.TabPane tab="Bình luận" key="2">
                      <CommentView
                        initQuery={{ taskId: selectedTask.id }}
                        refreshTrigger={commentRefreshTrigger}
                      />
                    </Tabs.TabPane>
                  )}
                </Tabs>

                {/* Action Buttons */}
                <div
                  className="mt-[16px]"
                  style={{
                    display: "flex",
                    justifyContent: "flex-end",
                    gap: "12px",
                  }}
                >
                  {!readonly && (
                    <CustomButton
                      variant="outline"
                      className="cta-button"
                      onClick={() => {
                        if (status == "create") {
                          navigate(
                            `/progress-management/${PermissionNames.taskList}`
                          );
                        } else {
                          setDataToForm(selectedTask!);
                          setReadonly(true);
                        }
                      }}
                    >
                      Hủy
                    </CustomButton>
                  )}
                  <CustomButton
                    className="cta-button"
                    loading={loading}
                    onClick={() => {
                      if (!readonly) {
                        handleSubmit();
                      } else {
                        setReadonly(false);
                      }
                    }}
                    disabled={
                      status == "update" &&
                      (!selectedTask || !canEditRecord(selectedTask))
                    }
                  >
                    {status == "create"
                      ? "Tạo công việc"
                      : readonly
                      ? "Chỉnh sửa"
                      : "Lưu chỉnh sửa"}
                  </CustomButton>
                </div>
              </Card>
            </Col>

            <Col span={6}>
              {/* quy trình duyệt */}
              <ApprovalStepsCard
                steps={approvalSteps}
                loading={loadingApprove}
                onSelectStep={setApprovalSteps}
                onRemove={setRemoveApprovalList}
                // onApprove={handleApproveProcess}
                // onReject={handleRejectProcess}
                templateType={ApprovalTemplateType.Task}
                editable={true}
              />

              {/* người theo dõi */}
              <FollowerSelector
                //@ts-ignore
                followers={followers}
                //@ts-ignore
                setFollowers={setFollowers}
                readonly={readonly}
                headerTitle={`Người theo dõi (${followers?.length})`}
              />
            </Col>
          </Row>
        </Form>
      </Spin>
    </div>
  );
}

export default observer(CreateOrUpdateTaskPage);
