import { AxiosPromise } from "axios";
import { request } from "utils/request";

export const accountApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/account",
      params,
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/account/${id}`,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/account",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/account/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/account/${id}`,
      method: "delete",
    }),
  block: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/account/${id}/block`,
      method: "patch",
      data,
    }),
  resetPassword: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/account/${id}/password`,
      method: "patch",
      data,
    }),
};
