import { useCallback, useEffect, useMemo, useState } from "react";
import { Button, Space, Table, Tooltip } from "antd";
import { EnterOutlined, PlusCircleOutlined } from "@ant-design/icons";
import styled from "styled-components";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import EditButton from "components/Button/EditButton";
import DeleteButton from "components/Button/DeleteButton";
import "./TreeDataTableView.scss";
import { filterActionColumnIfNoPermission } from "utils/auth";
import { ArrowDownIcon } from "assets/svgs/ArrowDownIcon";
import { useTheme } from "context/ThemeContext";
import { ExpandableConfig } from "antd/es/table/interface";

export interface TreeDataItem {
  id: number;
  name: string;
  code: string;
  dictionaries: TreeDataItem[];
  description?: string;
  level?: number;
  isActive?: boolean;
  parentId?: number;
}

export interface TreeDataTableViewProps {
  data: TreeDataItem[];
  onAdd?: (parentId?: number) => void;
  onEdit?: (item: TreeDataItem, parentId?: number) => void;
  onDelete?: (item: TreeDataItem) => void;
  canAdd?: boolean;
  canEdit?: boolean;
  canDelete?: boolean;
  maxDepth?: number;
  showDescription?: boolean;
  expandedKeys?: Set<number>;
  onExpandedKeysChange?: (expandedKeys: Set<number>) => void;
  className?: string;
}

const TreeDataTableView: React.FC<TreeDataTableViewProps> = ({
  data,
  onAdd,
  onEdit,
  onDelete,
  canAdd = false,
  canEdit = false,
  canDelete = false,
  maxDepth = 5,
  showDescription = false,
  expandedKeys: controlledExpandedKeys,
  onExpandedKeysChange,
  className = "",
}) => {
  const [internalExpandedKeys, setInternalExpandedKeys] = useState<Set<number>>(
    new Set()
  );
  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);

  const isControlled = controlledExpandedKeys !== undefined;
  const expandedKeys = isControlled
    ? controlledExpandedKeys
    : internalExpandedKeys;
  const { darkMode } = useTheme();

  const expandAll = useCallback(() => {
    const getAllIds = (items: TreeDataItem[]): number[] => {
      return items.reduce((acc, item) => {
        acc.push(item.id);
        if (item.dictionaries?.length > 0) {
          acc.push(...getAllIds(item.dictionaries));
        }
        return acc;
      }, [] as number[]);
    };

    const allIds = new Set(getAllIds(data));
    if (isControlled) {
      onExpandedKeysChange?.(allIds);
    } else {
      setInternalExpandedKeys(allIds);
    }
  }, [data, isControlled, onExpandedKeysChange]);

  const collapseAll = useCallback(() => {
    const emptySet = new Set<number>();
    if (isControlled) {
      onExpandedKeysChange?.(emptySet);
    } else {
      setInternalExpandedKeys(emptySet);
    }
  }, [isControlled, onExpandedKeysChange]);

  const columns: CustomizableColumn<TreeDataItem>[] = useMemo(
    () => [
      {
        title: "Mã",
        dataIndex: "code",
        key: "code",
        render: (_, record) => (
          <div className="flex items-center gap-2">{record.code}</div>
        ),
      },
      {
        title: "Tên",
        dataIndex: "name",
        key: "name",
        render: (_, record) => (
          <div className="flex items-center gap-2">{record.name}</div>
        ),
      },
      {
        title: "Xử lý",
        key: "actions",
        fixed: "right",
        width: 100,
        align: "center",
        alwaysVisible: true,
        render: (_, record) => {
          const canAddChild =
            canAdd && (!maxDepth || (record.level || 0) < maxDepth);
          return (
            <Space size="small">
              {canAddChild && (
                <Tooltip title="Thêm con">
                  <Button
                    type="text"
                    icon={<PlusCircleOutlined style={{ fontSize: 20 }} />}
                    className="hierarchical-item__action-btn add-btn"
                    onClick={(e) => {
                      e.stopPropagation();
                      onAdd?.(record.id);
                    }}
                  />
                </Tooltip>
              )}
              {canEdit && (
                <EditButton
                  onClick={(e) => {
                    e.stopPropagation();
                    onEdit?.(record, record.parentId);
                  }}
                />
              )}
              {canDelete && (
                <DeleteButton
                  onClick={(e) => {
                    e.stopPropagation();
                    onDelete?.(record);
                  }}
                />
              )}
            </Space>
          );
        },
      },
    ],
    [data]
  );

  const expandedRowRender = useCallback((record: TreeDataItem) => {
    return (
      <Table
        columns={filterActionColumnIfNoPermission(columns, [
          canAdd || false,
          canEdit,
          canDelete,
        ])}
        dataSource={record.dictionaries}
        rowKey="id"
        loading={false}
        pagination={false}
        bordered={false}
        size="small"
        showHeader={false}
        className="role-sub-table"
        expandable={expandableConfig}
      />
    );
  }, []);

  const expandableConfig: ExpandableConfig<TreeDataItem> = {
    expandedRowRender,
    expandedRowKeys: expandedRowKeys,
    onExpandedRowsChange: (keys) => {
      console.log("keys:", keys);
      setExpandedRowKeys(keys as string[]);
    },
    expandIcon: ({ expanded, onExpand, record }) => (
      <Button
        type="text"
        size="small"
        icon={
          <ArrowDownIcon
            className={`transition-transform duration-200 ${
              expanded ? "rotate-0" : "-rotate-90"
            }`}
            fill={darkMode ? "#ffffff" : "#6b7280"}
          />
        }
        onClick={(e) => onExpand(record, e)}
        className="!border-0 !shadow-none hover:!bg-gray-100"
      />
    ),
    expandIconColumnIndex: 0,

    rowExpandable: (record) =>
      record.dictionaries && record.dictionaries.length > 0,
  };

  return (
    <div className={`hierarchical-view ${className}`}>
      <div className="hierarchical-view__header">
        <div className="hierarchical-view__controls">
          <Space size="small">
            <Button size="small" onClick={expandAll}>
              Mở rộng tất cả
            </Button>
            <Button size="small" onClick={collapseAll}>
              Thu gọn tất cả
            </Button>
          </Space>
        </div>
      </div>

      <CustomizableTable
        pagination={{ position: [] }}
        columns={filterActionColumnIfNoPermission(columns, [
          canEdit,
          canDelete,
          canAdd,
        ])}
        dataSource={data}
        rowKey={"id"}
        expandable={expandableConfig}
        className="expandable-membership-table"
        tableId="membership-page"
      />
    </div>
  );
};

export default TreeDataTableView;
