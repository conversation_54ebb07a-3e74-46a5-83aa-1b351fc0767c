import React from "react";
import { But<PERSON>, Tooltip } from "antd";
import "./CustomButton.scss";
import PencilIcon from "assets/svgs/PencilIcon";

interface CustomButtonProps {
  onClick?: React.MouseEventHandler<HTMLElement> | undefined;
  toolTipContent?: string;
}

const EditButton: React.FC<CustomButtonProps> = ({
  onClick,
  toolTipContent = "Chỉnh sửa",
}) => {
  return (
    <Tooltip title={toolTipContent} mouseEnterDelay={0.3}>
      <Button
        type="text"
        icon={<PencilIcon size={16} />}
        onClick={(e) => {
          e.stopPropagation();
          onClick?.(e);
        }}
      />
    </Tooltip>
  );
};

export default EditButton;
