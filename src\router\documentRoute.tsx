import { ReactComponent as BlueprintIcon } from "assets/svgs/blueprint.svg";
import { lazy } from "react";
import { PermissionType } from "types/permission";
import { PermissionNames } from "types/PermissionNames";
import { Route } from "./RouteType";

const BlueprintPage = lazy(() => import("views/BlueprintPage/BlueprintPage"));
const CreateOrUpdateBluePrintPage = lazy(
  () => import("views/BlueprintPage/CreateOrUpdateBluePrintPage")
);
const CreateOrUpdateProjectDocumentPage = lazy(
  () => import("views/ProjectDocument/CreateOrUpdateProjectDocumentPage")
);
const ImagePage = lazy(() => import("views/ImagePage/ImagePage"));
const CreateOrUpdateImagePage = lazy(
  () => import("views/ImagePage/CreateOrUpdateImagePage")
);

const ProjectDocumentPage = lazy(() =>
  import("views/ProjectDocument/ProjectDocumentPage")
);

export const documentRoutes: Route[] = [
  {
    title: "T<PERSON><PERSON> li<PERSON><PERSON> & b<PERSON><PERSON> vẽ",
    breadcrumb: "doc-management",
    path: "/doc-management",
    name: "doc-management",
    aliasPath: "/doc-management",
    icon: <BlueprintIcon />,
    permissionTypes: [
      PermissionType.Add,
      PermissionType.Delete,
      PermissionType.Edit,
      PermissionType.List,
    ],
    needProject: true,
    children: [
      {
        title: "Bản vẽ",
        breadcrumb: "Bản vẽ",
        path: PermissionNames.blueprintList,
        name: PermissionNames.blueprintList,
        aliasPath: `/doc-management/${PermissionNames.blueprintList}`,
        element: <BlueprintPage title="Bản vẽ" />,
        permissionTypes: [PermissionType.List],
        // icon: <TbPackages />,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.blueprintViewAll,
        name: PermissionNames.blueprintViewAll,
        aliasPath: `/doc-management/${PermissionNames.blueprintViewAll}`,
        hidden: true,
      },
      {
        title: "Tạo bản vẽ",
        breadcrumb: "Tạo bản vẽ",
        path: PermissionNames.blueprintAdd,
        name: PermissionNames.blueprintAdd,
        aliasPath: `/doc-management/${PermissionNames.blueprintAdd}`,
        element: (
          <CreateOrUpdateBluePrintPage title="Tạo bản vẽ" status="create" />
        ),
        permissionTypes: [PermissionType.Add],
        hidden: true,
        isPublic: true,
        // icon: <TbPackages />,
      },
      {
        title: "Xóa bản vẽ",
        breadcrumb: "Xóa bản vẽ",
        path: PermissionNames.blueprintDelete,
        name: PermissionNames.blueprintDelete,
        aliasPath: `/doc-management/${PermissionNames.blueprintDelete}`,
        hidden: true,
      },
      {
        title: "Chỉnh sửa bản vẽ",
        path: PermissionNames.blueprintEdit,
        name: PermissionNames.blueprintEdit,
        aliasPath: `/doc-management/${PermissionNames.blueprintEdit.replace(
          "/:id",
          ""
        )}`,
        element: (
          <CreateOrUpdateBluePrintPage
            title="Chỉnh sửa bản vẽ"
            status="update"
          />
        ),
        permissionTypes: [PermissionType.Edit],
        hidden: true,
      },
      {
        title: "Tài liệu dự án",
        breadcrumb: "Tài liệu dự án",
        path: PermissionNames.projectDocList,
        name: PermissionNames.projectDocList,
        aliasPath: `/doc-management/${PermissionNames.projectDocList}`,
        element: <ProjectDocumentPage title="Tài liệu dự án" />,
        permissionTypes: [PermissionType.List],
        // icon: <TbPackages />,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.projectDocViewAll,
        name: PermissionNames.projectDocViewAll,
        aliasPath: `/doc-management/${PermissionNames.projectDocViewAll}`,
        hidden: true,
      },
      {
        title: "Tạo tài liệu dự án",
        breadcrumb: "Tạo tài liệu dự án",
        path: PermissionNames.projectDocAdd,
        name: PermissionNames.projectDocAdd,
        aliasPath: `/doc-management/${PermissionNames.projectDocAdd}`,
        element: (
          <CreateOrUpdateProjectDocumentPage
            title="Tạo tài liệu dự án"
            status="create"
          />
        ),
        permissionTypes: [PermissionType.Add],
        hidden: true,
        isPublic: true,
        // icon: <TbPackages />,
      },
      {
        title: "Chỉnh sửa tài liệu dự án",
        breadcrumb: "Chỉnh sửa tài liệu dự án",
        path: PermissionNames.projectDocEdit,
        name: PermissionNames.projectDocEdit,
        aliasPath: `/doc-management/${PermissionNames.projectDocEdit.replace(
          "/:id",
          ""
        )}`,
        element: (
          <CreateOrUpdateProjectDocumentPage
            title="Chỉnh sửa tài liệu dự án"
            status="update"
          />
        ),
        permissionTypes: [PermissionType.Edit],
        hidden: true,
        isPublic: true,
      },
      {
        title: "Khóa tài liệu dự án",
        breadcrumb: "Khóa tài liệu dự án",
        path: PermissionNames.projectDocBlock,
        name: PermissionNames.projectDocBlock,
        aliasPath: `/report/${PermissionNames.projectDocBlock}`,
        hidden: true,
      },
      {
        title: "Hình ảnh",
        breadcrumb: "Hình ảnh",
        path: PermissionNames.imagesList,
        name: PermissionNames.imagesList,
        aliasPath: `/doc-management/${PermissionNames.imagesList}`,
        element: <ImagePage title="Hình ảnh" />,
        permissionTypes: [PermissionType.List],

        // icon: <TbPackages />,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.imagesViewAll,
        name: PermissionNames.imagesViewAll,
        aliasPath: `/doc-management/${PermissionNames.imagesViewAll}`,
        hidden: true,
      },
      {
        title: "Thêm hình ảnh",
        breadcrumb: "Thêm hình ảnh",
        path: PermissionNames.imagesAdd,
        name: PermissionNames.imagesAdd,
        aliasPath: `/doc-management/${PermissionNames.imagesAdd}`,
        element: (
          <CreateOrUpdateImagePage title="Thêm hình ảnh" status="create" />
        ),
        permissionTypes: [PermissionType.Add],
        isPublic: true,
        hidden: true,
      },
      {
        title: "Chỉnh sửa hình ảnh",
        breadcrumb: "Chỉnh sửa hình ảnh",
        path: PermissionNames.imagesEdit,
        name: PermissionNames.imagesEdit,
        aliasPath: `/doc-management/${PermissionNames.imagesEdit.replace(
          "/:id",
          ""
        )}`,
        element: (
          <CreateOrUpdateImagePage title="Chỉnh sửa hình ảnh" status="update" />
        ),
        permissionTypes: [PermissionType.Edit],
        isPublic: true,
        hidden: true,
      },

      // icon: <TbPackages />,
    ],
  },
];
