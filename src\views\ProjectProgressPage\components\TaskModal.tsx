import { Col, Form, Input, message, Modal, Row } from "antd";
import { Rule } from "antd/lib/form";
import { taskApi } from "api/task.api";
import CustomInput from "components/Input/CustomInput";
import { TextInput } from "components/Input/TextInput";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { ModalStatus } from "types/modal";
import { Task } from "types/task";
import dayjs from "dayjs";
import { formatDate, formatDateTime } from "utils/date";
import { BMDTextArea } from "components/TextArea/BMDTextArea";

const rules: Rule[] = [{ required: true }];

export interface TaskModalRef {
  handleCreate: (start: Date, end: Date) => void;
  handleUpdate: (task: Task) => void;
}

interface TaskModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
}

export const TaskModal = React.forwardRef(
  ({ onClose, onSubmitOk }: TaskModalProps, ref) => {
    const [form] = Form.useForm<any>();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");
    const [selectedTask, setSelectedTask] = useState<Task>();
    const [timeSlot, setTimeSlot] = useState<{ start: Date; end: Date }>();

    useImperativeHandle<any, TaskModalRef>(
      ref,
      () => ({
        handleCreate(start: Date, end: Date) {
          form.resetFields();
          setTimeSlot({ start, end });
          setVisible(true);
          setStatus("create");
        },
        handleUpdate(task: Task) {
          setSelectedTask(task);
          form.setFieldsValue({
            title: task.title,
            description: task.description,
          });
          setVisible(true);
          setStatus("update");
        },
      }),
      []
    );

    const createData = async () => {
      const valid = await form.validateFields();
      const formData = form.getFieldsValue();

      if (!timeSlot) return;

      const data = {
        task: {
          title: formData.title,
          description: formData.description,
          startDate: dayjs(timeSlot.start).format("YYYY/MM/DD"),
          endDate: dayjs(timeSlot.end).format("YYYY/MM/DD"),
          type: "SCHEDULE",
          percentComplete: 0,
          weight: 1,
          cost: 0,
          workVolume: 1,
          requireApproval: false,
        },
      };

      setLoading(true);
      try {
        const res = await taskApi.create(data);
        message.success("Tạo task thành công!");
        onClose();
        onSubmitOk();
        setVisible(false);
      } catch (error) {
        console.error("Error creating task:", error);
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const valid = await form.validateFields();
      const formData = form.getFieldsValue();

      const data = {
        task: {
          title: formData.title,
          description: formData.description,
        },
      };

      setLoading(true);
      try {
        const res = await taskApi.update(selectedTask?.id || 0, data);
        message.success("Cập nhật task thành công!");
        onClose();
        onSubmitOk();
        setVisible(false);
      } catch (error) {
        console.error("Error updating task:", error);
      } finally {
        setLoading(false);
      }
    };

    return (
      <Modal
        className="footer-full"
        onCancel={() => {
          onClose?.();
          setVisible(false);
        }}
        centered
        open={visible}
        title={status == "create" ? "Tạo task mới" : "Chỉnh sửa task"}
        style={{ top: 20 }}
        width={550}
        confirmLoading={loading}
        cancelButtonProps={{ style: { display: "none" } }}
        okText={status == "create" ? "Tạo" : "Lưu"}
        onOk={() => {
          status == "create" ? createData() : updateData();
        }}
        getContainer={() => {
          return document.getElementById("App") as HTMLElement;
        }}
      >
        <Form layout="vertical" form={form}>
          <Row gutter={16}>
            {status === "create" && timeSlot && (
              <Col span={24}>
                <div className="mb-4 p-3 bg-gray-50 rounded">
                  <p>
                    <strong>Thông tin hiện tại:</strong>
                  </p>
                  <p>
                    <strong>Thời gian bắt đầu:</strong>{" "}
                    {dayjs(timeSlot.start).format("YYYY/MM/DD")}
                  </p>
                  <p>
                    <strong>Thời gian kết thúc:</strong>{" "}
                    {dayjs(timeSlot.end).format("YYYY/MM/DD")}
                  </p>
                </div>
              </Col>
            )}

            {status === "update" && selectedTask && (
              <Col span={24}>
                <div className="mb-4 p-3 bg-gray-50 rounded">
                  <p>
                    <strong>Thông tin hiện tại:</strong>
                  </p>
                  <p>
                    <strong>Thời gian bắt đầu:</strong> {selectedTask.startDate}
                  </p>
                  <p>
                    <strong>Thời gian kết thúc:</strong> {selectedTask.endDate}
                  </p>
                </div>
              </Col>
            )}

            <Col span={24}>
              <Form.Item name="title" label="Tên task" rules={rules}>
                <CustomInput placeholder="Nhập tên task" />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item name="description" label="Mô tả">
                <BMDTextArea placeholder="Nhập mô tả task" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
);
