import { DownloadOutlined, InboxOutlined } from "@ant-design/icons";
import { Alert, Modal, Space, Spin, Table, Upload, message } from "antd";
import { Rule } from "antd/es/form";
import { projectGroupApi } from "api/projectGroup.api";
import CustomButton from "components/Button/CustomButton";
import { forwardRef, useEffect, useImperativeHandle, useState } from "react";
import { Link } from "react-router-dom";
import { ProjectGroup } from "types/projectGroup";
import { readerData } from "utils/excel2";
import ImportPreviewModule from "./ImportPreviewModule";
import { handleConfirmImportExcel } from "utils/function";

const rules: Rule[] = [{ required: true }];
const { Dragger } = Upload;

export interface ImportProjectGroupModal {
  open: () => void;
  close: () => void;
}

export interface IValidate {
  index: number;
  message: string;
}

interface DataImportReturn {
  msg: string;
  rowNum: number;
  status: string;
}

export interface ProjectGroupImport extends ProjectGroup {
  rowNum: number;
}

interface IProps {
  onSuccess?: () => void;
  createApi?: (data: any) => any;
  onUploaded?: (excelData: any, setData: (data: any) => any) => void;
  demoExcel?: string;
  guide?: React.ReactNode[];
  uploadText?: string;
  okText?: string;
  onClose?: () => void;
  titleText?: string;
  validateMessage?: IValidate[];
  onDownloadDemoExcel?: () => void;
  loadingDownloadDemo?: boolean;
}

const ImportProjectGroup = forwardRef(
  (
    {
      onSuccess,
      createApi,
      onUploaded,
      onClose,
      validateMessage,
      guide,
      demoExcel,
      uploadText = "Kéo thả hoặc click vào đây để upload file",
      okText = "Nhập dữ liệu ngay",
      titleText = "Nhập excel dữ liệu",
      onDownloadDemoExcel,
      loadingDownloadDemo,
    }: IProps,
    ref
  ) => {
    const [errorsLog, setErrorsLog] = useState<any[]>([]);
    const [dataPosts, setDataPosts] = useState<ProjectGroupImport[]>([]);
    const [visible, setVisible] = useState<boolean>(false);
    const [loading, setLoading] = useState(false);
    const [dataReturn, setDataReturn] = useState<{
      data: DataImportReturn[];
      successCount: number;
      errorCount: number;
    }>();

    useEffect(() => {
      if (validateMessage?.length) {
        setErrorsLog([]);
      }
    }, [validateMessage]);

    // Define required fields for validation
    const requiredFields: (keyof ProjectGroupImport)[] = [
      "name", // Tên nhóm dự án - bắt buộc
    ];

    // Define preview columns for the table
    const previewColumns = [
      {
        key: "rowNum",
        title: "Dòng excel",
        dataIndex: "rowNum",
        width: 100,
        render: (text: number) => <span>{text}</span>,
      },
      {
        key: "code",
        title: "Mã nhóm dự án",
        dataIndex: "code",
        render: (text: string) => (
          <span className={!text ? "text-gray-400" : ""}>
            {text || "Tự sinh"}
          </span>
        ),
      },
      {
        key: "name",
        title: "Tên nhóm dự án",
        dataIndex: "name",
        render: (text: string, record: any) => (
          <span
            className={record.errors?.includes("name") ? "text-red-500" : ""}
          >
            {text}
          </span>
        ),
      },
      {
        key: "isActive",
        title: "Trạng thái",
        dataIndex: "isActive",
        render: (text: boolean, record: any) => {
          const displayText =
            text === true ? "Hoạt động" : text === false ? "Bị khóa" : "";
          return (
            <span
              className={
                record.errors?.includes("isActive") ? "text-red-500" : ""
              }
            >
              {displayText}
            </span>
          );
        },
      },
      {
        key: "errorMessage",
        title: "Lỗi",
        dataIndex: "errorMessage",
        width: 300,
        render: (text: string) => (
          <span className="text-red-500 whitespace-pre-line text-xs">
            {text}
          </span>
        ),
      },
    ];

    const handleValidateData = (
      data: ProjectGroupImport[]
    ): ProjectGroupImport[] => {
      console.log("🔍 handleValidateData called with:", data);
      return data.map((item) => {
        const additionalErrors: string[] = [];

        // Check required fields
        requiredFields.forEach((field) => {
          const value = item[field];
          if (!value || (typeof value === "string" && value.trim() === "")) {
            additionalErrors.push(field);
          }
        });

        // Add specific validation messages
        if (additionalErrors.includes("name")) {
          additionalErrors.push("Tên nhóm dự án không được để trống");
        }

        return {
          ...item,
          errors: additionalErrors,
        };
      });
    };

    const handleOnImport = async () => {
      if (!dataPosts.length) return;
      let errors: any = [];

      try {
        setLoading(true);
        await handleConfirmImportExcel();
        const { data } = await projectGroupApi.import({
          projectGroups: dataPosts.map((dataPost) => ({
            ...dataPost,
          })),
        });
        if (data.length) {
          const successCount = data.reduce(
            (acc: number, item: DataImportReturn) => {
              if (item.status == "ok") return acc + 1;
              return acc;
            },
            0
          );
          const errorCount = data.reduce(
            (acc: number, item: DataImportReturn) => {
              if (item.status == "error") return acc + 1;
              return acc;
            },
            0
          );
          if (errorCount == 0) {
            handleOnCancel();
          }
          setDataReturn({ data, successCount, errorCount });
          onSuccess?.();
          setDataPosts([]);
        }
      } catch (err) {
        console.log({ err });
      } finally {
        setLoading(false);
      }
    };

    const handleOnCancel = () => {
      setVisible(false);
      onClose?.();
    };

    useImperativeHandle(
      ref,
      () => ({
        open: () => setVisible(true),
        close: () => setVisible(false),
      }),
      []
    );

    return (
      <Modal
        maskClosable={false}
        width={1000}
        style={{ top: 50 }}
        visible={visible}
        onCancel={handleOnCancel}
        destroyOnClose={true}
        afterClose={() => {
          setDataPosts([]);
          setErrorsLog([]);
          setDataReturn(undefined);
        }}
        title={titleText}
        footer={[
          <CustomButton
            loading={loading}
            variant="primary"
            disabled={!dataPosts.length}
            onClick={() => {
              handleOnImport();
            }}
          >
            {okText}
          </CustomButton>,
          <CustomButton
            variant="outline"
            className="cta-button"
            onClick={() => {
              handleOnCancel();
            }}
          >
            Đóng
          </CustomButton>,
        ]}
      >
        <Spin spinning={false}>
          {guide && (
            <Alert
              style={{ padding: "10px", marginBottom: "10px" }}
              message={<b>Lưu ý</b>}
              type="warning"
              description={
                <ul>
                  {guide.map((text, index) => (
                    <li key={index}>
                      <p>{text}</p>
                    </li>
                  ))}
                </ul>
              }
            />
          )}
          {demoExcel && (
            <Link to={demoExcel} target="_blank" download>
              <Space className={`flex gap-2 cursor-pointer`}>
                <DownloadOutlined />
                Tải file import mẫu{" "}
              </Space>
            </Link>
          )}
          {onDownloadDemoExcel && (
            <a>
              <Space
                className={`flex gap-2 cursor-pointer`}
                onClick={() => {
                  onDownloadDemoExcel();
                }}
                style={{ pointerEvents: loadingDownloadDemo ? "none" : "auto" }}
              >
                {loadingDownloadDemo ? (
                  <Spin spinning={loadingDownloadDemo} />
                ) : (
                  <DownloadOutlined />
                )}
                Tải file import mẫu
              </Space>
            </a>
          )}

          <Dragger
            style={{ marginTop: "0.5em" }}
            maxCount={1}
            multiple={false}
            beforeUpload={async (file) => {
              //Check file type
              const isCSVFile = file.name.includes("xlsx");
              if (isCSVFile === false) {
                message.error("Bạn chỉ có thể upload file excel!");
                return Upload.LIST_IGNORE;
              }
              const excelData = await readerData(file, 0);
              setDataReturn(undefined);
              console.log("Data khi import vào là", excelData);
              onUploaded?.(excelData, setDataPosts);
              return false;
            }}
            onChange={(info) => {
              //reset data
              if (info.fileList.length == 0) {
                setErrorsLog([]);
                setDataPosts([]);
              }
            }}
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">{uploadText}</p>
          </Dragger>

          {/* Import Preview Module */}
          <ImportPreviewModule
            data={dataPosts}
            dataReturn={dataReturn}
            onValidateData={handleValidateData}
            duplicateCheckFields={["code", "name"]} // Kiểm tra trùng lập mã nhóm dự án và tên nhóm dự án
            columns={previewColumns}
            requiredFields={requiredFields}
            title="Xem danh sách nhóm dự án"
            previewButtonText="Xem danh sách nhóm dự án"
          />
        </Spin>
        <Space
          style={{ width: "100%", justifyContent: "end", marginTop: "1em" }}
        ></Space>
      </Modal>
    );
  }
);

export default ImportProjectGroup;
