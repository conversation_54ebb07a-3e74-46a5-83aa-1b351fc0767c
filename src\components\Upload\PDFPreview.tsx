import React, { useState } from "react";
import { Document, Page, pdfjs } from "react-pdf";
import "./PDFPreview.scss";

// <PERSON><PERSON><PERSON> hình worker cho react-pdf
pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  "pdfjs-dist/build/pdf.worker.min.mjs",
  import.meta.url
).toString();

interface PDFPreviewProps {
  url: string;
  width?: number;
  height?: number;
}

export const PDFPreview: React.FC<PDFPreviewProps> = ({
  url,
  width = "100%",
  height = "100%",
}) => {
  const [loadError, setLoadError] = useState(false);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [numPages, setNumPages] = useState<number>(0);
  const [pageNumber, setPageNumber] = useState<number>(1);

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    console.log("✅ PDF loaded successfully with", numPages, "pages");
    setNumPages(numPages);
    setLoading(false);
  };

  const onDocumentLoadError = (error: Error) => {
    console.error("❌ PDF load error:", error);
    console.log("📄 PDF URL:", url);
    setLoadError(true);
    setLoading(false);
  };

  // Debug: Log URL khi component mount
  console.log("🔍 PDFPreview rendering with URL:", url);

  // Fallback placeholder khi PDF lỗi
  if (loadError) {
    return (
      <div
        className="pdf-preview error"
        onClick={() => setShowModal(true)}
        style={{ width, height }}
      >
        <div className="icon">📄</div>
        <div className="title">PDF Document (Error)</div>
        <div className="subtitle">Click to view</div>
      </div>
    );
  }

  return (
    <>
      <div
        className="pdf-preview"
        onClick={() => setShowModal(true)}
        style={{ width, height }}
      >
        {loading && <div className="loading">⏳ Đang tải PDF...</div>}

        <div className="document">
          <Document
            file={url}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={onDocumentLoadError}
            loading=""
          >
            <Page
              pageNumber={1}
              renderTextLayer={false}
              renderAnnotationLayer={false}
              width={typeof width === "number" ? width - 20 : 1200}
              height={undefined}
              scale={1}
            />
          </Document>
        </div>

        {/* <button
          className="external-link"
          onClick={(e) => {
            e.stopPropagation();
            window.open(url, "_blank");
          }}
        >
          🔗
        </button> */}
      </div>

      {/* Modal */}
      {showModal && (
        <div className="pdf-modal" onClick={() => setShowModal(false)}>
          <div className="content" onClick={(e) => e.stopPropagation()}>
            <div className="header">
              <h3>PDF Document {numPages > 0 && `(${numPages} pages)`}</h3>
              <div className="controls">
                <button
                  className="button primary"
                  onClick={() => window.open(url, "_blank")}
                >
                  Mở trong tab mới
                </button>
                <button
                  className="button secondary"
                  onClick={() => setShowModal(false)}
                >
                  Đóng
                </button>
              </div>
            </div>

            <div className="body">
              <div className="pdf-container">
                <Document
                  file={url}
                  onLoadSuccess={onDocumentLoadSuccess}
                  onLoadError={onDocumentLoadError}
                  loading={<div>⏳ Đang tải PDF...</div>}
                >
                  <Page
                    pageNumber={pageNumber}
                    renderTextLayer={false}
                    renderAnnotationLayer={false}
                    width={800}
                  />
                </Document>
              </div>

              {numPages > 1 && (
                <div className="navigation">
                  <button
                    onClick={() => setPageNumber(pageNumber - 1)}
                    disabled={pageNumber <= 1}
                  >
                    ← Trước
                  </button>
                  <span>
                    {pageNumber} / {numPages}
                  </span>
                  <button
                    onClick={() => setPageNumber(pageNumber + 1)}
                    disabled={pageNumber >= numPages}
                  >
                    Tiếp →
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
};
