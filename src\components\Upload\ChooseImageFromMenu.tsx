import { FileImageOutlined } from "@ant-design/icons";
import { Button, Image, Modal } from "antd";
import ChooseImageModal, {
  ChooseImageModalRef,
  ChooseImageMode,
} from "components/Modal/ChooseImageModal";
import { divide } from "lodash";
import React, { useEffect, useRef, useState } from "react";
import { FileAttach } from "types/fileAttach";
import { $url } from "utils/url";

interface Props {
  fileUrl: string;
  onSelectOk: (url: string, file: FileAttach) => void;
  ratioText?: string | boolean;
  imageWidth?: number;
  imageHeight?: number;
  buttonText?: string;
  chooseMode?: ChooseImageMode;
  fileName?: string;
}

const ChooseFileFromMenu = ({
  fileUrl,
  onSelectOk,
  ratioText = "Tỉ lệ 1x1",
  imageHeight = 150,
  imageWidth = 150,
  buttonText = "Chọn ảnh",
  chooseMode = "image",
  fileName,
}: Props) => {
  const chooseThumbnailRef = useRef<ChooseImageModalRef>();
  const [previewVideo, setPreviewVideo] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  const [imageUrl, setImageUrl] = useState<string>("");

  // Handle khi change hình
  useEffect(() => {
    if (fileUrl) setImageUrl(fileUrl);
  }, [fileUrl]);

  return (
    <>
      {chooseMode == "image" && imageUrl && (
        <div className="flex flex-col justify-center items-center mb-2">
          <Image
            width={150}
            height={150}
            src={$url(imageUrl)}
            className="w-[150px] h-[150px] object-contain"
          ></Image>
          {fileName && <div className="mt-2 font-medium">{fileName}</div>}
        </div>
      )}
      {chooseMode == "video" && fileUrl && (
        <div className="flex flex-col justify-center items-center mb-2">
          <video
            onClick={() => {
              setPreviewVideo(true);
            }}
            src={$url(fileUrl)}
            className="w-[150px] h-[150px] object-contain"
          ></video>
          {fileName && <div className="mt-2 font-medium">{fileName}</div>}

          <Modal
            open={previewVideo}
            width={1000}
            destroyOnClose
            cancelText="Đóng"
            closeIcon={false}
            okButtonProps={{ className: "hidden" }}
            onCancel={() => {
              setPreviewVideo(false);
              videoRef.current?.pause();
            }}
          >
            <video
              autoPlay
              controls
              ref={videoRef}
              src={$url(fileUrl)}
              className="h-full w-full object-contain"
            ></video>
          </Modal>
        </div>
      )}
      <div className="flex flex-col items-center gap-2 w-full">
        <Button
          type="primary"
          icon={<FileImageOutlined />}
          onClick={() => {
            chooseThumbnailRef.current?.onOpen();
          }}
        >
          {buttonText}
        </Button>
        {ratioText && <b>{ratioText}</b>}
      </div>
      <ChooseImageModal
        ref={chooseThumbnailRef}
        onChoose={(file) => {
          console.log(file);
          onSelectOk(file.url, file);
        }}
        chooseMode={chooseMode}
      ></ChooseImageModal>
    </>
  );
};

export default ChooseFileFromMenu;
