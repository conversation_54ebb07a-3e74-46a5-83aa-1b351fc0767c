import { Dictionary } from "./dictionary";
import { FileAttach } from "./fileAttach";
import { Provider } from "./provider";
import { Unit } from "./unit";

export enum ServiceType {
  Transport = "TRANSPORT",
  EquipmentRental = "EQUIPMENT_RENTAL",
  Maintenance = "MAINTENANCE",
  ConsultingService = "CONSULTING_SERVICE",
  all = "",
}

export const ServiceTypeTrans = {
  [ServiceType.all]: {
    label: "Tất cả các loại",
    value: ServiceType.all,
    color: "",
  },
  [ServiceType.Transport]: {
    label: "Vận chuyển",
    value: ServiceType.Transport,
    color: "#303F9F",
  },
  [ServiceType.EquipmentRental]: {
    label: "Thuê thiết bị",
    value: ServiceType.EquipmentRental,
    color: "#0288D1",
  },
  [ServiceType.Maintenance]: {
    label: "Bảo trì",
    value: ServiceType.Maintenance,
    color: "#FFA000",
  },
  [ServiceType.ConsultingService]: {
    label: "Dịch vụ tư vấn",
    value: ServiceType.ConsultingService,
    color: "#388E3C",
  },
};

export enum ServiceStatus {
  Pending = "PENDING",
  InProgress = "IN_PROGRESS",
  Completed = "COMPLETED",
}

export const ServiceStatusTran = {
  [ServiceStatus.Completed]: {
    color: "green",
    label: "Hoàn tất",
    textColor: "#43A047",
    bgColor: "#E6F1E6",
  },
  [ServiceStatus.InProgress]: {
    color: "#FFAE0D",
    label: "Đang thực hiện",
    textColor: "#FFAE0D",
    bgColor: "#FDF0DA",
  },
  [ServiceStatus.Pending]: {
    color: "gray",
    label: "Tạm hoãn",
    textColor: "#636F73",
    bgColor: "#ECEEEF",
  },
};

export interface Service {
  id: number;
  code: string;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  avatar: string;
  name: string;
  description: string;
  type: ServiceType;
  status: ServiceStatus;
  estPrice: number; // chi phí ước tính
  startAt: number; // thời gian bắt đầu dịch vụ
  endAt: number; // thời gian kết thúc dịch vụ
  isActive: boolean;
  provider: Provider;
  fileAttaches: FileAttach[];
  files: string;
  workingDays: string;
  unit: Unit;
  dateType: DateType;
  serviceType: Dictionary;
}

export enum DateType {
  Day = "DAY",
  Week = "WEEK",
  Month = "MONTH",
  Year = "YEAR",
}

export const DateTypeTrans = {
  [DateType.Day]: {
    value: DateType.Day,
    label: "Ngày",
  },
  [DateType.Week]: {
    value: DateType.Week,
    label: "Tuần",
  },
  [DateType.Month]: {
    value: DateType.Month,
    label: "Tháng",
  },
  [DateType.Year]: {
    value: DateType.Year,
    label: "Năm",
  },
};
