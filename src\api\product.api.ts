import { AxiosPromise } from "axios";
import { request } from "utils/request";

export const productApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/product",
      params,
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/product/${id}`,
      method: "get",
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/product",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/product/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/product/${id}`,
      method: "delete",
    }),
  active: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/product/${id}/active`,
      method: "patch",
    }),
  inactive: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/product/${id}/inactive`,
      method: "patch",
    }),
};
