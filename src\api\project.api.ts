import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const projectApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/project",
      params,
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/project/${id}`,
      method: "get",
    }),
  findSummary: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/project/summary",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/project",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/project/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/project/${id}`,
      method: "delete",
    }),
};
