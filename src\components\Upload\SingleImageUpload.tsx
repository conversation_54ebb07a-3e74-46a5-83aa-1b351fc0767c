import { LoadingOutlined, PlusOutlined } from "@ant-design/icons";
import { message, Upload, Modal } from "antd";
import { UploadChangeParam } from "antd/lib/upload";
import dayjs from "dayjs";
import React, { useEffect, useRef, useState } from "react";
import { getImageSize } from "utils";
import { getToken } from "utils/auth";
import { $url } from "utils/url";
import { FileAttach } from "types/fileAttach";
import { LuImagePlus } from "react-icons/lu";
import clsx from "clsx";
import UploadImageIcon from "assets/svgs/UploadImageIcon";
import CustomButton from "components/Button/CustomButton";
import { PDFPreview } from "./PDFPreview";

interface SingleImageUploadProps {
  uploadUrl?: string;
  imageUrl: string;
  onUploadOk: (fileAttach: FileAttach) => void;
  width?: number | string;
  height?: number | string;
  recommendSize?: { width: number; height: number };
  recommendRatio?: string;
  recommendFileSize?: number; //In Byte
  disabled?: boolean;
  className?: string;
  hideUploadButton?: boolean;
  fileType?: string;
}

export const SingleImageUpload = React.memo(
  ({
    uploadUrl = import.meta.env.VITE_API_URL + "/v1/admin/fileAttach/upload",
    imageUrl,
    onUploadOk,
    height = 103,
    width = 103,
    disabled,
    recommendSize = { width: 400, height: 400 },
    recommendRatio,
    recommendFileSize,
    className,
    hideUploadButton = false,
    fileType = "default",
  }: SingleImageUploadProps) => {
    const [loading, setLoading] = useState(false);
    const uploadRef = useRef(null);
    const [imageError, setImageError] = useState(false);
    const [singleUploadId, setsingleUploadId] = useState(
      `single-upload-${dayjs().valueOf()}`
    );
    const [imageSize, setImageSize] = useState<{
      width: number;
      height: number;
    }>({ width: 0, height: 0 });
    const [previewVisible, setPreviewVisible] = useState(false);

    // dùng cho BlueprintPage
    let actualWidth = width;
    let actualHeight = height;
    if (fileType === "PDF") {
      actualWidth = "100%";
      actualHeight = 172;
    }

    const checkSize = async (file: File): Promise<boolean> => {
      return new Promise((resolve, reject) => {
        if (recommendSize) {
          const reader = new FileReader();
          reader.readAsDataURL(file);
          reader.addEventListener("load", (event) => {
            //@ts-ignore
            const _loadedImageUrl = event.target.result;
            const image = document.createElement("img");

            //@ts-ignore
            image.src = _loadedImageUrl;
            // image.addEventListener("load", () => {
            //   const { width, height } = image;
            //   if (
            //     width > recommendSize.width ||
            //     height > recommendSize.height
            //   ) {
            //     message.error(
            //       `Kích thước quá lớn (Đề xuất: ${recommendSize.width}x${recommendSize.height})`
            //     );

            //     return resolve(false);
            //   } else {
            //     resolve(true);
            //   }
            // });
            resolve(true);
          });
        } else {
          return resolve(true);
        }
      });
    };

    const checkFileSize = (size: number) => {
      if (recommendFileSize) {
        if (recommendFileSize < size) {
          message.error(
            `Dung lượng ảnh quá lớn (Tối đa: ${
              recommendFileSize / 1024 / 1024
            }MB)`,
            5
          );
          return false;
        }
        return true;
      }
      return true;
    };

    const beforeUpload = async (file: File) => {
      const notLargeDimension = await checkSize(file);
      const notLargeSize = checkFileSize(file.size);

      let isValidFile = false;
      if (fileType === "PDF") {
        isValidFile = file.type === "application/pdf";
        if (!isValidFile) {
          message.error("Chỉ chấp nhận định dạng PDF");
        }
      } else {
        isValidFile = file.type.includes("image");
        if (!isValidFile) {
          message.error("Chỉ chấp nhận định dạng ảnh");
        }
      }

      return notLargeDimension && notLargeSize && isValidFile;
    };

    const handleChange = (info: UploadChangeParam<any>) => {
      if (info.file.status === "uploading") {
        setLoading(true);
        return;
      }
      if (info.file.status === "done") {
        if (recommendSize) {
          getImageSize(
            setImageSize,
            import.meta.env.VITE_IMG_URL + info.file.response.data.path
          );
        }
        console.log(info.file.response.data);
        onUploadOk(info.file.response.data);
        setLoading(false);
      }
      if (info.file.status === "error") {
        message.error(info.file.response?.message);
        setLoading(false);
        return;
      }
    };

    const handleImageClick = (e: React.MouseEvent) => {
      e.stopPropagation();
      if (imageUrl) {
        setPreviewVisible(true);
      }
    };

    const handleUploadIconClick = (e: React.MouseEvent) => {
      if (hideUploadButton || disabled) {
        e.stopPropagation();
        e.preventDefault();
        return;
      }

      e.stopPropagation();
      e.preventDefault();

      // Create a temporary input element to trigger file selection
      const input = document.createElement("input");
      input.type = "file";
      input.accept = fileType === "PDF" ? ".pdf,application/pdf" : "image/*";
      input.style.display = "none";

      input.onchange = async (event: any) => {
        const file = event.target.files?.[0];
        if (file) {
          // Validate file before upload
          const isValid = await beforeUpload(file);
          if (isValid) {
            // Create FormData and upload manually
            const formData = new FormData();
            formData.append("file", file);

            try {
              setLoading(true);
              const response = await fetch(uploadUrl, {
                method: "POST",
                headers: {
                  token: getToken() || "",
                },
                body: formData,
              });

              const result = await response.json();

              if (response.ok && result.data) {
                if (recommendSize) {
                  getImageSize(
                    setImageSize,
                    import.meta.env.VITE_IMG_URL + result.data.path
                  );
                }
                onUploadOk(result.data);
                message.success("Upload ảnh thành công!");
              } else {
                message.error(result.message || "Upload ảnh thất bại!");
              }
            } catch (error) {
              message.error("Upload ảnh thất bại!");
              console.error("Upload error:", error);
            } finally {
              setLoading(false);
            }
          }
        }
        // Remove the temporary input
        document.body.removeChild(input);
      };

      // Add to DOM and trigger click
      document.body.appendChild(input);
      input.click();
    };

    const uploadButton = (
      <div
        style={{
          display: "flex",

          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          gap: 10,
          height: "100%",
          borderRadius: "0px",
        }}
      >
        {loading ? (
          <LoadingOutlined />
        ) : (
          <div style={{ width: 24, height: 24 }}>
            <UploadImageIcon />
          </div>
        )}
        <div className="flex flex-col gap-2 items-center">
          <div
            style={{
              fontWeight: 700,
              fontSize: 13,
              lineHeight: "140%",
            }}
          >
            {fileType === "PDF" ? "Tải bản vẽ" : "Tải ảnh minh họa"}
          </div>
          <div
            style={{
              fontWeight: 300,
              fontSize: 13,
              lineHeight: "140%",
              textAlign: "center",
            }}
          >
            {fileType === "PDF"
              ? "Hỗ trợ định dạng .PDF"
              : "Hỗ trợ định dạng .jpg, jpeg, webp."}
          </div>
          <div
            style={{
              fontWeight: 300,
              fontSize: 13,
              lineHeight: "140%",
            }}
          >
            {fileType === "PDF" ? "" : "Tỉ lệ 1:1"}
          </div>
          {!hideUploadButton && (
            <CustomButton variant="outline" className="max-w-[120px]">
              Tải ảnh
            </CustomButton>
          )}
        </div>
        {imageUrl && (
          <></>
          // <p className="text-center !my-2 font-semibold">
          //   Kích thước hiện tại: {imageSize.width} x {imageSize.height}
          // </p>
        )}
        {/* <p className="text-center !my-2 font-semibold">
          Kích thước đề xuất: {recommendSize.width} x {recommendSize.height}{" "}
          {recommendRatio && `(${recommendRatio})`}
        </p> */}
      </div>
    );

    useEffect(() => {
      if (recommendSize) {
        if (imageUrl) {
          getImageSize(setImageSize, $url(imageUrl));
        } else {
          setImageSize({ height: 0, width: 0 });
        }
      }
    }, [imageUrl]);

    return (
      <>
        {/* {recommendSize &&
          (imageSize.width > recommendSize?.width ||
            imageSize.height > recommendSize.height) && (
            <p style={{ color: "red" }}>
              Kích thước hình ảnh quá lớn cần chỉnh sửa lại
            </p>
          )} */}
        {recommendFileSize && (
          <p className="text-center">
            Dung lượng ảnh tối đa {recommendFileSize / 1024 / 1024}MB
          </p>
        )}
        <div
          className={clsx(singleUploadId, className)}
          style={{ position: "relative" }}
        >
          <Upload
            accept={fileType === "PDF" ? ".pdf,application/pdf" : "image/*"} // Thay đổi dòng này
            name="file"
            listType="picture-card"
            showUploadList={false}
            action={uploadUrl}
            headers={{
              token: getToken() || "",
            }}
            beforeUpload={beforeUpload}
            onChange={handleChange}
            ref={uploadRef}
            disabled={disabled}
            style={{ display: "none" }}
          ></Upload>

          {imageUrl ? (
            <div
              className="ant-upload ant-upload-select ant-upload-select-picture-card"
              style={{
                // padding: 16,
                width:
                  typeof actualWidth == "number"
                    ? `${actualWidth}px`
                    : actualWidth, // Sử dụng actualWidth
                height:
                  typeof actualHeight == "number"
                    ? `${actualHeight}px`
                    : actualHeight, // Sử dụng actualHeight
                position: "relative",
                cursor: hideUploadButton || disabled ? "default" : "pointer",
                border: "1px dashed #d9d9d9",
                borderRadius: "6px",
                backgroundColor: "#fafafa",
                transition: "border-color 0.3s",
              }}
            >
              {fileType === "PDF" ? (
                <PDFPreview
                  url={$url(imageUrl)}
                  //@ts-ignore
                  width={
                    actualWidth === "100%"
                      ? "100%"
                      : typeof actualWidth == "number"
                      ? actualWidth - 32
                      : 400
                  }
                  // height={
                  //   typeof actualHeight == "number" ? actualHeight - 32 : 140
                  // }
                />
              ) : (
                <img
                  src={$url(imageUrl)}
                  alt="avatar"
                  onError={(e) => {
                    //@ts-ignore
                    e.target.src = "/default-thumbnail.jpg";
                  }}
                  style={{
                    width: "100%",
                    height: "100%",
                    objectFit: "cover",
                    cursor: "pointer",
                  }}
                  onClick={handleImageClick}
                />
              )}
              {!hideUploadButton && !disabled && (
                <div
                  className="flex items-center justify-center absolute top-[10px] right-[10px] h-[34px] w-[34px] bg-[#050505] opacity-30 rounded-[100px] hover:opacity-50 transition-opacity"
                  onClick={handleUploadIconClick}
                  style={{ cursor: "pointer" }}
                >
                  <UploadImageIcon fill="#FFFFFF" />
                </div>
              )}
            </div>
          ) : (
            <div
              className="ant-upload ant-upload-select ant-upload-select-picture-card"
              style={{
                padding: 16,
                width:
                  typeof actualWidth == "number"
                    ? `${actualWidth}px`
                    : actualWidth,
                height:
                  typeof actualHeight == "number"
                    ? `${actualHeight}px`
                    : actualHeight,
                cursor: hideUploadButton || disabled ? "default" : "pointer",
                border: "1px dashed #d9d9d9",
                backgroundColor: "#fafafa",
                transition: "border-color 0.3s",
                opacity: hideUploadButton || disabled ? 0.5 : 1,
              }}
              onClick={
                hideUploadButton || disabled ? undefined : handleUploadIconClick
              }
              onMouseEnter={
                hideUploadButton || disabled
                  ? undefined
                  : (e) => {
                      e.currentTarget.style.borderColor = "#1890ff";
                    }
              }
              onMouseLeave={
                hideUploadButton || disabled
                  ? undefined
                  : (e) => {
                      e.currentTarget.style.borderColor = "#d9d9d9";
                    }
              }
            >
              {uploadButton}
            </div>
          )}
        </div>

        <Modal
          open={previewVisible}
          footer={null}
          onCancel={() => setPreviewVisible(false)}
          width="80%"
          style={{ maxWidth: "800px" }}
        >
          <img
            src={$url(imageUrl)}
            alt="Preview"
            style={{
              width: "100%",
              height: "auto",
              maxHeight: "70vh",
              objectFit: "contain",
            }}
          />
        </Modal>
        {/* {imageUrl && (
          <p className="text-center !my-2 font-semibold">
            Kích thước hiện tại: {imageSize.width} x {imageSize.height}
          </p>
        )}
        <p className="text-center !my-2 font-semibold">
          Kích thước đề xuất: {recommendSize.width} x {recommendSize.height}{" "}
          {recommendRatio && `(${recommendRatio})`}
        </p> */}
      </>
    );
  }
);
