import { useMemo, useState } from "react";
import { changeEventApi } from "api/changeEvent.api";
import { QueryParam } from "types/query";
import { ChangeEvent } from "types/changeEvent";

export interface ChangeEventQuery extends QueryParam { }
interface UseChangeEventProps {
  initQuery: ChangeEventQuery;
}

export const useChangeEvent = ({ initQuery }: UseChangeEventProps) => {
  const [data, setData] = useState<ChangeEvent[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<ChangeEventQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const isEmptyQuery = useMemo(
    () =>
      Object.keys(query).filter(
        (k) => query[k] && !["limit", "page", "queryObject"].includes(k)
      ).length === 0,
    [query]
  );

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await changeEventApi.findAll(query);
      setData(data.changeEvents ?? []);
      setTotal(data.total ?? 0);
    } finally {
      setLoading(false);
    }
  };

  return {
    changeEvents: data,
    total,
    fetchData,
    loading,
    setQuery,
    query,
    isEmptyQuery,
  };
};
