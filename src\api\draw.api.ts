import { request } from "utils/request";
import { AxiosPromise } from "axios";
import { find } from "lodash";

export const drawApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/draw",
      params,
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/draw/${id}`,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/draw",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/draw/${id}`,
      method: "patch",
      data,
    }),
  approve: (id: number, data?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/draw/${id}/approve`,
      method: "patch",
      data,
    }),
  reject: (id: number, data?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/draw/${id}/reject`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/draw/${id}`,
      method: "delete",
    }),
};
