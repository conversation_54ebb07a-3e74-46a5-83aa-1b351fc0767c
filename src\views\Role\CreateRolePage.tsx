import {
  But<PERSON>,
  Card,
  Checkbox,
  Col,
  Dropdown,
  Form,
  Input,
  Menu,
  message,
  Modal,
  Row,
  Space,
  Switch,
  Tree,
} from "antd";
import { Rule } from "antd/lib/form";
import { roleApi } from "api/role.api";
import React, {
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from "react";
import { settings } from "settings";
import { userStore } from "store/userStore";
import { ModalStatus } from "types/modal";

import { Role } from "types/role";
import { FaChevronDown } from "react-icons/fa6";
import { adminRoutes } from "router";
import { MinusOutlined, PlusOutlined } from "@ant-design/icons";
import {
  Permission,
  PermissionType,
  PermissionTypeTrans,
} from "types/permission";
import { usePermission } from "hooks/usePermission";
import { useNavigate } from "react-router-dom";
import TextArea from "antd/es/input/TextArea";
import CustomInput from "components/Input/CustomInput";
import CustomButton from "components/Button/CustomButton";

const rules: Rule[] = [{ required: true }];

interface ITreeData {
  title: string;
  key: string;
  children?: ITreeData[];
}

interface IPermission {
  key: string;
  title: string;
  children?: IPermission[];
  permissionTypes?: PermissionType[];
}

export const CreateRolePage = ({ title = "" }) => {
  const [form] = Form.useForm<Role>();
  const [loading, setLoading] = useState(false);
  const [loadingCheckedPermission, setLoadingCheckedPermission] =
    useState(false);
  const [visible, setVisible] = useState(false);
  const [status, setStatus] = useState<ModalStatus>("create");
  const [checkedKeys, setCheckedKeys] = useState<string[]>([]);
  const [role, setRole] = useState<Role>();
  const [treeData, setTreeData] = useState<ITreeData[]>([]);
  const [permissionData, setPermissionData] = useState<IPermission[]>([]);
  const { permissions, fetchData } = usePermission({
    initQuery: { page: 1, limit: 500 },
  });
  const [activeModule, setActiveModule] = useState<string | null>(null);
  const navigate = useNavigate();

  const generateSelectedKeys = async (role: Role) => {
    if (role.id) {
      try {
        setLoadingCheckedPermission(true);
        const res = await roleApi.findOne(role.id);
        const permissions: Permission[] = res.data.permissions;
        setCheckedKeys(permissions.map((e) => e.name));
      } catch (error) {
        console.log({ error });
      } finally {
        setLoadingCheckedPermission(false);
      }
    }
  };

  const generateTreeData = () => {
    if (treeData.length) {
      return;
    }
    for (const route of adminRoutes) {
      if (route.isPublic) {
        //Không hiển thị các route public
        continue;
      }
      if (!route.noRole && route.checkIsDev ? userStore.info.isDev : true) {
        const data: ITreeData = {
          title: route.title || "",
          key: route.path || "",
        };

        treeData.push(data);
        if (route.children) {
          data.children = [];
          for (const childRoute of route.children) {
            if (route.checkIsDev ? userStore.info.isDev : true) {
              data.children.push({
                title: childRoute.element
                  ? childRoute.title + " (Menu)"
                  : childRoute.title || "",
                // key: route.path + "/" + childRoute.name || "",
                key: childRoute.name || "",
              });
            }
          }
        }
      }
    }

    setTreeData([...treeData]);
  };

  const generateFlatData = (): void => {
    if (permissionData.length) {
      return;
    }
    for (const route of adminRoutes) {
      if (route.isPublic) {
        //Không hiển thị các route public
        continue;
      }
      if (!route.noRole && route.checkIsDev ? userStore.info.isDev : true) {
        const data: IPermission = {
          title: route.title || "",
          key: route.path || "",
          permissionTypes: route.permissionTypes || [],
        };

        permissionData.push(data);
        if (route.children) {
          data.children = [];
          for (const childRoute of route.children) {
            if (route.checkIsDev ? userStore.info.isDev : true) {
              data.children.push({
                title: childRoute.element
                  ? childRoute.title + " (Menu)"
                  : childRoute.title || "",
                // key: route.path + "/" + childRoute.name || "",
                key: childRoute.name || "",
                permissionTypes: childRoute.permissionTypes || [],
              });
            }
          }
        }
      }
    }

    setPermissionData([...permissionData]);
    setActiveModule(permissionData[0].key);
  };

  console.log({ dataNe: generateFlatData() });
  const onCheck = (checkedKeys: any, info: any) => {
    console.log(checkedKeys);
    setCheckedKeys(checkedKeys);
  };

  const handleSubmit = async () => {
    const valid = await form.validateFields();
    // debugger;
    const data = {
      role: form.getFieldsValue(),
      permissionIds: checkedKeys
        .map((e) => {
          const find = permissions.find((p) => p.name == e);
          return find?.id;
        })
        .filter((e) => e),
    };

    console.log({ data });
    // // debugger
    if (status == "create") {
      createData(data);
    } else {
      updateData(data);
    }
  };

  const createData = async (data: any) => {
    setLoading(true);
    try {
      const res = await roleApi.create(data);
      message.success("Tạo role thành công");
    } finally {
      setLoading(false);
    }
  };

  const updateData = async (data: any) => {
    setLoading(true);
    try {
      const res = await roleApi.update(role?.id || 0, data);
      message.success("Cập nhật role thành công");
    } finally {
      setLoading(false);
    }
  };

  const handleToggle = (index: number, checked: boolean) => {
    const updatedData = [...permissionData];
    setPermissionData(updatedData);
  };

  const handleActionChange = (
    index: number,
    selectedActions: IPermission[]
  ) => {
    const updatedData = [...permissionData];
    updatedData[index].children = selectedActions;
    setPermissionData(updatedData);
  };

  console.log({ permissionData });

  const renderDropdown = (actions: IPermission[], index: number) => {
    const menu = (
      <div
        className="custom-scrollbar flex items-start gap-2 flex-col bg-white rounded-md p-2 border border-solid"
        style={{
          maxHeight: "400px",
          overflowY: "auto", // This enables scrolling if content exceeds max height
        }}
      >
        {actions?.map((action) => (
          <div key={action.key} onClick={(e) => e.stopPropagation()}>
            <Checkbox
              checked={checkedKeys.includes(action.key)}
              onChange={(e) => {
                e.stopPropagation();
                // Cập nhật checkedKeys khi checkbox thay đổi
                const updatedCheckedKeys = e.target.checked
                  ? [...checkedKeys, action.key]
                  : checkedKeys.filter((key) => key !== action.key);

                // Cập nhật lại checkedKeys và đồng bộ hóa với onCheck
                setCheckedKeys(updatedCheckedKeys);
                onCheck(updatedCheckedKeys, {}); // Gọi onCheck để đồng bộ hóa với cây
              }}
            >
              {action.title}
            </Checkbox>
          </div>
        ))}
      </div>
    );

    return (
      <Dropdown overlay={menu} trigger={["click"]}>
        <FaChevronDown className="cursor-pointer" />
      </Dropdown>
    );
  };
  console.log({ permissionData });
  console.log({ treeData });
  console.log({ checkedKeys });

  return (
    <>
      <div>
        <div className="font-bold text-2xl mb-[20px]">Tạo vai trò</div>
        <Card>
          <Form layout="vertical" form={form}>
            <Row gutter={16}>
              <Col span={14}>
                <Form.Item label="Tên vai trò" name="name" rules={rules}>
                  <CustomInput
                    placeholder="Tên vai trò"
                    disabled={role?.isAdmin}
                  />
                </Form.Item>
              </Col>

              <Col span={14}>
                <Form.Item
                  label="Mô tả vai trò"
                  name="description"
                  rules={rules}
                >
                  <CustomInput
                    type="textarea"
                    placeholder="Mô tả"
                    disabled={role?.isAdmin}
                    rows={4}
                  />
                </Form.Item>
              </Col>
            </Row>
          </Form>

          <div style={{ display: "flex", gap: 32, marginTop: 16 }}>
            <div style={{ width: 250 }}>
              {permissionData.map((item) => (
                <div
                  key={item.key}
                  style={{
                    padding: "0 12px",
                    marginBottom: 8,
                    cursor: "pointer",
                    border:
                      activeModule === item.key ? "" : "1px solid #f0f0f0",
                    height: 50,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                    borderRadius: 8,
                    color: activeModule === item.key ? "white" : "black",
                    backgroundColor:
                      activeModule === item.key ? "#19345B" : "#fff",
                  }}
                  onClick={() => setActiveModule(item.key)}
                >
                  <div className="text-base font-medium">{item.title}</div>
                  <div>
                    {activeModule === item.key ? (
                      <PlusOutlined />
                    ) : (
                      <MinusOutlined />
                    )}
                  </div>
                </div>
              ))}
            </div>

            <div style={{ flex: 1 }}>
              {permissionData.map(
                (item) =>
                  activeModule === item.key &&
                  item.children && (
                    <div key={item.key} style={{ marginTop: 0 }}>
                      <Space className="mb-6">
                        <Switch
                          checked={
                            checkedKeys.includes(item.key) ||
                            item.children?.some((child) =>
                              checkedKeys.includes(child.key)
                            )
                          }
                          onChange={(checked) => {
                            if (checked) {
                              const newCheckedKeys = [
                                ...checkedKeys,
                                item.key,
                                //@ts-ignore
                                ...item?.children
                                  .map((child) => child.key)
                                  .filter((key) => !checkedKeys.includes(key)),
                              ];
                              setCheckedKeys(newCheckedKeys);
                            } else {
                              const newCheckedKeys = checkedKeys.filter(
                                (key) =>
                                  key !== item.key &&
                                  //@ts-ignore

                                  !item.children.some(
                                    (child) => child.key === key
                                  )
                              );
                              setCheckedKeys(newCheckedKeys);
                            }
                          }}
                        />
                        <div>Có toàn quyền trên tất cả danh mục</div>
                      </Space>
                      <table
                        className="permission-table"
                        style={{
                          width: "100%",
                          borderCollapse: "collapse",
                          border: "1px solid #F5F5F5",
                        }}
                      >
                        <thead>
                          <tr>
                            <th
                              style={{
                                textAlign: "left",
                                padding: "8px",
                                border: "1px solid #F5F5F5",
                                width: "250px",
                              }}
                            >
                              Chức năng
                            </th>
                            {item.permissionTypes?.map((permType) => (
                              <th
                                key={permType}
                                style={{
                                  textAlign: "center",
                                  padding: "8px",
                                  border: "1px solid #F5F5F5",
                                }}
                              >
                                {PermissionTypeTrans[permType]?.label}
                              </th>
                            ))}
                          </tr>
                        </thead>
                        <tbody>
                          {item.children.map((child) => (
                            <tr key={child.key}>
                              <td
                                style={{
                                  padding: "8px",
                                  border: "1px solid #F5F5F5",
                                }}
                              >
                                {child.title}
                              </td>
                              {item.permissionTypes?.map((permType) => {
                                const permissionKey = `${child.key}:${permType}`;
                                const isAllowed =
                                  child.permissionTypes?.includes(permType);
                                console.log({ checkedKeys });
                                console.log({ permissionKey });
                                return (
                                  <td
                                    key={permType}
                                    style={{
                                      textAlign: "center",
                                      padding: "8px",
                                      border: "1px solid #F5F5F5",
                                    }}
                                  >
                                    <Checkbox
                                      checked={
                                        !isAllowed
                                          ? false
                                          : checkedKeys.includes(child.key)
                                      }
                                      disabled={!isAllowed}
                                      onChange={(e) => {
                                        const updated = e.target.checked
                                          ? [...checkedKeys, child.key]
                                          : checkedKeys.filter(
                                              (k) => k !== child.key
                                            );
                                        console.log(updated);
                                        setCheckedKeys(updated);
                                      }}
                                    />
                                  </td>
                                );
                              })}
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )
              )}
            </div>
          </div>
          <div className="flex gap-[16px]  mt-2">
            <CustomButton
              className="cta-button"
              loading={loading}
              onClick={() => {
                handleSubmit();
              }}
            >
              Tạo vai trò
            </CustomButton>
            <CustomButton
              variant="outline"
              className="cta-button"
              onClick={() => {
                navigate("/master-data/role-list");
              }}
            >
              Hủy
            </CustomButton>
          </div>
        </Card>
      </div>
    </>
  );
};
