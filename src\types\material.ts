import { Brand } from "./brand";
import { Color } from "./color";
import { Component } from "./component";
import { Dictionary } from "./dictionary";
import { FileAttach } from "./fileAttach";
import { MaterialCategory } from "./materialCategory";
import { MaterialGroup } from "./materialGroup";
import { MaterialType } from "./materialType";
import { MaterialUsage } from "./materialUsage";
import { Pattern } from "./pattern";
import { ProductOrigin } from "./productOrigin";
import { Provider } from "./provider";
import { Season, SeasonType } from "./season";
import { StretchDirection } from "./stretchDirection";
import { Unit } from "./unit";


export enum EMaterialType {
  Material = 'MATERIAL',
  Product = 'PRODUCT'
}


export interface Material {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  code: string; // Mã nguyên liệu (*)
  name: string; // Tên nguyên liệu (*)
  description: string; // <PERSON>ô tả nguyên liệu (*)
  oldSystemCode?: string; // Mã hệ thống cũ
  supplierProductCode?: string; // Mã SP NCC
  // usageForm?: MaterialUsage; // Hình thức sử dụng
  productOrigin: ProductOrigin; // Nguồn gốc sản phẩm (*)
  lengthMm: number; // Dài (mm) (*)
  widthMm?: number; // Rộng (mm)
  heightMm?: number; // Cao (mm)
  thicknessMm?: number; // Dày (mm)
  diameterMm?: number; // Đường kính
  otherDimensions?: string; // Kích thước khác
  salePrice?: number; // Giá bán
  purchasePrice?: number; // Giá mua
  taxPercent?: number; // Thuế (%)
  trackInventory: boolean; // Theo dõi tồn kho (*)
  inventoryAccount?: string; // Tài khoản tồn kho (*)
  revenueAccount?: string; // Tài khoản doanh thu (*)
  salesReturnAccount?: string; // Tài khoản hàng bán trả lại (*)
  cogsAccount?: string; // Tài khoản giá vốn (*)
  discountAccount?: string; // Tài khoản chiết khấu (*)
  minQuantity?: number; // Số lượng tối thiểu
  maxQuantity?: number; // Số lượng tối đa
  shelfLifeDays?: number; // Thời gian tồn (ngày)
  isActive: boolean;
  unit: Unit;
  provider: Provider;
  brand: Brand;
  materialCategory: MaterialCategory;
  materialGroup: MaterialGroup;
  usageForm: MaterialUsage;
  image?: string;
  fileAttaches?: FileAttach[];
  type: EMaterialType
  avatar?: string;
}

export enum MaterialEnum {
  Fabric = "FABRIC",
  Lining = "LINING",
  Button = "BUTTON",
}

export const MaterialEnumTrans = {
  [MaterialEnum.Button]: {
    value: MaterialEnum.Button,
    label: "Button",
  },
  [MaterialEnum.Fabric]: {
    value: MaterialEnum.Fabric,
    label: "Fabric",
  },
  [MaterialEnum.Lining]: {
    value: MaterialEnum.Lining,
    label: "Lining",
  },
};
