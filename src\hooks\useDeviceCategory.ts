import { deviceCategoryApi } from "api/deviceCategory.api";
import { useState } from "react";
import { DeviceCategory } from "types/deviceCategory";
import { QueryParam } from "types/query";

export interface DeviceCategoryQuery extends QueryParam {}

interface UseDeviceCategoryProps {
  initQuery: DeviceCategoryQuery;
}

export const useDeviceCategory = ({ initQuery }: UseDeviceCategoryProps) => {
  const [data, setData] = useState<DeviceCategory[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<DeviceCategoryQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await deviceCategoryApi.findAll(query);

      setData(data.deviceCategories);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return {
    deviceCategories: data,
    total,
    fetchData,
    loading,
    setQuery,
    query,
    setData,
  };
};
