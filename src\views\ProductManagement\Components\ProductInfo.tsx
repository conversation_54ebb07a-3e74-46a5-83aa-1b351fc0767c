export {};

// import { <PERSON><PERSON>, <PERSON>confirm, Spin, Table, Tag } from "antd";
// import DropdownCell from "components/Table/DropdownCell";
// import React from "react";

// const ProductInfo = () => {
//   return (
//     <Spin spinning={loading}>
//       <Table
//         rowKey="id"
//         pagination={false}
//         dataSource={components}
//         scroll={{ x: "max-content" }}
//       >
//         {/* Tên nhóm */}
//         <Table.Column
//           title="Tên nhóm"
//           dataIndex="code"
//           align="left"
//           key="groupName"
//           render={(text) => <div>{text}</div>}
//         />

//         {/* Tên */}
//         <Table.Column
//           title="Tên"
//           dataIndex="code"
//           align="left"
//           key="name"
//           render={(text) => <div>{text}</div>}
//         />

//         {/* Tên (Tiế<PERSON>) */}
//         <Table.Column
//           title="Tên (Tiếng Việt)"
//           dataIndex="code"
//           align="left"
//           key="nameVi"
//           render={(text) => <div>{text}</div>}
//         />

//         {/* Loại sản phẩm */}
//         <Table.Column
//           title="Loại sản phẩm"
//           dataIndex="code"
//           align="left"
//           key="productType"
//           render={(text) => <div>{text}</div>}
//         />

//         {/* NVL sử dụng */}
//         <Table.Column
//           title="NVL sử dụng"
//           dataIndex="privateName"
//           align="left"
//           key="materialUsage"
//           render={(text) => <div>{text}</div>}
//         />

//         {/* Nhóm cha */}
//         <Table.Column
//           title="Nhóm cha"
//           dataIndex="parent"
//           align="center"
//           key="parentGroup"
//           render={(parent: Component) =>
//             parent ? (
//               <Tag color={getColor(parent.id)}>
//                 {parent.code} - {parent.name}
//               </Tag>
//             ) : (
//               "-"
//             )
//           }
//         />

//         {/* Ghi chú */}
//         <Table.Column
//           title="Ghi chú"
//           dataIndex="code"
//           align="left"
//           key="note"
//           render={(text) => <div>{text}</div>}
//         />

//         {/* Giá tiền */}
//         <Table.Column
//           title="Giá tiền"
//           dataIndex="code"
//           align="left"
//           key="price"
//           render={(text) => <div>{text}</div>}
//         />

//         {/* Trạng thái */}
//         <Table.Column
//           title="Trạng thái"
//           dataIndex="status"
//           align="center"
//           key="status"
//           render={(status) => (
//             <Tag
//               color={status ? "red" : "green"}
//               className="w-[60px] text-center"
//             >
//               {status ? "Khóa" : "Mở"}
//             </Tag>
//           )}
//         />

//         {/* Thao tác */}
//         <Table.Column
//           width={140}
//           fixed="right"
//           align="center"
//           key="action"
//           render={(_, record: Component) => (
//             <DropdownCell
//               text="Thao tác"
//               trigger={["click"]}
//               items={[
//                 {
//                   key: "update",
//                   label: (
//                     <Button
//                       className="w-full"
//                       type="primary"
//                       onClick={() => modalRef.current?.handleUpdate(record)}
//                     >
//                       Cập nhật
//                     </Button>
//                   ),
//                 },
//                 {
//                   key: "delete",
//                   label: (
//                     <Popconfirm
//                       title="Xác nhận xóa?"
//                       onConfirm={() => handleDelete(record.id)}
//                     >
//                       <Button className="w-full">Xóa thành phần</Button>
//                     </Popconfirm>
//                   ),
//                 },
//                 {
//                   key: "block",
//                   label: (
//                     <Popconfirm
//                       title={
//                         <div>
//                           <h1 className="text-sm">
//                             Xác nhận {record.isBlocked ? "mở khóa" : "khóa"}{" "}
//                             thành phần này?
//                           </h1>
//                         </div>
//                       }
//                       onConfirm={() => blockComponent(record)}
//                       okText="Đồng ý"
//                       cancelText="Không"
//                     >
//                       <Button
//                         icon={
//                           record.isBlocked ? (
//                             <UnlockOutlined />
//                           ) : (
//                             <LockOutlined />
//                           )
//                         }
//                         className={`w-full !text-white ${
//                           record.isBlocked ? "!bg-green-500" : "!bg-amber-500"
//                         } !font-medium`}
//                       >
//                         {record.isBlocked ? "Mở khóa" : "Khóa"}
//                       </Button>
//                     </Popconfirm>
//                   ),
//                 },
//               ]}
//             >
//               <a onClick={(e) => e.preventDefault()}>
//                 <Space>
//                   Thao tác
//                   <DownOutlined />
//                 </Space>
//               </a>
//             </DropdownCell>
//           )}
//         />
//       </Table>

//       {/* Pagination */}
//       <Pagination
//         currentPage={query.page}
//         total={total}
//         onChange={({ page, limit }) => {
//           setQuery({ ...query, page, limit });
//         }}
//       />
//     </Spin>
//   );
// };

// export default ProductInfo;
