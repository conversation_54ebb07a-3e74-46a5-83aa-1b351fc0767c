import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const departmentApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/department",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/department",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/department/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/department/${id}`,
      method: "delete",
    }),
};
