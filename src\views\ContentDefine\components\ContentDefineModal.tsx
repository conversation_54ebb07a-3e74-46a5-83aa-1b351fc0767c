import { Col, Form, Input, message, Modal, Row, Select } from "antd";
import { Rule } from "antd/lib/form";
import { contentDefineApi } from "api/content-define.api";
import { RichTextEditorV2 } from "components/Editor/RichTextEditorV2";
import { observer } from "mobx-react";
import { useEffect, useState } from "react";
import {
  ContentDefine,
  ContentDefineType,
  ContentDefineTypeTrans,
} from "types/content-define";
import { ModalStatus } from "types/modal";

const rules: Rule[] = [{ required: true, message: "Bắt buộc nhập!" }];
const { Option } = Select;

export const ContentDefineModal = observer(
  ({
    visible,
    status,
    contentDefine,
    onClose,
    onSubmitOk,
  }: {
    visible: boolean;
    status: ModalStatus;
    contentDefine: Partial<ContentDefine>;
    onClose: () => void;
    onSubmitOk: () => void;
  }) => {
    const [form] = Form.useForm<ContentDefine>();
    // const [, setForceUpdate] = useState({});
    const [loading, setLoading] = useState(false);
    // const [content, setContent] = useState("");
    // const editorRef = useRef<{ setContent: (content: string) => void }>(null);
    // const isEditorInit = useRef(false);
    const [content, setContent] = useState("");

    useEffect(() => {
      if (status == "create" && visible) {
        form.resetFields();
      } else {
      }
      // setContent(contentDefine.body || "");
    }, [visible, status]);

    useEffect(() => {
      form.setFieldsValue({ ...contentDefine });
      // if (isEditorInit.current) {
      //   editorRef.current?.setContent(contentDefine.body || "");
      // }
      setContent(contentDefine.body || "");
      // setForceUpdate({});
    }, [contentDefine]);

    const createData = async () => {
      const valid = await form.validateFields();
      const data = {
        contentDefine: {
          ...form.getFieldsValue(),
          // body: content,
        },
      };

      setLoading(true);
      try {
        const res = await contentDefineApi.create(data);
        message.success("Tạo mới thành công!");
        onClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const valid = await form.validateFields();

      const data = {
        contentDefine: {
          ...form.getFieldsValue(),
          // body:form.getFieldValue('body') ||  content,
        },
      };

      setLoading(true);
      try {
        const res = await contentDefineApi.update(contentDefine?.id || 0, data);
        message.success("Cập nhật thành công");
        onClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    return (
      <Modal
        destroyOnClose
        maskClosable={false}
        onCancel={onClose}
        visible={visible}
        title={status == "create" ? "Tạo mới nội dung" : "Cập nhật nội dung"}
        style={{ top: 20 }}
        width={1200}
        cancelText="Đóng"
        okText="Lưu"
        confirmLoading={loading}
        onOk={() => {
          status == "create" ? createData() : updateData();
        }}
      >
        <Form layout="vertical" form={form}>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label="Loại" name="type" rules={rules}>
                <Select disabled={status == "update"} style={{ width: "100%" }}>
                  {Object.values(ContentDefineType).map((e) => (
                    <Option key={e} value={e}>
                      {ContentDefineTypeTrans[e]}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={16}>
              {/* <Form.Item shouldUpdate noStyle>
                {() => (
                  <Form.Item required label="Nội dung" name="body">
                    <RichTextEditorV2
                      onChange={(body) => {
                        form.setFieldsValue({ body });
                      }}
                      content={form.getFieldValue("content")}
                    />
                  </Form.Item>
                )}
              </Form.Item> */}
              <Form.Item name="body" hidden>
                <Input />
              </Form.Item>
              <RichTextEditorV2
                label={<span className="font-medium">Nội dung</span>}
                onChange={(content) => {
                  setContent(content);
                  console.log({ content });
                  form.setFieldsValue({ body: content });
                }}
                content={content}
              />
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
);
