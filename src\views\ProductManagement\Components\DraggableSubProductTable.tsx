import {
  DndContext,
  Drag<PERSON><PERSON><PERSON><PERSON>,
  Mouse<PERSON><PERSON>or,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Table } from "antd";
import { ColumnsType, TableProps } from "antd/es/table";
import React, { memo } from "react";
import { LuArrowDownUp } from "react-icons/lu";
import { SubProduct } from "types/subProduct";

// Row sortable wrapper
const SortableRow = ({ children, ...props }: any) => {
  const id = props["data-row-key"];
  const { setNodeRef, transform, transition, attributes, listeners } =
    useSortable({ id });

  const style: React.CSSProperties = {
    transform: CSS.Transform.toString(transform),
    transition,
    cursor: "grab",
  };

  return (
    <tr ref={setNodeRef} style={style} {...attributes} {...listeners}>
      {children}
    </tr>
  );
};

// Drag icon
const DragHandle = () => {
  return (
    <div className="cursor-grab flex justify-center items-center h-full">
      <LuArrowDownUp />
    </div>
  );
};

interface DraggableSubProductTableProps extends TableProps<SubProduct> {
  dataSource: SubProduct[];
  onChangeOrder: (newList: SubProduct[]) => void;
}

const DraggableSubProductTable = ({
  dataSource,
  onChangeOrder,
  columns,
  ...props
}: DraggableSubProductTableProps) => {
  const sensors = useSensors(useSensor(MouseSensor));

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active?.id !== over?.id) {
      const oldIndex = dataSource.findIndex((item) => item.id === active.id);
      const newIndex = dataSource.findIndex((item) => item.id === over?.id);

      const newList = arrayMove([...dataSource], oldIndex, newIndex);
      onChangeOrder(newList);
    }
  };

  const columnsWithDrag: ColumnsType<SubProduct> = [
    ...(columns || []),
    {
      title: "Sắp xếp",
      width: 50,
      align: "center",
      key: "sort",
      render: () => <DragHandle />,
    },
  ];

  return (
    <DndContext sensors={sensors} onDragEnd={handleDragEnd}>
      <SortableContext
        items={dataSource.map((item) => item.id!)}
        strategy={verticalListSortingStrategy}
      >
        <Table
          {...props}
          rowKey={(record) => record.id!}
          columns={columnsWithDrag}
          dataSource={dataSource}
          pagination={false}
          components={{
            body: {
              row: (rowProps: any) => <SortableRow {...rowProps} />,
            },
          }}
        />
      </SortableContext>
    </DndContext>
  );
};

export default memo(DraggableSubProductTable);
