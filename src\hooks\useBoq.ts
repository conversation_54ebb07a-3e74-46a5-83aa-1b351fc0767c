import { boqApi } from "api/boq.api";
import { useState } from "react";
import { BOQ } from "types/boq";
import { QueryParam } from "types/query";

export interface BoqQuery extends QueryParam {}

interface UseBoqProps {
  initQuery: BoqQuery;
}

export const useBoq = ({ initQuery }: UseBoqProps) => {
  const [data, setData] = useState<BOQ[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<BoqQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await boqApi.findAll(query);

      setData(data.boqs);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return {
    boqs: data,
    totalBoq: total,
    fetchBoq: fetchData,
    loadingBoq: loading,
    setQueryBoq: setQuery,
    queryBoq: query,
  };
};
