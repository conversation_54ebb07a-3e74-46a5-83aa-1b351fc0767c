import {
  Card,
  Spin,
  Button,
  Space,
  Tooltip,
  Modal,
  Tag,
  Avatar,
  message,
  Select,
  Radio,
  Switch,
} from "antd";
import PencilIcon from "assets/svgs/PencilIcon";
import CustomButton from "components/Button/CustomButton";
import CustomInput from "components/Input/CustomInput";
import PageTitle from "components/PageTitle/PageTitle";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import { Pagination } from "components/Pagination";
import { useTheme } from "context/ThemeContext";
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { PermissionNames } from "types/PermissionNames";
import dayjs from "dayjs";
import { checkRoles, filterActionColumnIfNoPermission } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { observer } from "mobx-react";
import { $url } from "utils/url";
import logoImage from "assets/images/logo.png";
import { Staff } from "types/staff";
import { unixToDate } from "utils/dateFormat";
import DeleteIcon from "assets/svgs/DeleteIcon";
import QueryLabel from "components/QueryLabel/QueryLabel";
import { DictionarySelector } from "components/Selector/DictionarySelector";
import { DictionaryType } from "types/dictionary";
import { use } from "echarts";
import { projectItemApi } from "api/projectItem.api";
import { useProjectItem } from "hooks/useProjectItem";
import { TableProps } from "antd/lib";
import { CopyOutlined, PoweroffOutlined } from "@ant-design/icons";
import { useApprovalTemplate } from "hooks/useApprovalTemplate";
import { approvalTemplateApi } from "api/approvalTemplate.api";
import {
  ApprovalStepsCard,
  StepItem,
} from "components/ApproveProcess/ApprovalStepsCard";
import { ApprovalListStatus } from "types/approvalList";
import {
  ApprovalTemplate,
  ApprovalTemplateMode,
  ApprovalTemplateModeTrans,
  ApprovalTemplateType,
  ApprovalTemplateTypeOptions,
} from "types/approvalTemplate";
import EditButton from "components/Button/EditButton";
import DeleteButton from "components/Button/DeleteButton";
import ActiveStatusTagSelect from "components/ActiveStatus/ActiveStatusTagSelect";
import LockButton from "components/Button/LockButton";
import EditApprovalTemplateModal from "./components/EditApprovalTemplateModal";
import { getTitle } from "utils";
import { appStore } from "store/appStore";

interface ApproveProcessPageProps {
  title: string;
  projectId?: number;
  hidePageTitle?: boolean;
}

function ApproveProcessPage({
  title,
  projectId,
  hidePageTitle = false,
}: ApproveProcessPageProps) {
  const {
    haveAddPermission,
    haveDeletePermission,
    haveEditPermission,
    haveBlockPermission,
    haveViewAllPermission,
  } = checkRoles(
    {
      add: PermissionNames.approveProcessAdd,
      edit: PermissionNames.approveProcessEdit,
      delete: PermissionNames.approveProcessDelete,
      block: PermissionNames.approveProcessBlock,
      viewAll: PermissionNames.approveProcessViewAll,
    },

    permissionStore.permissions
  );

  const [searchValue, setSearchValue] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [loadingDelete, setLoadingDelete] = useState(false);
  const [page, setPage] = React.useState(1);
  const [limit, setLimit] = React.useState(20);
  // const [editingName, setEditingName] = useState<string>("");
  // const [editingDescription, setEditingDescription] = useState<string>("");
  // const [editingType, setEditingType] = useState<string>("");
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [editingRecord, setEditingRecord] = useState<any>(null);
  const [isEditMode, setIsEditMode] = useState(false);

  const {
    fetchData,
    approvalTemplates,
    loading,
    query,
    setQuery,
    total,
    isEmptyQuery,
  } = useApprovalTemplate({
    initQuery: {
      limit: 10,
      page: 1,
      isAdmin: haveViewAllPermission ? true : undefined,
      projectId: appStore.currentProject?.id,
    },
  });

  useEffect(() => {
    document.title = getTitle(title);
    fetchData();
  }, []);

  // useEffect(() => {
  //   if (editModalOpen && editingRecord) {
  //     setEditingName(editingRecord.name || "");
  //     setEditingDescription(editingRecord.description || "");
  //     setEditingType(editingRecord.type || "");
  //   }
  // }, [editModalOpen, editingRecord]);

  useEffect(() => {
    if (editingRecord) {
      const type =
        (editingRecord.approvalTemplateDetails?.length ?? 0) <= 2
          ? "simple"
          : "complex";
      setNewTemplateType(type);
    }
  }, [editingRecord]);

  // const handleTableChange: TableProps<any>["onChange"] = (
  //   pagination,
  //   filters,
  //   sorter
  // ) => {
  //   if (!Array.isArray(sorter)) {
  //     const fieldMap: Record<string, string> = {
  //       name: "projectItem.name",
  //       area: "projectItem.area",
  //       note: "projectItem.note",
  //       isActive: "projectItem.isActive",
  //     };
  //     const columnKey = sorter.field || sorter.column?.key;

  //     if (!sorter.order) {
  //       // setSortField(null);
  //       // setSortOrder(null);
  //       query.queryObject = undefined;
  //       setQuery({ ...query });
  //     } else {
  //       const order = sorter.order === "ascend" ? "ASC" : "DESC";
  //       // setSortField("jobCategory.name");
  //       // setSortOrder(order);
  //       const field = fieldMap[columnKey as string] || columnKey;
  //       const newQueryObject = JSON.stringify([
  //         {
  //           type: "sort",
  //           field,
  //           value: order,
  //         },
  //       ]);
  //       query.queryObject = newQueryObject;
  //       setQuery({ ...query });
  //     }
  //     fetchData();
  //   } else {
  //     query.queryObject = undefined;
  //     setQuery({ ...query });
  //     fetchData();
  //   }
  // };

  const { darkMode } = useTheme();
  const navigate = useNavigate();

  const handleSearch = (value: string) => {
    setSearchValue(value);
  };

  const handleStatusChange = (value: string) => {
    setStatusFilter(value);
    const newQuery = {
      ...query,
      page: 1,
      status: value || undefined,
    };
    setQuery(newQuery);

    setTimeout(() => {
      fetchData();
    }, 100);
  };

  // State cho modal
  const [openApprovalModal, setOpenApprovalModal] = useState(false);
  // const [approvalSteps, setApprovalSteps] = useState<StepItem[]>([]);
  const [loadingApprove, setLoadingApprove] = useState(false);
  const [removeApprovalList, setRemoveApprovalList] = useState<number[]>([]);
  // Thêm state để lưu loại quy trình khi bật
  const [pendingActiveRecord, setPendingActiveRecord] = useState<any>(null);
  // Thêm state
  const [newTemplateType, setNewTemplateType] = useState<"simple" | "complex">(
    "simple"
  );

  const handleCreateApprovalTemplate = () => {
    // setApprovalSteps([]);
    setNewTemplateType("simple");
    setOpenApprovalModal(true);
  };

  const handleEditApprovalTemplate = async (record: { id: number }) => {
    try {
      setLoadingApprove(true);
      const res = await approvalTemplateApi.findOne(record.id);
      setEditingRecord(res.data);
      // setApprovalSteps(
      //   (res.data.approvalTemplateDetails || []).map(
      //     (item: any, idx: number) => ({
      //       ...item,
      //       position: idx,
      //       status: item.status || ApprovalListStatus.Pending,
      //       membershipId: item.membershipId ?? item.membership?.id ?? 0,
      //       membership2Id:
      //         item.membership2Id ?? item.membership2?.id ?? undefined,
      //     })
      //   )
      // );
      setEditModalOpen(true);
    } catch (error) {
      console.log({ error });
      message.error("Không lấy được thông tin mẫu quy trình duyệt");
    } finally {
      setLoadingApprove(false);
    }
  };

  const handleUpdateApprovalProcess = async (payload: any) => {
    // debugger;

    if (!editingRecord?.id) return;
    try {
      setLoadingApprove(true);

      // Làm sạch dữ liệu: Nếu không có staff2Id trên UI thì set về 0 và xóa staff2
      // const cleanedSteps = (payload.approvalTemplateDetails || []).map(
      //   (item: any) => {
      //     if (!item.staff2Id) {
      //       return {
      //         ...item,
      //         staff2Id: 0,
      //         staff2: undefined,
      //       };
      //     }
      //     return item;
      //   }
      // );

      await approvalTemplateApi.update(editingRecord.id, {
        ...payload,
        // approvalTemplateDetails: cleanedSteps,
        approvalTemplate: {
          ...payload.approvalTemplate,
          projectId: editingRecord.projectId,
        },
      });

      setEditingRecord({
        ...editingRecord,
        ...payload.approvalTemplate,
        projectId: editingRecord.projectId,
      });

      message.success("Cập nhật quy trình duyệt thành công");
      setEditModalOpen(false);
      fetchData();
    } catch (error) {
      console.log({ error });
      // message.error("Cập nhật quy trình duyệt thất bại");
    } finally {
      setLoadingApprove(false);
    }
  };

  // // Hàm xử lý approve/reject
  // const handleApproveProcess = () => {
  //   // Xử lý duyệt
  // };
  // const handleRejectProcess = () => {
  //   // Xử lý từ chối
  // };

  const handleDeleteApprovalTemplate = async (id: number) => {
    try {
      await approvalTemplateApi.delete(id);
      message.success("Xóa mẫu quy trình phê duyệt thành công");
      fetchData();
    } catch (e) {
      console.log({ e });
    } finally {
    }
  };

  const handleActiveReport = async (id: number, isActive: boolean) => {
    // Tìm record hiện tại
    const record = approvalTemplates.find((item) => item.id === id);
    if (!record) return;

    try {
      await approvalTemplateApi.update(id, {
        // approvalTemplateDetails: record.approvalTemplateDetails || [],
        approvalTemplate: {
          // name: record.name,
          // description: record.description,
          // type: record.type,
          isActive: !isActive,
          // isDefault: record.isDefault,
        },
      });
      message.success(!isActive ? "Bật thành công" : "Tắt thành công");
      fetchData();
    } catch (error) {
      message.error("Có lỗi xảy ra khi thay đổi trạng thái");
    }
  };

  const handleRowClick = async (record: ApprovalTemplate) => {
    await handleEditApprovalTemplate(record);
    setIsEditMode(false);
  };

  const columns: CustomizableColumn<any>[] = [
    {
      key: "STT",
      title: "STT",
      dataIndex: "STT",
      width: 50,
      defaultVisible: true,
      alwaysVisible: false,
      align: "left",
      render: (_, __, index) => {
        return (
          <div className="text-left">
            {query.page && query.limit
              ? (query.page - 1) * query.limit + index + 1
              : index + 1}
          </div>
        );
      },
    },
    {
      key: "type",
      title: "Loại",
      dataIndex: "type",
      width: 150,
      defaultVisible: true,
      alwaysVisible: false,
      render: (value) => {
        const option = ApprovalTemplateTypeOptions.find(
          (opt) => opt.value === value
        );
        return option ? option.label : value;
      },
      onCell: (record) => ({
        style: { cursor: "pointer", color: "#1677ff" },
        onClick: () => handleRowClick(record),
      }),
    },
    {
      key: "actions",
      title: "Bật tắt quy trình",
      width: 150,
      align: "center",
      fixed: "left",
      defaultVisible: true,
      alwaysVisible: true,
      render: (_, record) => (
        <Space size="small">
          {haveBlockPermission && (
            <Switch
              checked={record.isActive}
              checkedChildren="Bật"
              unCheckedChildren="Tắt"
              onClick={async (checked, e) => {
                e.stopPropagation();
                Modal.confirm({
                  title: `Bật/Tắt quy trình duyệt`,
                  getContainer: () => {
                    return document.getElementById("App") as HTMLElement;
                  },
                  icon: null,
                  content: (
                    <>
                      <div>
                        Bạn có chắc chắn muốn <b>{checked ? "bật" : "tắt"}</b>{" "}
                        quy trình duyệt này không?
                      </div>
                    </>
                  ),
                  footer: (_, { OkBtn, CancelBtn }) => (
                    <>
                      <CustomButton
                        variant="outline"
                        className="cta-button"
                        onClick={() => {
                          Modal.destroyAll();
                        }}
                      >
                        Không
                      </CustomButton>
                      <CustomButton
                        variant="primary"
                        className="cta-button"
                        onClick={async () => {
                          await handleActiveReport(record.id, record.isActive);
                          Modal.destroyAll();
                        }}
                      >
                        Có
                      </CustomButton>
                    </>
                  ),
                });
                // if (!checked) {
                //   // Nếu đang bật, hiện modal xác nhận tắt
                //   Modal.confirm({
                //     title: `Tắt quy trình duyệt "${record.name}"`,
                //     getContainer: () => {
                //       return document.getElementById("App") as HTMLElement;
                //     },
                //     icon: null,
                //     content: (
                //       <>
                //         <div>
                //           Bạn có chắc chắn muốn <b>{checked ? "bật" : "tắt"}</b>{" "}
                //           quy trình duyệt này không?
                //         </div>
                //       </>
                //     ),
                //     footer: (_, { OkBtn, CancelBtn }) => (
                //       <>
                //         <CustomButton
                //           variant="outline"
                //           className="cta-button"
                //           onClick={() => {
                //             Modal.destroyAll();
                //           }}
                //         >
                //           Không
                //         </CustomButton>
                //         <CustomButton
                //           variant="primary"
                //           className="cta-button"
                //           onClick={async () => {
                //             await handleActiveReport(
                //               record.id,
                //               record.isActive
                //             );
                //             Modal.destroyAll();
                //           }}
                //         >
                //           Có
                //         </CustomButton>
                //       </>
                //     ),
                //   });
                // } else {
                //   // Nếu đang tắt, hỏi chọn loại quy trình
                //   await handleEditApprovalTemplate(record);
                //   setIsEditMode(true);
                // }
              }}
            />
          )}
        </Space>
      ),
    },
    {
      key: "mode",
      title: "Loại quy trình",
      dataIndex: "mode",
      width: 150,
      defaultVisible: true,
      alwaysVisible: false,
      render: (mode: ApprovalTemplateMode) => {
        return ApprovalTemplateModeTrans[mode]?.label;
        // const count = Array.isArray(approvalTemplateDetails)
        //   ? approvalTemplateDetails.length
        //   : 0;
        // return count <= 2 ? "Đơn giản" : "Phức tạp";
      },
    },
    {
      key: "name",
      title: "Tên mẫu quy trình duyệt",
      dataIndex: "name",
      width: 200,
      defaultVisible: true,
      alwaysVisible: false,
    },
    {
      key: "responders",
      title: "Người duyệt",
      dataIndex: "approvalTemplateDetails",
      width: 250,
      defaultVisible: true,
      alwaysVisible: false,
      render: (approvalTemplateDetails) => {
        // Lấy tất cả memberShip từ các bước duyệt, loại trùng lặp theo id
        const memberships = Array.isArray(approvalTemplateDetails)
          ? approvalTemplateDetails
              .map((step) => step.memberShip)
              .filter((m) => !!m)
              .reduce((acc: any[], curr: any) => {
                if (!acc.find((a) => a.id === curr.id)) acc.push(curr);
                return acc;
              }, [])
          : [];
        return memberships.length ? (
          <div style={{ display: "flex", flexWrap: "wrap", gap: 4 }}>
            {memberships.map((m) => (
              <Tag
                key={m.id}
                color="green"
                style={{ marginBottom: 2, fontSize: 10 }}
              >
                {m.name}
              </Tag>
            ))}
          </div>
        ) : (
          "-"
        );
      },
    },
    {
      key: "followMemberShips",
      title: "Người theo dõi",
      dataIndex: "followMemberShips",
      width: 250,
      defaultVisible: true,
      alwaysVisible: false,
      render: (followMemberShips) =>
        Array.isArray(followMemberShips) && followMemberShips.length ? (
          <div style={{ display: "flex", flexWrap: "wrap", gap: 4 }}>
            {followMemberShips.map((s) => (
              <Tag
                key={s.id}
                color="blue"
                style={{ marginBottom: 2, fontSize: 10 }}
              >
                {s.name}
              </Tag>
            ))}
          </div>
        ) : (
          "-"
        ),
    },
    {
      key: "responderMemberShips",
      title: "Người phản hồi",
      dataIndex: "responderMemberShips",
      width: 250,
      defaultVisible: true,
      alwaysVisible: false,
      render: (responderMemberShips) =>
        Array.isArray(responderMemberShips) && responderMemberShips.length ? (
          <div style={{ display: "flex", flexWrap: "wrap", gap: 4 }}>
            {responderMemberShips.map((s) => (
              <Tag
                key={s.id}
                color="orange"
                style={{ marginBottom: 2, fontSize: 10 }}
              >
                {s.name}
              </Tag>
            ))}
          </div>
        ) : (
          "-"
        ),
    },

    // {
    //   key: "description",
    //   title: "Mô tả",
    //   dataIndex: "description",
    //   width: 250,
    //   defaultVisible: true,
    //   alwaysVisible: false,
    // },
    {
      key: "actions",
      title: "Xử lý",
      width: 100,
      align: "center",
      fixed: "right",
      defaultVisible: true,
      alwaysVisible: true,
      render: (_, record) => (
        <Space size="small">
          {haveEditPermission && (
            <EditButton
              onClick={async (e) => {
                e.stopPropagation();
                await handleEditApprovalTemplate(record);
                setIsEditMode(true);
              }}
            />
          )}
        </Space>
      ),
    },
  ];

  return (
    <div>
      {/* Nút trên cùng */}
      {!hidePageTitle && (
        <PageTitle
          title={title}
          breadcrumbs={["Báo cáo", title]}
          // extra={
          //   haveAddPermission && (
          //     <CustomButton
          //       size="small"
          //       showPlusIcon
          //       onClick={handleCreateApprovalTemplate}
          //     >
          //       Tạo quy trình duyệt
          //     </CustomButton>
          //   )
          // }
        />
      )}

      <div className={hidePageTitle ? "" : "app-container"}>
        <Card>
          <div className="pb-[16px]">
            <div className="flex justify-between items-end mb-4">
              {/* Filter section */}
              <div className="flex gap-4">
                <div className="flex gap-[16px] items-center w-[300px]">
                  <CustomInput
                    tooltipContent={"Tìm theo tên quy trình duyệt"}
                    placeholder="Tìm kiếm"
                    label="Tìm kiếm"
                    onPressEnter={() => {
                      query.page = 1;
                      setQuery({ ...query });
                      fetchData();
                    }}
                    value={query.search}
                    onChange={(value) => {
                      query.search = value;
                      setQuery({ ...query });

                      if (!value) {
                        fetchData();
                      }
                    }}
                    allowClear
                  />
                </div>

                <div>
                  <QueryLabel>Loại quy trình</QueryLabel>
                  <Select
                    placeholder="Chọn loại quy trình"
                    allowClear
                    style={{ width: 200 }}
                    options={[
                      {
                        value: "",
                        label: "Tất cả",
                      },
                      ...ApprovalTemplateTypeOptions.map((opt) => ({
                        value: opt.value,
                        label: opt.label,
                      })),
                    ]}
                    value={query.type ?? ""}
                    onChange={(value) => {
                      query.type = value || undefined;
                      setQuery({ ...query });
                    }}
                  />
                </div>

                {/* Thêm div wrapper cho buttons với style align */}
                <div className="flex flex-col justify-end">
                  <div className="flex gap-2">
                    <CustomButton
                      onClick={() => {
                        if (!query.drawCategoryId) delete query.drawCategoryId;
                        if (query.isActive === undefined) delete query.isActive;
                        query.page = 1;
                        query.projectId = appStore.currentProject?.id;
                        setQuery({ ...query });
                        fetchData();
                      }}
                    >
                      Áp dụng
                    </CustomButton>

                    {!isEmptyQuery && (
                      <CustomButton
                        variant="outline"
                        onClick={() => {
                          delete query.drawCategoryId;
                          delete query.isActive;
                          delete query.search;
                          delete query.queryObject;
                          delete query.type;
                          query.page = 1;
                          query.projectId = appStore.currentProject?.id;
                          setQuery({ ...query });
                          fetchData();
                        }}
                      >
                        Bỏ lọc
                      </CustomButton>
                    )}
                  </div>
                </div>
              </div>

              {/* Nút tạo hạng mục chỉ hiện khi !hidePageTitle */}
              {/* {hidePageTitle && haveAddPermission && (
                // <CustomButton
                //   size="small"
                //   onClick={handleCreateApprovalTemplate}
                //   className="whitespace-nowrap"
                // >
                //   + Tạo quy trình duyệt
                // </CustomButton>
              )} */}
            </div>
            <Spin spinning={loading}>
              <CustomizableTable
                columns={filterActionColumnIfNoPermission(columns, [
                  haveEditPermission,
                  haveDeletePermission,
                ])}
                dataSource={approvalTemplates}
                rowKey="id"
                // loading={loading}
                pagination={false}
                scroll={{ x: 1200 }}
                // onChange={handleTableChange}
                bordered
                displayOptions
                onRowClick={handleRowClick}
              />
            </Spin>
          </div>

          <Pagination
            currentPage={query.page}
            total={total}
            defaultPageSize={query.limit}
            onChange={({ limit, page }) => {
              query.page = page;
              query.limit = limit;
              query.projectId = appStore.currentProject?.id;
              setQuery({ ...query });
              setPage(page);
              setLimit(limit);
              fetchData();
            }}
          />
        </Card>
      </div>

      {/* Modal tạo mới quy trình duyệt */}
      {/* <Modal
        open={openApprovalModal}
        onCancel={() => {
          setOpenApprovalModal(false);
          setPendingActiveRecord(null);
        }}
        footer={null}
        width={800}
        title="Tạo quy trình duyệt"
        destroyOnClose
      >
        <div style={{ marginBottom: 16 }}>
          <Radio.Group
            value={newTemplateType}
            onChange={(e) => setNewTemplateType(e.target.value)}
          >
            <Radio value="simple">Đơn giản</Radio>
            <Radio value="complex">Phức tạp</Radio>
          </Radio.Group>
        </div>
        <ApprovalStepsCard
          steps={approvalSteps}
          loading={loadingApprove}
          onSelectStep={setApprovalSteps}
          onRemove={setRemoveApprovalList}
          editable={false}
          processMode={newTemplateType}
        />
      </Modal> */}

      {/* Modal chỉnh sửa quy trình duyệt */}
      <EditApprovalTemplateModal
        open={editModalOpen}
        loading={loadingApprove}
        editingRecord={editingRecord}
        // approvalSteps={approvalSteps}
        // setApprovalSteps={setApprovalSteps}
        setEditModalOpen={setEditModalOpen}
        // setEditingName={setEditingName}
        // setEditingDescription={setEditingDescription}
        // setEditingType={setEditingType}
        setRemoveApprovalList={setRemoveApprovalList}
        handleUpdateApprovalProcess={handleUpdateApprovalProcess}
        onChangeEditMode={(value) => {
          setIsEditMode(value);
        }}
        isEditMode={isEditMode}
        followers={editingRecord?.followStaffs || []}
        responders={editingRecord?.responders || []}
        templateType={editingRecord?.type}
        processMode={newTemplateType}
      />
    </div>
  );
}

export default observer(ApproveProcessPage);
