// components/FollowerSelector/FollowerSelector.tsx

import React, { useState, useEffect } from "react";
import { Avatar, Card, Modal, Space, Tag, Input } from "antd";
import CustomButton from "components/Button/CustomButton";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import { Staff } from "types/staff";
import { WorkStatusTrans } from "types/workStatus";
import { $url } from "utils/url";
import { Pagination } from "components/Pagination";
import "./Follower.scss";
import { uniqBy } from "lodash";
import { useMemberShip } from "hooks/useMemberShip";
import { appStore } from "store/appStore";
import { MemberShip } from "types/memberShip";

interface FollowerSelectorProps {
  followers: MemberShip[];
  setFollowers: (memberships: MemberShip[]) => void;
  readonly?: boolean;
  headerTitle?: string;
}

export const FollowerSelector: React.FC<FollowerSelectorProps> = ({
  followers,
  setFollowers,
  readonly = false,
  headerTitle = "Người theo dõi",
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const [selectedMembers, setSelectedMembers] = useState<MemberShip[]>([]);
  const [search, setSearch] = useState("");
  const { fetchData, memberShips, query, setQuery, total } = useMemberShip({
    initQuery: { limit: 10, page: 1, projectId: appStore.currentProject?.id },
  });

  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys: React.Key[], selectedRows: MemberShip[]) => {
      setSelectedRowKeys(selectedKeys as number[]);
      setSelectedMembers(selectedRows);
    },
  };

  const columns: CustomizableColumn<MemberShip>[] = [
    {
      title: "Mã NV",
      dataIndex: "code",
      key: "code",
      render: (_, record) => (
        <div className="text-[#1677ff] cursor-pointer">{record.code}</div>
      ),
    },
    {
      title: "Nhân viên",
      dataIndex: "name",
      key: "name",
      render: (_, record) => (
        <div className="flex items-center gap-2">
          <div className="font-medium text-red-500">{record?.name}</div>
        </div>
      ),
    },
    {
      title: "Trạng thái",
      dataIndex: "workStatus",
      key: "staff.workStatus",
      align: "center",
      render: (_, record) => (
        <Tag color={WorkStatusTrans[record?.staff?.workStatus]?.color}>
          {WorkStatusTrans[record?.staff?.workStatus]?.label}
        </Tag>
      ),
    },
  ];

  useEffect(() => {
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [query.projectId, query.page, query.limit, query.search]);

  return (
    <Card
      className="content-card custom-follower-card"
      style={{ position: "sticky", top: "20px", marginTop: "16px" }}
      bodyStyle={{ padding: 0 }}
    >
      <div style={{}}>
        <div className="content-card-header">
          <h2 className="header-text">{headerTitle}</h2>
        </div>

        {!readonly && (
          <div style={{ padding: "0 20px 20px 20px" }}>
            <CustomButton
              variant="outline"
              size="medium"
              block
              className="add-button"
              onClick={() => {
                setIsModalVisible(true);
                fetchData();
                setSelectedRowKeys(followers.map((e) => e.id));
              }}
            >
              Thêm người
            </CustomButton>
          </div>
        )}

        <div style={{ padding: "0 20px" }}>
          {followers.map((user, index) => (
            <div
              key={index}
              style={{
                display: "flex",
                alignItems: "center",
                padding: "12px 0",
                borderBottom:
                  index < followers.length - 1 ? "1px solid #f0f0f0" : "none",
              }}
            >
              <Avatar
                size={40}
                src={
                  user?.staff?.avatar ? $url(user?.staff?.avatar) : undefined
                }
                style={{
                  backgroundColor: "#1890ff",
                  marginRight: "12px",
                  flexShrink: 0,
                }}
              >
                {user.name?.charAt(0)}
              </Avatar>
              <div style={{ flex: 1 }}>
                <div
                  style={{
                    fontSize: "14px",
                    fontWeight: "500",
                    color: "#262626",
                    marginBottom: "2px",
                  }}
                >
                  {user.name}
                </div>
                <div style={{ fontSize: "12px", color: "#8c8c8c" }}>
                  {user.code} | {user.jobTitle?.name}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <Modal
        title="Chọn người theo dõi"
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        onOk={() => {
          setFollowers(selectedMembers);
          setIsModalVisible(false);
        }}
        width={1000}
        footer={
          <Space>
            <CustomButton
              variant="outline"
              onClick={() => setIsModalVisible(false)}
            >
              Đóng
            </CustomButton>
            <CustomButton
              className="cta-button"
              style={{
                borderRadius: 8,
                height: 36,
                minWidth: 80,
                fontSize: 14,
              }}
              onClick={() => {
                setFollowers(selectedMembers);
                setIsModalVisible(false);
              }}
            >
              Chọn
            </CustomButton>
          </Space>
        }
      >
        <div className="flex gap-[8px] mb-4">
          <Input
            placeholder="Tìm kiếm theo tên hoặc mã nhân viên"
            allowClear
            style={{ width: 300 }}
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            onPressEnter={() => {
              setQuery({
                ...query,
                search,
                page: 1,
                projectId: appStore.currentProject?.id,
              });
            }}
            onClear={() => {
              setSearch("");
              setQuery({
                ...query,
                search: "",
                page: 1,
                limit: 10,
                projectId: appStore.currentProject?.id,
              });
            }}
          />
          <CustomButton
            onClick={() => {
              setQuery({
                ...query,
                search,
                page: 1,
                projectId: appStore.currentProject?.id,
              });
            }}
          >
            Áp dụng
          </CustomButton>
          {!!search && (
            <CustomButton
              variant="outline"
              onClick={() => {
                setSearch("");
                setQuery({
                  ...query,
                  search: "",
                  page: 1,
                  limit: 10,
                  projectId: appStore.currentProject?.id,
                });
              }}
            >
              Bỏ lọc
            </CustomButton>
          )}
        </div>
        <CustomizableTable
          columns={columns}
          dataSource={memberShips.filter((e) => e.id !== 0)}
          rowKey="id"
          rowSelection={rowSelection}
          pagination={false}
          scroll={{ x: 1000 }}
          bordered
          displayOptions
          tableId="staff-modal-select"
          autoAdjustColumnWidth={true}
        />
        <Pagination
          currentPage={query.page}
          defaultPageSize={query.limit}
          total={total}
          onChange={({ limit, page }) => {
            setQuery({
              ...query,
              limit,
              page,
              projectId: appStore.currentProject?.id,
              search,
            });
            fetchData();
          }}
        />
      </Modal>
    </Card>
  );
};
