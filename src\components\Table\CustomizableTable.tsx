import React, { useState, useEffect } from "react";
import { Table, Button, Tooltip } from "antd";
import { ColumnType } from "antd/es/table";
import { TableRowSelection } from "antd/es/table/interface";
import { ExpandableConfig, GetComponentProps } from "rc-table/lib/interface";
import { useTheme } from "context/ThemeContext";
import { TableProps } from "antd/lib";
import { Resizable } from "react-resizable";
import type { ResizeCallbackData } from "react-resizable";
import "./CustomizableTable.scss";
import ColumnVisibilityModal from "./ColumnVisibilityModal";
import SettingIcon from "assets/svgs/SettingIcon";
import { ReactComponent as EmptyIcon } from "assets/svgs/empty.svg";

export interface CustomizableColumn<T> extends ColumnType<T> {
  key: string;
  title: string | React.ReactNode;
  dataIndex?: string | string[];
  visible?: boolean;
  defaultVisible?: boolean;
  alwaysVisible?: boolean;
  showColumn?: boolean;
  indexColumn?: number;
}

export interface CustomizableTableProps<T> {
  columns?: CustomizableColumn<T>[];
  dataSource?: T[];
  rowKey?: string;
  title?: string;
  loading?: boolean;
  pagination?: any;
  onChange?: TableProps<any>["onChange"];
  onRow?: GetComponentProps<T>;
  scroll?: { x?: number | string; y?: number | string };
  rowSelection?: TableRowSelection<T>;
  size?: "small" | "middle" | "large";
  bordered?: boolean;
  className?: string;
  displayOptions?: boolean;
  tableId?: string;
  autoAdjustColumnWidth?: boolean;
  expandable?: ExpandableConfig<T>;
  showHeader?: boolean;
  onRowClick?: (record: T) => void;
}

const ResizableTitle: React.FC<
  React.HTMLAttributes<any> & {
    onResize: (e: React.SyntheticEvent, data: ResizeCallbackData) => void;
    width: number;
    children?: React.ReactNode;
  }
> = ({ onResize, width, children, ...restProps }) => {
  const titleRef = React.useRef<HTMLDivElement>(null);
  const [minWidth, setMinWidth] = useState(0);

  useEffect(() => {
    if (titleRef.current) {
      const width = titleRef.current.offsetWidth;
      setMinWidth(width + 16);
    }
  }, [children]);

  if (!width) return <th {...restProps}>{children}</th>;

  return (
    <Resizable
      width={width}
      height={0}
      handle={
        <span
          className="react-resizable-handle"
          onClick={(e) => e.stopPropagation()}
        />
      }
      onResize={onResize}
      draggableOpts={{ enableUserSelectHack: false }}
      minConstraints={[minWidth, 0]}
    >
      <th {...restProps}>
        <div
          ref={titleRef}
          style={{ display: "inline-block", whiteSpace: "nowrap" }}
        >
          {children}
        </div>
      </th>
    </Resizable>
  );
};

function CustomizableTable<T extends object>({
  columns,
  dataSource,
  rowKey,
  title,
  loading,
  pagination,
  onChange,
  onRow,
  scroll,
  rowSelection,
  size = "middle",
  bordered = false,
  className = "",
  displayOptions = false,
  tableId,
  autoAdjustColumnWidth = false,
  expandable,
  showHeader = true,
  onRowClick,
}: CustomizableTableProps<T>) {
  const [visibleColumns, setVisibleColumns] = useState<CustomizableColumn<T>[]>(
    []
  );
  const [isModalVisible, setIsModalVisible] = useState(false);
  const { darkMode } = useTheme();

  const tableClassName = `${darkMode ? "dark" : ""} data-table`;

  const getStorageKey = () =>
    `customizable-table-${tableId || title || "default"}-columns`;

  const loadColumnVisibility = (): Record<string, boolean> => {
    try {
      const stored = localStorage.getItem(getStorageKey());
      return stored ? JSON.parse(stored) : {};
    } catch {
      return {};
    }
  };

  const saveColumnVisibility = (columns: CustomizableColumn<T>[]) => {
    try {
      const visibilityMap = columns.reduce((acc, col) => {
        if (!col.alwaysVisible) acc[col.key] = col.visible || false;
        return acc;
      }, {} as Record<string, boolean>);
      localStorage.setItem(getStorageKey(), JSON.stringify(visibilityMap));
    } catch {}
  };

  const clearColumnVisibility = () => {
    try {
      localStorage.removeItem(getStorageKey());
    } catch {}
  };

  useEffect(() => {
    const savedVisibility = loadColumnVisibility();
    const initialVisibleColumns =
      columns
        ?.sort((a, b) => (a.indexColumn ?? 0) - (b.indexColumn ?? 0))
        .filter((e) => e.showColumn ?? true)
        .map((col) => ({
          ...col,
          visible: col.alwaysVisible
            ? true
            : savedVisibility[col.key] ??
              col.visible ??
              col.defaultVisible ??
              true,
        })) || [];
    setVisibleColumns(initialVisibleColumns);
  }, [columns]);

  const toggleColumnVisibility = (key: string, visible: boolean) => {
    setVisibleColumns((prev) =>
      prev.map((col) => (col.key === key ? { ...col, visible } : col))
    );
  };

  const handleApplyChanges = (
    updatedColumns: CustomizableColumn<T>[],
    isReset = false
  ) => {
    setVisibleColumns(updatedColumns);
    if (isReset) clearColumnVisibility();
    else saveColumnVisibility(updatedColumns);
    setIsModalVisible(false);
  };

  const handleResize =
    (index: number) =>
    (_: React.SyntheticEvent, { size }: ResizeCallbackData) => {
      setVisibleColumns((prev) => {
        const nextCols = [...prev];
        nextCols[index] = {
          ...nextCols[index],
          width: size.width,
        };
        return [...nextCols];
      });
    };

  const filteredColumns = visibleColumns.filter((col) => col.visible);

  const adjustedColumns = filteredColumns.map((col) => {
    if (!autoAdjustColumnWidth) return col;
    if (col.key === "actions" || col.fixed === "right")
      return { ...col, width: 100 };
    if (col.width && typeof col.width === "number" && col.width >= 50)
      return col;
    return { ...col, width: undefined };
  });

  const mergedColumns = adjustedColumns.map((col, index) => ({
    ...col,
    onHeaderCell: (column: CustomizableColumn<T>) => ({
      width: column.width,
      onResize: handleResize(index),
    }),
  }));

  const handleRow: GetComponentProps<T> = (record: T) => {
    if (onRow) {
      return onRow(record);
    }
    return {
      onDoubleClick: () => {
        onRowClick?.(record);
      },
    };
  };

  return (
    <div className={`customizable-table ${className}`}>
      {(title || true) && (
        <div className="customizable-table-header">
          {title && <h3 className="customizable-table-title">{title}</h3>}
          {displayOptions && (
            <Tooltip title="Tùy chỉnh hiển thị">
              <Button
                icon={<SettingIcon />}
                onClick={() => setIsModalVisible(true)}
                className="customize-button"
                type="text"
              >
                <span
                  className={
                    darkMode
                      ? "text-[var(--color-neutral-n6)]"
                      : "text-[var(color-primary)]"
                  }
                >
                  Tùy chỉnh hiển thị
                </span>
              </Button>
            </Tooltip>
          )}
        </div>
      )}

      <Table<T>
        //@ts-ignore
        columns={mergedColumns}
        components={{ header: { cell: ResizableTitle } }}
        dataSource={dataSource}
        rowKey={rowKey}
        loading={loading}
        pagination={pagination}
        onChange={onChange}
        scroll={scroll}
        rowSelection={rowSelection}
        size={size}
        bordered={bordered}
        className={tableClassName}
        onRow={handleRow}
        showHeader={showHeader}
        rowClassName={"table-row-color"}
        locale={{
          emptyText: () => (
            <div className="py-[50px]">
              <EmptyIcon
                className="table-empty-icon"
                style={{ width: 50, height: 50 }}
              />
              <div
                className="text-[13px] font-bold"
                style={{ color: "var(--color-neutral-n4)" }}
              >
                Chưa có dữ liệu
              </div>
            </div>
          ),
        }}
        expandable={expandable}
      />

      <ColumnVisibilityModal
        visible={isModalVisible}
        columns={visibleColumns}
        onCancel={() => setIsModalVisible(false)}
        onApply={handleApplyChanges}
        toggleColumnVisibility={toggleColumnVisibility}
      />
    </div>
  );
}

export default CustomizableTable;
