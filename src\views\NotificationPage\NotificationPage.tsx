import { useState, useEffect } from "react";
import { Spin, Badge, DatePicker, Select } from "antd";
import CustomInput from "components/Input/CustomInput";
import { Pagination } from "components/Pagination";
import type { Dayjs } from "dayjs";
import { checkRole } from "utils/auth";
import { PermissionNames } from "types/PermissionNames";
import { permissionStore } from "store/permissionStore";
import { observer } from "mobx-react";
import { useNavigate } from "react-router-dom";
import { Project } from "types/project";
import { appStore } from "store/appStore";
import { getTitle } from "utils";
import { useNotification } from "hooks/useNotification";
import { userStore } from "store/userStore";
import CardNotification from "./components/CardNotification";
import { notificationApi } from "api/notification.api";
import CustomButton from "components/Button/CustomButton";
import CustomSelect from "components/Input/CustomSelect";
import { settings } from "settings";

interface NotificationQuery {
  page: number;
  limit: number;
  search?: string;
  queryObject?: string;
  staffId?: number;
  fromAt?: number;
  toAt?: number;
  isRead?: boolean;
  status?: string;
}

const NOTIFICATIONS_TYPES = [
  { value: "", label: "Tất cả thông báo" },
  { value: "notRead", label: "Chưa đọc" },
  { value: "readed", label: "Đã đọc" },
];
function NotificationPage({ title }: { title: string }) {
  const haveAddPermission = checkRole(
    PermissionNames.projectAdd,
    permissionStore.permissions
  );
  const haveViewAllPermission = checkRole(
    PermissionNames.projectViewAll,
    permissionStore.permissions
  );
  const [searchValue, setSearchValue] = useState("");
  const [startDate, setStartDate] = useState<Dayjs | undefined>(undefined);
  const [endDate, setEndDate] = useState<Dayjs | undefined>(undefined);
  const [showCurrentStatus, setShowCurrentStatus] = useState(false);
  const [notificationStatus, setNotificationStatus] = useState("");
  const navigate = useNavigate();
  const [unreadNoti, setUnreadNoti] = useState(10);
  // const { projects, total, fetchData, loading, setQuery, query, isEmptyQuery } =
  //   useProject({
  //     initQuery: {
  //       page: 1,
  //       limit: 10,
  //       isAdmin: haveViewAllPermission ? true : undefined,
  //       search: "",
  //     },
  //   });

  const {
    notifications,
    totalNotification,
    fetchNotification,
    loadingNotification,
    setQueryNotification,
    queryNotification,
    isEmptyQuery,
  } = useNotification({
    initQuery: {
      limit: 100,
      page: 1,
      search: "",
      staffId: userStore.info?.id,
    } as NotificationQuery,
  });

  // Watch for query changes and refetch data
  useEffect(() => {
    fetchNotification();
  }, [queryNotification]);

  useEffect(() => {
    setUnreadNoti(notifications.filter((it) => !it.isRead).length);
  }, [notifications]);

  const handleClickReadNotification = async (
    notificationId: number,
    url: string
  ) => {
    try {
      const res = await notificationApi.handleReadNotification(notificationId);
      if (res.status === 200) {
        fetchNotification();
      }
      if (url) {
        window.open(url, "_blank");
      }
    } catch (error) {
      console.log("~ handleClickReadNotification ~ error:", error);
    }
  };

  useEffect(() => {
    document.title = getTitle(title);
  }, []);





  const handleSearch = (value: string) => {
    setSearchValue(value);
    // Only trigger search when value is empty (cleared)
    if (value === "") {
      setQueryNotification({ ...queryNotification, search: "", page: 1 });
    }
  };

  const handleSearchOnEnter = (value: string) => {
    setQueryNotification({ ...queryNotification, search: value, page: 1 });
  };


  const handleStartDateChange = (
    date: Dayjs | null,
    dateString: string | string[]
  ) => {
    setStartDate(date || undefined);
  };

  const handleEndDateChange = (
    date: Dayjs | null,
    dateString: string | string[]
  ) => {
    setEndDate(date || undefined);
  };

  const handleApplyFilter = () => {
    const newQuery: NotificationQuery = {
      ...queryNotification,
      search: searchValue,
      page: 1,
    };

    // Add date filters
    if (startDate) {
      newQuery.fromAt = startDate.startOf("day").unix();
    } else {
      delete newQuery.fromAt;
    }

    if (endDate) {
      newQuery.toAt = endDate.endOf("day").unix();
    } else {
      delete newQuery.toAt;
    }



    setQueryNotification(newQuery);
  };

  const handleClearFilter = () => {
    setSearchValue("");
    setStartDate(undefined);
    setEndDate(undefined);
    setNotificationStatus("");

    setShowCurrentStatus(false);

    const newQuery: NotificationQuery = {
      page: 1,
      limit: queryNotification.limit,
      search: "",
      staffId: userStore.info?.id,
    };

    // Remove all filters
    delete newQuery.fromAt;
    delete newQuery.toAt;
    delete newQuery.isRead;

    setQueryNotification(newQuery);
  };

  const pagination = {
    current: queryNotification.page,
    pageSize: queryNotification.limit,
    total: totalNotification,
    showSizeChanger: true,
  };

  const handleNavigateToDetail = async (project: Project) => {
    try {
      appStore.setCurrentProject(project);
      // Navigate sang ProjectDetailPage
      if (appStore.lastUrl) {
        navigate(appStore.lastUrl);
        appStore.lastUrl = "";
      } else {
        navigate(`/project-detail/${project.id}`);
      }
    } catch (error) {
      console.error("Error navigating to project detail:", error);
    }
  };

  return (
    <div>
      {/* Main Card containing search section and project list */}
      <div className="card-box">
        {/* Search Section */}
        <div className="mb-6">
          {/* Mobile: Search first row */}
          <div className="flex flex-col md:flex-row md:justify-between md:items-center mb-4 gap-4">
            <div className="font-bold text-[24px] flex items-center gap-2">
              Thông báo
              <Badge count={unreadNoti}></Badge>
            </div>

            <div className="w-full md:w-[500px]">
              <CustomInput
                tooltipContent={"Tìm kiếm theo tên thông báo"}
                placeholder="Tìm kiếm"
                value={searchValue}
                onChange={handleSearch}
                onPressEnter={handleSearchOnEnter}
                allowClear
                shouldPressEnter
              />
            </div>
          </div>

          {/* Filter Section */}
          <div className="flex flex-wrap gap-[16px] items-end pb-[12px] justify-between">
            <div className="flex flex-wrap gap-[16px] items-end max-w-full">
              <div className="w-[200px]">
                {/* <QueryLabel>Ngày bắt đầu</QueryLabel> */}
                <DatePicker
                  className="w-full"
                  placeholder="Từ ngày"
                  value={startDate}
                  onChange={handleStartDateChange}
                  format={settings.dateFormat}
                />
              </div>
              <div className="w-[200px]">
                {/* <QueryLabel>Ngày kết thúc</QueryLabel> */}
                <DatePicker
                  className="w-full"
                  placeholder="Đến ngày"
                  value={endDate}
                  onChange={handleEndDateChange}
                  format={settings.dateFormat}
                />
              </div>
              <div className="w-[200px]">
                {/* <QueryLabel>Tình trạng</QueryLabel> */}
                <CustomSelect
                  value={notificationStatus}
                  onChange={(value) => {
                    setNotificationStatus(value);

                    let newQuery: NotificationQuery = {
                      ...queryNotification,
                      page: 1,
                    };

                    if (value === "readed") {
                      newQuery = {
                        ...newQuery,
                        isRead: true,
                      };
                    } else if (value === "notRead") {
                      newQuery = {
                        ...newQuery,
                        isRead: false,
                      };
                    } else {
                      const { isRead, ...rest } = newQuery;
                      newQuery = rest;
                    }

                    setQueryNotification(newQuery);
                    fetchNotification();
                  }}
                  options={NOTIFICATIONS_TYPES}
                  className="!w-[200px]"
                  placeholder="Chọn trạng thái"
                />
              </div>

              <CustomButton onClick={handleApplyFilter}>Áp dụng</CustomButton>
              {!isEmptyQuery && (
              <CustomButton variant="outline" onClick={handleClearFilter}>
                  Bỏ lọc
                </CustomButton>
              )}
            </div>
          </div>
        </div>

        {/* Notification Cards */}
        <Spin spinning={loadingNotification}>
          <div className="space-y-6">
            {notifications.map((notification, index) => (
              <div key={notification.id}>
                <CardNotification
                  notification={notification}
                  onClick={handleClickReadNotification}
                />
              </div>
            ))}
          </div>
        </Spin>

        <Pagination
          currentPage={queryNotification.page}
          defaultPageSize={queryNotification.limit}
          total={totalNotification}
          onChange={({ limit, page }) => {
            queryNotification.page = page;
            queryNotification.limit = limit;
            setQueryNotification({ ...queryNotification });
            fetchNotification();
          }}
        />
      </div>
    </div>
  );
}

export default observer(NotificationPage);
