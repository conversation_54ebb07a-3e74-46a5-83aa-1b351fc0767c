import { <PERSON><PERSON>, Divider } from "antd";
import CardImage from "components/CardImage/CardImage";
import CardView from "components/CardView/CardView";
import Comment from "components/Comment/Comment";
import PageTitle from "components/PageTitle/PageTitle";
import {
  ColumnDirective,
  ColumnsDirective,
  GanttComponent,
  Inject,
  Toolbar,
} from "@syncfusion/ej2-react-gantt";
// import {ZoomEventArgs} from "@syncfusion/ej2-react-gantt"
const ComponentsPage = () => {
  const data = [
    {
      TaskID: 1,
      TaskName: "Project Initiation",
      StartDate: new Date("04/02/2019"),
      EndDate: new Date("04/21/2019"),
      subtasks: [
        {
          TaskID: 2,
          TaskName: "Identify Site location",
          StartDate: new Date("04/02/2019"),
          Duration: 4,
          Progress: 50,
        },
        {
          TaskID: 3,
          TaskName: "Perform Soil test",
          StartDate: new Date("04/02/2019"),
          Duration: 4,
          Progress: 50,
        },
        {
          TaskID: 4,
          TaskName: "Soil test approval",
          StartDate: new Date("04/02/2019"),
          Duration: 4,
          Progress: 50,
        },
      ],
    },
    {
      TaskID: 5,
      TaskName: "Project Estimation",
      StartDate: new Date("04/02/2019"),
      EndDate: new Date("04/21/2019"),
      subtasks: [
        {
          TaskID: 6,
          TaskName: "Develop floor plan for estimation",
          StartDate: new Date("04/04/2019"),
          Duration: 3,
          Progress: 50,
        },
        {
          TaskID: 7,
          TaskName: "List materials",
          StartDate: new Date("04/04/2019"),
          Duration: 3,
          Progress: 50,
        },
        {
          TaskID: 8,
          TaskName: "Estimation approval",
          StartDate: new Date("04/04/2019"),
          Duration: 3,
          Progress: 50,
        },
      ],
    },
  ];
  const taskSettings = {
    id: "TaskID",
    name: "TaskName",
    startDate: "StartDate",
    endDate: "EndDate",
    duration: "Duration",
    progress: "Progress",
    child: "subtasks",
  };

  const handleToolbarClick = (args: any) => {
    if (args.item.id === "ZoomIn") {
      // Handle ZoomIn functionality
    } else if (args.item.id === "ZoomOut") {
      // Handle ZoomOut functionality
    }
  };

  return (
    <div>
      <div>Gantt</div>
      <GanttComponent
        toolbarClick={handleToolbarClick}
        toolbar={[
          "ExpandAll",
          "CollapseAll",
          "Add",
          "Edit",
          "ZoomIn",
          "ZoomOut",
          "ZoomToFit",
          "Undo",
          "Redo",
          "CriticalPath",
        ]}
        dataSource={data}
        treeColumnIndex={1}
        taskFields={taskSettings}
        splitterSettings={{ position: "35%" }}
        labelSettings={{ leftLabel: "TaskName" }}
        height={500}
      >
        <ColumnsDirective>
          <ColumnDirective field="TaskID" width="80"></ColumnDirective>
          <ColumnDirective field="TaskName" width="250"></ColumnDirective>
          <ColumnDirective field="StartDate"></ColumnDirective>
          <ColumnDirective field="EndDate"></ColumnDirective>
          <ColumnDirective field="Duration"></ColumnDirective>
          <ColumnDirective field="Progress"></ColumnDirective>
        </ColumnsDirective>
        <Inject services={[Toolbar]} />
      </GanttComponent>
      <div>Card View</div>
      {/* <CardView /> */}
      <Divider />
      <PageTitle
        title="Page of content"
        breadcrumbs={["Level 1", "Level 2"]}
        extra={
          <div>
            <Button type="primary">Extra button</Button>
          </div>
        }
      />
      <Divider />
      <div>Card Image</div>
      <div className="w-[300px]">
        <CardImage />
      </div>
      <Divider />
      <div>Comment</div>
      <Comment />
      <Divider />
      <Divider />
      <Divider />
      <Divider />
      <Divider />
      <Divider />
      <Divider />
      <Divider />
      <Divider />
      <Divider />
      <Divider />
      <Divider />
    </div>
  );
};

export default ComponentsPage;
