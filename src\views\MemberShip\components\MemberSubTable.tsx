import React, { useEffect, useMemo } from "react";
import { Button, Space, Tooltip, Table, message } from "antd";
import { useNavigate, useLocation } from "react-router-dom";
import { useTheme } from "context/ThemeContext";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import PencilIcon from "assets/svgs/PencilIcon";
import { PermissionNames } from "types/PermissionNames";
import { checkRoles, filterActionColumnIfNoPermission } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { observer } from "mobx-react";
import ActiveStatusTag from "components/ActiveStatus/ActiveStatusTag";
import LockButton from "components/Button/LockButton";
import EditButton from "components/Button/EditButton";
import { ReactComponent as Copy } from "assets/svgs/copy.svg";
import { ArrowDownIcon } from "assets/svgs/ArrowDownIcon";

interface UserData {
  id: string;
  code: string; // Add code field
  companyCode: string;
  fullName: string;
  position: string;
  role: string;
  email: string;
  status: string;
  isActive: boolean;
  company?: string;
  phone: string;
  // Additional metadata for copy functionality
  memberShipCategoryId?: number;
  memberShipCategoryName?: string;
  jobTitleId?: number;
  jobTitleName?: string;
  companyId?: number;
  companyName?: string;
  roleId?: number;
  roleName?: string;
  staffId?: number;
  staffCode?: string;
  staffName?: string;
}

interface MemberSubTableProps {
  users: UserData[];
  projectId?: number;
  onToggleActive?: (id: number, isActive: boolean) => void;
  haveAddPermission?: boolean;
}

export const MemberSubTable: React.FC<MemberSubTableProps> = observer(
  ({ users, projectId, onToggleActive, haveAddPermission }) => {
    const { haveBlockPermission, haveEditPermission } = checkRoles(
      {
        edit: PermissionNames.projectPhoneBookEdit,
        block: PermissionNames.projectPhoneBookBlock,
      },
      permissionStore.permissions
    );

    const { darkMode } = useTheme();
    const navigate = useNavigate();
    const location = useLocation();

    const handleViewUser = (userId: string) => {
      const numericId = parseInt(userId);
      if (isNaN(numericId)) {
        console.error("Invalid ID:", userId);
        message.error("ID không hợp lệ");
        return;
      }

      navigate(
        `/report/${PermissionNames.projectPhoneBookEdit.replace(
          ":id",
          numericId.toString()
        )}`
      );
    };

    const handleEditUser = (userId: string, projectId?: number) => {
      const numericId = parseInt(userId);
      if (isNaN(numericId)) {
        console.error("Invalid ID:", userId);
        message.error("ID không hợp lệ");
        return;
      }

      if (projectId) {
        navigate(
          `/report/${PermissionNames.projectPhoneBookEdit.replace(
            ":id",
            numericId.toString()
          )}?update=1&projectId=${projectId}`
        );
      } else {
        navigate(
          `/report/${PermissionNames.projectPhoneBookEdit.replace(
            ":id",
            numericId.toString()
          )}?update=1`
        );
      }
    };

    const userColumns: CustomizableColumn<UserData>[] = [
      {
        key: "id",
        title: "",
        dataIndex: "code", // Display code but keep key as id
        width: 100,
        render: (_, userRecord) => {
          return (
            <div
              className="text-[#1677ff] cursor-pointer"
              onClick={() => handleViewUser(userRecord.id)}
            >
              {userRecord.code} {/* Display code instead of ID */}
            </div>
          );
        },
        defaultVisible: true,
        alwaysVisible: true,
      },
      {
        key: "companyCode",
        title: "",
        dataIndex: "companyCode",
        width: 120,
        defaultVisible: true,
        alwaysVisible: true,
      },
      {
        key: "fullName",
        title: "",
        dataIndex: "fullName",
        width: 174,
        defaultVisible: true,
        alwaysVisible: true,
      },
      {
        key: "company",
        title: "",
        dataIndex: "company",
        width: 175,
        defaultVisible: true,
        alwaysVisible: true,
      },
      {
        key: "role",
        title: "",
        dataIndex: "role",
        width: 100,
        defaultVisible: true,
        alwaysVisible: true,
      },
      {
        key: "email",
        title: "",
        dataIndex: "email",
        width: 200,
        defaultVisible: true,
        alwaysVisible: true,
      },
      {
        key: "phone",
        title: "",
        dataIndex: "phone",
        width: 120,
        defaultVisible: true,
        alwaysVisible: true,
      },
      {
        key: "status",
        title: "",
        dataIndex: "status",
        align: "center",
        width: 150,
        defaultVisible: true,
        alwaysVisible: true,
        render: (_, userRecord) => (
          <div className="flex justify-center">
            <ActiveStatusTag isActive={userRecord.isActive} />
          </div>
        ),
      },
      {
        key: "actions",
        title: "",
        align: "center",
        width: 150,
        defaultVisible: true,
        alwaysVisible: true,
        render: (_, userRecord) => (
          <Space size="small">
            {/* Copy button - placed first like in ProjectItemPage */}
            {haveAddPermission && (
              <Tooltip title="Tạo nhanh từ bản này">
                <Button
                  type="text"
                  icon={<Copy />}
                  onClick={(e) => {
                    e.stopPropagation();
                    // Check if we're in ProjectDetailPage context
                    const isInProjectDetail = location.pathname.includes('/project-detail/');
                    
                    if (projectId) {
                      navigate(
                        `/report/${PermissionNames.projectPhoneBookAdd}?projectId=${projectId}`,
                        {
                          state: {
                            copyData: userRecord,
                            projectId: projectId,
                            fromProjectDetail: isInProjectDetail,
                            fromReportSection: !isInProjectDetail,
                          },
                        }
                      );
                    } else {
                      navigate(`/report/${PermissionNames.projectPhoneBookAdd}`, {
                        state: {
                          copyData: userRecord,
                          fromReportSection: true,
                        },
                      });
                    }
                  }}
                />
              </Tooltip>
            )}

            {haveEditPermission && (
              <EditButton
                onClick={(e) => {
                  e.stopPropagation();
                  handleEditUser(userRecord.id, projectId);
                }}
              />
            )}

            {haveBlockPermission && (
              <LockButton
                isActive={userRecord.isActive}
                onAccept={() => {
                  if (onToggleActive) {
                    onToggleActive(
                      parseInt(userRecord.id),
                      userRecord.isActive
                    );
                  }
                }}
                modalTitle={`${
                  userRecord.isActive ? "Khóa" : "Mở khóa"
                } thành viên: ${userRecord.fullName}`}
                modalContent={
                  <div>
                    Khi {userRecord.isActive ? "khóa" : "mở khóa"} thành viên
                    này, trạng thái của thành viên sẽ được thay đổi.
                    <br />
                    Bạn có chắc chắn muốn{" "}
                    {userRecord.isActive ? "khóa" : "mở khóa"} thành viên này?
                  </div>
                }
              />
            )}
          </Space>
        ),
      },
    ];

    return (
      <Table
        columns={filterActionColumnIfNoPermission(userColumns, [
          haveAddPermission || false,
          haveEditPermission,
          haveBlockPermission,
        ])}
        dataSource={users}
        rowKey="id"
        loading={false}
        pagination={false}
        bordered={false}
        size="small"
        showHeader={false}
        className="role-sub-table"
        onRow={(record) => ({
          onDoubleClick: (e) => {
            handleViewUser(record.id);
          },
        })}
        expandable={{
          expandedRowRender: () => <></>,
          rowExpandable: () => false,
          expandIcon: ({ expanded, onExpand, record }) => (
            <Button
              type="text"
              size="small"
              icon={
                <ArrowDownIcon
                  className={`transition-transform duration-200 ${
                    expanded ? "rotate-0" : "-rotate-90"
                  }`}
                  fill={darkMode ? "#ffffff" : "#6b7280"}
                />
              }
              onClick={(e) => onExpand(record, e)}
              className="!border-0 !shadow-none hover:!bg-gray-100 invisible"
            />
          ),
        }}
      />
    );
  }
);
