import { Col, Form, Input, message, Modal, Row } from "antd";
import { Rule } from "antd/lib/form";
import { materialTypeApi } from "api/materialType";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { MaterialType } from "types/materialType";
import { ModalStatus } from "types/modal";

const rules: Rule[] = [{ required: true }];

export interface MaterialTypeModal {
  handleCreate: () => void;
  handleUpdate: (materialType: MaterialType) => void;
}
interface MaterialTypeModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
}

export const MaterialTypeModal = React.forwardRef(
  ({ onClose, onSubmitOk }: MaterialTypeModalProps, ref) => {
    const [form] = Form.useForm<MaterialType>();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");

    useImperativeHandle<any, MaterialTypeModal>(
      ref,
      () => ({
        handleCreate() {
          form.resetFields();
          setVisible(true);
          setStatus("create");
        },
        handleUpdate(materialType: MaterialType) {
          form.setFieldsValue({ ...materialType });
          setVisible(true);
          setStatus("update");
        },
      }),
      []
    );

    const createData = async () => {
      const valid = await form.validateFields();
      const data = { materialType: form.getFieldsValue() };

      setLoading(true);
      try {
        const res = await materialTypeApi.create(data);
        message.success("Tạo mới thành công");
        onClose();
        setVisible(false);
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const valid = await form.validateFields();
      const data = { materialType: form.getFieldsValue() };
      setLoading(true);
      try {
        const res = await materialTypeApi.update(
          data.materialType.id || 0,
          data
        );
        message.success("Cập nhật thành công");
        onClose();
        onSubmitOk();
        setVisible(false);
      } finally {
        setLoading(false);
      }
    };

    return (
      <Modal
        onCancel={() => {
          onClose?.();
          setVisible(false);
        }}
        visible={visible}
        title={status == "create" ? "Tạo mới loại NVL" : "Cập nhật loại NVL"}
        style={{ top: 20 }}
        width={700}
        confirmLoading={loading}
        onOk={() => {
          status == "create" ? createData() : updateData();
        }}
      >
        <Form layout="vertical" form={form}>
          <Row gutter={16}>
            <Form.Item hidden name="id"></Form.Item>
            <Col span={12}>
              <Form.Item label="Tên Loại NVL" name="name" rules={rules}>
                <Input placeholder="" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Mô tả" name="description">
                <Input placeholder="" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
);
