.customizable-table {
  width: 100%;

  .react-resizable-handle {
    position: absolute;
    right: -5px;
    top: 0;
    bottom: 0;
    width: 10px;
    cursor: col-resize;
    z-index: 1;
  }

  .react-resizable-handle:after {
    content: "";
    display: block;
    position: absolute;
    right: 2px;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: 24px;
    background: #d4d4d4;
    border-radius: 2px;
    opacity: 0.7;
  }

  .ant-table-thead {
    height: 38px;

    .ant-dropdown-trigger {
      color: var(--color-neutral-n0) !important;
    }
  }

  .data-table .ant-table {
    border: none !important;
  }

  .customizable-table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0px;

    .customizable-table-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: var(--color-neutral-n7);
    }

    .ant-btn {
      padding: 0 !important;
    }

    .customize-button {
      display: flex;
      align-items: center;
      color: var(--color-primary);
      font-weight: 700;
      // height: 20px;
      font-size: 13px;
      // line-height: 140%;
      // letter-spacing: 0%;

      &:hover {
        color: var(--color-primary-hover);
      }
    }
  }

  .data-table {
    border-radius: 8px;
    overflow: hidden;
    // Table header styling
    .ant-table-thead > tr > th {
      background-color: var(--color-primary);
      color: var(--color-text-selected);
      font-weight: 600;
      border-bottom: 1px solid var(--color-neutral-n3);
      border-inline-end: none !important;

      &::before {
        content: none !important;
      }
    }

    // Table body row styling - alternating colors
    // .ant-table-tbody > tr:nth-child(odd) {
    //   background-color: var(--color-neutral-n1);
    // }

    .ant-table-tbody > tr:nth-child(even):not(.ant-table-placeholder) {
      background-color: var(--color-neutral-n0);
    }

    // Hover state for table rows - chỉ apply cho rows có data
    .ant-table-tbody > tr:not(.ant-table-placeholder):hover > td {
      background-color: var(--color-neutral-n1) !important;
    }

    // Table cell styling
    .ant-table-tbody > tr > td {
      color: var(--color-neutral-n8);
      border-bottom: 1px solid var(--color-neutral-n2);
      border-top: none !important;
      border-left: none !important;
      border-right: none !important;
    }

    // Empty state styling - chỉ apply cho placeholder row
    .ant-table-tbody > tr.ant-table-placeholder > td {
      background-color: white !important;

      &:hover {
        background-color: white !important;
      }
    }

    // Table border styling
    .ant-table {
      border: 1px solid var(--color-neutral-n3);

      .ant-table-container {
        .ant-table-content {
          colgroup {
            .ant-table-expand-icon-col {
              width: 20px;
            }
          }

          .ant-table-thead {
            .ant-table-cell {
              &.ant-table-row-expand-icon-cell {
                // padding: 4px 0px !important;
                // width: 50px !important;
              }
            }
          }

          .ant-table-tbody {
            .ant-table-expanded-row.ant-table-expanded-row-level-1 {
              .ant-table-row:nth-child(even) {
                background-color: var(--table-even-row-2);

                .ant-table-cell-fix-right {
                  background-color: var(--table-even-row-2);
                }
              }
            }
            .ant-table-row:nth-child(even) {
              background-color: var(--table-even-row);

              .ant-table-cell-fix-right {
                background-color: var(--table-even-row);
              }
            }
            .ant-table-row:nth-child(odd) {
              background-color: var(--table-even-row-2);

              .ant-table-cell-fix-right {
                background-color: var(--table-even-row-2);
              }
            }
            .ant-table-row {
              &:has(.ant-table-row-expand-icon-cell) {
                &:has(:only-child) {
                  // background-color: var(--color-neutral-n2);
                }

                // & > .ant-table-cell {
                //   font-weight: 700;
                // }
              }

              .ant-table-row-expand-icon-cell {
                div {
                  display: flex;
                }
              }
            }
            .ant-table-expanded-row {
              & > .ant-table-cell {
                padding: 0 !important;
              }
            }
          }
        }
      }
    }

    // Selection column styling (if using row selection)
    .ant-table-selection-column {
      .ant-checkbox-wrapper {
        .ant-checkbox {
          border-color: var(--color-neutral-n4);

          &.ant-checkbox-checked {
            background-color: var(--color-primary);
            border-color: var(--color-primary);
          }
        }
      }
    }

    .ant-btn {
      .ant-btn-icon {
        width: 20px;
        aspect-ratio: 1;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}
