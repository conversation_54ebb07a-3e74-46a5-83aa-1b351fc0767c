import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperative<PERSON>andle,
  useMemo,
} from "react";
import { debounce, uniqBy } from "lodash";
import { useUnit } from "hooks/useUnit";
import { Dictionary, DictionaryType } from "types/dictionary";
import { QueryParams2 } from "types/query";
import CustomSelect from "components/Input/CustomSelect";
import { useDictionary } from "hooks/useDictionary";
import { Select, Tooltip, TreeSelect } from "antd";
import { isTypeTreeData } from "utils/common";

type CustomFormItemProps = {
  value?: number;
  initQuery?: QueryParams2;
  disabled?: boolean;
  selectedUnit?: Dictionary[];
  multiple?: boolean;
  onChange?: (value: any) => void;
  initOptionItem?: Dictionary | Dictionary[];
  valueIsOption?: boolean;
  allowClear?: boolean;
  placeholder?: string;
  label?: string;
  addonOptions?: any[];
  showSearch?: boolean;
  className?: string;
  tooltipContent?: string;
};

export interface DictionarySelector {
  refresh(): void;
}

export const DictionarySelector = forwardRef(
  (
    {
      value,
      label,
      onChange,
      initQuery,
      disabled,
      multiple = false,
      selectedUnit,
      initOptionItem,
      valueIsOption,
      allowClear = true,
      placeholder = "",
      addonOptions = [],
      showSearch = true,
      className,
      tooltipContent,
    }: CustomFormItemProps,
    ref
  ) => {
    const { dictionaries, loading, fetchData, query } = useDictionary({
      initQuery: { page: 1, limit: 0, ...initQuery },
    });

    const isSelectTree = useMemo(() => {
      return isTypeTreeData(initQuery?.type);
    }, [initQuery]);

    useImperativeHandle<any, DictionarySelector>(
      ref,
      () => ({
        refresh() {
          fetchData();
        },
      }),
      []
    );

    useEffect(() => {
      fetchData();
    }, [selectedUnit]);

    const debounceSearch = useCallback(
      debounce((keyword) => {
        query.search = keyword;
        fetchData();
      }, 300),
      [query]
    );

    const renderInput = () => {
      return (
        <Select
          value={value}
          onChange={handleChange}
          disabled={disabled}
          options={options}
          mode={multiple ? "multiple" : undefined}
          allowClear={allowClear}
          placeholder={placeholder}
          onSearch={debounceSearch}
          loading={loading}
          showSearch={showSearch}
          filterOption={false}
          className={className}
        />
      );
    };

    const options = useMemo(() => {
      let data = [...dictionaries];
      if (initOptionItem) {
        if ((initOptionItem as Dictionary[])?.length) {
          data = data.concat(initOptionItem);
        } else {
          data.push(initOptionItem as Dictionary);
        }
      }
      // Filter out items with no id to prevent undefined errors
      return uniqBy(
        [...addonOptions, ...data].filter(
          (item) => item && item.id !== undefined
        ),
        (item) => item.id
      ).map((item) => ({
        label: item.name || "Không có tên",
        value: item.id,
        item, // lưu nguyên object nếu cần dùng sau
        children: item.children || undefined,
      }));
    }, [dictionaries, initOptionItem, addonOptions]);

    // transform options to treeData
    function transformToTreeData(items: any[]): any[] {
      return items.map((item) => {
        const node = {
          value: item.value,
          title: item.label,
          item: item.item, // lưu nguyên object nếu cần dùng sau
          children: undefined as any,
        };

        if (item.children?.length) {
          node.children = transformToTreeData(
            item.children.map((child: any) => ({
              value: child.id,
              label: child.name,
              item: child,
              children: child.children,
            }))
          );
        }

        return node;
      });
    }

    const handleChange = (v: any, option: any) => {
      if (valueIsOption) {
        if (v == undefined) {
          onChange?.(null);
        } else if (option instanceof Array) {
          onChange?.(option.map((opt) => opt.item));
        } else {
          onChange?.(option.item);
        }
      } else {
        onChange?.(v);
      }
    };

    if (isSelectTree) {
      return (
        <TreeSelect
          value={value}
          onChange={handleChange}
          disabled={disabled}
          treeData={transformToTreeData(options)}
          multiple={multiple}
          allowClear={allowClear}
          placeholder={placeholder}
          onSearch={debounceSearch}
          loading={loading}
          showSearch={showSearch}
          className={className}
          treeNodeFilterProp="title"
          listHeight={200}
          getPopupContainer={(trigger) => document.body}
          dropdownStyle={{ maxHeight: 400, overflow: "auto" }}
          dropdownMatchSelectWidth={false}
          dropdownAlign={{ offset: [0, 4] }}
          popupClassName="custom-tree-select-dropdown"
        />
      );
    }

    return (
      <>
        {tooltipContent ? (
          <Tooltip mouseEnterDelay={0.3} title={tooltipContent}>
            {renderInput()}
          </Tooltip>
        ) : (
          renderInput()
        )}
      </>
    );
  }
);
