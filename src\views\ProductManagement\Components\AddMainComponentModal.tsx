import { Checkbox, Col, Form, Input, message, Modal, Row, Select } from "antd";
import { materialApi } from "api/material.api";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { ModalStatus } from "types/modal";
import { Material } from "types/material";
import { useWatch } from "antd/lib/form/Form";
import { InputNumber } from "components/Input/InputNumber";
import { requiredRule } from "utils/validateRule";
import { uniqueId } from "lodash";
import { BOM, MainComponent, Product } from "types/product";
import { componentApi } from "api/component.api";
import { ComponentsSelector } from "components/Selector/ComponentsSelector";
import { MainComponentSelector } from "components/Selector/MainComponentSelector ";
import {
  Component,
  DisplayImageTypeTrans,
  ZoomPosition,
  ZoomPositionTrans,
} from "types/component";
export interface MainComponentForm extends Partial<Component> {
  parentId?: number;
  position?: number;
  isEmbroidery?: boolean;
}
export interface mainComponentRef {
  handleCreate: (productId: number) => void;
  handleUpdate: (mainComponent: MainComponent, productId: number) => void;
}
interface MainComponentModalProps {
  onClose: () => void;
  onSubmitOk: (item: Partial<Component>) => void;
  onUpdate: (item: Partial<Component>) => void;
  mainComponentArray?: Component[];
}

export const AddMainComponentModal = React.forwardRef<
  mainComponentRef,
  MainComponentModalProps
>(({ onClose, onSubmitOk, onUpdate, mainComponentArray }, ref) => {
  const [form] = Form.useForm<MainComponentForm>();
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [status, setStatus] = useState<ModalStatus>("create");
  const [currentProduct, setCurrentProduct] = useState<number>();
  const [componentGroupInitOption, setComponentGroupInitOption] =
    useState<Component>();
  const [exceptMainComponentId, setExceptMainComponentId] = useState<number>();
  const materialId = useWatch("materialId", form);
  const handleFetchOneComponent = async (id: number) => {
    try {
      if (id) {
        const { data } = await componentApi.findOne(id);
        // Nếu API trả về mảng, chọn phần tử đầu tiên (nếu có)
        if (Array.isArray(data)) {
          return data.length > 0 ? data[0] : undefined;
        }
        return data;
      }
      return undefined;
    } catch (error) {
      console.log(error);
      return undefined;
    }
  };

  const displayImageTypeOptions = Object.values(DisplayImageTypeTrans).map(
    (item) => ({
      label: item.label,
      value: item.value,
    })
  );
  useImperativeHandle(
    ref,
    () => ({
      handleCreate(productId) {
        console.log("ProductId là", productId);
        form.resetFields();
        form.setFieldValue("isDefaultConfig", true);
        form.setFieldValue("zoomPosition", ZoomPosition.centerCenter);
        setStatus("create");
        setVisible(true);
        setCurrentProduct(productId);
        setExceptMainComponentId(undefined);
      },
      handleUpdate(mainComponent, productId) {
        console.log("Main component is", mainComponent);
        form.setFieldsValue({
          name: mainComponent.name,
          layer: mainComponent.layer,
          id: mainComponent.id,
          parentId: mainComponent.parent?.id || undefined,
          //@ts-ignore
          zoomPosition: mainComponent.zoomPosition || "",
          position: mainComponent.position,
          isEmbroidery: mainComponent.isEmbroidery,
          isDefaultConfig: mainComponent.isDefaultConfig,
          displayImage: mainComponent.displayImage,
        });
        handleFetchOneComponent(mainComponent.parent?.id!).then((res) => {
          console.log("Main component bằng findone là", res);
          setComponentGroupInitOption(res);
        });
        setExceptMainComponentId(mainComponent.id);
        setCurrentProduct(productId);
        setStatus("update");
        setVisible(true);
      },
    }),
    []
  );

  const handleOk = async () => {
    try {
      const values = form.getFieldsValue();
      setLoading(true);
      const parent = await handleFetchOneComponent(values.parentId!);
      const payload: Partial<Component> = {
        id: status == "update" ? values.id : Number(uniqueId()),
        name: values.name,
        layer: values.layer,
        //@ts-ignore
        parent: parent || 0,
        zoomPosition: values.zoomPosition,
        position: values.position,
        isEmbroidery: values.isEmbroidery,
        isDefaultConfig: values.isDefaultConfig,
        displayImage: values.displayImage,
      };

      if (status === "create") {
        // const { data } = await componentApi.create({
        //   component: {
        //     name: payload.name,
        //   },
        //   productId: currentProduct,
        // });
        // console.log("What is main component after create", data);
        console.log("Before send", payload);
        onSubmitOk(payload);
      } else {
        console.log("Before send", payload);

        onUpdate(payload);
      }
      // message.success(
      //   status === "create" ? "Thêm mới thành công" : "Cập nhật thành công"
      // );
      handleClose();
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setVisible(false);
    form.resetFields();
    onClose();
  };

  return (
    <Modal
      open={visible}
      onCancel={handleClose}
      onOk={() => form.submit()}
      confirmLoading={loading}
      destroyOnClose
      centered
      title={
        status === "create"
          ? "Thêm thành phần chính"
          : "Cập nhật thành phần chính"
      }
      style={{ top: 20 }}
    >
      <Form layout="vertical" form={form} onFinish={handleOk}>
        <Form.Item hidden name={"id"}></Form.Item>
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              label="Tên thành phần chính"
              name="name"
              rules={[requiredRule]}
            >
              <Input />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="Thứ tự hiển thị" name="position">
              <InputNumber allowClear />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="Lớp layer" name="layer">
              <InputNumber />
            </Form.Item>
          </Col>

          {/* <Col span={24}>
            <Form.Item
              label="Thành phần cha"
              name="parentId"
              // rules={[requiredRule]}
            >
              <MainComponentSelector
                isParent
                exceptMainComponentId={exceptMainComponentId}
                placeholder="Chọn nhóm cha"
                isMainComponent
                productId={currentProduct}
                initOptionItem={componentGroupInitOption || undefined}
              />
            </Form.Item>
          </Col> */}
          <Col span={12}>
            <Form.Item
              label="Vị trí zoom"
              name="zoomPosition"
              // rules={[requiredRule]}
            >
              <Select
                options={Object.values(ZoomPositionTrans)}
                placeholder="Chọn vị trí"
                onChange={(value) => {
                  if (value == undefined) {
                    form.setFieldValue(
                      "zoomPosition",
                      ZoomPosition.centerCenter
                    );
                  }
                }}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Truy xuất hình hiển thị"
              name="displayImage"
              rules={[requiredRule]}
            >
              <Select allowClear options={displayImageTypeOptions}></Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label=""
              name="isDefaultConfig"
              valuePropName="checked" // ⚠️ Bắt buộc để map giá trị đúng
            >
              <Checkbox>Ẩn bước cấu hình</Checkbox>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label=""
              name="isEmbroidery"
              valuePropName="checked" // ⚠️ Bắt buộc để map giá trị đúng
            >
              <Checkbox>Thêu</Checkbox>
            </Form.Item>
          </Col>
          {status == "update" && (
            <Col span={12} className="hidden">
              <Form.Item label="Định mức sử dụng" name="index">
                <InputNumber />
              </Form.Item>
            </Col>
          )}
        </Row>
      </Form>
    </Modal>
  );
});
