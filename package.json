{"name": "320-ems-admin", "version": "0.1.0", "private": true, "license": "UNLICENSED", "dependencies": {"-": "^0.0.1", "@ant-design/icons": "^4.7.0", "@aws-sdk/client-s3": "3.34.0", "@ckeditor/ckeditor5-build-classic": "^32.0.0", "@ckeditor/ckeditor5-react": "^4.0.0", "@craco/craco": "^6.4.3", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@googlemaps/js-api-loader": "^1.13.7", "@mui/material": "^7.1.0", "@mui/x-charts": "^8.4.0", "@originjs/vite-plugin-commonjs": "^1.0.3", "@react-google-maps/api": "^2.20.6", "@react-pdf/renderer": "^4.3.0", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@syncfusion/ej2-react-gantt": "^30.1.39", "@testing-library/jest-dom": "^5.16.1", "@testing-library/react": "^12.1.2", "@testing-library/user-event": "^13.5.0", "@tinymce/tinymce-react": "^3.13.0", "@types/file-saver": "^2.0.5", "@types/jest": "^27.4.0", "@types/md5": "^2.3.2", "@types/node": "^20.4.6", "@types/rc-tree": "^3.0.0", "@types/react": "^18.2.14", "@types/react-beautiful-dnd": "^13.1.2", "@types/react-color": "^3.0.6", "@types/react-copy-to-clipboard": "^5.0.4", "@types/react-dom": "^18.2.6", "@types/react-input-mask": "^3.0.1", "@types/styled-components": "^5.1.34", "@types/uuid": "^9.0.8", "@vitejs/plugin-react": "^2.2.0", "antd": "5.24.7", "array-move": "^4.0.0", "axios": "^0.25.0", "clsx": "^2.1.1", "craco-less": "^2.0.0", "dayjs": "^1.11.13", "echarts": "^5.3.1", "echarts-for-react": "^3.0.2", "emoji-picker-react": "^3.5.1", "env-cmd": "^10.1.0", "exceljs": "^4.3.0", "file-saver": "^2.0.5", "filesize": "^10.1.6", "google-map-react": "^2.1.10", "html-to-image": "^1.11.11", "i": "^0.3.7", "js-cookie": "^3.0.1", "jszip": "^3.10.1", "libphonenumber-js": "^1.11.3", "lodash": "^4.17.21", "md5": "^2.3.0", "mobx": "^6.3.13", "mobx-persist-store": "^1.0.4", "mobx-react": "^7.2.1", "moment": "^2.30.1", "npm": "^8.5.2", "path-to-regexp": "^8.2.0", "pdfjs-dist": "^5.3.31", "qrcode.react": "^4.2.0", "rc-drawer": "^4.4.3", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-big-calendar": "^1.19.4", "react-color": "^2.19.3", "react-colorful": "^5.6.1", "react-copy-to-clipboard": "^5.1.0", "react-countup": "^6.4.1", "react-device-detect": "^2.2.3", "react-dom": "^18.2.0", "react-error-boundary": "^6.0.0", "react-gesture-responder": "^2.1.0", "react-google-autocomplete": "^2.6.1", "react-grid-dnd": "^2.1.2", "react-icons": "^4.12.0", "react-image-size": "^1.0.4", "react-input-mask": "^2.0.4", "react-onesignal": "^2.0.4", "react-pdf": "^10.0.1", "react-qr-code": "^2.0.14", "react-quill": "^2.0.0", "react-resizable": "^3.0.5", "react-router-dom": "6.2.1", "react-sortable-hoc": "^2.0.0", "react-to-print": "^3.1.1", "react-transition-group": "^4.4.5", "rollup-plugin-node-polyfills": "^0.2.1", "rollup-plugin-polyfill-node": "^0.13.0", "rxjs": "^7.8.1", "sass": "^1.49.7", "styled-components": "^6.1.1", "typescript": "4.8.4", "ua-parser-js": "^1.0.37", "uuid": "^9.0.1", "vite": "^3.2.4", "vite-plugin-commonjs": "^0.6.0", "vite-plugin-svgr": "^2.2.2", "vite-tsconfig-paths": "^3.6.0", "web-vitals": "^2.1.4", "xlsx": "0.14.1"}, "scripts": {"start": "vite --host --port 5177", "build:prod": "tsc && vite build", "build:stage": "tsc && vite build --mode staging", "build:uat": "tsc && vite build --mode uat", "test": "craco start test", "eject": "react-scripts eject", "serve": "vite preview"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/ckeditor__ckeditor5-core": "^28.0.10", "@types/echarts": "^4.9.22", "@types/google-map-react": "^2.1.5", "@types/google.maps": "^3.48.1", "@types/js-cookie": "^3.0.1", "@types/lodash": "^4.14.179", "@types/qrcode.react": "^3.0.0", "@types/react-big-calendar": "^1.16.2", "@types/react-pdf": "^7.0.0", "@types/react-resizable": "^3.0.8", "@types/ua-parser-js": "^0.7.39", "autoprefixer": "^10.4.16", "babel-plugin-transform-remove-console": "^6.9.4", "postcss": "^8.4.32", "tailwindcss": "^3.3.6"}}