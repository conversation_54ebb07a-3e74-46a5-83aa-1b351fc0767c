import { Col, Form, Input, message, Modal, Row, Select } from "antd";
import { Rule } from "antd/lib/form";
import { accountApi } from "api/account.api";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { ModalStatus } from "types/modal";
import { Account } from "types/account";
import { useStaff } from "hooks/useStaff";
import { useRole } from "hooks/useRole";

const rules: Rule[] = [{ required: true }];

export interface AccountModal {
  handleCreate: () => void;
  handleUpdate: (Account: Account) => void;
}
interface AccountModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
}

export const AccountModal = React.forwardRef(
  ({ onClose, onSubmitOk }: AccountModalProps, ref) => {
    const [form] = Form.useForm<
      Account & { staffId: number; roleId: number }
    >();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");
    const [selectedAccount, setSelectedAccount] = useState<Account>();

    const {
      fetchStaff,
      staffs,
      loading: staffLoading,
    } = useStaff({
      initQuery: { limit: 100, page: 1 }, // Tăng limit để lấy nhiều staff hơn
    });

    const {
      fetchRole,
      loadingRole,
      queryRole,
      roles,
      setQueryRole,
      totalRole,
    } = useRole({ initQuery: { page: 1, limit: 100 } });

    // Fetch staff data khi modal mở
    useEffect(() => {
      if (visible) {
        fetchStaff();
        fetchRole();
      }
    }, [visible]);

    useImperativeHandle<any, AccountModal>(
      ref,
      () => ({
        handleCreate() {
          form.resetFields();
          setVisible(true);
          setStatus("create");
          setSelectedAccount(undefined);
        },
        handleUpdate(Account: Account) {
          form.setFieldsValue({ ...Account });
          setVisible(true);
          setStatus("update");
          setSelectedAccount(Account);
        },
      }),
      []
    );

    const getPayload = () => {
      const { ...rest } = form.getFieldsValue();
      return rest;
    };

    const submitForm = async () => {
      try {
        setLoading(true);
        const valid = await form.validateFields();
        const data = getPayload();
        console.log(typeof data);
        const createBody = {
          staffId: data.staffId,
          roleId: data.roleId,
          account: {
            password: data.password,
            username: data.username,
          },
        };
        let res: any = undefined;
        switch (status) {
          case "create":
            res = await accountApi.create(createBody);
            message.success("Create Account successfully!");
            break;
          case "update":
            res = await accountApi.update(selectedAccount?.id || 0, data);
            message.success("Update Account successfully!");
            break;
        }
        onSubmitOk();
        handleClose();
      } finally {
        setLoading(false);
      }
    };

    const handleClose = () => {
      onClose?.();
      setVisible(false);
      setSelectedAccount(undefined);
    };

    return (
      <Modal
        onCancel={() => {
          handleClose();
        }}
        visible={visible}
        title={status == "create" ? "Tạo tài khoản mới" : "Cập nhật tài khoản"}
        style={{ top: 20 }}
        width={700}
        confirmLoading={loading}
        onOk={submitForm}
      >
        <Form layout="vertical" form={form}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="Tài khoản" name="username" rules={rules}>
                <Input placeholder="" />
              </Form.Item>
            </Col>

            {status == "create" && (
              <Col span={12}>
                <Form.Item label="Mật khẩu" name="password" rules={rules}>
                  <Input placeholder="" type="password" />
                </Form.Item>
              </Col>
            )}

            <Col span={12}>
              <Form.Item label="Nhân viên" name="staffId" rules={rules}>
                <Select
                  placeholder="Chọn nhân viên"
                  loading={staffLoading}
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    (option?.label ?? "")
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  options={staffs.map((staff) => ({
                    value: staff.id,
                    label: `${staff.fullName} (${staff.code})`,
                  }))}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Vai trò" name="roleId" rules={rules}>
                <Select
                  placeholder="Chọn vai trò"
                  loading={loadingRole}
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    (option?.label ?? "")
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  options={roles.map((role) => ({
                    value: role.id,
                    label: `${role.name}`,
                  }))}
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
);
