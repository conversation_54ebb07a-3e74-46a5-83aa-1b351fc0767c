import { Route } from "./RouteType";
import { ReactComponent as SettingIcon } from "assets/svgs/setting.svg";
import { PermissionType } from "types/permission";
import { PermissionNames } from "types/PermissionNames";
import { lazy } from "react";
import { DictionaryType } from "types/dictionary";
const DictionaryPage = lazy(() =>
  import("views/DictionaryPage/DictionaryPage").then((m) => ({
    default: m.DictionaryPage,
  }))
);
const CreateOrUpdateCompanyPage = lazy(() =>
  import("views/CompanyPage/CreateOrUpdateCompanyPage").then((m) => ({
    default: m.CreateOrUpdateCompanyPage,
  }))
);
const CompanyPage = lazy(() =>
  import("views/CompanyPage/CompanyPage").then((m) => ({
    default: m.CompanyPage,
  }))
);

export const configRoutes: Route[] = [
  {
    title: "C<PERSON>u hình",
    icon: <SettingIcon className="size-[24px]" />,
    path: "/system-config",
    name: "system-config",
    aliasPath: `/system-config`,
    breadcrumb: "<PERSON><PERSON><PERSON> hình",
    children: [
      {
        title: "Tạo loại dịch vụ",
        breadcrumb: "Tạo loại dịch vụ",
        path: PermissionNames.serviceTypeAdd,
        name: PermissionNames.serviceTypeAdd,
        aliasPath: `/system-config/${PermissionNames.serviceTypeAdd}`,
        hidden: true,
      },
      {
        title: "Sửa loại dịch vụ",
        breadcrumb: "Sửa loại dịch vụ",
        path: PermissionNames.serviceTypeEdit,
        name: PermissionNames.serviceTypeEdit,
        aliasPath: `/system-config/${PermissionNames.serviceTypeEdit}`,
        hidden: true,
      },
      {
        title: "Xóa loại dịch vụ",
        breadcrumb: "Xóa loại dịch vụ",
        path: PermissionNames.serviceTypeDelete,
        name: PermissionNames.serviceTypeDelete,
        aliasPath: `/system-config/${PermissionNames.serviceTypeDelete}`,
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.serviceTypeViewAll,
        name: PermissionNames.serviceTypeViewAll,
        aliasPath: `/system-config/${PermissionNames.serviceTypeViewAll}`,
        hidden: true,
      },
      {
        title: "Loại dịch vụ",
        breadcrumb: "Loại dịch vụ",
        path: PermissionNames.serviceTypeList,
        name: PermissionNames.serviceTypeList,
        aliasPath: `/system-config/${PermissionNames.serviceTypeList}`,
        element: (
          <DictionaryPage
            type={DictionaryType.ServiceType}
            title="Loại dịch vụ"
          />
        ),
      },
      {
        title: "Tạo chức vụ",
        breadcrumb: "Tạo chức vụ",
        path: PermissionNames.jobTitleAdd,
        name: PermissionNames.jobTitleAdd,
        aliasPath: `/system-config/${PermissionNames.jobTitleAdd}`,
        hidden: true,
      },
      {
        title: "Cập nhật chức vụ",
        breadcrumb: "Cập nhật chức vụ",
        path: PermissionNames.jobTitleEdit,
        name: PermissionNames.jobTitleEdit,
        aliasPath: `/system-config/${PermissionNames.jobTitleEdit}`,
        hidden: true,
      },
      {
        title: "Xóa chức vụ",
        breadcrumb: "Xóa chức vụ",
        path: PermissionNames.jobTitleDelete,
        name: PermissionNames.jobTitleDelete,
        aliasPath: `/system-config/${PermissionNames.jobTitleDelete}`,
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.jobTitleViewAll,
        name: PermissionNames.jobTitleViewAll,
        aliasPath: `/system-config/${PermissionNames.jobTitleViewAll}`,
        hidden: true,
      },
      {
        title: "Chức vụ",
        breadcrumb: "Chức vụ",
        path: PermissionNames.jobTitleList,
        name: PermissionNames.jobTitleList,
        aliasPath: `/system-config/${PermissionNames.jobTitleList}`,
        element: (
          <DictionaryPage
            key={PermissionNames.jobTitleList}
            type={DictionaryType.JobTitle}
            title="Chức vụ"
          />
        ),
      },
      {
        title: "Tạo cấp bậc",
        breadcrumb: "Tạo cấp bậc",
        path: PermissionNames.levelAdd,
        name: PermissionNames.levelAdd,
        aliasPath: `/system-config/${PermissionNames.levelAdd}`,
        hidden: true,
      },
      {
        title: "Cập nhật cấp bậc",
        breadcrumb: "Cập nhật cấp bậc",
        path: PermissionNames.levelEdit,
        name: PermissionNames.levelEdit,
        aliasPath: `/system-config/${PermissionNames.levelEdit}`,
        hidden: true,
      },
      {
        title: "Xóa cấp bậc",
        breadcrumb: "Xóa cấp bậc",
        path: PermissionNames.levelDelete,
        name: PermissionNames.levelDelete,
        aliasPath: `/system-config/${PermissionNames.levelDelete}`,
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.levelViewAll,
        name: PermissionNames.levelViewAll,
        aliasPath: `/system-config/${PermissionNames.levelViewAll}`,
        hidden: true,
      },
      {
        title: "Cấp bậc",
        breadcrumb: "Cấp bậc",
        path: PermissionNames.levelList,
        aliasPath: `/system-config/${PermissionNames.levelList}`,
        name: PermissionNames.levelList,
        element: (
          <DictionaryPage
            key={PermissionNames.levelList}
            type={DictionaryType.Level}
            title="Cấp bậc"
          />
        ),
      },
      {
        title: "Tạo phòng ban",
        breadcrumb: "Tạo phòng ban",
        path: PermissionNames.departmentAdd,
        name: PermissionNames.departmentAdd,
        aliasPath: `/system-config/${PermissionNames.departmentAdd}`,
        hidden: true,
      },
      {
        title: "Cập nhật phòng ban",
        breadcrumb: "Cập nhật phòng ban",
        path: PermissionNames.departmentEdit,
        name: PermissionNames.departmentEdit,
        aliasPath: `/system-config/${PermissionNames.departmentEdit}`,
        hidden: true,
      },
      {
        title: "Xóa phòng ban",
        breadcrumb: "Xóa phòng ban",
        path: PermissionNames.departmentDelete,
        name: PermissionNames.departmentDelete,
        aliasPath: `/system-config/${PermissionNames.departmentDelete}`,
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.departmentViewAll,
        name: PermissionNames.departmentViewAll,
        aliasPath: `/system-config/${PermissionNames.departmentViewAll}`,
        hidden: true,
      },
      {
        title: "Phòng ban",
        breadcrumb: "Phòng ban",
        path: PermissionNames.departmentList,
        aliasPath: `/system-config/${PermissionNames.departmentList}`,
        name: PermissionNames.departmentList,
        element: (
          <DictionaryPage
            key={PermissionNames.departmentList}
            type={DictionaryType.Department}
            title="Phòng ban"
          />
        ),
      },
      {
        title: "Tạo thương hiệu",
        breadcrumb: "Tạo thương hiệu",
        path: PermissionNames.brandAdd,
        name: PermissionNames.brandAdd,
        aliasPath: `/system-config/${PermissionNames.brandAdd}`,
        hidden: true,
      },
      {
        title: "Cập nhật thương hiệu",
        breadcrumb: "Cập nhật thương hiệu",
        path: PermissionNames.brandEdit,
        name: PermissionNames.brandEdit,
        aliasPath: `/system-config/${PermissionNames.brandEdit}`,
        hidden: true,
      },
      {
        title: "Xóa thương hiệu",
        breadcrumb: "Xóa thương hiệu",
        path: PermissionNames.brandDelete,
        name: PermissionNames.brandDelete,
        aliasPath: `/system-config/${PermissionNames.brandDelete}`,
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.brandViewAll,
        name: PermissionNames.brandViewAll,
        aliasPath: `/system-config/${PermissionNames.brandViewAll}`,
        hidden: true,
      },
      {
        title: "Thương hiệu",
        breadcrumb: "Thương hiệu",
        path: PermissionNames.brandList,
        aliasPath: `/system-config/${PermissionNames.brandList}`,
        name: PermissionNames.brandList,
        element: (
          <DictionaryPage
            key={PermissionNames.brandList}
            type={DictionaryType.Brand}
            title="Thương hiệu"
          />
        ),
      },
      {
        title: "Tạo nhóm tài khoản",
        breadcrumb: "Tạo nhóm tài khoản",
        path: PermissionNames.accountGroupAdd,
        name: PermissionNames.accountGroupAdd,
        aliasPath: `/system-config/${PermissionNames.accountGroupAdd}`,
        hidden: true,
      },
      {
        title: "Cập nhật nhóm tài khoản",
        breadcrumb: "Cập nhật nhóm tài khoản",
        path: PermissionNames.accountGroupEdit,
        name: PermissionNames.accountGroupEdit,
        aliasPath: `/system-config/${PermissionNames.accountGroupEdit}`,
        hidden: true,
      },
      {
        title: "Xóa nhóm tài khoản",
        breadcrumb: "Xóa nhóm tài khoản",
        path: PermissionNames.accountGroupDelete,
        name: PermissionNames.accountGroupDelete,
        aliasPath: `/system-config/${PermissionNames.accountGroupDelete}`,
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.accountGroupViewAll,
        name: PermissionNames.accountGroupViewAll,
        aliasPath: `/system-config/${PermissionNames.accountGroupViewAll}`,
        hidden: true,
      },
      {
        title: "Nhóm tài khoản",
        breadcrumb: "Nhóm tài khoản",
        path: PermissionNames.accountGroupList,
        name: PermissionNames.accountGroupList,
        aliasPath: `/system-config/${PermissionNames.accountGroupList}`,
        element: (
          <DictionaryPage
            key={PermissionNames.accountGroupList}
            type={DictionaryType.AccountGroup}
            title="Nhóm tài khoản"
          />
        ),
      },
      {
        title: "Tạo nhóm hàng hóa",
        breadcrumb: "Tạo nhóm hàng hóa",
        path: PermissionNames.productGroupAdd,
        name: PermissionNames.productGroupAdd,
        aliasPath: `/system-config/${PermissionNames.productGroupAdd}`,
        hidden: true,
      },
      {
        title: "Cập nhật nhóm hàng hóa",
        breadcrumb: "Cập nhật nhóm hàng hóa",
        path: PermissionNames.productGroupEdit,
        name: PermissionNames.productGroupEdit,
        aliasPath: `/system-config/${PermissionNames.productGroupEdit}`,
        hidden: true,
      },
      {
        title: "Xóa nhóm hàng hóa",
        breadcrumb: "Xóa nhóm hàng hóa",
        path: PermissionNames.productGroupDelete,
        name: PermissionNames.productGroupDelete,
        aliasPath: `/system-config/${PermissionNames.productGroupDelete}`,
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.productGroupViewAll,
        name: PermissionNames.productGroupViewAll,
        aliasPath: `/system-config/${PermissionNames.productGroupViewAll}`,
        hidden: true,
      },
      {
        title: "Nhóm hàng hóa",
        breadcrumb: "Nhóm hàng hóa",
        path: PermissionNames.productGroupList,
        name: PermissionNames.productGroupList,
        aliasPath: `/system-config/${PermissionNames.productGroupList}`,
        element: (
          <DictionaryPage
            key={PermissionNames.productGroupList}
            type={DictionaryType.ProductGroup}
            title="Nhóm hàng hóa"
          />
        ),
      },
      {
        title: "Tạo nhóm BOQ",
        breadcrumb: "Tạo nhóm BOQ",
        path: PermissionNames.boqGroupAdd,
        name: PermissionNames.boqGroupAdd,
        aliasPath: `/system-config/${PermissionNames.boqGroupAdd}`,
        hidden: true,
      },
      {
        title: "Cập nhật nhóm BOQ",
        breadcrumb: "Cập nhật nhóm BOQ",
        path: PermissionNames.boqGroupEdit,
        name: PermissionNames.boqGroupEdit,
        aliasPath: `/system-config/${PermissionNames.boqGroupEdit}`,
        hidden: true,
      },
      {
        title: "Xóa nhóm BOQ",
        breadcrumb: "Xóa nhóm BOQ",
        path: PermissionNames.boqGroupDelete,
        name: PermissionNames.boqGroupDelete,
        aliasPath: `/system-config/${PermissionNames.boqGroupDelete}`,
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.boqGroupViewAll,
        name: PermissionNames.boqGroupViewAll,
        aliasPath: `/system-config/${PermissionNames.boqGroupViewAll}`,
        hidden: true,
      },
      {
        title: "Nhóm BOQ",
        breadcrumb: "Nhóm BOQ",
        path: PermissionNames.boqGroupList,
        name: PermissionNames.boqGroupList,
        aliasPath: `/system-config/${PermissionNames.boqGroupList}`,
        element: (
          <DictionaryPage
            key={PermissionNames.boqGroupList}
            type={DictionaryType.BOQGroup}
            title="Nhóm BOQ"
          />
        ),
      },
      {
        title: "Tạo nhóm nguyên vật liệu",
        breadcrumb: "Tạo nhóm nguyên vật liệu",
        path: PermissionNames.materialGroupAdd,
        name: PermissionNames.materialGroupAdd,
        aliasPath: `/system-config/${PermissionNames.materialGroupAdd}`,
        hidden: true,
      },
      {
        title: "Cập nhật nhóm nguyên vật liệu",
        breadcrumb: "Cập nhật nhóm nguyên vật liệu",
        path: PermissionNames.materialGroupEdit,
        name: PermissionNames.materialGroupEdit,
        aliasPath: `/system-config/${PermissionNames.materialGroupEdit}`,
        hidden: true,
      },
      {
        title: "Xóa nhóm nguyên vật liệu",
        breadcrumb: "Xóa nhóm nguyên vật liệu",
        path: PermissionNames.materialGroupDelete,
        name: PermissionNames.materialGroupDelete,
        aliasPath: `/system-config/${PermissionNames.materialGroupDelete}`,
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.materialGroupViewAll,
        name: PermissionNames.materialGroupViewAll,
        aliasPath: `/system-config/${PermissionNames.materialGroupViewAll}`,
        hidden: true,
      },
      {
        title: "Nhóm nguyên vật liệu",
        breadcrumb: "Nhóm nguyên vật liệu",
        path: PermissionNames.materialGroupList,
        name: PermissionNames.materialGroupList,
        aliasPath: `/system-config/${PermissionNames.materialGroupList}`,
        element: (
          <DictionaryPage
            key={PermissionNames.materialGroupList}
            type={DictionaryType.MaterialGroup}
            title="Nhóm nguyên vật liệu"
          />
        ),
      },
      {
        title: "Tạo Nhóm thiết bị",
        breadcrumb: "Tạo Nhóm thiết bị",
        path: PermissionNames.deviceGroupAdd,
        name: PermissionNames.deviceGroupAdd,
        aliasPath: `/system-config/${PermissionNames.deviceGroupAdd}`,
        hidden: true,
      },
      {
        title: "Cập nhật Nhóm thiết bị",
        breadcrumb: "Cập nhật Nhóm thiết bị",
        path: PermissionNames.deviceGroupEdit,
        name: PermissionNames.deviceGroupEdit,
        aliasPath: `/system-config/${PermissionNames.deviceGroupEdit}`,
        hidden: true,
      },
      {
        title: "Xóa Nhóm thiết bị",
        breadcrumb: "Xóa Nhóm thiết bị",
        path: PermissionNames.deviceGroupDelete,
        name: PermissionNames.deviceGroupDelete,
        aliasPath: `/system-config/${PermissionNames.deviceGroupDelete}`,
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.deviceGroupViewAll,
        name: PermissionNames.deviceGroupViewAll,
        aliasPath: `/system-config/${PermissionNames.deviceGroupViewAll}`,
        hidden: true,
      },
      {
        title: "Nhóm thiết bị",
        breadcrumb: "Nhóm thiết bị",
        path: PermissionNames.deviceGroupList,
        name: PermissionNames.deviceGroupList,
        aliasPath: `/system-config/${PermissionNames.deviceGroupList}`,
        element: (
          <DictionaryPage
            key={PermissionNames.deviceGroupList}
            type={DictionaryType.DeviceCategory}
            title="Nhóm thiết bị"
          />
        ),
      },
      {
        title: "Tạo nhóm máy",
        breadcrumb: "Tạo nhóm máy",
        path: PermissionNames.machineGroupAdd,
        name: PermissionNames.machineGroupAdd,
        aliasPath: `/system-config/${PermissionNames.machineGroupAdd}`,
        hidden: true,
      },
      {
        title: "Cập nhật nhóm máy",
        breadcrumb: "Cập nhật nhóm máy",
        path: PermissionNames.machineGroupEdit,
        name: PermissionNames.machineGroupEdit,
        aliasPath: `/system-config/${PermissionNames.machineGroupEdit}`,
        hidden: true,
      },
      {
        title: "Xóa nhóm máy",
        breadcrumb: "Xóa nhóm máy",
        path: PermissionNames.machineGroupDelete,
        name: PermissionNames.machineGroupDelete,
        aliasPath: `/system-config/${PermissionNames.machineGroupDelete}`,
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.machineGroupViewAll,
        name: PermissionNames.machineGroupViewAll,
        aliasPath: `/system-config/${PermissionNames.machineGroupViewAll}`,
        hidden: true,
      },
      {
        title: "Nhóm máy",
        breadcrumb: "Nhóm máy",
        path: PermissionNames.machineGroupList,
        name: PermissionNames.machineGroupList,
        aliasPath: `/system-config/${PermissionNames.machineGroupList}`,
        element: (
          <DictionaryPage
            key={PermissionNames.machineGroupList}
            type={DictionaryType.MachineCategory}
            title="Nhóm máy"
          />
        ),
      },
      {
        title: "Tạo loại nhà cung cấp",
        breadcrumb: "Tạo loại nhà cung cấp",
        path: PermissionNames.providerCategoryAdd,
        name: PermissionNames.providerCategoryAdd,
        aliasPath: `/system-config/${PermissionNames.providerCategoryAdd}`,
        hidden: true,
      },
      {
        title: "Cập nhật loại nhà cung cấp",
        breadcrumb: "Cập nhật loại nhà cung cấp",
        path: PermissionNames.providerCategoryEdit,
        name: PermissionNames.providerCategoryEdit,
        aliasPath: `/system-config/${PermissionNames.providerCategoryEdit}`,
        hidden: true,
      },
      {
        title: "Xóa loại nhà cung cấp",
        breadcrumb: "Xóa loại nhà cung cấp",
        path: PermissionNames.providerCategoryDelete,
        name: PermissionNames.providerCategoryDelete,
        aliasPath: `/system-config/${PermissionNames.providerCategoryDelete}`,
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.providerCategoryViewAll,
        name: PermissionNames.providerCategoryViewAll,
        aliasPath: `/system-config/${PermissionNames.providerCategoryViewAll}`,
        hidden: true,
      },
      {
        title: "Loại nhà cung cấp",
        breadcrumb: "Loại nhà cung cấp",
        path: PermissionNames.providerCategoryList,
        name: PermissionNames.providerCategoryList,
        aliasPath: `/system-config/${PermissionNames.providerCategoryList}`,
        element: (
          <DictionaryPage
            key={PermissionNames.providerCategoryList}
            type={DictionaryType.ProviderCategory}
            title="Loại nhà cung cấp"
          />
        ),
      },
      {
        title: "Tạo quốc gia",
        breadcrumb: "Tạo quốc gia",
        path: PermissionNames.countryAdd,
        name: PermissionNames.countryAdd,
        aliasPath: `/system-config/${PermissionNames.countryAdd}`,
        hidden: true,
      },
      {
        title: "Cập nhật quốc gia",
        breadcrumb: "Cập nhật quốc gia",
        path: PermissionNames.countryEdit,
        name: PermissionNames.countryEdit,
        aliasPath: `/system-config/${PermissionNames.countryEdit}`,
        hidden: true,
      },
      {
        title: "Xóa quốc gia",
        breadcrumb: "Xóa quốc gia",
        path: PermissionNames.countryDelete,
        name: PermissionNames.countryDelete,
        aliasPath: `/system-config/${PermissionNames.countryDelete}`,
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.countryViewAll,
        name: PermissionNames.countryViewAll,
        aliasPath: `/system-config/${PermissionNames.countryViewAll}`,
        hidden: true,
      },
      {
        title: "Quốc gia",
        breadcrumb: "Quốc gia",
        path: PermissionNames.countryList,
        name: PermissionNames.countryList,
        aliasPath: `/system-config/${PermissionNames.countryList}`,
        element: (
          <DictionaryPage
            key={PermissionNames.countryList}
            type={DictionaryType.Country}
            title="Quốc gia"
          />
        ),
      },
      {
        title: "Tạo loại thầu phụ",
        breadcrumb: "Tạo loại thầu phụ",
        path: PermissionNames.subcontractorCategoryAdd,
        name: PermissionNames.subcontractorCategoryAdd,
        aliasPath: `/system-config/${PermissionNames.subcontractorCategoryAdd}`,
        hidden: true,
      },
      {
        title: "Cập nhật loại thầu phụ",
        breadcrumb: "Cập nhật loại thầu phụ",
        path: PermissionNames.subcontractorCategoryEdit,
        name: PermissionNames.subcontractorCategoryEdit,
        aliasPath: `/system-config/${PermissionNames.subcontractorCategoryEdit}`,
        hidden: true,
      },
      {
        title: "Xóa loại thầu phụ",
        breadcrumb: "Xóa loại thầu phụ",
        path: PermissionNames.subcontractorCategoryDelete,
        name: PermissionNames.subcontractorCategoryDelete,
        aliasPath: `/system-config/${PermissionNames.subcontractorCategoryDelete}`,
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.subcontractorCategoryViewAll,
        name: PermissionNames.subcontractorCategoryViewAll,
        aliasPath: `/system-config/${PermissionNames.subcontractorCategoryViewAll}`,
        hidden: true,
      },
      {
        title: "Loại thầu phụ",
        breadcrumb: "Loại thầu phụ",
        path: PermissionNames.subcontractorCategoryList,
        name: PermissionNames.subcontractorCategoryList,
        aliasPath: `/system-config/${PermissionNames.subcontractorCategoryList}`,
        element: (
          <DictionaryPage
            key={PermissionNames.subcontractorCategoryList}
            type={DictionaryType.SubContractorCategory}
            title="Loại thầu phụ"
          />
        ),
      },
      {
        title: "Tạo loại công tác",
        breadcrumb: "Tạo loại công tác",
        path: PermissionNames.workTypeAdd,
        name: PermissionNames.workTypeAdd,
        aliasPath: `/system-config/${PermissionNames.workTypeAdd}`,
        hidden: true,
      },
      {
        title: "Cập nhật loại công tác",
        breadcrumb: "Cập nhật loại công tác",
        path: PermissionNames.workTypeEdit,
        name: PermissionNames.workTypeEdit,
        aliasPath: `/system-config/${PermissionNames.workTypeEdit}`,
        hidden: true,
      },
      {
        title: "Xóa loại công tác",
        breadcrumb: "Xóa loại công tác",
        path: PermissionNames.workTypeDelete,
        name: PermissionNames.workTypeDelete,
        aliasPath: `/system-config/${PermissionNames.workTypeDelete}`,
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.workTypeViewAll,
        name: PermissionNames.workTypeViewAll,
        aliasPath: `/system-config/${PermissionNames.workTypeViewAll}`,
        hidden: true,
      },
      {
        title: "Loại công tác",
        breadcrumb: "Loại công tác",
        path: PermissionNames.workTypeList,
        name: PermissionNames.workTypeList,
        aliasPath: `/system-config/${PermissionNames.workTypeList}`,
        element: (
          <DictionaryPage
            key={PermissionNames.workTypeList}
            type={DictionaryType.WorkType}
            title="Loại công tác"
          />
        ),
      },
      {
        title: "Tạo hạng mục",
        breadcrumb: "Tạo hạng mục",
        path: PermissionNames.instructionCategoryAdd,
        name: PermissionNames.instructionCategoryAdd,
        aliasPath: `/system-config/${PermissionNames.instructionCategoryAdd}`,
        hidden: true,
      },
      {
        title: "Cập nhật hạng mục",
        breadcrumb: "Cập nhật hạng mục",
        path: PermissionNames.instructionCategoryEdit,
        name: PermissionNames.instructionCategoryEdit,
        aliasPath: `/system-config/${PermissionNames.instructionCategoryEdit}`,
        hidden: true,
      },
      {
        title: "Xóa hạng mục",
        breadcrumb: "Xóa hạng mục",
        path: PermissionNames.instructionCategoryDelete,
        name: PermissionNames.instructionCategoryDelete,
        aliasPath: `/system-config/${PermissionNames.instructionCategoryDelete}`,
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.instructionCategoryViewAll,
        name: PermissionNames.instructionCategoryViewAll,
        aliasPath: `/system-config/${PermissionNames.instructionCategoryViewAll}`,
        hidden: true,
      },
      {
        title: "Hạng mục",
        breadcrumb: "Hạng mục",
        path: PermissionNames.instructionCategoryList,
        name: PermissionNames.instructionCategoryList,
        aliasPath: `/system-config/${PermissionNames.instructionCategoryList}`,
        element: (
          <DictionaryPage
            key={PermissionNames.instructionCategoryList}
            type={DictionaryType.InstructionCategory}
            title="Hạng mục"
          />
        ),
      },
      {
        title: "Tạo loại RFIs",
        breadcrumb: "Tạo loại RFIs",
        path: PermissionNames.rfiCategoryAdd,
        name: PermissionNames.rfiCategoryAdd,
        aliasPath: `/system-config/${PermissionNames.rfiCategoryAdd}`,
        hidden: true,
      },
      {
        title: "Cập nhật loại RFIs",
        breadcrumb: "Cập nhật loại RFIs",
        path: PermissionNames.rfiCategoryEdit,
        name: PermissionNames.rfiCategoryEdit,
        aliasPath: `/system-config/${PermissionNames.rfiCategoryEdit}`,
        hidden: true,
      },
      {
        title: "Xóa loại RFIs",
        breadcrumb: "Xóa loại RFIs",
        path: PermissionNames.rfiCategoryDelete,
        name: PermissionNames.rfiCategoryDelete,
        aliasPath: `/system-config/${PermissionNames.rfiCategoryDelete}`,
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.rfiCategoryViewAll,
        name: PermissionNames.rfiCategoryViewAll,
        aliasPath: `/system-config/${PermissionNames.rfiCategoryViewAll}`,
        hidden: true,
      },
      {
        title: "Loại RFIs",
        breadcrumb: "Loại RFIs",
        path: PermissionNames.rfiCategoryList,
        name: PermissionNames.rfiCategoryList,
        aliasPath: `/system-config/${PermissionNames.rfiCategoryList}`,
        element: (
          <DictionaryPage
            key={PermissionNames.rfiCategoryList}
            type={DictionaryType.RfiCategory}
            title="Loại RFIs"
          />
        ),
      },
      {
        title: "Tạo Phân loại",
        breadcrumb: "Tạo Phân loại",
        path: PermissionNames.classifyAdd,
        name: PermissionNames.classifyAdd,
        aliasPath: `/system-config/${PermissionNames.classifyAdd}`,
        hidden: true,
      },
      {
        title: "Cập nhật Phân loại",
        breadcrumb: "Cập nhật Phân loại",
        path: PermissionNames.classifyEdit,
        name: PermissionNames.classifyEdit,
        aliasPath: `/system-config/${PermissionNames.classifyEdit}`,
        hidden: true,
      },
      {
        title: "Xóa Phân loại",
        breadcrumb: "Xóa Phân loại",
        path: PermissionNames.classifyDelete,
        name: PermissionNames.classifyDelete,
        aliasPath: `/system-config/${PermissionNames.classifyDelete}`,
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.classifyViewAll,
        name: PermissionNames.classifyViewAll,
        aliasPath: `/system-config/${PermissionNames.classifyViewAll}`,
        hidden: true,
      },
      {
        title: "Phân loại",
        breadcrumb: "Phân loại",
        path: PermissionNames.classifyList,
        name: PermissionNames.classifyList,
        aliasPath: `/system-config/${PermissionNames.classifyList}`,
        element: (
          <DictionaryPage
            key={PermissionNames.classifyList}
            type={DictionaryType.ChangeEventCategory}
            title="Phân loại"
          />
        ),
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.fileAttachCategoryViewAll,
        name: PermissionNames.fileAttachCategoryViewAll,
        aliasPath: `/system-config/${PermissionNames.fileAttachCategoryViewAll}`,
        hidden: true,
      },
      {
        title: "Loại hình ảnh",
        breadcrumb: "Loại hình ảnh",
        path: PermissionNames.fileAttachCategoryList,
        name: PermissionNames.fileAttachCategoryList,
        aliasPath: `/system-config/${PermissionNames.fileAttachCategoryList}`,
        element: (
          <DictionaryPage
            key={PermissionNames.fileAttachCategoryList}
            type={DictionaryType.FileAttachCategory}
            title="Loại hình ảnh"
          />
        ),
        hidden: true,
      },
      {
        title: "Tạo loại danh bạ",
        breadcrumb: "Tạo loại danh bạ",
        path: PermissionNames.contactsAdd,
        name: PermissionNames.contactsAdd,
        aliasPath: `/system-config/${PermissionNames.contactsAdd}`,
        hidden: true,
      },
      {
        title: "Cập nhật loại danh bạ",
        breadcrumb: "Cập nhật loại danh bạ",
        path: PermissionNames.contactsEdit,
        name: PermissionNames.contactsEdit,
        aliasPath: `/system-config/${PermissionNames.contactsEdit}`,
        hidden: true,
      },
      {
        title: "Xóa loại danh bạ",
        breadcrumb: "Xóa loại danh bạ",
        path: PermissionNames.contactsDelete,
        name: PermissionNames.contactsDelete,
        aliasPath: `/system-config/${PermissionNames.contactsDelete}`,
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.contactsViewAll,
        name: PermissionNames.contactsViewAll,
        aliasPath: `/system-config/${PermissionNames.contactsViewAll}`,
        hidden: true,
      },
      {
        title: "Loại danh bạ",
        breadcrumb: "Loại danh bạ",
        path: PermissionNames.contactsList,
        name: PermissionNames.contactsList,
        aliasPath: `/system-config/${PermissionNames.contactsList}`,
        element: (
          <DictionaryPage
            key={PermissionNames.contactsList}
            type={DictionaryType.MemberShipCategory}
            title="Loại danh bạ"
          />
        ),
      },
      {
        title: "Công ty",
        breadcrumb: "Danh sách công ty",
        path: PermissionNames.companyList,
        name: PermissionNames.companyList,
        aliasPath: `/system-config/${PermissionNames.companyList}`,
        element: <CompanyPage title="Danh sách công ty" />,
        permissionTypes: [PermissionType.List],
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.companyViewAll,
        name: PermissionNames.companyViewAll,
        aliasPath: `/system-config/${PermissionNames.companyViewAll}`,
        hidden: true,
      },
      {
        title: "Tạo công ty",
        breadcrumb: "Tạo công ty",
        path: PermissionNames.companyAdd,
        name: PermissionNames.companyAdd,
        aliasPath: `/system-config/${PermissionNames.companyAdd}`,
        element: (
          <CreateOrUpdateCompanyPage status="create" title="Tạo công ty" />
        ),
        permissionTypes: [PermissionType.Add],
        hidden: true,
        isPublic: true,
        // icon: <TbPackages />,
      },
      {
        title: "Chỉnh sửa công ty",
        breadcrumb: "Chỉnh sửa công ty",
        path: PermissionNames.companyEdit,
        name: PermissionNames.companyEdit,
        aliasPath: `/system-config/${PermissionNames.companyEdit.replace(
          "/:id",
          ""
        )}`,
        element: (
          <CreateOrUpdateCompanyPage
            status="update"
            title="Chỉnh sửa danh sách công ty"
          />
        ),
        permissionTypes: [PermissionType.Edit],
        hidden: true,
        isPublic: true,
        // icon: <TbPackages />,
      },
      {
        title: "Xóa công ty",
        breadcrumb: "Xóa công ty",
        path: PermissionNames.companyDelete,
        name: PermissionNames.companyDelete,
        aliasPath: `/system-config/${PermissionNames.companyDelete}`,
        hidden: true,
      },
    ],
  },
];
