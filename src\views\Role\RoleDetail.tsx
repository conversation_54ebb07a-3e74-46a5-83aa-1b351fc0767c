import { Card, message, Modal, Space, Spin, Tabs } from "antd";
import { roleApi } from "api/role.api";
import CustomButton from "components/Button/CustomButton";
import PageTitle from "components/PageTitle/PageTitle";
import React, { useEffect, useMemo, useState } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { PermissionNames } from "types/PermissionNames";
import { Role } from "types/role";
import { formatVND } from "utils";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import {
  Permission,
  PermissionType,
  PermissionTypeTrans,
} from "types/permission";
import { ReactComponent as CheckIcon } from "assets/svgs/check.svg";
import { useStaff } from "hooks/useStaff";
import { Staff } from "types/staff";
import { Pagination } from "components/Pagination";
import { settings } from "settings";
import { $url } from "utils/url";
import CustomInput from "components/Input/CustomInput";
import RoleTable, { RoleColumn } from "./components/RoleTable";

export const RoleDetail = ({ title = " " }) => {
  const params = useParams();
  const navigate = useNavigate();

  const {
    deleteStaff,
    fetchStaff,
    isEmptyQuery,
    loading: loadingStaff,
    query,
    setQuery,
    staffs,
    total,
  } = useStaff({
    initQuery: { page: 1, limit: 20 },
  });

  const [loading, setLoading] = useState(false);
  const [selectedRole, setSelectedRole] = useState<Role>();
  const [checkedNames, setCheckedNames] = useState<string[]>([]);

  useEffect(() => {
    const roleId = params.id;
    if (roleId) {
      getOneRole(+roleId);
    }
  }, []);

  const generateSelectedKeys = async (role: Role) => {
    if (role.id) {
      const permissions: Permission[] = role.permissions;
      setCheckedNames(permissions.map((e) => e.name));
    }
  };

  const getOneRole = async (roleId: number) => {
    try {
      setLoading(true);
      const { data } = await roleApi.findOne(roleId);
      query.roleId = data.id;
      setQuery({ ...query });
      fetchStaff();
      setSelectedRole(data);
      generateSelectedKeys(data);
    } catch (e) {
      console.log({ e });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteRole = async (roleId: number) => {
    try {
      const res = await roleApi.delete(roleId);
      navigate(`/master-data/${PermissionNames.roleList}`);
      message.success("Xóa vai trò thành công!");
    } catch (error) {
      console.log({ error });
    }
  };

  const columns: CustomizableColumn<RoleColumn>[] = useMemo(() => {
    const cols: CustomizableColumn<RoleColumn>[] = [
      {
        title: "Chức năng",
        key: "feature",
        dataIndex: "feature",
      },
    ];

    Object.values(PermissionTypeTrans).forEach((it) => {
      cols.push({
        title: it.label,
        key: it.value,
        dataIndex: it.value,
        align: "center",
        width: 100,
        render: (_, record) => {
          return record.routeChildren ? null : it.value ==
              PermissionType.List &&
            checkedNames.includes(record.name as any) ? (
            <CheckIcon />
          ) : (
            <div className="font-bold">-</div>
          );
        },
      });
    });

    return cols;
  }, [checkedNames]);

  const staffColumns: CustomizableColumn<Staff>[] = [
    {
      title: "Tài khoản",
      dataIndex: "account",
      key: "account",
      render: (_, record) => (
        <div className="flex items-center gap-2 py-1">
          <img
            src={$url(record.avatar) || settings.defaultAvatar}
            className="size-[32px] rounded-full"
            style={{ border: "1px solid var(--color-neutral-n2)" }}
          />
          <div className="font-bold">{record.fullName}</div>
        </div>
      ),
      defaultVisible: true,
    },
    {
      title: "Số điện thoại",
      dataIndex: "phone",
      key: "phone",
      width: 150,
      render: (_, record) => record.phone || "-",
      defaultVisible: true,
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
      width: 300,
      render: (_, record) => record.email || "-",
      defaultVisible: true,
    },
    {
      title: "Phòng ban",
      dataIndex: "department",
      key: "department",
      width: 250,
      render: (_, record) => record.department?.name || "-",
      defaultVisible: true,
    },
    {
      title: "Chức vụ",
      dataIndex: "jobTitle",
      key: "jobTitle",
      width: 250,
      render: (_, record) => record.jobTitle?.name || "-",
      defaultVisible: true,
    },
  ];

  return (
    <div>
      <PageTitle
        back
        title={selectedRole?.name || ""}
        breadcrumbs={[
          { label: "Dữ liệu nguồn" },
          {
            label: "Phân quyền",
            href: `/master-data/${PermissionNames.roleList}`,
          },
          { label: selectedRole?.name || "" },
        ]}
        extra={
          <Space>
            <CustomButton
              variant="outline"
              onClick={(e) => {
                e.stopPropagation();
                Modal.confirm({
                  title: `Xóa vai trò ${selectedRole?.name}`,
                  getContainer: () => {
                    return document.getElementById("App") as HTMLElement;
                  },
                  icon: null,
                  content: (
                    <>
                      <ul>
                        <li>
                          Các tài khoản có vai trò này sẽ không thể truy cập
                        </li>
                        <li>Không thể khôi phục vai trò này sau khi xóa</li>
                      </ul>
                      <div>Bạn có chắc chắn muốn xóa vai trò này?</div>
                    </>
                  ),
                  footer: (_, { OkBtn, CancelBtn }) => (
                    <>
                      <CustomButton
                        variant="outline"
                        className="cta-button"
                        onClick={() => {
                          handleDeleteRole(selectedRole!.id);
                          Modal.destroyAll();
                        }}
                      >
                        Có
                      </CustomButton>
                      <CustomButton
                        onClick={() => {
                          Modal.destroyAll();
                        }}
                        className="cta-button"
                      >
                        Không
                      </CustomButton>
                    </>
                  ),
                });
              }}
            >
              Xóa
            </CustomButton>
            <CustomButton
              onClick={() => {
                navigate(
                  `/master-data/${PermissionNames.roleEdit.replace(
                    ":id",
                    selectedRole!.id + ""
                  )}`
                );
              }}
            >
              Chỉnh sửa
            </CustomButton>
          </Space>
        }
      />
      <Spin spinning={loading}>
        <Card>
          <Tabs
            className="role-tabs"
            items={[
              {
                key: "info",
                label: "Thông tin chung",
                children: selectedRole ? (
                  <div>
                    {[
                      { label: "Tên vai trò", value: selectedRole.name },
                      // { label: "Mô tả", value: selectedRole.description },
                      {
                        label: "Số lượng phân quyền được phép truy cập",
                        value: formatVND(selectedRole.permissions.length),
                      },
                      {
                        label: "Số lượng tài khoản được gán vai trò này",
                        value: formatVND(selectedRole.totalStaff),
                      },
                    ].map((it, i) => (
                      <div
                        key={i}
                        className="py-[16px] flex gap-[16px] items-center"
                        style={{
                          border: "0px solid var(--color-neutral-n2)",
                          borderBottomWidth: "1px",
                        }}
                      >
                        <div className="w-[140px] font-light">{it.label}</div>
                        <div className="font-bold">{it.value}</div>
                      </div>
                    ))}
                  </div>
                ) : null,
              },
              {
                key: "permission",
                label: "Phân quyền",
                children: (
                  <div>
                    <RoleTable
                      tableProps={{ scroll: { y: 600 } }}
                      readOnly
                      checkedNames={checkedNames}
                      setCheckedNames={setCheckedNames}
                      // columns={columns}
                    />
                  </div>
                ),
              },
              {
                key: "account",
                label: "Tài khoản",
                children: (
                  <div>
                    <div className="flex gap-[16px] items-end pb-[12px]">
                      <div className="w-full max-w-[500px]">
                        <CustomInput
                          tooltipContent={"Tìm theo tên, sđt, email nhân viên"}
                          placeholder="Tìm kiếm"
                          value={query.search}
                          onChange={(value) => {
                            if (!value) {
                              delete query.search;
                              fetchStaff({ ...query });
                            } else {
                              query.search = value;
                              setQuery({ ...query });
                            }
                          }}
                          onPressEnter={() => {
                            // Pass all parameters including filters to API
                            query.page = 1;
                            fetchStaff({ ...query });
                          }}
                          allowClear
                        />
                      </div>
                    </div>
                    <CustomizableTable
                      columns={staffColumns}
                      dataSource={staffs}
                      rowKey="id"
                      loading={loadingStaff}
                      pagination={false}
                      scroll={{ x: 1200 }}
                      // displayOptions
                      className="staff-table-no-empty-hover"
                      tableId="staff-page"
                      autoAdjustColumnWidth={true}
                      //@ts-ignore
                      // onChange={handleTableChange}
                      // onRow={(record) => ({
                      //   onClick: () => {
                      //     navigate(
                      //       `/master-data/${PermissionNames.staffEdit.replace(
                      //         ":id",
                      //         record!.id + ""
                      //       )}`
                      //     );
                      //   },
                      // })}
                    />

                    <Pagination
                      currentPage={query.page}
                      defaultPageSize={query.limit}
                      total={total}
                      onChange={({ limit, page }) => {
                        query.page = page;
                        query.limit = limit;
                        setQuery({ ...query });
                        fetchStaff({ ...query });
                      }}
                    />
                  </div>
                ),
              },
            ]}
          />
        </Card>
      </Spin>
    </div>
  );
};
