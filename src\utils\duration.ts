export function getDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;
  if (hours > 0) {
    return `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;
  } else {
    return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
      .toString()
      .padStart(2, "0")}`;
  }
}
export function getDurationFullContent(
  seconds: number,
  hoursText = " giờ ",
  minutesText = " phút"
): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  return `${hours.toString() + hoursText}${
    minutes.toString().padStart(2, "0") + minutesText
  }`;
  if (hours > 0) {
    return `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;
  } else {
    return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
      .toString()
      .padStart(2, "0")}`;
  }
}

export const weeksToSeconds = (weeks: number) => {
  const secondsInDay = 24 * 60 * 60;
  const secondsInWeek = 7 * secondsInDay;
  return weeks * secondsInWeek;
};

// Hàm chuyển đổi số tháng thành số giây
export const monthsToSeconds = (months: number) => {
  const secondsInDay = 24 * 60 * 60;
  const secondsInMonth = 30 * secondsInDay; // Giả định một tháng có 30 ngày
  return months * secondsInMonth;
};
