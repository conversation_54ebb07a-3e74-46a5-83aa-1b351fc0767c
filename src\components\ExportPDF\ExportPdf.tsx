import { <PERSON><PERSON>, <PERSON>confirm } from "antd";
import React, { useRef } from "react";
import { useReactToPrint } from "react-to-print";

interface ExportPdfProps {
  fileName?: string;
  title?: string;
  columns: string[];
  data: any[][];
}

const TableToPrint = React.forwardRef<
  HTMLDivElement,
  {
    title: string;
    columns: string[];
    data: any[][];
  }
>(({ title, columns, data }, ref) => (
  <div ref={ref}>
    <h2 style={{ fontSize: 18, marginBottom: 12, fontWeight: "bold" }}>
      {title}
    </h2>
    <table
      style={{ width: "100%", borderCollapse: "collapse", margin: "8px 0" }}
    >
      <thead>
        <tr>
          {columns.map((col, idx) => (
            <th
              key={idx}
              style={{
                fontWeight: "bold",
                background: "#eee",
                padding: 4,
                border: "1px solid #ccc",
                fontSize: 12,
              }}
            >
              {col}
            </th>
          ))}
        </tr>
      </thead>
      <tbody>
        {data.map((row, rowIdx) => (
          <tr key={rowIdx}>
            {row.map((cell, cellIdx) => (
              <td
                key={cellIdx}
                style={{
                  padding: 4,
                  border: "1px solid #ccc",
                  fontSize: 12,
                }}
              >
                {cell}
              </td>
            ))}
          </tr>
        ))}
      </tbody>
    </table>
  </div>
));

TableToPrint.displayName = "TableToPrint";

export const ExportPdf: React.FC<ExportPdfProps> = ({
  fileName = "export.pdf",
  title = "Danh sách",
  columns,
  data,
}) => {
  const componentRef = useRef<HTMLDivElement>(null);

  const handlePrint = useReactToPrint({
    //@ts-ignore
    content: () => componentRef.current,
    documentTitle: fileName.replace(/\.pdf$/i, ""),
  });

  return (
    <>
      <Popconfirm
        title="Bạn có chắc chắn muốn xuất PDF không?"
        okText="Đồng ý"
        cancelText="Hủy"
        onConfirm={handlePrint}
      >
        <Button type="primary">Xuất PDF</Button>
      </Popconfirm>
      {/* Hidden printable content */}
      <div style={{ display: "none" }}>
        <TableToPrint
          ref={componentRef}
          title={title!}
          columns={columns}
          data={data}
        />
      </div>
    </>
  );
};
