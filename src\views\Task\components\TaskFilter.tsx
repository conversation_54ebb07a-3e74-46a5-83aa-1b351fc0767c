import React, { useState, useEffect } from "react";
import { Select, DatePicker, Space } from "antd";
import dayjs, { Dayjs } from "dayjs";
import CustomInput from "components/Input/CustomInput";
import CustomButton from "components/Button/CustomButton";
import QueryLabel from "components/QueryLabel/QueryLabel";
import { StaffSelector } from "components/Selector/StaffSelector";
import { TaskPriorityTrans, TaskStatusTrans } from "types/task";
import { MembershipSelector } from "components/Selector/MembershipSelector";
import { ProjectSelector } from "components/Selector/ProjectSelector";

interface TaskFilterProps {
  type: "assign" | "create";
  onQueryChange: (query: any) => void;
  initialQuery?: any;
}

function TaskFilter({
  type,
  onQueryChange,
  initialQuery = {},
}: TaskFilterProps) {
  // Filter states
  const [searchValue, setSearchValue] = useState("");
  const [statusFilter, setStatusFilter] = useState<string | undefined>(
    undefined
  );
  const [assigneeFilter, setAssigneeFilter] = useState<number | undefined>(
    undefined
  );
  const [dueDateFilter, setDueDateFilter] = useState<Dayjs | undefined>(
    undefined
  );
  const [priorityFilter, setPriorityFilter] = useState<string | undefined>(
    undefined
  );
  const [projectFilter, setProjectFilter] = useState<number | undefined>(
    undefined
  );

  // Chỉ set filter state từ initialQuery khi mount
  useEffect(() => {
    setSearchValue(initialQuery.search || "");
    setStatusFilter(initialQuery.status || undefined);
    setAssigneeFilter(initialQuery.assigneeMemberShipId || undefined);
    setDueDateFilter(
      initialQuery.endDate ? dayjs(initialQuery.endDate) : undefined
    );
    setPriorityFilter(initialQuery.priority || undefined);
    setProjectFilter(initialQuery.projectId || undefined);
    // eslint-disable-next-line
  }, []); // chỉ chạy 1 lần khi mount

  // Update search handler
  const handleSearch = (value: string) => {
    setSearchValue(value);
    // Only trigger search when value is empty (cleared)
    if (value === "") {
      const newQuery = {
        ...initialQuery,
        page: 1,
        search: "",
      };
      onQueryChange(newQuery);
    }
  };

  const handleSearchOnEnter = (value: string) => {
    const newQuery = {
      ...initialQuery,
      page: 1,
      search: value,
    };
    onQueryChange(newQuery);
  };

  // Update status filter handler
  const handleStatusChange = (value: string) => {
    setStatusFilter(value);
  };

  // Update assignee filter handler
  const handleAssigneeChange = (value: number) => {
    setAssigneeFilter(value || undefined);
  };

  // Update due date filter handler
  const handleDueDateChange = (date: Dayjs | null) => {
    setDueDateFilter(date || undefined);
  };

  // Update priority filter handler
  const handlePriorityChange = (value: string) => {
    setPriorityFilter(value);
  };

  // Update project filter handler
  const handleProjectChange = (value: number) => {
    setProjectFilter(value || undefined);
  };

  // Apply filters
  const handleApplyFilter = () => {
    const newQuery: any = {
      ...initialQuery,
      page: 1,
      search: searchValue,
    };

    // Add status filter
    if (statusFilter) {
      newQuery.status = statusFilter;
    } else {
      delete newQuery.status;
    }

    // Add assignee filter
    if (assigneeFilter) {
      newQuery.assigneeMemberShipId = assigneeFilter;
    } else {
      delete newQuery.assigneeMemberShipId;
    }

    // Add due date filter
    if (dueDateFilter) {
      newQuery.endDate = dueDateFilter.format("YYYY-MM-DD");
    } else {
      delete newQuery.endDate;
    }

    // Add priority filter
    if (priorityFilter) {
      newQuery.priority = priorityFilter;
    } else {
      delete newQuery.priority;
    }

    // Add project filter
    if (projectFilter) {
      newQuery.projectId = projectFilter;
    } else {
      delete newQuery.projectId;
    }

    console.log("TaskFilter - Apply Filter Query:", newQuery);

    onQueryChange(newQuery);
  };

  // Reset filters
  const handleResetFilters = () => {
    setSearchValue("");
    setStatusFilter(undefined);
    setAssigneeFilter(undefined);
    setDueDateFilter(undefined);
    setPriorityFilter(undefined);
    setProjectFilter(undefined);

    const newQuery = {
      page: 1,
      limit: 0,
      search: "",
      status: "",
      assigneeMemberShipId: 0,
      endDate: "",
      priority: "",
      projectId: "",
    };
    onQueryChange(newQuery);
  };

  const hasActiveFilters =
    searchValue !== "" ||
    (statusFilter !== "" && statusFilter !== undefined) ||
    assigneeFilter !== undefined ||
    dueDateFilter !== undefined ||
    (priorityFilter !== "" && priorityFilter !== undefined) ||
    projectFilter !== undefined;

  return (
    <div className="flex flex-wrap gap-[16px] items-end pb-[12px] justify-between">
      <div className="flex flex-wrap gap-[16px] items-end max-w-full">
        {/* Search */}
        <div className="w-[250px]">
          <CustomInput
            tooltipContent={"Tìm theo mã, tên công việc"}
            label="Tìm kiếm"
            placeholder="Tìm kiếm"
            value={searchValue}
            onChange={handleSearch}
            onPressEnter={handleSearchOnEnter}
            allowClear
          />
        </div>

        {/* Status Filter */}
        <div className="w-[200px]">
          <QueryLabel>Trạng thái</QueryLabel>
          <Select
            className="w-full"
            placeholder="Chọn trạng thái"
            allowClear
            value={statusFilter}
            onChange={handleStatusChange}
            options={Object.entries(TaskStatusTrans).map(([key, value]) => ({
              value: key,
              label: value,
            }))}
          />
        </div>

        {/* Assignee Filter */}
        <div className="w-[200px]">
          <QueryLabel>Người phụ trách</QueryLabel>
          <MembershipSelector
            value={assigneeFilter}
            onChange={handleAssigneeChange}
            allowClear={true}
            placeholder="Chọn người phụ trách"
          />
        </div>

        {/* Due Date Filter */}
        <div className="w-[200px]">
          <QueryLabel>Ngày đến hạn</QueryLabel>
          <DatePicker
            className="w-full"
            placeholder="Chọn ngày"
            value={dueDateFilter}
            onChange={handleDueDateChange}
            format="DD/MM/YYYY"
            allowClear
          />
        </div>

        {/* Priority Filter */}
        <div className="w-[200px]">
          <QueryLabel>Mức độ ưu tiên</QueryLabel>
          <Select
            className="w-full"
            placeholder="Chọn mức độ"
            allowClear
            value={priorityFilter}
            onChange={handlePriorityChange}
            options={Object.entries(TaskPriorityTrans).map(([key, value]) => ({
              value: key,
              label: value,
            }))}
          />
        </div>

        {/* Project Filter */}
        <div className="w-[200px]">
          <QueryLabel>Dự án</QueryLabel>
          <ProjectSelector
            value={projectFilter}
            onChange={handleProjectChange}
            allowClear={true}
            placeholder="Chọn dự án"
          />
        </div>

        {/* Apply Filter Button */}
        <CustomButton onClick={handleApplyFilter}>Áp dụng</CustomButton>

        {/* Clear Filter Button */}
        {hasActiveFilters && (
          <CustomButton variant="outline" onClick={handleResetFilters}>
            Bỏ lọc
          </CustomButton>
        )}
      </div>
    </div>
  );
}

export default TaskFilter;
