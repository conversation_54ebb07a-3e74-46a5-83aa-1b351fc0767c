import { FileAttach } from "./fileAttach";
import { Project } from "./project";
import { Staff } from "./staff";

export interface Report {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  code: string;
  type: ReportType; // Lo<PERSON>i báo c<PERSON>o (ngày, tuần, tháng)
  note: string;
  startAt: number;
  endAt: number;
  workVolume: number;
  content: string;
  rain: string; // lượng mưa
  temperature: string; // nhiệt độ
  project: Project; // Nhập dự án
  reportDetails: ReportDetail[];
  // info create update
  createdBy: Staff;
  updatedBy: Staff;
  fileAttaches: FileAttach[];
}

export interface ReportDetail {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  status: ReportDetailStatus; // Status
  task: string;
  startDate?: string; // Ngày bắt đầu
  endDate?: string; // Ng<PERSON>y kết thúc
  progressPercent: number; // Work status done (%)
  dailyImages?: string; // Ảnh công tác thi công trong ngày (đường dẫn)
  taskInput?: string; // Nhập công việc
  weather: string; // Thời tiết
  // custom
  report: Report;
  project: Project;
}

export enum ReportType {
  Daily = "DAILY",
  Weekly = "WEEKLY",
  Monthly = "MONTHLY",
}

export enum ReportDetailStatus {
  Pending = "PENDING",
  InProgress = "IN_PROGRESS",
  Completed = "COMPLETED",
}
