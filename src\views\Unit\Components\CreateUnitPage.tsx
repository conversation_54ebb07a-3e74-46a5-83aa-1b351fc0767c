import {
  <PERSON><PERSON>,
  Card,
  Col,
  Divider,
  Form,
  Input,
  message,
  Modal,
  Row,
  Select,
} from "antd";
import TextArea from "antd/es/input/TextArea";
import { Rule } from "antd/lib/form";
import { providerApi } from "api/provider.api";
import { unitApi } from "api/unit.api";
import CustomButton from "components/Button/CustomButton";
import CustomInput from "components/Input/CustomInput";
import CustomSelect from "components/Input/CustomSelect";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { useNavigate } from "react-router-dom";
import { FileAttach } from "types/fileAttach";
import { ModalStatus } from "types/modal";
import { Provider, ProviderTypeTrans } from "types/provider";
import { UnitTypeTrans } from "types/unit";
import { getTitle } from "utils";

const rules: Rule[] = [{ required: true }];

export const CreateUnitPage = ({ title = "" }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    document.title = getTitle(title);
  }, []);

  const createData = async () => {
    const valid = await form.validateFields();
    const data = { unit: form.getFieldsValue() };

    setLoading(true);
    try {
      const res = await unitApi.create(data);
      message.success("Tạo đơn vị thành công!");
      navigate("/master-data/unit-list");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <div className="font-bold text-2xl mb-[20px]">Tạo đơn vị</div>
      <Card>
        <Form layout="vertical" form={form}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="Mã đơn vị" name="code">
                <CustomInput placeholder="Nếu không điền hệ thống sẽ tự sinh mã" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Tên đơn vị" name="name" rules={rules}>
                <CustomInput placeholder="Nhập tên" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Loại đơn vị" name="type" rules={rules}>
                <CustomSelect
                  placeholder="Chọn loại dịch vụ"
                  options={Object.values(UnitTypeTrans).map((item) => ({
                    label: item.label,
                    value: item.value,
                  }))}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Ký hiệu" name="symbol" rules={rules}>
                <CustomInput placeholder="Nhập ký hiệu" />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item label="Mô tả" name="description">
                <CustomInput placeholder="Mô tả" type="textarea" rows={6} />
              </Form.Item>
            </Col>
          </Row>
        </Form>
        <div className="flex gap-[16px] justify-end mt-2">
          <CustomButton
            variant="outline"
            className="cta-button"
            onClick={() => {
              navigate("/master-data/unit-list");
            }}
          >
            Hủy
          </CustomButton>
          <CustomButton
            className="cta-button"
            loading={loading}
            onClick={() => {
              createData();
            }}
          >
            Tạo đơn vị
          </CustomButton>
        </div>
      </Card>
    </div>
  );
};
