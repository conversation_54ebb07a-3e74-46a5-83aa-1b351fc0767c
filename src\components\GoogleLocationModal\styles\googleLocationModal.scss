.address-modal {
    .pac-target-input {
      border: 1px solid #efefef;
      border-radius: 5px;
      padding-left: 10px;
      margin-bottom: 20px;
  
      &:focus {
        outline: none;
        border-color: #666;
      }
    }
  }
  .pac-container {
    z-index: 1201 !important;
  }
  .custom-link {
    .ant-input-group-addon:last-child {
      padding: 0;
      background-color: transparent;
    }
  
    // button {
    //   border-radius: 0 0.475rem 0.475rem 0 !important;
    //   border: none !important;
    // }
  }
  
  .custom-input {
    border-radius: 0.475rem 0.9rem 0.9rem 0.475rem !important;
  }
  
  // .custom-link .ant-btn-primary {
  //   // background-color: #a6ce80;
  //   border: none;
  //   border-radius: 6px;
  //   font-weight: bold;
  //   height: 100%; /* <PERSON><PERSON><PERSON> bảo chiều cao bằng input */
  //   display: flex;
  //   align-items: center;
  //   justify-content: center;
  //   padding: 0 12px; /* Thêm padding ngang */
  //   white-space: nowrap; /* <PERSON><PERSON><PERSON> bảo chữ không bị xuống dòng */
  // }
  
  .custom-link .ant-input-group-addon {
    padding: 0;
    // border: none;
    height: 100%; /* Căn chỉnh chiều cao với input */
  }
  
  .custom-link .ant-input-affix-wrapper {
    display: flex;
    align-items: center;
    height: 40px; /* Hoặc điều chỉnh theo chiều cao mong muốn */
  }
  
  .address-modal {
    .ant-modal-footer {
      display: flex;
      justify-content: flex-end;
      gap: 12px; // Khoảng cách giữa nút "Lưu" và "Đóng", bạn có thể tăng/giảm
    }
  }
  