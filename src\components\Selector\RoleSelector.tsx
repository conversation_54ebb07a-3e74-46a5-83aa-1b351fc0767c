import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
} from "react";
import { debounce, uniqBy } from "lodash";
import { Role } from "types/role";
import { QueryParams2 } from "types/query";
import CustomSelect from "components/Input/CustomSelect";
import { useRole } from "hooks/useRole";
import { Select, SelectProps } from "antd";

type CustomFormItemProps = {
  value?: number;
  disabled?: boolean;
  selectedRole?: Role[];
  selectProps?: SelectProps;
  multiple?: boolean;
  onChange?: (value: any) => void;
  initOptionItem?: Role | Role[];
  valueIsOption?: boolean;
  allowClear?: boolean;
  placeholder?: string;
  label?: string;
  addonOptions?: any[];
  showSearch?: boolean;
};

export interface RoleSelector {
  refresh(): void;
}

export const RoleSelector = forwardRef(
  (
    {
      value,
      label,
      onChange,
      disabled,
      multiple = false,
      selectedRole,
      initOptionItem,
      valueIsOption,
      allowClear = true,
      placeholder = "",
      addonOptions = [],
      showSearch = true,
      selectProps,
    }: CustomFormItemProps,
    ref
  ) => {
    const { roles, fetchRole } = useRole({
      initQuery: { page: 1, limit: 100 },
    });

    useImperativeHandle<any, RoleSelector>(
      ref,
      () => ({
        refresh() {
          fetchRole();
        },
      }),
      []
    );

    useEffect(() => {
      fetchRole();
    }, [selectedRole]);

    const options = useMemo(() => {
      let data = [...roles];
      if (initOptionItem) {
        if ((initOptionItem as Role[])?.length) {
          data = data.concat(initOptionItem);
        } else {
          data.push(initOptionItem as Role);
        }
      }
      return uniqBy([...addonOptions, ...data], (item) => item.id).map(
        (item) => ({
          label: item.name,
          value: item.id,
          item, // lưu nguyên object nếu cần dùng sau
        })
      );
    }, [roles, initOptionItem, addonOptions]);

    const handleChange = (v: any, option: any) => {
      if (valueIsOption) {
        if (option instanceof Array) {
          onChange?.(option.map((opt) => opt.item));
        } else {
          onChange?.(option.item);
        }
      } else {
        onChange?.(v);
      }
    };
    return (
      <Select
        {...selectProps}
        value={value}
        onChange={handleChange}
        disabled={disabled}
        options={options}
        mode={multiple ? "multiple" : undefined}
        allowClear={allowClear}
        placeholder={placeholder}
        showSearch={showSearch}
        filterOption={true}
      />
    );
  }
);
