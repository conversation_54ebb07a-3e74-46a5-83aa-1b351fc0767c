import { result } from "lodash";
import { TableColumnProps } from "antd";
import { MyExcelColumn } from "./MyExcel";
import { Cell, Workbook, WorksheetState } from "exceljs";

export type MyTableColumn<T = any> = Omit<
  MyExcelColumn<T>,
  "render" | "eachCell"
> &
  TableColumnProps<T> & {
    fixed?: "true" | "left" | "right";
    excelOnly?: boolean;
    antdTableOnly?: boolean;
    excelWidth?: number;
    excelHeader?: string;
    excelRender?: (data: T & { errorMessage: string }) => any;
    eachCell?: (cell: Cell, rowNumber: number) => void;
  };

/**
 * Thêm sheet mới vào workbook
 * @param workbook
 * @param sheetName
 * @param data
 * @returns
 */
export const addNewSheet = (
  workbook: Workbook,
  sheetName: string,
  data: string[],
  state: WorksheetState = "visible"
) => {
  const sheet = workbook.addWorksheet(sheetName, { state });
  // Thêm dữ liệu
  data.forEach((name: string, index: number) => {
    const row = sheet.getRow(index + 1);
    row.getCell(1).value = name;
  });
  sheet.getColumn(1).width = 30;
  return sheet;
};
