import { Select } from "antd";
import { DefaultOptionType } from "antd/es/select";
import { SelectProps } from "antd/lib";
import { projectApi } from "api/project.api";
import { useProject } from "hooks/useProject";
import { debounce, isEmpty, uniqBy } from "lodash";
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
} from "react";
import { Project } from "types/project";
import { QueryParams2 } from "types/query";

type CustomFormItemProps = {
  value?: number;
  initQuery?: QueryParams2;
  disabled?: boolean;
  selectedProject?: Project[];
  multiple?: boolean;
  onChange?: (value: any) => void;
  selectProps?: SelectProps;
  initOptionItem?: Project | Project[];
  valueIsOption?: boolean;
  allowClear?: boolean;
  placeholder?: string;
};

export interface ProjectSelector {
  refresh(): void;
}

/**
 * A demo that changes it's number value on click.
 * @param value initialValue passed by parent Form.Item.
 * @param onChange a callback for Form.Item to read its child component's value.
 * @constructor
 */
export const ProjectSelector = forwardRef(
  (
    {
      value,
      onChange,
      initQuery,
      disabled,
      multiple = false,
      selectedProject,
      initOptionItem,
      valueIsOption,
      selectProps,
      allowClear = true,
      placeholder = "Chọn dự án",
    }: CustomFormItemProps,
    ref
  ) => {
    const {
      projects,
      total,
      loading,
      fetchData,
      query,
      setData: setProjects,
      isFetched,
    } = useProject({
      initQuery: {
        page: 1,
        limit: 50,
        ...initQuery,
      },
    });

    useImperativeHandle<any, ProjectSelector>(
      ref,
      () => ({
        refresh() {
          fetchData();
        },
      }),
      []
    );

    useEffect(() => {
      fetchData();
    }, [selectedProject]);

    const debounceSearch = useCallback(
      debounce((keyword) => {
        query.search = keyword;
        fetchData();
      }, 300),
      [query]
    );

    //xử lý nếu trong projects thiếu value hiện tại thì add vào
    useEffect(() => {
      if (value && isFetched) {
        const find = projects.find((e) => e.id == value);
        if (!find) {
          projectApi.findOne(value).then((res) => {
            if (!isEmpty(res.data)) {
              setProjects((prev) => [res.data, ...prev]);
            }
          });
        }
      }
    }, [value, projects, isFetched]);

    const options = useMemo(() => {
      let data = [...projects];
      if (initOptionItem) {
        if ((initOptionItem as Project[])?.length) {
          data = data.concat(initOptionItem);
        } else {
          data.push(initOptionItem as Project);
        }
      }

      return uniqBy(data, (data) => data.id);
    }, [projects, initOptionItem]);

    return (
      <Select<any, DefaultOptionType>
        mode={multiple ? "multiple" : undefined}
        disabled={disabled}
        loading={loading}
        style={{ width: "100%" }}
        allowClear={allowClear}
        onSearch={debounceSearch}
        showSearch
        placeholder={placeholder}
        filterOption={false}
        value={value}
        onChange={(v, opts) => {
          if (v === undefined || v.length == 0) {
            debounceSearch(v);
          }
          if (valueIsOption) {
            if (opts instanceof Array) {
              onChange?.(opts?.map((v) => v.item));
            } else {
              onChange?.(opts?.item);
            }
          } else {
            // Trả về giá trị id trực tiếp thay vì object
            onChange?.(v);
          }
        }}
        {...selectProps}
      >
        {options?.map((item) => (
          <Select.Option item={item} value={item.id} key={item.id}>
            <div className="flex items-center gap-2">
              <span>{item.name}</span>
            </div>
          </Select.Option>
        ))}
      </Select>
    );
  }
);
