import { Component } from "./component";
import { Material } from "./material";
import { Product } from "./product";
import { Variant } from "./variant";

export interface OrderDetailComponent {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  unitOfMaterial: number;
  extraPrice: number; // gia cong them
  component: Component;
  material: Material;
  variant: Variant;
  orderDetail: OrderDetail;
}

export interface OrderDetail {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  name: string;
  quantity: number;
  price: number; //  giá product
  totalExtraPrice: number; // tổng giá mở rộng
  totalPrice: number; // tông giá = giá product + tổng giá mở rộng
  fabricPrice: number; // giá của vải
  order: Order;
  product: Product; // Product Single như cái Quần, cái áo
  orderDetailComponents: OrderDetailComponent[];
}

export interface Order {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  code: string;
  customerPhone: string; // số điện thoại khách hàng
  customerName: string;
  address: string;
  note: string;
  moneyFinal: number;
  moneyOrigin: number; // giá ban đầu
  moneyExtra: number; // giá công thêm
  moneyTotal: number;
  link: string; // link
  orderDetails: OrderDetail[];
  product: Product;
}
