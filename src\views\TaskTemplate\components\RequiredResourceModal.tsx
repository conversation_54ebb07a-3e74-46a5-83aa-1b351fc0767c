import { Col, Form, Input, message, Modal, Row, Tabs } from "antd";
import { Rule } from "antd/lib/form";
import React, { Key, useEffect, useImperativeHandle, useState } from "react";
import { ModalStatus } from "types/modal";
import { RequiredResource } from "types/requiredResource";
import { DevicePage } from "views/DevicePage/DevicePage";
import { Device, DeviceType } from "types/device";

export type TabRequiredResource = "device" | "machine";

const rules: Rule[] = [{ required: true }];

export interface RequiredResourceModal {
  handleView: (
    tab: TabRequiredResource,
    selectedRowDevices: Device[],
    selectedRowMachines: Device[]
  ) => void;
}
interface RequiredResourceModalProps {
  onSubmitOk: (
    selectedRowDevice: Device[],
    selectedRowMachine: Device[]
  ) => void;
}

export const RequiredResourceModal = React.forwardRef(
  ({ onSubmitOk }: RequiredResourceModalProps, ref) => {
    const [visible, setVisible] = useState(false);
    const [tab, setTab] = useState<TabRequiredResource>("device");
    const [selectedRowDevice, setSelectedRowDevice] = useState<Device[]>([]);
    const [selectedRowMachine, setSelectedRowMachine] = useState<Device[]>([]);

    useImperativeHandle<any, RequiredResourceModal>(
      ref,
      () => ({
        handleView,
      }),
      []
    );

    const handleView = (
      tab: TabRequiredResource,
      selectedRowDevices: Device[],
      selectedRowMachines: Device[]
    ) => {
      setTab(tab);
      setVisible(true);
      setSelectedRowDevice(selectedRowDevices);
      setSelectedRowMachine(selectedRowMachines);
    };

    const handleClose = () => {
      setVisible(false);
    };

    const handleChangeRowSelect =
      (type: TabRequiredResource) =>
      (selectedRowKeys: Key[], selectedRows: Device[]) => {
        if (type == "device") {
          setSelectedRowDevice(selectedRows);
        } else {
          setSelectedRowMachine(selectedRows);
        }
      };

    return (
      <Modal
        onCancel={handleClose}
        visible={visible}
        title="Chọn tài nguyên cần thiết"
        style={{ top: 20 }}
        width={1200}
        onOk={() => {
          handleClose();
          onSubmitOk(selectedRowDevice, selectedRowMachine);
        }}
        cancelButtonProps={{ style: { display: "none" } }}
        okText="Xong"
      >
        <Tabs defaultActiveKey={tab} type="line" className="mt-[16px]">
          <Tabs.TabPane tab="Vật tư" key="device">
            <DevicePage
              type={DeviceType.Equipment}
              showSelect
              rowSelection={{
                type: "checkbox",
                selectedRowKeys: selectedRowDevice.map((e) => e.id),
                onChange: handleChangeRowSelect("device"),
              }}
            />
          </Tabs.TabPane>

          <Tabs.TabPane tab="Máy thi công" key="machine">
            <DevicePage
              type={DeviceType.Machine}
              showSelect
              rowSelection={{
                type: "checkbox",
                selectedRowKeys: selectedRowMachine.map((e) => e.id),
                onChange: handleChangeRowSelect("machine"),
              }}
            />
          </Tabs.TabPane>
        </Tabs>
      </Modal>
    );
  }
);
