import React from "react";
import { Notification } from "types/notification";
import { formatDate } from "utils/date";
import CalendarIcon from "assets/svgs/CalendarIcon";
import BellNotificationIcon from "assets/svgs/BellNotification";
import "./CardNotification.scss";

interface CardNotificationProps {
  notification: Notification;
  onClick?: (notificationId: number, url: string) => void;
  className?: string;
}

const CardNotification: React.FC<CardNotificationProps> = ({
  notification,
  onClick,
  className = "",
}) => {
  const handleClick = () => {
    if (onClick) {
      onClick(notification.id, notification.url);
    }
  };

  return (
    <div
      className={`notification-item cursor-pointer ${
        notification.isRead
          ? "notification-item--read"
          : "notification-item--unread"
      } ${className}`}
      onClick={handleClick}
    >
      <div className="notification-item-header">
        <div className="notification-item-icon">
          <BellNotificationIcon
            isRead={notification.isRead}
            width={30}
            height={30}
          />
        </div>
        <div className="notification-item-info">
          <div className="notification-item-title">{notification.title}</div>
          <div className="notification-item-content">
            {notification.content}
          </div>
          <div className="flex items-center justify-end gap-2 my-2 mr-4">
            <CalendarIcon />
            <div className="text-[12px]">
              {formatDate(notification.createdAt, "DD/MM/YYYY HH:mm")}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CardNotification;
