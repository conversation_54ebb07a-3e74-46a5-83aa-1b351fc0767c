import {
  DeleteOutlined,
  EditOutlined,
  PlusCircleOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import {
  Button,
  Card,
  Divider,
  Input,
  message,
  Modal,
  Space,
  Spin,
  Table,
  Tooltip,
  TreeDataNode,
} from "antd";
import { dictionaryApi } from "api/dictionary.api";
import { Pagination } from "components/Pagination";
import React, { useEffect, useState, useRef } from "react";
import { ModalStatus } from "types/modal";
import { QueryParam } from "types/query";
import {
  Dictionary,
  DictionaryType,
  DictionaryTypeForRoleCheck,
} from "types/dictionary";
import { getTitle } from "utils";
import { $url } from "utils/url";
import { DictionaryModal } from "./components/DictionaryModal";
import { useDictionary } from "hooks/useDictionary";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import { checkRole } from "utils/auth";
import { PermissionNames } from "types/PermissionNames";
import { permissionStore } from "store/permissionStore";
import { observer } from "mobx-react";
import PencilIcon from "assets/svgs/PencilIcon";
import { useNavigate } from "react-router-dom";
import DeleteIcon from "assets/svgs/DeleteIcon";
import CustomButton from "components/Button/CustomButton";
import CustomInput from "components/Input/CustomInput";
import PageTitle from "components/PageTitle/PageTitle";
import { isTypeTreeData, removeSubstringFromKeys } from "utils/common";
import { ExcelImportButton } from "components/ExcelImportButton/ExcelImportButton";
import { ReactComponent as SearchIcon } from "assets/svgs/search.svg";
import { userStore } from "store/userStore";
import { PermissionType } from "types/permission";
import EditButton from "components/Button/EditButton";
import DeleteButton from "components/Button/DeleteButton";
import { TreeDataItem, TreeDataTableView } from "components/TreeDataTable";
import {
  HierarchicalView,
  HierarchicalItem,
} from "components/HierarchicalView";

const { ColumnGroup, Column } = Table;

const getPermissionNameForDictionaryType = (
  type: string,
  permission: string
) => {
  return `${type}/${permission}`;
};

export const DictionaryPage = observer(
  ({ title = "", type }: { title: string; type: DictionaryType }) => {
    const hasCreate = checkRole(
      getPermissionNameForDictionaryType(
        DictionaryTypeForRoleCheck[type],
        PermissionType.Add
      ),
      permissionStore.permissions
    );
    const hasEdit = checkRole(
      getPermissionNameForDictionaryType(
        DictionaryTypeForRoleCheck[type],
        PermissionType.Edit
      ),
      permissionStore.permissions
    );
    const hasDelete = checkRole(
      getPermissionNameForDictionaryType(
        DictionaryTypeForRoleCheck[type],
        PermissionType.Delete
      ),
      permissionStore.permissions
    );

    const hasViewAll = checkRole(
      getPermissionNameForDictionaryType(
        DictionaryTypeForRoleCheck[type],
        PermissionType.ViewAll
      ),
      permissionStore.permissions
    );
    console.log("hasViewAll", hasViewAll);

    const navigate = useNavigate();

    const { dictionaries, fetchData, loading, query, setQuery, total } =
      useDictionary({
        initQuery: {
          page: 1,
          limit: 20,
          type,
          isAdmin: hasViewAll ? true : undefined,
        },
      });

    const [treeData, setTreeData] = useState<TreeDataNode[]>([]);
    const [expandedKeys, setExpandedKeys] = useState<Set<number>>(new Set());
    const [hierarchicalData, setHierarchicalData] = useState<
      HierarchicalItem[]
    >([]);

    const modalRef = useRef<DictionaryModal>(null);

    useEffect(() => {
      document.title = getTitle(title);
      fetchData();
    }, []);

    useEffect(() => {
      if (dictionaries.length > 0 && isTypeTreeData(type)) {
        setTreeData(getTreeData(dictionaries));
        setHierarchicalData(convertToHierarchicalData(dictionaries));
      } else {
        setTreeData([]);
        setHierarchicalData([]);
      }
    }, [dictionaries, type]);

    const handleDeleteDictionary = async (id: number) => {
      try {
        await dictionaryApi.delete(id);
        message.success("Xóa cấu hình thành công!");
        fetchData();
      } catch (e) {
        console.log({ e });
      } finally {
      }
    };

    const excelColumns = [
      {
        header: "Tên",
        key: "name",
        required: true,
        dependColumn: "",
      },
    ];

    // if (DictionaryTreeTypes.includes(type)) {
    //   excelColumns.push({
    //     header: "Tên cha",
    //     key: "parentName",
    //     required: false,
    //     dependColumn: "A",
    //   });
    // }

    const handleProcessImportData = (
      results: any[],
      setData: (data: any[]) => void
    ) => {
      const importData = results?.map((item: any) => {
        const refineRow = removeSubstringFromKeys(item, " *");
        const name = refineRow["Tên"];
        // const parentName = refineRow["Tên cha"];

        return {
          name,
          // parentName,
          type,
          rowNum: item.__rowNum__,
        };
      });

      setData(importData);
    };

    const convertToHierarchicalData = (
      dictionaries: Dictionary[],
      level = 0,
      parentId?: number
    ): HierarchicalItem[] => {
      return dictionaries.map((dictionary) => ({
        id: dictionary.id,
        name: dictionary.name,
        code: dictionary.code,
        parentId,
        level,
        children: convertToHierarchicalData(
          dictionary.children || [],
          level + 1,
          dictionary.id
        ),
        isActive: dictionary.isActive,
        description: `ID: ${dictionary.id}${
          dictionary.isActive ? "" : " (Không hoạt động)"
        }`,
      }));
    };

    const getTreeData = (
      dictionaries: Dictionary[],
      level = 0
    ): TreeDataNode[] => {
      return dictionaries.map((dictionary) => {
        const isLeaf = dictionary.children.length === 0;
        return {
          key: dictionary.id,
          title: (
            <div className="flex gap-2">
              <div>{dictionary.name}</div>
              <div className="flex gap-2 items-center hover:opacity-100 opacity-0">
                {hasCreate && (
                  <PlusCircleOutlined
                    className="text-green-600"
                    onClick={() => {
                      modalRef.current?.handleCreate(dictionary.id);
                    }}
                  />
                )}
                {hasEdit && (
                  <EditOutlined
                    className="text-blue-600"
                    onClick={() => {
                      modalRef.current?.handleUpdate(dictionary);
                    }}
                  />
                )}
                {hasDelete && (
                  <DeleteOutlined
                    className="text-red-600"
                    onClick={() => {
                      confirmDeleteDictionary(dictionary);
                    }}
                  />
                )}
              </div>
            </div>
          ),
          children:
            dictionary.children.length > 0
              ? getTreeData(dictionary.children, level + 1)
              : [],
          selectable: false,
        };
      });
    };

    const handleHierarchicalAdd = (parentId?: number) => {
      modalRef.current?.handleCreate(parentId);
    };

    const handleHierarchicalEdit = (
      item: HierarchicalItem,
      parentId?: number
    ) => {
      const dictionary = findDictionaryById(dictionaries, item.id);
      if (dictionary) {
        modalRef.current?.handleUpdate(dictionary, parentId);
      }
    };

    const handleHierarchicalDelete = (item: HierarchicalItem) => {
      const dictionary = findDictionaryById(dictionaries, item.id);
      if (dictionary) {
        confirmDeleteDictionary(dictionary);
      }
    };

    const findDictionaryById = (
      dictionaries: Dictionary[],
      id: number
    ): Dictionary | null => {
      for (const dict of dictionaries) {
        if (dict.id === id) return dict;
        if (dict.children && dict.children.length > 0) {
          const found = findDictionaryById(dict.children, id);
          if (found) return found;
        }
      }
      return null;
    };

    const confirmDeleteDictionary = (record: Dictionary) => {
      Modal.confirm({
        title: `Xóa cấu hình ${record.name}`,
        getContainer: () => {
          return document.getElementById("App") as HTMLElement;
        },
        icon: null,
        content: (
          <>
            <div>
              Bạn sẽ không thể khôi phục dữ liệu này sau khi xóa.
              <br />
              Các dữ liệu con thuộc cấu hình này cũng sẽ bị xóa.
              <br />
              Bạn có chắc chắn muốn xóa dữ liệu này?
            </div>
          </>
        ),
        footer: (_, { OkBtn, CancelBtn }) => (
          <>
            <CustomButton
              variant="outline"
              className="cta-button"
              onClick={() => {
                handleDeleteDictionary(record.id);
                Modal.destroyAll();
              }}
            >
              Có
            </CustomButton>
            <CustomButton
              onClick={() => {
                Modal.destroyAll();
              }}
              className="cta-button"
            >
              Không
            </CustomButton>
          </>
        ),
      });
    };

    const columns: CustomizableColumn<Dictionary>[] = [
      {
        title: "STT",
        dataIndex: "name",
        align: "center",
        key: "STT",
        width: 50,
        render: (_, record, i) => i + 1,
      },
      {
        title: "Tên",
        dataIndex: "name",
        key: "name",
        render: (_, record) => (
          <div
            className="flex items-center gap-2 text-[#1677ff] cursor-pointer"
            onClick={() => {
              modalRef.current?.handleUpdate(record);
            }}
          >
            {record.name}
          </div>
        ),
      },
      {
        title: "Xử lý",
        key: "actions",
        fixed: "right",
        width: 100,
        align: "center",
        alwaysVisible: true,
        render: (_, record) => (
          <Space>
            {hasEdit && (
              <EditButton
                onClick={() => {
                  modalRef.current?.handleUpdate(record);
                }}
              />
            )}
            {hasDelete && (
              <DeleteButton
                onClick={() => {
                  confirmDeleteDictionary(record);
                }}
              />
            )}
          </Space>
        ),
      },
    ];

    return (
      <div className="app-container">
        <PageTitle
          breadcrumbs={["Cấu hình", title]}
          title={title}
          extra={
            <Space>
              {hasCreate && (
                <>
                  <CustomButton
                    size="small"
                    showPlusIcon
                    onClick={() => {
                      modalRef.current?.handleCreate();
                    }}
                  >
                    Thêm mới
                  </CustomButton>
                  <ExcelImportButton
                    columns={excelColumns}
                    templateFileName={`file_mau_nhap_${title
                      .toLowerCase()
                      .replace(/\s+/g, "_")}.xlsx`}
                    onImportSuccess={() => {
                      query.page = 1;
                      fetchData();
                    }}
                    sheetName={`DS ${title}`}
                    onProcessImportData={handleProcessImportData}
                    guide={[
                      "Vui lòng tải và sử dụng file mẫu để import dữ liệu đúng cách",
                      "Không được thay đổi tiêu đề trong file csv/excel mẫu để tránh import thiếu dữ liệu",
                      "Tên cấu hình không được để trống",
                    ]}
                    buttonText="Nhập excel"
                    buttonSize="small"
                    type={type}
                    validationAPIs={{
                      Dictionary: {
                        api: dictionaryApi.findAll,
                        dataField: "dictionaries",
                        query: { type, page: 1, limit: 0 },
                        propField: "name",
                      },
                    }}
                  />
                </>
              )}
            </Space>
          }
        />
        <Card>
          <Space className="items-end gap-[16px] pb-[12px]">
            <div className="w-[250px]">
              <CustomInput
                label="Tìm kiếm"
                placeholder="Tìm kiếm"
                value={query.search}
                onPressEnter={() => {
                  query.page = 1;
                  setQuery({ ...query });
                  fetchData();
                }}
                onChange={(value) => {
                  query.search = value;
                  setQuery({ ...query });

                  if (!value) {
                    fetchData();
                  }
                }}
                allowClear
              />
            </div>

            <CustomButton
              onClick={() => {
                query.page = 1;
                setQuery({ ...query });
                fetchData();
              }}
            >
              Áp dụng
            </CustomButton>
          </Space>

          {isTypeTreeData(type) ? (
            <>
              <HierarchicalView
                data={hierarchicalData}
                onAdd={handleHierarchicalAdd}
                onEdit={handleHierarchicalEdit}
                onDelete={handleHierarchicalDelete}
                canAdd={hasCreate}
                canEdit={hasEdit}
                canDelete={hasDelete}
                maxDepth={5}
                showDescription={true}
                expandedKeys={expandedKeys}
                onExpandedKeysChange={setExpandedKeys}
              />
            </>
          ) : (
            <>
              <CustomizableTable
                key={type}
                columns={columns}
                dataSource={dictionaries}
                rowKey="id"
                loading={loading}
                pagination={false}
                bordered
                displayOptions
                onRowClick={(record) => {
                  modalRef.current?.handleUpdate(record);
                }}
              />

              <Pagination
                currentPage={query.page}
                defaultPageSize={query.limit}
                total={total}
                onChange={({ limit, page }) => {
                  query.page = page;
                  query.limit = limit;
                  setQuery({ ...query });
                  fetchData();
                }}
              />
            </>
          )}
        </Card>

        <DictionaryModal
          type={type}
          onSubmitOk={fetchData}
          onClose={() => {}}
          ref={modalRef}
        />
      </div>
    );
  }
);
