#!/usr/bin/env sh

# abort on errors
set -e

echo '====================================================================================='
echo '=============================...DEPLOYING PRODUCTION...============================='
echo '====================================================================================='

npm run build:prod

echo '====================================================================================='
echo '=====================================...BUILD...====================================='
echo '====================================================================================='

cp .htaccess dist
cp UPDATE.md dist
cd dist

git init
git add -A
git commit -m 'deploy'
git branch -M master

echo '====================================================================================='
echo '==================================...PUSHING GIT...=================================='
echo '====================================================================================='
git push -f 
# git push -f 252 master

cd -

rm -rf dist

green=`tput setaf 2`
reset=`tput sgr0`
now=$(date +"%T")

echo "${green}====================================================================================="
echo "${green}=======================...DEPLOY SUCCESS PRODUCTION AT $now...======================"
echo "${green}=====================================================================================${reset}"
