.login-page {
  min-height: 100vh;
  background: #f2f2f7;
  display: flex;
  justify-content: center;

  .login-container {
    width: 450px;
    box-shadow: rgb(16 25 40 / 8%) 0px 4px 8px 0px;
    padding: 24px 40px;
    background-color: #fff;
    border-radius: 5px;
  }

  .ant-input-affix-wrapper {
    input.ant-input {
      // border: 1px solid #d9d9d9 !important;
      // padding: 4px 11px !important;
      margin-left: 0 !important; // ghi đè global margin-left: 12px
      height: 100% !important;
      padding-left: 8px !important;
    }

    // Reset lại prefix margin
    .ant-input-prefix {
      margin-left: 8px !important; // ghi đè global margin-left: 16px
      margin-right: 8px !important; // ghi đè global margin-right: -10px
    }

    // Fix autofill overlapping icons
    input:-webkit-autofill,
    input:-webkit-autofill:hover,
    input:-webkit-autofill:focus,
    input:-webkit-autofill:active {
      -webkit-box-shadow: 0 0 0 30px white inset !important;
      background: transparent !important;
      padding-left: 8px !important; // Ko cho auto fill đè lên icon
    }
  }
}

.login-form {
  .ant-input-affix-wrapper {
    border-radius: 5px;

    input.ant-input {
      // border: 1px solid #d9d9d9 !important;
      // padding: 4px 11px !important;
      margin-left: 0 !important; // ghi đè global margin-left: 12px
      height: 100% !important;
      padding-left: 8px !important;
    }

    // Reset lại prefix margin
    .ant-input-prefix {
      margin-left: 8px !important; // ghi đè global margin-left: 16px
      margin-right: 8px !important; // ghi đè global margin-right: -10px
    }

    // Fix autofill overlapping icons
    input:-webkit-autofill,
    input:-webkit-autofill:hover,
    input:-webkit-autofill:focus,
    input:-webkit-autofill:active {
      -webkit-box-shadow: 0 0 0 30px white inset !important;
      background: transparent !important;
      padding-left: 8px !important; // Ko cho auto fill đè lên icon
    }
  }
}

// Dark mode styles
.dark .login-page {
  background: #141414;

  .login-container {
    background: #1f1f1f;
  }

  // Input styles for dark mode
  .ant-input-affix-wrapper {
    background-color: #262626;
    border-color: #404040;

    .ant-input {
      background-color: #262626;
      color: #fff;
    }

    .ant-input::placeholder {
      color: #8c8c8c;
    }

    // Icon colors
    .anticon {
      color: #8c8c8c;
    }
  }

  .ant-input-affix-wrapper:hover {
    background-color: #262626;
    border-color: #1890ff;
  }

  .ant-input-affix-wrapper-focused {
    background-color: #262626;
    border-color: #1890ff;
  }

  // Form labels
  .ant-form-item-label > label {
    color: #fff;
  }

  // Error messages
  .ant-form-item-explain-error {
    color: #ff4d4f;
  }

  // Version text
  div:last-child {
    color: #8c8c8c;
  }
}
