import { DownloadOutlined, InboxOutlined } from "@ant-design/icons";
import { Alert, Modal, Space, Spin, Table, Upload, message } from "antd";
import { Rule } from "antd/es/form";
import { serviceApi } from "api/service.api";
import { readerData } from "utils/excel2";
import CustomButton from "components/Button/CustomButton";
import { forwardRef, useImperativeHandle, useState, useEffect } from "react";
import { companyApi } from "api/company.api";
import { Company } from "types/company";
import ImportPreviewModule from "./ImportPreviewModule";
import { validateNumber } from "utils/validateRule";
import { Link } from "react-router-dom";

const { Dragger } = Upload;

const rules: Rule[] = [{ required: true }];

export interface ImportCompanyModal {
  open: () => void;
  close: () => void;
}

export interface IValidate {
  index: number;
  message: string;
}

interface DataImportReturn {
  msg: string;
  rowNum: number;
  status: string;
}

export interface CompanyImport extends Company {
  rowNum: number;
}

interface IProps {
  onSuccess?: () => void;
  createApi?: (data: any) => any;
  onUploaded?: (excelData: any, setData: (data: any[]) => void) => void;
  demoExcel?: string;
  guide?: React.ReactNode[];
  uploadText?: string;
  okText?: string;
  onClose?: () => void;
  titleText?: string;
  validateMessage?: IValidate[];
  onDownloadDemoExcel?: () => void;
  loadingDownloadDemo?: boolean;
}

const ImportCompanies = forwardRef((props: IProps, ref) => {
  const {
    onSuccess,
    createApi,
    onUploaded,
    onClose,
    validateMessage,
    guide,
    demoExcel,
    uploadText = "Kéo thả hoặc click vào đây để upload file",
    okText = "Nhập dữ liệu ngay",
    titleText = "Nhập excel dữ liệu",
    onDownloadDemoExcel,
    loadingDownloadDemo,
  } = props;

  const [visible, setVisible] = useState(false);
  const [dataPosts, setDataPosts] = useState<CompanyImport[]>([]);
  const [dataReturn, setDataReturn] = useState<{
    data: DataImportReturn[];
    successCount: number;
    errorCount: number;
  }>();
  const [loading, setLoading] = useState(false);
  const [hasValidationErrors, setHasValidationErrors] = useState(false);

  useImperativeHandle(ref, () => ({
    open: () => setVisible(true),
    close: () => setVisible(false),
  }));

  useEffect(() => {
    if (validateMessage?.length) {
      setDataReturn(undefined);
    }
  }, [validateMessage]);

  const handleImport = async () => {
    if (!dataPosts.length) return;

    try {
      setLoading(true);
      const { data } = await companyApi.import({ companies: dataPosts });
      const successCount = data.filter((d: any) => d.status === "ok").length;
      const errorCount = data.filter((d: any) => d.status === "error").length;

      setDataReturn({ data, successCount, errorCount });
      setDataPosts([]);
      onSuccess?.();
    } catch (err) {
      console.error("Import error", err);
      message.error("Đã xảy ra lỗi trong quá trình nhập dữ liệu");
    } finally {
      setLoading(false);
    }
  };

  // Define preview columns for the table
  const previewColumns = [
    {
      key: "rowNum",
      title: "Dòng excel",
      dataIndex: "rowNum",
      width: 100,
      render: (text: number) => <span>{text}</span>,
    },
    {
      key: "code",
      title: "Mã công ty",
      dataIndex: "code",
      render: (text: string) => (
        <span className={!text ? "text-gray-400" : ""}>
          {text || "Tự sinh"}
        </span>
      ),
    },
    {
      key: "name",
      title: "Tên công ty *",
      dataIndex: "name",
      render: (text: string, record: any) => (
        <span className={!text ? "text-red-500" : ""}>{text || "Thiếu"}</span>
      ),
    },
    {
      key: "phone",
      title: "Số điện thoại *",
      dataIndex: "phone",
      render: (text: string, record: any) => (
        <span className={!text ? "text-red-500" : ""}>{text || "Thiếu"}</span>
      ),
    },
    {
      key: "email",
      title: "Email *",
      dataIndex: "email",
      render: (text: string) => (
        <span className={!text ? "text-red-500" : ""}>{text || "Thiếu"}</span>
      ),
    },
    {
      key: "address",
      title: "Địa chỉ *",
      dataIndex: "address",
      render: (text: string) => (
        <span className={!text ? "text-red-500" : ""}>{text || "Thiếu"}</span>
      ),
    },
    {
      key: "note",
      title: "Ghi chú",
      dataIndex: "note",
      render: (text: string) => (
        <span className={!text ? "text-gray-400" : ""}>
          {text || "Không có"}
        </span>
      ),
    },
    {
      key: "taxCode",
      title: "Mã số thuế *",
      dataIndex: "taxCode",
      render: (text: string) => (
        <span className={!text ? "text-red-500" : ""}>{text || "Thiếu"}</span>
      ),
    },
    {
      key: "errorMessage",
      title: "Lỗi",
      dataIndex: "errorMessage",
      width: 300,
      render: (text: string) => (
        <span className="text-red-500 whitespace-pre-line text-xs">{text}</span>
      ),
    },
  ];

  const requiredFields: (keyof CompanyImport)[] = [
    "name", // Tên công ty
    "email", // Email
    "phone", // Số điện thoại
    "address", // Địa chỉ
    "taxCode", // Mã số thuế
  ];

  const handleValidateData = (data: CompanyImport[]): CompanyImport[] => {
    console.log("🔍 handleValidateData called with:", data);
    return data.map((item) => {
      const additionalErrors: string[] = [];

      // Name validation (should not be too long)
      if (item.name && item.name.length > 100) {
        additionalErrors.push("Tên công ty không được vượt quá 100 ký tự");
      }

      // Email validation (only if email exists)
      if (item.email && item.email.trim()) {
        if (!/\S+@\S+\.\S+/.test(item.email)) {
          additionalErrors.push("Email không đúng định dạng");
        }
      }

      // Phone validation (only if phone exists)
      if (item.phone && item.phone.trim()) {
        const { isValid } = validateNumber(item.phone);
        if (!isValid) {
          additionalErrors.push("Số điện thoại không đúng định dạng Việt Nam");
        }
      }

      if (item.phone2 && item.phone2.trim()) {
        const { isValid } = validateNumber(item.phone2);
        if (!isValid) {
          additionalErrors.push("Số điện thoại không đúng định dạng Việt Nam");
        }
      }

      console.log(
        "🔍 Additional validation for item:",
        item,
        "additional errors:",
        additionalErrors
      );

      // Return item with additional errors (don't overwrite existing errorMessage)
      return {
        ...item,
        errorMessage:
          additionalErrors.length > 0 ? additionalErrors.join("; ") : undefined,
      };
    });
  };

  // Callback to receive validation status from ImportPreviewModule
  const handleValidationStatusChange = (hasErrors: boolean) => {
    console.log("🚨 Validation status changed:", hasErrors);
    setHasValidationErrors(hasErrors);
  };

  const handleFileUpload = async (file: File) => {
    if (!file.name.includes("xlsx")) {
      message.error("Chỉ chấp nhận file Excel (.xlsx)");
      return Upload.LIST_IGNORE;
    }

    const excelData = await readerData(file, 0);
    setDataReturn(undefined);
    onUploaded?.(excelData, setDataPosts);
    return false;
  };

  const renderUploadBlock = () => (
    <Dragger
      style={{ marginTop: "0.5em" }}
      maxCount={1}
      multiple={false}
      beforeUpload={handleFileUpload}
      onChange={(info) => {
        if (info.fileList.length === 0) {
          setDataPosts([]);
          setDataReturn(undefined);
        }
      }}
    >
      <p className="ant-upload-drag-icon">
        <InboxOutlined />
      </p>
      <p className="ant-upload-text">{uploadText}</p>
    </Dragger>
  );

  const renderAlert = () => {
    if (!dataReturn) return null;

    const errors = dataReturn.data.filter((d) => d.status === "error");

    return (
      <Alert
        className="p-3 mt-2"
        type="warning"
        description={
          <div>
            <div className="text-blue-600 font-bold">
              Tổng dòng nhập: {dataReturn.data.length}
            </div>
            <div className="text-green-500">
              Tổng dòng thành công: {dataReturn.successCount}
            </div>
            <div className="text-red-500">
              Tổng dòng thất bại: {dataReturn.errorCount}
            </div>
            <div className="font-bold mt-2">Danh sách dòng thất bại</div>
            <Table
              className="mt-1"
              size="small"
              columns={[
                { title: "Dòng", dataIndex: "rowNum" },
                { title: "Lỗi", dataIndex: "msg" },
              ]}
              dataSource={errors}
              pagination={false}
              rowKey="rowNum"
            />
          </div>
        }
      />
    );
  };

  const handleClose = () => {
    setVisible(false);
    onClose?.();
    setDataPosts([]);
    setDataReturn(undefined);
  };

  return (
    <Modal
      open={visible}
      title={titleText}
      maskClosable={false}
      width={1000}
      destroyOnClose
      onCancel={handleClose}
      afterClose={handleClose}
      footer={[
        <CustomButton
          key="import"
          variant="primary"
          loading={loading}
          disabled={!dataPosts.length || hasValidationErrors}
          onClick={handleImport}
        >
          {okText}
        </CustomButton>,
        <CustomButton key="close" variant="outline" onClick={handleClose}>
          Đóng
        </CustomButton>,
      ]}
    >
      <Spin spinning={false}>
        {guide && (
          <Alert
            className="mb-3"
            type="warning"
            message={<strong>Lưu ý</strong>}
            description={
              <ul className="list-disc pl-4">
                {guide.map((item, idx) => (
                  <li key={idx}>{item}</li>
                ))}
              </ul>
            }
          />
        )}

        {demoExcel && (
          <Link to={demoExcel} target="_blank" download>
            <Space className={`flex gap-2 cursor-pointer`}>
              <DownloadOutlined />
              Tải file import mẫu{" "}
            </Space>
          </Link>
        )}

        {onDownloadDemoExcel && (
          <a>
            <Space
              className={`flex gap-2 cursor-pointer`}
              onClick={() => {
                onDownloadDemoExcel();
              }}
              style={{ pointerEvents: loadingDownloadDemo ? "none" : "auto" }}
            >
              {loadingDownloadDemo ? (
                <Spin spinning={loadingDownloadDemo} />
              ) : (
                <DownloadOutlined />
              )}
              Tải file import mẫu
            </Space>
          </a>
        )}

        {renderUploadBlock()}

        {/* Import Preview Module */}
        <ImportPreviewModule
          data={dataPosts}
          dataReturn={dataReturn}
          onValidateData={handleValidateData}
          duplicateCheckFields={["code", "name", "phone", "email"]} // Kiểm tra trùng lặp mã, tên, số điện thoại và email
          requiredFields={requiredFields}
          columns={previewColumns}
          title="Xem danh sách cấu hình"
          previewButtonText="Xem danh sách cấu hình"
          onValidationStatusChange={handleValidationStatusChange}
        />

        {/* {renderAlert()} */}
      </Spin>
    </Modal>
  );
});

export default ImportCompanies;
