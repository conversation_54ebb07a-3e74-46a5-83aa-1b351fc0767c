import { DownloadOutlined, InboxOutlined } from "@ant-design/icons";
import { Alert, Modal, Space, Spin, Table, Upload, message } from "antd";
import { Rule } from "antd/es/form";
import { materialApi } from "api/material.api";
import CustomButton from "components/Button/CustomButton";
import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
  useMemo,
} from "react";
import { Link } from "react-router-dom";
import { Material, EMaterialType } from "types/material";
import { readerData } from "utils/excel2";
import ImportPreviewModule from "./ImportPreviewModule";
import { handleConfirmImportExcel } from "utils/function";

const rules: Rule[] = [{ required: true }];
const { Dragger } = Upload;

export interface ImportMaterialModal {
  open: () => void;
  close: () => void;
}

export interface IValidate {
  index: number;
  message: string;
}

interface DataImportReturn {
  msg: string;
  rowNum: number;
  status: string;
}

export interface MaterialImport extends Material {
  rowNum: number;
  materialGroupName?: string; // From Excel
  unitName?: string; // From Excel
  brandName?: string; // From Excel
  providerName?: string; // From Excel
}

interface IProps {
  onSuccess?: () => void;
  createApi?: (data: any) => any;
  onUploaded?: (excelData: any, setData: (data: any) => any) => void;
  demoExcel?: string;
  guide?: React.ReactNode[];
  uploadText?: string;
  okText?: string;
  onClose?: () => void;
  titleText?: string;
  validateMessage?: IValidate[];
  onDownloadDemoExcel?: () => void;
  loadingDownloadDemo?: boolean;
  materialType?: EMaterialType; // Thêm prop để xác định loại material
}

const ImportMaterial = forwardRef(
  (
    {
      onSuccess,
      createApi,
      onUploaded,
      onClose,
      validateMessage,
      guide,
      demoExcel,
      uploadText = "Kéo thả hoặc click vào đây để upload file",
      okText = "Nhập dữ liệu ngay",
      titleText = "Nhập excel dữ liệu",
      onDownloadDemoExcel,
      loadingDownloadDemo,
      materialType,
    }: IProps,
    ref
  ) => {
    const [errorsLog, setErrorsLog] = useState<any[]>([]);
    const [dataPosts, setDataPosts] = useState<MaterialImport[]>([]);
    const [visible, setVisible] = useState<boolean>(false);
    const [loading, setLoading] = useState(false);
    const [hasValidationErrors, setHasValidationErrors] = useState(false);
    const [dataReturn, setDataReturn] = useState<{
      data: DataImportReturn[];
      successCount: number;
      errorCount: number;
    }>();

    useEffect(() => {
      if (validateMessage?.length) {
        setErrorsLog([]);
      }
    }, [validateMessage]);

    // Callback to receive validation status from ImportPreviewModule
    const handleValidationStatusChange = (hasErrors: boolean) => {
      console.log("🚨 Material validation status changed:", hasErrors);
      setHasValidationErrors(hasErrors);
    };

    // Tự động nhận diện loại material dựa trên dữ liệu hoặc prop
    const detectedMaterialType = useMemo(() => {
      if (materialType) return materialType;

      // Có thể phân tích dữ liệu để tự động phát hiện dựa trên các trường đặc trưng
      // Ví dụ: nếu có thông tin về sản phẩm hoàn chỉnh thì là Product
      if (dataPosts.length > 0) {
        const hasProductFields = dataPosts.some(
          (item) =>
            item.salePrice !== undefined &&
            item.salePrice !== null &&
            item.purchasePrice !== undefined &&
            item.purchasePrice !== null
        );
        return hasProductFields
          ? EMaterialType.Product
          : EMaterialType.Material;
      }

      return EMaterialType.Material; // Default
    }, [materialType, dataPosts]);
    const isProduct = detectedMaterialType === EMaterialType.Product;

    // Tự động tạo title và button text dựa trên loại material
    const dynamicTexts = useMemo(() => {
      return {
        modalTitle:
          titleText ||
          `Nhập excel ${isProduct ? "hàng hóa" : "nguyên vật liệu"}`,
        previewTitle: `Xem danh sách ${
          isProduct ? "hàng hóa" : "nguyên vật liệu"
        }`,
        previewButtonText: `Xem danh sách ${
          isProduct ? "hàng hóa" : "nguyên vật liệu"
        }`,
      };
    }, [detectedMaterialType, titleText]);

    const handleOnImport = async () => {
      if (!dataPosts.length) return;
      let errors: any = [];

      try {
        setLoading(true);
        await handleConfirmImportExcel();
        const { data } = await materialApi.import({
          materials: dataPosts.map((dataPost) => ({
            ...dataPost,
          })),
        });
        if (data.length) {
          const successCount = data.reduce(
            (acc: number, item: DataImportReturn) => {
              if (item.status == "ok") return acc + 1;
              return acc;
            },
            0
          );
          const errorCount = data.reduce(
            (acc: number, item: DataImportReturn) => {
              if (item.status == "error") return acc + 1;
              return acc;
            },
            0
          );
          if (errorCount == 0) {
            handleOnCancel();
          }
          setDataReturn({ data, successCount, errorCount });
          onSuccess?.();
          setDataPosts([]);
        }
      } catch (err) {
        console.log({ err });
      } finally {
        setLoading(false);
      }
    };

    const handleOnCancel = () => {
      setVisible(false);
      onClose?.();
    };

    useImperativeHandle(
      ref,
      () => ({
        open: () => setVisible(true),
        close: () => setVisible(false),
      }),
      []
    );

    // Utility function to extract number from text with units
    const extractNumber = (
      value: any
    ): { number: number; isValid: boolean; hasValue: boolean } => {
      const hasValue = value !== undefined && value !== null && value !== "";

      if (!hasValue) {
        return { number: 0, isValid: false, hasValue: false };
      }

      // Convert to string first
      const str = String(value).trim();

      // Remove common units and currency symbols
      const cleanedStr = str
        .replace(/\s*(mm|m|cm|km|VNĐ|VND|đ|%)\s*$/gi, "") // Remove units at the end
        .replace(/[,\s]/g, "") // Remove commas and spaces
        .replace(/^\$/, ""); // Remove $ at the beginning

      const numValue = Number(cleanedStr);
      const isValid = !isNaN(numValue) && isFinite(numValue);

      return { number: numValue, isValid, hasValue: true };
    };

    // Define preview columns for the table - dynamic based on material type
    const previewColumns = [
      {
        key: "rowNum",
        title: "Dòng excel",
        dataIndex: "rowNum",
        width: 100,
        render: (text: number) => <span>{text}</span>,
      },
      {
        key: "code",
        title: isProduct ? "Mã hàng hóa" : "Mã nguyên vật liệu",
        dataIndex: "code",
        render: (text: string) => (
          <span className={!text ? "text-gray-400" : ""}>
            {text || "Tự sinh"}
          </span>
        ),
      },
      {
        key: "name",
        title: isProduct ? "Tên hàng hóa *" : "Tên nguyên vật liệu *",
        dataIndex: "name",
        render: (text: string, record: any) => (
          <span className={!text ? "text-red-500" : ""}>{text || "Thiếu"}</span>
        ),
      },
      {
        key: "materialGroupName",
        title: isProduct ? "Nhóm hàng hóa *" : "Nhóm hàng *",
        dataIndex: "materialGroupName",
        render: (text: string, record: any) => (
          <span className={!text ? "text-red-500" : ""}>{text || "Thiếu"}</span>
        ),
      },
      {
        key: "unitName",
        title: "Đơn vị tính *",
        dataIndex: "unitName",
        render: (text: string, record: any) => (
          <span className={!text ? "text-red-500" : ""}>{text || "Thiếu"}</span>
        ),
      },
      {
        key: "lengthMm",
        title: "Dài (mm) *",
        dataIndex: "lengthMm",
        width: 100,
        render: (text: any, record: any) => {
          const { number: numValue, isValid, hasValue } = extractNumber(text);

          return (
            <span className={!hasValue ? "text-red-500" : ""}>
              {hasValue && isValid
                ? `${numValue} mm`
                : hasValue
                ? `${text} (Không hợp lệ)`
                : "Thiếu"}
            </span>
          );
        },
      },
      {
        key: "widthMm",
        title: "Rộng (mm)",
        dataIndex: "widthMm",
        render: (text: any) => {
          const { number: numValue, isValid, hasValue } = extractNumber(text);

          return (
            <span className={!hasValue ? "text-gray-400" : ""}>
              {hasValue && isValid
                ? `${numValue} mm`
                : hasValue
                ? `${text} (Không hợp lệ)`
                : "Không có"}
            </span>
          );
        },
      },
      {
        key: "heightMm",
        title: "Cao (mm)",
        dataIndex: "heightMm",
        render: (text: any) => {
          const { number: numValue, isValid, hasValue } = extractNumber(text);

          return (
            <span className={!hasValue ? "text-gray-400" : ""}>
              {hasValue && isValid
                ? `${numValue} mm`
                : hasValue
                ? `${text} (Không hợp lệ)`
                : "Không có"}
            </span>
          );
        },
      },
      {
        key: "thicknessMm",
        title: "Dày (mm)",
        dataIndex: "thicknessMm",
        render: (text: any) => {
          const { number: numValue, isValid, hasValue } = extractNumber(text);

          return (
            <span className={!hasValue ? "text-gray-400" : ""}>
              {hasValue && isValid
                ? `${numValue} mm`
                : hasValue
                ? `${text} (Không hợp lệ)`
                : "Không có"}
            </span>
          );
        },
      },
      {
        key: "diameterMm",
        title: "Đường kính (mm)",
        dataIndex: "diameterMm",
        render: (text: any) => {
          const { number: numValue, isValid, hasValue } = extractNumber(text);

          return (
            <span className={!hasValue ? "text-gray-400" : ""}>
              {hasValue && isValid
                ? `${numValue} mm`
                : hasValue
                ? `${text} (Không hợp lệ)`
                : "Không có"}
            </span>
          );
        },
      },
      {
        key: "purchasePrice",
        title: "Giá mua",
        dataIndex: "purchasePrice",
        render: (text: any) => {
          const { number: numValue, isValid, hasValue } = extractNumber(text);

          return (
            <span className={!hasValue ? "text-gray-400" : ""}>
              {hasValue && isValid
                ? `${numValue.toLocaleString()} VNĐ`
                : hasValue
                ? `${text} (Không hợp lệ)`
                : "Không có"}
            </span>
          );
        },
      },
      {
        key: "salePrice",
        title: "Giá bán",
        dataIndex: "salePrice",
        render: (text: any) => {
          const { number: numValue, isValid, hasValue } = extractNumber(text);

          return (
            <span className={!hasValue ? "text-gray-400" : ""}>
              {hasValue && isValid
                ? `${numValue.toLocaleString()} VNĐ`
                : hasValue
                ? `${text} (Không hợp lệ)`
                : "Không có"}
            </span>
          );
        },
      },
      {
        key: "taxPercent",
        title: "Thuế (%)",
        dataIndex: "taxPercent",
        render: (text: any) => {
          const { number: numValue, isValid, hasValue } = extractNumber(text);

          return (
            <span className={!hasValue ? "text-gray-400" : ""}>
              {hasValue && isValid
                ? `${numValue}%`
                : hasValue
                ? `${text} (Không hợp lệ)`
                : "Không có"}
            </span>
          );
        },
      },
      {
        key: "providerName",
        title: "Nhà cung cấp",
        dataIndex: "providerName",
        render: (text: string) => (
          <span className={!text ? "text-gray-400" : ""}>
            {text || "Không có"}
          </span>
        ),
      },
      {
        key: "errorMessage",
        title: "Lỗi",
        dataIndex: "errorMessage",
        width: 300,
        render: (text: string) => (
          <span className="text-red-500 whitespace-pre-line text-xs">
            {text}
          </span>
        ),
      },
    ];

    // Define required fields based on guide comments and Material type
    const requiredFields: (keyof MaterialImport)[] = [
      "name", // Tên hàng hóa/nguyên vật liệu
      "materialGroupName", // Nhóm hàng hóa/hàng (from Excel)
      "unitName", // Đơn vị tính (from Excel)
      "lengthMm", // Dài (mm) - DUY NHẤT field dimension bắt buộc
    ];

    const handleValidateData = (data: MaterialImport[]): MaterialImport[] => {
      console.log("🔍 handleValidateData called with:", data);
      return data.map((item) => {
        const additionalErrors: string[] = [];

        // Helper function để kiểm tra giá trị có hợp lệ không
        const hasValidValue = (value: any): boolean => {
          return (
            value !== undefined && value !== null && value !== "" && value !== 0
          );
        };

        // Dimension validations (must be positive numbers if provided)
        const dimensionFields = [
          { field: "lengthMm", name: "Dài" },
          { field: "widthMm", name: "Rộng" },
          { field: "heightMm", name: "Cao" },
          { field: "thicknessMm", name: "Dày" }, // Không bắt buộc, chỉ validate khi có giá trị
          { field: "diameterMm", name: "Đường kính" },
        ];

        dimensionFields.forEach(({ field, name }) => {
          const value = item[field as keyof MaterialImport];

          // Chỉ validate khi có giá trị
          if (hasValidValue(value)) {
            const { number: numValue, isValid } = extractNumber(value);
            if (!isValid || numValue < 0) {
              additionalErrors.push(`${name} phải là số không âm`);
            }
          }
        });

        // Price validations (must be positive numbers if provided)
        const priceFields = [
          { field: "purchasePrice", name: "Giá mua" },
          { field: "salePrice", name: "Giá bán" },
        ];

        priceFields.forEach(({ field, name }) => {
          const value = item[field as keyof MaterialImport];

          // Chỉ validate khi có giá trị
          if (hasValidValue(value)) {
            const { number: numValue, isValid } = extractNumber(value);
            if (!isValid || numValue < 0) {
              additionalErrors.push(`${name} phải là số không âm`);
            }
          }
        });

        // Tax percent validation (0-100 if provided)
        if (hasValidValue(item.taxPercent)) {
          const { number: numValue, isValid } = extractNumber(item.taxPercent);

          if (!isValid || numValue < 0 || numValue > 100) {
            additionalErrors.push("Thuế phải là số từ 0 đến 100");
          }
        }

        // Quantity validations (must be positive integers if provided)
        const quantityFields = [
          { field: "minQuantity", name: "Số lượng tối thiểu" },
          { field: "maxQuantity", name: "Số lượng tối đa" },
        ];

        quantityFields.forEach(({ field, name }) => {
          const value = item[field as keyof MaterialImport];

          // Chỉ validate khi có giá trị
          if (hasValidValue(value)) {
            const { number: numValue, isValid } = extractNumber(value);
            if (!isValid || numValue < 0 || !Number.isInteger(numValue)) {
              additionalErrors.push(`${name} phải là số nguyên không âm`);
            }
          }
        });

        // Shelf life validation (must be positive integer if provided)
        if (hasValidValue(item.shelfLifeDays)) {
          const { number: numValue, isValid } = extractNumber(
            item.shelfLifeDays
          );

          if (!isValid || numValue < 0 || !Number.isInteger(numValue)) {
            additionalErrors.push("Thời gian tồn phải là số nguyên không âm");
          }
        }

        // Min/Max quantity relationship validation
        const minQty = item.minQuantity;
        const maxQty = item.maxQuantity;

        if (hasValidValue(minQty) && hasValidValue(maxQty)) {
          const { number: minValue, isValid: minValid } = extractNumber(minQty);
          const { number: maxValue, isValid: maxValid } = extractNumber(maxQty);

          if (minValid && maxValid && minValue > maxValue) {
            additionalErrors.push(
              "Số lượng tối thiểu không được lớn hơn số lượng tối đa"
            );
          }
        }

        console.log(
          "🔍 Additional validation for item:",
          item,
          "additional errors:",
          additionalErrors
        );

        // Return item with additional errors (don't overwrite existing errorMessage)
        return {
          ...item,
          errorMessage:
            additionalErrors.length > 0
              ? additionalErrors.join("; ")
              : undefined,
        };
      });
    };

    return (
      <Modal
        maskClosable={false}
        width={1000} // Increase width to accommodate more columns
        style={{ top: 50 }}
        visible={visible}
        onCancel={handleOnCancel}
        destroyOnClose={true}
        afterClose={() => {
          setDataPosts([]);
          setErrorsLog([]);
          setDataReturn(undefined);
        }}
        title={dynamicTexts.modalTitle}
        footer={[
          <CustomButton
            key="import"
            loading={loading}
            variant="primary"
            disabled={!dataPosts.length || hasValidationErrors}
            onClick={() => {
              handleOnImport();
            }}
          >
            {okText}
          </CustomButton>,
          <CustomButton
            key="close"
            variant="outline"
            className="cta-button"
            onClick={() => {
              handleOnCancel();
            }}
          >
            Đóng
          </CustomButton>,
        ]}
      >
        <Spin spinning={false}>
          {guide && (
            <Alert
              style={{ padding: "10px", marginBottom: "10px" }}
              message={<b>Lưu ý</b>}
              type="warning"
              description={
                <ul>
                  {guide.map((text, index) => (
                    <li key={index}>
                      <p>{text}</p>
                    </li>
                  ))}
                </ul>
              }
            />
          )}
          {demoExcel && (
            <Link to={demoExcel} target="_blank" download>
              <Space className={`flex gap-2 cursor-pointer`}>
                <DownloadOutlined />
                Tải file import mẫu{" "}
              </Space>
            </Link>
          )}
          {onDownloadDemoExcel && (
            <a>
              <Space
                className={`flex gap-2 cursor-pointer`}
                onClick={() => {
                  onDownloadDemoExcel();
                }}
                style={{ pointerEvents: loadingDownloadDemo ? "none" : "auto" }}
              >
                {loadingDownloadDemo ? (
                  <Spin spinning={loadingDownloadDemo} />
                ) : (
                  <DownloadOutlined />
                )}
                Tải file import mẫu
              </Space>
            </a>
          )}

          <Dragger
            style={{ marginTop: "0.5em" }}
            maxCount={1}
            multiple={false}
            beforeUpload={async (file) => {
              //Check file type
              const isCSVFile = file.name.includes("xlsx");
              if (isCSVFile === false) {
                message.error("Bạn chỉ có thể upload file excel!");
                return Upload.LIST_IGNORE;
              }
              const excelData = await readerData(file, 0);
              setDataReturn(undefined);
              console.log("Data khi import vào là", excelData);
              onUploaded?.(excelData, setDataPosts);
              return false;
            }}
            onChange={(info) => {
              //reset data
              if (info.fileList.length == 0) {
                setErrorsLog([]);
                setDataPosts([]);
              }
            }}
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">{uploadText}</p>
          </Dragger>

          {/* Import Preview Module */}
          <ImportPreviewModule
            data={dataPosts}
            dataReturn={dataReturn}
            onValidateData={handleValidateData}
            duplicateCheckFields={["code", "name"]} // Kiểm tra trùng lặp mã nguyên liệu và tên nguyên liệu
            requiredFields={requiredFields}
            columns={previewColumns}
            title={dynamicTexts.previewTitle}
            previewButtonText={dynamicTexts.previewButtonText}
            onValidationStatusChange={handleValidationStatusChange}
          />
        </Spin>
        <Space
          style={{ width: "100%", justifyContent: "end", marginTop: "1em" }}
        ></Space>
      </Modal>
    );
  }
);

export default ImportMaterial;
