// This file combines Create and Edit Indicative Page into a single component
import {
  Card,
  Col,
  Row,
  Form,
  Button,
  Avatar,
  Space,
  Spin,
  Tabs,
  Select,
  message,
  Input,
  Modal,
  Tag,
  DatePicker,
} from "antd";
import PageTitle from "components/PageTitle/PageTitle";
import React, { useEffect, useMemo, useState } from "react";
import { Rule } from "antd/lib/form";
import CustomButton from "components/Button/CustomButton";
import { ModalStatus } from "types/modal";
import { PermissionNames } from "types/PermissionNames";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { FileAttach } from "types/fileAttach";
import { isEmpty } from "lodash";
import { getTitle } from "utils";
import { FileUploadMultiple2 } from "components/Upload/FileUploadMultiple2";
import { ProjectSelector } from "components/Selector/ProjectSelector";
import { useWard } from "hooks/useWard";
import { useWatch } from "antd/es/form/Form";
import { DictionarySelector } from "components/Selector/DictionarySelector";
import { Dictionary, DictionaryType } from "types/dictionary";
import { fileAttachApi } from "api/fileAttach.api";
import { $url } from "utils/url";
import dayjs from "dayjs";
import { ProviderSelector } from "components/Selector/ProviderSelector";
import { ProviderModule } from "types/provider";
import { Staff } from "types/staff";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import { WorkStatusTrans } from "types/workStatus";
import { Pagination } from "components/Pagination";
import {
  ApprovalStepsCard,
  ApproveData,
  StepItem,
} from "components/ApproveProcess/ApprovalStepsCard";
import { FollowerSelector } from "components/Follower/FollowerSelector";
import TextArea from "antd/es/input/TextArea";
import { settings } from "settings";
import { dictionaryApi } from "api/dictionary.api";
import clsx from "clsx";
import { observer } from "mobx-react";
import { checkRoles } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { memberShipApi } from "api/memberShip.api";
import { DeviceType } from "types/device";
import { CommentView } from "components/Comment/CommentView";
import { StaffSelector } from "components/Selector/StaffSelector";
import QRCodeSection from "./components/QRCodeSection";
import { Draw } from "types/draw";
import { drawApi } from "api/draw.api";
import { FileUpload } from "components/Upload/FileUpload";
import BlueprintUpload from "./components/BlueprintUpload";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import DeleteIcon from "assets/svgs/DeleteIcon";
import { PlusOutlined } from "@ant-design/icons";
import AddCategoryModal from "./components/AddCategoryModal";
import { formatDateTime } from "utils/date";
import { ApprovalListType } from "types/approvalList";
import { approvalListApi } from "api/approvalList.api";
import { MemberShip } from "types/memberShip";
import { MembershipSelector } from "components/Selector/MembershipSelector";
import { BMDTextArea } from "components/TextArea/BMDTextArea";
import { transformApproveData } from "components/ApproveProcess/approveUtil";
import { BMDCKEditor } from "components/Editor";
import {
  ApprovalTemplateName,
  ApprovalTemplateType,
} from "types/approvalTemplate";

const rules: Rule[] = [{ required: true, message: "Trường này là bắt buộc" }];
const descriptionRules: Rule[] = [{ required: false }];

interface EditDrawPageProps {
  title: string;
  status: ModalStatus;
}

interface DrawForm extends Draw {
  code: string;
  title: string;
  section: string;
  ax: string;
  ay: string;
  bx: string;
  by: string;
  cx: string;
  cy: string;
  dx: string;
  dy: string;
  note: string;
  drawCategory: Dictionary;
  memberShip: MemberShip;
  fileAttachId: number;
  memberShipId: number;
  pdfFile?: FileAttach;
}

function CreateOrUpdateBluePrintPage({
  title = "",
  status,
}: EditDrawPageProps) {
  const { haveEditPermission } = checkRoles(
    {
      edit: PermissionNames.blueprintEdit,
    },
    permissionStore.permissions
  );

  const [form] = Form.useForm<DrawForm>();
  const [loading, setLoading] = useState(false);
  const [selectedDraw, setSelectedDraw] = useState<Draw>();
  const [fileList, setFileList] = useState<FileAttach[]>([]);
  const navigate = useNavigate();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [followers, setFollowers] = useState<MemberShip[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(false);
  const [drawCategories, setDrawCategories] = useState<any[]>([]);
  const [memberShips, setMemberShips] = useState<any[]>([]);
  const [loadingMemberShips, setLoadingMemberShips] = useState(false);
  const [pdfUrl, setPdfUrl] = useState("");
  const [pdfFileAttach, setPdfFileAttach] = useState<FileAttach | null>(null); // Thêm state này
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [approvalSteps, setApprovalSteps] = useState<StepItem[]>([]); // danh sach quy trinh duyet
  const [loadingApprove, setLoadingApprove] = useState(false);
  const [removeApprovalList, setRemoveApprovalList] = useState<number[]>([]);
  const [commentRefreshTrigger, setCommentRefreshTrigger] = useState(0);

  // Tạo pdfRules bên trong component để có thể truy cập state
  const pdfRules: Rule[] = [
    {
      required: true,
      message: "Bản vẽ là bắt buộc",
      validator: (_, value) => {
        if (!value && !pdfFileAttach && !selectedDraw?.fileAttach) {
          return Promise.reject(new Error("Bản vẽ là bắt buộc"));
        }
        return Promise.resolve();
      },
    },
  ];

  const handleAddFollowers = () => {
    setIsModalVisible(true);
  };

  const [readonly, setReadonly] = useState(true);
  const [loadingFetch, setLoadingFetch] = useState(false);
  const params = useParams();
  const [searchParams, setSearchParams] = useSearchParams();
  const type = useWatch("type", form);
  const [senderInfo, setSenderInfo] = useState<any>(null);
  const [receiverInfo, setReceiverInfo] = useState<any>(null);

  // Watch form values để cập nhật QR code
  const blueprintCode = useWatch("code", form);
  const blueprintTitle = useWatch("title", form);
  const blueprintSection = useWatch("section", form);
  const blueprintStaff = useWatch("staff", form);
  const blueprintDrawCategory = useWatch("drawCategory", form);
  const memberShipObj = memberShips.find(
    (m) => m.id === form.getFieldValue("memberShipId")
  );

  console.log({
    blueprintCode,
    blueprintTitle,
    blueprintSection,
    blueprintDrawCategory,
    blueprintStaff,
  });

  const fetchMemberShips = async () => {
    try {
      setLoadingMemberShips(true);
      const { data } = await memberShipApi.findAll({
        isActive: true,
      });
      setMemberShips(data?.memberShips || []);
    } catch (error) {
      console.error("Error fetching memberShips:", error);
      message.error("Không thể tải danh sách thành viên");
    } finally {
      setLoadingMemberShips(false);
    }
  };

  const setDataToForm = async (data: Draw) => {
    console.log("Setting form data with:", data);

    // Validate data structure
    if (!data) {
      console.error("Data is null or undefined");
      return;
    }

    // Reset form first to clear any existing data
    form.resetFields();

    const formData = {
      code: data.code || "",
      title: data.title || "",
      section: data.section || "",
      note: data.note || "",
      ax: data.ax || "",
      ay: data.ay || "",
      bx: data.bx || "",
      by: data.by || "",
      cx: data.cx || "",
      cy: data.cy || "",
      dx: data.dx || "",
      dy: data.dy || "",
      fileAttachId: data.fileAttach?.id || 0,
      drawCategory: data.drawCategory || undefined,
      staff: data.staff || undefined,
      memberShipId: data.memberShip?.id || undefined,
    };

    console.log("Form data to set:", formData);

    try {
      form.setFieldsValue(formData);
      console.log("Form fields set successfully");

      // Force form to update
      setTimeout(() => {
        form.validateFields().catch(() => {
          // Ignore validation errors when setting initial data
        });
      }, 50);
    } catch (error) {
      console.error("Error setting form fields:", error);
    }

    // Load PDF file trực tiếp từ data response
    //@ts-ignore
    if (data.fileAttach) {
      console.log("Setting PDF file data:", data.fileAttach);
      //@ts-ignore
      setPdfUrl(data.fileAttach.url);
      //@ts-ignore
      setPdfFileAttach(data.fileAttach);
      // Set form value for validation
      form.setFieldsValue({ pdfFile: data.fileAttach });
      form.validateFields(["pdfFile"]);
    }

    const transformedApproveData = transformApproveData(
      data.approvalLists,
      data.createdBy
    );
    setApprovalSteps(transformedApproveData);
    // Set followers
    // setFollowers(data.followMemberShips || []);
  };

  const getOneDraw = async (id: number) => {
    try {
      setLoadingFetch(true);
      console.log("Fetching draw with ID:", id);
      const { data } = await drawApi.findOne(id);
      console.log("API response data:", data);

      if (isEmpty(data)) {
        console.log("Data is empty, navigating to 404");
        navigate("/404");

        return;
      }

      console.log("Setting selectedDraw and form data");
      setSelectedDraw(data);
      // Add a small delay to ensure form is ready
      setTimeout(() => {
        setDataToForm(data);
      }, 200);

      //   if (data.serviceType) {
      //     setServiceTypes([data.serviceType]);
      //   }

      return data as Draw;
    } catch (e: any) {
      console.error("Error fetching draw:", e);
      message.error("Không thể tải dữ liệu bản vẽ!");
    } finally {
      setLoadingFetch(false);
    }
  };

  useEffect(() => {
    document.title = getTitle(title);

    if (status == "update") {
      const instructionId = params.id;

      if (instructionId) {
        // Add a small delay to ensure form is ready
        setTimeout(() => {
          getOneDraw(+instructionId);
        }, 100);
        setReadonly(!haveEditPermission || searchParams.get("update") !== "1");
      }
    } else {
      setReadonly(false);
    }

    fetchDrawCategories();
    fetchMemberShips();
  }, [haveEditPermission]);

  const fetchDrawCategories = async () => {
    try {
      setLoadingCategories(true);
      const { data } = await dictionaryApi.findAll({
        isActive: true,
      });
      setDrawCategories(data?.dictionaries || []);
    } catch (error) {
      console.error("Error fetching Draw categories:", error);
      message.error("Không thể tải danh sách phân loại Draw");
    } finally {
      setLoadingCategories(false);
    }
  };

  const handleDeleteCategory = async (id: number) => {
    try {
      await drawApi.delete(id);
      message.success("Xóa hạng mục thành công!");
      // fetchdraw();
    } catch (e) {
      console.log({ e });
    } finally {
    }
  };

  const getDataSubmit = async () => {
    const {
      drawCategory,
      memberShipId,
      code,
      title,
      section,
      note,
      ax,
      ay,
      bx,
      by,
      cx,
      cy,
      dx,
      dy,
      ...data
    } = form.getFieldsValue();

    const fileAttachIds: number[] = [];

    // Xử lý file PDF chính
    if (pdfFileAttach) {
      // Trường hợp upload file PDF mới
      try {
        const resFileAttach = await fileAttachApi.create({
          fileAttach: {
            name: pdfFileAttach.name || "blueprint.pdf",
            url:
              pdfFileAttach.url ||
              (pdfFileAttach.path ? $url(pdfFileAttach.path) : ""),
            mimetype: "application/pdf",
            size: pdfFileAttach.size || 0,
          },
        });
        fileAttachIds.push(resFileAttach.data.id);
        console.log("Created PDF file record:", resFileAttach.data);
      } catch (error) {
        console.error("Error creating PDF file record:", error);
      }
    } else if (selectedDraw?.fileAttach?.id) {
      // Trường hợp update nhưng không thay đổi file PDF, giữ lại fileAttachId cũ
      fileAttachIds.push(selectedDraw.fileAttach.id);
      console.log("Keeping existing PDF file ID:", selectedDraw.fileAttach.id);
    } else if (selectedDraw?.fileAttach && !selectedDraw.fileAttach.id) {
      // Trường hợp có fileAttach object nhưng không có id (có thể từ response API)
      try {
        const resFileAttach = await fileAttachApi.create({
          fileAttach: {
            name: selectedDraw.fileAttach.name || "blueprint.pdf",
            url:
              selectedDraw.fileAttach.url ||
              (selectedDraw.fileAttach.path
                ? $url(selectedDraw.fileAttach.path)
                : ""),
            mimetype: selectedDraw.fileAttach.mimetype || "application/pdf",
            size: selectedDraw.fileAttach.size || 0,
          },
        });
        fileAttachIds.push(resFileAttach.data.id);
        console.log(
          "Created PDF file record from existing data:",
          resFileAttach.data
        );
      } catch (error) {
        console.error(
          "Error creating PDF file record from existing data:",
          error
        );
      }
    }

    // Xử lý các file khác từ FileUploadMultiple2
    for (const file of fileList) {
      if (file.id) {
        fileAttachIds.push(file.id);
      } else if (file.url && !file.originFile) {
        try {
          const resFileAttach = await fileAttachApi.create({
            fileAttach: {
              name: file.name,
              url: file.url,
              mimetype: file.mimetype || "application/octet-stream",
              size: file.size || 0,
            },
          });
          fileAttachIds.push(resFileAttach.data.id);
          console.log("Created file record:", resFileAttach.data);
        } catch (error) {
          console.error("Error creating file record:", error);
        }
      } else if (file.originFile) {
        try {
          const { data: uploadData } = await fileAttachApi.upload(
            file.originFile
          );

          const resFileAttach = await fileAttachApi.create({
            fileAttach: {
              name: file.name,
              url: uploadData.path ? $url(uploadData.path) : "",
              mimetype: file.mimetype || file.originFile.type,
              size: file.size || file.originFile.size,
            },
          });

          fileAttachIds.push(resFileAttach.data.id);
        } catch (error) {
          console.error("Error uploading file:", error);
        }
      }
    }

    const approvalLists = approvalSteps.map((step, i) => {
      const _approvalListDetails = step.approvers.map((approver, index) => {
        if (
          !approver.memberShipId &&
          step.name !== ApprovalTemplateName.Create
        ) {
          message.error("Vui lòng chọn người duyệt cho bước " + (i + 1));
          throw new Error("Vui lòng chọn người duyệt cho bước " + (i + 1));
        }
        return {
          position: index,
          memberShipId: approver.memberShipId,
          roleId: approver.roleId,
        };
      });
      return {
        id: step.id,
        name: step.name,
        type: ApprovalListType.Draw,
        position: step.position,
        note: step.note,
        isAllApproval: step.isAllApproval,
        actionText: step.actionText,
        statusText: step.statusText,
        statusColor: step.statusColor,
        approvalListDetails: _approvalListDetails,
        // memberShipId: step.memberShipId,
        // memberShip2Id: step.memberShip2Id,
        staffId: step.staffId,
        drawId: selectedDraw!?.id || 0,
      };
    });

    const payload = {
      drawCategoryId:
        typeof drawCategory === "object" && drawCategory?.id
          ? drawCategory.id
          : drawCategory || 0,
      memberShipId: memberShipId || 0,
      approveStaffIds: [],
      followStaffIds: followers?.map((it) => it.id) || [],
      fileAttachId: fileAttachIds.length > 0 ? fileAttachIds[0] : 0,
      draw: {
        code: code || "",
        title: title || "",
        section: section || "",
        ax: ax || "",
        ay: ay || "",
        bx: bx || "",
        by: by || "",
        cx: cx || "",
        cy: cy || "",
        dx: dx || "",
        dy: dy || "",
        note: note || "",
        isActive: selectedDraw?.isActive ?? true,
        approvalLists: approvalLists,
      },
    };

    console.log("Submit payload:", {
      fileAttachIds,
      fileAttachId: payload.fileAttachId,
      pdfFileAttach,
      selectedDrawFileAttach: selectedDraw?.fileAttach,
    });

    return payload;
  };

  const createData = async () => {
    const valid = await form.validateFields();

    // Kiểm tra bổ sung cho file PDF
    if (!pdfFileAttach) {
      message.error("Bản vẽ là bắt buộc!");
      return;
    }

    setLoading(true);
    try {
      const res = await drawApi.create(await getDataSubmit());

      console.log("Created draw:", res.data);

      message.success("Tạo bản vẽ thành công!");
      navigate(`/doc-management/${PermissionNames.blueprintList}`);
      setFileList([]);
    } catch (error) {
      console.error("Error creating draw:", error);
      message.error("Có lỗi xảy ra khi tạo bản vẽ!");
    } finally {
      setLoading(false);
    }
  };

  const updateData = async () => {
    const valid = await form.validateFields();

    // Kiểm tra bổ sung cho file PDF
    if (!pdfFileAttach && !selectedDraw?.fileAttach) {
      message.error("Bản vẽ là bắt buộc!");
      return;
    }

    setLoading(true);
    try {
      const res = await drawApi.update(
        selectedDraw!?.id || 0,
        await getDataSubmit()
      );

      // Fetch lại dữ liệu đầy đủ sau khi update thành công
      await getOneDraw(selectedDraw!?.id || 0);

      message.success("Chỉnh sửa bản vẽ thành công!");
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = () => {
    if (status == "create") {
      createData();
    } else {
      updateData();
    }
  };

  const handleApproveProcess = async (data: ApproveData) => {
    console.log("Approve process");
    try {
      setLoadingApprove(true);
      await drawApi.approve(selectedDraw!.id || 0, data);
      message.success("Duyệt bản vẽ thành công!");
      await getOneDraw(selectedDraw!.id || 0);
      setCommentRefreshTrigger((prev) => prev + 1);
    } finally {
      setLoadingApprove(false);
    }
  };

  const handleRejectProcess = async (data: ApproveData) => {
    console.log("Reject process");
    try {
      setLoadingApprove(true);
      await drawApi.reject(selectedDraw!.id || 0, data);
      message.success("Từ chối bản vẽ thành công!");
      await getOneDraw(selectedDraw!.id || 0);
      setCommentRefreshTrigger((prev) => prev + 1);
    } finally {
      setLoadingApprove(false);
    }
  };

  const pageTitle = useMemo(
    () => (status == "create" ? "Tạo bản vẽ" : "Chỉnh sửa bản vẽ"),
    [status]
  );

  // Thêm state cho items trong tab Hạng mục
  const [items, setItems] = useState<any[]>([]);
  const [loadingItems, setLoadingItems] = useState(false);

  // Columns cho table trong tab Hạng mục
  const itemColumns: CustomizableColumn<any>[] = [
    {
      key: "page",
      title: "Preview",
      dataIndex: "page",
      width: 607,
      defaultVisible: true,
      alwaysVisible: true,
      align: "left",
      render: (_, record, index) => {
        return <div>Trang {index + 1}</div>;
      },
    },
    {
      key: "projectItemId",
      title: "Hạng mục",
      dataIndex: "projectItemId",
      width: 140,
      defaultVisible: true,
      alwaysVisible: true,
      render: (_, record) => {
        return <div>{record.projectItemId || "Kho nguyên liệu"}</div>;
      },
    },
    {
      key: "floor",
      title: "Tầng",
      dataIndex: "floor",
      width: 110,
      defaultVisible: true,
      alwaysVisible: true,
      render: (_, record) => {
        return <div>{record.floor || "Tầng"}</div>;
      },
    },
    {
      key: "actions",
      title: (
        <PlusOutlined
          style={{ fontSize: 18, cursor: readonly ? "not-allowed" : "pointer" }}
          onClick={() => !readonly && handleCategoryItem()}
        />
      ),
      width: 80,
      align: "center",
      fixed: "right",
      render: (_, record, index) => (
        <Button
          type="text"
          danger
          icon={<DeleteIcon />}
          onClick={(e) => {
            e.stopPropagation();
            Modal.confirm({
              title: `Xóa hạng mục "${record.materialStorage}"`,
              getContainer: () => {
                return document.getElementById("App") as HTMLElement;
              },
              icon: null,
              content: (
                <>
                  <div>
                    Bạn sẽ không thể khôi phục dữ liệu này sau khi xóa.
                    <br />
                    Bạn có chắc chắn muốn xóa dữ liệu này?
                  </div>
                </>
              ),
              footer: (_, { OkBtn, CancelBtn }) => (
                <>
                  <CustomButton
                    variant="outline"
                    className="cta-button"
                    onClick={() => {
                      handleDeleteCategory(record.id);
                      Modal.destroyAll();
                    }}
                  >
                    Có
                  </CustomButton>
                  <CustomButton
                    onClick={() => {
                      Modal.destroyAll();
                    }}
                    className="cta-button"
                  >
                    Không
                  </CustomButton>
                </>
              ),
            });
          }}
        />
      ),
    },
  ];

  // Thêm mock data cho items
  const mockItems = [
    { id: 1, materialStorage: "Kho nguyên liệu", inventory: "Tồn trữ" },
    { id: 2, materialStorage: "Kho nguyên liệu", inventory: "Tồn trữ" },
  ];

  const handleCategoryItem = () => {
    setAddModalVisible(true);
  };

  // Trong useEffect, thêm mock data
  useEffect(() => {
    // ...existing code...

    // Load mock data for items
    setItems(mockItems);
  }, [haveEditPermission]);

  return (
    <div className="app-container">
      <PageTitle
        back
        breadcrumbs={[
          { label: "Quản lý tài liệu & bản vẽ" },
          {
            label: "Bản vẽ",
            href: `/doc-management/${PermissionNames.blueprintList}`,
          },
          { label: pageTitle },
        ]}
        title={pageTitle}
      />
      <Spin spinning={loadingFetch}>
        <Form
          form={form}
          layout="vertical"
          disabled={readonly}
          className={clsx(readonly ? "readonly" : "")}
          onFinish={handleSubmit}
          initialValues={{
            createdDate: dayjs(),
          }}
        >
          <Row gutter={24}>
            <Col span={18}>
              <Card className="content-card">
                <Form.Item
                  name="pdfFile"
                  label="Bản vẽ"
                  rules={pdfRules}
                  validateTrigger={["onChange", "onBlur"]}
                >
                  <div style={{ width: "100%", position: "relative" }}>
                    <SingleImageUpload
                      imageUrl={pdfUrl}
                      onUploadOk={(fileAttach) => {
                        setPdfUrl(fileAttach.path);
                        setPdfFileAttach(fileAttach);
                        form.setFieldsValue({ pdfFile: fileAttach });
                        form.validateFields(["pdfFile"]);
                      }}
                      fileType="PDF"
                      hideUploadButton={readonly}
                    />
                    {pdfUrl && !readonly && (
                      <Button
                        type="text"
                        danger
                        size="small"
                        style={{
                          position: "absolute",
                          top: 8,
                          right: 8,
                          zIndex: 10,
                          background: "rgba(255, 255, 255, 0.9)",
                          border: "1px solid #ff4d4f",
                        }}
                        onClick={() => {
                          setPdfUrl("");
                          setPdfFileAttach(null);
                          form.setFieldsValue({ pdfFile: undefined });
                          form.validateFields(["pdfFile"]);
                        }}
                      >
                        Xóa
                      </Button>
                    )}
                  </div>
                </Form.Item>

                <Row gutter={16} className="mt-[16px]">
                  {/* First Row */}
                  <Col span={8}>
                    <Form.Item name="code" label="Mã bản vẽ" rules={rules}>
                      <Input placeholder="Mã bản vẽ" />
                    </Form.Item>
                  </Col>

                  <Col span={8}>
                    <Form.Item
                      name="drawCategory"
                      label="Loại bản vẽ"
                      rules={rules}
                    >
                      <DictionarySelector
                        placeholder="Loại bản vẽ"
                        initQuery={{
                          type: DictionaryType.DrawCategory,
                          isActive: true,
                        }}
                      />
                    </Form.Item>
                  </Col>

                  <Col span={8}>
                    <Form.Item
                      name="memberShipId"
                      label="Người phụ trách"
                      rules={rules}
                    >
                      <MembershipSelector placeholder="Người phụ trách" />
                    </Form.Item>
                  </Col>

                  <Col span={24}>
                    <Form.Item name="title" label="Tiêu đề" rules={rules}>
                      <Input placeholder="Tiêu đề" />
                    </Form.Item>
                  </Col>

                  <Col span={24}>
                    <Form.Item
                      name="note"
                      label="Ghi chú"
                      rules={descriptionRules}
                    >
                      <BMDCKEditor
                        placeholder="Ghi chú"
                        value={selectedDraw?.note}
                        disabled={readonly}
                        inputHeight={300}
                        onChange={(content) => {
                          form.setFieldsValue({ note: content });
                        }}
                      />
                    </Form.Item>
                  </Col>

                  {/* QR Code Section với đầy đủ props */}
                  <Col span={24}>
                    <div style={{ marginTop: "16px" }}>
                      <QRCodeSection
                        blueprintCode={blueprintCode}
                        blueprintId={selectedDraw?.id}
                        title={blueprintTitle}
                        section={blueprintSection}
                        staff={memberShipObj}
                        drawCategory={blueprintDrawCategory}
                      />
                    </div>
                  </Col>
                </Row>

                {/* Second Row */}
                <Tabs defaultActiveKey="0" type="line" className="mt-[16px]">
                  <Tabs.TabPane tab="Hạng mục" key="0">
                    <div>
                      <CustomizableTable
                        columns={itemColumns}
                        dataSource={items}
                        loading={loadingItems}
                        pagination={false}
                        rowKey="id"
                        scroll={{ x: 400 }}
                        size="small"
                        bordered
                      />
                    </div>
                  </Tabs.TabPane>
                  <Tabs.TabPane tab="Phiên bản" key="1">
                    <Form.Item
                      shouldUpdate={true}
                      style={{ marginBottom: 0, height: "100%" }}
                      className="form-height-full"
                    >
                      {() => {
                        return (
                          <Form.Item
                            label={""}
                            noStyle
                            style={{ marginBottom: 0 }}
                            name="files"
                            className="h-full "
                          >
                            <FileUploadMultiple2
                              hideUploadButton={readonly}
                              showSearch
                              className="h-full"
                              fileList={fileList}
                              onUploadOk={(file) => {
                                fileList.push(file);
                                setFileList([...fileList]);
                              }}
                              onDelete={(file) => {
                                const findIndex = fileList.findIndex(
                                  (e) => e.uid == file.uid
                                );

                                if (findIndex > -1) {
                                  fileList.splice(findIndex, 1);
                                  setFileList([...fileList]);
                                }
                              }}
                            />
                          </Form.Item>
                        );
                      }}
                    </Form.Item>{" "}
                  </Tabs.TabPane>
                  {selectedDraw && readonly && (
                    <Tabs.TabPane tab="Bình luận" key="2">
                      <CommentView
                        initQuery={{ drawId: selectedDraw?.id }}
                        refreshTrigger={commentRefreshTrigger}
                      />
                    </Tabs.TabPane>
                  )}
                </Tabs>

                {/* Action Buttons */}
                <Col span={24}>
                  <div
                    className="mt-[16px]"
                    style={{
                      display: "flex",
                      justifyContent: "flex-end",
                      gap: "12px",
                    }}
                  >
                    {!readonly && (
                      <CustomButton
                        variant="outline"
                        className="cta-button"
                        onClick={() => {
                          if (status == "create") {
                            navigate(
                              `/doc-management/${PermissionNames.blueprintList}`
                            );
                          } else {
                            setDataToForm(selectedDraw!);
                            setReadonly(true);
                          }
                        }}
                      >
                        Hủy
                      </CustomButton>
                    )}
                    <CustomButton
                      className="cta-button"
                      loading={loading}
                      disabled={status == "update" && !haveEditPermission}
                      onClick={() => {
                        if (!readonly) {
                          handleSubmit();
                        } else {
                          setReadonly(false);
                        }
                      }}
                    >
                      {status == "create"
                        ? "Tạo bản vẽ"
                        : readonly
                        ? "Chỉnh sửa"
                        : "Lưu chỉnh sửa"}
                    </CustomButton>
                  </div>
                </Col>
              </Card>
            </Col>

            <Col span={6}>
              <ApprovalStepsCard
                steps={approvalSteps}
                loading={loadingApprove}
                onSelectStep={setApprovalSteps}
                onRemove={setRemoveApprovalList}
                onApprove={handleApproveProcess}
                onReject={handleRejectProcess}
                templateType={ApprovalTemplateType.Draw}
                isShowActionButton={status == "update"}
              />

              <FollowerSelector
                followers={followers}
                setFollowers={setFollowers}
                readonly={readonly}
                headerTitle={`Người theo dõi (${followers?.length})`}
              />
            </Col>
          </Row>
        </Form>
      </Spin>

      {/* Modal thêm Hạng mục */}
      <AddCategoryModal
        visible={addModalVisible}
        onClose={() => setAddModalVisible(false)}
        onSubmit={(item) => {
          // Xử lý thêm item vào bảng
          setAddModalVisible(false);
        }}
        sectionOptions={[
          { label: "Kho nguyên liệu", value: "Kho nguyên liệu" },
          // ...các hạng mục khác
        ]}
        floorOptions={[
          { label: "Tầng trệt", value: "Tầng trệt" },
          // ...các tầng khác
        ]}
      />
    </div>
  );
}

export default observer(CreateOrUpdateBluePrintPage);
