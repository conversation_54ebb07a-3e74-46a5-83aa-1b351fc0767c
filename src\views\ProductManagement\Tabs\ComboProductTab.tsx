import {
  DownOutlined,
  LockOutlined,
  PlusOutlined,
  SearchOutlined,
  UnlockOutlined,
} from "@ant-design/icons";
import {
  Button,
  Input,
  message,
  Popconfirm,
  Space,
  Spin,
  Table,
  Tag,
} from "antd";
import { productApi } from "api/product.api";
import { Pagination } from "components/Pagination";
import React, { useEffect, useState, useRef, useCallback } from "react";
import { ModalStatus } from "types/modal";
import { QueryParam } from "types/query";
import {
  Product,
  ProductStatus,
  ProductType,
  ProductTypeTrans,
} from "types/product";
import { formatVND, getTitle } from "utils";
import { $url } from "utils/url";
import { ComboProductTabModal } from "../Components/ComboProductTabModal";
import { useProduct } from "hooks/useProduct";
import DropdownCell from "components/Table/DropdownCell";
import { CreateComboProductModal } from "../Components/CreateComboProductModal";
import { Link } from "react-router-dom";
import { debounce } from "lodash";

const { ColumnGroup, Column } = Table;
const productComboUrl = import.meta.env.VITE_PRODUCT_COMBO_URL;

export const ComboProductTab = ({ title = "" }) => {
  const [data, setData] = useState<Product[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<Partial<Product>>({});
  const modalRef = useRef<ComboProductTabModal>(null);
  const [deleteLoading, setDeleteLoading] = useState<boolean>(false);
  const lastFilterChanged = useRef<"name" | "code" | undefined>();
  useEffect(() => {
    document.title = getTitle(title);
  }, []);

  const { products, fetchData, query, loading, setQuery, total } = useProduct({
    initQuery: {
      page: 1,
      limit: 10,
      search: "",
      type: ProductType.Combo,
    },
  });
  useEffect(() => {
    fetchData();
  }, [query]);
  const handleDelete = async (id: number) => {
    try {
      setDeleteLoading(true);
      await productApi.delete(id);
      message.success("Xóa thành công");
      fetchData();
    } catch (e) {
      console.log({ e });
    } finally {
      setDeleteLoading(false);
    }
  };
  const blockProduct = async (product: Product) => {
    if (product.status == ProductStatus.Active) {
      await productApi.inactive(product.id);
    } else {
      await productApi.active(product.id);
    }
    message.success(
      `${
        product.status == ProductStatus.Inactive ? "Hiện" : "Ẩn"
      } sản phẩm thành công!`
    );
    fetchData();
  };
  const handleFilterOfTable = (pagination: any, filters: any, sorter: any) => {
    const filterCode = filters.code?.[0];
    const filterName = filters.name?.[0];

    const queryObject: any[] = [];

    if (filterCode && filterName) {
      // Xử lý clear 1 trong 2 theo người dùng chọn cuối
      if (lastFilterChanged.current === "name") {
        filters.code = undefined;
      } else if (lastFilterChanged.current === "code") {
        filters.name = undefined;
      }
    }

    if (filters.code?.[0]) {
      queryObject.push({
        type: "sort",
        field: "product.code",
        value: filters.code?.[0],
      });
    }

    if (filters.name?.[0]) {
      queryObject.push({
        type: "sort",
        field: "product.name",
        value: filters.name?.[0],
      });
    }

    query.queryObject = JSON.stringify(queryObject);
    console.log(
      "Khi filter",
      (query.queryObject = JSON.stringify(queryObject))
    );
    setQuery({ ...query });
    fetchData();
  };
  const debounceSearch = useCallback(
    debounce(
      (keyword) => setQuery({ ...query, search: keyword, page: 1 }),
      300
    ),
    [query]
  );
  return (
    <div>
      <div className="filter-container">
        <Space>
          <div className="filter-item">
            <label htmlFor="">Tìm kiếm</label>
            <Input
              allowClear
              onKeyDown={(ev) => {
                if (ev.code == "Enter") {
                  query.page = 1;
                  setQuery({ ...query });
                }
              }}
              size="middle"
              onChange={(ev) => {
                // query.search = ev.currentTarget.value;
                debounceSearch(ev.target.value);
              }}
              placeholder="Tìm kiếm"
            />
          </div>

          <div className="filter-item btn">
            <Button
              onClick={fetchData}
              type="primary"
              icon={<SearchOutlined />}
            >
              Tìm kiếm
            </Button>
          </div>

          <div className="filter-item btn">
            <Button
              onClick={() => {
                modalRef.current?.handleCreate();
              }}
              type="primary"
              icon={<PlusOutlined />}
            >
              Thêm mới
            </Button>
          </div>
        </Space>
      </div>

      <Spin spinning={loading}>
        <Table
          pagination={false}
          rowKey="id"
          dataSource={products}
          scroll={{ x: "max-content" }}
          onChange={handleFilterOfTable}
        >
          <Column
            title="ID"
            dataIndex="id"
            key="id"
            align="center"
            render={(text, record: Product) => {
              return <div>{record.id}</div>;
            }}
          />
          <Column
            title="Ảnh"
            dataIndex="fileAttachIcon"
            key="fileAttachIcon"
            align="center"
            render={(text, record: Product) => {
              return (
                <div>
                  {record.fileAttachIcon && (
                    <img
                      width={40}
                      height={40}
                      style={{ objectFit: "cover" }}
                      src={$url(record.fileAttachIcon?.url)}
                      alt=""
                    />
                  )}
                </div>
              );
            }}
          />
          <Column
            title="Mã sản phẩm"
            dataIndex="code"
            align="left"
            key="code"
            render={(text, record) => {
              return <div>{text}</div>;
            }}
            filteredValue={(() => {
              try {
                const obj = JSON.parse(query.queryObject || "[]");
                const item = obj.find((o: any) => o.field === "product.code");
                return item ? [item.value] : null;
              } catch (e) {
                return null;
              }
            })()}
            filterDropdownProps={{
              onOpenChange: (open) => {
                if (open) lastFilterChanged.current = "code";
              },
            }}
            filterMultiple={false}
            filters={[
              { text: "A-Z", value: "ASC" },
              { text: "Z-A", value: "DESC" },
            ]}
          />
          <Column
            title="Tên nội bộ"
            dataIndex="name"
            align="left"
            key="name"
            render={(text, record) => {
              return <div>{text}</div>;
            }}
            filterMultiple={false}
            filteredValue={(() => {
              try {
                const obj = JSON.parse(query.queryObject || "[]");
                const item = obj.find((o: any) => o.field === "product.name");
                return item ? [item.value] : null;
              } catch (e) {
                return null;
              }
            })()}
            filterDropdownProps={{
              onOpenChange: (open) => {
                if (open) lastFilterChanged.current = "name";
              },
            }}
            filters={[
              { text: "A-Z", value: "ASC" },
              { text: "Z-A", value: "DESC" },
            ]}
          />
          <Column
            title="Tên hiển thị"
            dataIndex="nameVi"
            align="left"
            key="nameVi"
            render={(text, record) => {
              return <div>{text}</div>;
            }}
          />
          <Column
            title="Đường dẫn"
            dataIndex="url"
            align="left"
            key="url"
            render={(text, record: Product) => {
              return (
                <Button
                  className="px-0"
                  type="link"
                  onClick={() => {
                    const url = `${productComboUrl}?productComboId=${record.id}`;
                    window.open(url, "_blank", "noopener,noreferrer");
                  }}
                >
                  Đường dẫn tới bộ sản phẩm
                </Button>
              );
            }}
          />
          <Column
            title="Loại sản phẩm"
            dataIndex="type"
            align="left"
            key="type"
            render={(type: ProductType, record: Product) => {
              return <div>{ProductTypeTrans[type].label}</div>;
            }}
          />
          {/* <Column
            title="Nhóm cha"
            dataIndex="parent"
            key="parent"
            align="left"
            render={(parent: Component, record: Component) => (
              <div className="flex flex-wrap gap-1 justify-center w-full">
                {parent && (
                  <Tag color={getColor(parent?.id)}>
                    {parent?.code}-{parent?.name}
                  </Tag>
                )}
              </div>
            )}
          /> */}
          <Column
            title="Ghi chú"
            dataIndex="note"
            align="left"
            key="note"
            render={(text, record) => {
              return <div>{text}</div>;
            }}
          />
          {/* <Column
            title="Giá tiền ($)"
            dataIndex="price"
            align="left"
            key="price"
            render={(price, record) => {
              return <div>{formatVND(price)}</div>;
            }}
          /> */}
          <Column
            title="Trạng thái"
            align="center"
            dataIndex="status"
            key="status"
            render={(status) => {
              return (
                <Tag
                  className=" text-center"
                  color={status == ProductStatus.Active ? "green" : "red"}
                >
                  {status == ProductStatus.Active ? "Hiện" : "Ẩn"}
                </Tag>
              );
            }}
          />
          <Column
            width={120}
            fixed="right"
            align="center"
            title=""
            key="action"
            render={(text, record: Product) => (
              <DropdownCell
                text="Thao tác"
                items={[
                  {
                    onClick: () => "",
                    label: (
                      <Button
                        className="w-full"
                        type="primary"
                        onClick={() => {
                          modalRef.current?.handleUpdate(record);
                        }}
                      >
                        Cập nhật
                      </Button>
                    ),
                    key: "update",
                  },
                  {
                    onClick: () => "",
                    label: (
                      <Popconfirm
                        onConfirm={() => {
                          handleDelete(record.id);
                        }}
                        title="Xác nhận xóa"
                      >
                        <Button
                          loading={deleteLoading}
                          className="w-full"
                          //   onClick={() => {
                          //     modalRef.current?.handleUpdate(record);
                          //   }}
                        >
                          Xóa sản phẩm
                        </Button>
                      </Popconfirm>
                    ),
                  },

                  {
                    label: (
                      <Popconfirm
                        placement="topLeft"
                        title={
                          <div>
                            <h1 className="text-sm">
                              Xác nhận{" "}
                              {record.status == ProductStatus.Inactive
                                ? "hiện "
                                : "ẩn "}
                              bộ sản phẩm này?
                            </h1>
                          </div>
                        }
                        onConfirm={() => blockProduct(record)}
                        okText="Đồng ý"
                        cancelText="Không"
                      >
                        <Button
                          icon={
                            record.status == ProductStatus.Inactive ? (
                              <UnlockOutlined />
                            ) : (
                              <LockOutlined />
                            )
                          }
                          // type="ghost"
                          className={`w-full !text-white ${
                            record.status ? "!bg-green-500" : "!bg-amber-500"
                          } !font-medium`}
                        >
                          {record.status == ProductStatus.Inactive
                            ? "Hiện bộ sản phẩm"
                            : "Ẩn bộ sản phẩm"}
                        </Button>
                      </Popconfirm>
                    ),
                    key: "blockStaff",
                  },
                ]}
                trigger={["click"]}
              >
                <a onClick={(e) => e.preventDefault()}>
                  <Space className="bg-black">
                    Thao tác
                    <DownOutlined />
                  </Space>
                </a>
              </DropdownCell>
            )}
          />
        </Table>
        <Pagination
          currentPage={query.page}
          total={total}
          defaultPageSize={query.limit}
          onChange={({ limit, page }) => {
            query.page = page;
            query.limit = limit;
            setQuery({ ...query });
          }}
        />
      </Spin>

      <CreateComboProductModal
        onSubmitOk={fetchData}
        onClose={() => {}}
        ref={modalRef}
        isCombo={true}
      />
    </div>
  );
};
