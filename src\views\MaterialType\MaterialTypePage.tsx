import { PlusOutlined, SearchOutlined } from "@ant-design/icons";
import { Button, Input, message, Popconfirm, Space, Spin, Table } from "antd";
import { Pagination } from "components/Pagination";
import React, { useEffect, useState, useRef, useCallback } from "react";

import { getTitle } from "utils";
import { $url } from "utils/url";
import { useMaterialType } from "hooks/useMaterialType";
import { MaterialTypeModal } from "./components/MaterialTypeModal";
import { MaterialType } from "types/materialType";
import { materialTypeApi } from "api/materialType";
import { debounce } from "lodash";

const { ColumnGroup, Column } = Table;

export const MaterialTypePage = ({ title = "" }) => {
  const modalRef = useRef<MaterialTypeModal>(null);

  useEffect(() => {
    document.title = getTitle(title);
  }, []);
  const { fetchData, loading, materialTypes, query, setQuery, total } =
    useMaterialType({
      initQuery: {
        limit: 10,
        page: 1,
      },
    });
  useEffect(() => {
    fetchData();
  }, [query]);
  const handleDelete = async (id: number) => {
    try {
      await materialTypeApi.delete(id);
      message.success("Xóa thành công");
      fetchData();
    } catch (e) {
      console.log({ e });
    } finally {
      // setLoadingDelete(false);
    }
  };
  const debounceSearch = useCallback(
    debounce(
      (keyword) => setQuery({ ...query, search: keyword, page: 1 }),
      300
    ),
    []
  );
  const handleFilterOfTable = (pagination: any, filters: any, sorter: any) => {
    console.log("sorter:", sorter);
    console.log("filters:", filters);

    let sortValue: "ASC" | "DESC" | undefined;
    if (!Array.isArray(sorter) && sorter?.order) {
      sortValue =
        sorter.order === "ascend"
          ? "ASC"
          : sorter.order === "descend"
          ? "DESC"
          : undefined;
    }
    const queryObject: any[] = [];
    if (filters.name?.[0] !== undefined) {
      queryObject.push({
        type: "sort",
        field: "materialType.name",
        value: filters.name?.[0],
      });
    }
    query.queryObject = JSON.stringify(queryObject);
    fetchData();
  };
  return (
    <div>
      <div className="filter-container">
        <Space>
          <div className="filter-item">
            <label htmlFor="">Tìm kiếm</label>
            <Input
              onKeyDown={(ev) => {
                if (ev.code == "Enter") {
                  query.page = 1;
                  setQuery({ ...query });
                }
              }}
              allowClear
              size="middle"
              onChange={(e) => {
                debounceSearch(e.target.value);
              }}
              placeholder="Tìm kiếm"
            />
          </div>

          <div className="filter-item btn">
            <Button
              onClick={fetchData}
              type="primary"
              icon={<SearchOutlined />}
            >
              Tìm kiếm
            </Button>
          </div>

          <div className="filter-item btn">
            <Button
              onClick={() => {
                modalRef.current?.handleCreate();
              }}
              type="primary"
              icon={<PlusOutlined />}
            >
              Thêm mới
            </Button>
          </div>
        </Space>
      </div>

      <Spin spinning={loading}>
        <Table
          // loading={loading}
          pagination={false}
          rowKey="id"
          dataSource={materialTypes}
          onChange={handleFilterOfTable}
        >
          <Column
            title="Loại NVL"
            dataIndex="name"
            key="name"
            filterMultiple={false}
            filters={[
              { text: "A-Z", value: "ASC" },
              { text: "Z-A", value: "DESC" },
            ]}
          />
          <Column title="Mô tả" dataIndex="description" key="description" />
          <Column
            fixed="right"
            width={150}
            title="Thao tác"
            key="action"
            render={(text, record: MaterialType) => (
              <div className="flex gap-2">
                <Popconfirm
                  onConfirm={() => {
                    handleDelete(record.id);
                  }}
                  title="Xác nhận xóa"
                >
                  <Button danger>Xóa</Button>
                </Popconfirm>
                <Button
                  type="primary"
                  onClick={() => {
                    modalRef.current?.handleUpdate(record);
                  }}
                >
                  Cập nhật
                </Button>
              </div>
            )}
          />
        </Table>

        <Pagination
          defaultPageSize={query.limit}
          currentPage={query.page}
          total={total}
          onChange={({ limit, page }) => {
            query.page = page;
            query.limit = limit;
            setQuery({ ...query });
          }}
        />
      </Spin>

      <MaterialTypeModal
        onSubmitOk={fetchData}
        onClose={() => {}}
        ref={modalRef}
      />
    </div>
  );
};
