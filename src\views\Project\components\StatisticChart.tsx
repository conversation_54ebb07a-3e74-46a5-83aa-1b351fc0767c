import { useTheme } from "context/ThemeContext";
import ReactECharts from "echarts-for-react";
import { ColorThemes } from "utils/theme";

interface StatisticChartProps {
  className?: string;
}

function StatisticChart({ className }: StatisticChartProps) {
  // Project statistics data
  const projectData = [
    { value: 5, name: "<PERSON>ế hoạch", itemStyle: { color: "#4F46E5" } },
    { value: 8, name: "<PERSON><PERSON> thi công", itemStyle: { color: "#F59E0B" } },
    { value: 1, name: "Tạ<PERSON> hoãn", itemStyle: { color: "#9CA3AF" } },
    { value: 16, name: "<PERSON><PERSON><PERSON> tất", itemStyle: { color: "#10B981" } },
  ];

  // Work status data
  const workData = [
    { value: 40, name: "<PERSON><PERSON><PERSON> thường", itemStyle: { color: "#4F46E5" } },
    { value: 5, name: "<PERSON>ă<PERSON> tốc", itemStyle: { color: "#10B981" } },
    { value: 3, name: "<PERSON><PERSON><PERSON> <PERSON>", itemStyle: { color: "#F59E0B" } },
    { value: 2, name: "<PERSON><PERSON><PERSON> ro", itemStyle: { color: "#F97316" } },
    { value: 1, name: "Chậm trễ", itemStyle: { color: "#EF4444" } },
  ];

  const { darkMode } = useTheme();

  // Consistent text style for graphic elements
  const getTextStyle = (fontSize: number, fontWeight: string = "normal") => ({
    fontSize,
    fontWeight,
    fontFamily:
      "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
    fill: darkMode ? ColorThemes.dark.neutral.n8 : ColorThemes.light.neutral.n8,
  });

  const getSubTextStyle = (fontSize: number) => ({
    fontSize,
    fontFamily:
      "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
    fill: darkMode ? ColorThemes.dark.neutral.n6 : ColorThemes.light.neutral.n6,
  });

  // Check if mobile/tablet viewport
  const isMobile = typeof window !== "undefined" && window.innerWidth < 768;

  const projectOption = {
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c} ({d}%)",
    },
    legend: {
      orient: isMobile ? "horizontal" : "vertical",
      top: isMobile ? "bottom" : "center",
      left: isMobile ? "center" : "auto",
      right: isMobile ? "auto" : 5,
      bottom: isMobile ? 10 : "auto",
      itemAlign: "left",
      itemGap: isMobile ? 15 : 20,
      itemWidth: isMobile ? 12 : 14,
      itemHeight: isMobile ? 12 : 14,
      formatter: function (name: string) {
        const item = projectData.find((d) => d.name === name);
        const percentage = (((item?.value || 0) / 30) * 100).toFixed(2);
        return `${name}  ${item?.value} (${percentage}%)`;
      },
      textStyle: {
        fontSize: isMobile ? 12 : 14,
        fontFamily:
          "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
        color: darkMode
          ? ColorThemes.dark.neutral.n8
          : ColorThemes.light.neutral.n8,
      },
    },
    series: [
      {
        name: "Dự án",
        type: "pie",
        radius: ["50%", "75%"],
        center: isMobile ? ["50%", "50%"] : ["20%", "50%"],
        avoidLabelOverlap: false,
        label: {
          show: false,
        },
        emphasis: {
          label: {
            show: false,
          },
        },
        labelLine: {
          show: false,
        },
        data: projectData,
      },
    ],
    graphic: [
      {
        type: "text",
        style: {
          text: "THỐNG KÊ DỰ ÁN",
          ...getTextStyle(14, "bold"),
          textAlign: isMobile ? "center" : "left",
        },
        left: isMobile ? "50%" : "1%",
        top: 5,
      },
      {
        type: "group",
        left: isMobile ? "50%" : "20%",
        top: isMobile ? "45%" : "45%",
        bounding: "raw",
        children: [
          {
            type: "text",
            style: {
              text: "30",
              ...getTextStyle(34, "bold"),
              textAlign: "center",
              textVerticalAlign: "middle",
            },
            x: 0,
            y: 0,
          },
          {
            type: "text",
            style: {
              text: "Dự án",
              ...getSubTextStyle(12),
              textAlign: "center",
              textVerticalAlign: "top",
            },
            x: 0,
            y: 30,
          },
        ],
      },
    ],
  };

  const workOption = {
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c} ({d}%)",
    },
    legend: {
      orient: isMobile ? "horizontal" : "vertical",
      top: isMobile ? "bottom" : "center",
      left: isMobile ? "center" : "auto",
      right: isMobile ? "auto" : 5,
      bottom: isMobile ? 10 : "auto",
      itemAlign: "left",
      itemGap: isMobile ? 15 : 20,
      itemWidth: isMobile ? 12 : 14,
      itemHeight: isMobile ? 12 : 14,
      formatter: function (name: string) {
        const item = workData.find((d) => d.name === name);
        const percentage = (((item?.value || 0) / 51) * 100).toFixed(2);
        return `${name}  ${item?.value} (${percentage}%)`;
      },
      textStyle: {
        fontSize: 14,
        fontFamily:
          "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
        color: darkMode
          ? ColorThemes.dark.neutral.n8
          : ColorThemes.light.neutral.n8,
      },
    },
    series: [
      {
        name: "Công việc",
        type: "pie",
        radius: ["50%", "75%"],
        center: isMobile ? ["50%", "50%"] : ["20%", "50%"],
        avoidLabelOverlap: false,
        label: {
          show: false,
        },
        emphasis: {
          label: {
            show: false,
          },
        },
        labelLine: {
          show: false,
        },
        data: workData,
      },
    ],
    graphic: [
      {
        type: "text",
        style: {
          text: "THỐNG KÊ CÔNG VIỆC",
          ...getTextStyle(14, "bold"),
          textAlign: isMobile ? "center" : "left",
        },
        left: isMobile ? "50%" : "1%",
        top: 5,
      },
      {
        type: "group",
        left: isMobile ? "50%" : "20%",
        top: isMobile ? "45%" : "45%",
        bounding: "raw",
        children: [
          {
            type: "text",
            style: {
              text: "51",
              ...getTextStyle(34, "bold"),
              textAlign: "center",
              textVerticalAlign: "middle",
            },
            x: 0,
            y: 0,
          },
          {
            type: "text",
            style: {
              text: "Công việc",
              ...getSubTextStyle(12),
              textAlign: "center",
              textVerticalAlign: "top",
            },
            x: 0,
            y: 30,
          },
        ],
      },
    ],
  };

  return (
    <div className={`grid grid-cols-1 lg:grid-cols-2 gap-6 ${className || ""}`}>
      <div className="card-box">
        <ReactECharts
          option={projectOption}
          style={{ height: "210px", width: "100%" }}
          opts={{ renderer: "canvas" }}
        />
      </div>
      <div className="card-box">
        <ReactECharts
          option={workOption}
          style={{ height: "210px", width: "100%" }}
          opts={{ renderer: "canvas" }}
        />
      </div>
    </div>
  );
}

export default StatisticChart;
