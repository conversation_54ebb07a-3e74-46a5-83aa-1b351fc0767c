// ImageManager.tsx
import { LockOutlined, UnlockOutlined, LeftOutlined } from "@ant-design/icons";
import {
  Button,
  Card,
  message,
  Modal,
  Select,
  Space,
  Tag,
  Tooltip,
} from "antd";
import { TableProps } from "antd/lib";
import { documentApi } from "api/document.api";
import PencilIcon from "assets/svgs/PencilIcon";
import CustomButton from "components/Button/CustomButton";
import LockButton from "components/Button/LockButton";
import CustomInput from "components/Input/CustomInput";
import { ChooseImageMode } from "components/Modal/ChooseImageModal";
import PageTitle from "components/PageTitle/PageTitle";
import { Pagination } from "components/Pagination";
import QueryLabel from "components/QueryLabel/QueryLabel";
import { DictionarySelector } from "components/Selector/DictionarySelector";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import { useDocument } from "hooks/useDocument";
import { observer } from "mobx-react";
import React, { useEffect, useState, useRef } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { permissionStore } from "store/permissionStore";
import { userStore } from "store/userStore";
import { DictionaryType } from "types/dictionary";
import { Document, DocumentType, DocumentModule } from "types/document";
import { FileAttach } from "types/fileAttach";
import { PermissionNames } from "types/PermissionNames";
import { checkRoles, filterActionColumnIfNoPermission } from "utils/auth";
import { MyExcelColumn } from "utils/MyExcelFromPageToPage";
import { FolderCreateModal } from "components/FolderCreateModal/FolderCreateModal";
import { FolderOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
import { FileIcon } from "assets/svgs/FileIcon";
import FolderIcon from "assets/svgs/FolderIcon";
import EditButton from "components/Button/EditButton";

const DocumentPage = ({
  title,
  chooseMode,
  onChoose,
}: {
  title: string;
  chooseMode?: ChooseImageMode;
  onChoose?: (file: FileAttach) => void;
}) => {
  const {
    haveAddPermission,
    haveBlockPermission,
    haveEditPermission,
    haveViewAllPermission,
  } = checkRoles(
    {
      add: PermissionNames.documentAdd,
      edit: PermissionNames.documentEdit,
      block: PermissionNames.documentBlock,
      viewAll: PermissionNames.documentViewAll,
    },
    permissionStore.permissions
  );

  const [exportExcelLoading, setExportExcelLoading] = useState<boolean>(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [fromPage, setFromPage] = useState<number | undefined>();
  const [toPage, setToPage] = useState<number | undefined>();
  const [currentPath, setCurrentPath] = useState<string>("/");
  const [searchParams, setSearchParams] = useSearchParams();
  const folderCreateModalRef = useRef<FolderCreateModal>();
  const navigate = useNavigate();

  // Update the component to add folder editing state
  const [editingFolder, setEditingFolder] = useState<Document | null>(null);

  // Get initial path from URL params for useDocument initialization
  const getInitialPath = () => {
    const pathParam = searchParams.get("path");
    if (pathParam) {
      try {
        const decodedPath = decodeURIComponent(pathParam.replace(/\+/g, " "));
        console.log("🔍 Initial path from URL:", decodedPath);
        return decodedPath;
      } catch (error) {
        console.error("Error decoding initial path:", error);
        return "/";
      }
    }
    return "/";
  };

  const { documents, fetchData, loading, setQuery, total, query } = useDocument(
    {
      initQuery: {
        page: 1,
        limit: 10,
        isAdmin: haveViewAllPermission ? true : undefined,
        type: DocumentType.Document,
        path: getInitialPath(),
      },
    }
  );

  const onSelectChange = (
    newSelectedRowKeys: React.Key[],
    newSelectedRows: Document[]
  ) => {
    setSelectedRowKeys(newSelectedRowKeys);
    console.log("Selected Rows: ", newSelectedRows);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  console.log("🔍 DocumentPage - Current query:", query);
  console.log("🔍 DocumentPage - Current currentPath:", currentPath);

  const applyFilters = () => {
    console.log("Áp dụng");
    query.page = 1; // Reset về trang 1 khi apply filter
    setQuery({ ...query });
    fetchData();
  };

  const exportColumns: MyExcelColumn<Document>[] = [
    {
      header: "Tên tài liệu",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "name",
      columnKey: "name",
    },
  ];

  const handleDeleteDocument = async (ids?: React.Key[]) => {
    try {
      if ((ids ?? []).length > 0) {
        if (ids && ids.length > 0) {
          await Promise.all(ids.map((id) => documentApi.delete(Number(id))));
        }
        await fetchData();
        message.success("Xóa tài liệu thành công");
        setSelectedRowKeys([]);
      }
    } catch (error) {
      console.log("Error", error);
    }
  };

  const handleBlockDocument = async (id: number, isActive: boolean) => {
    try {
      await documentApi.update(id, {
        document: {
          isActive: !isActive,
        },
      });
      message.success(isActive ? "Khóa thành công" : "Mở khóa thành công");
      fetchData();
    } catch (error) {
      console.log("Error", error);
    }
  };

  // Update handleCreateFolder to handle both create and update
  const handleCreateFolder = async (name: string) => {
    try {
      const payload = {
        categoryId: 0,
        fileAttachIds: [],
        departmentId: 0,
        createdById: userStore.info?.id || 0,
        projectId: 0,
        document: {
          name: name,
          isActive: true,
          code: "", // Will be auto-generated
          path: currentPath,
          module: DocumentModule.Folder,
          description: ``,
          type: DocumentType.Document,
          createdDate: dayjs().format("YYYY-MM-DD"),
          releaseDate: dayjs().format("YYYY-MM-DD"),
        },
      };

      await documentApi.create(payload);
      message.success("Tạo thư mục thành công!");
      fetchData(); // Refresh the data
    } catch (error) {
      console.error("Error creating folder:", error);
      message.error("Lỗi khi tạo thư mục");
    }
  };

  // Add folder update function
  const handleUpdateFolder = async (name: string) => {
    if (!editingFolder) return;

    try {
      await documentApi.update(editingFolder.id, {
        document: {
          name: name,
        },
      });
      message.success("Cập nhật tên thư mục thành công!");
      setEditingFolder(null);
      fetchData(); // Refresh the data
    } catch (error) {
      console.error("Error updating folder:", error);
      message.error("Lỗi khi cập nhật thư mục");
    }
  };

  // Update the modal callback to handle both modes
  const handleFolderModalSubmit = (name: string) => {
    if (editingFolder) {
      handleUpdateFolder(name);
    } else {
      handleCreateFolder(name);
    }
  };

  const handleFolderClick = (folder: Document) => {
    if (folder.module === DocumentModule.Folder) {
      const newPath =
        folder.path === "/"
          ? `/${folder.name}`
          : `${folder.path}/${folder.name}`;
      setCurrentPath(newPath);
      setSearchParams({ path: newPath });
      // Cập nhật query ngay lập tức với path mới
      setQuery({ ...query, path: newPath });
    }
  };

  const handleBackToParent = () => {
    if (currentPath !== "/") {
      const parentPath =
        currentPath.substring(0, currentPath.lastIndexOf("/")) || "/";
      setCurrentPath(parentPath);
      setSearchParams(parentPath === "/" ? {} : { path: parentPath });
      // Cập nhật query ngay lập tức với path mới
      setQuery({ ...query, path: parentPath });
    }
  };

  const getBreadcrumbItems = () => {
    if (currentPath === "/") return [];

    const pathSegments = currentPath.split("/").filter(Boolean);
    const breadcrumbs: { label: string; onClick?: () => void }[] = [
      {
        label: "Thư mục gốc",
        onClick: () => {
          setCurrentPath("/");
          setSearchParams({});
          setQuery({ ...query, path: "/" });
        },
      },
    ];

    // Tạo breadcrumb cho từng segment
    pathSegments.forEach((segment, index) => {
      // Tính path cho segment này
      const pathForThisSegment =
        "/" + pathSegments.slice(0, index + 1).join("/");

      breadcrumbs.push({
        label: segment,
        onClick:
          index < pathSegments.length - 1 // Chỉ có onClick nếu không phải segment cuối
            ? () => {
                setCurrentPath(pathForThisSegment);
                setSearchParams({ path: pathForThisSegment });
                setQuery({ ...query, path: pathForThisSegment });
              }
            : undefined,
      });
    });

    return breadcrumbs;
  };

  useEffect(() => {
    if (!chooseMode) {
      document.title = title;
    }

    // Set initial currentPath from URL
    const initialPath = getInitialPath();
    setCurrentPath(initialPath);
    console.log("🔍 Set initial currentPath:", initialPath);
  }, []);

  useEffect(() => {
    const pathParam = searchParams.get("path");
    console.log("🔍 DocumentPage - Raw pathParam from URL:", pathParam);

    if (pathParam) {
      try {
        // Properly decode the path parameter
        const decodedPath = decodeURIComponent(pathParam.replace(/\+/g, " "));
        console.log("🔍 DocumentPage - Decoded path:", decodedPath);
        setCurrentPath(decodedPath);
        if (query.path !== decodedPath) {
          setQuery({ ...query, path: decodedPath });
        }
      } catch (error) {
        console.error("DocumentPage - Error decoding path:", error);
        setCurrentPath("/");
        setQuery({ ...query, path: "/" });
      }
    } else {
      setCurrentPath("/");
      setQuery({ ...query, path: "/" });
    }
    // Remove fetchData() from here to avoid race condition
  }, []);

  // Separate useEffect to handle URL path changes
  useEffect(() => {
    const pathParam = searchParams.get("path");
    if (pathParam) {
      try {
        const decodedPath = decodeURIComponent(pathParam.replace(/\+/g, " "));
        console.log("🔍 URL path changed to:", decodedPath);
        setCurrentPath(decodedPath);
        if (query.path !== decodedPath) {
          setQuery((prevQuery) => ({ ...prevQuery, path: decodedPath }));
        }
      } catch (error) {
        console.error("Error decoding path:", error);
        setCurrentPath("/");
        setQuery((prevQuery) => ({ ...prevQuery, path: "/" }));
      }
    } else {
      setCurrentPath("/");
      setQuery((prevQuery) => ({ ...prevQuery, path: "/" }));
    }
  }, [searchParams]);

  // This useEffect will handle all fetchData calls when query changes
  useEffect(() => {
    console.log("🔍 Fetching data for path:", query.path);
    fetchData();
  }, [query.path, query.page, query.limit]); // Chỉ gọi API khi những giá trị quan trọng thay đổi

  const handleCancel = () => {
    setFromPage(undefined);
    setToPage(undefined);
  };

  useEffect(() => {
    setQuery({
      ...query,
      page: fromPage || 1,
      toPage: toPage!,
    });
  }, [fromPage, toPage]);

  const hasActiveFilters = () => {
    return query.search || query.isActive !== undefined || query.departmentId;
  };

  const handleTableChange: TableProps<any>["onChange"] = (
    pagination,
    filters,
    sorter
  ) => {
    if (!Array.isArray(sorter)) {
      const fieldMap: Record<string, string> = {
        name: "document.name",
        code: "document.code",
        department: "department.name",
        category: "category.name",
        createdBy: "createdBy.fullName",
        description: "document.description",
      };
      const columnKey = sorter.field || sorter.column?.key;

      if (!sorter.order) {
        query.queryObject = undefined;
        setQuery({ ...query });
      } else {
        const order = sorter.order === "ascend" ? "ASC" : "DESC";
        const field = fieldMap[columnKey as string];

        const newQueryObject = JSON.stringify([
          {
            type: "sort",
            field,
            value: order,
          },
        ]);
        query.queryObject = newQueryObject;
        setQuery({ ...query });
      }
      fetchData();
    } else {
      query.queryObject = undefined;
      setQuery({ ...query });
      fetchData();
    }
  };

  const handleRowClick = (record: Document) => {
    if (record.module === DocumentModule.Folder) {
      handleFolderClick(record);
    } else {
      navigate(
        `/master-data/${PermissionNames.documentEdit.replace(
          ":id",
          record!.id + ""
        )}?path=${encodeURIComponent(currentPath)}`
      );
    }
  };

  const columns: CustomizableColumn<Document>[] = [
    {
      title: "Mã",
      dataIndex: "code",
      key: "code",
      width: 120,
      render: (_, record) => (
        <div
          className="text-[#1677ff] cursor-pointer"
          onClick={() => handleRowClick(record)}
        >
          {record?.code || "-"}
        </div>
      ),
      defaultVisible: true,
      sorter: true,
    },
    {
      title: "Tên",
      dataIndex: "name",
      key: "name",
      width: 200,
      render: (_, record) => (
        <div
          className={`flex items-center gap-2 ${
            record.module === DocumentModule.Folder
              ? "text-[#1677ff] cursor-pointer hover:text-blue-700"
              : ""
          }`}
          onClick={() => {
            if (record.module === DocumentModule.Folder) {
              handleFolderClick(record);
            }
          }}
        >
          {record.module === DocumentModule.Folder ? (
            <FolderIcon />
          ) : (
            <FileIcon />
          )}
          {record?.name || "-"}
        </div>
      ),
      defaultVisible: true,
      sorter: true,
    },
    {
      title: "Phòng ban",
      dataIndex: "department",
      key: "department",
      width: 200,
      render: (_, record) => <div>{record?.department?.name || ""}</div>,
      defaultVisible: true,
      sorter: true,
    },
    {
      title: "Danh mục",
      dataIndex: "category",
      key: "category",
      width: 200,
      render: (_, record) => <div>{record?.category?.name || "-"}</div>,
      defaultVisible: true,
      sorter: true,
    },
    // {
    //   title: "Mô tả",
    //   dataIndex: "description",
    //   key: "description",
    //   width: 200,
    //   render: (_, record) => (
    //     <div className="text-ellipsis-2" title={record?.description}>
    //       {record?.description || "-"}
    //     </div>
    //   ),
    //   defaultVisible: true,
    //   sorter: true,
    // },
    {
      title: "Người tạo",
      dataIndex: "createdBy",
      key: "createdBy",
      width: 200,
      render: (_, record) => <div>{record.createdBy?.fullName || ""}</div>,
      defaultVisible: true,
      sorter: true,
    },
    {
      key: "status",
      title: "Trạng thái",
      width: 100,
      align: "center",
      dataIndex: "status",
      render: (status, record) => (
        <div className="justify-center flex">
          {record.isActive ? (
            <Tag color="green" className="status-tag !mr-0">
              Hoạt động
            </Tag>
          ) : (
            <Tag color="red" className="status-tag !mr-0">
              Bị khóa
            </Tag>
          )}
        </div>
      ),
      defaultVisible: true,
    },
    {
      key: "actions",
      title: "Xử lý",
      width: 100,
      align: "center",
      fixed: "right",
      render: (_, record) => (
        <Space size="small">
          {haveEditPermission && record.module === DocumentModule.Folder && (
            <EditButton
              toolTipContent="Sửa tên thư mục"
              onClick={(e) => {
                e.stopPropagation();
                setEditingFolder(record);
                folderCreateModalRef.current?.handleUpdate(record.name);
              }}
            />
          )}
          {haveEditPermission && record.module !== DocumentModule.Folder && (
            <EditButton
              onClick={(e) => {
                e.stopPropagation();
                navigate(
                  `/master-data/${PermissionNames.documentEdit.replace(
                    ":id",
                    record!.id + ""
                  )}?update=1&path=${encodeURIComponent(currentPath)}`
                );
              }}
            />
          )}
          {haveBlockPermission && (
            <LockButton
              isActive={record.isActive}
              onAccept={() => handleBlockDocument(record.id, record.isActive)}
              modalTitle={`${record.isActive ? "Khóa" : "Mở khóa"} ${
                record.module === DocumentModule.Folder ? "thư mục" : "tài liệu"
              } ${record.name}`}
              modalContent={
                <>
                  <div>
                    Khi {record.isActive ? "khóa" : "mở khóa"}{" "}
                    {record.module === DocumentModule.Folder
                      ? "thư mục"
                      : "tài liệu"}{" "}
                    các thông tin của{" "}
                    {record.module === DocumentModule.Folder
                      ? "thư mục"
                      : "tài liệu"}{" "}
                    này cũng sẽ được {record.isActive ? "khóa" : "mở khóa"}.
                  </div>
                  <div>
                    Bạn có chắc chắn muốn {record.isActive ? "khóa" : "mở khóa"}{" "}
                    {record.module === DocumentModule.Folder
                      ? "thư mục"
                      : "tài liệu"}{" "}
                    này?
                  </div>
                </>
              }
            />
          )}
        </Space>
      ),
    },
  ];

  return (
    <div className="app-container">
      <PageTitle
        title={title}
        breadcrumbs={["Dữ liệu nguồn", title]}
        extra={
          haveAddPermission && (
            <Space>
              {/* {currentPath !== "/" && (
                <CustomButton
                  size="small"
                  icon={<LeftOutlined />}
                  onClick={handleBackToParent}
                >
                  Quay lại
                </CustomButton>
              )} */}
              <CustomButton
                size="small"
                icon={<FolderOutlined />}
                onClick={() => {
                  folderCreateModalRef.current?.handleCreate();
                }}
              >
                Tạo thư mục
              </CustomButton>
              <CustomButton
                size="small"
                showPlusIcon
                onClick={() => {
                  navigate(
                    `/master-data/${
                      PermissionNames.documentAdd
                    }?path=${encodeURIComponent(currentPath)}`
                  );
                }}
              >
                Tạo tài liệu
              </CustomButton>
            </Space>
          )
        }
      />

      <Card>
        <div className="flex gap-[16px] items-end pb-[16px] justify-between">
          <div className="flex gap-[16px] items-end flex-wrap">
            <div className="w-[300px]">
              <CustomInput
                tooltipContent={"Tìm theo mã, tên tài liệu"}
                label="Tìm kiếm"
                placeholder="Tìm kiếm"
                onPressEnter={applyFilters}
                value={query.search}
                onChange={(value) => {
                  query.search = value;
                  query.page = 1;
                  setQuery({ ...query });
                  if (!value) {
                    fetchData();
                  }
                }}
                allowClear
              />
            </div>
            <div className="w-[200px]">
              <QueryLabel>Phòng ban</QueryLabel>
              <DictionarySelector
                label="Phòng ban"
                value={query.departmentId ?? ""}
                onChange={(value) => {
                  query.departmentId = value || "";
                  setQuery({ ...query });
                }}
                initQuery={{ type: DictionaryType.Department }}
                addonOptions={[
                  {
                    id: "",
                    name: "Tất cả phòng ban",
                  },
                ]}
                className="w-full"
              />
            </div>
            <div className="w-[200px]">
              <QueryLabel>Trạng thái</QueryLabel>
              <Select
                placeholder="Chọn trạng thái"
                value={query.isActive ?? ""}
                onChange={(value) => {
                  query.isActive = value === "" ? "" : value;
                  setQuery({ ...query });
                }}
                allowClear
                options={[
                  {
                    value: "",
                    label: "Tất cả trạng thái",
                  },
                  {
                    value: true,
                    label: "Hoạt động",
                  },
                  {
                    value: false,
                    label: "Bị khóa",
                  },
                ]}
                className="w-full"
              />
            </div>

            <CustomButton
              onClick={() => {
                query.page = 1;
                setQuery({ ...query });
                applyFilters();
              }}
              className="cta-button"
            >
              Áp dụng
            </CustomButton>
            {hasActiveFilters() && (
              <CustomButton
                variant="outline"
                onClick={() => {
                  delete query.search;
                  delete query.departmentId;
                  delete query.isActive;
                  query.page = 1;
                  setQuery({ ...query });
                  fetchData();
                }}
                className="cta-button"
              >
                Bỏ lọc
              </CustomButton>
            )}
          </div>
        </div>

        {currentPath !== "/" && (
          <div className="mb-4 p-3 bg-gray-50 rounded-md">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <span>Đường dẫn hiện tại:</span>
              <div className="flex items-center gap-1">
                {getBreadcrumbItems().map((item, index) => (
                  <React.Fragment key={index}>
                    {index > 0 && <span className="mx-1">/</span>}
                    <button
                      className={`hover:text-blue-600 ${
                        item.onClick
                          ? "cursor-pointer text-blue-500"
                          : "text-gray-800"
                      }`}
                      onClick={item.onClick}
                      disabled={!item.onClick}
                    >
                      {item.label}
                    </button>
                  </React.Fragment>
                ))}
              </div>
            </div>
          </div>
        )}

        <CustomizableTable
          columns={filterActionColumnIfNoPermission(columns, [
            haveEditPermission,
            haveBlockPermission,
          ])}
          dataSource={documents}
          rowKey="id"
          loading={loading}
          pagination={false}
          scroll={{ x: 1200 }}
          bordered
          displayOptions
          //@ts-ignore
          onChange={handleTableChange}
          onRowClick={handleRowClick}
        />

        <Pagination
          currentPage={query.page}
          defaultPageSize={query.limit}
          total={total}
          onChange={({ limit, page }) => {
            query.page = page;
            query.limit = limit;
            setQuery({ ...query });
            fetchData();
          }}
        />
      </Card>

      <FolderCreateModal
        onClose={() => {
          setEditingFolder(null);
        }}
        onSubmitOk={handleFolderModalSubmit}
        ref={folderCreateModalRef}
      />
    </div>
  );
};

export default observer(DocumentPage);
