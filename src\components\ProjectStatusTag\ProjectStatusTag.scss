.project-status-tag {
  .ant-select {
    width: fit-content;
    min-width: unset;
    height: 25px;

    &.color-NEW {
      .ant-select-selector {
        background-color: var(--color-project-planning);
      }
    }
    &.color-IN_PROGRESS {
      .ant-select-selector {
        background-color: var(--color-project-in-progress);
      }
    }
    &.color-HOLD {
      .ant-select-selector {
        background-color: var(--color-project-hold);
      }
    }
    &.color-DONE {
      .ant-select-selector {
        background-color: var(--color-project-done);
      }
    }

    .ant-select-selector {
      border: none;
      .ant-select-selection-wrap {
        line-height: 140%;

        &::after {
          line-height: 140%;
        }
        .ant-select-selection-search {
          height: 25px;
        }
        .ant-select-selection-search,
        .ant-select-selection-item {
          padding-inline-end: 16px;
        }

        .ant-select-selection-item {
          line-height: 140%;
          color: var(--color-text-selected);
          font-weight: 500;
        }
      }
    }
  }
}
