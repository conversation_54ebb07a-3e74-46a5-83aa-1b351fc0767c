.notification-item--read {
  opacity: 0.8;
  border: 1.5px solid #e0e0e0;
  background: #f7f7f7;
  .notification-item-title {
    font-weight: 600;
    color: #333;
  }
}

.notification-item.notification-item--unread {
  border: 1px solid var(--color-logo);
  background: var(--color-neutral-n1);
  box-shadow: 0 2px 8px var(--color-table-even-row-2);
  .notification-item-title {
    font-weight: 900;
    color: var(--color-logo);
    font-size: 16px;
    letter-spacing: 0.2px;
  }
}

.notification-item {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  // padding: 10px;
  margin-bottom: 8px;
  background: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
    border-color: #d0d0d0;
  }

  &:last-child {
    margin-bottom: 0;
  }

  .notification-item-header {
    display: flex;
    align-items: flex-start;
    gap: 10px;

    .notification-item-icon {
      flex-shrink: 0;
      margin: auto 15px;
    }

    .notification-item-info {
      flex: 1;
      min-width: 0;

      .notification-item-title {
        font-weight: 600;
        font-size: 16px;
        line-height: 1.3;
        margin-bottom: 8px;
        margin-top: 15px;
        color: #333;
      }

      .notification-item-content {
        font-size: 14px;
        line-height: 1.4;
        color: var(--color-neutral-n7);
        word-wrap: break-word;
      }
    }
  }

  .notification-item-time {
    font-size: 10px;
    color: #999;
    margin-top: 6px;
    padding-left: 38px;
  }
}
