import { request } from "utils/request";
import { AxiosPromise } from "axios";


export const colorApi = {
  findAll: (params?:any):AxiosPromise<any> => request({
    url: '/v1/admin/color',
    params
  }),
  create: (data:any):AxiosPromise<any> => request({
    url: '/v1/admin/color',
    data,
    method: 'post'
  }),
  update: (id:number, data:any):AxiosPromise<any> => request({
    url: `/v1/admin/color/${id}`,
    method: 'patch',
    data
  }),
  delete: (id:number):AxiosPromise<any> => request({
    url: `/v1/admin/color/${id}`,
    method: 'delete'
  }),
}
