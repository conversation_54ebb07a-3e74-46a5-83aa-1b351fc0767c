import {
  <PERSON><PERSON>,
  Card,
  Col,
  DatePicker,
  Form,
  message,
  Row,
  Select,
  Input,
  Spin,
  Space,
  Divider,
} from "antd";
import { Rule } from "antd/lib/form";
import { staffApi } from "api/staff.api";
import { roleApi } from "api/role.api";
import { permissionApi } from "api/permission.api";
import { companyApi } from "api/company.api";
import CustomButton from "components/Button/CustomButton";
import CustomInput from "components/Input/CustomInput";
import PageTitle from "components/PageTitle/PageTitle";
import { phoneNumberRule, phoneValidate, emailRules } from "utils/validateRule";
import React, { useEffect, useState, useMemo } from "react";
import {
  useNavigate,
  useParams,
  useSearchParams,
  useLocation,
} from "react-router-dom";
import { Staff } from "types/staff";
import { Role } from "types/role";
import { Permission } from "types/permission";
import { Company } from "types/company";
import { getTitle } from "utils";
import { settings } from "settings";
import { PermissionNames } from "types/PermissionNames";
import { ModalStatus } from "types/modal";
import dayjs, { Dayjs } from "dayjs";
import { DictionarySelector } from "components/Selector/DictionarySelector";
import { DictionaryType } from "types/dictionary";
import { memberShipApi } from "api/memberShip.api";
import { useCompany } from "hooks/useCompany";
import { isEmpty } from "lodash";
import ActiveStatusTagSelect from "components/ActiveStatus/ActiveStatusTagSelect";
import clsx from "clsx";
import { observer } from "mobx-react";
import { checkRoles } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { MemberShipType } from "types/memberShip";
import { PermissionSelectionModal } from "./components/PermissionSelectionModal";
import { usePermission } from "hooks/usePermission";
import { useRole } from "hooks/useRole";
import { RoleSelector } from "components/Selector/RoleSelector";
import { useWatch } from "antd/es/form/Form";
import RoleTable from "views/Role/components/RoleTable";
import { StaffSelector } from "components/Selector/StaffSelector";
import { formatDate } from "utils/date";

const rules: Rule[] = [{ required: true }];

interface Props {
  title: string;
  status: ModalStatus;
  projectId?: number;
}

function CreateOrUpdateMemberShipPage({
  title = "",
  projectId = 0,
  status,
}: Props) {
  const navigate = useNavigate();
  const params = useParams();
  const [searchParams] = useSearchParams();
  const location = useLocation();

  const { haveEditPermission } = checkRoles(
    { edit: PermissionNames.projectPhoneBookEdit },
    permissionStore.permissions
  );

  const {
    permissions: permissionList,
    loading: loadingPermissions,
    fetchData: fetchPermission,
  } = usePermission({ initQuery: { limit: 0, page: 1 } });
  const {
    roles: roleList,
    loadingRole: loadingRoles,
    fetchRole,
  } = useRole({ initQuery: { limit: 0, page: 1 } });
  const {
    companies,
    loading: loadingCompanies,
    fetchData: fetchCompanyData,
  } = useCompany({
    initQuery: { page: 1, limit: 100 },
  });

  const [form] = Form.useForm();

  const roleId: number | undefined = useWatch("roleId", form);
  // const role: Role | undefined = useWatch("role", form);
  const [selectedRole, setSelectedRole] = useState<Role>();

  const [loading, setLoading] = useState(false);
  const [loadingFetch, setLoadingFetch] = useState(false);
  const [staffList, setStaffList] = useState<Staff[]>([]);
  // const [roleList, setRoleList] = useState<Role[]>([]);
  // const [permissionList, setPermissionList] = useState<Permission[]>([]);
  const [loadingStaff, setLoadingStaff] = useState(false);
  // const [loadingRoles, setLoadingRoles] = useState(false);
  // const [loadingPermissions, setLoadingPermissions] = useState(false);
  const [selectedStaff, setSelectedStaff] = useState<Staff | null>(null);
  const [selectedMemberShip, setSelectedMemberShip] = useState<any>(null);
  const [readonly, setReadonly] = useState(true);

  const searchParamsFromLocation = new URLSearchParams(location.search);
  const projectIdFromQuery = searchParamsFromLocation.get("projectId");
  const effectiveProjectId =
    projectId || (projectIdFromQuery ? Number(projectIdFromQuery) : undefined);
  const [selectedRoleName, setSelectedRoleName] = useState<string>("");
  const [selectedPermissionNames, setSelectedPermissionNames] = useState<
    string[]
  >([]);
  const [selectedPermissionIds, setSelectedPermissionIds] = useState<number[]>(
    []
  );
  const [permissionModalVisible, setPermissionModalVisible] = useState(false);
  const [selectedCompanyId, setSelectedCompanyId] = useState<
    number | undefined
  >(undefined);

  // Sử dụng useCompany hook

  const setDataToForm = (data: any) => {
    form.setFieldsValue({
      membershipCode: data.code,
      memberShipCategoryId: data.memberShipCategory?.id,
      staffCode: data.staff?.id, // Set staff ID for StaffSelector display
      fullName: data.name,
      phoneNumber: data.phone,
      email: data.email,
      isActive: data.isActive,
      status: data.isActive, // Set status field based on isActive
      company: data.company?.code,
      jobTitleId: data.jobTitle?.id,
      role: data.role?.name,
      roleId: data.role?.id,
      date: data.date ? dayjs(data.date) : null,
    });

    // Set selected staff nếu có
    if (data.staff) {
      setSelectedStaff(data.staff);
    }

    // Set selected company ID
    if (data.company?.id) {
      setSelectedCompanyId(data.company.id);
    }

    // Set selected role name
    if (data.role?.name) {
      setSelectedRoleName(data.role.name);
    }

    // Set selected permission names
    if (data.permissions && Array.isArray(data.permissions)) {
      setSelectedPermissionNames(data.permissions.map((p: any) => p.name));
      setSelectedPermissionIds(data.permissions.map((p: any) => p.id));
    }
  };

  useEffect(() => {
    document.title = getTitle(title);
    fetchStaffList(); // Initial load without company filter
    // fetchRoleList();
    // fetchPermissionList();
    fetchPermission();
    fetchRole();
    fetchCompanyData();

    if (status === "update") {
      const memberShipId = params.id;

      if (memberShipId) {
        fetchMemberShipData(Number(memberShipId));
        setReadonly(searchParams.get("update") !== "1");
      }
    } else {
      setReadonly(false);
      // Set default status to active for new membership
      form.setFieldsValue({
        status: true,
        isActive: true,
      });
    }
  }, [status, params.id]);

  const fetchMemberShipDataForCopy = async (id: number) => {
    setLoadingFetch(true);
    try {
      const response = await memberShipApi.findOne(id);
      const memberShipData = response.data;

      if (isEmpty(memberShipData)) {
        message.error("Không tìm thấy dữ liệu để sao chép");
        return;
      }

      console.log("Copy data from API:", memberShipData);

      // Set form values từ data API, loại bỏ ID và tự sinh code mới
      form.setFieldsValue({
        membershipCode: "", // Để trống để tự sinh code mới
        memberShipCategoryId: memberShipData.memberShipCategory?.id,
        staffCode: memberShipData.staff?.id, // Set staff ID for StaffSelector display
        fullName: memberShipData.name,
        phoneNumber: memberShipData.phone,
        email: memberShipData.email,
        company: memberShipData.company?.code,
        jobTitleId: memberShipData.jobTitle?.id,
        role: memberShipData.role?.name,
        status: true, // Default to active cho bản copy
        isActive: true,
        date: memberShipData.date ? dayjs(memberShipData.date) : null,
      });

      // Set selected staff
      if (memberShipData.staff) {
        setSelectedStaff(memberShipData.staff);
      }

      // Set selected company ID
      if (memberShipData.company?.id) {
        setSelectedCompanyId(memberShipData.company.id);
        // Fetch staff list for the company
        fetchStaffList(memberShipData.company.id);
      }

      // Set selected role name
      if (memberShipData.role?.name) {
        setSelectedRoleName(memberShipData.role.name);
      }

      // Set selected permission names
      if (
        memberShipData.permissions &&
        Array.isArray(memberShipData.permissions)
      ) {
        setSelectedPermissionNames(
          memberShipData.permissions.map((p: any) => p.name)
        );
        setSelectedPermissionIds(
          memberShipData.permissions.map((p: any) => p.id)
        );
      }
    } catch (error) {
      console.error("Error fetching membership data for copy:", error);
      message.error("Lỗi khi tải dữ liệu để sao chép");
    } finally {
      setLoadingFetch(false);
    }
  };

  useEffect(() => {
    if (
      status === "create" &&
      location.state &&
      typeof location.state === "object" &&
      "copyData" in location.state
    ) {
      const locationState = location.state as {
        copyData: any;
        projectId?: number;
      };
      const copyData = locationState.copyData;
      const stateProjectId = locationState.projectId;

      console.log("copyData received:", copyData);
      console.log("projectId from state:", stateProjectId);

      // Nếu copyData có ID, gọi API findOne để lấy đầy đủ thông tin
      if (copyData.id) {
        fetchMemberShipDataForCopy(copyData.id);
      } else {
        // Fallback: sử dụng data truyền vào nếu không có ID
        // handleCopyDataWithoutAPI(copyData);
        setDataToForm(copyData);
      }
    }
  }, [status, location.state]);

  useEffect(() => {
    if (selectedRole) {
      setSelectedPermissionNames(selectedRole.permissions.map((p) => p.name));
    }
  }, [selectedRole]);

  const fetchStaffList = async (companyId?: number) => {
    setLoadingStaff(true);
    try {
      const queryParams: any = { page: 1, limit: 100, isBlocked: false };

      // Add companyId filter if provided
      if (companyId) {
        queryParams.companyId = companyId;
      }

      const response = await staffApi.findAll(queryParams);
      setStaffList(response.data.staffs || []);
    } catch (error) {
      console.error("Error fetching staff list:", error);
      message.error("Lỗi khi tải danh sách nhân viên");
    } finally {
      setLoadingStaff(false);
    }
  };

  // const fetchRoleList = async () => {
  //   setLoadingRoles(true);
  //   try {
  //     const response = await roleApi.findAll({ page: 1, limit: 0 });
  //     setRoleList(response.data.roles || []);
  //   } catch (error) {
  //     console.error("Error fetching role list:", error);
  //     message.error("Lỗi khi tải danh sách vai trò");
  //   } finally {
  //     setLoadingRoles(false);
  //   }
  // };

  // const fetchPermissionList = async () => {
  //   setLoadingPermissions(true);
  //   try {
  //     const response = await permissionApi.findAll({ page: 1, limit: 0 });
  //     setPermissionList(response.data.permissions || []);
  //   } catch (error) {
  //     console.error("Error fetching permission list:", error);
  //     message.error("Lỗi khi tải danh sách quyền");
  //   } finally {
  //     setLoadingPermissions(false);
  //   }
  // };

  const fetchMemberShipData = async (id: number) => {
    setLoadingFetch(true);
    try {
      const response = await memberShipApi.findOne(id);
      const memberShipData = response.data;

      if (isEmpty(memberShipData)) {
        navigate("/404");
        return;
      }

      setSelectedMemberShip(memberShipData);

      setDataToForm(memberShipData);

      return memberShipData;
    } catch (error) {
      console.error("Error fetching membership data:", error);
      message.error("Lỗi khi tải dữ liệu danh bạ");
      navigate("/404");
    } finally {
      setLoadingFetch(false);
    }
  };

  const handleStaffIdChange = (staffId: number | null) => {
    if (staffId) {
      const staff = staffList.find((s) => s.id === staffId);
      if (staff) {
        setSelectedStaff(staff);
        // Don't set staffCode here - let StaffSelector handle its own display
        form.setFieldsValue({
          fullName: staff.fullName,
          phoneNumber: staff.phone,
          email: staff.email,
          role: staff.role?.name,
          company: staff.company?.code,
          jobTitleId: staff.jobTitle?.id,
        });
        setSelectedRoleName(staff.role?.name || "");
      }
    } else {
      // Handle clear case
      setSelectedStaff(null);
      setSelectedRoleName("");
      form.setFieldsValue({
        fullName: "",
        phoneNumber: "",
        email: "",
        role: "",
      });
    }
  };

  const handleCompanyChange = (companyCode: string) => {
    const selectedCompany = companies.find((c) => c.code === companyCode);

    if (selectedCompany) {
      setSelectedCompanyId(selectedCompany.id);

      // Clear current staff selection when company changes
      setSelectedStaff(null);
      form.setFieldsValue({
        staffCode: undefined, // Clear staff ID
        fullName: "",
        phoneNumber: "",
        email: "",
        role: undefined,
      });
      setSelectedRoleName("");

      // Fetch staff list for the selected company
      fetchStaffList(selectedCompany.id);
    } else {
      setSelectedCompanyId(undefined);
      setSelectedStaff(null);
      // Fetch all staff when no company is selected
      fetchStaffList();
    }
  };

  // Create data
  const createData = async () => {
    await form.validateFields();
    const formData = form.getFieldsValue();
    const payload = getPayloadFromForm(formData);
    console.log("createData - payload:", payload);

    setLoading(true);
    try {
      await memberShipApi.create(payload);
      message.success("Tạo danh bạ dự án thành công!");

      // Check navigation source using location state
      const locationState = location.state as any;
      const isFromProjectDetail = locationState?.fromProjectDetail;
      const isFromReportSection = locationState?.fromReportSection;
      const projectId = locationState?.projectId || effectiveProjectId;

      console.log("Navigation info:", {
        isFromProjectDetail,
        isFromReportSection,
        projectId,
        effectiveProjectId,
      });

      if (isFromProjectDetail && projectId) {
        // Navigate back to ProjectDetailPage with phonebook tab active
        navigate(`/project-detail/${projectId}?tab=phonebook`);
      } else if (isFromReportSection || projectId) {
        // Navigate back to Report section MemberShipPage
        if (projectId) {
          navigate(
            `/report/${PermissionNames.projectPhoneBookList}?projectId=${projectId}`
          );
        } else {
          navigate(`/report/${PermissionNames.projectPhoneBookList}`);
        }
      } else {
        // Default fallback navigation
        navigate(-1);
      }
    } catch (error) {
      console.error("Error creating membership:", error);
      // message.error("Lỗi khi tạo danh bạ dự án");
    } finally {
      setLoading(false);
    }
  };

  // Update data
  const updateData = async () => {
    await form.validateFields();
    const formData = form.getFieldsValue();
    const payload = getPayloadFromForm(formData);

    setLoading(true);
    try {
      const response = await memberShipApi.update(Number(params.id), payload);
      message.success("Cập nhật danh bạ dự án thành công!");
      fetchMemberShipData(Number(params.id));
    } catch (error) {
      console.error("Error updating membership:", error);
      // message.error("Lỗi khi cập nhật danh bạ dự án");
    } finally {
      setLoading(false);
    }
  };

  // Get payload from form data
  const getPayloadFromForm = (formData: any) => {
    // console.log(
    //   "getPayloadFromForm - selectedPermissionIds:",
    //   selectedPermissionIds
    // );

    // // Lấy quyền từ role
    // const rolePermissionIds: number[] = [];
    // if (selectedRoleName) {
    //   const selectedRole = roleList.find((r) => r.name === selectedRoleName);
    //   if (selectedRole?.permissions) {
    //     rolePermissionIds.push(...selectedRole.permissions.map((p) => p.id));
    //   }
    // }

    // // Gộp quyền role + quyền bổ sung
    // const allPermissionIds = [
    //   ...new Set([...rolePermissionIds, ...selectedPermissionIds]),
    // ];

    // console.log("Role permission IDs:", rolePermissionIds);
    // console.log("Additional permission IDs:", selectedPermissionIds);
    // console.log("Final all permission IDs:", allPermissionIds);

    const selectedRole = roleList.find((r) => r.name === formData.role);
    // Ưu tiên sử dụng roleId từ form (RoleSelector)
    let roleId = formData.roleId || 0;

    // Fallback: nếu không có roleId, tìm theo role name
    if (!roleId && formData.role) {
      const selectedRole = roleList.find((r) => r.name === formData.role);
      roleId = selectedRole ? selectedRole.id : 0;
    }

    // Fallback cuối: role từ selected staff
    if (!roleId && selectedStaff?.role?.id) {
      roleId = selectedStaff.role.id;
    }

    const selectedCompany = companies.find((c) => c.code === formData.company);
    const companyId = selectedCompany
      ? selectedCompany.id
      : selectedStaff?.company?.id || 0;

    return {
      projectId: effectiveProjectId,
      staffId: selectedStaff ? selectedStaff.id : 0,
      permissionIds: selectedPermissionNames
        .map((e) => {
          const find = permissionList.find((p) => p.name == e);
          return find?.id;
        })
        .filter((e) => e), // Gộp quyền role + quyền bổ sung
      roleId: roleId,
      companyId: companyId,
      jobTitleId: formData.jobTitleId,
      memberShipCategoryId: formData.memberShipCategoryId,

      memberShip: {
        code: formData.membershipCode || "",
        staffCode: selectedStaff?.code || "", // Use selectedStaff.code, not formData.staffCode
        type: formData.membershipType || "",
        name: formData.fullName || "",
        phone: formData.phoneNumber || "",
        date: (formData.date as Dayjs).format("YYYY-MM-DD") || "",
        email: formData.email || "",
        isActive:
          formData.status !== undefined
            ? formData.status
            : selectedMemberShip?.isActive ?? true,
      },
    };
  };

  // Handle submit - giống ServicePage
  const handleSubmit = () => {
    if (status === "create") {
      createData();
    } else {
      updateData();
    }
  };

  const handleViewPermissions = () => {
    const selectedRole = roleList.find((r) => r.name === selectedRoleName);
    if (selectedRole) {
      navigate(
        `/master-data/${PermissionNames.roleDetail.replace(
          ":id",
          selectedRole.id + ""
        )}`
      );
    }
  };

  const handleAddPermissions = () => {
    setPermissionModalVisible(true);
  };

  // Tính tổng số quyền (role + bổ sung) để hiển thị
  const getTotalPermissionCount = () => {
    const rolePermissions: string[] = [];

    if (selectedRoleName) {
      const selectedRole = roleList.find((r) => r.name === selectedRoleName);
      if (selectedRole?.permissions) {
        rolePermissions.push(...selectedRole.permissions.map((p) => p.name));
      }
    }

    // Gộp quyền role + quyền bổ sung
    const allPermissions = [
      ...new Set([...rolePermissions, ...selectedPermissionNames]),
    ];
    return allPermissions.length;
  };

  const handlePermissionSave = (ids: number[], names: string[]) => {
    // Với Admin role, chỉ lưu quyền bổ sung được thêm mới
    // Không động vào quyền gốc của Admin
    if (selectedRoleName === "Admin") {
      console.log("Admin role detected - only saving additional permissions");
      setSelectedPermissionIds(ids); // Lưu tất cả IDs từ modal làm quyền bổ sung
      setSelectedPermissionNames(names);
      return;
    }

    // Lấy quyền từ role hiện tại
    const rolePermissions: string[] = [];
    const rolePermissionIds: number[] = [];

    if (selectedRoleName) {
      const selectedRole = roleList.find((r) => r.name === selectedRoleName);
      if (selectedRole?.permissions) {
        rolePermissions.push(...selectedRole.permissions.map((p) => p.name));
        rolePermissionIds.push(...selectedRole.permissions.map((p) => p.id));
      }
    }

    console.log("=== PERMISSION DEBUGGING ===");
    console.log("Role name:", selectedRoleName);
    console.log("Role permissions count:", rolePermissions.length);
    console.log("Modal returned count:", names.length);
    console.log("Modal returned IDs count:", ids.length);

    // Tìm permissions trong role nhưng không có trong modal
    const missingFromModal = rolePermissions.filter(
      (name) => !names.includes(name)
    );
    console.log(
      "Missing from modal:",
      missingFromModal.length,
      missingFromModal
    );

    // Tìm permissions trong modal nhưng không có trong role
    const extraInModal = names.filter(
      (name) => !rolePermissions.includes(name)
    );
    console.log("Extra in modal:", extraInModal.length, extraInModal);

    // Tách quyền bổ sung (loại bỏ quyền của role)
    const additionalPermissionIds = ids.filter(
      (id) => !rolePermissionIds.includes(id)
    );
    const additionalPermissionNames = names.filter(
      (name) => !rolePermissions.includes(name)
    );

    // Lưu chỉ quyền bổ sung (không bao gồm quyền của role)
    setSelectedPermissionIds(additionalPermissionIds);
    setSelectedPermissionNames(additionalPermissionNames);

    console.log("handlePermissionSave - role permissions:", rolePermissions);
    console.log(
      "handlePermissionSave - additional permissions:",
      additionalPermissionNames
    );
    console.log(
      "handlePermissionSave - final additional ids:",
      additionalPermissionIds
    );
  };

  // Page title logic - giống ServicePage
  const pageTitle = useMemo(
    () => (status === "create" ? "Tạo danh bạ" : "Chỉnh sửa danh bạ"),
    [status]
  );

  // Convert role list to options for select
  const roleOptions = roleList.map((role) => ({
    label: role.name,
    value: role.name,
  }));

  // Get company options
  const companyOptions = companies.map((company) => ({
    label: `${company.code} - ${company.name}`,
    value: company.code,
  }));

  // Tính toán quyền để hiển thị trong modal
  const getInitialPermissionsForModal = () => {
    const rolePermissions: string[] = [];

    // Lấy quyền từ role đã chọn
    if (selectedRoleName) {
      const selectedRole = roleList.find((r) => r.name === selectedRoleName);
      if (selectedRole?.permissions) {
        rolePermissions.push(...selectedRole.permissions.map((p) => p.name));
      }
    }

    // Gộp quyền role + quyền bổ sung để hiển thị trong modal
    return [...new Set([...rolePermissions, ...selectedPermissionNames])];
  };

  const allSelectedPermissionNames = useMemo(() => {
    const rolePermissions: string[] = [];

    // Lấy permissions từ role đã chọn
    if (selectedRoleName) {
      const selectedRole = roleList.find((r) => r.name === selectedRoleName);
      if (selectedRole?.permissions) {
        rolePermissions.push(...selectedRole.permissions.map((p) => p.name));
      }
    }

    // Merge với permissions bổ sung
    return [...new Set([...rolePermissions, ...selectedPermissionNames])];
  }, [selectedRoleName, selectedPermissionNames, roleList]);

  if (loadingFetch) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="app-container">
      <PageTitle
        back
        breadcrumbs={[
          { label: "Báo cáo" },
          {
            label: "Danh bạ dự án",
            href: `/report/${PermissionNames.projectPhoneBookList}`,
          },
          { label: pageTitle },
        ]}
        title={pageTitle}
        // extra={
        //   selectedMemberShip &&
        //   status === "update" && (
        //     <Space>
        //       <ActiveStatusTagSelect
        //         disabled={readonly}
        //         isActive={selectedMemberShip?.isActive}
        //         onChange={(value) => {
        //           setSelectedMemberShip({
        //             ...selectedMemberShip,
        //             isActive: value,
        //           });
        //         }}
        //       />
        //     </Space>
        //   )
        // }
      />
      <Card>
        <Spin spinning={loadingFetch}>
          <div className="pb-[16px]">
            <Form
              layout="vertical"
              form={form}
              className={clsx(readonly ? "readonly" : "")}
              disabled={readonly}
            >
              {/* Hidden field for isActive */}
              <Form.Item name="isActive" hidden />

              {/* Row 1: Mã danh bạ, Loại danh bạ, Mã nhân viên, Người tham gia dự án */}
              <Row gutter={16} style={{ marginBottom: "-8px" }}>
                <Col span={6}>
                  <Form.Item label="Mã danh bạ" name="membershipCode">
                    <Input
                      placeholder={
                        status === "create" ? "Không nhập mã sẽ tự sinh" : ""
                      }
                      disabled={status === "update"}
                    />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label="Loại danh bạ"
                    name="memberShipCategoryId"
                    rules={rules}
                  >
                    <DictionarySelector
                      placeholder="Chọn loại danh bạ"
                      initQuery={{
                        type: DictionaryType.MemberShipCategory,
                        isActive: true,
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="Công ty" name="company" rules={rules}>
                    <Select
                      placeholder="Chọn công ty"
                      options={companyOptions}
                      loading={loadingCompanies}
                      showSearch
                      onChange={handleCompanyChange}
                      filterOption={(input, option) =>
                        (option?.label ?? "")
                          .toLowerCase()
                          .includes(input.toLowerCase())
                      }
                      allowClear
                    />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label="Chọn nhân viên / đối tượng"
                    name="staffCode"
                    rules={rules}
                  >
                    <StaffSelector
                      placeholder={
                        selectedCompanyId
                          ? "Tìm kiếm nhân viên theo mã hoặc tên"
                          : "Vui lòng chọn công ty trước"
                      }
                      value={selectedStaff?.id}
                      valueIsOption={false}
                      onChange={handleStaffIdChange}
                      disabled={readonly || !selectedCompanyId}
                      companyId={selectedCompanyId}
                      initOptionItem={selectedStaff || undefined}
                      allowClear
                    />
                  </Form.Item>
                </Col>
              </Row>

              {/* Row 2: Mã nhân viên, Số điện thoại, Địa chỉ email, Họ và tên */}
              <Row gutter={16}>
                <Col span={6}>
                  <Form.Item
                    name="date"
                    label="Ngày tham gia dự án"
                    rules={rules}
                  >
                    <DatePicker
                      placeholder="Chọn ngày tham gia"
                      format={settings.dateFormat}
                      style={{ width: "100%" }}
                    />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="Họ và tên" name="fullName" rules={rules}>
                    <Input placeholder="Nhập họ và tên" />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label="Số điện thoại"
                    name="phoneNumber"
                    rules={[{ required: true }, phoneNumberRule]}
                  >
                    <Input placeholder="Nhập số điện thoại" />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label="Địa chỉ email"
                    name="email"
                    rules={emailRules}
                  >
                    <Input placeholder="Nhập địa chỉ email" type="email" />
                  </Form.Item>
                </Col>
              </Row>

              {/* Row 3: Chức vụ, Vai trò, Trạng thái, Buttons */}
              <Row gutter={16}>
                <Col span={6}>
                  <Form.Item label="Trạng thái" name="status" rules={rules}>
                    <Select
                      placeholder="Chọn trạng thái"
                      options={[
                        { label: "Hoạt động", value: true },
                        { label: "Bị khóa", value: false },
                      ]}
                      onChange={(value) => {
                        // Update the hidden isActive field
                        form.setFieldValue("isActive", value);
                        if (selectedMemberShip) {
                          setSelectedMemberShip({
                            ...selectedMemberShip,
                            isActive: value,
                          });
                        }
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="Chức vụ" name="jobTitleId" rules={rules}>
                    <DictionarySelector
                      placeholder="Chọn chức vụ"
                      initQuery={{
                        type: DictionaryType.JobTitle,
                        isActive: true,
                      }}
                      showSearch
                    />
                  </Form.Item>
                </Col>
                {/* <Col span={6}>
                  <Form.Item label="Vai trò" name="role" rules={rules}>
                    <Select
                      placeholder="Chọn vai trò"
                      options={roleList.map((role) => ({
                        label: role.name,
                        value: role.name,
                      }))}
                      showSearch
                      disabled={readonly}
                      onChange={(value) => {
                        setSelectedRoleName(value);
                        const selectedRole = roleList.find(
                          (r) => r.name === value
                        );
                        form.setFieldValue(
                          "roleId",
                          selectedRole ? selectedRole.id : undefined
                        );
                      }}
                    />
                  </Form.Item>
                </Col> */}
                {/* <Col span={3}>
                  <Form.Item label=" " style={{ marginBottom: 0 }}>
                    <div className="flex flex-col gap-2">
                      <CustomButton
                        size="small"
                        variant="outline"
                        className="cta-button"
                        onClick={handleViewPermissions}
                        disabled={readonly || !selectedRoleName}
                      >
                        Xem chi tiết quyền
                      </CustomButton>
                    </div>
                  </Form.Item>
                </Col> */}
                {/* <Col span={3}>
                  <Form.Item label=" " style={{ marginBottom: 0 }}>
                    <div className="flex flex-col gap-2">
                      <CustomButton
                        size="small"
                        className="cta-button"
                        onClick={handleAddPermissions}
                        disabled={readonly || !selectedRoleName}
                      >
                        Thêm quyền bổ sung
                      </CustomButton>
                      {selectedRoleName && (
                        <div className="text-xs text-gray-500">
                          Tổng quyền: {getTotalPermissionCount()} quyền
                        </div>
                      )}
                    </div>
                  </Form.Item>
                </Col> */}
              </Row>
              <Divider className="my-2" />
              <Row gutter={16}>
                {!readonly && (
                  <Col span={6}>
                    <Form.Item label="Vai trò mẫu" name="roleId">
                      <RoleSelector
                        allowClear={false}
                        valueIsOption
                        onChange={(option) => {
                          setSelectedRole(option);
                          form.setFieldValue("roleId", option.id);
                        }}
                      />
                      {/* <Select
                      placeholder="Chọn vai trò"
                      options={roleOptions}
                      loading={loadingRoles}
                      showSearch
                      disabled={
                        readonly || (!!selectedStaff && !!selectedStaff.role)
                      }
                      onChange={(value) => {
                        setSelectedRoleName(value || "");
                        // Reset selected permissions when changing role
                        if (status === "create") {
                          setSelectedPermissionNames([]);
                        }
                      }}
                      filterOption={(input, option) =>
                        (option?.label ?? "")
                          .toLowerCase()
                          .includes(input.toLowerCase())
                      }
                    /> */}
                    </Form.Item>
                  </Col>
                )}
                {selectedPermissionNames.length > 0 && (
                  <>
                    <Col span={24}>
                      <RoleTable
                        readOnly={readonly}
                        tableProps={{ scroll: { y: 600 } }}
                        checkedNames={selectedPermissionNames}
                        setCheckedNames={setSelectedPermissionNames}
                      />
                    </Col>
                  </>
                )}
              </Row>
            </Form>

            {/* Action buttons - Logic giống ServicePage */}
            <div className="flex gap-[16px] justify-end mt-6">
              {!readonly && (
                <CustomButton
                  variant="outline"
                  className="cta-button"
                  onClick={() => {
                    if (status === "create") {
                      navigate(-1);
                    } else {
                      setReadonly(true);
                      setDataToForm(selectedMemberShip);
                    }
                  }}
                >
                  Hủy
                </CustomButton>
              )}

              <CustomButton
                loading={loading}
                className="cta-button"
                disabled={status == "update" && !haveEditPermission}
                onClick={() => {
                  if (!readonly) {
                    handleSubmit();
                  } else {
                    setReadonly(false);
                  }
                }}
              >
                {status === "create"
                  ? "Tạo danh bạ dự án"
                  : readonly
                  ? "Chỉnh sửa"
                  : "Lưu chỉnh sửa"}
              </CustomButton>
            </div>
          </div>
        </Spin>
      </Card>

      {/* Permission Selection Modal */}
      <PermissionSelectionModal
        visible={permissionModalVisible}
        onCancel={() => setPermissionModalVisible(false)}
        onSave={handlePermissionSave}
        initialPermissionNames={getInitialPermissionsForModal()} // Truyền quyền role + quyền bổ sung
        title="Chọn quyền bổ sung"
        permissionList={permissionList || []}
      />
    </div>
  );
}

export default observer(CreateOrUpdateMemberShipPage);
