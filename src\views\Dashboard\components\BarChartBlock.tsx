import React, { useState } from "react";
import { Paper, Typography, Box } from "@mui/material";
import { BarChart } from "@mui/x-charts/BarChart";
import { DatePicker } from "antd";
import dayjs, { Dayjs } from "dayjs";
import weekday from "dayjs/plugin/weekday";
import localeData from "dayjs/plugin/localeData";
import "dayjs/locale/vi"; // hoặc "en", "fr", tùy locale bạn dùng
dayjs.extend(weekday);
dayjs.extend(localeData);
dayjs.locale("vi"); // nếu bạn muốn hiển thị tiếng Việt
type Props = {
  title: string;
  labels: string[];
  data: number[];
  width?: number;
  height?: number;
  query?: any;
  setQuery?: any;
  label?: string;
  refetchData?: () => void;
} & React.HTMLAttributes<HTMLDivElement>;

export const BarChartBlock = ({
  title,
  labels,
  data,
  width,
  height = 300,
  className,
  query,
  setQuery,
  label,
  refetchData,
  ...rest
}: Props) => {
  const handleRangeChange = (dates: any, dateStrings: [string, string]) => {
    console.log("Dates là", dates);
    if (dates) {
      setQuery?.({
        ...query,
        fromAt: dates[0]?.startOf("day").unix(),
        toAt: dates[1]?.endOf("day").unix(),
      });
      // refetchData?.();
    } else {
      const newQuery = { ...query };
      delete newQuery.fromAt;
      delete newQuery.toAt;
      console.log("Query trước khi set là", newQuery);
      setQuery?.(newQuery);
      // refetchData?.();
    }
  };
  // const hasValidRange =
  //   typeof query?.fromAt === "number" &&
  //   typeof query?.toAt === "number" &&
  //   !isNaN(query.fromAt) &&
  //   !isNaN(query.toAt);

  // const selectedRange = hasValidRange
  //   ? ([dayjs.unix(query.fromAt), dayjs.unix(query.toAt)] as [Dayjs, Dayjs])
  //   : null;
  // console.log("ngày tháng lấy đc là", query);
  const maxValue = Math.max(...data, 10); // tránh trường hợp toàn 0
  const alignedMax = Math.ceil(maxValue / 10) * 10;
  return (
    <Paper
      elevation={3}
      sx={{ p: 2 }}
      {...rest}
      className="!shadow-none w-full"
    >
      <Box display="flex" justifyContent="space-between" alignItems="center">
        <div className="flex flex-col items-start gap-2">
          <Typography variant="h6" className="!font-bold">
            {title}
          </Typography>
          <DatePicker.RangePicker
            className="min-h-[32px]"
            value={
              typeof query?.fromAt === "number" &&
              typeof query?.toAt === "number"
                ? [dayjs.unix(query.fromAt), dayjs.unix(query.toAt)]
                : null
            }
            // allowClear={false}
            onChange={handleRangeChange}
            format="DD/MM/YYYY"
            placeholder={["Từ ngày", "Đến ngày"]}
            size="small"
          />
        </div>
      </Box>
      {data.length === 0 ? (
        <div className="h-[300px] flex items-center justify-center text-gray-500">
          Chưa có dữ liệu
        </div>
      ) : (
        <BarChart
          className={className}
          xAxis={[{ scaleType: "band", data: labels }]}
          series={[{ data, label: label }]}
          yAxis={[
            {
              max: alignedMax,
              min: 0,
              tickMinStep: 10, // ✅ đảm bảo bước nhảy tối thiểu là 10
              valueFormatter: (v: any) => `${v}`, // hoặc v.toLocaleString("vi-VN")
            },
          ]}
          width={width}
          height={height}
        />
      )}
    </Paper>
  );
};
