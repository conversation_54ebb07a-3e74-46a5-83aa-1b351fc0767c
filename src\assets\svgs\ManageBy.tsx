import { useTheme } from "context/ThemeContext";
import * as React from "react";

const ManageByIcon = ({ fill = "#050505" }) => {
  const { darkMode } = useTheme();
  if (darkMode) {
    fill = "#ffffff";
  }
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width={16} height={16} fill={fill}>
      <path
        fill={fill}
        fillRule="evenodd"
        d="M8 .833a3.167 3.167 0 1 0 0 6.334A3.167 3.167 0 0 0 8 .833ZM5.833 4a2.167 2.167 0 1 1 4.334 0 2.167 2.167 0 0 1-4.334 0ZM5.333 8.167a2.75 2.75 0 0 0-2.73 2.428l-.433 3.68a.5.5 0 1 0 .993.117l.433-3.68a1.75 1.75 0 0 1 1.237-1.473v2.796c0 .599 0 1.098.053 1.495.057.418.18.796.484 1.1.304.303.682.427 1.1.483.397.054.896.054 1.495.054h.07c.599 0 1.098 0 1.495-.054.418-.056.796-.18 1.1-.483.303-.304.427-.682.483-1.1.054-.397.054-.896.054-1.495V9.239a1.75 1.75 0 0 1 1.237 1.472l.433 3.68a.5.5 0 0 0 .993-.116l-.433-3.68a2.75 2.75 0 0 0-2.73-2.428H5.333Zm.5 3.833V9.167h4.334V12c0 .643-.002 1.074-.045 1.396-.041.308-.113.44-.2.527-.086.086-.218.158-.526.2-.322.043-.753.044-1.396.044-.643 0-1.074-.001-1.397-.045-.307-.041-.439-.113-.526-.2-.087-.086-.158-.218-.2-.526-.043-.322-.044-.753-.044-1.396Z"
        clipRule="evenodd"
      />
    </svg>
  );
};
export default ManageByIcon;
