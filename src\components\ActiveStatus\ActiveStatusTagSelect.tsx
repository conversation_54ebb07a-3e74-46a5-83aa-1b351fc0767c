import { Select, Tag } from "antd";
import { TiArrowSortedDown } from "react-icons/ti";
import "./styles/ActiveStatusTagSelectStyle.scss";

interface Props {
  onChange?: (value: boolean) => void;
  isActive?: boolean;
  loading?: boolean;
  disabled?: boolean;
}

const ActiveStatusTagSelect = ({
  isActive,
  loading,
  onChange,
  disabled,
}: Props) => {
  return (
    <div className="active-status-tag-select">
      <Select
        disabled={disabled}
        className={isActive ? "active" : "inactive"}
        options={[
          { label: "Hoạt động", value: true },
          { label: "Bị khóa", value: false },
        ]}
        suffixIcon={
          <TiArrowSortedDown
            size={16}
            style={{
              color: isActive
                ? "var(--color-project-done)"
                : // : "var(--color-project-hold)",
                  "#d81322",
            }}
          />
        }
        value={isActive}
        loading={loading}
        onChange={onChange}
      />
    </div>
  );
};

export default ActiveStatusTagSelect;
