import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const projectCategoryApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/projectCategory",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/projectCategory",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/projectCategory/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/projectCategory/${id}`,
      method: "delete",
    }),
};
