import { variantApi } from "api/variant.api";
import { useState } from "react";
import { Variant } from "types/variant";
import { QueryParam } from "types/query";

export interface VariantQuery extends QueryParam {}

interface UseVariantProps {
  initQuery: VariantQuery;
}

export const useVariant = ({ initQuery }: UseVariantProps) => {
  const [data, setData] = useState<Variant[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<VariantQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await variantApi.findAll(query);

      setData(data.variants);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return { variants: data, total, fetchData, loading, setQuery, query };
};
