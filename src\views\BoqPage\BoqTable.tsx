import React, {
  useState,
  useMemo,
  useCallback,
  useEffect,
  useRef,
} from "react";
import {
  Card,
  Button,
  Table,
  Input,
  InputNumber,
  Select,
  Space,
  Popconfirm,
  message,
  Switch,
  Tooltip,
} from "antd";
import {
  PlusOutlined,
  DeleteOutlined,
  TeamOutlined,
  FileTextOutlined,
  DownOutlined,
  RightOutlined,
  ImportOutlined,
} from "@ant-design/icons";
import { useTheme } from "context/ThemeContext";
import "./BoqTable.scss";
import { BOQ, BOQDetail, BOQDetailType } from "types/boq";
import { formatVND } from "utils";
import { CustomizableColumn } from "components/Table/CustomizableTable";
import BoqSubTable from "./components/BoqSubTable";
import clsx from "clsx";
import CustomButton from "components/Button/CustomButton";
import CustomInput from "components/Input/CustomInput";
import { BiListPlus } from "react-icons/bi";
import { UnitSelector } from "components/Selector/UnitSelector";
import { DictionarySelector } from "components/Selector/DictionarySelector";
import { Dictionary, DictionaryType } from "types/dictionary";
import ImportBOQDetails, {
  BOQDetailImport,
  ImportBOQDetailsModal,
} from "components/ImportDocument/ImportBOQDetails";
import { excelDateToDayjs } from "utils/date";
import { removeSubstringFromKeys } from "utils/common";
import { boqApi } from "api/boq.api";
import { unitApi } from "api/unit.api";
import { projectItemApi } from "api/projectItem.api";
import {
  flatChildrenList,
  getListNameByApi,
  getListNameByTypeDictionary,
} from "hooks/useDictionary";
import { exportTemplateWithValidation } from "utils/TemplateExcel";
import { buildTreeByLevel } from "utils/data";
import { cloneDeep } from "lodash";
import { dictionaryApi } from "api/dictionary.api";
import { ProjectItemSelector } from "components/Selector/ProjectItemSelector";
import { Project } from "types/project";
import { ProjectItem } from "types/projectItem";
import { BMDTextArea } from "components/TextArea/BMDTextArea";
import * as ExcelJS from "exceljs";

// Tạo interface mới với các trường không bắt buộc để tạo item mới
interface NewBOQDetail
  extends Omit<BOQDetail, "project" | "projectItem" | "workType"> {
  project?: Project;
  projectItem?: ProjectItem;
  workType?: Dictionary;
  parentCode?: string;
}

// Types
// interface BOQDetail {
//   id: number;
//   createdAt: number;
//   updatedAt: number;
//   deletedAt: number;
//   isDeleted: boolean;
//   type: "GROUP" | "ITEM";
//   name: string;
//   lengthMm: number;
//   widthMm: number;
//   heightMm: number;
//   diameterMm: number;
//   mass: number;
//   quantity: number;
//   materialCost: number;
//   labor: number;
//   total: number;
//   amount: number;
//   unit: string | null;
//   children: BOQDetail[];
//   // Additional fields for UI
//   level?: number;
//   parentId?: number;
// }

interface DynamicEditableTableProps {
  isEdit?: boolean;
  boqDetails?: BOQDetail[];
  onSave?: (data: BOQDetail[]) => void;
  version?: number;
  setBoqDetails: React.Dispatch<React.SetStateAction<BOQDetail[]>>;
  setIsEdit?: React.Dispatch<React.SetStateAction<boolean>>;
}

const BOQ_DETAIL_TYPE_LABELS = {
  GROUP: "Nhóm",
  ITEM: "Hạng mục",
};

const DumbBOQDetails = [
  {
    id: 2,
    createdAt: 1752633216,
    updatedAt: 1752633216,
    deletedAt: 0,
    isDeleted: false,
    type: "GROUP",
    name: "Cha 1",
    lengthMm: 12,
    widthMm: 12,
    heightMm: 0,
    diameterMm: 0,
    mass: 0,
    quantity: 0,
    materialCost: 0,
    labor: 0,
    total: 0,
    amount: 0,
    children: [
      {
        id: 3,
        createdAt: 1752633275,
        updatedAt: 1752633275,
        deletedAt: 0,
        isDeleted: false,
        type: "GROUP",
        name: "Cha 2",
        lengthMm: 12,
        widthMm: 12,
        heightMm: 0,
        diameterMm: 0,
        mass: 0,
        quantity: 0,
        materialCost: 0,
        labor: 0,
        total: 0,
        amount: 0,
        unit: null,
        children: [
          {
            id: 4,
            createdAt: 1752633275,
            updatedAt: 1752633275,
            deletedAt: 0,
            isDeleted: false,
            type: "ITEM",
            name: "Con 1",
            lengthMm: 12,
            widthMm: 12,
            heightMm: 0,
            diameterMm: 0,
            mass: 0,
            quantity: 0,
            materialCost: 0,
            labor: 0,
            total: 0,
            amount: 0,
            unit: null,
            children: [],
          },
        ],
      },
    ],
  },
];

const calculateParentCode = (
  targetRecord: BOQDetail,
  allData: BOQDetail[]
): string => {
  const findPath = (
    items: BOQDetail[],
    target: BOQDetail,
    path: number[] = []
  ): number[] | null => {
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      const currentPath = [...path, i + 1];

      if (item.id === target.id) {
        return currentPath;
      }

      if (item.children && item.children.length > 0) {
        const childPath = findPath(item.children, target, currentPath);
        if (childPath) {
          return childPath;
        }
      }
    }
    return null;
  };

  const path = findPath(allData, targetRecord);
  if (!path || path.length <= 1) {
    return ""; // Root level has no parent
  }

  // Return parent path (exclude the last element)
  const parentPath = path.slice(0, -1);
  return parentPath.join(".");
};

const BoqTable: React.FC<DynamicEditableTableProps> = ({
  isEdit = false,
  boqDetails,
  onSave,
  setBoqDetails,
  setIsEdit,
}) => {
  const { darkMode } = useTheme();

  // const [dataSource, setDataSource] = useState<any[]>(
  //   initialData.length > 0
  //     ? initialData
  //     : [
  //         {
  //           id: "1",
  //           name: "Nhóm A",
  //           type: "GROUP",
  //           amount: 1000000,
  //           children: [
  //             {
  //               id: "1-1",
  //               name: "Hạng mục A1",
  //               type: "ITEM",
  //               lengthMm: 100,
  //               widthMm: 200,
  //               heightMm: 50,
  //               quantity: 2,
  //               materialCost: 300000,
  //               labor: 200000,
  //               total: 500000,
  //               amount: 1000000,
  //             },
  //           ],
  //         },
  //         {
  //           id: "2",
  //           name: "Nhóm B",
  //           type: "GROUP",
  //           amount: 800000,
  //           children: [],
  //         },
  //       ]
  // );

  const tableClassName = `${darkMode ? "dark" : ""} boq-table`;
  const [expandedRowKeys, setExpandedRowKeys] = useState<number[]>([]);
  const [searchText, setSearchText] = useState("");
  const [loadingDownloadDemo, setLoadingDownloadDemo] = useState(false);

  // const [boqDetails, setBoqDetails] = useState<BOQDetail[]>([]);
  const importModal = useRef<ImportBOQDetailsModal>();
  useEffect(() => {
    if (boqDetails && boqDetails?.length > 0) {
      let ids: number[] = [];
      getFlatIds(boqDetails, ids);
      setExpandedRowKeys(ids);
    }
  }, [boqDetails]);

  const getFlatIds = (boqDetails: BOQDetail[], ids: number[]) => {
    boqDetails.forEach((it) => {
      ids.push(it.id);
      if (it.children) {
        return getFlatIds(it.children, ids);
      }
    });
  };

  // Flatten data structure
  // const flattenDataWithHierarchy = useMemo(() => {
  //   const flattenRecursive = (
  //     items: BOQDetail[],
  //     level = 0,
  //     parentId?: number
  //   ): BOQDetail[] => {
  //     const result: BOQDetail[] = [];

  //     items.forEach((item) => {
  //       const processedItem = {
  //         ...item,
  //         level,
  //         parentId,
  //       };

  //       result.push(processedItem);

  //       if (item.children && item.children.length > 0) {
  //         const childrenFlattened = flattenRecursive(
  //           item.children,
  //           level + 1,
  //           item.id
  //         );
  //         result.push(...childrenFlattened);
  //       }
  //     });

  //     return result;
  //   };

  //   return flattenRecursive(dataSource);
  // }, [dataSource]);

  // Filter data based on search text
  // const filteredData = useMemo(() => {
  //   if (!searchText) return flattenDataWithHierarchy;

  //   const searchLower = searchText.toLowerCase();
  //   return flattenDataWithHierarchy.filter((item) => {
  //     return (
  //       item.name.toLowerCase().includes(searchLower) ||
  //       item.id.toString().includes(searchText)
  //     );
  //   });
  // }, [flattenDataWithHierarchy, searchText]);

  // Get visible rows based on expanded state
  // const visibleRows = useMemo(() => {
  //   const result: BOQDetail[] = [];

  //   const isItemVisible = (item: BOQDetail): boolean => {
  //     if (item.level === 0) {
  //       return true;
  //     }

  //     let currentParentId = item.parentId;
  //     while (currentParentId) {
  //       if (!expandedRowKeys.includes(currentParentId)) {
  //         return false;
  //       }

  //       const parent = filteredData.find((p) => p.id === currentParentId);
  //       currentParentId = parent?.parentId;
  //     }

  //     return true;
  //   };

  //   filteredData.forEach((item) => {
  //     if (isItemVisible(item)) {
  //       result.push(item);
  //     }
  //   });

  //   return result;
  // }, [filteredData, expandedRowKeys]);

  // Helper function to update nested data
  const updateNestedData = (
    items: BOQDetail[],
    targetId: number,
    field: keyof BOQDetail,
    value: any
  ): BOQDetail[] => {
    return items.map((item) => {
      if (item.id === targetId) {
        const updatedItem = { ...item, [field]: value };

        // Auto calculate for ITEM type
        if (
          item.type === "ITEM" &&
          ["materialCost", "labor", "quantity"].includes(field)
        ) {
          const materialCost =
            field === "materialCost" ? value : updatedItem.materialCost || 0;
          const labor = field === "labor" ? value : updatedItem.labor || 0;
          const quantity =
            field === "quantity" ? value : updatedItem.quantity || 1;

          updatedItem.total = materialCost + labor;
          updatedItem.amount = (updatedItem.total || 0) * quantity;
        }

        // Auto calculate for convert fields
        if (
          item.type === "ITEM" &&
          ["priceConvert", "quantityConvert"].includes(field)
        ) {
          const priceConvert =
            field === "priceConvert" ? value : updatedItem.priceConvert || 0;
          const quantityConvert =
            field === "quantityConvert"
              ? value
              : updatedItem.quantityConvert || 0;

          updatedItem.amountConvert = priceConvert * quantityConvert;
        }

        return updatedItem;
      }
      if (item.children) {
        return {
          ...item,
          children: updateNestedData(item.children, targetId, field, value),
        };
      }
      return item;
    });
  };

  // Helper function to add new item
  const addItemToData = (
    items: BOQDetail[],
    parentId: number | null,
    newItem: BOQDetail
  ): BOQDetail[] => {
    if (!parentId) {
      return [...items, newItem];
    }

    return items.map((item) => {
      if (item.id === parentId) {
        return {
          ...item,
          children: [...(item.children || []), newItem],
        };
      }
      if (item.children) {
        return {
          ...item,
          children: addItemToData(item.children, parentId, newItem),
        };
      }
      return item;
    });
  };

  // Helper function to delete item
  const deleteItemFromData = (
    items: BOQDetail[],
    targetId: number
  ): BOQDetail[] => {
    return items.filter((item) => {
      if (item.id === targetId) {
        return false;
      }
      if (item.children) {
        item.children = deleteItemFromData(item.children, targetId);
      }
      return true;
    });
  };

  // Generate new ID
  const generateNewId = (): number => {
    return Date.now();
  };

  const handleDownloadDemoExcel = async () => {
    try {
      setLoadingDownloadDemo(true);
      const [unitNames, workTypeNames, projectItemNames] = await Promise.all([
        getListNameByApi({ api: unitApi.findAll, dataKey: "units" }),
        getListNameByTypeDictionary(DictionaryType.WorkType),
        getListNameByApi({
          api: projectItemApi.findAll,
          dataKey: "projectItems",
        }),
      ]);

      // Tạo workbook mới thay vì sử dụng template có sẵn
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet("BOQ Import");

      // Định nghĩa headers theo đúng columns của BoqTable
      const headers = [
        "Ref",
        "Ref cha",
        "Tên",
        "Tiêu chuẩn kỹ thuật",
        "Mã loại công tác",
        "Hạng mục",
        "Tầng",
        "Đơn vị",
        "Dài",
        "Rộng",
        "Cao",
        "Đường kính",
        "Số lượng",
        "Đơn giá vật tư",
        "Đơn giá nhân công",
        "Đơn vị tính (quy đổi)",
        "Số lượng (quy đổi)",
        "Đơn giá (quy đổi)",
        "Ghi chú",
      ];

      // Thêm header row
      worksheet.addRow(headers);

      // Style header row
      const headerRow = worksheet.getRow(1);
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "FFE0E0E0" },
      };

      // Set column widths
      worksheet.columns.forEach((column: any, index: number) => {
        column.width = 15;
      });

      // Thêm validation sheets
      const unitSheet = workbook.addWorksheet("Unit", { state: "veryHidden" });
      unitNames.forEach((name, index) => {
        unitSheet.getCell(`A${index + 1}`).value = name;
      });

      const workTypeSheet = workbook.addWorksheet("WorkType", {
        state: "veryHidden",
      });
      workTypeNames.forEach((name, index) => {
        workTypeSheet.getCell(`A${index + 1}`).value = name;
      });

      const projectItemSheet = workbook.addWorksheet("ProjectItem", {
        state: "veryHidden",
      });
      projectItemNames.forEach((name, index) => {
        projectItemSheet.getCell(`A${index + 1}`).value = name;
      });

      // Thêm data validation
      const maxRow = 10000;

      // Validation cho cột "Mã loại công tác" (column E - đã chuyển từ D do thêm cột "Ref cha")
      for (let row = 2; row <= maxRow; row++) {
        worksheet.getCell(`E${row}`).dataValidation = {
          type: "list",
          allowBlank: true,
          formulae: [`'WorkType'!$A$1:$A$${workTypeNames.length}`],
          error: "Vui lòng chọn một giá trị từ danh sách.",
          showErrorMessage: true,
        };
      }

      // Validation cho cột "Hạng mục" (column F - đã chuyển từ E)
      for (let row = 2; row <= maxRow; row++) {
        worksheet.getCell(`F${row}`).dataValidation = {
          type: "list",
          allowBlank: true,
          formulae: [`'ProjectItem'!$A$1:$A$${projectItemNames.length}`],
          error: "Vui lòng chọn một giá trị từ danh sách.",
          showErrorMessage: true,
        };
      }

      // Validation cho cột "Đơn vị" (column H - đã chuyển từ G)
      for (let row = 2; row <= maxRow; row++) {
        worksheet.getCell(`H${row}`).dataValidation = {
          type: "list",
          allowBlank: true,
          formulae: [`'Unit'!$A$1:$A$${unitNames.length}`],
          error: "Vui lòng chọn một giá trị từ danh sách.",
          showErrorMessage: true,
        };
      }

      // Validation cho cột "Đơn vị tính (quy đổi)" (column P - đã chuyển từ O)
      for (let row = 2; row <= maxRow; row++) {
        worksheet.getCell(`P${row}`).dataValidation = {
          type: "list",
          allowBlank: true,
          formulae: [`'Unit'!$A$1:$A$${unitNames.length}`],
          error: "Vui lòng chọn một giá trị từ danh sách.",
          showErrorMessage: true,
        };
      }

      // Export file
      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });

      const { saveAs } = await import("file-saver");
      saveAs(blob, "file_mau_nhap_boq_details.xlsx");
      message.success("Tải file import mẫu thành công!");
    } catch (error) {
      message.error(
        `Có lỗi xảy ra: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    } finally {
      setLoadingDownloadDemo(false);
    }
  };

  const handleOnUploadedFile = async (excelData: any, setData: any) => {
    const importData = excelData.results?.map((item: any) => {
      const refineRow = removeSubstringFromKeys(item, " *");
      console.log("🚀 ~ importData ~ refineRow:", refineRow);

      // Calculate total and amount
      const materialCost = parseFloat(refineRow["Đơn giá vật tư"]) || 0;
      const labor = parseFloat(refineRow["Đơn giá nhân công"]) || 0;
      const quantity = parseFloat(refineRow["Số lượng"]) || 0;
      const total = materialCost + labor;
      const amount = total * quantity;

      // Calculate convert amount
      const quantityConvert = parseFloat(refineRow["Số lượng (quy đổi)"]) || 0;
      const priceConvert = parseFloat(refineRow["Đơn giá (quy đổi)"]) || 0;
      const amountConvert = quantityConvert * priceConvert;

      return {
        // Required BOQDetail fields
        id: Date.now() + Math.random(), // Temporary ID for import
        createdAt: Date.now(),
        updatedAt: Date.now(),
        deletedAt: 0,
        isDeleted: false,
        type: "ITEM" as BOQDetailType, // Will be determined by buildTreeByParentRef
        name: refineRow["Tên"] || "",
        technical: refineRow["Tiêu chuẩn kỹ thuật"] || "",
        workType: refineRow["Mã loại công tác"] || null,
        projectItem: refineRow["Hạng mục"] || null,
        floors: parseFloat(refineRow["Tầng"]) || 0,
        unit: refineRow["Đơn vị"] || null,
        lengthMm: parseFloat(refineRow["Dài"]) || 0,
        widthMm: parseFloat(refineRow["Rộng"]) || 0,
        heightMm: parseFloat(refineRow["Cao"]) || 0,
        diameterMm: parseFloat(refineRow["Đường kính"]) || 0,
        mass: 0,
        quantity: quantity,
        materialCost: materialCost,
        labor: labor,
        total: total,
        amount: amount,
        unitConvert: refineRow["Đơn vị tính (quy đổi)"] || null,
        quantityConvert: quantityConvert,
        priceConvert: priceConvert,
        amountConvert: amountConvert,
        note: refineRow["Ghi chú"] || "",

        // Raw data from Excel - these will be displayed exactly in preview
        ref: refineRow["Ref"] || "", // Raw Ref from Excel
        parentRef: refineRow["Ref cha"] || "", // Raw Parent Ref from Excel
        rowNum: item.__rowNum__,
        isNew: true,
      };
    });

    setData(importData);
  };

  // Build tree structure based on Ref and Ref cha columns
  const buildTreeByParentRef = (
    items: BOQDetailImport[]
  ): BOQDetailImport[] => {
    const result: BOQDetailImport[] = [];
    const refMap: { [key: string]: BOQDetailImport } = {};

    // First pass: Create map of all items by their ref - IMPORTANT: Trim consistently
    items.forEach((item) => {
      if (item.ref && item.ref.trim()) {
        refMap[item.ref.trim()] = {
          ...item,
          children: [],
        };
      }
    });

    // Second pass: Build parent-child relationships - use trimmed values
    items.forEach((item) => {
      if (!item.ref || !item.ref.trim()) return;

      const trimmedRef = item.ref.trim();
      const currentItem = refMap[trimmedRef];
      if (!currentItem) return;

      const trimmedParentRef = item.parentRef ? item.parentRef.trim() : "";
      
      if (!trimmedParentRef || !refMap[trimmedParentRef]) {
        // No parent or parent not found - add to root
        result.push(currentItem);
      } else {
        // Has valid parent - add to parent's children
        const parent = refMap[trimmedParentRef];
        if (parent) {
          parent.children = parent.children || [];
          parent.children.push(currentItem);
        }
      }
    });

    // Third pass: Determine GROUP/ITEM types and assign numeric refs
    const assignNumericRefs = (
      nodes: BOQDetailImport[],
      parentPath: string = ""
    ): BOQDetailImport[] => {
      return nodes.map((node, index) => {
        const numericRef = parentPath
          ? `${parentPath}.${index + 1}`
          : `${index + 1}`;

        // Determine if it's a GROUP or ITEM based on whether it has children
        const hasChildren = node.children && node.children.length > 0;

        const processedNode: BOQDetailImport = {
          ...node,
          type: hasChildren ? BOQDetailType.GROUP : BOQDetailType.ITEM,
          level: parentPath ? parentPath.split(".").length : 0,
          numericRef: numericRef, // Store numeric ref for UI display
          children: hasChildren
            ? assignNumericRefs(node.children!, numericRef)
            : [],
        };

        return processedNode;
      });
    };

    return assignNumericRefs(result);
  };

  const handleCustomImport = async (initData: BOQDetailImport[]) => {
    const treeData = buildTreeByParentRef(initData);

    const { data } = await dictionaryApi.findAll({
      page: 1,
      limit: 0,
      type: DictionaryType.WorkType,
    });
    const workTypes = flatChildrenList(data.dictionaries);

    const { data: dataUnits } = await unitApi.findAll({
      page: 1,
      limit: 0,
    });
    const units = dataUnits.units;

    const { data: dataProjectItems } = await projectItemApi.findAll({
      page: 1,
      limit: 0,
    });
    const projectItems = dataProjectItems.projectItems;

    const setTypeTreeData = (tree: BOQDetailImport[]): any[] => {
      return tree.map((item) => {
        const {
          children,
          workType,
          unit,
          unitConvert,
          projectItem,
          ref,
          numericRef,
          parentRef,
          ...rest
        } = item;

        const workTypeObj = workTypes.find((wt: any) => wt.name === workType);
        const unitObj = units.find((u: { name: string }) => u.name === unit);
        const unitConvertObj = units.find(
          (u: { name: string }) => u.name === unitConvert
        );
        const projectItemObj = projectItems.find(
          (pi: { name: string }) => pi.name === projectItem
        );

        const processedItem = {
          ...rest,
          // Use numericRef for display in main table (1, 1.1, 1.1.1 format)
          // but keep original ref as a backup
          originalRef: ref,
          originalParentRef: parentRef,
          workType: workTypeObj || null,
          unit: unitObj || null,
          unitConvert: unitConvertObj || null,
          projectItem: projectItemObj || null,
        };

        if (children && children.length > 0) {
          return {
            ...processedItem,
            children: setTypeTreeData(children),
          };
        }

        return processedItem;
      });
    };

    const treeDataWithType = setTypeTreeData(treeData);

    // Set to edit mode and replace current data
    setBoqDetails(cloneDeep(treeDataWithType));

    // Set edit mode after import
    if (setIsEdit) {
      setIsEdit(true);
    }
  };

  // Add new item/group
  const handleAddItem = (
    parentId: number | null,
    type: BOQDetailType,
    level: number
  ) => {
    const newId = generateNewId();
    const newItem: NewBOQDetail = {
      id: newId,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      deletedAt: 0,
      isDeleted: false,
      name: type === "GROUP" ? "Nhóm" : "Hàng",
      type,
      lengthMm: 0,
      widthMm: 0,
      heightMm: 0,
      diameterMm: 0,
      mass: 0,
      quantity: type === "ITEM" ? 1 : 0,
      materialCost: 0,
      labor: 0,
      total: 0,
      amount: 0,
      price: 0,
      amountConvert: 0,
      priceConvert: 0,
      quantityConvert: 0,
      noteConvert: "",
      floors: 0,
      technical: "",
      note: "",
      realAmount: 0,
      version: 0,
      unit: null,
      unitConvert: null,
      level,
      children: [],
      isNew: true,
      parent: null,
    };

    // setDataSource((prev) => addItemToData(prev, parentId, newItem));
    setBoqDetails((prev) =>
      addItemToData(prev, parentId, newItem as BOQDetail)
    );

    // Auto expand parent if adding to a group
    if (parentId && !expandedRowKeys.includes(parentId)) {
      setExpandedRowKeys((prev) => [...prev, parentId]);
    }
  };

  // Delete item
  const handleDeleteItem = (record: BOQDetail) => {
    // setDataSource((prev) => deleteItemFromData(prev, record.id));
    setBoqDetails((prev) => deleteItemFromData(prev, record.id));
    // message.success("Xóa thành công!");
  };

  // Handle save all
  // const handleSaveAll = () => {
  //   onSave?.(dataSource);
  //   message.success("Lưu thành công!");
  // };

  // Handle expand
  // const handleExpand = useCallback(
  //   (expanded: boolean, record: BOQDetail) => {
  //     const key = record.id;

  //     setExpandedRowKeys((prev) => {
  //       if (expanded) {
  //         return prev.includes(key) ? prev : [...prev, key];
  //       } else {
  //         const getDescendantKeys = (parentKey: number): number[] => {
  //           const descendants: number[] = [];
  //           const children = filteredData.filter(
  //             (item) => item.parentId === parentKey
  //           );

  //           children.forEach((child) => {
  //             const childKey = child.id;
  //             descendants.push(childKey);
  //             descendants.push(...getDescendantKeys(childKey));
  //           });

  //           return descendants;
  //         };

  //         const keysToRemove = [key, ...getDescendantKeys(key)];
  //         return prev.filter((k) => !keysToRemove.includes(k));
  //       }
  //     });
  //   },
  //   [filteredData]
  // );

  // // Format currency
  // const formatVND = (value: number): string => {
  //   return new Intl.NumberFormat("vi-VN", {
  //     style: "currency",
  //     currency: "VND",
  //   }).format(value);
  // };

  // Handle cell value change
  const handleCellChange = (
    recordId: number,
    field: keyof BOQDetail,
    value: any
  ) => {
    // setDataSource((prev) => updateNestedData(prev, recordId, field, value));
    setBoqDetails((prev) => updateNestedData(prev, recordId, field, value));
  };

  // Render editable cell
  const renderEditableCell = (
    value: any,
    record: BOQDetail,
    dataIndex: keyof BOQDetail,
    inputType: "text" | "number" | "select" | "switch" | "textarea" = "text"
  ) => {
    if (!isEdit) {
      // if (dataIndex == "name") debugger;
      if (dataIndex === "type") {
        return (
          <Space>
            {record.type === "GROUP" ? <TeamOutlined /> : <FileTextOutlined />}
            {BOQ_DETAIL_TYPE_LABELS[record.type]}
          </Space>
        );
      }

      // if (dataIndex === "lengthMm" && record.type === "ITEM") {
      //   return `${record.lengthMm || 0} x ${record.widthMm || 0}${
      //     (record.heightMm || 0) > 0 ? ` x ${record.heightMm}` : ""
      //   }`;
      // }

      if (dataIndex === "unit") {
        return record?.unit?.name || "-";
      }

      if (dataIndex === "unitConvert") {
        return record?.unitConvert?.name || "-";
      }

      if (dataIndex === "workType") {
        return record?.workType?.code || "-";
      }

      if (["materialCost", "labor", "total", "amount"].includes(dataIndex)) {
        return formatVND(value || 0);
      }

      return value;
    }

    // Edit mode
    if (inputType === "select") {
      return (
        <Select
          value={value}
          style={{ width: "100%" }}
          onChange={(newValue) =>
            handleCellChange(record.id, dataIndex, newValue)
          }
        >
          <Select.Option value="GROUP">
            <Space>
              <TeamOutlined />
              Nhóm
            </Space>
          </Select.Option>
          <Select.Option value="ITEM">
            <Space>
              <FileTextOutlined />
              Hạng mục
            </Space>
          </Select.Option>
        </Select>
      );
    }
    if (inputType === "switch") {
      return (
        <Switch
          checked={value === "GROUP"}
          checkedChildren="Nhóm"
          unCheckedChildren="Hạng mục"
          onChange={(checked) =>
            handleCellChange(record.id, dataIndex, checked ? "GROUP" : "ITEM")
          }
          disabled
        />
      );
    }

    if (inputType === "number") {
      return (
        // <InputNumber
        //   value={value}
        //   style={{ width: "100%" }}
        //   min={0}
        //   onChange={(newValue) =>
        //     handleCellChange(record.id, dataIndex, newValue || 0)
        //   }
        //   formatter={(value) =>
        //     `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",")
        //   }
        //   parser={(value) => value!.replace(/\$\s?|(,*)/g, "")}
        // />
        <InputNumber
          value={value}
          style={{ width: "100%" }}
          min={0}
          onChange={(newValue) =>
            handleCellChange(record.id, dataIndex, newValue ?? 0)
          }
          onKeyPress={(e) => {
            const charCode = e.charCode;
            // Allow only digits (0-9), minus sign (-), and control keys (e.g., backspace, enter)
            if (
              charCode !== 45 && // Minus sign
              (charCode < 48 || charCode > 57) && // Digits 0-9
              ![0, 8, 13].includes(charCode) // Allow backspace (8), enter (13)
            ) {
              e.preventDefault();
            }
          }}
          formatter={(val) =>
            val ? `${val}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",") : ""
          }
          parser={(val) => {
            if (!val) return 0;
            const parsed = parseFloat(val.replace(/,/g, ""));
            return isNaN(parsed) ? 0 : parsed;
          }}
        />
      );
    }

    if (inputType === "textarea") {
      return (
        <BMDTextArea
          value={value}
          style={{ width: "100%" }}
          onChange={(e) =>
            handleCellChange(record.id, dataIndex, e.target.value)
          }
        />
      );
    }

    return (
      <Input
        value={value}
        onChange={(e) => handleCellChange(record.id, dataIndex, e.target.value)}
      />
    );
  };

  const columns: CustomizableColumn<BOQDetail>[] = [
    // {
    //   title: "",
    //   dataIndex: "",
    //   key: "expandIcon",
    //   width: 50,
    //   render: (text: string, record: BOQDetail) => (
    //     // <div
    //     //   style={{
    //     //     paddingLeft: `${(record.level || 0) * 20}px`,
    //     //     display: "flex",
    //     //     alignItems: "center",
    //     //   }}
    //     // >
    //     //   {renderEditableCell(text, record, "name", "text")}
    //     // </div>
    //     <></>
    //   ),
    // },
    {
      title: "Tên",
      dataIndex: "name",
      key: "name",
      width: 200,
      fixed: "left",
      render: (value: string, record: BOQDetail) => (
        <div
          style={{
            paddingLeft: `${(record.level || 0) * 20}px`,
            display: "flex",
            alignItems: "center",
          }}
        >
          {renderEditableCell(value, record, "name", "textarea")}
        </div>
      ),
    },
    // {
    //   title: "Loại",
    //   dataIndex: "type",
    //   key: "type",
    //   width: 120,
    //   render: (text: string, record: BOQDetail) =>
    //     renderEditableCell(text, record, "type", "switch"),
    // },
    // {

    {
      title: "Mã công tác cha",
      dataIndex: "parentCode",
      key: "parentCode",
      width: 120,
      render: (text: string, record: BOQDetail) => {
        // Tính toán mã công tác cha dựa trên vị trí trong cây
        const parentCode = calculateParentCode(record, boqDetails || []);
        return (
          <Input value={parentCode} disabled={true} style={{ width: "100%" }} />
        );
      },
    },

    {
      title: "Tiêu chuẩn kỹ thuật",
      dataIndex: "technical",
      key: "technical",
      align: "left",
      width: 150,
      render: (text: string, record: BOQDetail) => {
        if (record.type === "GROUP") return null;
        return renderEditableCell(text, record, "technical", "textarea");
      },
    },

    {
      title: "Mã loại công tác",
      key: "workType",
      dataIndex: "workType",
      width: 120,
      render: (text: string, record: BOQDetail) => {
        if (record.type === "GROUP") return null;

        if (isEdit) {
          return (
            <DictionarySelector
              value={
                typeof record.workType === "object"
                  ? record.workType?.id
                  : record.workType
              }
              placeholder="Chọn"
              initQuery={{ type: DictionaryType.WorkType }}
              initOptionItem={
                typeof record.workType === "object" && record.workType
                  ? record.workType
                  : undefined
              }
              valueIsOption={false}
              className="w-[100px]"
              onChange={(value) =>
                handleCellChange(record.id, "workType", value)
              }
            />
          );
        }

        return renderEditableCell(text, record, "workType" as keyof BOQDetail);
      },
    },

    {
      title: "Hạng mục",
      dataIndex: "projectItem",
      key: "projectItem",
      width: 150,
      render: (text: string, record: BOQDetail) => {
        if (record.type === "GROUP") return null;

        if (isEdit) {
          return (
            <ProjectItemSelector
              value={
                typeof record.projectItem === "object"
                  ? record.projectItem?.id
                  : record.projectItem
              }
              placeholder="Chọn hạng mục"
              initOptionItem={
                typeof record.projectItem === "object" && record.projectItem
                  ? record.projectItem
                  : undefined
              }
              valueIsOption={true}
              selectProps={{ style: { width: 120 } }}
              onChange={(value) =>
                handleCellChange(record.id, "projectItem", value?.id || value)
              }
            />
          );
        }

        return record?.projectItem?.name || "-";
      },
    },
    {
      title: "Tầng",
      dataIndex: "floors",
      key: "floors",
      width: 100,
      render: (text: string, record: BOQDetail) => {
        if (record.type === "GROUP") return null;
        return renderEditableCell(text, record, "floors", "number");
      },
    },
    {
      title: "Đơn vị",
      key: "unit",
      dataIndex: "unit",
      width: 120,
      render: (text: string, record: BOQDetail) => {
        if (record.type === "GROUP") return null;

        if (isEdit) {
          return (
            <UnitSelector
              value={
                typeof record.unit === "object" ? record.unit?.id : record.unit
              }
              initOptionItem={
                typeof record.unit === "object" && record.unit
                  ? record.unit
                  : undefined
              }
              valueIsOption={true}
              placeholder="Đơn vị"
              style={{ width: 100 }}
              // size="small"
              dropdownStyle={{ minWidth: 120 }}
              onChange={(value) =>
                handleCellChange(record.id, "unit", value?.id || 0)
              }
            />
          );
        }

        return renderEditableCell(text, record, "unit");
      },
    },
    {
      title: "Dài",
      key: "lengthMm",
      dataIndex: "lengthMm",
      width: 100,
      render: (text: string, record: BOQDetail) => {
        if (record.type === "GROUP") return null;

        if (isEdit) {
          return (
            <InputNumber
              placeholder="Dài"
              style={{ width: 60 }}
              value={record.lengthMm}
              onChange={(value) =>
                handleCellChange(record.id, "lengthMm", value || 0)
              }
            />
          );
        }

        return renderEditableCell(text, record, "lengthMm");
      },
    },
    {
      title: "Rộng",
      key: "widthMm",
      dataIndex: "widthMm",
      width: 100,
      render: (text: string, record: BOQDetail) => {
        if (record.type === "GROUP") return null;

        if (isEdit) {
          return (
            <InputNumber
              placeholder="Rộng"
              style={{ width: 60 }}
              value={record.widthMm}
              onChange={(value) =>
                handleCellChange(record.id, "widthMm", value || 0)
              }
            />
          );
        }

        return renderEditableCell(text, record, "widthMm");
      },
    },
    {
      title: "Cao",
      key: "heightMm",
      dataIndex: "heightMm",
      width: 100,
      render: (text: string, record: BOQDetail) => {
        if (record.type === "GROUP") return null;

        if (isEdit) {
          return (
            <InputNumber
              placeholder="Cao"
              style={{ width: 60 }}
              value={record.heightMm}
              onChange={(value) =>
                handleCellChange(record.id, "heightMm", value || 0)
              }
            />
          );
        }

        return renderEditableCell(text, record, "heightMm");
      },
    },
    {
      title: "Đường kính",
      key: "diameterMm",
      dataIndex: "diameterMm",
      width: 120,
      render: (text: string, record: BOQDetail) => {
        if (record.type === "GROUP") return null;

        if (isEdit) {
          return (
            <InputNumber
              placeholder="Đường kính"
              style={{ width: 60 }}
              value={record.diameterMm}
              onChange={(value) =>
                handleCellChange(record.id, "diameterMm", value || 0)
              }
            />
          );
        }

        return renderEditableCell(text, record, "diameterMm");
      },
    },
    {
      title: "Số lượng",
      dataIndex: "quantity",
      key: "quantity",
      width: 100,
      render: (text: number, record: BOQDetail) => {
        if (record.type === "GROUP") return null;
        return renderEditableCell(text, record, "quantity", "number");
      },
    },
    {
      title: "Đơn giá vật tư",
      dataIndex: "materialCost",
      key: "materialCost",
      width: 130,
      render: (text: number, record: BOQDetail) => {
        if (record.type === "GROUP") return null;
        return renderEditableCell(text, record, "materialCost", "number");
      },
    },
    {
      title: "Đơn giá nhân công",
      dataIndex: "labor",
      key: "labor",
      width: 150,
      render: (text: number, record: BOQDetail) => {
        if (record.type === "GROUP") return null;
        return renderEditableCell(text, record, "labor", "number");
      },
    },
    {
      title: "Tổng cộng",
      dataIndex: "totalCost",
      key: "totalCost",
      width: 150,
      render: (text: number, record: BOQDetail) => {
        if (record.type === "GROUP") return null;
        return formatVND(record.labor + record.materialCost || 0);
      },
    },

    {
      title: "Số tiền (VND)",
      dataIndex: "amount",
      key: "amount",
      align: "right",
      width: 120,
      render: (text: number, record: BOQDetail) => {
        if (record.type === "GROUP") return null;
        return formatVND(
          (record.labor + record.materialCost) * record.quantity || 0
        );
      },
    },
    // {
    //   title: "Thành tiền",
    //   dataIndex: "amount",
    //   key: "amount",
    //   align: "right",
    //   fixed: "right",
    //   width: 120,
    //   render: (text: number, record: BOQDetail) => {
    //     if (record.type === "GROUP") return null;
    //     return formatVND(text || 0);
    //   },
    // },
    // {
    //   title: "Thực tế",
    //   dataIndex: "amount",
    //   key: "amount",
    //   align: "right",
    //   width: 120,
    //   render: (text: number, record: BOQDetail) => {
    //     if (record.type === "GROUP") return null;
    //     return formatVND(
    //       (record.labor + record.materialCost) * record.quantity || 0
    //     );
    //   },
    // },
    // {
    //   title: "Số tiền (VND)",
    //   dataIndex: "realAmount",
    //   key: "realAmount",
    //   width: 150,
    //   align: "right",
    //   render: (text: number, record: BOQDetail) => {
    //     if (record.type === "GROUP") return null;
    //     return renderEditableCell(text, record, "realAmount", "number");
    //   },
    // },
    {
      title: "Đơn vị tính (quy đổi)",
      dataIndex: "unitConvert",
      key: "unitConvert",
      width: 150,
      render: (text: string, record: BOQDetail) => {
        if (record.type === "GROUP") return null;

        if (isEdit) {
          return (
            <UnitSelector
              value={
                typeof record.unitConvert === "object"
                  ? record.unitConvert?.id
                  : record.unitConvert
              }
              initOptionItem={
                typeof record.unitConvert === "object" && record.unitConvert
                  ? record.unitConvert
                  : undefined
              }
              valueIsOption={true}
              placeholder="Đơn vị"
              style={{ width: 100 }}
              dropdownStyle={{ minWidth: 120 }}
              onChange={(value) =>
                handleCellChange(record.id, "unitConvert", value?.id || 0)
              }
            />
          );
        }

        return renderEditableCell(text, record, "unitConvert");
      },
    },
    {
      title: "Số lượng (quy đổi)",
      dataIndex: "quantityConvert",
      key: "quantityConvert",
      width: 150,
      render: (text: number, record: BOQDetail) => {
        if (record.type === "GROUP") return null;
        return renderEditableCell(text, record, "quantityConvert", "number");
      },
    },
    {
      title: "Đơn giá (quy đổi)",
      dataIndex: "priceConvert",
      key: "priceConvert",
      width: 150,
      align: "right",
      render: (text: number, record: BOQDetail) => {
        if (record.type === "GROUP") return null;
        return renderEditableCell(text, record, "priceConvert", "number");
      },
    },
    {
      title: "Thành tiền (quy đổi)",
      dataIndex: "amountConvert",
      key: "amountConvert",
      width: 150,
      align: "right",
      render: (text: number, record: BOQDetail) => {
        if (record.type === "GROUP") return null;
        return formatVND(record.amountConvert || 0);
      },
    },
    {
      title: "Ghi chú",
      dataIndex: "note",
      key: "note",
      width: 150,
      render: (text: string, record: BOQDetail) => {
        if (record.type === "GROUP") return null;
        return renderEditableCell(text, record, "note", "textarea");
      },
    },
    ...(isEdit
      ? ([
          {
            title: "Thao tác",
            key: "actions",
            fixed: "right",
            width: 120,
            render: (text: string, record: BOQDetail) => (
              <Space size={2} className="flex justify-center">
                {record.type === "GROUP" && (
                  <>
                    <Tooltip title="Thêm hàng">
                      <Button
                        type="text"
                        icon={<PlusOutlined style={{ fontSize: "16px" }} />}
                        onClick={() =>
                          handleAddItem(
                            record.id,
                            BOQDetailType.ITEM,
                            record.level + 1
                          )
                        }
                        className="icon-only-button"
                      />
                    </Tooltip>
                    <Tooltip title="Thêm nhóm">
                      <Button
                        type="text"
                        icon={<BiListPlus size={16} />}
                        onClick={() =>
                          handleAddItem(
                            record.id,
                            BOQDetailType.GROUP,
                            record.level + 1
                          )
                        }
                        className="icon-only-button"
                      />
                    </Tooltip>
                  </>
                )}
                <Popconfirm
                  title="Bạn có chắc chắn muốn xóa?"
                  onConfirm={() => handleDeleteItem(record)}
                  okText="Xóa"
                  cancelText="Hủy"
                >
                  <Button
                    type="text"
                    danger
                    icon={<DeleteOutlined style={{ fontSize: "16px" }} />}
                    className="icon-only-button"
                  />
                </Popconfirm>
              </Space>
            ),
          },
        ] as CustomizableColumn<BOQDetail>[])
      : []),
  ];

  // Function to calculate total amount recursively
  const calculateTotalAmount = (items: BOQDetail[]): number => {
    return items.reduce((total, item) => {
      if (item.type === "ITEM") {
        // Only calculate for ITEM type
        const itemAmount =
          (item.labor + item.materialCost) * item.quantity || 0;
        total += itemAmount;
      }

      // Recursively calculate for children
      if (item.children && item.children.length > 0) {
        total += calculateTotalAmount(item.children);
      }

      return total;
    }, 0);
  };

  const totalAmount = useMemo(() => {
    return calculateTotalAmount(boqDetails || []);
  }, [boqDetails]);

  return (
    <div className="">
      <div>
        <div className="flex gap-4 items-end pb-3 justify-between">
          <div className="flex gap-4 items-end flex-wrap flex-1 justify-end">
            {/* <div className="w-80">
              <CustomInput
                placeholder="Tìm kiếm theo tên, mã"
                value={searchText}
                onChange={(value) => setSearchText(value)}
                allowClear
              />
            </div> */}
            {isEdit && (
              <Space className="flex justify-end">
                <CustomButton
                  icon={<PlusOutlined />}
                  onClick={() => handleAddItem(null, BOQDetailType.GROUP, 0)}
                >
                  Thêm nhóm
                </CustomButton>
                <Button
                  icon={<PlusOutlined />}
                  onClick={() => handleAddItem(null, BOQDetailType.ITEM, 0)}
                >
                  Thêm hàng
                </Button>
                <CustomButton
                  size="small"
                  icon={<ImportOutlined />}
                  onClick={() => importModal.current?.open()}
                >
                  Nhập excel
                </CustomButton>
              </Space>
            )}
          </div>

          {/* {isEdit && (
            <Button type="primary" onClick={handleSaveAll} size="large">
              Lưu tất cả
            </Button>
          )} */}
        </div>

        <Table
          columns={columns}
          dataSource={boqDetails}
          rowKey="id"
          pagination={false}
          scroll={{ x: "max-content" }}
          bordered
          // summary={() => (
          //   <Table.Summary fixed>
          //     <Table.Summary.Row>
          //       <Table.Summary.Cell index={0} colSpan={columns.length - 3}>
          //         <strong>Tổng cộng</strong>
          //       </Table.Summary.Cell>
          //       <Table.Summary.Cell index={1}>
          //         <strong>Thành tiền: {formatVND(totalAmount)}</strong>
          //       </Table.Summary.Cell>
          //       <Table.Summary.Cell index={2}>
          //         <strong>Thành tiền: {formatVND(totalAmount)}</strong>
          //       </Table.Summary.Cell>
          //       {isEdit && <Table.Summary.Cell index={3}></Table.Summary.Cell>}
          //     </Table.Summary.Row>
          //   </Table.Summary>
          // )}
          expandable={{
            // childrenColumnName: "children2",
            expandedRowRender: (record, index, indent) => {
              // console.log({ indent });
              return null;
              return (
                <BoqSubTable
                  boqDetails={record.children}
                  columns={columns}
                  indent={indent}
                  key={record.id}
                />
              );
            },
            expandedRowKeys,
            onExpand: (expanded, record) => {
              if (expanded) {
                setExpandedRowKeys((pre) => {
                  pre.push(record.id);
                  return pre;
                });
              } else {
                setExpandedRowKeys((pre) => {
                  return pre.filter((it) => it !== record.id);
                });
              }
            },
            expandIcon: ({ expanded, onExpand, record }) => {
              const hasChildren = record.children && record.children.length > 0;
              // if (!hasChildren) {
              //   return <></>;
              // }

              return (
                <Button
                  type="text"
                  size="small"
                  className={clsx(!hasChildren && "invisible")}
                  icon={expanded ? <DownOutlined /> : <RightOutlined />}
                  onClick={(e) => {
                    e.stopPropagation();
                    onExpand(record, e);
                  }}
                />
              );
            },
            // expandIconColumnIndex: 0,
            rowExpandable: (record) =>
              record.children && record.children.length > 0,
          }}
          className={tableClassName}
        />
        {useMemo(
          () => (
            <ImportBOQDetails
              guide={[
                "Vui lòng tải và sử dụng file mẫu để import dữ liệu đúng cách",
                "Không được thay đổi tiêu đề trong file csv/excel mẫu để tránh import thiếu dữ liệu",
                "Cột 'Ref': Mã định danh duy nhất cho mỗi dòng (có thể là A, B, I, II, 1, 2, v.v.)",
                "Cột 'Ref cha': Mã của dòng cha (để trống nếu là dòng gốc)",
                "Ví dụ: Ref=A2, Ref cha=A có nghĩa A2 là con của A",
                "Preview sẽ hiển thị y hệt dữ liệu Excel, UI cuối cùng sẽ hiển thị dạng 1, 1.1, 1.1.1",
                "Các trường bắt buộc: Ref, Tên",
                "Import thành công sẽ tự động chuyển sang chế độ chỉnh sửa và thay thế dữ liệu hiện tại",
              ]}
              onSuccess={() => {
                // query.page = 1;
                // fetchData();
              }}
              ref={importModal}
              createApi={boqApi.create}
              onUploaded={(excelData, setData) =>
                handleOnUploadedFile(excelData, setData)
              }
              okText={`Nhập BOQ ngay`}
              onDownloadDemoExcel={handleDownloadDemoExcel}
              onCustomImport={handleCustomImport}
            />
          ),
          [loadingDownloadDemo]
        )}
      </div>
    </div>
  );
};

export default BoqTable;
