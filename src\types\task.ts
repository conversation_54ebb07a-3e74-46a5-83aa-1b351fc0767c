import { ApprovalHistory } from "./approvalHistory";
import { ApprovalList } from "./approvalList";
import { Dictionary } from "./dictionary";
import { FileAttach } from "./fileAttach";
import { Project } from "./project";
import { Staff } from "./staff";
import { Unit } from "./unit";
import { RFI } from "./rfi";
import { Draw } from "./draw";
import { Submittal } from "./submittal";
import { Punch } from "./punch";
import { Provider } from "./provider";
import { MemberShip } from "./memberShip";
import { ProjectItem } from "./projectItem";
import { Comment } from "./comment";
import { Dayjs } from "dayjs";
import { Todo } from "./todo";

export enum TaskStatus {
  NotStarted = "NOT_STARTED", // Chưa thực hiện
  InProgress = "IN_PROGRESS", // Đang thực hiện
  Completed = "COMPLETED", // Hoàn thành
  Closed = "CLOSED", // Đ<PERSON>g
  PendingApproval = "PENDING_APPROVAL", // Ch<PERSON> duyệt
  Approved = "APPROVED", // Đ<PERSON> duyệt
  Rejected = "REJECTED", // Từ chối
  NeedEdit = "NEED_EDIT", // Yêu cầu chỉnh
  Reopen = "REOPEN", //mở lại
}

export const TaskStatusTrans: Record<TaskStatus, string> = {
  [TaskStatus.NotStarted]: "Chưa thực hiện",
  [TaskStatus.InProgress]: "Đang thực hiện",
  [TaskStatus.Completed]: "Hoàn thành",
  [TaskStatus.Closed]: "Đóng",
  [TaskStatus.PendingApproval]: "Chờ duyệt",
  [TaskStatus.Approved]: "Đã duyệt",
  [TaskStatus.Rejected]: "Từ chối",
  [TaskStatus.NeedEdit]: "Yêu cầu chỉnh",
  [TaskStatus.Reopen]: "Mở lại",
};

export enum TaskType {
  Schedule = "SCHEDULE", // Tiến độ
  Task = "TASK", // Công việc
}

export const TaskTypeTrans = {
  [TaskType.Schedule]: "Tiến độ",
  [TaskType.Task]: "Công việc",
};

export enum TaskPriority {
  Low = "LOW", // Thấp
  Medium = "MEDIUM", // Trung bình
  High = "HIGH", // Cao
}

export const TaskPriorityTrans = {
  [TaskPriority.Low]: "Thấp",
  [TaskPriority.Medium]: "Trung bình",
  [TaskPriority.High]: "Cao",
};

export interface Task {
  id: number;
  code: string;
  createdAt: number;
  dependencyType: string;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  // Enum nguồn tạo task
  title: string; // Tiêu đề công việc
  description?: string; // Mô tả
  //   status: TaskStatus; // Trạng thái
  //   source: TaskSource; // Nguồn tạo
  //     priority: TaskPriority; // Ưu tiên:
  startDate: string;
  endDate: string;
  isPrivate: boolean;
  duration: number;
  priority: TaskPriority; // Ưu tiên:
  startAt: number;
  endAt: number;
  type: TaskType; // Loại công việc
  percentComplete: number;
  weight: number; // trọng số
  cost: number; // chi phí
  workVolume: number; // khôi lượng công việc
  module: string;
  moduleId: number;
  // Ngày đến hạn
  dueAt: number;
  // Có bật quy trình duyệt không?
  requireApproval: boolean;
  // Đính kèm tài liệu
  attachments: FileAttach[];
  // Lịch sử phê duyệt
  approvalHistories: ApprovalHistory[];
  // Bình luận
  comments: Comment[];
  project: Project;
  workTypes: Dictionary[]; // Loại công tác
  // Người phụ trách
  assignee: Staff;
  assigneeMemberShip: MemberShip;
  // Người giao việc
  reporter: Staff;
  parent: Task;
  children: Task[];
  // Danh sách người duyệt (nếu bật quy trình duyệt)
  approverStaffs: Staff[];
  followStaffs: Staff[];
  approvalLists: ApprovalList[];
  followMemberShips: MemberShip[];
  //   activityLogs: ActivityLog[];
  taskCategory: Dictionary | null; // hạng mục dùng cho schedule và task
  projectItem: ProjectItem; //hạng mục
  taskGroup: Dictionary | null; // nhóm công việc
  scheduleGroup: Dictionary | null; // nhóm tiến độ
  unit: Unit;
  provider: Provider | null;
  department: Dictionary;
  // liên kết
  rfi: RFI;
  draw: Draw;
  submittal: Submittal;
  punch: Punch;
  // info create update
  createdBy: Staff;
  updatedBy: Staff;
  // res
}

export interface TaskForm extends Partial<Task> {
  projectId?: number;
  createdById: number;
  projectItemId?: number;
  parentId?: number;
  assigneeMemberShipId?: number;
  coordinatorIds?: number[];
  dueAtDate: Dayjs;
  departmentId?: number;
  createdDate: Dayjs;
  workTypeIds?: number[];
  todos?: Todo[];
}

export enum TaskModule {
  RFI = "RFI",
  Instruction = "INSTRUCTION",
  Draw = "DRAW",
  Task = "TASK",
  NCR = "NCR",
}
