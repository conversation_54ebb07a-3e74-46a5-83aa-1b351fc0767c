import { FacebookFilled } from "@ant-design/icons";
import { Modal } from "antd";
import React, { forwardRef, useImperativeHandle, useState } from "react";
import DocumentPage from "views/ImageManagement/DocumentPage";
export interface ChooseImageModalProps {
  onChoose: (file: any) => void;
}
export interface ChooseImageModalRef {
  onOpen: () => void;
}
const ChooseImageModal = forwardRef(
  ({ onChoose }: ChooseImageModalProps, ref) => {
    const [visible, setVisible] = useState(false);
    const handleChoose = (file: any) => {
      onChoose(file); // Gọi callback truyền từ component cha
      setVisible(false); // Đóng modal sau khi chọn
    };
    const onOpenModal = () => {
      setVisible(true);
    };
    const handleCloseModal = () => {
      setVisible(false);
    };
    useImperativeHandle(
      ref,
      () => ({
        onOpen: () => {
          onOpenModal();
        },
      }),
      []
    );
    return (
      <Modal
        title="Chọn ảnh"
        closable
        onCancel={handleCloseModal}
        open={visible}
        width={1200}
        footer={null}
      >
        <DocumentPage title="Chọn ảnh" onChoose={handleChoose} />
      </Modal>
    );
  }
);

export default ChooseImageModal;
