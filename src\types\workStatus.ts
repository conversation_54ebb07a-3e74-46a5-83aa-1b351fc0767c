export enum WorkStatus {
  Active = "ACTIVE",
  Inactive = "INACTIVE",
  Draft = "DRAFT",
  Probation = "PROBATION",
  Resigned = "RESIGNED",
  Terminated = "TERMINATED",
  Retired = "RETIRED",
  Deleted = "DELETED",
}

export const WorkStatusTrans = {
  [WorkStatus.Draft]: {
    label: "Khởi tạo",
    value: WorkStatus.Draft,
    color: "gray",
  },
  [WorkStatus.Probation]: {
    label: "Thử việc",
    value: WorkStatus.Probation,
    color: "orange",
  },
  [WorkStatus.Active]: {
    label: "Đang làm việc",
    value: WorkStatus.Active,
    color: "green",
  },
  [WorkStatus.Inactive]: {
    label: "Tạm ngừng",
    value: WorkStatus.Inactive,
    color: "blue",
  },
  [WorkStatus.Resigned]: {
    label: "Đã nghỉ việc",
    value: WorkStatus.Resigned,
    color: "purple",
  },
  [WorkStatus.Terminated]: {
    label: "<PERSON><PERSON> chấm dứt",
    value: WorkStatus.Terminated,
    color: "red",
  },
  [WorkStatus.Retired]: {
    label: "Nghỉ hưu",
    value: WorkStatus.Retired,
    color: "teal",
  },
  [WorkStatus.Deleted]: {
    label: "Đã xóa",
    value: WorkStatus.Deleted,
    color: "gray",
  },
};
