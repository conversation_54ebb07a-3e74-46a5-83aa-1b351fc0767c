import {
  <PERSON><PERSON>,
  Card,
  Col,
  Divider,
  Form,
  Input,
  message,
  Modal,
  Row,
  Select,
} from "antd";
import TextArea from "antd/es/input/TextArea";
import { UploadFile } from "antd/lib";
import { Rule } from "antd/lib/form";
import { providerApi } from "api/provider.api";
import { UnitSelector } from "components/Selector/UnitSelector";
import { FileAttachPayload } from "components/Upload/FileUploadItem";
import { FileUploadMultiple } from "components/Upload/FileUploadMultiple";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import React, { useEffect, useImperativeHandle, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import { FileAttach } from "types/fileAttach";
import { ModalStatus } from "types/modal";
import { Provider, ProviderTypeTrans } from "types/provider";
import { getTitle } from "utils";
import UploadImg from "assets/images/upload-img.png";
import { $url } from "utils/url";
import { useWard } from "hooks/useWard";
import { useCity } from "hooks/useCity";
import { AddressSelect } from "components/AddressSelect/AddressSelect";
import CustomInput from "components/Input/CustomInput";
import CustomSelect from "components/Input/CustomSelect";
import { CountryTrans, CurrencyTrans } from "types/address";
import CustomButton from "components/Button/CustomButton";
import { DictionarySelector } from "components/Selector/DictionarySelector";
import { DictionaryType } from "types/dictionary";

const rules: Rule[] = [{ required: true }];

export const CreateProviderPage = ({ title = "" }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const addressSelectRef = useRef<any>();

  // const { fetchData, query, setQuery, wards } = useWard({
  //   initQuery: {
  //     limit: 100,
  //     page: 1,
  //   },
  // });

  // const {} = useCity({ init });

  useEffect(() => {
    document.title = getTitle(title);
  }, []);

  const createData = async () => {
    const valid = await form.validateFields();
    const {
      files,
      cityId,
      districtId,
      wardId,
      accountGroupId,
      materialGroupIds,
      ...data
    } = form.getFieldsValue();

    const payload = {
      provider: {
        ...data,
        files: typeof files === "string" ? files : JSON.stringify(files),
      },
      cityId,
      districtId,
      wardId,
      accountGroupId,
      materialGroupIds,
    };
    setLoading(true);
    try {
      const res = await providerApi.create(payload);
      message.success("Tạo nhà cung cấp thành công!");
      navigate("/master-data/provider-list");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <div className="font-bold text-2xl mb-[20px]">Tạo nhà cung cấp</div>
      <Card>
        <Form layout="vertical" form={form}>
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item
                style={{ marginBottom: 0, height: "100%" }}
                label={<div>Logo nhà cung cấp</div>}
                name="logo"
                className="[&_.ant-form-item-row]:h-full [&_.ant-form-item-control-input]:h-full [&_.ant-form-item-control-input-content]:h-full"
              >
                <SingleImageUpload
                  onUploadOk={(file: FileAttach) => {
                    form.setFieldsValue({
                      logo: file.path,
                    });
                  }}
                  height={"100%"}
                  width={"100%"}
                  imageUrl={form.getFieldValue("logo")}
                  className="h-full"
                />
              </Form.Item>
            </Col>

            <Col span={9}>
              <Form.Item label="Mã nhà cung cấp" name="code">
                <CustomInput placeholder="Nếu không điền hệ thống sẽ tự sinh mã" />
              </Form.Item>
              <Form.Item label="Tên nhà cung cấp" name="name" rules={rules}>
                <CustomInput placeholder="Tên nhà cung cấp" />
              </Form.Item>
              <Form.Item label="Loại nhà cung cấp" name="type" rules={rules}>
                <CustomSelect
                  placeholder="Chọn loại"
                  options={Object.values(ProviderTypeTrans).map((item) => ({
                    label: item.label,
                    value: item.value,
                  }))}
                />
              </Form.Item>
              <Form.Item label="Mô tả" name="description" rules={rules}>
                <CustomInput
                  type="textarea"
                  placeholder="Mô tả nhà cung cấp"
                  rows={4}
                />
              </Form.Item>
            </Col>
            <Col span={9}>
              <Form.Item
                shouldUpdate={true}
                style={{ marginBottom: 0, height: "100%" }}
                className="[&_.ant-form-item-row]:!h-full [&_.ant-form-item-control-input]:!h-full [&_.ant-form-item-control-input-content]:!h-full [&_.ant-form-item-has-success]:!h-full"
              >
                {() => {
                  return (
                    <Form.Item
                      label={"Tệp đính kèm"}
                      style={{ marginBottom: 0, height: "100%" }}
                      name="files"
                      className="[&_.ant-form-item-row]:!h-full [&_.ant-form-item-control-input]:!h-full [&_.ant-form-item-control-input-content]:!h-full [&_.ant-form-item-has-success]:!h-full"
                    >
                      <FileUploadMultiple
                        className="h-full"
                        draggerContent={
                          <p className="ant-upload-text ">
                            <img
                              src={UploadImg}
                              height={30}
                              width={30}
                              className="mr-2"
                            />
                            <div className="font-bold">
                              {"Tải lên tệp đính kèm"}
                            </div>
                            <div className="text-gray-500">
                              Tệp hợp đồng, hóa đơn, tài liệu
                            </div>
                          </p>
                        }
                        fileList={fileList}
                        onUploadOk={(fileList) => {
                          console.log({ fileList });
                          const filePayloads: FileAttachPayload[] =
                            fileList.map((item) => {
                              return {
                                url:
                                  item.url || $url(item.response?.data?.path),
                                name: item.name,
                                size: item?.size,
                                uid: item?.uid,
                                type: item.type,
                                path: item.response?.data?.path,
                                destination: item.response?.data?.destination,
                              };
                            });

                          console.log({ filePayload: filePayloads });
                          setFileList(fileList);
                          form.setFieldsValue({
                            files: JSON.stringify(filePayloads),
                          });
                        }}
                        onDelete={setFileList}
                      />
                    </Form.Item>
                  );
                }}
              </Form.Item>
            </Col>
          </Row>

          <Card title=" Thông tin chung" className="mb-0 form-card mt-[16px]">
            <Row gutter={16}>
              <Col span={6}>
                <Form.Item label="Tên khác" name="aliasName">
                  <CustomInput placeholder="Tên khác" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="Nhóm tài khoản" name="accountGroupId">
                  <DictionarySelector
                    placeholder="Chọn nhóm tài khoản"
                    initQuery={{ type: DictionaryType.AccountGroup }}
                  />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="Nhóm sản phẩm" name="materialGroupIds">
                  <DictionarySelector
                    multiple
                    placeholder="Chọn nhóm sản phẩm"
                    initQuery={{ type: DictionaryType.ProductGroup }}
                  />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="Website" name="website">
                  <CustomInput placeholder="https://" />
                </Form.Item>
              </Col>
              {/* <Col span={6}>
                <Form.Item label="Điện thoại 1" name="phone1">
                  <Input />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="Điện thoại 2" name="phone2">
                  <Input />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="Fax" name="fax">
                  <Input />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="Email" name="email">
                  <Input />
                </Form.Item>
              </Col> */}
              <Col span={6}>
                <Form.Item label="Quốc gia" name="country">
                  <CustomSelect
                    placeholder="Chọn quốc gia"
                    options={Object.values(CountryTrans).map((item) => ({
                      label: item.label,
                      value: item.value,
                    }))}
                  />{" "}
                </Form.Item>
              </Col>
              <AddressSelect
                ref={addressSelectRef}
                form={form}
                onChange={function (data: any): void {}}
                // buttonText={merchant?.address ? "Update" : "Chọn vị trí"}
              ></AddressSelect>
              {/* <Col span={6}>
              <Form.Item label="Tỉnh" name="code">
                <Input />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="TP/Quận/Huyện" name="code">
                <Input />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="Phường/Xã" name="code">
                <Input />
              </Form.Item>
            </Col> */}
              <Col span={12}>
                <Form.Item label="Địa chỉ" name="address">
                  <CustomInput placeholder="Địa chỉ" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Mã vùng" name="areaCode">
                  <CustomInput placeholder="Mã vùng" />
                </Form.Item>
              </Col>
            </Row>
          </Card>
          <Card
            title="Thông tin giao dịch"
            className="mb-0 form-card mt-[16px]"
          >
            <Row gutter={16}>
              {/* <Col span={6}>
                <Form.Item label="Phương thức thanh toán" name="paymentMethod">
                  <CustomInput placeholder="Phương thức thanh toán" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="Điều khoản thanh toán" name="paymentTerms">
                  <Input />
                </Form.Item>
              </Col> */}
              <Col span={12}>
                <Form.Item label="Loại tiền tệ" name="currency">
                  <CustomSelect
                    placeholder="Chọn loại tiền tệ"
                    options={Object.values(CurrencyTrans).map((item) => ({
                      label: item.label,
                      value: item.value,
                    }))}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Mã số thuế" name="taxNumber">
                  <CustomInput placeholder="MST" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Tên ngân hàng 1" name="bankName1">
                  <CustomInput placeholder="Tên ngân hàng" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Số tài khoản 1" name="bankAccount1">
                  <CustomInput placeholder="STK" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Tên ngân hàng 2" name="bankName2">
                  <CustomInput placeholder="Tên ngân hàng" />
                </Form.Item>
              </Col>

              <Col span={12}>
                <Form.Item label="Số tài khoản 2" name="bankAccount2">
                  <CustomInput placeholder="STK" />
                </Form.Item>
              </Col>
            </Row>
          </Card>
          <Card
            title="Thông tin người liên hệ"
            className="mb-0 form-card mt-[16px]"
          >
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item label="Danh xưng" name="salutation">
                  <CustomInput placeholder="Danh xưng" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="Tên người liên hệ" name="contactName">
                  <CustomInput placeholder="Họ và tên" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="Điện thoại" name="contactPhone">
                  <CustomInput placeholder="SĐT" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="Điện thoại di động" name="contactMobile">
                  <CustomInput placeholder="SĐT DĐ" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="Email" name="contactEmail">
                  <CustomInput type="email" placeholder="<EMAIL>" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="Địa chỉ" name="contactAddress">
                  <CustomInput placeholder="Địa chỉ" />
                </Form.Item>
              </Col>
            </Row>
          </Card>
        </Form>
        <div className="flex gap-[16px] justify-end mt-2">
          <CustomButton
            variant="outline"
            className="cta-button"
            onClick={() => {
              navigate("/master-data/provider-list");
            }}
          >
            Hủy
          </CustomButton>
          <CustomButton
            className="cta-button"
            loading={loading}
            onClick={() => {
              createData();
            }}
          >
            Tạo nhà cung cấp
          </CustomButton>
        </div>
      </Card>
    </div>
  );
};
