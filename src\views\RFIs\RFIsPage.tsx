import {
  Card,
  Spin,
  Button,
  Space,
  Tooltip,
  Modal,
  Tag,
  Avatar,
  message,
  Select,
} from "antd";
import PencilIcon from "assets/svgs/PencilIcon";
import CustomButton from "components/Button/CustomButton";
import CustomInput from "components/Input/CustomInput";
import PageTitle from "components/PageTitle/PageTitle";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import { Pagination } from "components/Pagination";
import { useTheme } from "context/ThemeContext";
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { PermissionNames } from "types/PermissionNames";
import { Instruction, InstructionTypeTrans } from "types/instruction";
import { handleExport, MyExcelColumn } from "utils/MyExcel";
import dayjs from "dayjs";
import {
  checkDeletePermissionByCreator,
  checkEditPermissionByCreator,
  checkRoles,
  filterActionColumnIfNoPermission,
} from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { observer } from "mobx-react";
import { $url } from "utils/url";
import logoImage from "assets/images/logo.png";
import { Staff } from "types/staff";
import { useRfi } from "hooks/useRfi";
import { rfiApi } from "api/rfi.api";
import {
  getOverallApprovalStatus,
  ProgressRFIStatus,
  RFI,
  RFIStatus,
  RFIStatusOptions,
} from "types/rfi";
import { unixToDate } from "utils/dateFormat";
import DeleteIcon from "assets/svgs/DeleteIcon";
import QueryLabel from "components/QueryLabel/QueryLabel";
import EditButton from "components/Button/EditButton";
import DeleteButton from "components/Button/DeleteButton";
import ProgressLegend from "components/ProgressLegend/ProgressLegend";
import { useTransition } from "hooks/useTransition";
import { getTitle } from "utils";
import { appStore } from "store/appStore";
import { MemberShip } from "types/memberShip";
import { userStore } from "store/userStore";
import { StepItem } from "components/ApproveProcess/ApprovalStepsCard";
import {
  findLastApprovalStep,
  transformApproveData,
} from "components/ApproveProcess/approveUtil";

const exportColumns: MyExcelColumn<RFI>[] = [
  {
    header: "ID",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "code",
    columnKey: "code",
    render: (record) => record.code,
  },
  {
    header: "Tiêu đề",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "title",
    columnKey: "title",
    render: (record) => record.title,
  },
  {
    header: "Phân loại",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "rfiCategory",
    columnKey: "rfiCategory",
    render: (record) => record.rfiCategory?.name || "",
  },
  {
    header: "Mã bản vẽ",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "codeDraw",
    columnKey: "codeDraw",
    render: (record) => record.codeDraw || "",
  },
  {
    header: "Phần thông số",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "specifications",
    columnKey: "specifications",
    render: (record) => record.specifications || "",
  },
  {
    header: "Trạng thái",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "statusapproval",
    columnKey: "statusapproval",
    render: (record: RFI) => {
      const statusInfo =
        ProgressRFIStatus[
          record.status as unknown as keyof typeof ProgressRFIStatus
        ];
      return statusInfo?.label || "";
    },
  },
  {
    header: "Trạng thái phản hồi",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "status",
    columnKey: "status",
    render: (record) => {
      const statusObj = RFIStatusOptions.find(
        (opt) => opt.value === record.status
      );
      return statusObj ? statusObj.label : "-";
    },
  },
  {
    header: "Ngày gửi",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "requestDate",
    columnKey: "requestDate",
    render: (record) => {
      if (!record.requestDate) return "";
      const date = new Date(record.requestDate);
      return date.toLocaleDateString("vi-VN");
    },
  },
  {
    header: "Ngày đến hạn",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "dueAt",
    columnKey: "dueAt",
    render: (record) => {
      if (!record.dueAt || record.dueAt === 0) return "";
      const jsDate = new Date(record.dueAt * 1000);
      return jsDate.toLocaleDateString("vi-VN");
    },
  },
  {
    header: "Người phụ trách",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "responsibility",
    columnKey: "responsibility",
    render: (record) => record.responsibility?.fullName || "",
  },
  {
    header: "Người gửi",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "sender",
    columnKey: "sender",
    render: (record) => record.sender?.fullName || "",
  },
  {
    header: "Công ty người gửi",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "sendCompany",
    columnKey: "sendCompany",
    render: (record) => record.sendCompany?.name || "",
  },
  {
    header: "Chức vụ người gửi",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "senderLevel",
    columnKey: "senderLevel",
    render: (record) => record.senderLevel?.name || "",
  },
  {
    header: "Người nhận",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "recipientMemberShip",
    columnKey: "recipientMemberShip",
    render: (record) => record.recipientMemberShip?.name || "",
  },
  {
    header: "Công ty người nhận",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "receiverCompany",
    columnKey: "receiverCompany",
    render: (record) => record.receiverCompany?.name || "",
  },
  {
    header: "Chức vụ người nhận",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "receiverLevel",
    columnKey: "receiverLevel",
    render: (record) => record.receiverLevel?.name || "",
  },
  {
    header: "Thông tin yêu cầu",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "requestInfo",
    columnKey: "requestInfo",
    render: (record) => record.requestInfo || "",
  },
];

function RFIsPage({ title }: { title: string }) {
  const {
    haveAddPermission,
    haveDeletePermission,
    haveEditPermission,
    haveViewAllPermission,
  } = checkRoles(
    {
      add: PermissionNames.rfisAdd,
      edit: PermissionNames.rfisEdit,
      delete: PermissionNames.rfisDelete,
      viewAll: PermissionNames.rfisViewAll,
    },
    permissionStore.permissions
  );

  const canEditRecord = (record: RFI) => {
    return checkEditPermissionByCreator(
      userStore.info.id,
      record.createdBy?.id,
      haveEditPermission,
      haveViewAllPermission
    );
  };

  const canDeleteRecord = (record: RFI) => {
    return checkDeletePermissionByCreator(
      userStore.info.id,
      record.createdBy?.id,
      haveDeletePermission,
      haveViewAllPermission
    );
  };

  const [searchValue, setSearchValue] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [loadingDelete, setLoadingDelete] = useState(false);
  const [page, setPage] = React.useState(1);
  const [limit, setLimit] = React.useState(20);
  const { isLoaded } = useTransition();

  const {
    fetchRfi,
    rfis,
    loadingRfi,
    queryRfi,
    setQueryRfi,
    totalRfi,
    statusRfi,
  } = useRfi({
    initQuery: {
      limit: 10,
      page: 1,
      isAdmin: haveViewAllPermission ? true : undefined,
      projectId: appStore.currentProject?.id,
    },
  });

  const { darkMode } = useTheme();
  const navigate = useNavigate();

  const handleSearch = (value: string) => {
    setSearchValue(value);
  };
  const handleStatusChange = (value: string) => {
    setStatusFilter(value);
    const newQuery = {
      ...queryRfi,
      page: 1,
      status: value || undefined,
    };
    setQueryRfi(newQuery);
  };

  useEffect(() => {
    if (isLoaded) {
      fetchRfi();
    }
  }, [isLoaded, queryRfi]);

  useEffect(() => {
    document.title = getTitle(title);
  }, []);

  const handleCreateRFIs = () => {
    navigate(`/report/${PermissionNames.rfisAdd}`);
  };

  const handleDeleteRFI = async (id: number) => {
    try {
      await rfiApi.delete(id);
      message.success("Xóa RFI thành công");
      fetchRfi();
    } catch (e) {
      console.log({ e });
    } finally {
    }
  };

  const handleRowClick = (record: any) => {
    navigate(
      `/report/${PermissionNames.rfisEdit.replace(":id", record!.id + "")}`
    );
  };

  const columns: CustomizableColumn<any>[] = [
    {
      key: "code",
      title: "Mã",
      dataIndex: "code",
      width: 100,
      defaultVisible: true,
      alwaysVisible: false,
      align: "left",

      render: (_, record) => {
        return (
          <div
            className="text-[#1677ff] cursor-pointer"
            onClick={() => handleRowClick(record)}
          >
            {record.code}
          </div>
        );
      },
    },
    {
      key: "title",
      title: "Mô tả",
      dataIndex: "title",
      width: 212,
      defaultVisible: true,
      alwaysVisible: false,
    },
    {
      key: "recipientMemberShip",
      title: "Người nhận",
      dataIndex: "recipientMemberShip",
      width: 200,
      defaultVisible: true,
      alwaysVisible: false,
      render: (_, record: RFI) =>
        _renderMembershipRow(record.recipientMemberShip),
    },
    // {
    //   key: "responsibilityMemberShip",
    //   title: "Người phụ trách",
    //   dataIndex: "responsibilityMemberShip",
    //   width: 200,
    //   defaultVisible: true,
    //   alwaysVisible: false,
    //   render: (_, record: RFI) =>
    //     _renderMembershipRow(record.responsibilityMemberShip),
    // },
    {
      key: "requestDate",
      title: "Ngày gửi",
      width: 175,
      dataIndex: "requestDate",
      align: "right",
      render: (_, record: RFI) => (
        <div className="service-cell">
          <div className="service-info">
            <div className="service-name">
              {record.requestDate
                ? dayjs(record.requestDate).format("DD/MM/YYYY")
                : ""}
            </div>
          </div>
        </div>
      ),
      defaultVisible: true,
      alwaysVisible: false,
      sorter: (a: RFI, b: RFI) => {
        if (!a.requestDate && !b.requestDate) return 0;
        if (!a.requestDate) return 1;
        if (!b.requestDate) return -1;
        return dayjs(a.requestDate).unix() - dayjs(b.requestDate).unix();
      },
    },
    {
      key: "dueAt",
      title: "Ngày đến hạn",
      width: 175,
      dataIndex: "dueAt",
      align: "right",
      render: (_, record: RFI) => (
        <div className="service-cell">
          <div className="service-info">
            <div className="service-name">
              {record.dueAt && record?.dueAt > 0
                ? unixToDate(record.dueAt || 0)
                : ""}
            </div>
          </div>
        </div>
      ),
      defaultVisible: true,
      alwaysVisible: false,
      sorter: (a: RFI, b: RFI) => {
        if (!a.dueAt && !b.dueAt) return 0;
        if (!a.dueAt) return 1;
        if (!b.dueAt) return -1;
        return a.dueAt - b.dueAt;
      },
    },
    {
      title: "Trạng thái",
      dataIndex: "statusapproval",
      key: "statusapproval",
      align: "center",
      width: 130,
      render: (statusapproval, record) => {
        // Sort approvalLists theo position trước khi xử lý
        const lastApprovalStep: StepItem | undefined = findLastApprovalStep(
          transformApproveData(record.approvalLists, record.createdBy)
        );
        // const sortedApprovalLists = record.approvalLists
        //   ? record.approvalLists
        //       .slice()
        //       .sort(
        //         (a: { position: number }, b: { position: number }) =>
        //           a.position - b.position
        //       )
        //   : [];
        return (
          <ProgressLegend
            // status={
            //   ProgressRFIStatus[getOverallApprovalStatus(sortedApprovalLists)]
            // }
            statusColor={lastApprovalStep?.statusColor}
            statusText={lastApprovalStep?.statusText}
            steps={record.approvalLists}
          />
        );
      },
    },
    {
      key: "status",
      title: "Trạng thái phản hồi",
      dataIndex: "status",
      width: 150,
      align: "center",
      render: (_, record: RFI) => {
        // Lấy label tiếng Việt từ RFIStatusOptions dựa vào record.status
        const statusObj = RFIStatusOptions.find(
          (opt) => opt.value === record.status
        );
        return statusObj ? statusObj.label : "-";
      },
      defaultVisible: true,
      alwaysVisible: false,
    },
    {
      key: "actions",
      title: "Xử lý",
      width: 100,
      align: "center",
      fixed: "right",
      defaultVisible: true,
      alwaysVisible: true,
      render: (_, record) => (
        <Space size="small">
          {canEditRecord(record) && (
            <EditButton
              onClick={(e) => {
                e.stopPropagation();
                navigate(
                  `/report/${PermissionNames.rfisEdit.replace(
                    ":id",
                    record!.id + ""
                  )}?update=1`
                );
              }}
            />
          )}

          {canDeleteRecord(record) && (
            <DeleteButton
              onClick={(e) => {
                e.stopPropagation();
                Modal.confirm({
                  title: `Xóa RFI "${record.title}"`,
                  getContainer: () => {
                    return document.getElementById("App") as HTMLElement;
                  },
                  icon: null,
                  content: (
                    <>
                      <div>
                        Bạn sẽ không thể khôi phục dữ liệu này sau khi xóa.
                        <br />
                        Bạn có chắc chắn muốn xóa dữ liệu này?
                      </div>
                    </>
                  ),
                  footer: (_, { OkBtn, CancelBtn }) => (
                    <>
                      <CustomButton
                        variant="outline"
                        className="cta-button"
                        onClick={() => {
                          handleDeleteRFI(record.id);
                          Modal.destroyAll();
                        }}
                      >
                        Có
                      </CustomButton>
                      <CustomButton
                        onClick={() => {
                          Modal.destroyAll();
                        }}
                        className="cta-button"
                      >
                        Không
                      </CustomButton>
                    </>
                  ),
                });
              }}
            />
          )}
        </Space>
      ),
    },
  ];

  // const _renderStaffRow = (staff: Staff) => {
  //   return (
  //     <div className="flex items-center gap-[8px]">
  //       <Avatar
  //         size={28}
  //         src={staff?.avatar ? $url(staff?.avatar) : undefined}
  //         style={{ backgroundColor: "#1890ff", flexShrink: 0 }}
  //       >
  //         {staff?.fullName?.charAt(0)}
  //       </Avatar>
  //       <label htmlFor="" className="text-neutral-800 text-bold">
  //         {staff?.fullName}
  //       </label>
  //     </div>
  //   );
  // };

  const _renderMembershipRow = (membership: MemberShip) => {
    if (!membership || !membership.name) return null;
    return (
      <div className="flex items-center gap-[8px]">
        <Avatar
          size={28}
          src={
            membership.staff.avatar ? $url(membership.staff.avatar) : undefined
          }
          style={{ backgroundColor: "#1890ff", flexShrink: 0 }}
        >
          {membership.name.charAt(0)}
        </Avatar>
        <label htmlFor="" className="text-neutral-800 text-bold">
          {membership.name}
        </label>
      </div>
    );
  };

  return (
    <div className="app-container">
      <PageTitle
        title="RFIs"
        breadcrumbs={["Báo cáo", "RFIs"]}
        extra={
          <Space>
            {haveAddPermission && (
              <CustomButton
                size="small"
                showPlusIcon
                onClick={handleCreateRFIs}
              >
                Tạo yêu cầu
              </CustomButton>
            )}
            <CustomButton
              onClick={() => {
                Modal.confirm({
                  title: `Bạn có muốn xuất file excel?`,
                  getContainer: () => {
                    return document.getElementById("App") as HTMLElement;
                  },
                  icon: null,

                  footer: (_, { OkBtn, CancelBtn }) => (
                    <>
                      <CustomButton
                        variant="outline"
                        className="cta-button"
                        onClick={() => {
                          handleExport({
                            onProgress(percent) {
                              console.log("What is percent", percent);
                            },
                            exportColumns,
                            fileType: "xlsx",
                            dataField: "rfis",
                            query: {
                              ...queryRfi,
                              exportExcel: true,
                            },
                            api: rfiApi.findAll,
                            fileName: "Danh sách RFI",
                            sheetName: "Danh sách RFI",
                          });
                          Modal.destroyAll();
                        }}
                      >
                        Có
                      </CustomButton>
                      <CustomButton
                        onClick={() => {
                          Modal.destroyAll();
                        }}
                        className="cta-button"
                      >
                        Không
                      </CustomButton>
                    </>
                  ),
                });
              }}
            >
              Xuất excel
            </CustomButton>
          </Space>
        }
      />

      <Card>
        <div className="pb-[16px]">
          <div className="flex justify-between items-center mb-4">
            <div className="flex gap-[16px] items-end">
              <div className="w-[300px]">
                <CustomInput
                  tooltipContent={"Tìm theo mô tả"}
                  label="Tìm kiếm"
                  placeholder="Tìm kiếm"
                  value={searchValue}
                  onChange={handleSearch}
                  onPressEnter={() => {
                    setQueryRfi({ ...queryRfi, page: 1, search: searchValue });
                  }}
                  allowClear
                />
              </div>

              {/* <div>
                <QueryLabel>Trạng thái phản hồi</QueryLabel>
                <Select
                  allowClear={true}
                  placeholder="Chọn trạng thái phản hồi"
                  value={statusFilter ?? ""}
                  onChange={setStatusFilter}
                  options={[
                    {
                      value: "",
                      label: "Tất cả trạng thái phản hồi",
                    },
                    ...RFIStatusOptions.map((status) => ({
                      label: status.label,
                      value: status.value,
                    })),
                  ]}
                />
              </div> */}
              <CustomButton
                onClick={() => {
                  // Pass all filter parameters to API
                  setQueryRfi({
                    ...queryRfi,
                    page: 1,
                    search: searchValue,
                    status: statusFilter,
                    projectId: appStore.currentProject?.id,
                  });
                }}
              >
                Áp dụng
              </CustomButton>

              {(!!searchValue || !!statusFilter) && (
                <CustomButton
                  variant="outline"
                  onClick={() => {
                    setSearchValue("");
                    setStatusFilter("");
                    setQueryRfi({
                      ...queryRfi,
                      search: "",
                      status: "",
                      projectId: appStore.currentProject?.id,
                      page: 1,
                    });
                  }}
                >
                  Bỏ lọc
                </CustomButton>
              )}
            </div>
          </div>

          <Spin spinning={loadingRfi}>
            <CustomizableTable
              columns={filterActionColumnIfNoPermission(columns, [
                haveEditPermission,
                haveDeletePermission,
              ])}
              dataSource={rfis}
              rowKey="id"
              // loading={loadingRfi}
              pagination={false}
              scroll={{ x: 1200 }}
              bordered
              displayOptions
              onRowClick={handleRowClick}
            />
          </Spin>
        </div>

        <Pagination
          currentPage={queryRfi.page}
          total={totalRfi}
          defaultPageSize={queryRfi.limit}
          onChange={({ limit, page }) => {
            setQueryRfi({
              ...queryRfi,
              page,
              limit,
              projectId: appStore.currentProject?.id,
            });
          }}
        />
      </Card>
    </div>
  );
}

export default observer(RFIsPage);
