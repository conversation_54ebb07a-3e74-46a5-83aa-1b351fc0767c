import { Company } from "./company";
import { ProjectCategory } from "./projectCategory";

export interface Project {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  avatar: string;
  name: string;
  code: string;
  description: string;
  startAt: number;
  endAt: number;
  isActive: boolean;
  projectCategory: ProjectCategory;
  parent: Project; // dư án cha
  children: Project[];
  // Additional fields for project form
  location?: string; // địa điểm thi công
  budget?: number; // ngân sách dự án
  companyIds?: number[]; // IDs công ty
  engineerIds?: number[]; // IDs kỹ sư tham gia
  investor?: Company; // tên chủ đầu tư
  status: ProjectStatus;
  siteCamera: string;
  vr360: string;
}

export enum ProjectStatus {
  Draft = "DRAFT", // nháp
  New = "NEW", // Chưa bắt đầu
  InProgress = "IN_PROGRESS", // Đang tiến hành
  <PERSON> = "PAUSE", // Tạm dừng
  Completed = "COMPLETED", // Hoàn thành
  Cancel = "CANCEL", // Hủy bỏ
}

export const ProjectStatusTrans = {
  [ProjectStatus.Draft]: {
    value: ProjectStatus.Draft,
    color: "--color-project-draft",
    label: "Nháp",
  },
  [ProjectStatus.New]: {
    value: ProjectStatus.New,
    color: "--color-project-planning",
    label: "Chưa bắt đầu",
  },
  [ProjectStatus.InProgress]: {
    value: ProjectStatus.InProgress,
    color: "--color-project-in-progress",
    label: "Đang thi công",
  },
  [ProjectStatus.Pause]: {
    value: ProjectStatus.Pause,
    color: "--color-project-pause",
    label: "Tạm dừng",
  },
  [ProjectStatus.Completed]: {
    value: ProjectStatus.Completed,
    color: "--color-project-done",
    label: "Hoàn thành",
  },
  [ProjectStatus.Cancel]: {
    value: ProjectStatus.Cancel,
    color: "--color-project-cancel",
    label: "Hủy bỏ",
  },
};
