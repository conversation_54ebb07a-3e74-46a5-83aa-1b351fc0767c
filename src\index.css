@import "https://cdn.syncfusion.com/ej2/30.1.37/bootstrap5.css";
@import "https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
    monospace;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 5px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #555;
}

@layer utilities {
  .custom-sider .ant-layout-sider-children {
    @apply dark:bg-[#141414];
  }
}
/* Dark mode styles */
.dark {
  @apply bg-gray-900 text-gray-100;
}

.dark .site-layout-background {
  background-color: #1f1f1f !important;
}

.dark .ant-layout {
  background-color: #141414;
}

.dark .ant-layout-sider {
  background-color: #1f1f1f;
}

.dark .ant-menu.ant-menu-dark,
.dark .ant-menu-dark .ant-menu-sub,
.dark .ant-menu.ant-menu-dark .ant-menu-sub {
  background-color: #1f1f1f;
}

.dark .ant-card {
  background-color: #1f1f1f;
  border-color: #303030;
}

.dark .ant-table {
  background-color: #1f1f1f;
  color: #f0f0f0;
}

.dark .ant-table-thead > tr > th {
  background-color: #141414;
  color: #f0f0f0;
  border-bottom: 1px solid #303030;
}

.dark .ant-table-tbody > tr > td {
  border-bottom: 1px solid #303030;
}

.dark .ant-input,
.dark .ant-select-selector,
.dark .ant-picker,
.dark .ant-input-number,
.dark .ant-input-affix-wrapper {
  background-color: #141414;
  border-color: #303030;
  color: #f0f0f0;
}

.dark .ant-btn:not(.ant-btn-primary):not(.ant-btn-link):not(.ant-btn-text) {
  background-color: #141414;
  border-color: #303030;
  color: #f0f0f0;
}
