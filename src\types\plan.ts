import { ApprovalHistory } from "./approvalHistory";
import { Dictionary } from "./dictionary";
import { FileAttach } from "./fileAttach";
import { Project } from "./project";
import { Staff } from "./staff";
import { Task } from "./task";

export interface Plan {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
//   type: PlanType;
//   status: PlanStatus; // tình trạng lưu
  work: string; // công việc nghiệm thu
  area: string; // khu vực nghiệm thu
  position: string; // "Vị trí nghiệm thu"
  object: string; //  "Đối tượng nghiệm thu"
  date: string; // ngày nghiệm thu
  pycntCode: string; // Số biên bản PYCNT
  ntcvxdCode: string; // "Số biên bản NTCVXD"
//   sign: PlanSignStatus;
  project: Project;
  taskCategory: Dictionary;
  task: Task;
  draw: FileAttach;
  fileAttaches: FileAttach[];
  // duyệt
  followStaffs: Staff[];
  approveStaffs: Staff[];
  approvalHistories: ApprovalHistory[];
  // info create update
  createdBy: Staff;
  updatedBy: Staff;
  // follow and approve
}
