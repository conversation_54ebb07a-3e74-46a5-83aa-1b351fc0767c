.project-info {
  display: flex;
  flex-direction: column;

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
  }

  .error-container {
    padding: 1.5rem;

    .back-button {
      margin-top: 1rem;
    }
  }

  .not-found {
    text-align: center;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;

    h3 {
      margin-bottom: 1rem;
    }
  }

  .header-info {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    margin-top: 0.25rem;
    align-items: start;

    .info-item {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: start;
      gap: 0.25rem;

      .info-content {
        display: flex;
        gap: 0.5rem;
        align-items: center;

        .icon-wrapper {
          width: 32px;
          height: 32px;
          background: #e4eced;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
}
