// components/Comment/CommentView.tsx
import React, { useEffect, useState, useRef, useMemo } from "react";
import {
  Card,
  Avatar,
  Input,
  Spin,
  UploadFile,
  Modal,
  Tag,
  Dropdown,
} from "antd";
import { $url } from "utils/url";
import { userStore } from "store/userStore";
import { CommentQuery, useComment } from "hooks/useComment";
import { Comment } from "types/comment";
import "./Comment.scss";
import {
  SendOutlined,
  CloseOutlined,
  EditOutlined,
  WechatOutlined,
  CheckOutlined,
  ClockCircleOutlined,
  DeleteOutlined,
  MoreOutlined,
} from "@ant-design/icons";
import { commentApi } from "api/comment.api";
import { formatDateTime } from "utils/date";
import CustomInput from "components/Input/CustomInput";
import CustomSelect from "components/Input/CustomSelect";
import { Pagination } from "components/Pagination";
import { isEmpty, isEqual } from "lodash";
import { CommentUploadMultiple } from "components/Upload/CommentUploadFile/CommentUploadMultiple";
import CustomButton from "components/Button/CustomButton";
import { LinkModuleCommentModal } from "components/Modal/LinkModuleCommentModal";
import DisplayFileUpload from "components/Comment/DisplayFileUpload";
import DisplayLinkModule from "components/Comment/DisplayLinkModule";
import { ApprovalList, ApprovalListStatus } from "types/approvalList";
import { StepType, StepTypeTrans } from "types/approvalStep";
import { ignore } from "antd/lib/theme/useToken";

const COMMENT_TYPES = [
  { value: "", label: "Tất cả bình luận" },
  { value: "comment", label: "Bình luận" },
  { value: "approval", label: "Trạng thái quy trình" },
  { value: "isConfirm", label: "Phản hồi RFIs" },
];

interface CommentViewProps {
  initQuery?: CommentQuery;
  refreshTrigger?: number; // Trigger to refresh data from outside
  commentConfirm?: Comment | null;
  handleCommentConfirm?: (
    rfiId: number,
    commentId: number
  ) => Promise<void> | undefined;
}

export const CommentView: React.FC<CommentViewProps> = ({
  initQuery,
  refreshTrigger,
  commentConfirm,
  handleCommentConfirm,
}) => {
  const [content, setContent] = useState("");
  const [commentType, setCommentType] = useState("");
  const { comments, total, fetchData, loading, setQuery, query } = useComment({
    initQuery: { ...initQuery, page: 1, limit: 5, typeFilter: commentType },
  });
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [replyTo, setReplyTo] = useState<Comment | null>(null);
  const [linkedModules, setLinkedModules] = useState<any[]>([]);
  const [selectedModule, setSelectedModule] = useState<string>("");
  const [selectedModuleId, setSelectedModuleId] = useState<number | null>(null);
  const [editingComment, setEditingComment] = useState<Comment | null>(null);
  const [editContent, setEditContent] = useState<string>("");
  const [editFileList, setEditFileList] = useState<UploadFile[]>([]);
  const [editLinkedModules, setEditLinkedModules] = useState<any[]>([]);
  const replyInputRef = useRef<any>(null);
  const editInputRef = useRef<any>(null);

  // Sắp xếp comments để hiển thị commentConfirm lên đầu
  const sortedComments = useMemo(() => {
    if (!commentConfirm) return comments;

    const _confirmedComment = comments.find(
      (comment) => comment.id === commentConfirm?.id
    );

    // Nếu không tìm thấy comment đã chốt trong danh sách, chỉ trả về comments gốc
    if (!_confirmedComment) return comments;

    return [
      _confirmedComment,
      ...comments.filter((comment) => comment.id !== _confirmedComment.id),
    ].filter(Boolean); // Lọc bỏ các giá trị undefined/null
  }, [comments, commentConfirm]);

  useEffect(() => {
    fetchData();
  }, [query]);

  // Focus vào input khi chế độ reply được kích hoạt
  useEffect(() => {
    if (replyTo && replyInputRef.current) {
      // Sử dụng setTimeout để đảm bảo DOM đã được render
      setTimeout(() => {
        // Tìm input bên trong div và focus vào nó
        const input = replyInputRef.current.querySelector("input, textarea");
        if (input) {
          input.focus();
        }
      }, 100);
    }
  }, [replyTo]);

  // Focus vào input khi chế độ edit được kích hoạt
  useEffect(() => {
    if (editingComment && editInputRef.current) {
      // Sử dụng setTimeout để đảm bảo DOM đã được render
      setTimeout(() => {
        // Tìm input bên trong div và focus vào nó
        const input = editInputRef.current.querySelector("input, textarea");
        if (input) {
          input.focus();
        }
      }, 100);
    }
  }, [editingComment]);

  // Refresh data when refreshTrigger changes
  useEffect(() => {
    if (refreshTrigger && refreshTrigger > 0) {
      fetchData();
    }
  }, [refreshTrigger]);

  const handleCreateComment = async () => {
    const comment = {
      ...initQuery,
      comment: {
        content: content,
        file: !!fileList?.length ? JSON.stringify(fileList) : "",
        module: selectedModule,
        moduleId: selectedModuleId || undefined,
      },
      parentId: replyTo?.id || null,
    };
    await commentApi.create(comment);
    setContent("");
    setFileList([]);
    setReplyTo(null);
    setSelectedModule("");
    setSelectedModuleId(null);
    setLinkedModules([]);
    fetchData();
  };

  const handleUpdateComment = async () => {
    if (!editingComment) return;

    await commentApi.update(editingComment.id, {
      comment: {
        content: editContent,
        file: !!editFileList?.length ? JSON.stringify(editFileList) : "",
        linkedModules: editLinkedModules,
      },
    });
    setEditingComment(null);
    setEditContent("");
    setEditFileList([]);
    setEditLinkedModules([]);
    fetchData();
  };

  const handleStartEdit = (comment: Comment) => {
    setEditingComment(comment);
    setEditContent(comment.content);

    // Load existing files
    const existingFiles: UploadFile[] = JSON.parse(comment?.file || "[]");
    setEditFileList(existingFiles);

    // Load existing linked modules
    const existingModules = comment.linkedModules || [];
    setEditLinkedModules(existingModules);
  };

  const handleCancelEdit = () => {
    setEditingComment(null);
    setEditContent("");
    setEditFileList([]);
    setEditLinkedModules([]);
  };

  const handleConfirm = (rfiId: number, commentId: number) => {
    Modal.confirm({
      title: "Bạn có muốn chốt phản hồi này không?",
      getContainer: () => {
        return document.getElementById("App") as HTMLElement;
      },
      icon: null,
      content: "",
      footer: (_, { OkBtn, CancelBtn }) => (
        <>
          <CustomButton
            onClick={() => {
              Modal.destroyAll();
            }}
            className="cta-button"
          >
            Huỷ
          </CustomButton>
          <CustomButton
            variant="outline"
            className="cta-button"
            onClick={async () => {
              await handleCommentConfirm?.(rfiId, commentId);
              fetchData();
              Modal.destroyAll();
            }}
          >
            Chốt phản hồi
          </CustomButton>
        </>
      ),
    });
  };

  const handleDelete = (id: number) => {
    Modal.confirm({
      title: "Bạn có chắc chắn muốn xóa bình luận này không?",
      getContainer: () => {
        return document.getElementById("App") as HTMLElement;
      },
      icon: null,
      content: "",
      footer: (_, { OkBtn, CancelBtn }) => (
        <>
          <CustomButton
            onClick={() => {
              Modal.destroyAll();
            }}
            className="cta-button"
          >
            Huỷ
          </CustomButton>
          <CustomButton
            variant="outline"
            className="cta-button"
            onClick={async () => {
              await commentApi.delete(id);
              fetchData();
              Modal.destroyAll();
            }}
          >
            Xoá
          </CustomButton>
        </>
      ),
    });
  };

  const _renderApprovalListCompact = (approvalList: ApprovalList) => {
    const getStatusColor = (status: ApprovalListStatus) => {
      switch (status) {
        case ApprovalListStatus.Approved:
          return "text-green-600 bg-green-100";
        case ApprovalListStatus.Rejected:
          return "text-red-600 bg-red-100";
        case ApprovalListStatus.Pending:
        default:
          return "text-orange-600 bg-orange-100";
      }
    };

    const getStatusText = (status: ApprovalListStatus) => {
      switch (status) {
        case ApprovalListStatus.Approved:
          return "Đã duyệt";
        case ApprovalListStatus.Rejected:
          return "Từ chối";
        case ApprovalListStatus.Pending:
        default:
          return "Chờ duyệt";
      }
    };

    return (
      <div className="flex items-center gap-2 mx-2">
        <span
          className={`px-2 py-0.5 rounded text-xs font-medium ${getStatusColor(
            approvalList.status
          )}`}
        >
          {getStatusText(approvalList.status)}
        </span>
        {/* <span className="text-xs text-gray-500">
          Quy trình: {StepTypeTrans[approvalList.name as StepType]}
        </span> */}
      </div>
    );
  };

  const _renderStatus = (status: ApprovalListStatus) => {
    const getStatusColor = (status: ApprovalListStatus) => {
      switch (status) {
        case ApprovalListStatus.Approved:
          return "text-green-600 bg-green-100";
        case ApprovalListStatus.Rejected:
          return "text-red-600 bg-red-100";
        case ApprovalListStatus.Pending:
        default:
          return "text-orange-600 bg-orange-100";
      }
    };
    const getStatusText = (status: ApprovalListStatus) => {
      switch (status) {
        case ApprovalListStatus.Approved:
          return "Đã duyệt";
        case ApprovalListStatus.Rejected:
          return "Từ chối";
        case ApprovalListStatus.Pending:
        default:
          return "Chờ duyệt";
      }
    };

    return (
      <div className="flex items-center gap-2 mx-2">
        <span
          className={`px-3 py-0.5 rounded text-[12px] font-medium ${getStatusColor(
            status
          )}`}
        >
          {getStatusText(status)}
        </span>
        {/* <span className="text-xs text-gray-500">
          Quy trình: {StepTypeTrans[approvalList.name as StepType]}
        </span> */}
      </div>
    );
  };

  const _renderCommentTypeSelect = () => {
    const availableCommentTypes = initQuery?.rfiId
      ? COMMENT_TYPES
      : COMMENT_TYPES.filter((type) => type.value !== "isConfirm");

    return (
      <div className="mb-4 flex-shrink-0 flex items-center justify-between">
        <CustomSelect
          value={commentType}
          onChange={(value) => {
            setCommentType(value);
            const newQuery = {
              ...query,
              typeFilter: value,
              page: 1,
            };
            setQuery(newQuery);
            fetchData(newQuery);
          }}
          options={availableCommentTypes}
          className="!w-[200px]"
          placeholder="Chọn loại bình luận"
          disabled={!!editingComment}
        />
        {/* <div className="text-sm w-[100px] text-neutral-n4">
          {total} bình luận
        </div> */}
      </div>
    );
  };

  const _renderStaffInput = (isReply: boolean = false) => {
    const canSendComment =
      content.trim() || fileList.length > 0 || linkedModules.length > 0;
    return (
      <div className={isReply ? "ml-10 mt-2" : ""}>
        <div className="flex items-start gap-x-[8px]">
          <Avatar
            size={44}
            src={
              userStore.info.avatar ? $url(userStore.info.avatar) : undefined
            }
            style={{ backgroundColor: "#1890ff", flexShrink: 0 }}
          >
            {userStore.info.fullName?.charAt(0)}
          </Avatar>
          <div className="flex-1" ref={isReply ? replyInputRef : undefined}>
            <CustomInput
              type="textarea"
              value={content}
              rows={3}
              onChange={setContent}
              placeholder={
                isReply
                  ? "Viết phản hồi, nhấn Shift + Enter để gửi phản hồi"
                  : "Viết bình luận, nhấn Shift + Enter để gửi bình luận"
              }
              onPressEnter={(value, isShiftKey) => {
                if (
                  isShiftKey &&
                  (content.trim() ||
                    fileList.length > 0 ||
                    linkedModules.length > 0)
                ) {
                  handleCreateComment();
                }
              }}
            />
          </div>
        </div>

        <div className="flex items-center justify-between mt-2">
          <div className="flex gap-4">
            <CommentUploadMultiple
              isCompact
              fileList={fileList}
              onUploadOk={setFileList}
            />
            <LinkModuleCommentModal
              linkedModules={linkedModules}
              onModuleChange={setLinkedModules}
              onSelectModule={(module, moduleId) => {
                setSelectedModule(module);
                setSelectedModuleId(moduleId);
              }}
            />
          </div>
          <div className="flex items-center gap-2">
            {isReply && (
              <div
                className="text-neutral-n4 hover:text-neutral-n6 cursor-pointer p-1 flex items-center gap-1"
                onClick={() => {
                  setReplyTo(null);
                  setContent("");
                  setFileList([]);
                }}
              >
                <CloseOutlined style={{ fontSize: "14px" }} />
                <span className="text-sm">Hủy</span>
              </div>
            )}
            <div
              className={`cursor-pointer p-1 flex items-center gap-1 ${
                !canSendComment
                  ? "text-neutral-n3"
                  : "text-primary hover:text-primary-dark"
              }`}
              onClick={() => {
                if (canSendComment) {
                  handleCreateComment();
                }
              }}
            >
              <SendOutlined style={{ fontSize: "14px" }} />
              <span className="text-sm">{isReply ? "Phản hồi" : "Gửi"}</span>
            </div>
          </div>
        </div>

        {/* Preview section */}
        <div className="">
          {fileList && fileList.length > 0 && (
            <div className="mb-4">
              <DisplayFileUpload
                fileList={fileList}
                fileProgress={{}}
                onDelete={(uid) => {
                  const newFileList = fileList.filter(
                    (file) => file.uid !== uid
                  );
                  setFileList(newFileList);
                }}
              />
            </div>
          )}

          {linkedModules && linkedModules.length > 0 && (
            <div>
              <DisplayLinkModule
                linkedModules={linkedModules}
                onDelete={(moduleId) => {
                  const newLinkedModules = linkedModules.filter(
                    (module) => module.id !== moduleId
                  );
                  setLinkedModules(newLinkedModules);
                  setSelectedModule("");
                  setSelectedModuleId(null);
                }}
              />
            </div>
          )}
        </div>
      </div>
    );
  };

  const getCommentActions = (comment: Comment) => {
    const items = [];

    if (
      comment.staff?.id === userStore.info.id &&
      comment.status === ("" as any)
    ) {
      items.push(
        {
          key: "edit",
          // icon: <EditOutlined />,
          label: "Chỉnh sửa",
          onClick: () => handleStartEdit(comment),
        },
        {
          key: "delete",
          // icon: <DeleteOutlined />,
          label: "Xóa",
          onClick: () => handleDelete(comment.id),
          // danger: true,
        }
      );
    }

    // if (!editingComment && comment.status === ("" as any)) {
    //   items.push({
    //     key: 'reply',
    //     icon: <WechatOutlined />,
    //     label: 'Trả lời',
    //     onClick: () => setReplyTo(comment)
    //   });
    // }

    return items;
  };

  const _renderComment = (item: Comment, index: number) => {
    // Kiểm tra item có tồn tại không
    if (!item) return null;

    const fileListOrigin: UploadFile[] = JSON.parse(item?.file || "[]");
    const isConfirmedComment = commentConfirm?.id === item.id;

    return (
      <>
        <div key={item.id}>
          <div
            className={`comment-item my-4 border-2 ${
              isConfirmedComment ? "border-green-500" : "border-gray-200"
            } rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 p-4 bg-white`}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-x-[4px] flex-wrap flex-1">
                <Avatar
                  size={32}
                  src={item.staff?.avatar ? $url(item.staff.avatar) : undefined}
                  style={{ backgroundColor: "#1890ff", flexShrink: 0 }}
                >
                  {item.staff?.fullName?.charAt(0)}
                </Avatar>
                <div className="staff-name">{item.staff?.fullName}</div>
                {item.status && _renderStatus(item.status)}
                <div className="flex items-center ml-2 text-[12px] text-neutral-n4">
                  <div className="time flex items-center gap-x-[4px]">
                    <ClockCircleOutlined />
                    {formatDateTime(item.createdAt)}
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-2">
                {initQuery?.rfiId && (
                  <div className="flex items-center gap-2">
                    {isConfirmedComment ? (
                      <CustomButton
                        className="cta-button"
                        loading={loading}
                        onClick={() => {}}
                        disabled={false}
                      >
                        Phản hồi đã chốt
                      </CustomButton>
                    ) : (
                      <CustomButton
                        className="cta-button"
                        loading={loading}
                        onClick={() => handleConfirm(initQuery?.rfiId, item.id)}
                        disabled={!!commentConfirm}
                      >
                        Chốt phản hồi
                      </CustomButton>
                    )}
                  </div>
                )}
                {getCommentActions(item).length > 0 && (
                  <Dropdown
                    menu={{ items: getCommentActions(item) }}
                    placement="bottomRight"
                    trigger={["click"]}
                    getPopupContainer={(trigger) =>
                      trigger.parentElement || document.body
                    }
                  >
                    <div className="cursor-pointer p-2 hover:bg-gray-100 rounded-full transition-colors">
                      <MoreOutlined style={{ fontSize: "16px" }} />
                    </div>
                  </Dropdown>
                )}
              </div>
            </div>

            <div className="ml-[44px]">
              {editingComment?.id === item.id ? (
                <div className="flex-1 mt-2 mb-2" ref={editInputRef}>
                  <CustomInput
                    type="textarea"
                    value={editContent}
                    rows={3}
                    onChange={setEditContent}
                    placeholder="Chỉnh sửa bình luận..."
                    onPressEnter={(value, isShiftKey) => {
                      if (
                        isShiftKey &&
                        (content.trim() ||
                          fileList.length > 0 ||
                          linkedModules.length > 0)
                      ) {
                        handleUpdateComment();
                      }
                    }}
                  />

                  {/* Upload and Link actions for edit mode */}
                  <div className="flex items-center justify-between mt-2">
                    <div className="flex gap-4">
                      <CommentUploadMultiple
                        isCompact
                        fileList={editFileList}
                        onUploadOk={setEditFileList}
                      />
                      <LinkModuleCommentModal
                        linkedModules={editLinkedModules}
                        onModuleChange={setEditLinkedModules}
                      />
                    </div>
                    <div className="flex items-center gap-2">
                      <div
                        className="text-neutral-n4 hover:text-neutral-n6 cursor-pointer p-1 flex items-center gap-1"
                        onClick={handleCancelEdit}
                      >
                        <CloseOutlined style={{ fontSize: "14px" }} />
                        <span className="text-sm">Hủy</span>
                      </div>
                      <div
                        className={`cursor-pointer p-1 flex items-center gap-1 ${
                          !editContent.trim()
                            ? "text-neutral-n3"
                            : "text-primary hover:text-primary-dark"
                        }`}
                        onClick={() => {
                          if (
                            editContent.trim() ||
                            editFileList.length > 0 ||
                            editLinkedModules.length > 0
                          ) {
                            handleUpdateComment();
                          }
                        }}
                      >
                        <CheckOutlined style={{ fontSize: "14px" }} />
                        <span className="text-sm">Lưu</span>
                      </div>
                    </div>
                  </div>

                  {/* Preview section for edit mode */}
                  <div className="mt-2">
                    {editFileList && editFileList.length > 0 && (
                      <div className="mb-4">
                        <DisplayFileUpload
                          fileList={editFileList}
                          fileProgress={{}}
                          onDelete={(uid) => {
                            const newFileList = editFileList.filter(
                              (file) => file.uid !== uid
                            );
                            setEditFileList(newFileList);
                          }}
                        />
                      </div>
                    )}

                    {editLinkedModules && editLinkedModules.length > 0 && (
                      <div>
                        <DisplayLinkModule
                          linkedModules={editLinkedModules}
                          onDelete={(moduleId) => {
                            const newLinkedModules = editLinkedModules.filter(
                              (module) => module.id !== moduleId
                            );
                            setEditLinkedModules(newLinkedModules);
                          }}
                        />
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="comment-content ml-10 whitespace-pre-line">
                  {item.content}
                </div>
              )}

              {fileListOrigin &&
                fileListOrigin.length > 0 &&
                !editingComment && (
                  <DisplayFileUpload
                    fileList={fileListOrigin}
                    fileProgress={{}}
                  />
                )}

              {item.linkedModules &&
                item.linkedModules.length > 0 &&
                !editingComment && (
                  <DisplayLinkModule linkedModules={item.linkedModules} />
                )}

              {/* Hiển thị module từ comment.module */}
              {item.module && item.moduleId && !editingComment && (
                <DisplayLinkModule
                  linkedModules={[
                    {
                      id: item.moduleId.toString(),
                      type: "module",
                      code: item.module,
                      name: item.module,
                    },
                  ]}
                />
              )}
            </div>

            <div className="flex items-center justify-end gap-x-[4px] text-sm text-neutral-n4 mt-3">
              {!editingComment && item.status === ("" as any) && (
                <div
                  className="cursor-pointer hover:text-blue-500 flex items-center gap-1 px-2 py-1 rounded-md hover:bg-blue-50 transition-colors"
                  onClick={() => setReplyTo(item)}
                >
                  {/* <WechatOutlined /> */}
                  <span>Trả lời</span>
                </div>
              )}
            </div>
          </div>
          {replyTo?.id === item.id &&
            !editingComment &&
            _renderStaffInput(true)}
        </div>
        {item.children?.map((child) => {
          // Kiểm tra child có tồn tại không
          if (!child) return null;

          const childFileList: UploadFile[] = JSON.parse(child?.file || "[]");
          const isChildConfirmed = commentConfirm?.id === child.id;

          return (
            <div key={child.id} className="ml-10">
              <div
                className={`comment-item mb-4 border-2 ${
                  isChildConfirmed
                    ? "border-green-500 border-l-4"
                    : "border-blue-100 border-l-4 border-l-blue-400"
                } rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 p-4 ${
                  isChildConfirmed ? "bg-green-50/30" : "bg-blue-50/30"
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-x-[4px] flex-wrap flex-1">
                    <Avatar
                      size={32}
                      src={
                        child.staff?.avatar
                          ? $url(child.staff.avatar)
                          : undefined
                      }
                      style={{ backgroundColor: "#1890ff", flexShrink: 0 }}
                    >
                      {child.staff?.fullName?.charAt(0)}
                    </Avatar>
                    <div className="staff-name">{child.staff?.fullName}</div>
                    {child.approvalList &&
                      _renderApprovalListCompact(child.approvalList)}
                    <div className="flex items-center ml-2 text-sm text-neutral-n4">
                      <div className="time flex items-center gap-x-[4px]">
                        <ClockCircleOutlined />
                        {formatDateTime(child.createdAt)}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    {initQuery?.rfiId && (
                      <div className="flex items-center gap-2">
                        {isChildConfirmed ? (
                          <Tag color="success">Phản hồi đã chốt</Tag>
                        ) : (
                          <CustomButton
                            className="cta-button"
                            loading={loading}
                            onClick={() =>
                              handleConfirm(initQuery?.rfiId, child.id)
                            }
                            disabled={!!commentConfirm}
                          >
                            Chốt phản hồi
                          </CustomButton>
                        )}
                      </div>
                    )}
                    {getCommentActions(child).length > 0 && (
                      <Dropdown
                        menu={{ items: getCommentActions(child) }}
                        placement="bottomRight"
                        trigger={["click"]}
                        getPopupContainer={(trigger) =>
                          trigger.parentElement || document.body
                        }
                      >
                        <div className="cursor-pointer p-2 hover:bg-gray-100 rounded-full transition-colors">
                          <MoreOutlined style={{ fontSize: "16px" }} />
                        </div>
                      </Dropdown>
                    )}
                  </div>
                </div>

                <div className="ml-[44px]">
                  {editingComment?.id === child.id ? (
                    <div className="flex-1 mt-2 mb-2" ref={editInputRef}>
                      <CustomInput
                        type="textarea"
                        value={editContent}
                        rows={3}
                        onChange={setEditContent}
                        placeholder="Chỉnh sửa phản hồi..."
                        onPressEnter={(value, isShiftKey) => {
                          if (
                            isShiftKey &&
                            (editContent.trim() ||
                              editFileList.length > 0 ||
                              editLinkedModules.length > 0)
                          ) {
                            handleUpdateComment();
                          }
                        }}
                      />

                      {/* Upload and Link actions for edit mode */}
                      <div className="flex items-center justify-between mt-2">
                        <div className="flex gap-4">
                          <CommentUploadMultiple
                            isCompact
                            fileList={editFileList}
                            onUploadOk={setEditFileList}
                          />
                          <LinkModuleCommentModal
                            linkedModules={editLinkedModules}
                            onModuleChange={setEditLinkedModules}
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <div
                            className="text-neutral-n4 hover:text-neutral-n6 cursor-pointer p-1 flex items-center gap-1"
                            onClick={handleCancelEdit}
                          >
                            <CloseOutlined style={{ fontSize: "14px" }} />
                            <span className="text-sm">Hủy</span>
                          </div>
                          <div
                            className={`cursor-pointer p-1 flex items-center gap-1 ${
                              !editContent.trim()
                                ? "text-neutral-n3"
                                : "text-primary hover:text-primary-dark"
                            }`}
                            onClick={() => {
                              if (
                                editContent.trim() ||
                                editFileList.length > 0 ||
                                editLinkedModules.length > 0
                              ) {
                                handleUpdateComment();
                              }
                            }}
                          >
                            <CheckOutlined style={{ fontSize: "14px" }} />
                            <span className="text-sm">Lưu</span>
                          </div>
                        </div>
                      </div>

                      {/* Preview section for edit mode */}
                      <div className="mt-2">
                        {editFileList && editFileList.length > 0 && (
                          <div className="mb-4">
                            <DisplayFileUpload
                              fileList={editFileList}
                              fileProgress={{}}
                              onDelete={(uid) => {
                                const newFileList = editFileList.filter(
                                  (file) => file.uid !== uid
                                );
                                setEditFileList(newFileList);
                              }}
                            />
                          </div>
                        )}

                        {editLinkedModules && editLinkedModules.length > 0 && (
                          <div>
                            <DisplayLinkModule
                              linkedModules={editLinkedModules}
                              onDelete={(moduleId) => {
                                const newLinkedModules =
                                  editLinkedModules.filter(
                                    (module) => module.id !== moduleId
                                  );
                                setEditLinkedModules(newLinkedModules);
                              }}
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  ) : (
                    <div className="comment-content whitespace-pre-line">
                      {child.content}
                    </div>
                  )}

                  {childFileList &&
                    childFileList.length > 0 &&
                    !editingComment && (
                      <DisplayFileUpload
                        fileList={childFileList}
                        fileProgress={{}}
                      />
                    )}

                  {/* {child.linkedModules && child.linkedModules.length > 0 && (
                  <DisplayLinkModule linkedModules={child.linkedModules} />
                )} */}

                  {/* Hiển thị module từ comment.module cho child comments */}
                  {child.module && child.moduleId && !editingComment && (
                    <DisplayLinkModule
                      linkedModules={[
                        {
                          id: child.moduleId.toString(),
                          type: "module",
                          code: child.module,
                          name: child.module,
                        },
                      ]}
                    />
                  )}
                </div>

                {/* <div className="flex items-center justify-end gap-x-[4px] text-sm text-neutral-n4 mt-3">
                  {!editingComment && child.status === ("" as any) && (
                    <div
                      className="cursor-pointer hover:text-blue-500 flex items-center gap-1 px-2 py-1 rounded-md hover:bg-blue-50 transition-colors"
                      onClick={() => setReplyTo(child)}
                    >
                      <WechatOutlined />
                      <span>Trả lời</span>
                    </div>
                  )}
                </div> */}
              </div>
            </div>
          );
        })}
      </>
    );
  };

  return (
    <div className="comment-card">
      {_renderCommentTypeSelect()}
      {!replyTo && !editingComment && _renderStaffInput()}
      <Spin spinning={loading}>
        <div className="comment-list">
          {sortedComments.filter(Boolean).map(_renderComment)}

          <Pagination
            currentPage={query.page!}
            defaultPageSize={query.limit}
            total={total}
            onChange={({ limit, page }) => {
              if (editingComment) return; // Prevent pagination change during edit
              query.page = page;
              query.limit = limit;
              setQuery({ ...query });
              fetchData();
            }}
          />
        </div>
      </Spin>
    </div>
  );
};
