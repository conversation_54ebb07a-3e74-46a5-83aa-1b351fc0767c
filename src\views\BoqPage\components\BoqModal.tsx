import { Col, Form, Input, message, Modal, Row, Select } from "antd";
import { Rule } from "antd/lib/form";
import { boqApi } from "api/boq.api";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { ModalStatus } from "types/modal";
import { BOQ } from "types/boq";
import BoqTable from "../BoqTable";
import { formatFullDateTime } from "utils/date";
import { set } from "lodash";

const rules: Rule[] = [{ required: true }];

export interface BoqModal {
  handleOpen: (selectedBoq?: BOQ, paramsId?: string | undefined) => void;
  handleUpdate: (boq: BOQ) => void;
}
interface BoqModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
}

export const BoqModal = React.forwardRef(
  ({ onClose, onSubmitOk }: BoqModalProps, ref) => {
    const [form] = Form.useForm<BOQ>();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");
    const [selectedBoq, setSelectedBoq] = useState<BOQ>();
    const [selectedVersion, setSelectedVersion] = useState<number>(
      selectedBoq?.version || 1
    );
    const [versionLength, setVersionLength] = useState<number>(
      selectedBoq?.version || 1
    );
    const [loadingFetch, setLoadingFetch] = useState(false);
    const [boqDetails, setBoqDetails] = useState<BOQ["boqDetails"]>([]);
    const [boqId, setBoqId] = useState<number>(0);

    useImperativeHandle<any, BoqModal>(
      ref,
      () => ({
        handleOpen(selectedBoq?: BOQ, paramsId?: string | undefined) {
          form.resetFields();
          setVisible(true);
          setStatus("create");
          setSelectedBoq(selectedBoq);
          setBoqId(paramsId ? parseInt(paramsId) : 0);
          // Thêm cập nhật version khi mở modal
          if (selectedBoq?.version) {
            setSelectedVersion(selectedBoq.version);
            setVersionLength(selectedBoq.version);
          }
        },
        handleUpdate(boq: BOQ) {
          //   form.setFieldsValue({ ...boq });
          setVisible(true);
          setStatus("update");
          setSelectedBoq(boq);
          if (boq?.version) {
            setSelectedVersion(boq.version);
            setVersionLength(boq.version);
          }
        },
      }),
      []
    );

    // Sửa lại useEffect để theo dõi selectedBoq
    useEffect(() => {
      if (selectedBoq?.id) {
        setBoqId(selectedBoq.id);
        getOneBoq(selectedBoq.id, selectedVersion);
      }
    }, [selectedVersion, selectedBoq?.id]);

    const getOneBoq = async (id: number, version: number) => {
      try {
        setLoadingFetch(true);
        const { data } = await boqApi.findBoqDetail({
          boqId,
          version,
        });
        setBoqDetails(data.boqDetails || []);

        return data as BOQ;
      } catch (e: any) {
        message.error("Có lỗi xảy ra khi tải dữ liệu");
      } finally {
        setLoadingFetch(false);
      }
    };

    const handleVersionChange = (value: number) => {
      setSelectedVersion(value);
      getOneBoq(boqId, value);
    };

    const getPayload = () => {
      const { ...rest } = form.getFieldsValue();
      return { boq: rest };
    };

    const submitForm = async () => {
      try {
        setLoading(true);
        const valid = await form.validateFields();
        const data = getPayload();
        let res: any = undefined;
        switch (status) {
          case "create":
            res = await boqApi.create(data);
            message.success("Create Boq successfully!");
            break;
          case "update":
            res = await boqApi.update(selectedBoq?.id || 0, data);
            message.success("Update Boq successfully!");
            break;
        }
        onSubmitOk();
        handleClose();
      } finally {
        setLoading(false);
      }
    };

    const handleClose = () => {
      onClose?.();
      setVisible(false);
      setSelectedBoq(undefined);
    };

    return (
      <Modal
        onCancel={() => {
          handleClose();
        }}
        visible={visible}
        title={status == "create" ? "Xem phiên bản" : "Update Boq"}
        style={{ top: 20 }}
        width={"100%"}
        confirmLoading={loading}
        onOk={submitForm}
        cancelText="Đóng"
        okButtonProps={{
          className: "hidden",
        }}
      >
        <Form layout="vertical" form={form}>
          <Row gutter={16}>
            <Col span={3}>
              <Form.Item label="Phiên bản" name="version">
                <Select
                  placeholder="Chọn version"
                  options={Array.from(
                    { length: versionLength },
                    (_, index) => ({
                      label: `Phiên bản ${versionLength - index}`,
                      value: versionLength - index,
                    })
                  )}
                  onChange={handleVersionChange}
                  style={{ width: "100%" }}
                  value={selectedVersion}
                  defaultValue={selectedVersion}
                />
              </Form.Item>
            </Col>
            <Col span={4}>
              {/* <Form.Item label="Thời gian thay đổi" name="version">
                <Select
                  placeholder="Chọn version"
                  options={Array.from(
                    { length: versionLength },
                    (_, index) => ({
                      label: `Phiên bản ${versionLength - index}`,
                      value: versionLength - index,
                    })
                  )}
                  onChange={handleVersionChange}
                  style={{ width: "100%" }}
                  value={selectedVersion}
                  defaultValue={selectedVersion}
                />
              </Form.Item> */}
              <div style={{ marginBottom: 12 }}>
                <span className="font-bold">Thời gian thay đổi</span>
              </div>
              <span>{formatFullDateTime(boqDetails[0]?.updatedAt)}</span>
            </Col>

            <Col span={24}>
              <BoqTable
                boqDetails={boqDetails || []}
                version={selectedVersion}
                setBoqDetails={setBoqDetails}
              />
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
);
