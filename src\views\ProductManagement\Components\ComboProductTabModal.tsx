import { Col, Form, Input, message, Modal, Row } from "antd";
import { Rule } from "antd/lib/form";
import { productApi } from "api/product.api";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { ModalStatus } from "types/modal";
import { Product } from "types/product";

const rules: Rule[] = [{ required: true }];

export interface ComboProductTabModal {
  handleCreate: () => void;
  handleUpdate: (ComboProductTab: Product) => void;
}
interface ComboProductTabModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
}

export const ComboProductTabModal = React.forwardRef(
  ({ onClose, onSubmitOk }: ComboProductTabModalProps, ref) => {
    const [form] = Form.useForm<Product>();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");

    useImperativeHandle<any, ComboProductTabModal>(
      ref,
      () => ({
        handleCreate() {
          form.resetFields();
          setVisible(true);
          setStatus("create");
        },
        handleUpdate(ComboProductTab) {
          form.setFieldsValue({ ...ComboProductTab });
          setVisible(true);
          setStatus("update");
        },
      }),
      []
    );

    const createData = async () => {
      const valid = await form.validateFields();
      const data = { ComboProductTab: form.getFieldsValue() };

      setLoading(true);
      try {
        const res = await productApi.create(data);
        message.success("Create ComboProductTab successfully!");
        onClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const valid = await form.validateFields();
      const data = { ComboProductTab: form.getFieldsValue() };
      setLoading(true);
      try {
        const res = await productApi.update(data.ComboProductTab.id || 0, data);
        message.success("Update ComboProductTab successfully!");
        onClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    return (
      <Modal
        onCancel={() => {
          onClose?.();
          setVisible(false);
        }}
        visible={visible}
        title={
          status == "create"
            ? "Create ComboProductTab"
            : "Update ComboProductTab"
        }
        style={{ top: 20 }}
        width={700}
        confirmLoading={loading}
        onOk={() => {
          status == "create" ? createData() : updateData();
        }}
      >
        <Form layout="vertical" form={form}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="Username" name="username" rules={rules}>
                <Input placeholder="" />
              </Form.Item>
            </Col>

            {status == "create" && (
              <Col span={12}>
                <Form.Item label="Password" name="password" rules={rules}>
                  <Input placeholder="" />
                </Form.Item>
              </Col>
            )}

            <Col span={12}>
              <Form.Item label="Name" name="name" rules={rules}>
                <Input placeholder="" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item label="Phone" name="phone" rules={rules}>
                <Input placeholder="" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item label="Email" name="email">
                <Input placeholder="" />
              </Form.Item>
            </Col>
          </Row>

          {/* <Form.Item shouldUpdate={true}>
            {() => {
              return (
                <Form.Item label="Avatar" name="avatar">
                  <SingleImageUpload
                    onUploadOk={(path: string) => {
                      console.log("onUploadOk:", path);
                      form.setFieldsValue({
                        avatar: path,
                      });
                    }}
                    imageUrl={form.getFieldValue("avatar")}
                  />
                </Form.Item>
              );
            }}
          </Form.Item> */}
        </Form>
      </Modal>
    );
  }
);
