import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const componentApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/component",
      params,
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/component/${id}`,
      method: "get",
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/component",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/component/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/component/${id}`,
      method: "delete",
    }),
  deleteMany: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/component/batch`,
      method: "delete",
      data,
    }),
  import: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/component/import`,
      method: "post",
      data,
    }),
  block: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/component/${id}/block`,
      method: "patch",
      data,
    }),
};
