import React from "react";
import { Image, Empty } from "antd";

interface ImageItem {
  id: string;
  src: string;
  alt?: string;
}

interface DisplayImageProps {
  images: ImageItem[];
  onImageClick?: (image: ImageItem, index: number) => void;
}

const DisplayImage: React.FC<DisplayImageProps> = ({
  images,
  onImageClick,
}) => {
  if (!images || images.length === 0) {
    return (
      <div
        className="display-image__empty"
        style={{
          width: "100%",
          height: "100%",
          backgroundColor: "var(--color-neutral-n1)",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Empty
          description="Không có hình ảnh"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          style={{
            color: "var(--color-neutral-n4)",
            fontSize: "12px",
          }}
        />
      </div>
    );
  }

  const handleImageClick = (image: ImageItem, index: number) => {
    if (onImageClick) {
      onImageClick(image, index);
    }
  };

  const imageStyle = {
    width: "100%",
    height: "100%",
    objectFit: "cover" as const,
    cursor: onImageClick ? "pointer" : "default",
  };

  const containerStyle = {
    width: "100%",
    height: "100%",
    overflow: "hidden",
  };

  // Single image
  if (images.length === 1) {
    return (
      <div className="display-image__single" style={containerStyle}>
        <Image
          src={images[0].src}
          alt={images[0].alt || `Image 1`}
          style={imageStyle}
          preview={{
            mask: false,
          }}
          onClick={() => handleImageClick(images[0], 0)}
        />
      </div>
    );
  }

  // Two images
  if (images.length === 2) {
    return (
      <div
        className="display-image__double"
        style={{
          ...containerStyle,
          display: "flex",
          gap: "2px",
        }}
      >
        {images.slice(0, 2).map((image, index) => (
          <div key={image.id} style={{ flex: 1, height: "100%", overflow: "hidden" }}>
            <Image
              src={image.src}
              alt={image.alt || `Image ${index + 1}`}
              style={imageStyle}
              preview={{
                mask: false,
              }}
              onClick={() => handleImageClick(image, index)}
            />
          </div>
        ))}
      </div>
    );
  }

  // Three images
  if (images.length === 3) {
    return (
      <div
        className="display-image__triple"
        style={{
          ...containerStyle,
          display: "flex",
          gap: "2px",
        }}
      >
        {/* Main image (left) */}
        <div style={{ flex: 1, height: "100%", overflow: "hidden" }}>
          <Image
            src={images[0].src}
            alt={images[0].alt || "Image 1"}
            style={imageStyle}
            preview={{
              mask: false,
            }}
            onClick={() => handleImageClick(images[0], 0)}
          />
        </div>

        {/* Side images (right) */}
        <div
          style={{
            flex: 1,
            height: "100%",
            display: "flex",
            flexDirection: "column",
            gap: "2px",
          }}
        >
          {images.slice(1, 3).map((image, index) => (
            <div key={image.id} style={{ flex: 1, height: "50%", overflow: "hidden" }}>
              <Image
                src={image.src}
                alt={image.alt || `Image ${index + 2}`}
                style={imageStyle}
                preview={{
                  mask: false,
                }}
                onClick={() => handleImageClick(image, index + 1)}
              />
            </div>
          ))}
        </div>
      </div>
    );
  }

  // More than 3 images
  const remainingCount = images.length - 3;

  return (
    <Image.PreviewGroup>
      <div
        className="display-image__multiple"
        style={{
          ...containerStyle,
          display: "flex",
          gap: "2px",
        }}
      >
        {/* Main image (left) */}
        <div style={{ flex: 1, height: "100%", overflow: "hidden" }}>
          <Image
            src={images[0].src}
            alt={images[0].alt || "Image 1"}
            style={imageStyle}
            preview={{
              mask: false,
            }}
            onClick={() => handleImageClick(images[0], 0)}
          />
        </div>

        {/* Side images (right) */}
        <div
          style={{
            flex: 1,
            height: "100%",
            display: "flex",
            flexDirection: "column",
            gap: "2px",
          }}
        >
          {/* First side image */}
          <div style={{ flex: 1, height: "50%", overflow: "hidden" }}>
            <Image
              src={images[1].src}
              alt={images[1].alt || "Image 2"}
              style={imageStyle}
              preview={{
                mask: false,
              }}
              onClick={() => handleImageClick(images[1], 1)}
            />
          </div>

          {/* Second side image with overlay */}
          <div style={{ flex: 1, height: "50%", position: "relative", overflow: "hidden" }}>
            <Image
              src={images[2].src}
              alt={images[2].alt || "Image 3"}
              style={imageStyle}
              preview={{
                mask: false,
              }}
              onClick={() => handleImageClick(images[2], 2)}
            />

            {/* Hidden images for preview group */}
            {images.slice(3).map((image) => (
              <Image
                key={image.id}
                src={image.src}
                alt={image.alt}
                style={{ display: "none" }}
                preview={{
                  mask: false,
                }}
              />
            ))}

            {/* Overlay with count */}
            <div
              style={{
                position: "absolute",
                inset: 0,
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                color: "white",
                fontSize: "20px",
                fontWeight: "bold",
                cursor: "pointer",
                zIndex: 1,
              }}
              onClick={() => handleImageClick(images[2], 2)}
            >
              +{remainingCount}
            </div>
          </div>
        </div>
      </div>
    </Image.PreviewGroup>
  );
};

export default DisplayImage;
