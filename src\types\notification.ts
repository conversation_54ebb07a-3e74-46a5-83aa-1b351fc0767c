import { ChangeEvent } from "react";
import { MemberShip } from "./memberShip";
import { RFI } from "./rfi";
import { Staff } from "./staff";
import { Project } from "./project";

export enum NotificationType {
  Membership = "MEMBERSHIP",
}

export enum NotificationScope {
  Public = "PUBLIC",
  Private = "PRIVATE",
}

export interface ReadNotification {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  notification: Notification;
  staff: Staff;
}

export interface Notification {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  title: string;
  shortContent: string;
  content: string;
  url: string;
  type: NotificationType;
  scope: NotificationScope;
  project: Project;
  // custom
  staff?: Staff;
  memberShip?: MemberShip;
  rfi?: RFI;
  changeEvent?: ChangeEvent;
  readNotifications: ReadNotification[];
  isRead?: boolean;
}
