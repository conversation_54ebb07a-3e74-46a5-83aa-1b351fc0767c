import { FileAttach } from "./fileAttach";
import { Project } from "./project";
import { Staff } from "./staff";
import { Task } from "./task";
import { ApprovalHistory } from "./approvalHistory";
import { Dictionary } from "./dictionary";

export interface Punch {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  name: string;
  dateAt: number; // Ngày báo cáo
  location: string; // vị trí
  description: string;
  cause: string;
  solution: string;
//   priority: PunchPriority;
  duaAt: number; // ngày đến hạn
//   status: PunchStatus;
  note: string;
  impact: string; // Đ<PERSON>h giá mức độ ảnh hưởng của lỗi đến dự án, bao gồm ảnh hưởng về tiến độ (Schedule Impact), chi ph<PERSON> (Cost Impact), hoặc chất lượng (Quality Impact). Điều này giúp ưu tiên xử lý các lỗi có tác động lớn. Ví dụ: "Chậm tiến độ 2 ngày", "Tăng chi phí 5 triệu VNĐ".
  verificationAt: number;
  cost: number;
  recurrence: boolean;
  project: Project;
  images: FileAttach[]; // image báo cáo
  imageAfters: FileAttach[]; // image sau xử lý
  punchCategory: Dictionary;
  reporter: Staff;
  assignee: Staff;
  task: Task;
  followStaffs: Staff[];
  approveStaffs: Staff[];
  ApprovalHistories: ApprovalHistory[];
  // info create update
  createdBy: Staff;
  updatedBy: Staff;
}
