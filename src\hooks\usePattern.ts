import { patternApi } from "api/pattern.api";
import { useState } from "react";
import { Pattern } from "types/pattern";
import { QueryParam } from "types/query";

export interface PatternQuery extends QueryParam {}

interface UsePatternProps {
  initQuery: PatternQuery;
}

export const usePattern = ({ initQuery }: UsePatternProps) => {
  const [data, setData] = useState<Pattern[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<PatternQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await patternApi.findAll(query);

      setData(data.patterns);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return { patterns: data, total, fetchData, loading, setQuery, query };
};
