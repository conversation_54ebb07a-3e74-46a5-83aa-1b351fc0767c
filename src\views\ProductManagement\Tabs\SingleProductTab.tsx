import {
  CheckOutlined,
  CloseOutlined,
  DownOutlined,
  ExportOutlined,
  ImportOutlined,
  LockOutlined,
  PlusOutlined,
  SearchOutlined,
  UnlockOutlined,
} from "@ant-design/icons";
import {
  Button,
  Checkbox,
  Input,
  message,
  Popconfirm,
  Space,
  Spin,
  Table,
  Tabs,
  TabsProps,
  Tag,
} from "antd";
import Column from "antd/es/table/Column";
import { Pagination } from "components/Pagination";
import { useComponent } from "hooks/useComponent";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  Component,
  ComponentShowTypeTrans,
  RelationComponent,
} from "types/component";
import { formatVND } from "utils";
import { $url } from "utils/url";

import { componentApi } from "api/component.api";
import { handleExport, MyExcelColumn } from "utils/MyExcel";
import ImportSettingComponent, {
  ImportSettingComponentModal,
} from "components/ImportDocument/ImportSettingComponent";
import { removeSubstringFromKeys } from "utils/common";
import DropdownCell from "components/Table/DropdownCell";
import {
  ComponentModal,
  CreateProductModal,
} from "../Components/CreateProductModal";
import {
  Product,
  ProductStatus,
  ProductType,
  ProductTypeTrans,
} from "types/product";
import { productApi } from "api/product.api";
import { useProduct } from "hooks/useProduct";
import { debounce } from "lodash";
interface SingleProductTabProps {
  isChooseMode?: boolean;
  title?: string;
  onHandleChooseSingleProduct?: (product: Product[]) => void;
  singleProducts?: Product[];
}
const exportColumns: MyExcelColumn<Component>[] = [
  {
    header: "Nhóm cha",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "nameGroup",
    columnKey: "nameGroup",
    render: (record) => record?.name,
  },
  {
    header: "TP mặc định của nhóm",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "isDefault",
    columnKey: "isDefault",
    render: (record) => (record?.isDefault ? "Phải" : "Không"),
  },
  {
    header: "Mã nhóm",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "code",
    columnKey: "code",
  },
  {
    header: "Tên",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "name",
    columnKey: "name",
  },
  {
    header: "Nhóm điều kiện",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "depComponents",
    columnKey: "depComponents",
    render: (record) =>
      record.depComponents
        ?.map((item: any) => item.component2?.name)
        .join(", "),
  },
  {
    header: "Nhóm loai trừ",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "excludeComponents",
    columnKey: "excludeComponents",
    render: (record) =>
      record.excludeComponents
        ?.map((item: any) => item.component2?.name)
        .join(", "),
  },
  {
    header: "Tên nội bộ",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "privateName",
    columnKey: "privateName",
  },
  {
    header: "Mô tả",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "desc",
    columnKey: "desc",
  },
  {
    header: "Truy xuất thông tin mô tả",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "featureImageShowType",
    columnKey: "featureImageShowType",
    render: (record: Component) =>
      ComponentShowTypeTrans[record?.featureImageShowType]?.label || "-",
  },
  {
    header: "Giá cộng thêm ($)",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "extraPrice",
    columnKey: "extraPrice",
    render: (record) => formatVND(record.extraPrice),
  },
  {
    header: "Định mức",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "standard",
    columnKey: "standard",
  },

  {
    header: "Thêu tên",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "isEmbroidery",
    columnKey: "isEmbroidery",
    render: (record) => (record.isEmbroidery == true ? "Có" : "Không"),
  },
  {
    header: "Số ký tự tối đa",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "maxLength",
    columnKey: "maxLength",
  },
  {
    header: "Trạng thái",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "isBlocked",
    columnKey: "isBlocked",
    render: (record) => (record.isBlocked == true ? "Khóa" : "Mở"),
  },
  // {
  //   header: "Hình mặt trước",
  //   headingStyle: {
  //     font: {
  //       bold: true,
  //     },
  //   },
  //   key: "avatar",
  //   columnKey: "avatar",
  //   render(record) {
  //     return $url(record.fileAttachAvatar?.url);
  //   },
  // },
  // {
  //   header: "Hình mặt sau",
  //   headingStyle: {
  //     font: {
  //       bold: true,
  //     },
  //   },
  //   key: "avatarBack",
  //   columnKey: "avatarBack",
  //   render(record) {
  //     return $url(record.fileAttachAvatarBack?.url);
  //   },
  // },

  {
    header: "Hình feature",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "featureImage",
    columnKey: "featureImage",
    render(record) {
      return $url(record.fileAttachFeatureImage?.url);
    },
  },
];

export const SingleProductTab = ({
  title = "",
  isChooseMode = false,
  onHandleChooseSingleProduct,
  singleProducts,
}: SingleProductTabProps) => {
  const [loadingDelete, setLoadingDelete] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<Product[]>([]);
  const lastFilterChanged = useRef<"name" | "code" | undefined>();
  const { products, fetchData, query, loading, setQuery, total } = useProduct({
    initQuery: {
      page: 1,
      limit: 10,
      search: "",
      type: ProductType.Single,
    },
  });

  useEffect(() => {
    if (singleProducts) {
      setSelectedRowKeys(singleProducts.map((it) => it.id));
      setSelectedProducts(singleProducts);
    }
  }, [singleProducts]);

  useEffect(() => {
    fetchData();
  }, [query]);
  const modalRef = React.useRef<ComponentModal>(null);
  const tagColors = [
    "magenta",
    "volcano",
    "orange",
    "gold",
    "lime",
    "green",
    "cyan",
    "blue",
    "geekblue",
    "purple",
  ];

  const getColor = (index: number) => {
    return tagColors[index % tagColors.length];
  };
  const handleDelete = async (id: number) => {
    try {
      setLoadingDelete(true);
      await productApi.delete(id);
      message.success("Xóa thành công");
      fetchData();
    } catch (e) {
      console.log({ e });
    } finally {
      setLoadingDelete(false);
    }
  };
  const importModal = useRef<ImportSettingComponentModal>();
  const handleOnUploadedFile = async (excelData: any, setData: any) => {
    const { results } = excelData;

    console.log("results", results);

    const importData = results?.map((item: any) => {
      const refineRow = removeSubstringFromKeys(item, " (*)");

      const name = refineRow["TÊN"] || "";
      const isDefault = refineRow["TP MẶC ĐỊNH CỦA NHÓM"] || "";
      const privateName = refineRow["TÊN NỘI BỘ"] || "";
      const code = refineRow["MÃ NHÓM"] || "";
      const avatar = refineRow["HÌNH MẶT TRƯỚC"] || "";
      const avatarBack = refineRow["HÌNH MẶT SAU"] || "";
      const featureImage = refineRow["HÌNH FEATURE"] || "";
      const desc = refineRow["MÔ TẢ"] || "";
      const extraPrice = refineRow["GIÁ CỘNG THÊM"] || 0;
      const isEmbroidery = refineRow["THÊU TÊN"] || false;
      const maxLength = refineRow["SỐ KÝ TỰ TỐI ĐA"] || 0;
      const componentDepCodes = refineRow["NHÓM ĐIỀU KIỆN"] || "";
      const componentExcludeCodes = refineRow["NHÓM LOẠI TRỪ"] || "";
      // const parentCode = refineRow["NHÓM CHA"] || "";
      // const note = refineRow["GHI CHÚ TIẾNG VIỆT (DÀNH CHO THỢ MAY)"] || "";
      const featureImageShowType = refineRow["TRUY XUẤT THÔNG TIN MÔ TẢ"];

      const standard = refineRow["ĐỊNH MỨC"] || "";

      return {
        name,
        privateName,
        code,
        avatar,
        avatarBack,
        featureImage,
        isDefault,
        desc,
        extraPrice,
        isEmbroidery,
        maxLength,
        componentDepCodes,
        componentExcludeCodes,
        // parentCode: parentCode,
        featureImageShowType,
        standard,
        rowNum: item.__rowNum__,
      };
    });
    console.log("importData", importData);

    setData(importData);
  };
  const blockProduct = async (product: Product) => {
    if (product.status == ProductStatus.Active) {
      await productApi.inactive(product.id);
    } else {
      await productApi.active(product.id);
    }
    message.success(
      `${
        product.status == ProductStatus.Inactive ? "Hiện" : "Ẩn"
      } sản phẩm thành công!`
    );
    fetchData();
  };
  const handleFilterOfTable = (pagination: any, filters: any, sorter: any) => {
    const filterCode = filters.code?.[0];
    const filterName = filters.name?.[0];

    const queryObject: any[] = [];

    if (filterCode && filterName) {
      // Xử lý clear 1 trong 2 theo người dùng chọn cuối
      if (lastFilterChanged.current === "name") {
        filters.code = undefined;
      } else if (lastFilterChanged.current === "code") {
        filters.name = undefined;
      }
    }

    if (filters.code?.[0]) {
      queryObject.push({
        type: "sort",
        field: "product.code",
        value: filters.code?.[0],
      });
    }

    if (filters.name?.[0]) {
      queryObject.push({
        type: "sort",
        field: "product.name",
        value: filters.name?.[0],
      });
    }

    query.queryObject = JSON.stringify(queryObject);
    console.log(
      "Khi filter",
      (query.queryObject = JSON.stringify(queryObject))
    );
    setQuery({ ...query });
    fetchData();
  };
  const debounceSearch = useCallback(
    debounce(
      (keyword) => setQuery({ ...query, search: keyword, page: 1 }),
      300
    ),
    [query]
  );
  return (
    <div>
      <div className="filter-container">
        <Space>
          <div className="filter-item">
            <label htmlFor="">Tìm kiếm</label>
            <Input
              allowClear
              onKeyDown={(ev) => {
                if (ev.code == "Enter") {
                  query.page = 1;
                  setQuery({ ...query });
                }
              }}
              size="middle"
              onChange={(ev) => {
                // query.search = ev.currentTarget.value;
                debounceSearch(ev.target.value);
              }}
              placeholder="Tìm kiếm"
            />
          </div>

          <div className="filter-item btn">
            <Button
              onClick={fetchData}
              type="primary"
              icon={<SearchOutlined />}
            >
              Tìm kiếm
            </Button>
          </div>

          <div className={`filter-item btn ${isChooseMode ? "hidden" : ""}`}>
            <Button
              onClick={() => {
                modalRef.current?.handleCreate();
              }}
              type="primary"
              icon={<PlusOutlined />}
            >
              Thêm mới
            </Button>
          </div>
          {/* <div className="filter-item btn">
            <Popconfirm
              title={`Bạn có muốn xuất file excel`}
              onConfirm={() =>
                handleExport({
                  onProgress(percent) {},
                  exportColumns,
                  fileType: "xlsx",
                  dataField: "components",
                  query: query,
                  api: componentApi.findAll,
                  fileName: "Danh sách cấu hình thành phần",
                  sheetName: "Danh sách cấu hình thành phần",
                })
              }
              okText={"Xuất excel"}
              cancelText={"Huỷ"}
            >
              <Button type="primary" loading={false} icon={<ExportOutlined />}>
                Xuất file excel
              </Button>
            </Popconfirm>
          </div>
          <div className="filter-item btn">
            <Button
              onClick={() => {
                importModal.current?.open();
              }}
              type="primary"
              icon={<ImportOutlined />}
            >
              Nhập excel
            </Button>
          </div> */}
        </Space>
      </div>

      <Spin spinning={loading}>
        <Table
          pagination={false}
          rowKey="id"
          dataSource={products}
          scroll={{ x: "max-content" }}
          rowSelection={
            isChooseMode
              ? {
                  selectedRowKeys,
                  onChange: (selectedRowKeys, selectedRows) => {
                    setSelectedRowKeys(selectedRowKeys);
                    setSelectedProducts(selectedRows);
                    onHandleChooseSingleProduct?.(selectedRows);
                  },
                }
              : undefined
          }
          onChange={handleFilterOfTable}
        >
          <Column
            title="Ảnh"
            dataIndex="fileAttachIcon"
            key="fileAttachIcon"
            align="center"
            render={(text, record: Product) => {
              return (
                <div>
                  {record.fileAttachIcon && (
                    <img
                      width={40}
                      height={40}
                      style={{ objectFit: "cover" }}
                      src={$url(record.fileAttachIcon?.url)}
                      alt=""
                    />
                  )}
                </div>
              );
            }}
          />
          <Column
            title="Mã sản phẩm"
            dataIndex="code"
            align="left"
            key="code"
            render={(text, record: Component) => {
              return <div>{text}</div>;
            }}
            filteredValue={(() => {
              try {
                const obj = JSON.parse(query.queryObject || "[]");
                const item = obj.find((o: any) => o.field === "product.code");
                return item ? [item.value] : null;
              } catch (e) {
                return null;
              }
            })()}
            filterDropdownProps={{
              onOpenChange: (open) => {
                if (open) lastFilterChanged.current = "code";
              },
            }}
            filterMultiple={false}
            filters={[
              { text: "A-Z", value: "ASC" },
              { text: "Z-A", value: "DESC" },
            ]}
          />
          {/* <Column
            title="Ảnh mặt trước"
            dataIndex="fileAttachAvatar"
            key="fileAttachAvatar"
            align="center"
            render={(text, record: Component) => {
              return (
                <div>
                  {record.fileAttachAvatar && (
                    <img
                      width={40}
                      height={40}
                      style={{ objectFit: "cover" }}
                      src={$url(record.fileAttachAvatar?.url)}
                      alt=""
                    />
                  )}
                </div>
              );
            }}
          /> */}
          <Column
            title="Tên nội bộ"
            dataIndex="name"
            align="left"
            key="name"
            render={(text, record: Component) => {
              return <div>{text}</div>;
            }}
            filterMultiple={false}
            filteredValue={(() => {
              try {
                const obj = JSON.parse(query.queryObject || "[]");
                const item = obj.find((o: any) => o.field === "product.name");
                return item ? [item.value] : null;
              } catch (e) {
                return null;
              }
            })()}
            filterDropdownProps={{
              onOpenChange: (open) => {
                if (open) lastFilterChanged.current = "name";
              },
            }}
            filters={[
              { text: "A-Z", value: "ASC" },
              { text: "Z-A", value: "DESC" },
            ]}
          />
          <Column
            title="Tên hiển thị"
            dataIndex="nameVi"
            align="left"
            key="nameVi"
            render={(text, record: Component) => {
              return <div>{text}</div>;
            }}
          />
          <Column
            title="Loại sản phẩm"
            dataIndex="type"
            align="left"
            key="type"
            render={(type: ProductType, record: Product) => {
              return <div>{ProductTypeTrans[type].label}</div>;
            }}
          />
          {/* <Column
            title="Nhóm cha"
            dataIndex="parent"
            key="parent"
            align="left"
            render={(parent: Component, record: Component) => (
              <div className="flex flex-wrap gap-1 justify-center w-full">
                {parent && (
                  <Tag color={getColor(parent?.id)}>
                    {parent?.code}-{parent?.name}
                  </Tag>
                )}
              </div>
            )}
          /> */}
          <Column
            title="Ghi chú"
            dataIndex="note"
            align="left"
            key="note"
            render={(text, record: Component) => {
              return <div>{text}</div>;
            }}
          />
          <Column
            title="Giá tiền ($)"
            dataIndex="price"
            align="left"
            key="price"
            render={(price, record: Component) => {
              return <div>{formatVND(price)}</div>;
            }}
          />
          <Column
            title="Trạng thái"
            align="center"
            dataIndex="status"
            key="status"
            render={(status) => {
              return (
                <Tag
                  className=" text-center"
                  color={status == ProductStatus.Active ? "green" : "red"}
                >
                  {status == ProductStatus.Active ? "Hiện" : "Ẩn"}
                </Tag>
              );
            }}
          />
          <Column
            hidden={isChooseMode ? true : false}
            width={120}
            fixed="right"
            align="center"
            title=""
            key="action"
            render={(text, record: Product) => (
              <DropdownCell
                text="Thao tác"
                items={[
                  {
                    onClick: () => "",
                    label: (
                      <Button
                        className="w-full"
                        type="primary"
                        onClick={() => {
                          modalRef.current?.handleUpdate(record);
                        }}
                      >
                        Cập nhật
                      </Button>
                    ),
                    key: "update",
                  },
                  {
                    onClick: () => "",
                    label: (
                      <Popconfirm
                        onConfirm={() => {
                          handleDelete(record.id);
                        }}
                        title="Xác nhận xóa"
                      >
                        <Button
                          className="w-full"
                          //   onClick={() => {
                          //     modalRef.current?.handleUpdate(record);
                          //   }}
                        >
                          Xóa sản phẩm
                        </Button>
                      </Popconfirm>
                    ),
                  },

                  {
                    label: (
                      <Popconfirm
                        placement="topLeft"
                        title={
                          <div>
                            <h1 className="text-sm">
                              Xác nhận {record.status ? "hiện " : "ẩn"}
                              sản phẩm này?
                            </h1>
                          </div>
                        }
                        onConfirm={() => blockProduct(record)}
                        okText="Đồng ý"
                        cancelText="Không"
                      >
                        <Button
                          icon={
                            record.status == ProductStatus.Inactive ? (
                              <UnlockOutlined />
                            ) : (
                              <LockOutlined />
                            )
                          }
                          // type="ghost"
                          className={`w-full !text-white ${
                            record.status ? "!bg-green-500" : "!bg-amber-500"
                          } !font-medium`}
                        >
                          {record.status == ProductStatus.Inactive
                            ? "Hiện sản phẩm"
                            : "Ẩn sản phẩm"}
                        </Button>
                      </Popconfirm>
                    ),
                    key: "blockStaff",
                  },
                ]}
                trigger={["click"]}
              >
                <a onClick={(e) => e.preventDefault()}>
                  <Space className="bg-black">
                    Thao tác
                    <DownOutlined />
                  </Space>
                </a>
              </DropdownCell>
            )}
          />
        </Table>
        <Pagination
          defaultPageSize={query.limit}
          currentPage={query.page}
          total={total}
          onChange={({ limit, page }) => {
            query.page = page;
            query.limit = limit;
            setQuery({ ...query });
          }}
        />
      </Spin>

      {useMemo(
        () => (
          <ImportSettingComponent
            guide={[
              "Vui lòng tải và sử dụng file mẫu để import dữ liệu đúng cách",
              "Không được thay đổi tiêu đề trong file csv/excel mẫu để tránh import thiếu dữ liệu",
              "Chi tiết xem tại sheet Hướng dẫn của file import mẫu",
            ]}
            onSuccess={() => {
              fetchData();
              // message.success("Nhập dữ liệu thành công.");
            }}
            ref={importModal}
            createApi={componentApi.create}
            onUploaded={(excelData, setData) => {
              console.log("up gì lên vậy", excelData);
              handleOnUploadedFile(excelData, setData);
            }}
            okText={`Nhập cấu hình thành phần ngay`}
            demoExcel="/exportFile/file_mau_nhap_thanh_phan.xlsx"
          />
        ),
        []
      )}
      <CreateProductModal
        onSubmitOk={fetchData}
        onClose={() => {}}
        ref={modalRef}
        refetchData={fetchData}
      />
    </div>
  );
};

export default SingleProductTab;
