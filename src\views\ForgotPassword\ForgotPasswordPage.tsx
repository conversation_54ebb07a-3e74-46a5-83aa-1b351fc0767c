import { Button, Form, Input } from "antd";
import { authApi } from "api/auth.api";
import { useEffect, useRef, useState } from "react";
import { IoKeyOutline } from "react-icons/io5";
import { Link } from "react-router-dom";
import { settings } from "settings";
import { rules } from "utils/validateRule";
import logo from "../../assets/images/logo.png";
import { OtpModal, OtpModalRef } from "./components/OtpModal";

interface IForm {
  email: string;
  otp: string;
  newPassword: string;
}

const ForgotPasswordPage = () => {
  const [form] = Form.useForm<IForm>();

  const otpModalRef = useRef<OtpModalRef>();

  const [isVerified, setIsVerified] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    document.title = "Quên mật khẩu";
  }, []);

  const handleButtonClick = () => {
    if (isVerified) {
      handleResetPassword();
    } else {
      verifyOtp();
    }
  };

  const handleResetPassword = async () => {
    const { email, otp, newPassword } = form.getFieldsValue();

    try {
      setLoading(true);
      await authApi.resetPassword({ email, otp, newPassword });
      setIsSuccess(true);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const verifyOtp = async () => {
    await form.validateFields();
    console.log("first");
    try {
      setLoading(true);
      const { email } = form.getFieldsValue();
      await authApi.sendOtp({ email });
      otpModalRef.current?.openModal(email);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-page">
      <div style={{ paddingTop: 120 }}>
        <div className="login-container">
          <div className="logo text-center">
            <span style={{ fontSize: 22 }}>
              <img src={logo} width={80} style={{ borderRadius: 5 }} alt="" />
            </span>
          </div>

          {isSuccess ? (
            <div className="flex flex-col gap-3">
              {/* <Image
                src={checkedIcon}
                alt=""
                className="mx-auto size-[100px]"
              /> */}
              <div className="mt-4 font-svn text-[22px] font-bold text-primary sm:text-dark-green">
                Bạn đã đổi mật khẩu mới thành công
              </div>
              <div className="text-white sm:text-black mt-2 font-semibold">
                Vui lòng đăng nhập lại
              </div>
              <Link to={"/login"}>
                <Button style={{ width: "100%" }} type="primary" size="large">
                  Đăng nhập
                </Button>
              </Link>
            </div>
          ) : (
            <div className=" mt-2">
              <h3 className="text-white text-center sm:text-black">
                Quên mật khẩu
              </h3>
              {isVerified && (
                <p className="text-white mt-[20px] text-[14px] font-medium sm:text-text md:text-[15px]">
                  {isVerified
                    ? " Vui lòng nhập mật khẩu mới"
                    : " Vui lòng nhập Email vào form dưới để xác thực"}
                </p>
              )}
              <Form form={form} layout="vertical" className="mt-[30px]">
                <Form.Item hidden name={"otp"}></Form.Item>
                <Form.Item
                  name={"email"}
                  rules={[...rules]}
                  label={"Email"}
                  className="text-lg !mb-0"
                  //   className={clsx("font-svn font-bold")}
                >
                  <Input
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        console.log(111);
                        handleButtonClick();
                      }
                    }}
                    placeholder="Nhập email"
                    size="large"
                    disabled={isVerified}
                    className="!rounded-lg "
                  />
                </Form.Item>
                {isVerified && (
                  <>
                    <Form.Item
                      className="!mb-0 !mt-6"
                      name={"newPassword"}
                      label={"Mật khẩu mới"}
                      rules={rules}
                      style={{
                        display: !isVerified ? "none" : "block",
                      }}
                    >
                      <Input.Password
                        placeholder="Nhập mật khẩu mới"
                        className="!rounded-lg py-3"
                        size="large"
                      />
                    </Form.Item>
                  </>
                )}
                <div className=" flex cursor-pointer items-center justify-end gap-1 text-primary">
                  <Link to={"/login"}>
                    <IoKeyOutline
                      // color="#abca74"
                      className="translate-y-0.5 -translate-x-1 text-primary"
                    />

                    <span className="text-primary">Quay lại đăng nhập</span>
                  </Link>
                </div>
                {/* <div className="flex justify-end !w-full ">
                  <Link to={"/login"}>
                    <IoKeyOutline
                      // color="#abca74"
                      className="translate-y-0.5 -translate-x-1 text-primary"
                    />

                    <span className="text-primary">Đăng nhập</span>
                  </Link>
                </div> */}
                <Button
                  //   block
                  //   green
                  //   shadowLight
                  onClick={() => handleButtonClick()}
                  //   loading={loading}
                  style={{ width: "100%", marginTop: 10 }}
                  type="primary"
                  loading={loading}
                  size="large"
                >
                  {isVerified ? "Đổi mật khẩu" : "Tiếp tục"}
                </Button>
              </Form>
            </div>
          )}
          <div>Version: {settings.version}</div>
        </div>
      </div>
      <OtpModal
        ref={otpModalRef}
        // onSubmitOk={(otp) => {
        //   setIsVerified(true);
        //   form.setFieldValue("otp", otp);
        // }}
      />
    </div>
    // <section>

    // </section>
  );
};

export default ForgotPasswordPage;
