import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const permissionApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/permission",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/permission",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/permission/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/permission/${id}`,
      method: "delete",
    }),
};
