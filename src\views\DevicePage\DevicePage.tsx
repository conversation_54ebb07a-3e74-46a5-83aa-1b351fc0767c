import {
  ImportOutlined,
  LockOutlined,
  UnlockOutlined,
} from "@ant-design/icons";
import {
  Button,
  Card,
  Divider,
  message,
  Modal,
  Select,
  Space,
  Table,
  Tag,
  Tooltip,
} from "antd";
import { TableRowSelection } from "antd/es/table/interface";
import { TableProps } from "antd/lib";
import { deviceApi } from "api/device.api";
import { deviceCategoryApi } from "api/deviceCategory.api";
import { dictionaryApi } from "api/dictionary.api";
import { providerApi } from "api/provider.api";
import { unitApi } from "api/unit.api";
import PencilIcon from "assets/svgs/PencilIcon";
import { AxiosPromise } from "axios";
import CustomButton from "components/Button/CustomButton";
import ImportDevice, {
  ImportDeviceModal,
} from "components/ImportDocument/ImportDevice";
import CustomInput from "components/Input/CustomInput";
import PageTitle from "components/PageTitle/PageTitle";
import { Pagination } from "components/Pagination";
import QueryLabel from "components/QueryLabel/QueryLabel";
import { DictionarySelector } from "components/Selector/DictionarySelector";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import dayjs from "dayjs";
import * as ExcelJS from "exceljs";
import { saveAs } from "file-saver";
import { useDevice } from "hooks/useDevice";
import { useDeviceCategory } from "hooks/useDeviceCategory";
import { Fragment, useEffect, useMemo, useRef, useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { PermissionNames } from "types/PermissionNames";
import { permissionStore } from "store/permissionStore";
import { Device, DeviceType, ProductOriginTrans } from "types/device";
import { Dictionary, DictionaryType } from "types/dictionary";
import { QueryParam } from "types/query";
import { Unit } from "types/unit";
import { getTitle } from "utils";
import { handleExport, MyExcelColumn } from "utils/MyExcel";
import {
  checkRole,
  checkRoles,
  filterActionColumnIfNoPermission,
} from "utils/auth";
import { removeSubstringFromKeys } from "utils/common";
import { excelDateToDayjs } from "utils/date";
import { unixToDate } from "utils/dateFormat";
import { addNewSheet } from "utils/excel";
import LockButton from "components/Button/LockButton";
import { getListNameByTypeDictionary } from "hooks/useDictionary";
import EditButton from "components/Button/EditButton";
import { exportTemplateWithValidation } from "utils/TemplateExcel";
import { BMDImage } from "components/Image/BMDImage";
import { $url } from "utils/url";
import logoImage from "assets/images/logo.png";

const { ColumnGroup, Column } = Table;

interface DevicePageProps {
  title?: string;
  type?: DeviceType;
  rowSelection?: TableRowSelection<Device>; // Su dung khi muon select row
  showSelect?: boolean; // Su dung khi muon an page title va nut them moi
}

export const DevicePage = ({
  title = "",
  type = DeviceType.Equipment,
  rowSelection,
  showSelect,
}: DevicePageProps) => {
  const exportColumns: MyExcelColumn<Device>[] = [
    {
      header: type == DeviceType.Equipment ? "Mã thiết bị" : "Mã máy",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "code",
      columnKey: "code",
    },
    {
      header: type == DeviceType.Equipment ? "Thiết bị *" : "Máy thi công *",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "name",
      columnKey: "name",
    },
    {
      header: type == DeviceType.Equipment ? "Loại thiết bị *" : "Loại máy *",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "deviceCategory",
      columnKey: "deviceCategory",
      render: (record: Device) => record.deviceCategory?.name,
    },
    // {
    //   header: "Mô tả",
    //   headingStyle: {
    //     font: {
    //       bold: true,
    //     },
    //   },
    //   key: "description",
    //   columnKey: "description",
    //   width: 70,
    //   style: { alignment: { wrapText: true } },
    //   // style: { numFmt: "###,##" },

    //   render: (record: Device) => record.description,
    // },
    {
      header: "Số hiệu",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "serialNumber",
      columnKey: "serialNumber",
      render: (record: Device) => record.serialNumber,
    },
    {
      header: "Mã hệ thống cũ",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "oldCode",
      columnKey: "oldCode",
      render: (record: Device) => record.oldCode,
    },
    {
      header: "Đơn vị tính *",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "unit",
      columnKey: "unit",
      render: (record: Device) => record.unit?.name,
    },
    {
      header: "Nguồn gốc sản phẩm",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "productOrigin",
      columnKey: "productOrigin",
      render: (record: Device) =>
        ProductOriginTrans[
          record.productOrigin as keyof typeof ProductOriginTrans
        ]?.label,
    },
    {
      header: "Nhà cung cấp",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "provider",
      columnKey: "provider",
      render: (record: Device) => record.provider?.name,
    },
    {
      header: "Mã SP NCC",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "supplierProductCode",
      columnKey: "supplierProductCode",
      render: (record: Device) => record.supplierProductCode,
    },
    {
      header: "Xuất xứ",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "placeOfManufacture",
      columnKey: "placeOfManufacture",
      render: (record: Device) => record.placeOfManufacture,
    },
    {
      header: "Ghi chú",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "notes",
      columnKey: "notes",
      render: (record: Device) => record.notes,
    },
    {
      header: "Mã định danh",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "identificationCode",
      columnKey: "identificationCode",
      render: (record: Device) => record.identificationCode,
    },
    {
      header: "Mã thuộc tính",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "attributeCode",
      columnKey: "attributeCode",
      render: (record: Device) => record.attributeCode,
    },
    {
      header: "Thuộc tính định danh",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "identificationAttribute",
      columnKey: "identificationAttribute",
      render: (record: Device) => record.identificationAttribute,
    },
    {
      header: "Thương hiệu",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "brand",
      columnKey: "brand",
      render: (record: Device) => record.brand?.name,
    },
    {
      header: "Số tháng bảo hành",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "warrantyPeriodMonths",
      columnKey: "warrantyPeriodMonths",
      style: { numFmt: "###,##" },

      render: (record: Device) => record.warrantyPeriodMonths,
    },
    {
      header: "Ngày mua",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "purchaseAt",
      columnKey: "purchaseAt",
      render: (record: Device) =>
        record.purchaseAt && record.purchaseAt > 0
          ? unixToDate(record.purchaseAt)
          : "",
    },
    {
      header: "Điều kiện bảo hành",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "warrantyConditions",
      columnKey: "warrantyConditions",
      render: (record: Device) => record.warrantyConditions,
      width: 70,
      style: { alignment: { wrapText: true } },
    },
    {
      header: "Chi phí thuê",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "rentalCost",
      columnKey: "rentalCost",
      style: { numFmt: "###,##" },

      render: (record: Device) => record.rentalCost,
    },
    {
      header: "Chi phí mua",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "purchaseCost",
      columnKey: "purchaseCost",
      style: { numFmt: "###,##" },

      render: (record: Device) => record.purchaseCost,
    },
    {
      header: "Trạng thái",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "isActive",
      columnKey: "isActive",
      render: (record: Device) => (record.isActive ? "Hoạt động" : "Bị khóa"),
    },
  ];

  const importModal = useRef<ImportDeviceModal>();

  const isEquipment = type == DeviceType.Equipment;

  const {
    haveAddPermission,
    haveBlockPermission,
    haveEditPermission,
    haveViewAllPermission,
  } = checkRoles(
    {
      add: isEquipment ? PermissionNames.deviceAdd : PermissionNames.machineAdd,
      edit: isEquipment
        ? PermissionNames.deviceEdit
        : PermissionNames.machineEdit,
      block: isEquipment
        ? PermissionNames.deviceBlock
        : PermissionNames.machineBlock,
      viewAll: isEquipment
        ? PermissionNames.deviceViewAll
        : PermissionNames.machineViewAll,
    },
    permissionStore.permissions
  );

  const [loadingDelete, setLoadingDelete] = useState(false);
  const [isDownloadDemoExcel, setIsDownloadDemoExcel] = useState(false);

  const { devices, fetchData, loading, query, setQuery, total, isEmptyQuery } =
    useDevice({
      initQuery: {
        limit: 10,
        page: 1,
        type,
        isAdmin: haveViewAllPermission ? true : undefined,
        isActive: showSelect ? true : undefined,
      },
    });

  const navigate = useNavigate();
  useEffect(() => {
    document.title = getTitle(title);
    fetchData();
  }, []);

  const handleDeleteDevice = async (id: number) => {
    try {
      setLoadingDelete(false);
      await deviceApi.delete(id);
      message.success("Xóa thành công");
      fetchData();
    } catch (error) {
    } finally {
      setLoadingDelete(true);
    }
  };

  const handleActiveDevice = async (id: number, value: boolean) => {
    try {
      setLoadingDelete(false);
      await deviceApi.update(id, { device: { isActive: !value } });
      message.success(value ? "Khóa thành công" : "Mở khóa thành công");
      fetchData();
    } catch (error) {
    } finally {
      setLoadingDelete(true);
    }
  };

  const handleOnUploadedFile = async (excelData: any, setData: any) => {
    const { results } = excelData;

    console.log("results", results);

    const importData = results?.map((item: any) => {
      const refineRow = removeSubstringFromKeys(item, " *");

      const code =
        type == DeviceType.Equipment
          ? refineRow["Mã thiết bị"]
          : refineRow["Mã máy"];
      const name =
        type == DeviceType.Equipment
          ? refineRow["Thiết bị"]
          : refineRow["Máy thi công"];
      const deviceCategoryName =
        type == DeviceType.Equipment
          ? refineRow["Loại thiết bị"]
          : refineRow["Loại máy"];
      // const description = refineRow["Mô tả"];
      const serialNumber = refineRow["Số hiệu"];
      const oldCode = refineRow["Mã hệ thống cũ"];
      const unitName = refineRow["Đơn vị tính"];
      const productOrigin = refineRow["Nguồn gốc sản phẩm"];
      const providerName = refineRow["Nhà cung cấp"];
      const supplierProductCode = refineRow["Mã SP NCC"];
      const placeOfManufacture = refineRow["Xuất xứ"];
      const notes = refineRow["Ghi chú"];
      const identificationCode = refineRow["Mã định danh"];
      const attributeCode = refineRow["Mã thuộc tính"];
      const identificationAttribute = refineRow["Thuộc tính định danh"];
      const brandName = refineRow["Thương hiệu"];
      const warrantyPeriodMonths = refineRow["Số tháng bảo hành"];

      const purchaseDate = refineRow["Ngày mua"]
        ? excelDateToDayjs(refineRow["Ngày mua"])
        : "";
      const warrantyConditions = refineRow["Điều kiện bảo hành"];
      const rentalCost = refineRow["Chi phí thuê"];
      const purchaseCost = refineRow["Chi phí mua"];
      const isActive =
        refineRow["Trạng thái"] === "Hoạt động"
          ? true
          : refineRow["Trạng thái"] === "Bị khóa"
          ? false
          : undefined;

      return {
        code,
        name,
        type,
        deviceCategoryName,
        // description,
        serialNumber,
        oldCode,
        unitName,
        productOrigin,
        providerName,
        supplierProductCode,
        placeOfManufacture,
        notes,
        identificationCode,
        attributeCode,
        identificationAttribute,
        brandName,
        warrantyPeriodMonths,
        purchaseDate,
        warrantyConditions,
        rentalCost,
        purchaseCost,
        isActive,
        rowNum: item.__rowNum__,
      };
    });
    console.log("importData", importData);

    setData(importData);
  };

  const getListFromAPI = async <T extends Record<string, any>>({
    api,
    dataField,
    query,
    propField,
  }: {
    api: (params?: any) => AxiosPromise<{ [key: string]: T[] }>;
    dataField: string;
    query?: QueryParam;
    propField?: keyof T;
  }): Promise<(string | T)[]> => {
    const { data } = await api({ limit: 0, page: 1, ...query });

    if (propField) return data[dataField].map((p) => String(p[propField]));
    else return data[dataField];
  };

  const handleDownloadExcelDemoFile = async () => {
    try {
      // Lấy dữ liệu deviceCategory
      const deviceOrMachineCategoryNames = (await getListNameByTypeDictionary(
        type == DeviceType.Equipment
          ? DictionaryType.DeviceCategory
          : DictionaryType.MachineCategory
      )) as string[];
      const brandNames = (await getListFromAPI<Dictionary>({
        api: dictionaryApi.findAll,
        dataField: "dictionaries",
        propField: "name",
        query: {
          page: 1,
          limit: 0,
          type: DictionaryType.Brand,
        },
      })) as string[];

      const unitNames = (await getListFromAPI<Unit>({
        api: unitApi.findAll,
        dataField: "units",
        propField: "name",
      })) as string[];

      const providerNames = (await getListFromAPI<Unit>({
        api: providerApi.findAll,
        dataField: "providers",
        propField: "name",
      })) as string[];

      const countryNames = (await getListFromAPI<Dictionary>({
        api: dictionaryApi.findAll,
        dataField: "dictionaries",
        propField: "name",
        query: {
          page: 1,
          limit: 0,
          type: DictionaryType.Country,
        },
      })) as string[];

      const result = await exportTemplateWithValidation({
        templatePath:
          type == DeviceType.Equipment
            ? "/exportFile/file_mau_nhap_thiet_bi.xlsx"
            : "/exportFile/file_mau_nhap_may_thi_cong.xlsx",
        outputFileName:
          type == DeviceType.Equipment
            ? "file_mau_nhap_thiet_bi.xlsx"
            : "file_mau_nhap_may_thi_cong.xlsx",
        sheetsToAdd: [
          { name: "Category", data: deviceOrMachineCategoryNames },
          { name: "Brand", data: brandNames },
          { name: "Unit", data: unitNames },
          { name: "Provider", data: providerNames },
          { name: "Country", data: countryNames },
          { name: "ProductOrigin", data: ["Nội địa", "Nhập khẩu"] },
          { name: "Active", data: ["Hoạt động", "Bị khóa"] },
        ],
        validations: [
          {
            headerName:
              type == DeviceType.Equipment ? "Loại thiết bị" : "Loại máy",
            type: "list",
            formulae: [
              `'Category'!$A$1:$A$${deviceOrMachineCategoryNames.length}`,
            ],
            error: "Vui lòng chọn một giá trị từ danh sách.",
          },
          {
            headerName: "Đơn vị tính",
            type: "list",
            formulae: [`'Unit'!$A$1:$A$${unitNames.length}`],
            error: "Vui lòng chọn một giá trị từ danh sách.",
          },
          {
            headerName: "Thương hiệu",
            type: "list",
            formulae: [`'Brand'!$A$1:$A$${brandNames.length}`],
            error: "Vui lòng chọn một giá trị từ danh sách.",
          },
          {
            headerName: "Nhà cung cấp",
            type: "list",
            formulae: [`'Provider'!$A$1:$A$${providerNames.length}`],
            error: "Vui lòng chọn một giá trị từ danh sách.",
          },
          {
            headerName: "Xuất xứ",
            type: "list",
            formulae: [`'Country'!$A$1:$A$${countryNames.length}`],
            error: "Vui lòng chọn một giá trị từ danh sách.",
          },
          {
            headerName: "Nguồn gốc sản phẩm",
            type: "list",
            formulae: [`'ProductOrigin'!$A$1:$A$2`],
            error: "Vui lòng chọn một giá trị từ danh sách.",
          },
          {
            headerName: "Trạng thái",
            type: "list",
            formulae: [`'Active'!$A$1:$A$2`],
            error: "Vui lòng chọn một giá trị từ danh sách.",
          },
        ],
      });
    } catch (error) {
      message.error(
        `Có lỗi xảy ra: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  };

  const handleRowClick = (record: Device) => {
    if (showSelect) {
      return;
    }
    if (type == DeviceType.Equipment) {
      navigate(
        `/master-data/${PermissionNames.deviceEdit.replace(
          ":id",
          record!.id + ""
        )}`
      );
    }
    if (type == DeviceType.Machine) {
      navigate(
        `/master-data/${PermissionNames.machineEdit.replace(
          ":id",
          record!.id + ""
        )}`
      );
    }
  };

  const handleTableChange: TableProps<any>["onChange"] = (
    pagination,
    filters,
    sorter
  ) => {
    if (!Array.isArray(sorter)) {
      const fieldMap: Record<string, string> = {
        name: "device.name",

        code: "device.code",
        deviceCategory: "deviceCategory.name",
        brand: "brand.name",
        unit: "unit.name",
      };
      const columnKey = sorter.field || sorter.column?.key;

      if (!sorter.order) {
        // setSortField(null);
        // setSortOrder(null);
        query.queryObject = undefined;
        setQuery({ ...query });
      } else {
        const order = sorter.order === "ascend" ? "ASC" : "DESC";
        // setSortField("jobCategory.name");
        // setSortOrder(order);
        const field = fieldMap[columnKey as string];

        const newQueryObject = JSON.stringify([
          {
            type: "sort",
            field,
            value: order,
          },
        ]);
        query.queryObject = newQueryObject;
        setQuery({ ...query });
      }
      fetchData();
    } else {
      query.queryObject = undefined;
      setQuery({ ...query });
      fetchData();
    }
  };

  const columns: CustomizableColumn<Device>[] = [
    {
      key: "code",
      title: type === DeviceType.Equipment ? "Mã" : "Mã",
      dataIndex: "code",
      width: 100,
      defaultVisible: true,
      sorter: true,

      alwaysVisible: true,
      render: (_, record) => {
        return (
          <div
            className="text-[#1677ff] cursor-pointer"
            onClick={() => handleRowClick(record)}
          >
            {record.code}
          </div>
        );
        // return (
        //   <Link
        //     to={`/master-data/${PermissionNames.deviceEdit.replace(
        //       ":id",
        //       record.id + ""
        //     )}`}
        //   >
        //     {record.code}
        //   </Link>
        // );
      },
    },
    {
      key: "name",
      title: type === DeviceType.Equipment ? "Thiết bị" : "Máy thi công",
      dataIndex: "name",
      defaultVisible: true,
      alwaysVisible: true,
      sorter: true,
      width: 200,

      render: (_, record) => (
        <div className="flex items-center gap-2">
          <BMDImage
            src={
              record.avatar && record.avatar.trim()
                ? $url(record.avatar)
                : logoImage
            }
            className="size-[34px] object-cover"
            width={34}
            height={34}
            fallback={logoImage}
          />
          <label htmlFor="" className="text-bold">
            {record.name}
          </label>
        </div>
      ),
    },

    {
      key: "deviceCategory",
      title: type == DeviceType.Equipment ? "Loại thiết bị" : "Loại máy",
      dataIndex: "deviceCategory",
      width: 140,
      defaultVisible: true,
      render: (_, record) => record.deviceCategory?.name,
      sorter: true,
    },

    {
      key: "unit",
      title: "ĐVT",
      dataIndex: "unit",
      width: 100,
      defaultVisible: true,
      sorter: true,
      render: (_, record) => record.unit?.name,
    },

    {
      key: "brand",
      title: "Thương hiệu",
      dataIndex: "brand",
      width: 140,
      defaultVisible: true,
      sorter: true,
      render: (_, record) => record.brand?.name,
    },
    // {
    //   key: "type",
    //   title: type === DeviceType.Equipment ? "Loại thiết bị" : "Loại máy",
    //   width: 100,
    //   dataIndex: "type",

    //   render: (_, record) => (
    //     <div className="service-cell">
    //       <div className="service-info">
    //         <div className="service-name">{record.deviceCategory?.name}</div>
    //       </div>
    //     </div>
    //   ),
    //   defaultVisible: true,
    // },

    {
      key: "status",
      title: "Trạng thái",
      width: 100,
      align: "center",
      dataIndex: "status",
      render: (status, record) => (
        <div className="justify-center flex">
          {record.isActive ? (
            <Tag color="green" className="status-tag !mr-0">
              Hoạt động
            </Tag>
          ) : (
            <Tag color="red" className="status-tag !mr-0">
              Bị khóa
            </Tag>
          )}
        </div>
      ),
      defaultVisible: true,
    },
    {
      key: "actions",
      title: "Xử lý",
      align: "center",
      width: 100,
      fixed: "right",
      render: (_, record) => (
        <Space size="small">
          {haveEditPermission && type == DeviceType.Equipment && (
            <EditButton
              onClick={(e) => {
                e.stopPropagation();
                // modalRef?.current?.handleUpdate(record);
                navigate(
                  `/master-data/${PermissionNames.deviceEdit.replace(
                    ":id",
                    record!.id + ""
                  )}?update=1`
                );
              }}
            />
          )}
          {haveEditPermission && type == DeviceType.Machine && (
            <EditButton
              onClick={(e) => {
                e.stopPropagation();
                // modalRef?.current?.handleUpdate(record);
                navigate(
                  `/master-data/${PermissionNames.machineEdit.replace(
                    ":id",
                    record!.id + ""
                  )}?update=1`
                );
              }}
            />
          )}

          {haveBlockPermission && (
            <LockButton
              isActive={record.isActive}
              onAccept={() => handleActiveDevice(record.id, record.isActive)}
              modalTitle={`${record.isActive ? "Khóa" : "Mở khóa"} ${
                record.type === DeviceType.Equipment
                  ? "thiết bị"
                  : "máy thi công"
              } ${record.name}`}
              modalContent={
                <>
                  <div>
                    Khi {record.isActive ? "khóa" : "mở khóa"}{" "}
                    {record.type === DeviceType.Equipment
                      ? "thiết bị"
                      : "máy thi công"}{" "}
                    các thông tin của{" "}
                    {record.type === DeviceType.Equipment
                      ? "thiết bị"
                      : "máy thi công"}{" "}
                    này cũng sẽ được {record.isActive ? "khóa" : "mở khóa"}.
                  </div>
                  <div>
                    Bạn có chắc chắn muốn {record.isActive ? "khóa" : "mở khóa"}{" "}
                    {record.type === DeviceType.Equipment
                      ? "thiết bị"
                      : "máy thi công"}{" "}
                    này?
                  </div>
                </>
              }
            />
          )}
        </Space>
      ),
      defaultVisible: true,
      alwaysVisible: true,
    },
  ];

  const CardComponent = showSelect ? Fragment : Card; // Neu co select thi khong render card

  return (
    <div className="app-container">
      {!showSelect && (
        <PageTitle
          title={title}
          breadcrumbs={["Dữ liệu nguồn", title]}
          extra={
            <Space>
              {haveAddPermission && type == DeviceType.Equipment ? (
                <CustomButton
                  size="small"
                  showPlusIcon
                  onClick={() => {
                    // modalRef.current?.handleCreate();
                    navigate(`/master-data/${PermissionNames.deviceAdd}`);
                  }}
                >
                  Tạo thiết bị
                </CustomButton>
              ) : haveAddPermission && type == DeviceType.Machine ? (
                <CustomButton
                  size="small"
                  showPlusIcon
                  onClick={() => {
                    // modalRef.current?.handleCreate();
                    navigate(`/master-data/${PermissionNames.machineAdd}`);
                  }}
                >
                  Tạo máy thi công
                </CustomButton>
              ) : null}
              {haveAddPermission && (
                <CustomButton
                  size="small"
                  icon={<ImportOutlined />}
                  onClick={() => {
                    importModal.current?.open();
                  }}
                >
                  Nhập excel
                </CustomButton>
              )}
            </Space>
          }
        />
      )}

      <CardComponent>
        <div className="flex gap-[16px] items-end pb-[12px] justify-between">
          <div className="flex gap-[16px] items-end flex-wrap">
            <div className="w-[300px]">
              <CustomInput
                tooltipContent={"Tìm theo mã, tên"}
                label="Tìm kiếm"
                placeholder="Tìm kiếm"
                onPressEnter={() => {
                  console.log("onPressEnter:");
                  query.page = 1;
                  setQuery({ ...query });
                  fetchData();
                }}
                value={query.search}
                onChange={(value) => {
                  query.search = value;
                  setQuery({ ...query });

                  if (!value) {
                    fetchData();
                  }
                }}
                allowClear
              />
            </div>
            <div>
              <QueryLabel>Loại</QueryLabel>
              <DictionarySelector
                label="Loại"
                initQuery={{
                  type:
                    type == DeviceType.Equipment
                      ? DictionaryType.DeviceCategory
                      : DictionaryType.MachineCategory,
                }}
                allowClear={true}
                addonOptions={[
                  {
                    id: "",
                    name: "Tất cả các loại",
                  },
                ]}
                value={query.deviceCategoryId ?? ""}
                onChange={(value) => {
                  query.deviceCategoryId = value || "";
                  setQuery({ ...query });
                }}
              />
            </div>
            <div>
              <QueryLabel>Trạng thái</QueryLabel>
              <Select
                value={query.isActive}
                options={[
                  { label: "Hoạt động", value: true },
                  {
                    label: "Bị khóa",
                    value: false,
                  },
                ]}
                placeholder="Trạng thái"
                allowClear
                onChange={(value) => {
                  query.isActive = value;
                  setQuery({ ...query });
                }}
              />
            </div>
            <CustomButton
              onClick={() => {
                query.page = 1;
                setQuery({ ...query });
                fetchData();
              }}
            >
              Áp dụng
            </CustomButton>
            {!isEmptyQuery && (
              <CustomButton
                variant="outline"
                onClick={() => {
                  delete query.deviceCategoryId;
                  delete query.isActive;
                  delete query.search;
                  setQuery({ ...query });
                  fetchData();
                }}
              >
                Bỏ lọc
              </CustomButton>
            )}
          </div>
          {!showSelect && (
            <CustomButton
              onClick={() => {
                Modal.confirm({
                  title: `Bạn có muốn xuất file excel?`,
                  getContainer: () => {
                    return document.getElementById("App") as HTMLElement;
                  },
                  icon: null,

                  footer: (_, { OkBtn, CancelBtn }) => (
                    <>
                      <CustomButton
                        variant="outline"
                        className="cta-button"
                        onClick={() => {
                          handleExport({
                            onProgress(percent) {
                              console.log("What is percent", percent);
                            },
                            exportColumns,
                            fileType: "xlsx",
                            dataField: "devices",
                            query: query,
                            api: deviceApi.findAll,
                            fileName:
                              type == DeviceType.Equipment
                                ? "Danh sách thiết bị"
                                : "Danh sách máy thi công",
                            sheetName:
                              type == DeviceType.Equipment
                                ? "Danh sách thiết bị"
                                : "Danh sách máy thi công",
                          });
                          Modal.destroyAll();
                        }}
                      >
                        Có
                      </CustomButton>
                      <CustomButton
                        onClick={() => {
                          Modal.destroyAll();
                        }}
                        className="cta-button"
                      >
                        Không
                      </CustomButton>

                      {/* <OkBtn />
                      <CancelBtn /> */}
                    </>
                  ),
                });
              }}
            >
              Xuất excel
            </CustomButton>
          )}
        </div>
        <CustomizableTable
          rowSelection={rowSelection}
          columns={filterActionColumnIfNoPermission(columns, [
            haveEditPermission,
            haveBlockPermission,
          ])}
          dataSource={devices}
          rowKey="id"
          loading={loading}
          pagination={false}
          bordered
          displayOptions
          //@ts-ignore
          onChange={handleTableChange}
          onRowClick={handleRowClick}
        />

        <Pagination
          currentPage={query.page}
          defaultPageSize={query.limit}
          total={total}
          onChange={({ limit, page }) => {
            query.page = page;
            query.limit = limit;
            setQuery({ ...query });
            fetchData();
          }}
        />
      </CardComponent>
      {useMemo(
        () => (
          <ImportDevice
            deviceType={type} // Truyền deviceType để ImportDevice biết hiển thị đúng tiêu đề
            guide={[
              "Vui lòng tải và sử dụng file mẫu để import dữ liệu đúng cách",
              "Không được thay đổi tiêu đề trong file csv/excel mẫu để tránh import thiếu dữ liệu",
              "Chi tiết xem tại sheet Hướng dẫn của file import mẫu",
            ]}
            onSuccess={() => {
              query.page = 1;
              fetchData();
            }}
            ref={importModal}
            onUploaded={(excelData, setData) => {
              handleOnUploadedFile(excelData, setData);
            }}
            onDownloadDemoExcel={handleDownloadExcelDemoFile}
            // demoExcel={
            //   type == DeviceType.Equipment
            //     ? "/exportFile/file_mau_nhap_thiet_bi.xlsx"
            //     : "/exportFile/file_mau_nhap_may_thi_cong.xlsx"
            // }
          />
        ),
        [type] // Thêm type vào dependency array
      )}
    </div>
  );
};

{
  /* <Column
            title={type == DeviceType.Equipment ? "Mã thiết bị" : "Mã máy"}
            dataIndex="code"
            key="code"
          />

          <Column
            title={type == DeviceType.Equipment ? "Thiết bị" : "Máy thi công"}
            dataIndex="name"
            key="name"
          />
          <Column title="Mã nhóm" dataIndex="groupCode" key="groupCode" />
          <Column
            title="ĐVT"
            dataIndex="unit"
            key={"unit"}
            render={(_, record: Device) => <>{record.unit?.name}</>}
          />
          <Column
            title="Loại thiết bị"
            dataIndex="deviceCategory"
            key={"deviceCategory"}
            render={(_, record: Device) => <>{record.deviceCategory?.name}</>}
          />
          <Column
            title="Trạng thái"
            dataIndex="status"
            key={"status"}
            render={(_, record: Device) => (
              <Tag color={DeviceStatusTrans[record.status]?.color}>
                {DeviceStatusTrans[record.status]?.label}
              </Tag>
            )}
          />

          <Column
            title="Xử lý"
            key="action"
            render={(text, record: Device) => (
              <Space>
                {hasEditDevice && type == DeviceType.Equipment && (
                  <Button
                    type="primary"
                    onClick={() => {
                      navigate(`/device/edit-device?deviceId=${record.id}`);

                      // modalRef.current?.handleUpdate(record);
                    }}
                  >
                    <EditOutlined />
                  </Button>
                )}
                {hasEditMachine && type == DeviceType.Machine && (
                  <Button
                    type="primary"
                    onClick={() => {
                      navigate(`/machine/edit-machine?machineId=${record.id}`);

                      // modalRef.current?.handleUpdate(record);
                    }}
                  >
                    <EditOutlined />
                  </Button>
                )}

                <Popconfirm
                  title="Bạn có chắc chắn muốn xóa?"
                  onConfirm={() => handleDeleteDevice(record.id)}
                  okText="Xóa"
                  cancelText="Hủy"
                >
                  <Button danger>
                    <DeleteOutlined />
                  </Button>
                </Popconfirm>
              </Space>
            )}
          /> */
}
