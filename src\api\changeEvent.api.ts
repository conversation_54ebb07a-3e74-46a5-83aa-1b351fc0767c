import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const changeEventApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/changeEvent",
      params,
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/changeEvent/${id}`,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/changeEvent",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/changeEvent/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/changeEvent/${id}`,
      method: "delete",
    }),
  approve: (id: number, data?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/changeEvent/${id}/approve`,
      method: "patch",
      data,
    }),
  reject: (id: number, data?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/changeEvent/${id}/reject`,
      method: "patch",
      data,
    }),
};
