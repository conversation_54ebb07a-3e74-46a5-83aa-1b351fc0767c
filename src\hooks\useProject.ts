import { projectApi } from "api/project.api";
import { useState, useMemo, useRef } from "react";
import { Project } from "types/project";
import { QueryParam } from "types/query";

export interface ProjectQuery extends QueryParam { }

interface UseProjectProps {
  initQuery: ProjectQuery;
}

export const useProject = ({ initQuery }: UseProjectProps) => {
  const [data, setData] = useState<Project[]>([]);
  const dataRef = useRef<Project[]>([]);
  dataRef.current = data;
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<ProjectQuery>(initQuery);
  const [loading, setLoading] = useState(false);
  const [detailLoading, setDetailLoading] = useState(false);
  const [isFetched, setIsFetched] = useState(false);

  const isEmptyQuery = useMemo(
    () =>
      Object.keys(query).filter(
        (k) => query[k] && !["limit", "page"].includes(k)
      ).length == 0,
    [query]
  );

  const fetchData = async (newQuery?: ProjectQuery) => {
    setLoading(true);
    try {
      const { data } = await projectApi.findAll({ ...query, ...newQuery });

      setData(data.projects);
      setTotal(data.total);
    } finally {
      setIsFetched(true);
      setLoading(false);
    }
  };

  const loadMore = async () => {
    if (dataRef.current.length != total) {
      try {
        setLoading(true);
        query.page!++;
        const res = await projectApi.findAll(query);
        setData([...dataRef.current, ...res.data.projects]);
        setTotal(res.data.total);
      } finally {
        setLoading(false);
      }
    }
  };

  const fetchDataDetail = async (id: number): Promise<Project | null> => {
    setDetailLoading(true);
    try {
      const { data } = await projectApi.findOne(id);
      return data.project || data; // Tùy thuộc vào cấu trúc response từ API
    } catch (error) {
      console.error("Error fetching project detail:", error);
      return null;
    } finally {
      setDetailLoading(false);
    }
  };

  return {
    projects: data,
    total,
    fetchData,
    fetchDataDetail,
    loading,
    detailLoading,
    setQuery,
    query,
    isEmptyQuery,
    loadMore,
    isFetched,
    setData,
  };
};
