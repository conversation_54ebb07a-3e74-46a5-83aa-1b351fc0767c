import { Col, Form, Input, message, Modal, Row, Select } from "antd";
import { materialApi } from "api/material.api";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { ModalStatus } from "types/modal";
import { Material } from "types/material";
import { useWatch } from "antd/lib/form/Form";
import { InputNumber } from "components/Input/InputNumber";
import { requiredRule } from "utils/validateRule";
import { set, uniqueId } from "lodash";
import {
  BOM,
  MainComponent,
  ProductDetail,
  ProductStyleDetail,
} from "types/product";
import { ComponentsSelector } from "components/Selector/ComponentsSelector";
import { useComponent } from "hooks/useComponent";
import { VariantSelector } from "components/Selector/VariantSelector";
import { useVariant } from "hooks/useVariant";
import { componentApi } from "api/component.api";
import { CiLight } from "react-icons/ci";
import { MainComponentSelector } from "components/Selector/MainComponentSelector ";
import { variantApi } from "api/variant.api";
import { Component } from "types/component";
import { Variant } from "types/variant";
export interface mainComponentForStyleRef {
  handleCreate: (productId: number) => void;
  handleUpdate: (mainComponent: ProductStyleDetail, productId: number) => void;
}
interface MainComponentModalProps {
  onClose: () => void;
  onSubmitOk: (item: ComponentAndVariant) => void;
  onUpdate: (item: ComponentAndVariant) => void;
  mainComponentArray: Component[];
}
export interface ComponentAndVariant {
  id?: number;
  componentGroupId: number;
  componentId: number;
  // variantId: number;
}
export const AddMainComponentForStyle = React.forwardRef<
  mainComponentForStyleRef,
  MainComponentModalProps
>(({ onClose, onSubmitOk, onUpdate, mainComponentArray }, ref) => {
  const [form] = Form.useForm<ComponentAndVariant>();
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [status, setStatus] = useState<ModalStatus>("create");
  const [productId, setProductId] = useState<number>();
  // const [isMainComponentSelected, setIsMainComponentSelected] = useState<any>();
  // const [isComponentSelected, setIsComponentSelected] = useState<any>();
  const [mainComponentForInitOption, setMainComponentForInitOption] =
    useState<Component>();
  const [componentForInitOption, setComponentForInitOption] =
    useState<Component>();
  const [variantForInitOption, setVariantForInitOption] = useState<Variant>();
  const isMainComponentSelected = useWatch("componentGroupId", form);
  const isComponentSelected = useWatch("componentId", form);
  useImperativeHandle(
    ref,
    () => ({
      handleCreate(productId) {
        form.resetFields();
        setStatus("create");
        setVisible(true);
        setProductId(productId);
        // setIsComponentSelected(undefined);
        // setIsMainComponentSelected(undefined);
      },
      handleUpdate(mainComponent, productId) {
        console.log("Nhận đc gì khi upadte", mainComponent);
        form.setFieldsValue({
          componentGroupId: mainComponent.componentGroup?.id,
          componentId: mainComponent.component?.id,
          // variantId: mainComponent.variant?.id,
          id: mainComponent.id,
        });
        setComponentForInitOption(mainComponent?.component);
        setMainComponentForInitOption(mainComponent?.componentGroup);
        // setVariantForInitOption(mainComponent?.variant);
        setProductId(productId);
        setStatus("update");
        setVisible(true);
      },
    }),
    []
  );
  const handleOk = async () => {
    try {
      const values = form.getFieldsValue();
      setLoading(true);
      const payload: ComponentAndVariant = {
        id: status == "update" ? values.id : Number(uniqueId()),
        componentGroupId: values.componentGroupId,
        componentId: values.componentId,
        // variantId: values.variantId,
      };

      if (status === "create") {
        onSubmitOk(payload);
      } else {
        onUpdate(payload);
      }
      // message.success(
      //   status === "create" ? "Cấu hình thành công" : "Cập nhật thành công"
      // );
      handleClose();
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setVisible(false);
    form.resetFields();
    onClose();
  };
  const handleGetOneComponent = async (id: number) => {
    try {
      const { data } = await componentApi.findOne(id);
      return data;
    } catch (error) {
      console.error(error);
    }
  };
  const {
    variants,
    fetchData: fetchVariants,
    total: totalVariant,
    loading: loadingVariant,
  } = useVariant({
    initQuery: {
      page: 1,
      limit: 50,
    },
  });
  useEffect(() => {
    fetchVariants();
  }, []);
  const handleGetOneVariant = async (id: number) => {
    try {
      const { data } = await variantApi.findOne(id);
      return data;
    } catch (error) {
      console.log(error);
    }
  };
  return (
    <Modal
      open={visible}
      onCancel={handleClose}
      onOk={() => form.submit()}
      width={1200}
      confirmLoading={loading}
      title={
        status === "create"
          ? "Thêm cấu hình biến thể thành phần chính"
          : "Cập nhật cấu hình biến thể thành phần chính"
      }
      style={{ top: 20 }}
    >
      <Form layout="vertical" form={form} onFinish={handleOk}>
        <Form.Item hidden name={"id"}></Form.Item>
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              label="Tên thành phần chính"
              name="componentGroupId"
              rules={[requiredRule]}
            >
              <MainComponentSelector
                allowClear
                isMainComponent
                initOptionItem={mainComponentForInitOption}
                productId={productId || undefined}
                placeholder="Chọn thành phần chính"
                onChange={(id) => {
                  console.log("ID là", id);
                  // setIsMainComponentSelected(id);
                  form.setFieldValue("componentGroupId", id);
                  form.setFieldValue("componentId", undefined);
                  // form.setFieldValue("variantId", undefined);
                  // handleGetOneComponent(id).then((data) => {
                  //   form.setFieldValue("mainComponent", data?.id);
                  // setComponentAndVariant((prev) => ({
                  //   component: {
                  //     id: data?.id,
                  //     name: data?.name,
                  //   },
                  //   variant: prev?.variant!,
                  // }));
                  // });
                }}
              />
              {/* <Select
                allowClear
                placeholder="Chọn thành phần chính"
                onChange={(id) => {
                  console.log("ID là", id);
                  // setIsMainComponentSelected(id);
                  form.setFieldValue("componentGroupId", id);
                  form.setFieldValue("componentId", undefined);
                  // form.setFieldValue("variantId", undefined);
                  // handleGetOneComponent(id).then((data) => {
                  //   form.setFieldValue("mainComponent", data?.id);
                  // setComponentAndVariant((prev) => ({
                  //   component: {
                  //     id: data?.id,
                  //     name: data?.name,
                  //   },
                  //   variant: prev?.variant!,
                  // }));
                  // });
                }}
              >
                {mainComponentArray.map((item) => (
                  <Select.Option key={item.id}>{item.name}</Select.Option>
                ))}
              </Select> */}
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              label="Tên thành phần"
              name="componentId"
              rules={[requiredRule]}
            >
              <ComponentsSelector
                isDisplayCodeAndName
                componentGroupId={isMainComponentSelected}
                allowClear
                placeholder="Chọn thành phần"
                initOptionItem={componentForInitOption}
                productId={productId || undefined}
                disabled={!isMainComponentSelected}
                onChange={(id) => {
                  console.log("ID là", id);
                  // setComponentValue(id);
                  // setIsComponentSelected(id);
                  form.setFieldValue("componentId", id);
                  // form.setFieldValue("variantId", undefined);
                }}
              />
            </Form.Item>
          </Col>
          {/* <Col span={24}>
            <Form.Item
              label="Tên biến thể"
              name="variantId"
              rules={[requiredRule]}
            >
              <VariantSelector
                disabled={!isComponentSelected}
                allowClear
                componentId={isComponentSelected}
                isDisplayNameAndCode
                isMainComponent
                initOptionItem={variantForInitOption}
                onChange={(id) => {
                  const variant = variants.find((item) => item.id == id);
                  console.log("variant là", variant);
                  form.setFieldValue("variantId", id);
                  // setComponentAndVariant((prev) => ({
                  //   variant: {
                  //     id: variant!?.id,
                  //     name: variant!?.privateName,
                  //   },
                  //   component: prev?.component!,
                  // }));
                }}
              />
            </Form.Item>
          </Col> */}
        </Row>
      </Form>
    </Modal>
  );
});
