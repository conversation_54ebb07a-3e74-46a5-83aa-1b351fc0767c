import { settings } from "settings";

const Comment = () => {
  return (
    <div
      className="p-2 flex flex-col gap-1"
      style={{ backgroundColor: "var(--color-neutral-n1)" }}
    >
      <div className="flex gap-1 items-center">
        <img
          src={settings.logo}
          className="rounded-full size-[32px] object-contain"
          style={{
            border: "1px solid var(--color-neutral-n2)",
          }}
        />
        <div
          className="leading-[140%] font-bold"
          style={{ color: "var(--color-logo)" }}
        >
          <PERSON><PERSON><PERSON> Van Trí (NV123) - <PERSON><PERSON><PERSON><PERSON> lý
        </div>
      </div>
      <div
        className="leading-[140%] font-light"
        style={{ color: "var(--color-logo)" }}
      >
        Note message
      </div>
      <div
        className="leading-[140%] font-light text-[12px]"
        style={{ color: "var(--color-neutral-n5)" }}
      >
        08:30 12/06/2025
      </div>
    </div>
  );
};

export default Comment;
