import {
  Card,
  Col,
  DatePicker,
  Form,
  Input,
  message,
  Row,
  Select,
  Spin,
  Tabs,
} from "antd";
import TextArea from "antd/es/input/TextArea";
import { Rule } from "antd/lib/form";
import { FileUploadMultiple } from "components/Upload/FileUploadMultiple";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import React, { useEffect, useMemo, useState } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { FileAttach } from "types/fileAttach";
import { ModalStatus } from "types/modal";
import { getTitle } from "utils";
import { FileAttachPayload } from "components/Upload/FileUploadItem";
import { $url } from "utils/url";
import { UploadFile } from "antd/lib";
import { useWatch } from "antd/es/form/Form";
import { staffApi } from "api/staff.api";
import {
  CustomerShiftTrans,
  MaritalStatusTrans,
  Staff,
  StaffType,
} from "types/staff";
import dayjs from "dayjs";
import { GenderTrans } from "constant";
import { settings } from "settings";
import CustomButton from "components/Button/CustomButton";
import { WorkStatus, WorkStatusTrans } from "types/workStatus";
import { useDepartment } from "hooks/useDepartment";
import { useLevel } from "hooks/useLevel";
import { DictionarySelector } from "components/Selector/DictionarySelector";
import { DictionaryType } from "types/dictionary";
import { RoleSelector } from "components/Selector/RoleSelector";
import clsx from "clsx";
import PageTitle from "components/PageTitle/PageTitle";
import { isEmpty } from "lodash";
import { PermissionNames } from "types/PermissionNames";
import { FileUploadMultiple2 } from "components/Upload/FileUploadMultiple2";
import { fileAttachApi } from "api/fileAttach.api";
import { InputNumber } from "components/Input/InputNumber";
import { phoneNumberRule, phoneValidate, emailRules } from "utils/validateRule";
import { observer } from "mobx-react";
import { checkRoles } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { AccountSelector } from "components/Selector/AccountSelector";
import { CompanySelector } from "components/Selector/CompanySelector";
import { BMDTextArea } from "components/TextArea/BMDTextArea";
import { BMDCKEditor } from "components/Editor";

const rules: Rule[] = [{ required: true }];
const phoneRules: Rule[] = [{ required: true }, ...phoneValidate];
const descriptionRules: Rule[] = [{ required: false }];

interface Props {
  title: string;
  status: ModalStatus;
  companyType?: string;
}

export const CreateOrUpdateStaffPage = observer(
  ({ title = "", status, companyType = "" }: Props) => {
    const isStaffOther = companyType === StaffType.Other;
    const { haveEditPermission } = checkRoles(
      {
        edit: isStaffOther
          ? PermissionNames.staffOtherEdit
          : PermissionNames.staffEdit,
      },
      permissionStore.permissions
    );

    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const navigate = useNavigate();
    const [fileList, setFileList] = useState<FileAttach[]>([]);
    const [selectedStaff, setSelectedStaff] = useState<Staff>();
    const avatar = useWatch("avatar", form);
    const [searchParams, setSearchParams] = useSearchParams();
    const [readonly, setReadonly] = useState(true);
    const [loadingFetch, setLoadingFetch] = useState(false);
    const params = useParams();

    const listHref = useMemo(() => {
      if (isStaffOther) {
        return `/master-data/${PermissionNames.staffOtherList}`;
      } else {
        return `/master-data/${PermissionNames.staffList}`;
      }
    }, []);

    // Format phone number for display (Vietnamese format)
    const formatPhoneDisplay = (phone: string) => {
      if (!phone) return phone;

      // Remove all non-digit characters
      const digits = phone.replace(/\D/g, "");

      // Vietnamese phone number formatting
      if (digits.length === 10 && digits.startsWith("0")) {
        // Format: 0XXX XXX XXX
        return `${digits.slice(0, 4)} ${digits.slice(4, 7)} ${digits.slice(7)}`;
      } else if (digits.length === 11 && digits.startsWith("84")) {
        // Format international: +84 XXX XXX XXX
        return `+84 ${digits.slice(2, 5)} ${digits.slice(5, 8)} ${digits.slice(
          8
        )}`;
      } else if (digits.length >= 9) {
        // Fallback formatting for other cases
        return `${digits.slice(0, 3)} ${digits.slice(3, 6)} ${digits.slice(6)}`;
      }

      return digits;
    };

    // Custom phone input component
    const PhoneInput = ({ value, onChange, ...props }: any) => {
      const [displayValue, setDisplayValue] = useState(value || "");

      useEffect(() => {
        setDisplayValue(formatPhoneDisplay(value || ""));
      }, [value]);

      const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const inputValue = e.target.value;
        // Allow only digits, spaces, dash, parentheses, and + for phone numbers
        const cleaned = inputValue.replace(/[^\d\s\-\(\)\+]/g, "");

        setDisplayValue(cleaned);

        // Store the clean digits only for validation and submission
        const digitsOnly = cleaned.replace(/\D/g, "");
        onChange?.(digitsOnly);
      };

      const handleBlur = () => {
        // Format the display value on blur
        const formatted = formatPhoneDisplay(displayValue);
        setDisplayValue(formatted);
      };

      return (
        <Input
          {...props}
          value={displayValue}
          onChange={handleChange}
          onBlur={handleBlur}
        />
      );
    };

    // Hook để fetch departments
    const {
      departments,
      loading: loadingDepartment,
      fetchData: fetchDepartments,
    } = useDepartment({
      initQuery: {
        limit: 100,
        page: 1,
      },
    });

    // Hook để fetch levels
    const {
      levels,
      loading: loadingLevels,
      fetchData: fetchLevels,
    } = useLevel({
      initQuery: {
        limit: 100,
        page: 1,
      },
    });

    useEffect(() => {
      fetchDepartments();
      fetchLevels();
    }, []);

    const setDataToForm = (data: Staff) => {
      form.setFieldsValue({
        ...data,
        startAt: data.startAt ? dayjs.unix(data.startAt) : undefined,
        officialStartDate: data?.officialStartDate
          ? dayjs(data?.officialStartDate, "YYYY-MM-DD")
          : undefined,
        dob: data?.dob ? dayjs(data?.dob, "YYYY-MM-DD") : undefined,
        departmentId: data.department ? data.department?.id : undefined,
        levelId: data.level ? data.level?.id : undefined,
        jobTitleId: data.jobTitle ? data.jobTitle?.id : undefined,
        roleId: data.role ? data.role?.id : undefined,
        accountId: data.account ? data.account?.id : undefined,
        companyId: data.company ? data.company?.id : undefined,
        identificationDate: data.identificationDate
          ? dayjs(data.identificationDate, "YYYY-MM-DD")
          : undefined,
      });

      //set files
      setFileList(data.fileAttaches ? [...data.fileAttaches] : []);
    };

    const getOneStaff = async (id: number) => {
      try {
        setLoadingFetch(true);
        const { data } = await staffApi.findOne(id);

        if (isEmpty(data)) {
          navigate("/404");

          return;
        }

        setSelectedStaff(data);
        setDataToForm(data);
      } catch (e) {
      } finally {
        setLoadingFetch(false);
      }
    };

    useEffect(() => {
      document.title = getTitle(title);

      if (status == "create") {
        setReadonly(false);
      } else {
        const staffId = params.id;
        if (staffId) {
          getOneStaff(+staffId);
          setReadonly(searchParams.get("update") != "1");
        } else {
          navigate("/404");
        }
      }
    }, []);

    const getDataSubmit = async () => {
      const {
        officialStartDate,
        dob,
        startAt,
        files,
        departmentId,
        levelId,
        jobTitleId,
        roleId,
        accountId,
        companyId,
        ...data
      } = form.getFieldsValue();

      const fileAttachIds: number[] = [];

      for (const file of fileList) {
        if (file.id) {
          fileAttachIds.push(file.id);
        } else if (file.originFile) {
          const { data } = await fileAttachApi.upload(file.originFile);

          const resFileAttach = await fileAttachApi.create({
            fileAttach: {
              ...file,
              url: $url(data.path),
            },
          });

          fileAttachIds.push(resFileAttach.data.id);
        }
      }

      const payload = {
        staff: {
          ...data,
          isBlocked: selectedStaff?.isBlocked,
          files: typeof files === "string" ? files : JSON.stringify(files),
          startAt: startAt ? startAt?.startOf("day")?.unix() : 0,
          identificationDate: data.identificationDate
            ? dayjs(data.identificationDate).format("YYYY-MM-DD")
            : "",
          officialStartDate: officialStartDate
            ? officialStartDate.format("YYYY-MM-DD")
            : "",
          dob: dob ? dob.format("YYYY-MM-DD") : "",
          companyType: companyType || undefined,
        },
        jobTitleId: jobTitleId ? jobTitleId : 0,
        departmentId: departmentId ? departmentId : 0,
        levelId: levelId ? levelId : 0,
        // roleId: roleId ? roleId : 0,
        // accountId: accountId ? accountId : 0,
        companyId: companyId ? companyId : 0,
        fileAttachIds,
      };
      return payload;
    };

    const createData = async () => {
      const valid = await form.validateFields();

      setLoading(true);

      const {
        officialStartDate,
        startAt,
        files,
        departmentId,
        levelId,
        jobTitleId,
        roleId,
        dob,
        ...data
      } = form.getFieldsValue();

      const payload = await getDataSubmit();

      try {
        const res = await staffApi.create(payload);
        message.success("Tạo nhân viên thành công!");
        navigate(listHref);
        setFileList([]);
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const valid = await form.validateFields();
      setLoading(true);

      const payload = await getDataSubmit();
      try {
        const res = await staffApi.update(selectedStaff?.id || 0, payload);
        message.success("Chỉnh sửa nhân viên thành công!");
        setReadonly(true);
      } finally {
        setLoading(false);
      }
    };

    const handleSubmit = () => {
      if (status == "create") {
        createData();
      } else {
        updateData();
      }
    };

    const titlePage = useMemo(() => {
      const name = isStaffOther ? "đối tượng" : "nhân viên";
      return status == "create" ? `Tạo ${name}` : `Chỉnh sửa ${name}`;
    }, [status]);

    // Tạo email rules array
    const emailRules: Rule[] = [
      { required: true },
      {
        type: "email",
        message: "Vui lòng nhập email đúng định dạng!",
      },
    ];

    return (
      <div className="app-container">
        <PageTitle
          back
          breadcrumbs={[
            { label: "Dữ liệu nguồn" },
            {
              label: isStaffOther
                ? "Danh sách đối tượng"
                : "Danh sách nhân viên",
              href: `/master-data/${
                isStaffOther
                  ? PermissionNames.staffOtherList
                  : PermissionNames.staffList
              }`,
            },
            { label: titlePage },
          ]}
          title={titlePage}
          // extra={
          //   selectedStaff && status == 'update' && (
          //     <Space>
          //       <ActiveStatusTagSelect
          //         disabled={readonly}
          //         isActive={!selectedStaff?.isBlocked}
          //         onChange={(value) => {
          //           setSelectedStaff({
          //             ...selectedStaff,
          //             isBlocked: !value,
          //           } as Staff);
          //         }}
          //       />
          //     </Space>
          //   )
          // }
        />
        <Card>
          <Spin spinning={loadingFetch}>
            <Form
              layout="vertical"
              form={form}
              className={clsx(readonly ? "readonly" : "")}
              disabled={readonly}
              initialValues={{
                workStatus: WorkStatus.Draft,
              }}
            >
              <div
                style={{
                  display: "flex",
                  gap: 20,
                }}
              >
                <div>
                  <Form.Item
                    style={{ marginBottom: 0, height: "100%" }}
                    // label={<div>Hình đại diện nhân viên</div>}
                    noStyle
                    name="avatar"
                    className="form-height-full"
                  >
                    <SingleImageUpload
                      onUploadOk={(file: FileAttach) => {
                        console.log(file);
                        form.setFieldsValue({
                          avatar: file.path,
                        });
                      }}
                      imageUrl={avatar}
                      height={200}
                      width={"100%"}
                      className="h-full upload-avatar"
                      hideUploadButton={readonly}
                      disabled={readonly}
                    />
                  </Form.Item>
                </div>

                <div
                  style={{
                    flex: 1,
                  }}
                >
                  <Row gutter={16} style={{}}>
                    <Col span={12}>
                      <Form.Item
                        label={isStaffOther ? "Mã đối tượng" : "Mã nhân viên"}
                        name="code"
                      >
                        <Input
                          disabled={status == "update"}
                          placeholder={
                            status == "create"
                              ? "Nếu không điền hệ thống sẽ tự sinh mã"
                              : ""
                          }
                        />
                      </Form.Item>
                    </Col>

                    <Col span={12}>
                      <Form.Item label="Họ tên" name="fullName" rules={rules}>
                        <Input placeholder="Nhập họ tên" />
                      </Form.Item>
                    </Col>

                    <Col span={12}>
                      <Form.Item
                        label="Số điện thoại"
                        name="phone"
                        rules={[{ required: true }, ...phoneValidate]}
                      >
                        <Input placeholder="Nhập số điện thoại" />
                      </Form.Item>
                    </Col>

                    <Col span={12}>
                      <Form.Item label="Email" name="email" rules={emailRules}>
                        <Input placeholder="Nhập địa chỉ email" type="email" />
                      </Form.Item>
                    </Col>

                    {/* <Col span={status == "create" ? 6 : 12}>
                      <Form.Item
                        label="Tài khoản đăng nhập"
                        name="username"
                        rules={rules}
                      >
                        <Input placeholder="Tài khoản đăng nhập" />
                      </Form.Item>
                    </Col>

                    {status == "create" && (
                      <Col span={6}>
                        <Form.Item
                          label="Mật khẩu"
                          name="password"
                          rules={rules}
                          className="input-password"
                        >
                          <Input placeholder="Nhập mật khẩu" type="password" />
                        </Form.Item>
                      </Col>
                    )} */}

                    {/* <Col span={12}>
                      <Form.Item label="Vai trò" name="roleId" rules={rules}>
                        <RoleSelector
                          placeholder="Chọn quyền"
                          showSearch
                          initOptionItem={selectedStaff?.role}
                        />
                      </Form.Item>
                    </Col> */}

                    {!isStaffOther && (
                      <Col span={12}>
                        <Form.Item
                          label="Tình trạng làm việc"
                          name="workStatus"
                        >
                          <Select
                            placeholder="Tình trạng làm việc"
                            options={Object.values(WorkStatusTrans)
                              .filter(
                                (item) => item.value !== WorkStatus.Deleted
                              )
                              .map((item) => ({
                                label: item.label,
                                value: item.value,
                              }))}
                          />
                        </Form.Item>
                      </Col>
                    )}

                    {isStaffOther && (
                      <>
                        <Col span={12}>
                          <Form.Item label="Công ty" name="companyId">
                            <CompanySelector
                              placeholder="Công ty"
                              initOptionItem={selectedStaff?.company}
                              initQuery={{ isDefault: false }}
                            />
                          </Form.Item>
                        </Col>

                        <Col span={12}>
                          <Form.Item label="Chức vụ" name="jobTitleId">
                            <DictionarySelector
                              placeholder="Chọn chức vụ"
                              initQuery={{
                                type: DictionaryType.JobTitle,
                                isActive: true,
                              }}
                              showSearch
                            />
                          </Form.Item>
                        </Col>
                      </>
                    )}
                  </Row>
                </div>
              </div>

              {!isStaffOther && (
                <>
                  <Card
                    title="Thông tin cá nhân"
                    className="mb-4 form-card mt-4"
                  >
                    <Row gutter={16}>
                      <Col span={6}>
                        <Form.Item
                          label="Giới tính"
                          name="gender"
                          rules={rules}
                        >
                          <Select
                            placeholder="Giới tính"
                            options={Object.values(GenderTrans).map((item) => ({
                              value: item.value,
                              label: item.label,
                            }))}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item label="Ngày sinh" name="dob">
                          <DatePicker
                            placeholder="Ngày sinh"
                            format={settings.dateFormat}
                            style={{ width: "100%" }}
                          />
                        </Form.Item>
                      </Col>

                      <Col span={6}>
                        <Form.Item
                          label="Tình trạng hôn nhân"
                          name="maritalStatus"
                        >
                          <Select
                            placeholder="Tình trạng hôn nhân"
                            options={Object.values(MaritalStatusTrans).map(
                              (item) => ({
                                label: item.label,
                                value: item.value,
                              })
                            )}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={6}></Col>
                      <Col span={6}>
                        <Form.Item
                          label="CCCD/Hộ chiếu"
                          name="identificationCard"
                        >
                          <Input placeholder="CCCD/Hộ chiếu" />
                        </Form.Item>
                      </Col>

                      <Col span={6}>
                        <Form.Item
                          label="Ngày cấp CCCD"
                          name="identificationDate"
                        >
                          <DatePicker
                            placeholder="Ngày cấp CCCD"
                            format={settings.dateFormat}
                            style={{ width: "100%" }}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item
                          label="Nơi cấp CCCD"
                          name="identificationPlace"
                        >
                          <Input placeholder="Nơi cấp CCCD" />
                        </Form.Item>
                      </Col>
                      <Col span={6}></Col>
                      <Col span={12}>
                        <Form.Item
                          label="Địa chỉ thường trú"
                          name="permanentAddress"
                        >
                          <Input placeholder="Địa chỉ thường trú" />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          label="Địa chỉ tạm trú"
                          name="temporaryAddress"
                        >
                          <Input placeholder="Địa chỉ tạm trú" />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Card>

                  <Card title="Thông tin công việc" className="mb-4 form-card">
                    <Row gutter={16}>
                      <Col span={6}>
                        <Form.Item label="Phòng ban" name="departmentId">
                          <DictionarySelector
                            placeholder="Chọn phòng ban"
                            initQuery={{
                              type: DictionaryType.Department,
                              isActive: true,
                            }}
                            showSearch
                          />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item label="Chức vụ" name="jobTitleId">
                          <DictionarySelector
                            placeholder="Chọn chức vụ"
                            initQuery={{
                              type: DictionaryType.JobTitle,
                              isActive: true,
                            }}
                            showSearch
                          />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item label="Cấp bậc" name="levelId">
                          <DictionarySelector
                            placeholder="Chọn cấp bậc"
                            initQuery={{
                              type: DictionaryType.Level,
                              isActive: true,
                            }}
                            showSearch
                          />
                        </Form.Item>
                      </Col>

                      <Col span={6}>
                        <Form.Item
                          label="Mã số thuế cá nhân"
                          name="personalTax"
                        >
                          <Input placeholder="Mã số thuế cá nhân" />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item
                          label="Hợp đồng lao động"
                          name="contractType"
                        >
                          <Input placeholder="Hợp đồng lao động" />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item label="Ngày vào làm" name="startAt">
                          <DatePicker
                            placeholder="Ngày vào làm"
                            format={settings.dateFormat}
                            style={{ width: "100%" }}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item
                          label="Ngày chính thức"
                          name="officialStartDate"
                        >
                          <DatePicker
                            placeholder="Ngày chính thức"
                            format={settings.dateFormat}
                            style={{ width: "100%" }}
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Card>

                  <Card
                    title="Thông tin chấm công/lương"
                    className="mb-4 form-card"
                  >
                    <Row gutter={16}>
                      <Col span={6}>
                        <Form.Item label="Mã chấm công" name="timekeepingCode">
                          <Input placeholder="Mã chấm công" />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item label="Ca làm việc" name="shift">
                          <Select
                            placeholder="Ca làm việc"
                            options={Object.values(CustomerShiftTrans).map(
                              (item) => ({
                                label: item.label,
                                value: item.value,
                              })
                            )}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item
                          label="Mức lương cơ bản"
                          name="baseSalary"
                          className="salary-field"
                        >
                          <InputNumber
                            placeholder="Mức lương cơ bản"
                            suffix="VNĐ"
                            className="salary-input-centered"
                          />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item
                          label="Phụ cấp"
                          name="allowances"
                          className="salary-field"
                        >
                          <InputNumber
                            placeholder="Phụ cấp"
                            suffix="VNĐ"
                            className="salary-input-centered"
                          />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item label="Tên ngân hàng" name="bankName">
                          <Input placeholder="Tên ngân hàng" />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item label="Số tài khoản" name="bankAccount">
                          <Input placeholder="Số tài khoản" />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item label="Địa chỉ làm việc" name="workAddress">
                          <Input placeholder="Địa chỉ làm việc" />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Card>
                  <Card title="Thông tin khác" className="mb-4 form-card">
                    <Row gutter={16} align="middle" justify="center">
                      <Col span={12}>
                        <Form.Item
                          label="Trình độ học vấn"
                          name="educationLevel"
                        >
                          <Input placeholder="Trình độ học vấn" />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          label="Bằng cấp chuyên môn"
                          name="professionalQualification"
                        >
                          <Input placeholder="Bằng cấp chuyên môn" />
                        </Form.Item>
                      </Col>
                      <Col span={24}>
                        <Form.Item
                          label="Kỹ năng nghề nghiệp"
                          name="professionalSkills"
                        >
                          <Input placeholder="Kỹ năng nghề nghiệp" />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item
                          label="Tên người liên hệ khẩn cấp"
                          name="contactPerson"
                        >
                          <Input placeholder="Tên người liên hệ khẩn cấp" />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item
                          label="Số điện thoại người liên hệ khẩn cấp"
                          name="emergencyContactPhone"
                          rules={[phoneNumberRule]}
                        >
                          <Input placeholder="Số điện thoại người liên hệ khẩn cấp" />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item
                          label="Mối quan hệ người liên hệ khẩn cấp"
                          name="emergencyContactRelationship"
                        >
                          <Input placeholder="Mối quan hệ người liên hệ khẩn cấp" />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Form.Item
                      label="Ghi chú"
                      name="notes"
                      rules={descriptionRules}
                    >
                      <BMDCKEditor
                        placeholder="Ghi chú"
                        disabled={readonly}
                        inputHeight={300}
                        onChange={(content) => {
                          form.setFieldsValue({ notes: content });
                        }}
                        value={selectedStaff?.notes}
                      />
                    </Form.Item>
                  </Card>
                </>
              )}

              <Tabs defaultActiveKey="1" type="line">
                <Tabs.TabPane tab="Tệp đính kèm" key="1">
                  <Form.Item
                    shouldUpdate={true}
                    style={{ marginBottom: 0, height: "100%" }}
                    className="form-height-full"
                  >
                    {() => {
                      return (
                        <Form.Item
                          label={""}
                          noStyle
                          style={{ marginBottom: 0 }}
                          name="files"
                          className="h-full "
                        >
                          <FileUploadMultiple2
                            className="h-full"
                            fileList={fileList}
                            onUploadOk={(file) => {
                              fileList.push(file);
                              setFileList([...fileList]);
                            }}
                            onDelete={(file) => {
                              const findIndex = fileList.findIndex(
                                (e) => e.uid == file.uid
                              );

                              if (findIndex > -1) {
                                fileList.splice(findIndex, 1);
                                setFileList([...fileList]);
                              }
                            }}
                            hideUploadButton={readonly}
                          />
                        </Form.Item>
                      );
                    }}
                  </Form.Item>
                </Tabs.TabPane>
              </Tabs>
            </Form>
            <div className="flex gap-[16px] justify-end mt-2">
              {!readonly && (
                <CustomButton
                  variant="outline"
                  className="cta-button"
                  onClick={() => {
                    if (status == "create") {
                      navigate(listHref);
                    } else {
                      setDataToForm(selectedStaff!);
                      setReadonly(true);
                    }
                  }}
                >
                  Hủy
                </CustomButton>
              )}

              <CustomButton
                className="cta-button"
                loading={loading}
                onClick={() => {
                  if (!readonly) {
                    handleSubmit();
                  } else {
                    setReadonly(false);
                  }
                }}
                disabled={status == "update" && !haveEditPermission}
              >
                {status == "create"
                  ? "Tạo nhân viên"
                  : readonly
                  ? "Chỉnh sửa"
                  : "Lưu chỉnh sửa"}
              </CustomButton>
            </div>
          </Spin>
        </Card>
      </div>
    );
  }
);
