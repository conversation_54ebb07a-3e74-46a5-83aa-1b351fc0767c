"use client";

import {
  Button,
  Col,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Row,
  Image,
} from "antd";
import { Rule } from "antd/lib/form";
import { authApi } from "api/auth.api";
import React, { useEffect, useImperativeHandle, useRef, useState } from "react";
import { settings } from "settings";
import logo from "assets/images/logo.png";
import { useNavigate } from "react-router-dom";
const rules: Rule[] = [{ required: true, message: "Bắt buộc!" }];

export interface OtpModalRef {
  openModal: (email: string) => void;
}
interface OtpModalProps {
  onClose?: () => void;
  // onSubmitOk: (otp: string) => void;
}

interface IForm {
  otp: number;
  password: string;
  email: string;
}

export const OtpModal = React.forwardRef(
  (
    {
      onClose,
    }: // onSubmitOk
    OtpModalProps,
    ref
  ) => {
    const [form] = Form.useForm<IForm>();
    const [loading, setLoading] = useState(false);
    const [open, setOpen] = useState(false);
    const navigate = useNavigate();
    const inputRef = useRef<HTMLInputElement>(null);
    const email = Form.useWatch("email", form);

    useImperativeHandle<any, OtpModalRef>(
      ref,
      () => ({
        openModal(email: string) {
          setOpen(true);
          form.resetFields();
          form.setFieldsValue({ email });
        },
        closeModal() {
          setOpen(false);
          form.resetFields();
          onClose?.();
        },
      }),
      []
    );

    // useEffect(() => {
    //   if (open) inputRef.current?.focus();
    // }, [open]);

    const submitForm = async () => {
      try {
        setLoading(true);
        await form.validateFields();
        const { ...rest } = form.getFieldsValue();
        await authApi.resetPassword({ ...rest });
        message.success("Đổi mật khẩu thành công!");
        navigate("/login");
        // onSubmitOk(otp);
        setOpen(false);
        form.resetFields();
      } catch (error) {
      } finally {
        setLoading(false);
      }
    };

    return (
      <Modal
        onCancel={() => {
          onClose?.();
          setOpen(false);
        }}
        maskClosable={false}
        // className={{ content: "!rounded-[20px] !py-[40px] !px-[16px]" }}
        open={open}
        centered
        confirmLoading={loading}
        footer={null}
        destroyOnClose
      >
        <div className="mx-auto flex max-w-[400px] flex-col items-center">
          <Image src={logo} alt="" className="!h-[100px]" preview={false} />
          <div className="text-base font-medium text-center my-3">
            Vui lòng nhập mã số chúng tôi đã gửi cho bạn qua email{" "}
            <span className="font-bold text-primary">{email}</span>. Mã xác thực
            có hiệu lực trong 5 phút
          </div>
          <p className="text-[16px] font-medium text-text">{""}</p>
          <Form form={form} layout="vertical" className="w-full">
            <Form.Item
              label={"Nhập OTP"}
              className="font-bold w-full"
              rules={[...rules]}
              name={"otp"}
            >
              <Input
                maxLength={6}
                minLength={6}
                className="!w-full py-3 !rounded-lg"
                // controls={false}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    submitForm();
                  }
                }}
                // ref={inputRef}
                placeholder="Nhập OTP"
                size="large"
              />
            </Form.Item>
            <Form.Item
              className="!mb-0 !mt-6"
              name={"password"}
              label={"Mật khẩu mới"}
              rules={rules}
            >
              <Input.Password
                placeholder="Nhập mật khẩu mới"
                className="!rounded-lg py-3"
                size="large"
              />
            </Form.Item>
            <Form.Item
              className="!mb-0 !mt-6"
              name={"email"}
              rules={rules}
              hidden
            ></Form.Item>
            <Button
              loading={loading}
              onClick={() => submitForm()}
              style={{ width: "100%" }}
              type="primary"
              size="large"
            >
              Đồng ý
            </Button>
          </Form>
        </div>
      </Modal>
    );
  }
);

OtpModal.displayName = "Otp Modal";
