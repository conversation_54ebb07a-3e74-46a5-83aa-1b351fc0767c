import { staffApi } from "api/staff.api";
import { useState } from "react";
import { Staff } from "types/staff";
import { QueryParam } from "types/query";

export interface StaffQuery extends QueryParam {
  companyId?: number;
}

interface UseStaffProps {
  initQuery: StaffQuery;
}

export const useStaff2 = ({ initQuery }: UseStaffProps) => {
  const [data, setData] = useState<Staff[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<StaffQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async (newQuery?: Partial<StaffQuery>) => {
    setLoading(true);
    try {
      const { data } = await staffApi.findAll({ ...query, ...newQuery });

      setData(data.staffs);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return { staffs: data, total, fetchData, loading, setQuery, query };
};
