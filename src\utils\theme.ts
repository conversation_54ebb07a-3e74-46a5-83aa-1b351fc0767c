export const ColorThemes = {
  light: {
    // Branding
    primary: "#193458",
    accent: "#ED1C24",
    logo: "#193458",

    // Neutral palette
    neutral: {
      n0: "#FFFFFF",
      n1: "#F7F7F7",
      n2: "#DDE4E5",
      n3: "#B9C3C5",
      n4: "#788588",
      n5: "#568266",
      n6: "#2D3233",
      n7: "#1F1D1E",
      n8: "#050505",
    },

    // Status colors for projects
    status: {
      planning: "#3949AB",
      inProgress: "#FF8300",
      hold: "#9E9E9E",
      done: "#43A047",
    },

    // Task status colors
    task: {
      normal: "#3949AB",
      speedup: "#43A047",
      warning: "#FDD835",
      issue: "#F88C00",
      slow: "#E53935",
    },
  },

  dark: {
    // Branding (inverted or adjusted for dark theme)
    primary: "#113872", // Lighter blue for dark backgrounds
    accent: "#ED1C24", // Slightly brighter red for visibility
    logo: "#FFFFFF",

    // Neutral palette (inverted)
    neutral: {
      n0: "#050505", // Dark background
      n1: "#1F1D1E",
      n2: "#2D3233",
      n3: "#568266",
      n4: "#788588",
      n5: "#B9C3C5",
      n6: "#DDE4E5",
      n7: "#F7F7F7",
      n8: "#FFFFFF", // Light text
    },

    // Status colors (adjusted for dark theme)
    status: {
      planning: "#283593",
      inProgress: "#FF8F00",
      hold: "#424242",
      done: "#2E7D32",
    },

    // Task status colors (adjusted for dark theme)
    task: {
      normal: "#283593",
      speedup: "#2E7D32",
      warning: "#F9AB25",
      issue: "#EF6C00",
      slow: "#C62828",
    },
  },
};

export const SIDEBAR_WIDTH = 250;
export const HEADER_HEIGHT = 68;
