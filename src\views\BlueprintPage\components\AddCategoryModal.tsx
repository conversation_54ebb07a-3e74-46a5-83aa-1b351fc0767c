import { Modal, Form, Select, Button, message } from "antd";
import { useState } from "react";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import { FileAttach } from "types/fileAttach";
import CustomButton from "components/Button/CustomButton";
import { ProjectItemSelector } from "components/Selector/ProjectItemSelector";
import { ProjectItem } from "types/projectItem";

interface AddCategoryModalProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (item: any) => void;
  loading?: boolean;
  sectionOptions?: { label: string; value: string }[];
  floorOptions?: { label: string; value: string }[];
}

export default function AddCategoryModal({
  visible,
  onClose,
  onSubmit,
  loading,
  sectionOptions,
  floorOptions,
}: AddCategoryModalProps) {
  const [form] = Form.useForm();
  const [image, setImage] = useState<string | undefined>();
  const [projectItem, setProjectItem] = useState<ProjectItem>();

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      if (!image) {
        message.warning("Vui lòng chọn ảnh!");
        return;
      }
      onSubmit({ ...values, image });
      form.resetFields();
      setImage(undefined);
    } catch {}
  };

  const handleCancel = () => {
    form.resetFields();
    setImage(undefined);
    onClose();
  };

  return (
    <Modal
      open={visible}
      title="Thêm hạng mục"
      onCancel={handleCancel}
      footer={null}
      width={600}
      destroyOnClose
    >
      <Form layout="vertical" form={form}>
        <Form.Item label="Ảnh" required>
          <SingleImageUpload
            onUploadOk={(file: FileAttach) => {
              form.setFieldsValue({
                logo: file.path,
              });
              setImage(file.path);
            }}
            height="150px"
            width={"90%"}
            //@ts-ignore
            imageUrl={image}
          />
        </Form.Item>
        <Form.Item
          label="Hạng mục"
          name="projectItemId"
          rules={[{ required: true, message: "Vui lòng chọn hạng mục" }]}
        >
          <ProjectItemSelector
            placeholder="Chọn hạng mục"
            valueIsOption
            onChange={(selectedProjectItem) => {
              console.log({ selectedProjectItem });
              if (selectedProjectItem) {
                setProjectItem(selectedProjectItem);
                // Reset giá trị tầng khi thay đổi hạng mục
                form.setFieldValue("floor", undefined);
                form.setFieldValue("projectItemId", selectedProjectItem.id);
              } else {
                setProjectItem(undefined);
              }
            }}
          />
        </Form.Item>
        <Form.Item
          label="Tầng"
          name="floor"
          rules={[{ required: true, message: "Vui lòng chọn tầng" }]}
        >
          <Select
            placeholder={
              projectItem ? "Chọn tầng" : "Vui lòng chọn hạng mục trước"
            }
            disabled={!projectItem}
            allowClear
            options={
              projectItem?.floors
                ? Array.from({ length: projectItem.floors }, (_, index) => ({
                    label: `Tầng ${index + 1}`,
                    value: index + 1,
                  }))
                : []
            }
            onChange={(value) => {
              form.setFieldValue("floor", value);
            }}
          />
        </Form.Item>
        <div
          style={{
            display: "flex",
            justifyContent: "flex-end",
            gap: 8,
            marginTop: 16,
          }}
        >
          <CustomButton variant="outline" onClick={handleCancel}>
            Đóng
          </CustomButton>
          <CustomButton onClick={handleOk} loading={loading}>
            Thêm
          </CustomButton>
        </div>
      </Form>
    </Modal>
  );
}
