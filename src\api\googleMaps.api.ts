import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const googleMapsApi = {
  getByLatLong: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/googleMaps/getByLatLong`,
      method: "post",
      data,
    }),
  searchPlace: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/googleMaps/searchPlace`,
      method: "post",
      data,
    }),
  getPlaceDetail: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/googleMaps/getPlaceDetail`,
      method: "post",
      data,
    }),
};
