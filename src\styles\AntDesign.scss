$menu-item-height: 44px;
$menu-item-icon-size: 20px;
$menu-item-font-size: 12px;

.ant-form-item-label label {
  font-weight: 500;
}

.ant-breadcrumb a {
  color: #e3e3e373 !important;
}
.custom-sider {
  background-color: var(--color-primary);
  // background-color: antiquewhite;

  .sidebar {
    --item-background-color: #011f3f;
    --item-background-color2: #000b25;
    --icon-color: var(--color-neutral-n3);
    --icon-color-hover: var(--color-text-selected);
    --text-color: var(--color-neutral-n3);
    --text-color-hover: var(--color-text-selected);

    &.ant-menu-light:not(.ant-menu-horizontal) {
      background-color: transparent;

      .ant-menu-item {
        &::after {
          transition: none;
        }
        &:not(.ant-menu-item-selected) {
          background-color: inherit;
        }
      }
    }

    &.ant-menu {
      overflow: hidden auto;
      padding: 0 0 20px;
      max-height: calc(100vh - 70px);
      border-right: 1px solid var(--color-neutral-n2);

      .ant-menu-title-content {
        font-size: $menu-item-font-size;
        transition: none;
      }

      .-title-content,
      .ant-menu-title-content {
        margin-left: 8px;
        margin-inline-start: 8px !important;
      }

      .ant-menu-inline {
        .ant-menu-title-content {
          line-height: 32px;
        }
      }

      &:not(.ant-menu-horizontal) {
        .ant-menu-item-selected {
          border-radius: 0 !important;
        }
      }
    }

    &.ant-menu-inline-collapsed {
      .ant-menu-item {
        height: 56px;
        display: flex;
        align-items: center;
      }

      .ant-menu-submenu {
        .ant-menu-submenu-title {
          padding: 0 0 0 16px !important;
          height: 56px;
          display: flex;
          align-items: center;

          .-title-content {
            opacity: 0;
          }
        }
      }
    }

    .ant-menu-item {
      margin: 0;
      width: 100%;
      border: none;
      padding: 0 16px !important;
      height: $menu-item-height;
      font-size: 11px;
      transition: none !important;

      &:active {
        border-radius: 0 !important;
      }

      &:not(.ant-menu-item-only-child) {
        .ant-menu-title-content {
          font-weight: 700;
          color: var(--text-color);
        }
      }
      &.ant-menu-item-active {
        .ant-menu-item-icon {
          path {
            fill: var(--icon-color-hover);
          }
        }
        .ant-menu-title-content {
          color: var(--text-color-hover);

          .menu-dot {
            border: 1px solid var(--text-color-hover);
          }
        }
      }

      &.ant-menu-item-selected {
        background-color: var(--item-background-color);
        color: var(--text-color);

        &::after {
          transition: none;
        }

        // .ant-menu-title-content {
        //   color: var(--text-color);
        // }

        // .ant-menu-item-icon {
        //   path {
        //     fill: var(--icon-color);
        //   }
        // }
      }

      .ant-menu-item-icon {
        width: $menu-item-icon-size;
        height: $menu-item-icon-size;
        flex-shrink: 0;

        // path {
        //   fill: var(--icon-color);
        // }
      }
    }

    .ant-menu-submenu {
      border-radius: 0 !important;
      border: none;

      .ant-menu-submenu-title {
        font-size: $menu-item-font-size;
      }

      &.ant-menu-submenu-active:not(.ant-menu-submenu-open) {
        .ant-menu-submenu-title {
          .-item-icon {
            path {
              fill: var(--icon-color-hover);
            }
          }
          .-title-content {
            color: var(--text-color-hover);
          }
        }
      }

      &.ant-menu-submenu-selected:not(.ant-menu-submenu-open) {
        background-color: var(--item-background-color);
      }

      &.ant-menu-submenu-open {
        background-color: unset;

        .ant-menu-submenu-title {
          color: var(--text-color);

          // .-item-icon {
          //   path {
          //     fill: var(--icon-color);
          //   }
          // }
        }
      }

      .ant-menu-submenu-title {
        margin: 0;
        padding: 0 16px !important;
        height: $menu-item-height;
        transition: none !important;
        width: 100%;

        .-item-icon {
          width: $menu-item-icon-size;
          height: $menu-item-icon-size;
          flex-shrink: 0;

          // path {
          //   fill: var(--icon-color);
          // }
        }

        .-title-content {
          font-weight: 700;
          transition: none !important;
          color: var(--text-color);
        }

        .ant-menu-submenu-arrow {
          width: 16px;
          transform: translateY(-50%) !important;
        }
      }

      .ant-menu-item {
        .ant-menu-title-content {
          font-weight: 400;
          margin-inline-start: 7px !important;

          .menu-dot {
            display: inline-block;
            border: 1px solid var(--text-color);
            border-radius: 100%;
            margin-right: 6px;
            width: 8px;
            height: 8px;
          }
        }

        &.ant-menu-item-selected {
          background-color: var(--item-background-color2);

          .ant-menu-title-content {
            color: var(--text-color);
            font-weight: 700;

            .menu-dot {
              background-color: var(--text-color);
            }
          }
        }
      }

      .ant-menu-submenu {
        &.ant-menu-submenu-selected:not(.ant-menu-submenu-open) {
          background-color: var(--item-background-color2);
        }
      }
      .ant-menu-sub.ant-menu-inline {
        background-color: var(--item-background-color);
      }
    }

    svg {
      path {
        fill: var(--icon-color);
      }
    }

    a {
      transition: none;
    }
  }
}

.custom-sub-menu-popup {
  &.ant-menu-submenu-popup {
    .ant-menu.ant-menu-sub.ant-menu-vertical {
      background-color: var(--color-neutral-n0);
      margin: 0;

      .ant-menu-item {
        border-radius: 0px;

        &.ant-menu-item-selected {
          background-color: var(--color-primary);

          .ant-menu-title-content {
            .menu-dot {
              border-color: var(--color-accent);
              background-color: var(--color-accent);
            }

            a {
              color: var(--color-text-selected);
            }
          }
        }

        .ant-menu-title-content {
          .menu-dot {
            display: inline-block;
            border-color: var(--color-neutral-n8);
            border-width: 1px;
            border-style: solid;
            border-radius: 100%;
            margin-right: 16px;
            width: 8px;
            height: 8px;
          }

          a {
            color: var(--color-neutral-n8);
          }
        }
      }
    }
  }
}

.ant-menu-item-only-child {
  border: none;
}

.menu-collapse-icon {
  color: var(--color-neutral-n8);
}

// .ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
//   border-radius: 8px !important;
// }
.ant-menu-item:active {
  border-radius: 8px !important;
}

//border input
.ant-select:not(.ant-select-customize-input) .ant-select-selector,
.ant-input,
.ant-picker,
.ant-input-number,
.ant-input-affix-wrapper {
  border-radius: 5px !important;
  // &:focus {
  //   box-shadow: none;
  // }
}

.ant-select-dropdown {
  .ant-select-item {
    &.ant-select-item-option-selected {
      background: #2e3a59;
      color: #fff;
    }

    .ant-select-item-option-content {
      white-space: normal;
    }
  }
}

.ant-input {
  // padding: 5px 16px !important;
  line-height: 18px;

  &:focus {
    box-shadow: 0 0 0 2px #020f191f;
  }

  &[type="password"] {
    padding: 0 5px !important;
  }
}

input.ant-input {
  font-size: 13px !important;
}

.ant-breadcrumb-separator {
  color: #e3e3e373 !important;
}

.ant-breadcrumb > span:last-child {
  color: #fff !important;
}

// .ant-modal-content {
.ant-btn {
  height: 35px;
  font-size: 13px;
  border-radius: 5px;
  padding: 6px 16px;

  &.login-btn {
    height: auto !important;
    padding: 12px 16px !important;
    font-size: 16px !important;
  }

  .ant-btn-icon {
    width: 16px;
    height: 16px;
  }
}

.ant-layout {
  .ant-layout-header {
    border-bottom: 1px solid var(--color-neutral-n2);
  }
}

.ant-layout-header {
  background: #fff !important;
}

.ant-layout-sider {
  // background: #fff !important;

  &.ant-layout-sider-collapsed {
    width: 56px !important;
    min-width: unset !important;
  }
}

.ant-btn-primary {
  color: #fff;
  // background-color: #101928 !important;
  // border-color: #101928 !important;
  -webkit-transition: all 0.3s;
  font-weight: 500;
  // font-size: 15px;
  // padding: 0 15px;

  transition: all 0.3s;

  //` &:hover {
  //   background-color: #4f5b6e !important;
  //   border-color: #4f5b6e;
  //   color: white;
  // }
}

.ant-input-outlined:focus-within {
  box-shadow: none !important;
}

.ant-form-item {
  margin-bottom: 0px !important;
}

.ant-row {
  .ant-form-item {
    margin-bottom: 8px !important;
  }

  .disable-margin {
    margin-bottom: 0px !important;
  }
}

.footer-full .ant-modal-footer {
  margin-top: 20px;
}

.ant-modal {
  border-radius: 8px !important;
  overflow: hidden;
}

.ant-btn:hover,
.ant-btn:focus {
  // border-color: transparent !important;
}

//upload
.ant-upload-picture-card-wrapper {
  text-align: center;
}

.campaign-modal {
  .ant-picker,
  .ant-input-number {
    width: 100%;
  }

  .ant-input-number-group-wrapper {
    width: 100%;
  }

  .add-product-btn {
    margin-bottom: 10px;
  }
}

//fix mask background action multiple upload
.ant-upload-list-picture-card .ant-upload-list-item-info::before {
  left: 0 !important;
}

.ant-upload-list-item svg {
  font-size: 18px;
}

.ant-upload-list-item .ant-upload-list-item-name {
  font-size: 16px;
}

.row-dragging {
  background: #fafafa;
  border: 1px solid #ccc;
  z-index: 9999;
}

.ant-descriptions-row > th,
.ant-descriptions-row > td {
  padding-bottom: 5px !important;
}

.ant-tag {
  font-weight: bold;
  border-radius: 5px;
}

.row-dragging td {
  padding: 16px;
}

.row-dragging .drag-visible {
  visibility: visible;
}

.drag-icon {
  padding: 0 !important;
}

.ant-descriptions-header {
  margin-bottom: 5px !important;
}

.image-box {
  overflow: hidden;
  position: relative;
  max-height: 120px;
  box-shadow: rgba(0, 0, 0, 0.02) 0px 1px 3px 0px,
    rgba(27, 31, 35, 0.15) 0px 0px 0px 1px;
  border-radius: 7px;
  background-color: #fff;
  display: flex;
  justify-content: center;
  cursor: pointer;

  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;

  &:hover {
    .copy-wrapper {
      opacity: 1;
    }
  }

  .checkbox-absolute {
    position: absolute;
    right: 5px;
    top: 5px;
    z-index: 99;
  }

  .image-name {
    position: absolute;
    bottom: 0;
    z-index: 5;
    padding: 0 10px;
    width: 100%;
    background-color: rgba($color: #000000, $alpha: 0.7);
    margin-bottom: 0;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-height: 200%;
    height: 30px;
    text-overflow: ellipsis;
    color: #fff;
  }

  img {
    height: 120px !important;
    margin: auto;
  }

  .copy-wrapper {
    opacity: 0;
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba($color: #fff, $alpha: 0.5);
    z-index: 9;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: 0.4s;
  }
}

.giftType-stats-table {
  tbody {
    display: none;
  }
}

.ant-select.readOnly {
  pointer-events: none;
}

// .ant-table-thead > tr > th {
//   background: #faf5ff !important;
// }

.ant-input[disabled] {
  color: black !important;
}

// .ant-table-cell {
//   padding: 4px 18px !important;
// }

.ant-input-number-handler-wrap {
  visibility: hidden;
}

.ant-form-vertical .ant-form-item-label > label {
  width: 100%;
}

.ant-modal.ant-modal-confirm {
  border-radius: unset !important;

  .ant-modal-content {
    border-radius: 0;
    padding: 20px;

    .ant-modal-confirm-title {
      color: var(--color-neutral-n8);
      font-size: 16px;
      font-weight: 700;
    }

    .ant-modal-confirm-content {
      color: var(--color-neutral-n8);
      font-size: 14px;
      font-weight: 300;
    }

    // --color-neutral-n8
  }
}

.cta-button {
  padding: 10px 16px;
}

.ant-tag.ant-tag-green {
  color: #43a047;
  background: #e3f1e4;

  border: none;
}

.ant-tag {
  height: 28px;
  width: 99px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 0;
}

.ant-input-affix-wrapper.input-number {
  height: 35px;
  padding: 0;
  padding-right: 16px !important;
  input {
    line-height: 35px !important;
    height: 34px;
    display: flex;
    align-items: center;
  }
}

// .ant-divider-vertical {
//   margin-inline: 16px;
// }

.ant-divider {
  color: var(--color-neutral-n2);
}

:where(.css-dev-only-do-not-override-13mspl4).ant-btn-color-primary {
  box-shadow: none;
}

.ant-modal-body {
  .ant-form-item-label {
    padding: 0 !important;
  }
}

.ant-modal-content {
  .ant-modal-title {
    font-weight: 700;
    font-size: 30px;
    line-height: 140%;
    letter-spacing: 0%;
    color: var(--color-neutral-n8);
  }

  .ant-modal-close-x {
    svg {
      color: var(--color-neutral-n8);

      path {
        width: 14px;
        height: 14px;
      }
    }
  }
}

.ant-form-item-label {
  margin-bottom: 4px !important;

  label {
    line-height: 20px;
  }
}

.ant-modal-header {
  margin-bottom: 20px !important;
}

.ant-modal {
  border-radius: 0px !important;
}

.ant-modal-footer {
  width: 100%;

  .ant-btn-color-primary {
    // min-width: 120px;
    height: 35px;
    border-radius: 5px;
    background-color: var(--color-primary);
    font-weight: 700;
    font-size: 13px;
    line-height: 140%;
    letter-spacing: 0%;
    color: var(--color-neutral-n0);
    margin-inline-start: 0 !important;
    padding: 6px 16px;
  }
}

.footer-full {
  .ant-modal-footer {
    width: 100%;

    .ant-btn-color-primary {
      // height: 44px !important;
      // width: 100% !important;
    }
  }
}

.ant-upload {
}

.ant-card-head {
  min-height: 38px !important;
}

.ant-modal-confirm-btns {
  .ant-btn-variant-solid:not(:disabled):not(.ant-btn-disabled):hover {
    background: #2d4869;
  }
}

.custom-input-container {
  .custom-input.custom-input-normal {
    &.custom-input-number {
      input {
        text-align: right;
      }
    }

    .ant-input-group {
      .ant-input {
        border: none;
        border-radius: 0px;

        // &:focus {
        //   box-shadow: none;
        // }
      }
    }

    .ant-input-suffix {
      color: var(--color-neutral-n8);
      font-size: 13px;
    }

    .ant-input-group-addon {
      background: transparent;
      border: none;
      .ant-select-selector {
        border: none !important;
        height: 30px;

        .ant-select-selection-item {
          color: var(--color-neutral-n4);
        }
      }
    }
  }
}

.ant-table-wrapper
  .ant-table-tbody
  .ant-table-row.ant-table-row-selected
  > .ant-table-cell {
  background: var(--color-neutral-n2);
}

.ant-table-container {
  .ant-table-cell-fix-right {
    background-color: unset;
  }
  .ant-table-cell-fix-right:after {
    display: none;
  }

  .ant-table-thead {
    .ant-checkbox-inner {
      background: var(--color-primary);
      border-radius: 4px;
    }

    .ant-checkbox-checked {
      .ant-checkbox-inner {
        border-color: #fff;
      }
    }

    .ant-checkbox-wrapper:not(.ant-checkbox-wrapper-disabled):hover
      .ant-checkbox-inner {
      border-color: #fff;
    }

    .ant-table-cell {
      font-size: 13px;
    }
  }

  .ant-table-tbody {
    .ant-checkbox-inner {
      border-radius: 4px;
    }

    .ant-checkbox.ant-checkbox-checked {
      border-radius: 4px;
    }

    .ant-table-cell {
      font-size: 13px;
    }
  }

  // Disable hover effect on empty table state
  .ant-table-placeholder {
    .ant-table-cell {
      &:hover {
        background-color: transparent !important;
      }
    }
  }

  // Alternative approach - disable hover on empty row specifically
  .ant-empty-normal {
    pointer-events: none;
  }

  // Disable hover on the entire row when table is empty
  .ant-table-tbody > tr.ant-table-placeholder:hover > td {
    background-color: transparent !important;
  }
}

// tabs
.ant-tabs {
  .ant-tabs-tab {
    padding: 10px 0;
  }

  .ant-tabs-tab.ant-tabs-tab-active {
    .ant-tabs-tab-btn {
      font-weight: bold;
      outline: unset;
    }
  }
}

// Custom class to disable hover on empty table state
.staff-table-no-empty-hover {
  .ant-table-placeholder {
    .ant-table-cell {
      &:hover {
        background-color: transparent !important;
      }
    }
  }

  // Disable hover on the entire row when table is empty
  .ant-table-tbody > tr.ant-table-placeholder:hover > td {
    background-color: transparent !important;
  }

  // Also disable pointer events on empty state to prevent any interaction
  .ant-empty {
    pointer-events: none;
  }
}

// StaffPage specific styles - reduce left padding for search input
.staff-page-container {
  .custom-input-container {
    .custom-input.ant-input {
      padding-left: 8px !important; // Giảm padding bên trái từ 16px xuống 8px
    }

    // Also apply to input with suffix (search icon)s
    .ant-input-affix-wrapper {
      .ant-input {
        padding-left: 8px !important;
      }
    }
  }
}

// Alternative approach using existing staff table class - target the parent card
.ant-card:has(.staff-table-no-empty-hover) {
  .custom-input-container {
    .custom-input.ant-input {
      padding-left: 8px !important; // Giảm padding bên trái từ 16px xuống 8px
    }

    // Also apply to input with suffix (search icon)
    .ant-input-affix-wrapper {
      .ant-input {
        padding-left: 8px !important;
      }
    }
  }
}

// Fallback for browsers that don't support :has() selector
// Use a more specific selector based on the structure
.staff-table-no-empty-hover {
  // Empty rule to establish context
}

// Target the preceding filter section in the same card
.staff-table-no-empty-hover {
  ~ * {
    // This won't work as intended, so let's use a different approach
  }
}

// Better approach - use a class-based selector that works with the current structure
.ant-card {
  // If this card contains a staff table, apply special input padding
  &:has(.staff-table-no-empty-hover) {
    .custom-input-container {
      .custom-input.ant-input {
        padding-left: 8px !important;
      }

      .ant-input-affix-wrapper {
        .ant-input {
          padding-left: 8px !important;
        }
      }
    }
  }
}

// Alternative approach that doesn't rely on :has() selector
// Since the table has class staff-table-no-empty-hover, we can target its parent card
// and apply styles to all inputs within that card
.staff-table-no-empty-hover {
  // This creates a context for the parent card
}

// Use adjacent sibling or general sibling combinators won't work here
// So let's use a different approach - add a specific class to the page

//Override input
.ant-select,
.ant-input,
.ant-picker {
  background-color: var(--color-neutral-n0);
  color: var(--color-neutral-n8);
  border-radius: 5px !important;

  font-size: 13px;
  height: 35px;
}

// .ant-input {
//   // Cho input khi không có value
//   &:not(:placeholder-shown) {
//     border: 1px solid var(--color-neutral-n4) !important;
//   }
// }

// .ant-input-affix-wrapper {
//   &.has-value {
//     border: 1px solid var(--color-neutral-n6);
//   }
// }

.ant-input-affix-wrapper {
  padding-left: 0;

  input.ant-input {
    height: unset !important;
  }

  .ant-input-prefix {
    margin-left: 16px;
    margin-right: -10px;
  }

  input[type="password"] {
    margin-left: 12px;
  }
}

.ant-select-open,
.ant-select-focused {
  .ant-select-selector {
    box-shadow: unset !important;
  }
}

.ant-picker .ant-picker-input > input {
  font-size: 13px;
}

.ant-input,
.ant-picker {
  padding: 0 16px !important;
  color: var(--color-neutral-n8);

  &[disabled] {
    color: var(--color-neutral-n8) !important;
  }
}

.ant-select {
  // min-width: 200px;

  .ant-select-selection-item {
    font-size: 13px !important;
    color: var(--color-neutral-n8);
  }

  .ant-select-selector {
    padding: 0 16px !important;
  }

  .ant-select-clear {
    width: 15px;
    height: 15px;
    top: 40%;

    .anticon {
      &.anticon-close-circle {
        width: 100%;
        height: 100%;

        svg {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}

// .ant-input-affix-wrapper {

// }

textarea.ant-input {
  padding: 6px 16px !important;

  &.ant-input-outlined {
    &.ant-input-status-success {
      &.ant-input-status-success {
        &.ant-input-disabled {
          line-height: unset !important;
        }
      }
    }
  }
}

// Di chuyển required indicator từ trước sang sau label
.ant-form-item-label > label.ant-form-item-required {
  // Ẩn dấu * mặc định phía trước
  &::before {
    display: none !important;
    content: none !important;
  }

  // Thêm dấu * phía sau với độ ưu tiên cao hơn
  &::after {
    content: "*" !important; // Thử bỏ khoảng trắng trước
    color: #ff4d4f !important; // Dùng màu trực tiếp thay vì CSS variable
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
      "Helvetica Neue", Arial, "Noto Sans", sans-serif !important;
    font-size: inherit !important;
    line-height: inherit !important;
    margin-left: 4px !important; // Tăng margin để dễ thấy hơn
    display: inline !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
}

// Backup rule với specificity cao hơn
.ant-form .ant-form-item .ant-form-item-label > label.ant-form-item-required {
  &::before {
    display: none !important;
    content: none !important;
  }

  &::after {
    content: "*" !important;
    color: red !important;
    margin-left: 4px !important;
    display: inline !important;
    font-weight: normal !important;
  }
}

// Thêm rule global backup
label.ant-form-item-required {
  &::before {
    display: none !important;
  }

  &::after {
    content: "*" !important;
    color: #ff4d4f !important;
    margin-left: 4px !important;
  }
}

// Border cho input/select chưa có giá trị - áp dụng toàn bộ ứng dụng
// .ant-input:placeholder-shown:not(:focus) {
//   border: 1px solid #B9C3C5 !important;
// }

.ant-select:not(.ant-select-focused) .ant-select-selector {
  // Khi select chưa có giá trị (placeholder đang hiển thị)
  &:has(.ant-select-selection-placeholder) {
    border: 1px solid #b9c3c5 !important;
  }
}

.ant-picker:not(.ant-picker-focused) {
  &.ant-picker-status-success {
    // border: 1px solid var(--color-primary) !important;

    .ant-picker-input {
      .ant-picker-suffix {
        color: var(--color-primary);
      }
    }
  }
}

// Cho CustomInput component - chỉ giữ lại rule này
.custom-input-container {
  .custom-input.custom-input-normal {
    // Input chưa có giá trị - chỉ áp dụng cho custom-input
    &:placeholder-shown:not(:focus) {
      border: 1px solid #b9c3c5 !important;
    }

    // Select chưa có giá trị
    &.ant-select:not(.ant-select-focused) .ant-select-selector {
      border: 1px solid #b9c3c5 !important;
    }

    // Override khi đã có giá trị
    // &.ant-select:not(.ant-select-focused)
    //   .ant-select-selector:has(.ant-select-selection-item) {
    //   border: 1px solid var(--color-neutral-n6) !important;
    // }

    // DatePicker chưa có giá trị
    &.ant-picker:placeholder-shown:not(.ant-picker-focused) {
      border: 1px solid #b9c3c5 !important;
    }
  }
}

// Đảm bảo hover và focus vẫn hoạt động bình thường
.ant-input:hover,
.ant-input:focus,
.ant-select:hover .ant-select-selector,
.ant-select.ant-select-focused .ant-select-selector,
.ant-picker:hover,
.ant-picker.ant-picker-focused {
  border-color: var(--color-primary) !important;
}

.ant-table-wrapper .ant-table-column-sorter {
  color: #fff;
}

.ant-table-wrapper .ant-table-thead th.ant-table-column-has-sorters:hover {
  background: var(--color-primary);
}

.ant-table-wrapper .ant-table-column-sorters:hover .ant-table-column-sorter {
  color: #fff;
}

.ant-form {
  // Alternative approach không dùng :has() (cho browser cũ)
  .ant-select:not(.ant-select-focused):not(.ant-select-customize-input):not(
      .ant-select-disabled
    )
    .ant-select-selector {
    // border: 1px solid #b9c3c5 !important;
  }

  // Override khi đã có giá trị
  // .ant-select:not(.ant-select-focused):not(.ant-select-disabled)
  //   .ant-select-selector:has(.ant-select-selection-item) {
  //   border: 1px solid var(--color-neutral-n6) !important;
  // }
}

// form readonly
form.ant-form.readonly {
  .ant-form-item-label {
    margin-bottom: 0px !important;
    height: 18px;
    line-height: 9px;
  }
  .ant-form-item-control {
    .ant-form-item-control-input {
      min-height: unset;

      .ant-input-disabled,
      .ant-picker-disabled {
        height: 22px;
        line-height: 11px;
      }

      textarea {
        margin-top: 6px;
      }
    }
  }

  .ant-input-disabled,
  .ant-picker-disabled {
    padding: 0 !important;
    background-color: transparent !important;
    border: none !important;
    // border-bottom: 1px solid var(--color-neutral-n3) !important;
    resize: none !important;
  }

  .ant-picker-disabled {
    .ant-picker-input {
      input {
        color: var(--color-neutral-n8);
      }
      .ant-picker-suffix {
        display: none;
      }
    }
  }

  .input-number {
    // text-align: left !important;
    input {
      border-bottom: unset !important;
    }
  }

  .ant-select-disabled {
    .ant-select-arrow {
      display: none;
    }

    .ant-select-selector {
      padding: 0 !important;
      background-color: transparent !important;
      border: none !important;
      // border-bottom: 1px solid var(--color-neutral-n3) !important;

      .ant-select-selection-wrap {
        &::after {
          line-height: 11px;
          height: 22px;
        }
        .ant-select-selection-search,
        .ant-select-selection-item {
          line-height: 11px;
          height: 22px;
        }

        .ant-select-selection-item-content {
          line-height: initial;
          color: var(--color-neutral-n8);
        }
      }
    }
  }

  .custom-input-container {
    .custom-input.custom-input-disabled {
      padding: 0 !important;
      background-color: transparent !important;
      border: none !important;
      margin-top: 0 !important;
    }
  }
}

.upload-file-container {
  // border
  .ant-upload-wrapper {
    .ant-upload-drag {
      border: 1px dashed var(--color-neutral-n3);
    }
  }

  .ant-upload-wrapper {
    line-height: 18px;
  }
}

.table-empty-icon {
  path {
    fill: var(--color-neutral-n5);
  }
}

.role-sub-table {
  .ant-table {
    margin-block: 0px -1px !important;
    margin-inline: 0px !important;
  }
}

.role-table {
  .ant-table-wrapper {
    .ant-table {
      .ant-table-container {
        .ant-table-body {
          colgroup {
            .ant-table-expand-icon-col {
              width: 20px;
            }
          }

          .ant-table-tbody {
            .ant-table-row {
              background-color: #e0ebff;
            }
            .ant-table-expanded-row {
              > .ant-table-cell {
                padding: 0;

                .role-sub-table {
                  .ant-table-wrapper {
                    .ant-table {
                      margin-block: 0px -1px !important;
                      margin-inline: 0px !important;

                      .ant-table-container {
                        .ant-table-content {
                          colgroup {
                            .ant-table-expand-icon-col {
                              width: 40px;
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  // .ant-checkbox-wrapper {
  //   .ant-checkbox {
  //     &.ant-checkbox-disabled {
  //       .ant-checkbox-inner {
  //         background: var(--color-neutral-n0);
  //         border-color: var(--color-neutral-n3);
  //       }
  //     }
  //     .ant-checkbox-inner {
  //       width: 21px;
  //       height: 21px;
  //       border-radius: 6px;
  //       border-color: var(--color-neutral-n8);
  //       border-width: 1px;

  //       &::after {
  //         width: 6px;
  //         height: 12px;
  //         transform: rotate(45deg) scale(1) translate(-60%, -60%);
  //       }
  //     }
  //   }
  // }
}

// .role-table-new {
//   .ant-checkbox-wrapper {
//     .ant-checkbox {
//       &.ant-checkbox-disabled {
//         .ant-checkbox-inner {
//           background: var(--color-neutral-n0);
//           border-color: var(--color-neutral-n3);
//         }
//       }
//       .ant-checkbox-inner {
//         width: 21px;
//         height: 21px;
//         border-radius: 6px;
//         border-color: var(--color-neutral-n8);
//         border-width: 1px;

//         &::after {
//           width: 6px;
//           height: 12px;
//           transform: rotate(45deg) scale(1) translate(-60%, -60%);
//         }
//       }
//     }
//   }
// }

.ant-select-multiple {
  height: unset;
  min-height: 35px;

  .ant-select-selector {
    height: unset;

    input {
      margin-left: -8px !important;
    }
  }

  .ant-select-selection-wrap {
    height: 100%;
  }

  .ant-select-selection-placeholder {
    inset-inline-start: 0;
  }
}

.ant-tabs.role-tabs {
  .ant-tabs-nav {
    .ant-tabs-nav-wrap {
      .ant-tabs-nav-list {
        .ant-tabs-tab {
          padding: 10px;
          font-weight: 700;

          &.ant-tabs-tab-active {
            .ant-tabs-tab-btn {
              color: var(--color-logo);
            }
          }

          & + .ant-tabs-tab {
            margin-left: 8px;
          }

          .ant-tabs-tab-btn {
            font-size: 20px;
            color: var(--color-neutral-n4);
          }
        }
      }
    }
  }
}

.content-card {
  .content-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;

    .header-text {
      font-weight: 700;
      font-size: 20px;
      color: var(--color-logo);
      margin: 0;
    }
  }
}

.ant-form {
  .input-password {
    .ant-input {
      &[type="password"] {
        padding: 0 16px !important;
      }
    }
  }
}

.switch-left-menu {
  .ant-switch-inner {
    border: 1px solid white;
  }
}
// Style cho submenu level 2 của cấu hình
// .sidebar .ant-menu-submenu .ant-menu-sub .ant-menu-submenu {
//   &.ant-menu-submenu-selected {
//     background-color: var(--color-primary);

//     .ant-menu-submenu-title {
//       color: var(--color-text-selected);
//     }
//   }

//   &.ant-menu-submenu-open {
//     background-color: unset;

//     .ant-menu-submenu-title {
//       color: var(--color-neutral-n8);
//     }
//   }
// }

// Style cho popup submenu level 2
.custom-sub-menu-popup.ant-menu-submenu-popup .ant-menu-sub .ant-menu-submenu {
  &.ant-menu-submenu-selected {
    background-color: var(--color-primary);

    .ant-menu-submenu-title {
      color: var(--color-text-selected);
    }
  }

  &.ant-menu-submenu-open {
    background-color: unset;

    .ant-menu-submenu-title {
      color: var(--color-neutral-n8);
    }
  }
}

.ant-alert-with-description {
  .ant-alert-description {
    li {
      p {
        margin: 0;
      }
    }
  }
}

.project-list-menu {
  background-color: #fff;

  .ant-dropdown-menu {
    height: 500px;
    overflow-y: visible;
    box-shadow: none;
  }
}

.ant-radio-wrapper {
  .ant-radio-inner {
    border-color: #979797;
  }
}

// Style Project
.card-view-separator {
  height: 3px;
  background-color: var(--color-neutral-n3);
  padding: 12px 0;
}

.custom-label-no-required {
  .ant-form-item-label > label::after {
    display: none !important;
  }
}

// Style lever-0 row
.ant-table-wrapper {
  .ant-table.ant-table-bordered {
    > .ant-table-container {
      > .ant-table-content {
        > table {
          > tbody {
            > tr {
              > td {
                padding: 8px;
              }
            }
          }
        }
      }
    }
  }
}

// hide badge border notification

.ant-badge .ant-badge-count {
  box-shadow: none;
}

.custom-modal {
  .ant-modal-confirm-body-wrapper {
    .ant-modal-confirm-btns {
      .ant-btn-primary {
        background-color: var(--color-primary);
      }
    }
  }
}

.ant-color-picker-trigger {
  border-radius: 4px;
}
