import { Form, Input, message, Modal } from "antd";
import { fileAttachApi } from "api/fileAttach.api";
import React, { forwardRef, useImperativeHandle, useState } from "react";
import { FileAttach, FileAttachType } from "types/fileAttach";
export interface UpdateImageModalRef {
  open: (image: any) => void;
  close: () => void;
}
interface UpdateImageModalProps {
  onCreateSuccess?: () => void;
}
const UpdateImageModal = forwardRef<UpdateImageModalRef, UpdateImageModalProps>(
  ({ onCreateSuccess }, ref) => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [currentImage, setCurrentImage] = useState();
    const [currentFolderPath, setCurrentFolderPath] = useState("");
    const [form] = Form.useForm();
    useImperativeHandle(ref, () => ({
      open: (image) => {
        setIsModalOpen(true);
        setCurrentImage(image);
        form.setFieldValue("id", image?.id);
        form.setFieldValue("name", image?.name);
      },
      close: () => setIsModalOpen(false),
    }));
    const handleCreateFolder = async () => {
      try {
        const values = await form.validateFields();

        const { data } = await fileAttachApi.update(values.id, {
          fileAttach: {
            name: values?.name,
          },
        });
        if (onCreateSuccess) {
          onCreateSuccess(); // ✅ Gọi callback để component cha cập nhật lại dữ liệu
        }
        message.success("Cập nhật tệp thành công");
        setIsModalOpen(false);
        form.resetFields();
      } catch (err) {
        console.log("❌ Validation failed", err);
      }
    };
    return (
      <Modal
        open={isModalOpen}
        title="Cập nhật hỉnh ảnh"
        onOk={handleCreateFolder}
        onCancel={() => {
          setIsModalOpen(false);
          form.resetFields();
        }}
      >
        <Form layout="vertical" form={form}>
          <Form.Item
            name="id"
            label="Tên thư mục"
            className="hidden"
            rules={[{ required: true, message: "Vui lòng nhập tên thư mục" }]}
          >
            <Input placeholder="Nhập tên thư mục" />
          </Form.Item>

          <Form.Item name="name" label="Tên ảnh">
            <Input placeholder="Tên ảnh" />
          </Form.Item>
        </Form>
      </Modal>
    );
  }
);

export default UpdateImageModal;
