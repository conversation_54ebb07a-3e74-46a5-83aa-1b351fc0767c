import { useMemo, useState } from "react";
import { QueryParam } from "types/query";
import { taskApi } from "api/task.api";
import { Task } from "types/task";

export interface TaskQuery extends QueryParam {
  ignoreId?: number;
  projectId?: number;
  assigneeMemberShipId?: number;
  createdById?: number;
  type?: string; // Thêm type để filter SCHEDULE
  status?: string;
  dueAt?: string;
  priority?: string;
}
interface UseTaskProps {
  initQuery: TaskQuery;
}

export const useTask = ({ initQuery }: UseTaskProps) => {
  const [data, setData] = useState<Task[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<TaskQuery>(initQuery);
  const [loading, setLoading] = useState(false);
  const [isFetched, setIsFetched] = useState(false);

  const isEmptyQuery = useMemo(
    () =>
      Object.keys(query).filter(
        (k) => query[k] && !["limit", "page", "queryObject"].includes(k)
      ).length === 0,
    [query]
  );

  const fetchData = async (params?: any) => {
    setLoading(true);
    try {
      const { data } = await taskApi.findAll({ ...query, ...params });
      setData(data.tasks ?? []);
      setTotal(data.total ?? 0);
    } finally {
      setLoading(false);
      setIsFetched(true);
    }
  };

  // Thêm hàm lấy chi tiết Task
  const fetchDataDetail = async (id: number) => {
    try {
      const { data } = await taskApi.findOne(id);
      return data as Task;
    } catch {
      return null;
    }
  };

  // Thêm hàm tạo task
  const createTask = async (taskData: any) => {
    try {
      const { data } = await taskApi.create(taskData);
      return data;
    } catch (error) {
      console.error("Error creating task:", error);
      throw error;
    }
  };

  // Thêm hàm cập nhật task
  const updateTask = async (id: number, taskData: any) => {
    try {
      const { data } = await taskApi.update(id, taskData);
      return data;
    } catch (error) {
      console.error("Error updating task:", error);
      throw error;
    }
  };

  return {
    tasks: data,
    total,
    fetchData,
    fetchDataDetail,
    createTask,
    updateTask,
    loading,
    setQuery,
    query,
    isEmptyQuery,
    isFetched,
    setData,
  };
};
