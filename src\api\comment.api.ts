import { request } from "utils/request";
import { AxiosPromise } from "axios";


export const commentApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: '/v1/admin/comment',
      params
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: '/v1/admin/comment',
      data,
      method: 'post'
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/comment/${id}`,
      method: 'patch',
      data
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/comment/${id}`,
      method: 'delete'
    }),
}
