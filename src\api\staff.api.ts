import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const staffApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/staff",
      params,
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/staff/${id}`,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/staff",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/staff/${id}`,
      method: "patch",
      data,
    }),
  resetPassword: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/staff/${id}/password/`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/staff/${id}`,
      method: "delete",
    }),
  block: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/staff/${id}/block`,
      method: "patch",
      data,
    }),
  import: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/staff/import",
      data,
      method: "post",
    }),
};
