import { Route } from "./RouteType";
import { ReactComponent as ContractIcon } from "assets/svgs/contract.svg";
import { PermissionType } from "types/permission";
import { PermissionNames } from "types/PermissionNames";
import { lazy } from "react";

const CreateOrUpdateBoqPage = lazy(
  () => import("views/BoqPage/CreateOrUpdateBoqPage")
);
const BoqPage = lazy(() =>
  import("views/BoqPage/BoqPage").then((m) => ({ default: m.BoqPage }))
);

export const boqRoutes: Route[] = [
  {
    title: "BOQ",
    breadcrumb: "BOQ",
    path: "/boq",
    name: "boq",
    aliasPath: "/boq",
    icon: <ContractIcon />,
    permissionTypes: [
      PermissionType.Add,
      PermissionType.List,
      PermissionType.Edit,
    ],
    needProject: true,
    children: [
      {
        title: "<PERSON><PERSON> sách BOQ",
        breadcrumb: "boq",
        path: PermissionNames.boqList,
        name: PermissionNames.boqList,
        aliasPath: `/boq/${PermissionNames.boqList}`,
        element: <BoqPage title="BOQ" />,
        permissionTypes: [PermissionType.List],
        isPublic: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.boqViewAll,
        name: PermissionNames.boqViewAll,
        aliasPath: `/boq/${PermissionNames.boqViewAll}`,
        hidden: true,
      },
      {
        title: "Tạo BOQ",
        path: PermissionNames.boqAdd,
        name: PermissionNames.boqAdd,
        aliasPath: `/boq/${PermissionNames.boqAdd}`,
        element: <CreateOrUpdateBoqPage title="Tạo BOQ" status="create" />,
        permissionTypes: [PermissionType.Add],
        hidden: true,
        isPublic: true,
      },
      {
        title: "Chỉnh sửa BOQ",
        path: PermissionNames.boqEdit,
        name: PermissionNames.boqEdit,
        aliasPath: `/boq/${PermissionNames.boqEdit.replace("/:id", "")}`,
        element: (
          <CreateOrUpdateBoqPage title="Chỉnh sửa BOQ" status="update" />
        ),
        permissionTypes: [PermissionType.Edit],
        hidden: true,
        isPublic: true,
      },
    ],
  },
];
