import { approvalTemplateApi } from "api/approvalTemplate.api";
import { useMemo, useState } from "react";
import { ApprovalTemplate } from "types/approvalTemplate";
import { QueryParam } from "types/query";

export interface ApprovalTemplateQuery extends QueryParam { }

interface UseApprovalTemplateProps {
  initQuery: ApprovalTemplateQuery;
}

export const useApprovalTemplate = ({ initQuery }: UseApprovalTemplateProps) => {
  const [data, setData] = useState<ApprovalTemplate[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<ApprovalTemplateQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const isEmptyQuery = useMemo(
    () =>
      Object.keys(query).filter(
        (k) => query[k] && !["limit", "page", "queryObject"].includes(k)
      ).length == 0,
    [query]
  );

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await approvalTemplateApi.findAll(query);

      setData(data.approvalTemplates);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return { approvalTemplates: data, total, fetchData, loading, setQuery, query, isEmptyQuery };
};
