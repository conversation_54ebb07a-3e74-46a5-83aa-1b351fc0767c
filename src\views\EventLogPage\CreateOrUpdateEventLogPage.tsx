import { Card, Col, Form, Input, message, Row, Select, Spin, Tabs } from "antd";
import { Rule } from "antd/lib/form";
import { useEffect, useMemo, useState } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { FileAttach } from "types/fileAttach";
import { getTitle } from "utils";
import { $url } from "utils/url";
import { useWatch } from "antd/es/form/Form";
import CustomButton from "components/Button/CustomButton";
import PageTitle from "components/PageTitle/PageTitle";
import { ModalStatus } from "types/modal";
import { isEmpty } from "lodash";
import { PermissionNames } from "types/PermissionNames";
import { DictionaryType } from "types/dictionary";
import { useDictionary } from "hooks/useDictionary";
import clsx from "clsx";
import { TextInput } from "components/Input/TextInput";
import { FileUploadMultiple2 } from "components/Upload/FileUploadMultiple2";
import { fileAttachApi } from "api/fileAttach.api";
import { observer } from "mobx-react";
import { checkEditPermissionByCreator, checkRoles } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import {
  ApprovalStepsCard,
  ApproveData,
  StepItem,
} from "components/ApproveProcess/ApprovalStepsCard";
import { FollowerSelector } from "components/Follower/FollowerSelector";
import { changeEventApi } from "api/changeEvent.api";
import { ChangeEvent, TypeLabel } from "types/changeEvent";
import { appStore } from "store/appStore";
import { transformApproveData } from "components/ApproveProcess/approveUtil";
import { ApprovalListType } from "types/approvalList";
import { approvalListApi } from "api/approvalList.api";
import { CommentView } from "components/Comment/CommentView";
import { ApprovalTemplateType } from "types/approvalTemplate";
import { MemberShip } from "types/memberShip";
import { useApprovalStep } from "hooks/useAppovalStep";
import { toJS } from "mobx";
import { userStore } from "store/userStore";
import { Staff } from "types/staff";
import { BMDTextArea } from "components/TextArea/BMDTextArea";
import { BMDCKEditor } from "components/Editor";

const rules: Rule[] = [{ required: true }];
const descriptionRules: Rule[] = [{ required: false }];

interface EditChangeEventPageProps {
  title: string;
  status: ModalStatus;
}

interface ChangeEventForm extends ChangeEvent {
  changeEventTypeId: number;
  unitId: number;
  providerId: number;
}

export const CreateOrUpdateEventLogPage = observer(
  ({ title = "", status }: EditChangeEventPageProps) => {
    const { haveEditPermission, haveViewAllPermission } = checkRoles(
      {
        edit: PermissionNames.serviceEdit,
        viewAll: PermissionNames.serviceViewAll,
      },
      permissionStore.permissions
    );

    const [form] = Form.useForm<ChangeEventForm>();
    const [loading, setLoading] = useState(false);
    const navigate = useNavigate();
    useEffect(() => {
      document.title = getTitle(title);
    }, []);
    const [fileList, setFileList] = useState<FileAttach[]>([]);
    const avatar = useWatch("avatar", form);
    const [searchParams, setSearchParams] = useSearchParams();
    const [selectedChangeEvent, setSelectedChangeEvent] =
      useState<ChangeEvent>();
    const {
      dictionaries: changeEventTypes,
      fetchData: fetchChangeEventTypes,
      setData: setChangeEventTypes,
    } = useDictionary({
      initQuery:
        status === "create"
          ? {
              page: 1,
              limit: 100,
              type: DictionaryType.ChangeEventCategory,
            }
          : {
              page: 1,
              limit: 100,
              type: DictionaryType.ChangeEventCategory,
            },
    });
    const [readonly, setReadonly] = useState(true);
    const [loadingFetch, setLoadingFetch] = useState(false);
    const params = useParams();

    const {
      followers,
      setFollowers,
      approvalSteps,
      setApprovalSteps,
      fetchApprovalTemplate,
    } = useApprovalStep();

    const [loadingApprove, setLoadingApprove] = useState(false);
    const [removeApprovalList, setRemoveApprovalList] = useState<number[]>([]);
    const [commentRefreshTrigger, setCommentRefreshTrigger] = useState(0);

    const setDataToForm = (data: ChangeEvent) => {
      form.setFieldsValue({
        code: data.code ?? "",
        title: data.title ?? "",
        type: data.type ?? undefined,
        description: data.description ?? "",
        contentChange: data.contentChange ?? "",
      });

      const transformedApproveData = transformApproveData(
        data.approvalLists,
        data.createdBy
      );
      setApprovalSteps(transformedApproveData);
      setFollowers(data.followMemberShips || []);
      setFileList(data.fileAttaches ? [...data.fileAttaches] : []);
    };

    const getOneChangeEvent = async (id: number) => {
      try {
        setLoadingFetch(true);
        const { data } = await changeEventApi.findOne(id);

        if (isEmpty(data)) {
          navigate("/404");

          return;
        }

        setSelectedChangeEvent(data);
        setDataToForm(data);

        if (data.changeEventCategory) {
          setChangeEventTypes([data.changeEventCategory]);
        }

        return data as ChangeEvent;
      } catch (e: any) {
      } finally {
        setLoadingFetch(false);
      }
    };

    useEffect(() => {
      document.title = getTitle(title);

      if (status === "create") {
        setReadonly(false);

        if (!appStore.currentProject) {
          return;
        }

        fetchApprovalTemplate({
          projectId: appStore.currentProject.id,
          type: ApprovalTemplateType.ChangeEvent,
          createdStaff: toJS(userStore.info) as Staff,
        });
        fetchChangeEventTypes();
      }

      if (status == "update") {
        const changeEventId = params.id;

        if (changeEventId) {
          getOneChangeEvent(+changeEventId).then((changeEvent) => {
            fetchChangeEventTypes().then((changeEventTypes) => {
              if (changeEvent?.changeEventCategory) {
                const exist = changeEventTypes.find(
                  (e) => e.id == changeEvent.changeEventCategory.id
                );

                if (!exist) {
                  changeEventTypes.unshift(changeEvent.changeEventCategory);
                  setChangeEventTypes([...changeEventTypes]);
                }
              }
            });
          });

          setReadonly(searchParams.get("update") != "1");
        }
      } else {
        setReadonly(false);
      }
    }, []);

    const getDataSubmit = async () => {
      const { files, ...data } = form.getFieldsValue();

      const fileAttachIds: number[] = [];

      for (const file of fileList) {
        if (file.id) {
          fileAttachIds.push(file.id);
        } else if (file.originFile) {
          const { data } = await fileAttachApi.upload(file.originFile);

          const resFileAttach = await fileAttachApi.create({
            fileAttach: {
              ...file,
              url: $url(data.path),
            },
          });

          fileAttachIds.push(resFileAttach.data.id);
        }
      }

      const approvalLists = approvalSteps.map((e, i) => ({
        ...(status === "update" && { id: e.id }),
        name: e.name,
        type: ApprovalListType.ChangeEvent,
        position: e.position,
        note: e.note,
        memberShipId: e.memberShipId,
        memberShip2Id: e.memberShip2Id,
        staffId: e.staffId,
        changeEventId: selectedChangeEvent?.id || 0,
      }));

      const payload = {
        changeEvent: {
          ...data,
          files: fileAttachIds.length === 0 ? "" : fileAttachIds,
          projectId: appStore.currentProject?.id,
        },
        fileAttachIds: fileAttachIds || [],
        approvalLists,
        followMemberShipIds: followers?.map((it) => it.id),
      };

      return payload;
    };

    const createData = async () => {
      await form.validateFields();

      setLoading(true);
      try {
        await changeEventApi.create(await getDataSubmit());
        message.success("Tạo sự kiện thay đổi thành công!");
        navigate(`/report/${PermissionNames.eventLogList}`);
        setFileList([]);
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const valid = await form.validateFields();

      setLoading(true);
      try {
        const res = await changeEventApi.update(
          selectedChangeEvent!?.id || 0,
          await getDataSubmit()
        );
        message.success("Chỉnh sửa sự kiện thay đổi thành công!");
      } finally {
        setLoading(false);
      }
    };

    const handleSubmit = () => {
      if (status == "create") {
        createData();
      } else {
        updateData();
      }
    };

    const handleApproveProcess = async (data: ApproveData) => {
      console.log("Approve process");
      try {
        setLoadingApprove(true);
        await changeEventApi.approve(selectedChangeEvent!.id || 0, data);
        message.success("Duyệt sự kiện thay đổi thành công!");
        await getOneChangeEvent(selectedChangeEvent!.id || 0);
        setCommentRefreshTrigger((prev) => prev + 1);
      } finally {
        setLoadingApprove(false);
      }
    };

    const handleRejectProcess = async (data: ApproveData) => {
      console.log("Reject process");
      try {
        setLoadingApprove(true);
        await changeEventApi.reject(selectedChangeEvent!.id || 0, data);
        message.success("Từ chối sự kiện thay đổi thành công!");
        await getOneChangeEvent(selectedChangeEvent!.id || 0);
        setCommentRefreshTrigger((prev) => prev + 1);
      } finally {
        setLoadingApprove(false);
      }
    };

    const pageTitle = useMemo(
      () =>
        status == "create"
          ? "Tạo sự kiện thay đổi"
          : "Chỉnh sửa sự kiện thay đổi",
      [status]
    );

    const canEditRecord = (record: ChangeEvent) => {
      if (!record) return false;
      return checkEditPermissionByCreator(
        userStore.info.id,
        record.createdBy?.id,
        haveEditPermission,
        haveViewAllPermission
      );
    };

    return (
      <div className="app-container">
        <PageTitle
          back
          breadcrumbs={[
            { label: "Báo cáo" },
            {
              label: "Sự kiện thay đổi",
              href: `/report/${PermissionNames.eventLogList}`,
            },
            { label: pageTitle },
          ]}
          title={pageTitle}
          // extra={
          //   selectedService &&
          //   status == "update" && (
          //     <Space>
          //       <ActiveStatusTagSelect
          //         disabled={readonly}
          //         isActive={selectedService?.isActive}
          //         onChange={(value) => {
          //           setSelectedService({
          //             ...selectedService,
          //             isActive: value,
          //           } as Service);
          //         }}
          //       />
          //     </Space>
          //   )
          // }
        />

        <Spin spinning={loadingFetch}>
          <Form
            layout="vertical"
            form={form}
            className={clsx(readonly ? "readonly" : "")}
            disabled={readonly}
          >
            <Row gutter={24}>
              <Col span={18}>
                <Card>
                  <Row gutter={16}>
                    <Col span={8}>
                      <Form.Item label="Mã" name="code">
                        <TextInput
                          disabled={status == "update"}
                          placeholder={
                            status == "create" ? "Để trống sẽ tự sinh" : ""
                          }
                        />
                      </Form.Item>
                    </Col>

                    <Col span={8}>
                      <Form.Item label="Tiêu đề" name="title" rules={rules}>
                        <Input placeholder="Tiêu đề" />
                      </Form.Item>
                    </Col>

                    <Col span={8}>
                      <Form.Item label="Phân loại" name="type" rules={rules}>
                        <Select
                          placeholder="Phân loại"
                          options={Object.values(TypeLabel).map((item) => ({
                            label: item.label,
                            value: item.value,
                          }))}
                        />
                      </Form.Item>
                    </Col>

                    <Col span={24}>
                      <Form.Item
                        label="Lý do"
                        name="description"
                        rules={descriptionRules}
                      >
                        <BMDCKEditor
                          placeholder="Lý do"
                          value={selectedChangeEvent?.description}
                          disabled={readonly}
                          inputHeight={300}
                          onChange={(content) => {
                            form.setFieldsValue({ description: content });
                          }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={24}>
                      <Form.Item
                        label="Nội dung thay đổi"
                        name="contentChange"
                        rules={descriptionRules}
                      >
                        <BMDCKEditor
                          placeholder="Nội dung thay đổi"
                          value={selectedChangeEvent?.contentChange}
                          disabled={readonly}
                          inputHeight={300}
                          onChange={(content) => {
                            form.setFieldsValue({ contentChange: content });
                          }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Tabs defaultActiveKey="0" type="line">
                    <Tabs.TabPane tab="Tệp đính kèm" key="0">
                      <Form.Item
                        shouldUpdate={true}
                        style={{ marginBottom: 0, height: "100%" }}
                        className="form-height-full"
                      >
                        {() => {
                          return (
                            <Form.Item
                              label={""}
                              noStyle
                              style={{ marginBottom: 0 }}
                              name="files"
                              className="h-full "
                            >
                              <FileUploadMultiple2
                                className="h-full"
                                fileList={fileList}
                                onUploadOk={(file) => {
                                  fileList.push(file);
                                  setFileList([...fileList]);
                                }}
                                onDelete={(file) => {
                                  const findIndex = fileList.findIndex(
                                    (e) => e.uid == file.uid
                                  );

                                  if (findIndex > -1) {
                                    fileList.splice(findIndex, 1);
                                    setFileList([...fileList]);
                                  }
                                }}
                                hideUploadButton={readonly}
                              />
                            </Form.Item>
                          );
                        }}
                      </Form.Item>
                    </Tabs.TabPane>
                    {selectedChangeEvent && (
                      <Tabs.TabPane tab="Bình luận" key="1">
                        <CommentView
                          initQuery={{
                            changeEventId: selectedChangeEvent!?.id,
                          }}
                          refreshTrigger={commentRefreshTrigger}
                        />
                      </Tabs.TabPane>
                    )}
                  </Tabs>

                  <div className="flex gap-[16px] justify-end mt-2">
                    {!readonly && (
                      <CustomButton
                        variant="outline"
                        className="cta-button"
                        onClick={() => {
                          if (status == "create") {
                            navigate(`/report/${PermissionNames.eventLogList}`);
                          } else {
                            setReadonly(true);
                            setDataToForm(selectedChangeEvent!);
                          }
                        }}
                      >
                        Hủy
                      </CustomButton>
                    )}

                    <CustomButton
                      className="cta-button"
                      loading={loading}
                      onClick={() => {
                        if (!readonly) {
                          handleSubmit();
                        } else {
                          setReadonly(false);
                        }
                      }}
                      disabled={
                        status == "update" &&
                        (!selectedChangeEvent ||
                          !canEditRecord(selectedChangeEvent))
                      }
                    >
                      {status == "create"
                        ? "Tạo sự kiện thay đổi"
                        : readonly
                        ? "Chỉnh sửa"
                        : "Lưu chỉnh sửa"}
                    </CustomButton>
                  </div>
                </Card>
              </Col>

              <Col span={6}>
                <ApprovalStepsCard
                  steps={approvalSteps}
                  loading={loadingApprove}
                  onSelectStep={setApprovalSteps}
                  onRemove={setRemoveApprovalList}
                  onApprove={handleApproveProcess}
                  onReject={handleRejectProcess}
                  templateType={ApprovalTemplateType.ChangeEvent}
                  editable={true}
                />

                <FollowerSelector
                  followers={followers}
                  setFollowers={setFollowers}
                  readonly={readonly}
                  headerTitle={`Người theo dõi (${followers?.length})`}
                />
              </Col>
            </Row>
          </Form>
        </Spin>
      </div>
    );
  }
);

export default CreateOrUpdateEventLogPage;
