import { Component, ComponentShowType } from "./component";
import { Material, MaterialEnum } from "./material";
import { FileAttach } from "./fileAttach";
import { MaterialType } from "./materialType";

export interface Variant {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  code: string;
  privateName: string;
  name: string;
  frontImage: string; // tỉ lệ 4:5
  backImage: string; // tỉ lệ 4:5
  material: Material;
  component: Component;
  fileAttachBackImage: FileAttach;
  fileAttachFrontImage: FileAttach;
  featureImageShowType: ComponentShowType;
}
export interface CreatingVariant extends Variant {
  // tỉ lệ 4:5
  materialId: number;
  componentId: number;
  materialType?: number;
}
