// This file combines Create and Edit Indicative Page into a single component
import {
  Card,
  Col,
  Row,
  Form,
  Spin,
  Tabs,
  Select,
  message,
  Input,
  Tag,
  DatePicker,
  Tooltip,
} from "antd";
import PageTitle from "components/PageTitle/PageTitle";
import React, { useEffect, useMemo, useState } from "react";
import { Rule } from "antd/lib/form";
import CustomButton from "components/Button/CustomButton";
import { ModalStatus } from "types/modal";
import {
  Instruction,
  InstructionType,
  InstructionTypeTrans,
} from "types/instruction";
import { PermissionNames } from "types/PermissionNames";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { FileAttach } from "types/fileAttach";
import { instructionApi } from "api/instruction.api";
import { isEmpty } from "lodash";
import { getTitle } from "utils";
import { FileUploadMultiple2 } from "components/Upload/FileUploadMultiple2";
import { ProjectSelector } from "components/Selector/ProjectSelector";
import { useWatch } from "antd/es/form/Form";
import { fileAttachApi } from "api/fileAttach.api";
import { $url } from "utils/url";
import dayjs from "dayjs";
import { ProviderSelector } from "components/Selector/ProviderSelector";
import { ProviderModule } from "types/provider";
import { Staff } from "types/staff";
import { CustomizableColumn } from "components/Table/CustomizableTable";
import { InfoCircleOutlined, StopOutlined } from "@ant-design/icons";
import { WorkStatusTrans } from "types/workStatus";
import { useStaff2 } from "hooks/useStaff2";
import { FollowerSelector } from "../../components/Follower/FollowerSelector";
import {
  ApprovalStepsCard,
  ApproveData,
  StepItem,
} from "../../components/ApproveProcess/ApprovalStepsCard";
import clsx from "clsx";
import { observer } from "mobx-react";
import { checkEditPermissionByCreator, checkRoles } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import TextArea from "antd/es/input/TextArea";
import { approvalListApi } from "api/approvalList.api";
import { ApprovalListStatus, ApprovalListType } from "types/approvalList";
import { transformApproveData } from "components/ApproveProcess/approveUtil";
import { CommentView } from "components/Comment/CommentView";
import { settings } from "settings";
import { appStore } from "store/appStore";
import { approvalTemplateApi } from "api/approvalTemplate.api";
import { MembershipSelector } from "components/Selector/MembershipSelector";
import {
  ApprovalTemplate,
  ApprovalTemplateName,
  ApprovalTemplateType,
} from "types/approvalTemplate";
import { MemberShip } from "types/memberShip";
import { useMemberShip } from "hooks/useMemberShip";
import { userStore } from "store/userStore";
import { toJS } from "mobx";
import { useApprovalStep } from "hooks/useAppovalStep";
import { ProjectScheduleSelector } from "components/Selector/ProjectScheduleSelector";
import { BMDTextArea } from "components/TextArea/BMDTextArea";
import { BMDCKEditor } from "components/Editor";

const rules: Rule[] = [{ required: true, message: "Trường này là bắt buộc" }];

interface EditInstructionPageProps {
  title: string;
  status: ModalStatus;
}

interface InstructionForm extends Instruction {
  projectId: number;
  providerId: number;
  receivedMemberShipById: number;
  // createdMemberShipById: number;
  projectItemId: number;
  scheduleId: number;
}

const columns: CustomizableColumn<Staff>[] = [
  {
    title: "Mã NV",
    dataIndex: "code",
    key: "code",
    width: 70,
    sorter: true,

    render: (_, record) => (
      <div className="text-[#1677ff] cursor-pointer">{record.code}</div>

      // <Link
      //   to={`/master-data/${PermissionNames.staffEdit.replace(
      //     ":id",
      //     record.id + ""
      //   )}`}
      // >
      //   {record.code}
      // </Link>
    ),
    defaultVisible: true,
  },
  {
    title: "Nhân viên",
    dataIndex: "fullName",
    key: "fullName",
    sorter: true,
    width: 200,
    render: (_, record) => (
      <>
        {record.isBlocked ? (
          <div className="flex items-center gap-2">
            <div>
              <div className="font-medium text-red-500">{record?.fullName}</div>
            </div>
            <StopOutlined style={{ color: "red" }} />
          </div>
        ) : (
          <div className="flex items-center gap-2">
            <div>
              <div className="font-medium">{record?.fullName}</div>
            </div>
          </div>
        )}
      </>
    ),
    defaultVisible: true,
  },
  {
    title: "SĐT",
    dataIndex: "phone",
    key: "phone",
    width: 150,
    sorter: true,

    render: (_, record) => record.phone || "-",
    defaultVisible: true,
  },
  {
    title: "Email",
    dataIndex: "email",
    key: "email",
    width: 250,
    sorter: true,

    render: (_, record) => record.email || "-",
    defaultVisible: true,
  },
  {
    title: "Phòng ban",
    dataIndex: "department",
    key: "department",
    width: 180,
    sorter: true,

    render: (_, record) => record.department?.name || "-",
    defaultVisible: true,
  },
  {
    title: "Chức vụ",
    dataIndex: "jobTitle",
    key: "jobTitle",
    width: 150,
    sorter: true,

    render: (_, record) => record.jobTitle?.name || "-",
    defaultVisible: true,
  },
  {
    title: "Cấp bậc",
    dataIndex: "level",
    key: "level",
    width: 120,
    sorter: true,

    render: (_, record) => record.level?.name || "-",
    defaultVisible: true,
  },
  {
    title: "Trạng thái",
    dataIndex: "workStatus",
    key: "staff.workStatus",
    align: "center",
    width: 120,
    render: (_, record) => (
      <div className="flex justify-center">
        <Tag
          className="!mr-0"
          color={WorkStatusTrans[record.workStatus]?.color || "gray"}
        >
          {WorkStatusTrans[record.workStatus]?.label || "Không xác định"}
        </Tag>
      </div>
    ),
    defaultVisible: true,
  },
];

function CreateOrUpdateInstructionPage({
  title = "",
  status,
}: EditInstructionPageProps) {
  const { haveEditPermission, haveViewAllPermission } = checkRoles(
    {
      edit: PermissionNames.indicativeEdit,
      viewAll: PermissionNames.indicativeViewAll,
    },
    permissionStore.permissions
  );

  const [form] = Form.useForm<InstructionForm>();
  const [loading, setLoading] = useState(false);
  const [selectedInstruction, setSelectedInstruction] = useState<Instruction>();
  3;
  const [fileList, setFileList] = useState<FileAttach[]>([]);
  const navigate = useNavigate();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedMemberShips, setSelectedMemberShips] = useState<MemberShip[]>(
    []
  );
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);

  const {
    followers,
    setFollowers,
    approvalSteps,
    setApprovalSteps,
    fetchApprovalTemplate,
  } = useApprovalStep();

  const [removeApprovalList, setRemoveApprovalList] = useState<number[]>([]);
  const [loadingApprove, setLoadingApprove] = useState(false);
  const [commentRefreshTrigger, setCommentRefreshTrigger] = useState(0);

  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys: React.Key[], selectedRows: MemberShip[]) => {
      setSelectedRowKeys(selectedKeys as number[]);
      setSelectedMemberShips(selectedRows);
    },
  };

  const handleAddFollowers = () => {
    setIsModalVisible(true);
  };

  const [readonly, setReadonly] = useState(true);
  const [loadingFetch, setLoadingFetch] = useState(false);
  const params = useParams();
  const [searchParams, setSearchParams] = useSearchParams();
  const type = useWatch("type", form);
  const setDataToForm = (data: Instruction) => {
    form.setFieldsValue({
      ...data,
      projectId: data.project?.id,
      providerId: data.provider?.id,
      scheduleId: data.schedule?.id,
      receivedMemberShipById: data.receivedMemberShipBy?.id,
      createdMemberShipById: data.createdMemberShipBy?.id,
      //@ts-ignore
      createdDate: data?.createdDate
        ? dayjs(data?.createdDate, "YYYY-MM-DD")
        : undefined,
      //@ts-ignore

      issuedDate: data?.issuedDate
        ? dayjs(data?.issuedDate, "YYYY-MM-DD")
        : undefined,
    });

    setFileList(data.fileAttaches ? [...data.fileAttaches] : []);
    // debugger;
    const transformedApproveData = transformApproveData(
      data.approvalLists,
      data.createdBy
    );
    setApprovalSteps(transformedApproveData);
    setFollowers(data.followMemberShips || []);
  };

  const descriptionRules: Rule[] = [{ required: false }];

  const canEditRecord = (record: Instruction) => {
    if (!record) return null;
    return checkEditPermissionByCreator(
      userStore.info.id,
      record.createdBy?.id,
      haveEditPermission,
      haveViewAllPermission
    );
  };

  const getOneInstruction = async (id: number) => {
    try {
      setLoadingFetch(true);
      const { data } = await instructionApi.findOne(id);

      if (isEmpty(data)) {
        navigate("/404");

        return;
      }

      setSelectedInstruction(data);
      setDataToForm(data);

      //   if (data.serviceType) {
      //     setServiceTypes([data.serviceType]);
      //   }

      return data as Instruction;
    } catch (e: any) {
      console.log({ e });
    } finally {
      setLoadingFetch(false);
    }
  };

  useEffect(() => {
    document.title = getTitle(title);

    if (status === "create") {
      setReadonly(false);

      if (!appStore.currentProject) {
        return;
      }

      // Set projectId mặc định khi tạo mới
      form.setFieldsValue({
        projectId: appStore.currentProject.id,
        address: appStore.currentProject.location,
      });

      fetchApprovalTemplate({
        projectId: appStore.currentProject.id,
        createdStaff: toJS(userStore.info) as Staff,
        type: ApprovalTemplateType.Instruction,
      });
    }

    if (status == "update") {
      const instructionId = params.id;
      if (instructionId) {
        getOneInstruction(+instructionId);
        setReadonly(searchParams.get("update") != "1");
      }
    } else {
      setReadonly(false);
    }
  }, []);

  const getDataSubmit = async () => {
    const {
      projectId,
      providerId,
      receivedMemberShipById,
      // createdMemberShipById,
      // issuedDate,
      createdDate,
      scheduleId,
      ...data
    } = form.getFieldsValue();

    const fileAttachIds: number[] = [];

    for (const file of fileList) {
      if (file.id) {
        fileAttachIds.push(file.id);
      } else if (file.originFile) {
        const { data } = await fileAttachApi.upload(file.originFile);

        const resFileAttach = await fileAttachApi.create({
          fileAttach: {
            ...file,
            url: $url(data.path),
          },
        });

        fileAttachIds.push(resFileAttach.data.id);
      }
    }

    const approvalLists = approvalSteps.map((step, i) => {
      const _approvalListDetails = step.approvers.map((approver, index) => {
        if (
          !approver.memberShipId &&
          step.name !== ApprovalTemplateName.Create
        ) {
          message.error("Vui lòng chọn người duyệt cho bước " + (i + 1));
          throw new Error("Vui lòng chọn người duyệt cho bước " + (i + 1));
        }
        return {
          position: index,
          memberShipId: approver.memberShipId,
          roleId: approver.roleId,
        };
      });
      return {
        id: step.id,
        name: step.name,
        type: ApprovalListType.Instruction,
        position: step.position,
        note: step.note,
        isAllApproval: step.isAllApproval,
        actionText: step.actionText,
        statusText: step.statusText,
        statusColor: step.statusColor,
        approvalListDetails: _approvalListDetails,
        // memberShipId: e.memberShipId,
        // memberShip2Id: e.memberShip2Id,
        instructionId: selectedInstruction!?.id || 0,
        staffId: step.staffId,
      };
    });

    // if (removeApprovalList.length) {
    //   for (let i = 0; i < removeApprovalList.length; i++) {
    //     const element = removeApprovalList[i];
    //     await approvalListApi.delete(element);
    //   }
    // }

    // if (approvalLists.length && status == "update") {
    //   const dataSubmit = {
    //     approvalLists: approvalLists,
    //   };
    //   await approvalListApi.batch(dataSubmit);
    // }

    const payload = {
      instruction: {
        ...data,
        address: appStore.currentProject?.location,
        // issuedDate: issuedDate ? dayjs(issuedDate).format("YYYY-MM-DD") : "",
        createdDate: createdDate ? dayjs(createdDate).format("YYYY-MM-DD") : "",

        isActive: selectedInstruction?.isActive,
      },
      fileAttachIds: fileAttachIds || [],
      projectId: projectId || 0,
      providerId: providerId || 0,
      receivedMemberShipById: receivedMemberShipById || 0,
      // createdMemberShipById: createdMemberShipById || 0,
      scheduleId: scheduleId || 0,
      followMemberShipIds: followers?.map((it) => it.id),
      approvalLists: approvalLists,
    };

    console.log(payload);

    return payload;
  };

  const createData = async () => {
    const valid = await form.validateFields();

    setLoading(true);
    try {
      const res = await instructionApi.create(await getDataSubmit());

      message.success("Tạo chỉ thị công trường thành công!");
      navigate(`/report/${PermissionNames.indicativeList}`);
      setFileList([]);
    } finally {
      setLoading(false);
    }
  };

  const updateData = async () => {
    const valid = await form.validateFields();

    setLoading(true);
    try {
      const res = await instructionApi.update(
        selectedInstruction!?.id || 0,
        await getDataSubmit()
      );
      setSelectedInstruction({ ...selectedInstruction, ...res.data });
      setRemoveApprovalList([]);

      message.success("Chỉnh sửa chỉ thị công trường thành công!");
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = () => {
    if (status == "create") {
      createData();
    } else {
      updateData();
    }
  };

  const handleApproveProcess = async (data: ApproveData) => {
    console.log("Approve process", data);
    try {
      setLoadingApprove(true);
      await instructionApi.approve(selectedInstruction!.id || 0, data);
      message.success("Duyệt chỉ thị công trường thành công!");
      await getOneInstruction(selectedInstruction!.id || 0);
      // Trigger refresh CommentView
      setCommentRefreshTrigger((prev) => prev + 1);
    } finally {
      setLoadingApprove(false);
    }
  };

  const handleRejectProcess = async (data: ApproveData) => {
    console.log("Reject process");
    try {
      setLoadingApprove(true);
      await instructionApi.reject(selectedInstruction!.id || 0, data);
      message.success("Từ chối chỉ thị công trường thành công!");
      await getOneInstruction(selectedInstruction!.id || 0);
      // Trigger refresh CommentView
      setCommentRefreshTrigger((prev) => prev + 1);
    } finally {
      setLoadingApprove(false);
    }
  };

  const handleDownloadTemplate = () => {
    console.log("Download template");
  };

  const pageTitle = useMemo(
    () =>
      status == "create"
        ? "Tạo chỉ thị công trường"
        : "Chỉnh sửa chỉ thị công trường",
    [status]
  );

  const validateIssuedDate = ({ getFieldValue }: any) => ({
    validator(_: any, value: any) {
      const createdDate = getFieldValue("createdDate");
      if (!value || !createdDate) {
        return Promise.resolve();
      }
      if (value.isBefore(createdDate, "day")) {
        return Promise.reject(
          new Error("Ngày phát chỉ thị phải sau ngày tạo chỉ thị")
        );
      }
      return Promise.resolve();
    },
  });

  return (
    <div className="app-container">
      <PageTitle
        back
        breadcrumbs={[
          { label: "Báo cáo" },
          {
            label: "Chỉ thị công trường",
            href: `/report/${PermissionNames.indicativeList}`,
          },
          { label: pageTitle },
        ]}
        title={pageTitle}
      />
      <Spin spinning={loadingFetch}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            createdDate: dayjs(),
            projectId: appStore.currentProject?.id,
          }}
          disabled={readonly}
          className={clsx(readonly ? "readonly" : "")}
        >
          <Row gutter={24}>
            <Col span={18}>
              <Card className="content-card ">
                <Card title="Thông tin cơ bản" className="mb-0 form-card">
                  <Row gutter={16}>
                    {/* First Row */}
                    <Col span={8}>
                      <Form.Item name="code" label="Số chỉ thị" rules={rules}>
                        <Input placeholder="Số chỉ thị" />
                      </Form.Item>
                    </Col>

                    <Col span={16}>
                      <Form.Item name="name" label="Tiêu đề" rules={rules}>
                        <Input placeholder="Tiêu đề" />
                      </Form.Item>
                    </Col>
                    <Col span={24}>
                      <Form.Item
                        name="content"
                        label="Nội dung chỉ thị"
                        rules={descriptionRules}
                      >
                        <BMDCKEditor
                          placeholder="Nhập nội dung chỉ thị"
                          value={selectedInstruction?.content}
                          disabled={readonly}
                          inputHeight={300}
                          onChange={(content) => {
                            form.setFieldsValue({ content: content });
                          }}
                        />
                      </Form.Item>
                    </Col>

                    {/* Second Row */}
                    <Col span={8}>
                      <Form.Item name="projectId" label="Dự án">
                        <ProjectSelector
                          placeholder="Chọn dự án"
                          value={appStore.currentProject?.id}
                          disabled={true}
                          onChange={({ value, item }) => {
                            console.log(item);
                            form.setFieldsValue({
                              address: item.item?.location,
                              projectId: value,
                            });
                          }}
                        />
                      </Form.Item>
                    </Col>

                    <Col span={8}>
                      <Form.Item
                        name="scheduleId"
                        label={
                          <span>
                            Hạng mục công việc&nbsp;
                            <Tooltip title="Chọn từ tiến độ công việc">
                              <InfoCircleOutlined
                                style={{ color: "#1890ff" }}
                              />
                            </Tooltip>
                          </span>
                        }
                        // rules={rules}
                      >
                        <ProjectScheduleSelector placeholder="Chọn hạng mục công việc" />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item name="type" label="Phân loại" rules={rules}>
                        <Select
                          placeholder="Phân loại"
                          options={Object.values(InstructionTypeTrans).map(
                            (item) => ({
                              label: item.label,
                              value: item.value,
                            })
                          )}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={24}>
                      <Form.Item
                        name="description"
                        label="Mô tả chi phí"
                        rules={descriptionRules}
                      >
                        <BMDCKEditor
                          placeholder="Mô tả chi phí"
                          value={selectedInstruction?.description}
                          disabled={readonly}
                          inputHeight={300}
                          onChange={(content) => {
                            form.setFieldsValue({ description: content });
                          }}
                        />
                      </Form.Item>
                    </Col>
                    {/* <Col span={8}>
                      <Form.Item
                        name="createdMemberShipById"
                        label="Người phát hành"
                      >
                        <MembershipSelector placeholder="Người phát hành" />
                      </Form.Item>
                    </Col> */}

                    {/* Second Row */}
                    <Col span={8}>
                      <Form.Item name="link" label="Link tài liệu">
                        <Input placeholder="Link tài liệu" />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        name="receivedMemberShipById"
                        label="Người nhận"
                        rules={type == InstructionType.Directive ? rules : []}
                      >
                        <MembershipSelector placeholder="Người nhận" />
                      </Form.Item>
                    </Col>

                    {/* Fourth Row */}

                    <Col span={8}>
                      <Form.Item name="createdDate" label="Ngày tạo chỉ thị">
                        <DatePicker
                          className="w-full"
                          format={settings.dateFormat}
                          disabled={readonly}
                          placeholder="Ngày tạo chỉ thị"
                        />
                      </Form.Item>
                    </Col>

                    {/** ISSUE 7847 */}
                    {/* <Col span={8}>
                      <Form.Item
                        name="issuedDate"
                        label="Ngày phát chỉ thị"
                        dependencies={["createdDate"]}
                        rules={[
                          validateIssuedDate,
                          {
                            required: true,
                            message: "Vui lòng chọn ngày phát chỉ thị",
                          },
                        ]}
                      >
                        <DatePicker
                          placeholder="Ngày phát chỉ thị"
                          className="w-full"
                          format="DD/MM/YYYY"
                          disabledDate={(current) => {
                            const createdDate =
                              form.getFieldValue("createdDate");
                            return (
                              current &&
                              createdDate &&
                              current.isBefore(createdDate, "day")
                            );
                          }}
                        />
                      </Form.Item>
                    </Col> */}

                    {/* Third Row */}
                    <Col span={8}>
                      <Form.Item name="providerId" label="Nhà thầu phụ">
                        <ProviderSelector
                          placeholder="Chọn thầu phụ"
                          initQuery={{
                            module: ProviderModule.SubContractor,
                          }}
                        />
                      </Form.Item>
                    </Col>

                    <Col span={8}>
                      {/* <Form.Item name="address" label="Địa chỉ">
                        <Input
                          placeholder="Lấy từ thông tin dự án"
                          value={appStore.currentProject?.location}
                        />
                      </Form.Item> */}
                      <div className="flex flex-col">
                        <label
                          className="font-bold text-[13px] mb-1" // Mimic Ant Design label styling
                          htmlFor="address-display"
                        >
                          Địa chỉ
                        </label>
                        <span
                          id="address-display"
                          className="text-[13px] text-gray-700"
                        >
                          {appStore.currentProject?.location ||
                            "Không có địa chỉ"}
                        </span>
                      </div>
                    </Col>

                    <Col span={8}>
                      <Form.Item
                        name="contractCode"
                        label="Hợp đồng nhà thầu phụ"
                      >
                        <Input placeholder="Hợp đồng" />
                      </Form.Item>
                    </Col>

                    {/* <Col span={6}>
                        <Form.Item name="status" label="Trạng thái duyệt">
                          <CustomSelect
                            placeholder="Trạng thái duyệt"
                            options={statusOptions}
                          />
                        </Form.Item>
                      </Col> */}

                    {/* File Upload Section */}

                    {/* <Col span={24}>
                      <Form.Item
                        shouldUpdate={true}
                        style={{ marginBottom: 0, height: "100%" }}
                      >
                        {() => {
                          return (
                            <Form.Item
                              label={""}
                              noStyle
                              style={{ marginBottom: 0 }}
                              name="files"
                              className="h-full"
                            >
                              <FileUploadMultiple2
                                className="h-full"
                                fileList={fileList}
                                onUploadOk={(file) => {
                                  fileList.push(file);
                                  setFileList([...fileList]);
                                }}
                                onDelete={(file) => {
                                  const findIndex = fileList.findIndex(
                                    (e) => e.uid == file.uid
                                  );

                                  if (findIndex > -1) {
                                    fileList.splice(findIndex, 1);
                                    setFileList([...fileList]);
                                  }
                                }}
                                hideUploadButton={readonly}
                                showSearch
                              />
                            </Form.Item>
                          );
                        }}
                      </Form.Item>
                    </Col> */}
                  </Row>
                </Card>

                <Tabs defaultActiveKey="0" type="line" className="mt-[16px]">
                  <Tabs.TabPane tab="Tệp đính kèm" key="0">
                    <Form.Item
                      shouldUpdate={true}
                      style={{ marginBottom: 0, height: "100%" }}
                      className="form-height-full"
                    >
                      {() => {
                        return (
                          <Form.Item
                            label={""}
                            noStyle
                            style={{ marginBottom: 0 }}
                            name="files"
                            className="h-full "
                          >
                            <FileUploadMultiple2
                              hideUploadButton={readonly}
                              showSearch
                              className="h-full"
                              fileList={fileList}
                              onUploadOk={(file) => {
                                fileList.push(file);
                                setFileList([...fileList]);
                              }}
                              onDelete={(file) => {
                                const findIndex = fileList.findIndex(
                                  (e) => e.uid == file.uid
                                );

                                if (findIndex > -1) {
                                  fileList.splice(findIndex, 1);
                                  setFileList([...fileList]);
                                }
                              }}
                            />
                          </Form.Item>
                        );
                      }}
                    </Form.Item>
                  </Tabs.TabPane>
                  {selectedInstruction && readonly && (
                    <Tabs.TabPane tab="Bình luận" key="1">
                      <CommentView
                        initQuery={{ instructionId: selectedInstruction.id }}
                        refreshTrigger={commentRefreshTrigger}
                      />
                    </Tabs.TabPane>
                  )}
                </Tabs>

                {/* Action Buttons */}
                <div
                  className="mt-[16px]"
                  style={{
                    display: "flex",
                    justifyContent: "flex-end",
                    gap: "12px",
                  }}
                >
                  {!readonly && (
                    <CustomButton
                      variant="outline"
                      className="cta-button"
                      onClick={() => {
                        if (status == "create") {
                          navigate(`/report/${PermissionNames.indicativeList}`);
                        } else {
                          setDataToForm(selectedInstruction!);
                          setReadonly(true);
                        }
                      }}
                    >
                      Hủy
                    </CustomButton>
                  )}
                  <CustomButton
                    className="cta-button"
                    loading={loading}
                    onClick={() => {
                      if (!readonly) {
                        handleSubmit();
                      } else {
                        setReadonly(false);
                      }
                    }}
                    disabled={
                      status == "update" &&
                      (!selectedInstruction ||
                        !canEditRecord(selectedInstruction))
                    }
                  >
                    {status == "create"
                      ? "Tạo chỉ thị công trường"
                      : readonly
                      ? "Chỉnh sửa"
                      : "Lưu chỉnh sửa"}
                  </CustomButton>
                </div>
              </Card>
            </Col>

            <Col span={6}>
              <ApprovalStepsCard
                steps={approvalSteps}
                loading={loadingApprove}
                onSelectStep={setApprovalSteps}
                onRemove={setRemoveApprovalList}
                onApprove={handleApproveProcess}
                onReject={handleRejectProcess}
                templateType={ApprovalTemplateType.Instruction}
                editable={true}
                isShowActionButton={status == "update"}
              />

              <FollowerSelector
                followers={followers}
                setFollowers={setFollowers}
                readonly={readonly}
                headerTitle={`Người theo dõi (${followers?.length})`}
              />
            </Col>
          </Row>
        </Form>
      </Spin>
    </div>
  );
}

export default observer(CreateOrUpdateInstructionPage);
