import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const projectGroupApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/projectGroup",
      params,
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/projectGroup/${id}`,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/projectGroup",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/projectGroup/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/projectGroup/${id}`,
      method: "delete",
    }),
  import: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/projectGroup/import",
      method: "post",
      data,
    }),
};
