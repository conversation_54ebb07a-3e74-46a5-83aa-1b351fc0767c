import { cityApi } from "api/city.api";
import { debounce } from "lodash";
import { useCallback, useRef, useState } from "react";
import { City } from "types/address";

import { QueryParam } from "types/query";

export interface CityQuery extends QueryParam {
  // storeId?: number;
}
interface IQueryProps {
  initQuery: CityQuery;
}

export const useCity = ({ initQuery }: IQueryProps) => {
  const [data, setData] = useState<City[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const query = useRef<CityQuery>(initQuery);

  const fetchData = async () => {
    setLoading(true);
    const { data } = await cityApi.findAll();

    setData(data.cities);
    setTotal(data.total);
    setLoading(false);
    return data.cities as City[];
  };

  const debounceFetchCity = useCallback(() => {
    debounce(() => fetchData(), 1000);
  }, [query]);

  return {
    cities: data,
    totalCity: total,
    fetchCity: fetchData,
    cityLoading: loading,
    queryCity: query.current,
    searchDebounce: debounceFetchCity,
  };
};
