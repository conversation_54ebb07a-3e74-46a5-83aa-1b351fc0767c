import { DownloadOutlined, InboxOutlined } from "@ant-design/icons";
import { <PERSON>ert, Modal, Space, Spin, Table, Upload, message } from "antd";
import { Rule } from "antd/es/form";
import { serviceApi } from "api/service.api";
import { readerData } from "utils/excel2";
import CustomButton from "components/Button/CustomButton";
import {
  forwardRef,
  useImperativeHandle,
  useState,
  useEffect,
  useCallback,
} from "react";
import { boqApi } from "api/boq.api";
import { BOQDetail, BOQDetailType } from "types/boq";
import ImportPreviewModule from "./ImportPreviewModule";
import { validateNumber } from "utils/validateRule";
import { Link } from "react-router-dom";
import { buildTreeByLevel } from "utils/data";

const { Dragger } = Upload;

const rules: Rule[] = [{ required: true }];

export interface ImportBOQDetailsModal {
  open: () => void;
  close: () => void;
}

export interface IValidate {
  index: number;
  message: string;
}

interface DataImportReturn {
  msg: string;
  rowNum: number;
  status: string;
}

export interface BOQDetailImport
  extends Omit<
    BOQDetail,
    "unit" | "workType" | "unitConvert" | "projectItem" | "children"
  > {
  unit: string;
  workType: string;
  unitConvert: string;
  projectItem: string;
  ref: string;
  parentRef: string;
  numericRef?: string; // Numeric ref for UI display (1, 1.1, 1.1.1)
  rowNum: number;
  children?: BOQDetailImport[];
  errorMessage?: string; // For validation errors
}

interface IProps {
  onSuccess?: () => void;
  createApi?: (data: any) => any;
  onUploaded?: (excelData: any, setData: (data: any[]) => void) => void;
  demoExcel?: string;
  guide?: React.ReactNode[];
  uploadText?: string;
  okText?: string;
  onClose?: () => void;
  titleText?: string;
  validateMessage?: IValidate[];
  onDownloadDemoExcel?: () => void;
  loadingDownloadDemo?: boolean;
  onCustomImport?: (data: BOQDetailImport[]) => Promise<void>;
}

const ImportBOQDetails = forwardRef((props: IProps, ref) => {
  const {
    onSuccess,
    createApi,
    onUploaded,
    onClose,
    validateMessage,
    guide,
    demoExcel,
    uploadText = "Kéo thả hoặc click vào đây để upload file",
    okText = "Nhập dữ liệu ngay",
    titleText = "Nhập excel dữ liệu",
    onDownloadDemoExcel,
    loadingDownloadDemo,
    onCustomImport,
  } = props;

  const [visible, setVisible] = useState(false);
  const [dataPosts, setDataPosts] = useState<BOQDetailImport[]>([]);
  const [validatedDataPosts, setValidatedDataPosts] = useState<
    BOQDetailImport[]
  >([]);
  const [dataReturn, setDataReturn] = useState<{
    data: DataImportReturn[];
    successCount: number;
    errorCount: number;
  }>();
  const [loading, setLoading] = useState(false);
  const [hasValidationErrors, setHasValidationErrors] = useState(false);

  useImperativeHandle(ref, () => ({
    open: () => setVisible(true),
    close: () => setVisible(false),
  }));

  useEffect(() => {
    if (validateMessage?.length) {
      setDataReturn(undefined);
    }
  }, [validateMessage]);

  const handleImport = async () => {
    if (!validatedDataPosts.length) return;

    try {
      setLoading(true);
      // Build tree data trước khi import
      if (onCustomImport) {
        await onCustomImport(validatedDataPosts);
        handleClose();
        return;
      }
      const treeData = buildTreeByLevel([...validatedDataPosts]);

      // Flatten tree thành mảng để import từng item (nếu API chỉ nhận từng dòng)
      const flatten = (arr: any[], result: any[] = []) => {
        arr.forEach((item) => {
          const { children, ...rest } = item;
          result.push(rest);
          if (children && children.length) flatten(children, result);
        });
        return result;
      };
      const flatData = flatten(treeData);

      // Import từng item (theo thứ tự tree)
      const results = await Promise.all(
        flatData.map(async (item) => {
          try {
            await boqApi.create(item);
            return { status: "ok", msg: "Thành công", rowNum: item.rowNum };
          } catch (error: any) {
            return {
              status: "error",
              msg: error.response?.data?.message || "Có lỗi xảy ra",
              rowNum: item.rowNum,
            };
          }
        })
      );

      const data = results;
      const successCount = data.filter((d: any) => d.status === "ok").length;
      const errorCount = data.filter((d: any) => d.status === "error").length;

      setDataReturn({ data, successCount, errorCount });
      setDataPosts([]);
      setValidatedDataPosts([]);
      onSuccess?.();
    } catch (err) {
      console.error("Import error", err);
      message.error("Đã xảy ra lỗi trong quá trình nhập dữ liệu");
    } finally {
      setLoading(false);
    }
  };

  // Define preview columns for the table
  const previewColumns = [
    {
      key: "ref",
      title: "Ref *",
      dataIndex: "ref",
      width: 80,
      fixed: "left",
      render: (text: string) => (
        <span className={!text ? "text-red-500" : ""}>{text || "Thiếu"}</span>
      ),
    },
    {
      key: "parentRef",
      title: "Ref cha",
      dataIndex: "parentRef",
      width: 80,
      fixed: "left",
      render: (text: string) => (
        <span className={!text ? "text-gray-400" : ""}>{text || ""}</span>
      ),
    },
    {
      key: "name",
      title: "Tên *",
      dataIndex: "name",
      width: 150,
      render: (text: string, record: any) => (
        <span className={!text ? "text-red-500" : ""}>{text || "Thiếu"}</span>
      ),
    },
    {
      key: "technical",
      title: "Tiêu chuẩn kỹ thuật",
      dataIndex: "technical",
      width: 120,
      render: (text: string) => (
        <span className={!text ? "text-gray-400" : ""}>{text || ""}</span>
      ),
    },
    {
      key: "workType",
      title: "Mã loại công tác",
      dataIndex: "workType",
      width: 120,
      render: (text: string) => (
        <span className={!text ? "text-gray-400" : ""}>{text || ""}</span>
      ),
    },
    {
      key: "projectItem",
      title: "Hạng mục",
      dataIndex: "projectItem",
      width: 120,
      render: (text: string) => (
        <span className={!text ? "text-gray-400" : ""}>{text || ""}</span>
      ),
    },
    {
      key: "floors",
      title: "Tầng",
      dataIndex: "floors",
      width: 80,
      render: (text: string) => (
        <span className={!text ? "text-gray-400" : ""}>{text || ""}</span>
      ),
    },
    {
      key: "unit",
      title: "Đơn vị",
      dataIndex: "unit",
      width: 100,
      render: (text: string) => (
        <span className={!text ? "text-gray-400" : ""}>{text || ""}</span>
      ),
    },
    {
      key: "lengthMm",
      title: "Dài",
      dataIndex: "lengthMm",
      width: 80,
      render: (text: string) => (
        <span className={!text ? "text-gray-400" : ""}>{text || ""}</span>
      ),
    },
    {
      key: "widthMm",
      title: "Rộng",
      dataIndex: "widthMm",
      width: 80,
      render: (text: string) => (
        <span className={!text ? "text-gray-400" : ""}>{text || ""}</span>
      ),
    },
    {
      key: "heightMm",
      title: "Cao",
      dataIndex: "heightMm",
      width: 80,
      render: (text: string) => (
        <span className={!text ? "text-gray-400" : ""}>{text || ""}</span>
      ),
    },
    {
      key: "diameterMm",
      title: "Đường kính",
      dataIndex: "diameterMm",
      width: 100,
      render: (text: string) => (
        <span className={!text ? "text-gray-400" : ""}>{text || ""}</span>
      ),
    },
    {
      key: "quantity",
      title: "Số lượng",
      dataIndex: "quantity",
      width: 100,
      render: (text: string) => (
        <span className={!text ? "text-gray-400" : ""}>{text || ""}</span>
      ),
    },
    {
      key: "materialCost",
      title: "Đơn giá vật tư",
      dataIndex: "materialCost",
      width: 120,
      render: (text: string) => (
        <span className={!text ? "text-gray-400" : ""}>{text || ""}</span>
      ),
    },
    {
      key: "labor",
      title: "Đơn giá nhân công",
      dataIndex: "labor",
      width: 120,
      render: (text: string) => (
        <span className={!text ? "text-gray-400" : ""}>{text || ""}</span>
      ),
    },
    {
      key: "unitConvert",
      title: "Đơn vị tính (quy đổi)",
      dataIndex: "unitConvert",
      width: 140,
      render: (text: string) => (
        <span className={!text ? "text-gray-400" : ""}>{text || ""}</span>
      ),
    },
    {
      key: "quantityConvert",
      title: "Số lượng (quy đổi)",
      dataIndex: "quantityConvert",
      width: 120,
      render: (text: string) => (
        <span className={!text ? "text-gray-400" : ""}>{text || ""}</span>
      ),
    },
    {
      key: "priceConvert",
      title: "Đơn giá (quy đổi)",
      dataIndex: "priceConvert",
      width: 120,
      render: (text: string) => (
        <span className={!text ? "text-gray-400" : ""}>{text || ""}</span>
      ),
    },
    {
      key: "note",
      title: "Ghi chú",
      dataIndex: "note",
      width: 120,
      render: (text: string) => (
        <span className={!text ? "text-gray-400" : ""}>{text || ""}</span>
      ),
    },
    {
      key: "errorMessage",
      title: "Lỗi",
      dataIndex: "errorMessage",
      width: 300,
      render: (text: string) => (
        <span className="text-red-500 whitespace-pre-line text-xs">{text}</span>
      ),
    },
  ];

  const requiredFields: (keyof BOQDetailImport)[] = ["ref", "name"];

  const performFullValidation = useCallback(
    (data: BOQDetailImport[]): BOQDetailImport[] => {
      const normalizedData = data.map((item) => ({
        ...item,
        ref: item.ref != null ? String(item.ref).trim() : "",
        parentRef: item.parentRef != null ? String(item.parentRef).trim() : "",
      }));

      // Tạo refMap để kiểm tra ref tồn tại và mối quan hệ
      const refMap: Record<string, BOQDetailImport> = {};
      normalizedData.forEach((item) => {
        if (item.ref) {
          refMap[item.ref] = item;
        }
      });

      // Hàm kiểm tra vòng lặp cha-con
      const checkCircular = (
        currentRef: string,
        visited = new Set<string>()
      ): boolean => {
        if (visited.has(currentRef)) return true;
        visited.add(currentRef);
        const parent = refMap[currentRef]?.parentRef;
        if (parent) return checkCircular(parent, visited);
        return false;
      };

      // Validate từng item
      return normalizedData.map((item) => {
        const errors: string[] = [];

        // Loại BOQ
        let itemType = item.type;
        if (!item.parentRef) {
          itemType = BOQDetailType.GROUP;
        }

        // Validate Ref
        if (!item.ref) {
          errors.push("Ref không được để trống");
        } else {
          const duplicates = normalizedData.filter((i) => i.ref === item.ref);
          if (duplicates.length > 1) {
            errors.push("Ref phải là duy nhất");
          }
        }

        // Validate parentRef
        if (item.parentRef) {
          if (!refMap[item.parentRef]) {
            errors.push(`Ref cha '${item.parentRef}' không tồn tại`);
          }
          if (checkCircular(item.ref)) {
            errors.push("Phát hiện tham chiếu vòng tròn trong quan hệ cha-con");
          }
        }

        // Validate name
        if (item.name && item.name.length > 100) {
          errors.push("Tên không được vượt quá 100 ký tự");
        }

        // Validate số lượng
        // if (item.quantity != null && item.quantity <= 0) {
        //   errors.push("Số lượng phải lớn hơn 0");
        // }

        // Validate đơn giá
        if (item.materialCost != null && item.materialCost < 0) {
          errors.push("Đơn giá vật tư không được âm");
        }
        if (item.labor != null && item.labor < 0) {
          errors.push("Đơn giá nhân công không được âm");
        }

        // Validate quy đổi
        // if (item.quantityConvert != null && item.quantityConvert <= 0) {
        //   errors.push("Số lượng quy đổi phải lớn hơn 0");
        // }
        if (item.priceConvert != null && item.priceConvert < 0) {
          errors.push("Đơn giá quy đổi không được âm");
        }

        // Validate kích thước
        if (item.lengthMm != null && item.lengthMm < 0) {
          errors.push("Chiều dài không được âm");
        }
        if (item.widthMm != null && item.widthMm < 0) {
          errors.push("Chiều rộng không được âm");
        }
        if (item.heightMm != null && item.heightMm < 0) {
          errors.push("Chiều cao không được âm");
        }
        if (item.diameterMm != null && item.diameterMm < 0) {
          errors.push("Đường kính không được âm");
        }
        if (item.mass != null && item.mass < 0) {
          errors.push("Khối lượng không được âm");
        }

        return {
          ...item,
          type: itemType,
          errorMessage: errors.length ? errors.join("; ") : undefined,
        };
      });
    },
    []
  );

  // Callback to receive validation status from ImportPreviewModule
  const handleValidationStatusChange = (hasErrors: boolean) => {
    console.log("🚨 Validation status changed:", hasErrors);
    setHasValidationErrors(hasErrors);
  };

  const handleFileUpload = async (file: File) => {
    if (!file.name.includes("xlsx")) {
      message.error("Chỉ chấp nhận file Excel (.xlsx)");
      return Upload.LIST_IGNORE;
    }

    const excelData = await readerData(file, 0);
    setDataReturn(undefined);

    // Handle the uploaded data
    onUploaded?.(excelData, (rawData: BOQDetailImport[]) => {
      setDataPosts(rawData);
      // Perform full validation on entire dataset
      const validatedData = performFullValidation(rawData);
      setValidatedDataPosts(validatedData);

      // Check if there are validation errors
      const hasErrors = validatedData.some((item) => item.errorMessage);
      setHasValidationErrors(hasErrors);
    });

    return false;
  };

  const renderUploadBlock = () => (
    <Dragger
      style={{ marginTop: "0.5em" }}
      maxCount={1}
      multiple={false}
      beforeUpload={handleFileUpload}
      onChange={(info) => {
        if (info.fileList.length === 0) {
          setDataPosts([]);
          setValidatedDataPosts([]);
          setDataReturn(undefined);
          setHasValidationErrors(false);
        }
      }}
    >
      <p className="ant-upload-drag-icon">
        <InboxOutlined />
      </p>
      <p className="ant-upload-text">{uploadText}</p>
    </Dragger>
  );

  const renderAlert = () => {
    if (!dataReturn || !Array.isArray(dataReturn.data)) return null;

    const errors = dataReturn.data.filter((d) => d.status === "error");

    const buildHierarchy = (rows: any[]) => {
      const result: any[] = [];
      const parents: { [level: number]: any } = {};
      rows.forEach((row) => {
        const level = Number(row.level || 1);
        // row.children = [];
        parents[level] = row;
        if (level === 1) {
          result.push(row);
        } else {
          const parent = parents[level - 1];
          if (parent) {
            parent.children = parent.children || [];
            parent.children.push(row);
          } else {
            result.push(row); // fallback if parent not found
          }
        }
      });
      return result;
    };

    const errorsWithLevel = errors.map((e) => ({
      ...e,
      rowNum: e.rowNum || 1,
    }));

    return (
      <Alert
        className="p-3 mt-2"
        type="warning"
        description={
          <div>
            <div className="text-blue-600 font-bold">
              Tổng dòng nhập: {dataReturn.data.length}
            </div>
            <div className="text-green-500">
              Tổng dòng thành công: {dataReturn.successCount}
            </div>
            <div className="text-red-500">
              Tổng dòng thất bại: {dataReturn.errorCount}
            </div>
            <div className="font-bold mt-2">Danh sách dòng thất bại</div>
            <Table
              className="mt-1"
              size="small"
              columns={[
                // { title: "Level", dataIndex: "level", width: 70 },
                { title: "Dòng", dataIndex: "rowNum", width: 70 },
                { title: "Lỗi", dataIndex: "msg" },
              ]}
              dataSource={buildHierarchy(errorsWithLevel)}
              pagination={false}
              rowKey="rowNum"
              expandable={{
                childrenColumnName: "children",
                defaultExpandAllRows: false,
              }}
            />
          </div>
        }
      />
    );
  };

  const handleClose = () => {
    setVisible(false);
    onClose?.();
    setDataPosts([]);
    setValidatedDataPosts([]);
    setDataReturn(undefined);
    setHasValidationErrors(false);
  };

  return (
    <Modal
      open={visible}
      title={titleText}
      maskClosable={false}
      width={1000}
      destroyOnClose
      onCancel={handleClose}
      afterClose={handleClose}
      footer={[
        <CustomButton
          key="import"
          variant="primary"
          loading={loading}
          disabled={!validatedDataPosts.length || hasValidationErrors}
          onClick={handleImport}
        >
          {okText}
        </CustomButton>,
        <CustomButton key="close" variant="outline" onClick={handleClose}>
          Đóng
        </CustomButton>,
      ]}
    >
      <Spin spinning={false}>
        {guide && (
          <Alert
            className="mb-3"
            type="warning"
            message={<strong>Lưu ý</strong>}
            description={
              <ul className="list-disc pl-4">
                {guide.map((item, idx) => (
                  <li key={idx}>{item}</li>
                ))}
              </ul>
            }
          />
        )}

        {demoExcel && (
          <Link to={demoExcel} target="_blank" download>
            <Space className={`flex gap-2 cursor-pointer`}>
              <DownloadOutlined />
              Tải file import mẫu{" "}
            </Space>
          </Link>
        )}

        {onDownloadDemoExcel && (
          <a>
            <Space
              className={`flex gap-2 cursor-pointer`}
              onClick={() => {
                onDownloadDemoExcel();
              }}
              style={{ pointerEvents: loadingDownloadDemo ? "none" : "auto" }}
            >
              {loadingDownloadDemo ? (
                <Spin spinning={loadingDownloadDemo} />
              ) : (
                <DownloadOutlined />
              )}
              Tải file import mẫu
            </Space>
          </a>
        )}

        {renderUploadBlock()}

        {/* Import Preview Module */}
        <ImportPreviewModule
          data={validatedDataPosts}
          dataReturn={dataReturn}
          duplicateCheckFields={["ref"]}
          requiredFields={requiredFields}
          columns={previewColumns}
          title="Xem danh sách cấu hình"
          previewButtonText="Xem danh sách cấu hình"
          onValidationStatusChange={handleValidationStatusChange}
        />

        {renderAlert()}
      </Spin>
    </Modal>
  );
});

export default ImportBOQDetails;
