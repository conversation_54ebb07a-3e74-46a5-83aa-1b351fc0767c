import { LinkOutlined } from "@ant-design/icons";
import { Modal } from "antd";
import React, { useState } from "react";
import { GrLink } from "react-icons/gr";
import CustomSelect from "components/Input/CustomSelect";
import CustomButton from "components/Button/CustomButton";

export enum ModuleType {
  INVOICE = "invoice",
  PURCHASE = "purchase",
}

interface LinkedModule {
  id: number;
  type: ModuleType;
  code: string;
  name: string;
}

interface LinkModuleCommentModalProps {
  linkedModules: LinkedModule[];
  onModuleChange: (modules: LinkedModule[]) => void;
  onSelectModule?: (module: ModuleType, moduleId: number) => void;
}

const MODULE_OPTIONS = [
  { value: ModuleType.INVOICE, label: "Hóa đơn bán hàng" },
  { value: ModuleType.PURCHASE, label: "Đơn mua hàng" },
];

const DATA_OPTIONS = {
  [ModuleType.INVOICE]: [
    { value: 1, label: "INV001 - Hóa đơn bán hàng 1" },
    { value: 2, label: "INV002 - Hóa đơn bán hàng 2" },
  ],
  [ModuleType.PURCHASE]: [
    { value: 3, label: "PO001 - Đơn mua hàng 1" },
    { value: 4, label: "PO002 - Đơn mua hàng 2" },
  ],
};

export const LinkModuleCommentModal: React.FC<LinkModuleCommentModalProps> = ({
  linkedModules = [],
  onModuleChange,
  onSelectModule,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedModule, setSelectedModule] = useState<ModuleType | "">("");
  const [selectedData, setSelectedData] = useState<number>();

  const handleOpenModal = () => {
    setIsModalOpen(true);
    setSelectedModule("");
    setSelectedData(undefined);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedModule("");
    setSelectedData(undefined);
  };

  const handleLinkModule = () => {
    if (!selectedModule || selectedData === undefined) return;

    const selectedOption = DATA_OPTIONS[selectedModule].find(
      option => option.value === selectedData
    );

    if (!selectedOption) return;

    const newModule: LinkedModule = {
      id: selectedData,
      type: selectedModule,
      code: selectedOption.label.split(" - ")[0],
      name: selectedOption.label.split(" - ")[1],
    };

    const updatedModules = [...linkedModules, newModule];
    onModuleChange(updatedModules);
    
    // Call onSelectModule with the selected module type and ID
    onSelectModule?.(selectedModule, selectedData);
    
    handleCloseModal();
  };

  return (
    <div className="">
      <div
        className="flex items-center gap-2 text-primary font-medium my-2 cursor-pointer"
        onClick={handleOpenModal}
      >
        <GrLink />
        <span>Module liên kết</span>
      </div>

      <Modal
        title="Thông tin module liên kết"
        open={isModalOpen}
        onCancel={handleCloseModal}
        width={800}
        footer={null}
      >
        <div className="p-4">
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="mb-2 flex items-center gap-1">
                <span className="text-sm font-bold">Module liên kết</span>
                <span className="text-red-500">*</span>
              </div>
              <CustomSelect
                value={selectedModule}
                onChange={(value) => {
                  setSelectedModule(value as ModuleType);
                  setSelectedData(undefined); // Reset selected data when module changes
                }}
                options={MODULE_OPTIONS}
                className="w-full"
                placeholder="Chọn module liên kết"
              />
            </div>

            <div className="flex-1">
              <div className="mb-2 flex items-center gap-1">
                <span className="text-sm font-bold">Dữ liệu liên kết</span>
                <span className="text-red-500">*</span>
              </div>
              <CustomSelect
                value={selectedData}
                onChange={(value) => setSelectedData(value as number)}
                options={selectedModule ? DATA_OPTIONS[selectedModule] : []}
                className="w-full"
                placeholder="Chọn dữ liệu liên kết"
                disabled={!selectedModule}
              />
            </div>
          </div>
          <div className="flex justify-end mt-4">
              <CustomButton
                onClick={handleLinkModule}
                disabled={!selectedModule || selectedData === undefined}
                className="cta-button"
              >
                Liên kết
              </CustomButton>
            </div>
        </div>
      </Modal>
    </div>
  );
};
