import { FileOutlined, UploadOutlined } from "@ant-design/icons";
import { Button, message, Upload } from "antd";
import { UploadChangeParam } from "antd/lib/upload";
import { UploadFile } from "antd/lib/upload/interface";
import React, { useEffect, useState } from "react";
import { getToken } from "utils/auth";
// import "./style/fileUpload.scss";

export const FileUpload = ({
  onUploadOk,
  onDelete,
  onBefore,
  fileList,
  fileTypes,
  fileTypeText = "PDF",
  placeholder,
  acceptType,
  readOnly = false,
  uploadUrl = import.meta.env.VITE_API_URL + "/v1/admin/fileAttach/upload",
}: {
  fileList: UploadFile[];
  fileTypeText?: string;
  fileTypes?: string[];
  placeholder?: string;
  uploadUrl?: string;
  acceptType?: string;
  readOnly?: boolean;
  onDelete: (uid: string) => void;
  onUploadOk: (filePath: string) => void;
  onBefore?: () => Promise<boolean>;
}) => {
  const [loading, setLoading] = useState(false);
  const [fileListOrigin, setFileListOrigin] = useState<UploadFile[]>();

  useEffect(() => {
    setFileListOrigin(() => fileList.map((e) => ({ ...e })));
  }, [fileList]);

  const checkFileType = async (file: any) => {
    // const isPDF = fileTypes?.includes(file.type);
    console.log("filetype", file.type);

    // if (!isPDF) {
    //   message.error(`Chỉ chấp nhận định dạng ${fileTypeText}`, 3);
    // }
    let isValid = true;
    if (onBefore) {
      isValid = await onBefore?.();
    }

    console.log("isValid", isValid);

    return isValid ? true : Upload.LIST_IGNORE;
  };

  const handleChange = (info: UploadChangeParam<any>) => {
    console.log("info", info);

    setFileListOrigin(info.fileList);
    // if (info.file.status === "uploading") {
    //   setLoading(true);
    //   return;
    // }
    if (info.file.status === "done") {
      // onUploadOk(process.env.REACT_APP_API_URL + info.file.response.data.path);
      console.log({ info });
      setTimeout(() => {
        onUploadOk(import.meta.env.VITE_API_URL + info.file.response.data.path);
      }, 100);

      setLoading(false);
    } else if (info.file.status === "removed") {
      onDelete(info.file.uid);
    }
  };

  return (
    <>
      <Upload
        beforeUpload={checkFileType}
        action={uploadUrl}
        headers={{ token: getToken() || "" }}
        accept={acceptType}
        onChange={handleChange}
        fileList={fileListOrigin}
        onPreview={(file) => window.open(file.uid, "_blank")}
        maxCount={1}
        disabled={readOnly}
      >
        <Button type="primary" icon={<FileOutlined />}>
          {placeholder ? placeholder : "Chọn file"}
        </Button>
      </Upload>
    </>
  );
};
