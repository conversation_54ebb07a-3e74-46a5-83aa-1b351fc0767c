import { useParams, useNavigate, useSearchParams } from "react-router-dom";
import { useEffect, useState } from "react";
import {
  Button,
  Card,
  Descriptions,
  Spin,
  Tag,
  Divider,
  Row,
  Col,
  Alert,
  Tabs,
} from "antd";
import { ArrowLeftOutlined } from "@ant-design/icons";
import { Project } from "types/project";
import { useProject } from "hooks/useProject";
import { formatNumber } from "utils";
import dayjs from "dayjs";
import PageTitle from "components/PageTitle/PageTitle";
import MemberShipPage from "views/MemberShip/MemberShipPage";
import InDevelopment from "components/InDevevelopment/InDevelopment";
import ProjectInfo from "./ProjectInfo";
import ProjectItemPage from "../ProjectItem/ProjectItemPage";
import ApproveProcessPage from "views/ApproveProcessPage/ApproveProcessPage";
import { PermissionNames } from "types/PermissionNames";
import { observer } from "mobx-react";
import { checkRoles } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { BoqPage } from "views/BoqPage/BoqPage";

const { TabPane } = Tabs;

function ProjectDetailPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>("info");
  const [searchParams, setSearchParams] = useSearchParams();

  // Check permissions for each tab
  const { haveListPermission: havePhonebookPermission } = checkRoles(
    { list: PermissionNames.projectPhoneBookList },
    permissionStore.permissions
  );

  const { haveListPermission: haveProjectItemPermission } = checkRoles(
    { list: PermissionNames.projectItemList },
    permissionStore.permissions
  );

  const { haveListPermission: haveApproveProcessPermission } = checkRoles(
    { list: PermissionNames.approveProcessList },
    permissionStore.permissions
  );

  const { fetchDataDetail } = useProject({
    initQuery: { page: 1, limit: 10, search: "" },
  });

  useEffect(() => {
    if (id) {
      loadProjectDetail(parseInt(id));
    }
  }, [id]);

  useEffect(() => {
    const tabParam = searchParams.get("tab");
    if (
      tabParam &&
      ["info", "phonebook", "category", "approve-process", "boq"].includes(
        tabParam
      )
    ) {
      setActiveTab(tabParam);
    }
  }, [searchParams]);

  const loadProjectDetail = async (projectId: number) => {
    setLoading(true);
    setError(null);
    try {
      const projectData = await fetchDataDetail(projectId);
      if (projectData) {
        setProject(projectData);
      } else {
        setError("Không tìm thấy dự án");
      }
    } catch (error: any) {
      console.error("Error loading project detail:", error);
      setError(error.message || "Có lỗi xảy ra khi tải thông tin dự án");
    } finally {
      setLoading(false);
    }
  };

  // Handle tab change and update URL
  const handleTabChange = (key: string) => {
    setActiveTab(key);

    // Update URL with tab parameter
    if (key === "info") {
      // Remove tab parameter for default tab
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.delete("tab");
      setSearchParams(newSearchParams);
    } else {
      // Add tab parameter
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set("tab", key);
      setSearchParams(newSearchParams);
    }
  };

  const handleBack = () => {
    navigate("/project");
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <Alert
          message="Lỗi"
          description={error}
          type="error"
          showIcon
          className="mb-4"
        />
        <Button onClick={handleBack}>Quay lại danh sách</Button>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="text-center min-h-[400px] flex items-center justify-center">
        <div>
          <h3>Không tìm thấy dự án</h3>
          <Button onClick={handleBack}>Quay lại danh sách</Button>
        </div>
      </div>
    );
  }

  // Build tabs array dynamically based on permissions
  const tabItems = [
    {
      key: "info",
      label: "Thông tin dự án",
      children: <ProjectInfo title="Thông tin dự án" viewContent={true} />,
    },
    // Only show phonebook tab if user has permission
    ...(havePhonebookPermission
      ? [
          {
            key: "phonebook",
            label: "Danh bạ dự án",
            children: (
              <MemberShipPage
                title="Danh bạ dự án"
                projectId={parseInt(id!)}
                hidePageTitle={true}
              />
            ),
          },
        ]
      : []),
    // Only show project items tab if user has permission
    ...(haveProjectItemPermission
      ? [
          {
            key: "category",
            label: "Hạng mục",
            children: (
              <ProjectItemPage
                title="Hạng mục"
                projectId={parseInt(id!)}
                hidePageTitle={true}
              />
            ),
          },
        ]
      : []),
    // Only show approve process tab if user has permission
    ...(haveApproveProcessPermission
      ? [
          {
            key: "approve-process",
            label: "Mẫu quy trình duyệt",
            children: (
              <ApproveProcessPage
                title="Mẫu quy trình duyệt"
                projectId={parseInt(id!)}
                hidePageTitle={true}
              />
            ),
          },
        ]
      : []),
    {
      key: "boq",
      label: "BOQ",
      children: <BoqPage title="BOQ" hidePageTitle={true} />,
    },
  ];

  return (
    <div>
      <PageTitle
        title={"Thông tin dự án"}
        breadcrumbs={[
          {
            label: "Dự án",
          },
          {
            label: "Danh sách dự án",
            href: `/project/${PermissionNames.projectList}`,
          },
          {
            label: project.name,
          },
        ]}
      />

      <div className="app-container">
        <Card
          styles={{
            body: { paddingTop: "0px" },
          }}
        >
          <Tabs
            activeKey={activeTab}
            onChange={handleTabChange}
            items={tabItems}
            type="line"
            size="large"
          />
        </Card>
      </div>
    </div>
  );
}

export default observer(ProjectDetailPage);
