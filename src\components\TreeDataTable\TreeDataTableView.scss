.hierarchical-view {
  &__header {
    margin-bottom: 16px;
    padding: 12px 16px;
    background: #fafafa;
    border-radius: 6px;
    border: 1px solid #f0f0f0;
  }

  &__controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.hierarchical-item {
  &__action-btn {
    border: none;
    box-shadow: none;
    
    &.add-btn {
      color: #52c41a;
      
      &:hover {
        background-color: #f6ffed;
        color: #389e0d;
      }
    }
  }
}


// Dark theme support
.dark {
  .hierarchical-view {
    &__header {
      background: #141414;
      border-color: #303030;
    }

    &__content {
      border-color: #303030;
    }

    &__empty {
      color: #8c8c8c;
      
      &-icon {
        color: #434343;
      }
    }
  }
}
