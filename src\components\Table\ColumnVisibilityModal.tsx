import React, { useState, useEffect } from "react";
import { Modal, Checkbox, List } from "antd";
import { CustomizableColumn } from "./CustomizableTable";
import { useThemeColors } from "theme/useThemeColors";
import "./ColumnVisibilityModal.scss";
import CustomButton from "components/Button/CustomButton";

interface ColumnVisibilityModalProps<T> {
  visible: boolean;
  columns: CustomizableColumn<T>[];
  onCancel: () => void;
  onApply: (columns: CustomizableColumn<T>[], isReset?: boolean) => void;
  toggleColumnVisibility: (key: string, visible: boolean) => void;
}

function ColumnVisibilityModal<T>({
  visible,
  columns,
  onCancel,
  onApply,
  toggleColumnVisibility,
}: ColumnVisibilityModalProps<T>) {
  const { color } = useThemeColors();
  const [localColumns, setLocalColumns] = useState<CustomizableColumn<T>[]>([]);

  useEffect(() => {
    if (visible) {
      setLocalColumns([...columns]);
    }
  }, [visible, columns]);

  // Tính toán trạng thái checkbox "Trường dữ liệu"
  const selectableColumns = localColumns.filter((col) => !col.alwaysVisible);
  const visibleSelectableColumns = selectableColumns.filter(
    (col) => col.visible
  );
  const isAllSelected =
    selectableColumns.length > 0 &&
    visibleSelectableColumns.length === selectableColumns.length;
  const isIndeterminate =
    visibleSelectableColumns.length > 0 &&
    visibleSelectableColumns.length < selectableColumns.length;

  const handleToggle = (key: string, checked: boolean) => {
    setLocalColumns((prev) =>
      prev.map((col) => (col.key === key ? { ...col, visible: checked } : col))
    );
  };

  const handleSelectAll = (checked: boolean) => {
    setLocalColumns((prev) =>
      prev.map((col) =>
        col.alwaysVisible ? col : { ...col, visible: checked }
      )
    );
  };

  const handleApply = () => {
    onApply(localColumns);
  };

  const handleReset = () => {
    const resetColumns = localColumns.map((col) => ({
      ...col,
      visible: col.alwaysVisible
        ? true
        : col.defaultVisible !== undefined
        ? col.defaultVisible
        : true,
    }));
    setLocalColumns(resetColumns);
    onApply(resetColumns, true);
  };

  return (
    <Modal
      title="Tùy chỉnh hiển thị dữ liệu"
      open={visible}
      onCancel={onCancel}
      footer={[
        <CustomButton
          key="reset"
          size="small"
          onClick={handleReset}
          variant="outline"
          className="cta-button"
        >
          Đặt lại mặc định
        </CustomButton>,
        <CustomButton
          key="cancel"
          size="small"
          onClick={onCancel}
          variant="outline"
          className="cta-button"
        >
          Hủy
        </CustomButton>,
        <CustomButton
          key="apply"
          size="small"
          onClick={handleApply}
          className="cta-button"
        >
          Áp dụng
        </CustomButton>,
      ]}
      className="column-visibility-modal"
      width={500}
      getContainer={() => {
        return document.getElementById("App") as HTMLElement;
      }}
    >
      <p className="modal-description">
        Hãy chọn các trường dữ liệu bạn muốn hiển thị
      </p>

      <div className="column-list-container">
        {/* Header với checkbox select all */}
        <div className="column-list-header">
          <Checkbox
            checked={isAllSelected}
            indeterminate={isIndeterminate}
            onChange={(e) => handleSelectAll(e.target.checked)}
            className="select-all-checkbox"
          >
            <span className="header-title">Trường dữ liệu</span>
          </Checkbox>
        </div>

        {/* Danh sách các cột */}
        <List
          className="column-list"
          dataSource={localColumns}
          renderItem={(item, index) => (
            <List.Item
              className={`column-list-item ${
                item.alwaysVisible ? "disabled" : ""
              } ${index % 2 === 0 ? "even" : "odd"}`}
            >
              <Checkbox
                checked={item.visible}
                onChange={(e) => handleToggle(item.key, e.target.checked)}
                disabled={item.alwaysVisible}
                className="column-checkbox"
              >
                <span className="column-title">{item.title as string}</span>
              </Checkbox>
            </List.Item>
          )}
        />
      </div>
    </Modal>
  );
}

export default ColumnVisibilityModal;
