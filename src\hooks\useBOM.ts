import { bomApi } from "api/bom";
import { useState } from "react";
import { BOM } from "types/bom";
import { QueryParam } from "types/query";

export interface BomQuery extends QueryParam {}

interface UseBomProps {
  initQuery: BomQuery;
}

export const useBom = ({ initQuery }: UseBomProps) => {
  const [data, setData] = useState<BOM[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<BomQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await bomApi.findAll(query);

      setData(data.boms);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return { boms: data, total, fetchData, loading, setQuery };
};
