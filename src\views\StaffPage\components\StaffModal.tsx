import { Col, Form, Input, message, Modal, Row, Select } from "antd";
import { Rule } from "antd/lib/form";
import { staffApi } from "api/staff.api";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
// import { AreaSelector } from "components/AreaSelector/AreaSelector";
import { useRole } from "hooks/useRole";
import { useEffect, useMemo, useState } from "react";
import { FileAttach } from "types/fileAttach";
import { ModalStatus } from "types/modal";
import { Staff } from "types/staff";
import { phoneNumberRule } from "utils/validateRule";

const rules: Rule[] = [{ required: true, message: "Bắt buộc nhập!" }];
const { Option } = Select;

interface StaffForm extends Staff {
  roleId?: number;
  storeId?: number;
  areaId?: number;
  profile?: string;
}

export const StaffModal = ({
  visible,
  status,
  staff,
  onClose,
  onSubmitOk,
}: {
  visible: boolean;
  status: ModalStatus;
  staff: Partial<Staff>;
  onClose: () => void;
  onSubmitOk: () => void;
}) => {
  const [loading, setLoading] = useState(false);
  const { fetchRole, roles } = useRole({ initQuery: { page: 1, limit: 100 } });

  const [form] = Form.useForm<StaffForm>();

  useEffect(() => {
    if (status == "create" && visible) {
      form.resetFields();
    }
  }, [visible, status]);

  useEffect(() => {
    fetchRole();
  }, []);

  useEffect(() => {
    if (status == "update") {
      form.setFieldsValue({
        ...staff,
        roleId: staff.role?.id,
      });
    }
  }, [staff, status, visible]);

  const handleSubmitForm = async () => {
    await form.validateFields();
    const error = form.getFieldsError();
    console.log(error);

    setLoading(true);
    const { roleId, areaId, ...data } = form.getFieldsValue();
    const dataPost = { staff: { ...data, isBlocked: staff.isBlocked }, roleId };

    console.log(data);

    let res;
    try {
      switch (status) {
        case "update":
          res = await staffApi.update(staff?.id || 0, {
            ...dataPost,
          });
          message.success("Đã cập nhật người dùng này");

          break;

        default:
          res = await staffApi.create({ ...dataPost });
          message.success("Đã thêm người dùng mới");
          break;
      }

      form.resetFields();
      onSubmitOk();
      onClose();
    } finally {
      setLoading(false);
    }
  };

  // const debounceSearchArea = useCallback(
  //   debounce((keyword) => {
  //     query.search = keyword;
  //     fetchData();
  //   }, 300),
  //   []
  // );

  const shouldDisableAdmin = useMemo(
    () => status == "update" && staff?.role?.isAdmin,
    [staff]
  );

  return (
    <Modal
      maskClosable={false}
      onCancel={() => {
        form.resetFields();
        onClose();
      }}
      visible={visible}
      title={
        <h1 className="mb-0 text-lg text-primary font-bold">
          {status == "create" ? "Tạo mới người dùng" : "Cập nhật người dùng"}
        </h1>
      }
      style={{ top: 20 }}
      width={700}
      cancelText="Đóng"
      confirmLoading={loading}
      onOk={handleSubmitForm}
      destroyOnClose
    >
      <Form
        layout="vertical"
        form={form}
        // initialValues={{ areaId: userStore?.info?.area?.id }}
      >
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item shouldUpdate={true} style={{ marginBottom: 0 }}>
              {() => {
                return (
                  <Form.Item
                    style={{ marginBottom: 0 }}
                    label={<div>Ảnh đại diện</div>}
                    name="avatar"
                  >
                    <SingleImageUpload
                      onUploadOk={(file: FileAttach) => {
                        form.setFieldsValue({
                          avatar: file.path,
                        });
                      }}
                      imageUrl={form.getFieldValue("avatar")}
                    />
                  </Form.Item>
                );
              }}
            </Form.Item>
          </Col>
          {/* <Col span={12}>
            <Form.Item shouldUpdate={true} style={{ marginBottom: 0 }}>
              {() => {
                return (
                  <Form.Item
                    style={{ marginBottom: 0 }}
                    label={<div>Ảnh profile</div>}
                    name="profileImage"
                  >
                    <SingleImageUpload
                      recommendSize={{ width: 764, height: 1080 }}
                      onUploadOk={(path: string) => {
                        form.setFieldsValue({
                          profileImage: path,
                        });
                      }}
                      imageUrl={form.getFieldValue("profileImage")}
                    />
                  </Form.Item>
                );
              }}
            </Form.Item>
          </Col> */}
          <Col span={12}>
            <Form.Item
              label="Username"
              name="username"
              rules={[
                { required: true },
                {
                  pattern: /^[a-zA-Z0-9]+$/,
                  message:
                    "Sai định dạng (Không được chứa khoảng cách, ký tự đặt biệt, chữ có dấu)",
                },
                { max: 50, message: "Giới hạn 50 ký tự" },
              ]}
            >
              <Input disabled={status == "update"} placeholder="" />
            </Form.Item>
          </Col>
          {status == "create" && (
            <Col span={12}>
              <Form.Item label="Mật khẩu" name="password" rules={rules}>
                <Input placeholder="" />
              </Form.Item>
            </Col>
          )}

          <Col span={12}>
            <Form.Item label="Họ tên" name="fullName" rules={rules}>
              <Input placeholder="" />
            </Form.Item>
          </Col>
          {/* <Col span={12}>
            <AreaSelector
              disabled={shouldDisableAdmin}
              hiddenLabel
              form={form}
              fieldName="areaId"
            />
          </Col> */}
          <Col span={12}>
            <Form.Item label="Email" name="email" rules={rules}>
              <Input placeholder="" />
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item
              label="Số điện thoại"
              name="phone"
              rules={[...rules, phoneNumberRule]}
            >
              <Input placeholder="" />
            </Form.Item>
          </Col>

          {/* <Col span={12}>
            <Form.Item label="Chức vụ" name="position">
              <Input placeholder="" />
            </Form.Item>
          </Col> */}

          <Col span={12}>
            <Form.Item label="Quyền" name="roleId" rules={rules}>
              <Select disabled={shouldDisableAdmin} style={{ width: "100%" }}>
                {roles?.map((role) => (
                  <Option key={role.id} value={role.id}>
                    {role.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          {/* <Col span={12}>
            <Form.Item
              label="Ẩn hiện trên web"
              name="visibleOnWeb"
              rules={rules}
              valuePropName="checked"
            >
              <Switch
                className="new-visible-btn"
                checkedChildren={VisibleStatusTrans.true.label}
                unCheckedChildren={VisibleStatusTrans.false.label}
              ></Switch>
            </Form.Item>
          </Col> */}
        </Row>
      </Form>
    </Modal>
  );
};
