import { projectApi } from "api/project.api";
import { useState, useEffect } from "react";
import { Project } from "types/project";

export const useProjectDetail = (id?: number) => {
  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchProjectDetail = async (projectId: number) => {
    setLoading(true);
    setError(null);
    try {
      const { data } = await projectApi.findOne(projectId);
      setProject(data.project || data);
    } catch (error: any) {
      console.error("Error fetching project detail:", error);
      setError(error.message || "Có lỗi xảy ra khi tải thông tin dự án");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (id) {
      fetchProjectDetail(id);
    }
  }, [id]);

  return {
    project,
    loading,
    error,
    fetchProjectDetail,
    refetch: () => id && fetchProjectDetail(id),
  };
}; 