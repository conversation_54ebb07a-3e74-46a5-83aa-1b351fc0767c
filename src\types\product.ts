import { Component, DisplayImageType } from "./component";
import { FileAttach } from "./fileAttach";
import { Material } from "./material";
import { Variant } from "./variant";
export enum ProductStatus {
  Active = "ACTIVE",
  Inactive = "INACTIVE",
}
export const ProductStatusTrans = {
  [ProductStatus.Active]: {
    value: ProductStatus.Active,
    label: "Đang hoạt động", // Hoặc "Active" nếu bạn muốn tiếng Anh
  },
  [ProductStatus.Inactive]: {
    value: ProductStatus.Inactive,
    label: "Ngưng hoạt động", // Hoặc "Inactive"
  },
};
export enum ProductType {
  Single = "SINGLE",
  Combo = "COMBO",
}
export const ProductTypeTrans = {
  [ProductType.Single]: {
    value: ProductType.Single,
    label: "Sản phẩm đơn", // Hoặc "Single" nếu tiếng Anh
  },
  [ProductType.Combo]: {
    value: ProductType.Combo,
    label: "Bộ sản phẩm",
  },
};
export enum ProductDetailType {
  Fabric = "FABRIC",
  Component = "COMPONENT",
}
export const ProductDetailTypeTrans = {
  [ProductDetailType.Fabric]: {
    value: ProductDetailType.Fabric,
    label: "Vải",
  },
  [ProductDetailType.Component]: {
    value: ProductDetailType.Component,
    label: "Thành phần",
  },
};
export interface BOM {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  quantity: number;
  material: Material;
  product: Product;
}
export interface ProductVariant {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  productDetail: ProductDetail;
  component: Component;
  material: Material;
  variant: Variant;
}
export interface ProductDetail {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  name: string;
  groupName: string;
  layer: number;
  isFront: boolean;
  isBack: boolean;
  type: ProductDetailType;
  product: Product;
  productVariants: ProductVariant[];
}
export interface MainComponent {
  id: number;
  layer: number;
  name: string;
  parent?: Component;
  position?: number;
  isEmbroidery?: boolean;
  isDefaultConfig?: boolean;
  displayImage: DisplayImageType;
}
export interface Product {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  name: string;
  nameVi: string;
  groupName: string;
  status: ProductStatus;
  type: ProductType;
  code: string;
  icon: FileAttach;
  note: string;
  price: number; // giá tiêu chuẩn
  children: Product[];
  parent: Product;
  productDetails: ProductDetail[];
  boms: BOM[];
  fileAttachIcon: FileAttach;
  mainComponents: MainComponent[];
  subProducts: any[];
  productStyles: ProductStyle[];
  // posId: number;
  isRequired: boolean;
  syncId?: string;
}
export interface ProductCreating extends Product {
  parentId: number;
  mainComponent: MainComponent[];
  defaultMaterialIds: number[];
}
export interface ProductStyleDetail {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  productStyle: ProductStyle;
  component: Component;
  componentGroup: Component;
  variant: Variant;
}
export interface ProductStyleDetailForCreating
  extends Omit<ProductStyleDetail, "component" | "componentGroup" | "variant"> {
  component: Partial<Component>;
  componentGroup: Partial<Component>;
  variant: Partial<Variant>;
}
export interface ProductStyle {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  name: string;
  description: string;
  product: Product;
  productStyleDetails: ProductStyleDetail[];
  fileAttach: FileAttach; // update ảnh
}
