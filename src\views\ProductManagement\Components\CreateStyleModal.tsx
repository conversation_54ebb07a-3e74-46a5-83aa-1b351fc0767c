import {
  Button,
  Col,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Row,
  Table,
} from "antd";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { ModalStatus } from "types/modal";
import { requiredRule } from "utils/validateRule";
import { cloneDeep, uniqueId, update } from "lodash";
import {
  MainComponent,
  ProductDetail,
  ProductStyle,
  ProductStyleDetail,
  ProductStyleDetailForCreating,
} from "types/product";
import { IoAddCircleOutline } from "react-icons/io5";
import Column from "antd/es/table/Column";
import { DeleteOutlined, EditOutlined } from "@ant-design/icons";
import {
  AddMainComponentForStyle,
  ComponentAndVariant,
  mainComponentForStyleRef,
} from "./AddMainComponentForStyle";
import { useComponent } from "hooks/useComponent";
import ChooseFileFromMenu from "components/Upload/ChooseImageFromMenu";
import { useWatch } from "antd/es/form/Form";
import { FileAttach } from "types/fileAttach";
import { variantApi } from "api/variant.api";
import { componentApi } from "api/component.api";
import { Component } from "types/component";
export interface StyleRef {
  handleCreate: (mainComponent: Partial<Component>[]) => void;
  handleUpdate: (mainComponent: StyleOfProduct) => void;
}
interface MainComponentModalProps {
  onClose: () => void;
  onSubmitOk: (item: StyleOfProduct) => void;
  onUpdate: (item: StyleOfProduct) => void;
  productId: number;
  mainComponentArray?: Component[];
}
export interface StyleOfProduct extends Partial<ProductStyle> {}
export const CreateStyleModal = React.forwardRef<
  StyleRef,
  MainComponentModalProps
>(({ onClose, onSubmitOk, onUpdate, productId, mainComponentArray }, ref) => {
  const [form] = Form.useForm<ProductStyle>();
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [status, setStatus] = useState<ModalStatus>("create");
  const [componentAndVariantArray, setComponentAndVariantArray] = useState<
    Partial<ProductStyleDetail>[]
  >([]);
  const [productDetailForViewing, setProductDetailForViewing] =
    useState<ProductStyle[]>();
  const fileAttachThumbnail = useWatch("fileAttach", form);
  useImperativeHandle(
    ref,
    () => ({
      handleCreate() {
        form.resetFields();
        setComponentAndVariantArray([]);
        setStatus("create");
        setVisible(true);
      },
      handleUpdate(componentAndVariant) {
        // debugger;
        form.setFieldsValue({
          ...componentAndVariant,
        });
        console.log("Componenta", componentAndVariant.productStyleDetails);
        setComponentAndVariantArray(
          componentAndVariant.productStyleDetails || []
        );
        setStatus("update");
        setVisible(true);
      },
    }),
    []
  );
  const handleOk = async () => {
    try {
      const values = form.getFieldsValue();
      setLoading(true);
      const payload: StyleOfProduct = {
        id: status == "update" ? values.id : Number(uniqueId()),
        name: values.name,
        productStyleDetails: componentAndVariantArray as ProductStyleDetail[],
        fileAttach: values.fileAttach,
        description: values.description,
      };

      if (status === "create") {
        onSubmitOk(payload);
      } else {
        onUpdate(payload);
      }
      // message.success(
      //   status === "create"
      //     ? "Thêm mới style thành công"
      //     : "Cập nhật style thành công"
      // );
      handleClose();
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setVisible(false);
    form.resetFields();
    onClose();
  };
  const mainComponentModalRef = React.useRef<mainComponentForStyleRef>(null);
  const { components, fetchData: fetchComponents } = useComponent({
    initQuery: {
      limit: 50,
      page: 1,
    },
  });
  useEffect(() => {
    fetchComponents();
  }, []);
  const handleAddComponentAndVariant = async (
    productStyleDetail: ComponentAndVariant
  ) => {
    console.log("ComponentAndVariant Nhận đc là", productStyleDetail);

    const finalItem = await handleConvertComponentAndVariantToProductDetail(
      productStyleDetail
    );
    if (!finalItem) return;

    const isDuplicate = componentAndVariantArray.some(
      (item) => item.componentGroup?.id === finalItem.componentGroup?.id
    );

    if (isDuplicate) {
      message.warning("Nhóm thành phần này đã tồn tại trong danh sách.");
      return;
    }

    const updated = [...componentAndVariantArray, finalItem];
    setComponentAndVariantArray(updated);
  };
  const handleUpdateComponentAndVariant = async (
    updatedItem: ComponentAndVariant
  ) => {
    // debugger
    const current = componentAndVariantArray.find(
      (item) => item.id == updatedItem.id
    );
    const finalArray = await handleConvertComponentAndVariantToProductDetail(
      updatedItem
    );
    const isDuplicate = componentAndVariantArray.some(
      (item) => item.componentGroup?.id === finalArray.componentGroup?.id
    );

    if (isDuplicate) {
      message.warning("Nhóm thành phần này đã tồn tại trong danh sách.");
      return;
    }
    console.log("Update item nhận đc gì đây", finalArray);
    if (current) {
      Object.assign(current, finalArray);
      setComponentAndVariantArray(cloneDeep(componentAndVariantArray));
      message.success("Cập nhật thành công.");
    }
  };
  const handleGetOneVariant = async (id: number) => {
    try {
      const { data } = await variantApi.findOne(id);
      return data;
    } catch (error) {
      console.log(error);
    }
  };
  const handleGetOneComponent = async (id: number) => {
    try {
      const { data } = await componentApi.findOne(id);
      return data;
    } catch (error) {
      console.error(error);
    }
  };
  const handleConvertComponentAndVariantToProductDetail = async (
    componentAndVariant: ComponentAndVariant
  ): Promise<Partial<ProductStyleDetail>> => {
    try {
      const [compGroup, compMain] = await Promise.all([
        handleGetOneComponent(componentAndVariant.componentGroupId),
        handleGetOneComponent(componentAndVariant.componentId),
        // handleGetOneVariant(componentAndVariant.variantId),
      ]);
      console.log("Component và group là", compGroup, compMain);
      const finalResult: Partial<ProductStyleDetail> = {
        componentGroup: compGroup,
        component: compMain,
        // variant: compExtra,
      };
      console.log("Kết quả finalResult:", finalResult);
      return finalResult;
    } catch (error) {
      console.log(error);
      return {}; // fallback empty object in case of error
    }
  };
  return (
    <Modal
      open={visible}
      onCancel={handleClose}
      width={1200}
      onOk={() => form.submit()}
      confirmLoading={loading}
      title={status === "create" ? "Thêm Style" : "Cập nhật Style"}
      style={{ top: 20 }}
    >
      <Form layout="vertical" form={form} onFinish={handleOk}>
        <Form.Item hidden name={"id"}></Form.Item>
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              label="Thumbnail"
              name="fileAttach"
              rules={[requiredRule]}
            >
              <ChooseFileFromMenu
                fileUrl={fileAttachThumbnail?.url}
                onSelectOk={(url, file) => {
                  form.setFieldValue("fileAttach", file);
                }}
                fileName={fileAttachThumbnail?.name}
              />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="Tên Style" name="name" rules={[requiredRule]}>
              <Input />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="Mô tả" name="description" rules={[requiredRule]}>
              <Input />
            </Form.Item>
          </Col>
          {/* <Col span={24}>
            <div className="flex items-center gap-2">
              <p className="text-[15px] font-semibold px-1">
                Cấu hình biến thể mặc định của thành phần chính
              </p>
              <span
                className="flex items-center cursor-pointer hover:text-green-500"
                onClick={() => {
                  mainComponentModalRef.current?.handleCreate(productId);
                }}
              >
                <IoAddCircleOutline className="text-[25px]" />
              </span>
            </div>
          </Col> */}
          {/* <Col span={24}>
            <Table
              pagination={false}
              rowKey="id"
              dataSource={componentAndVariantArray}
              scroll={{ x: "max-content" }}
            >
              <Column
                title="Thành phần chính"
                dataIndex="name"
                align="left"
                key="name"
                render={(text, record: ProductStyleDetail) => {
                  return <div>{record.componentGroup?.name}</div>;
                }}
              />
              <Column
                title="Thành phần"
                dataIndex="name"
                align="left"
                key="name"
                render={(text, record: ProductStyleDetail) => {
                  return <div>{record.component?.name}</div>;
                }}
              />
              <Column
                fixed="right"
                width={120}
                title="Thao tác"
                align="center"
                key="action"
                render={(text, record: ProductStyleDetail) => (
                  <div className="flex gap-2">
                    <Popconfirm
                      onConfirm={() => {
                        setComponentAndVariantArray(
                          cloneDeep(
                            componentAndVariantArray.filter(
                              (item) => item.id !== record.id
                            )
                          )
                        );
                      }}
                      title="Xác nhận xóa"
                    >
                      <Button danger>
                        <DeleteOutlined />
                      </Button>
                    </Popconfirm>
                    <Button
                      type="primary"
                      onClick={() => {
                        mainComponentModalRef.current?.handleUpdate(
                          record,
                          productId
                        );
                      }}
                    >
                      <EditOutlined />
                    </Button>
                  </div>
                )}
              />
            </Table>
          </Col> */}
        </Row>
      </Form>
      <AddMainComponentForStyle
        onSubmitOk={handleAddComponentAndVariant}
        onClose={() => {}}
        ref={mainComponentModalRef}
        onUpdate={handleUpdateComponentAndVariant}
        mainComponentArray={mainComponentArray!}
      />
    </Modal>
  );
});
