.pdf-preview {
  position: relative;
  cursor: pointer;
  background-color: #f8f9fa;
  transition: box-shadow 0.2s ease;
  overflow: hidden;
  width: 100%;
  height: 100%;

  &:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    z-index: 1;
    color: #666;
    font-size: 14px;
    animation: pulse 1.5s ease-in-out infinite;
  }

  .document {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;
    pointer-events: none;
    overflow: hidden;

    // Override react-pdf canvas styling để full chiều ngang
    .react-pdf__Page {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      max-width: none !important; // Bỏ giới hạn max-width
      max-height: 100% !important;
      width: 100% !important; // Full chiều ngang
      height: auto !important;
      object-fit: cover !important; // Cover để fill full
    }

    .react-pdf__Page__canvas {
      max-width: none !important;
      max-height: 100% !important;
      width: 100% !important; // Full chiều ngang
      height: auto !important;
      object-fit: cover !important;
    }
  }

  .external-link {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    cursor: pointer;
    z-index: 2;
    transition: background-color 0.2s ease;

    &:hover {
      background: rgba(0, 0, 0, 0.9);
    }
  }

  .error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    cursor: pointer;
    padding: 20px;
    text-align: center;
    transition: border-color 0.2s ease;

    &:hover {
      border-color: #007bff;
    }

    .icon {
      font-size: 48px;
      margin-bottom: 8px;
      color: #dc3545;
    }

    .title {
      font-size: 14px;
      color: #495057;
      font-weight: 600;
      margin-bottom: 4px;
    }

    .subtitle {
      font-size: 10px;
      color: #868e96;
    }
  }
}

// Modal giữ nguyên...
.pdf-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  .content {
    width: 90%;
    height: 90%;
    background-color: white;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  }

  .header {
    padding: 16px 20px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f8f9fa;

    h3 {
      margin: 0;
      font-size: 18px;
      color: #495057;
    }
  }

  .controls {
    display: flex;
    gap: 8px;

    .button {
      padding: 8px 12px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.2s ease;

      &.primary {
        background-color: #007bff;
        color: white;

        &:hover {
          background-color: #0056b3;
        }
      }

      &.secondary {
        background-color: #6c757d;
        color: white;

        &:hover {
          background-color: #545b62;
        }
      }
    }
  }

  .body {
    height: calc(100% - 65px);
    width: 100%;
    position: relative;
  }

  .pdf-container {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: auto;
    padding: 20px;
  }

  .navigation {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;

    button {
      padding: 8px 16px;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      background-color: #fff;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover:not(:disabled) {
        background-color: #e9ecef;
      }

      &:disabled {
        background-color: #e9ecef;
        cursor: not-allowed;
        opacity: 0.6;
      }
    }

    span {
      font-size: 14px;
      color: #495057;
      font-weight: 500;
    }
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
