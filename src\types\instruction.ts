import { Dictionary } from "types/dictionary";
import { Company } from "./company";
import { FileAttach } from "./fileAttach";
import { Project } from "./project";
import { Staff } from "./staff";
import { Provider } from "./provider";
import { ApprovalList } from "./approvalList";
import { ProjectItem } from "./projectItem";
import { MemberShip } from "./memberShip";
import { Schedule } from "./schedule";

export enum InstructionType {
  Directive = "DIRECTIVE", // chỉ thị
  Report = "REPORT", // biên bản
}

export const InstructionTypeTrans = {
  [InstructionType.Directive]: {
    label: "Chỉ thị",
    value: InstructionType.Directive,
  },
  [InstructionType.Report]: {
    label: "Biên bản hiện trường",
    value: InstructionType.Report,
  },
};

export enum InstructionStatus {
  Draft = "DRAFT",
  Pending = "PENDING",
  Approved = "APPROVED",
  Rejected = "REJECTED",
  WaitingApprove = "WAITING_APPROVE",
}

export function getOverallApprovalStatus(
  approvalLists: any[]
): InstructionStatus {
  if (!approvalLists || approvalLists.length === 0)
    return InstructionStatus.Draft;
  if (approvalLists.some((item) => item.status === "REJECTED"))
    return InstructionStatus.Rejected;

  // Nếu tất cả đều APPROVED
  if (approvalLists.every((item) => item.status === "APPROVED"))
    return InstructionStatus.Approved;

  // Nếu tất cả đều PENDING
  if (approvalLists.every((item) => item.status === "PENDING"))
    return InstructionStatus.Draft;

  // Nếu chỉ có CREATE là APPROVED, các bước sau đều PENDING => WAITING_APPROVE
  const firstStep = approvalLists[0];
  const restSteps = approvalLists.slice(1);
  if (
    firstStep &&
    firstStep.name === "CREATE" &&
    firstStep.status === "APPROVED" &&
    restSteps.every((item) => item.status === "PENDING")
  ) {
    return InstructionStatus.WaitingApprove;
  }

  // Nếu có ít nhất 1 bước APPROVE đã duyệt => Đang thực hiện
  if (
    approvalLists.some(
      (item, idx) =>
        idx > 0 && item.name === "APPROVE" && item.status === "APPROVED"
    )
  ) {
    return InstructionStatus.Pending;
  }

  // Nếu có ít nhất 1 bước PENDING => Đang thực hiện
  if (approvalLists.some((item) => item.status === "PENDING"))
    return InstructionStatus.Pending;

  return InstructionStatus.Draft;
}
// export enum RFIStatus {
//   Pending = "PENDING",
//   InProgress = "IN_PROGRESS",
//   Completed = "COMPLETED",
// }

export const ProgressInstructionStatus = {
  [InstructionStatus.Draft]: {
    progress: 10,
    label: "Chưa bắt đầu",
    color: "#3949AB",
    value: InstructionStatus.Draft,
  },
  [InstructionStatus.WaitingApprove]: {
    progress: 30,
    label: "Chờ duyệt",
    color: "#B9C3C5",
    value: InstructionStatus.WaitingApprove,
  },
  [InstructionStatus.Pending]: {
    progress: 50,
    label: "Đang thực hiện",
    color: "#FFB300",
    value: InstructionStatus.Pending,
  },
  [InstructionStatus.Approved]: {
    progress: 100,
    label: "Hoàn thành",
    color: "#43A047",
    value: InstructionStatus.Approved,
  },
  [InstructionStatus.Rejected]: {
    progress: 100,
    label: "Bị từ chối",
    color: "#E53935",
    value: InstructionStatus.Rejected,
    showProgressBar: false,
  },
};

export interface Instruction {
  followMemberShips: never[];
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  code: string;
  name: string;
  address: string; // Địa chỉ của công trường.
  isActive: boolean;
  // status: InstructionStatus
  content: string; // Nội dung chỉ thị
  description: string;
  link: string; // Link tài liệu
  contractCode: string; // mã hợp đồng (giai đoạn 2 mới liên kết với hợp đồng)
  commercialArrangement: string; // Thông tin về thỏa thuận thương mại hoặc hợp đồng liên quan.
  note: string;
  type: InstructionType; // phan loại
  createdDate: string; // ngày tạo
  issuedDate: string; // ngày phát hành
  project: Project;
  company: Company;
  instructionCategory: Dictionary;
  projectItem: ProjectItem;
  schedule: Schedule; // Hạng mục công việc
  provider: Provider;
  inspecStaffs: Staff[];
  followStaffs: Staff[];
  // relatedCosts: RelatedCost[];
  // approvalHistories: ApprovalHistory[];
  receivedBy: Staff; // Người nhận
  // info create update
  createdBy: Staff; // người phát hành
  updatedBy: Staff;
  fileAttaches: FileAttach[];
  approvalLists: ApprovalList[];
  createdMemberShipBy: MemberShip;
  receivedMemberShipBy: MemberShip;
}
