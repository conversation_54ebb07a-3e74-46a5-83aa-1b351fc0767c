import { DownloadOutlined, InboxOutlined } from "@ant-design/icons";
import { Alert, Modal, Space, Spin, Table, Upload, message } from "antd";
import { Rule } from "antd/es/form";
import { providerApi } from "api/provider.api";
import CustomButton from "components/Button/CustomButton";
import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
  useMemo,
} from "react";
import { Link } from "react-router-dom";
import { Provider, ProviderModule } from "types/provider";
import { readerData } from "utils/excel2";
import ImportPreviewModule from "./ImportPreviewModule";
import { handleConfirmImportExcel } from "utils/function";

const rules: Rule[] = [{ required: true }];
const { Dragger } = Upload;

export interface ImportProviderModal {
  open: () => void;
  close: () => void;
}

export interface IValidate {
  index: number;
  message: string;
}

interface DataImportReturn {
  msg: string;
  rowNum: number;
  status: string;
}

export interface ProviderImport extends Provider {
  rowNum: number;
  providerCategoryName?: string; // From Excel
  accountGroupName?: string; // From Excel
  materialGroupNames?: string; // From Excel (comma separated)
  countryName?: string; // From Excel
  cityName?: string; // From Excel
  districtName?: string; // From Excel
  wardName?: string; // From Excel
  workTypeNames?: string; // From Excel (comma separated for sub-contractors)
}

interface IProps {
  onSuccess?: () => void;
  createApi?: (data: any) => any;
  onUploaded?: (excelData: any, setData: (data: any) => any) => void;
  demoExcel?: string;
  guide?: React.ReactNode[];
  uploadText?: string;
  okText?: string;
  onClose?: () => void;
  titleText?: string;
  validateMessage?: IValidate[];
  onDownloadDemoExcel?: () => void;
  loadingDownloadDemo?: boolean;
  providerModule?: ProviderModule; // Thêm prop để xác định loại provider
}

const ImportProvider = forwardRef(
  (
    {
      onSuccess,
      createApi,
      onUploaded,
      onClose,
      validateMessage,
      guide,
      demoExcel,
      uploadText = "Kéo thả hoặc click vào đây để upload file",
      okText = "Nhập dữ liệu ngay",
      titleText = "Nhập excel dữ liệu",
      onDownloadDemoExcel,
      loadingDownloadDemo,
      providerModule,
    }: IProps,
    ref
  ) => {
    const [errorsLog, setErrorsLog] = useState<any[]>([]);
    const [dataPosts, setDataPosts] = useState<ProviderImport[]>([]);
    const [visible, setVisible] = useState<boolean>(false);
    const [loading, setLoading] = useState(false);
    const [hasValidationErrors, setHasValidationErrors] = useState(false);
    const [dataReturn, setDataReturn] = useState<{
      data: DataImportReturn[];
      successCount: number;
      errorCount: number;
    }>();

    useEffect(() => {
      if (validateMessage?.length) {
        setErrorsLog([]);
      }
    }, [validateMessage]);

    // Callback to receive validation status from ImportPreviewModule
    const handleValidationStatusChange = (hasErrors: boolean) => {
      console.log("🚨 Provider validation status changed:", hasErrors);
      setHasValidationErrors(hasErrors);
    };

    const handleOnImport = async () => {
      if (!dataPosts.length) return;
      let errors: any = [];

      try {
        setLoading(true);
        await handleConfirmImportExcel();
        const { data } = await providerApi.import({
          providers: dataPosts.map((dataPost) => ({
            ...dataPost,
          })),
        });
        if (data.length) {
          const successCount = data.reduce(
            (acc: number, item: DataImportReturn) => {
              if (item.status == "ok") return acc + 1;
              return acc;
            },
            0
          );
          const errorCount = data.reduce(
            (acc: number, item: DataImportReturn) => {
              if (item.status == "error") return acc + 1;
              return acc;
            },
            0
          );
          if (errorCount == 0) {
            handleOnCancel();
          }
          setDataReturn({ data, successCount, errorCount });
          onSuccess?.();
          setDataPosts([]);
        }
      } catch (err) {
        console.log({ err });
      } finally {
        setLoading(false);
      }
    };

    const handleOnCancel = () => {
      setVisible(false);
      onClose?.();
    };

    useImperativeHandle(
      ref,
      () => ({
        open: () => setVisible(true),
        close: () => setVisible(false),
      }),
      []
    );

    // Define required fields for validation - based on CreateOrUpdateProviderPage
    const requiredFields: (keyof ProviderImport)[] = [
      "name", // Tên nhà cung cấp
      "providerCategoryName", // Loại NCC (from Excel)
      "currency", // Loại tiền tệ - required
    ];
    if (providerModule == ProviderModule.SubContractor) {
      requiredFields.push(
        "cityName", // Tỉnh/Thành phố (from Excel) - required
        "districtName", // Quận/Huyện (from Excel) - required
        "wardName" // Xã/Phường (from Excel) - required
      );
    }

    const handleValidateData = (data: ProviderImport[]): ProviderImport[] => {
      console.log("🔍 handleValidateData called with:", data);
      return data.map((item) => {
        const additionalErrors: string[] = [];

        // Email validation (only if email exists)
        if (item.email) {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(item.email)) {
            additionalErrors.push("Email không hợp lệ");
          }
        }

        // Phone validation (only if phone1 exists)
        if (item.phone1) {
          const phoneRegex = /^[\d\s\-\+\(\)]+$/;
          if (!phoneRegex.test(item.phone1)) {
            additionalErrors.push("Số điện thoại không hợp lệ");
          }
        }

        // Website validation (only if website exists)
        if (item.website) {
          const websiteRegex = /^https?:\/\/.+/;
          if (!websiteRegex.test(item.website)) {
            additionalErrors.push(
              "Website phải bắt đầu bằng http:// hoặc https://"
            );
          }
        }

        // Staff count validation for sub-contractors (only if staffCount exists)
        if (item.staffCount !== undefined && item.staffCount !== null) {
          if (isNaN(Number(item.staffCount)) || Number(item.staffCount) < 0) {
            additionalErrors.push("Số lượng nhân sự phải là số không âm");
          }
        }

        // Average completion time validation (only if averageCompletionTime exists)
        if (
          item.averageCompletionTime !== undefined &&
          item.averageCompletionTime !== null
        ) {
          if (
            isNaN(Number(item.averageCompletionTime)) ||
            Number(item.averageCompletionTime) < 0
          ) {
            additionalErrors.push(
              "Thời gian hoàn thành trung bình phải là số không âm"
            );
          }
        }

        console.log(
          "🔍 Additional validation for item:",
          item,
          "additional errors:",
          additionalErrors
        );

        // Return item with additional errors (don't overwrite existing errorMessage)
        return {
          ...item,
          errorMessage:
            additionalErrors.length > 0
              ? additionalErrors.join("; ")
              : undefined,
        };
      });
    };

    // Thêm vào phần import
    const detectedProviderType = useMemo(() => {
      if (providerModule) return providerModule;

      // Nếu có dữ liệu, kiểm tra các trường đặc trưng của thầu phụ
      if (dataPosts.length > 0) {
        const hasSubContractorFields = dataPosts.some(
          (item) =>
            item.workTypeNames ||
            item.staffCount !== undefined ||
            item.averageCompletionTime !== undefined ||
            item.specialization
        );
        return hasSubContractorFields
          ? ProviderModule.SubContractor
          : ProviderModule.Supplier;
      }

      return ProviderModule.Supplier; // Default
    }, [providerModule, dataPosts]);

    // Tự động tạo title và button text dựa trên loại provider
    const dynamicTexts = useMemo(() => {
      const isSubContractor =
        detectedProviderType === ProviderModule.SubContractor;
      return {
        modalTitle:
          titleText ||
          `Nhập excel ${isSubContractor ? "thầu phụ" : "nhà cung cấp"}`,
        previewTitle: `Xem danh sách ${
          isSubContractor ? "thầu phụ" : "nhà cung cấp"
        }`,
        previewButtonText: `Xem danh sách ${
          isSubContractor ? "thầu phụ" : "nhà cung cấp"
        }`,
      };
    }, [detectedProviderType, titleText]);

    console.log("🔍 Debug ImportProvider:");
    console.log("providerModule:", providerModule);
    console.log("detectedProviderType:", detectedProviderType);
    console.log(
      "isSubContractor:",
      detectedProviderType === ProviderModule.SubContractor
    );
    console.log("dynamicTexts:", dynamicTexts);

    // Define preview columns for the table - dynamic based on provider type
    const isSubContractor =
      detectedProviderType === ProviderModule.SubContractor;
    const previewColumns = [
      {
        key: "rowNum",
        title: "Dòng excel",
        dataIndex: "rowNum",
        width: 100,
        render: (text: number) => <span>{text}</span>,
      },
      {
        key: "code",
        title: isSubContractor ? "Mã thầu phụ" : "Mã NCC",
        dataIndex: "code",
        width: 120,
        render: (text: string) => (
          <span className={!text ? "text-gray-400" : ""}>
            {text || "Tự sinh"}
          </span>
        ),
      },
      {
        key: "name",
        title: isSubContractor ? "Tên thầu phụ *" : "Tên nhà cung cấp *",
        dataIndex: "name",
        width: 200,
        render: (text: string, record: any) => (
          <span className={!text ? "text-red-500" : ""}>{text || "Thiếu"}</span>
        ),
      },
      {
        key: "providerCategoryName",
        title: isSubContractor ? "Loại thầu phụ *" : "Loại NCC *",
        dataIndex: "providerCategoryName",
        width: 200,
        render: (text: string, record: any) => (
          <span className={!text ? "text-red-500" : ""}>{text || "Thiếu"}</span>
        ),
      },
      providerModule == ProviderModule.Supplier && {
        key: "accountGroupName",
        title: "Nhóm tài khoản",
        dataIndex: "accountGroupName",
        width: 200,
        render: (text: string, record: any) => (
          <span className={!text ? "text-gray-400" : ""}>
            {text || "Không có"}
          </span>
        ),
      },
      {
        key: "cityName",
        title:
          "Tỉnh/Thành phố" +
          (providerModule == ProviderModule.SubContractor ? " *" : ""),
        dataIndex: "cityName",
        width: 150,
        render: (text: string, record: any) => (
          <span className={!text ? "text-gray-400" : ""}>
            {text || "Không có"}
          </span>
        ),
      },
      {
        key: "districtName",
        title:
          "Quận/Huyện" +
          (providerModule == ProviderModule.SubContractor ? " *" : ""),
        dataIndex: "districtName",
        width: 150,
        render: (text: string, record: any) => (
          <span className={!text ? "text-gray-400" : ""}>
            {text || "Không có"}
          </span>
        ),
      },
      {
        key: "wardName",
        title:
          "Xã/Phường" +
          (providerModule == ProviderModule.SubContractor ? " *" : ""),
        dataIndex: "wardName",
        width: 150,
        render: (text: string, record: any) => (
          <span className={!text ? "text-gray-400" : ""}>
            {text || "Không có"}
          </span>
        ),
      },
      {
        key: "phone1",
        title: "Điện thoại 1",
        dataIndex: "phone1",
        width: 150,
        render: (text: string) => (
          <span className={!text ? "text-gray-400" : ""}>
            {text || "Không có"}
          </span>
        ),
      },
      {
        key: "email",
        title: "Email",
        dataIndex: "email",
        width: 200,
        render: (text: string) => (
          <span className={!text ? "text-gray-400" : ""}>
            {text || "Không có"}
          </span>
        ),
      },
      {
        key: "address",
        title: "Địa chỉ",
        dataIndex: "address",
        width: 200,
        render: (text: string) => (
          <span className={!text ? "text-gray-400" : ""}>
            {text || "Không có"}
          </span>
        ),
      },
      {
        key: "countryName",
        title: "Quốc gia",
        dataIndex: "countryName",
        width: 100,
        render: (text: string) => (
          <span className={!text ? "text-gray-400" : ""}>
            {text || "Không có"}
          </span>
        ),
      },
      providerModule == ProviderModule.Supplier && {
        key: "materialGroupNames",
        title: "Nhóm sản phẩm",
        dataIndex: "materialGroupNames",
        width: 200,
        render: (text: string) => (
          <span className={!text ? "text-gray-400" : ""}>
            {text || "Không có"}
          </span>
        ),
      },
      {
        key: "website",
        title: "Website",
        dataIndex: "website",
        width: 150,
        render: (text: string) => (
          <span className={!text ? "text-gray-400" : ""}>
            {text || "Không có"}
          </span>
        ),
      },
      {
        key: "errorMessage",
        title: "Lỗi",
        dataIndex: "errorMessage",
        width: 300,
        render: (text: string) => (
          <span className="text-red-500 whitespace-pre-line text-xs">
            {text}
          </span>
        ),
      },
    ];

    return (
      <Modal
        maskClosable={false}
        width={1400} // Increase width to accommodate more columns
        style={{ top: 50 }}
        visible={visible}
        onCancel={handleOnCancel}
        destroyOnClose={true}
        afterClose={() => {
          setDataPosts([]);
          setErrorsLog([]);
          setDataReturn(undefined);
        }}
        title={dynamicTexts.modalTitle}
        footer={[
          <CustomButton
            key="import"
            loading={loading}
            variant="primary"
            disabled={!dataPosts.length || hasValidationErrors}
            onClick={() => {
              handleOnImport();
            }}
          >
            {okText}
          </CustomButton>,
          <CustomButton
            key="close"
            variant="outline"
            className="cta-button"
            onClick={() => {
              handleOnCancel();
            }}
          >
            Đóng
          </CustomButton>,
        ]}
      >
        <Spin spinning={false}>
          {guide && (
            <Alert
              style={{ padding: "10px", marginBottom: "10px" }}
              message={<b>Lưu ý</b>}
              type="warning"
              description={
                <ul>
                  {guide.map((text, index) => (
                    <li key={index}>
                      <p>{text}</p>
                    </li>
                  ))}
                </ul>
              }
            />
          )}
          {demoExcel && (
            <Link to={demoExcel} target="_blank" download>
              <Space className={`flex gap-2 cursor-pointer`}>
                <DownloadOutlined />
                Tải file import mẫu{" "}
              </Space>
            </Link>
          )}
          {onDownloadDemoExcel && (
            <a>
              <Space
                className={`flex gap-2 cursor-pointer`}
                onClick={() => {
                  onDownloadDemoExcel();
                }}
                style={{ pointerEvents: loadingDownloadDemo ? "none" : "auto" }}
              >
                {loadingDownloadDemo ? (
                  <Spin spinning={loadingDownloadDemo} />
                ) : (
                  <DownloadOutlined />
                )}
                Tải file import mẫu
              </Space>
            </a>
          )}

          <Dragger
            style={{ marginTop: "0.5em" }}
            maxCount={1}
            multiple={false}
            beforeUpload={async (file) => {
              //Check file type
              const isCSVFile = file.name.includes("xlsx");
              if (isCSVFile === false) {
                message.error("Bạn chỉ có thể upload file excel!");
                return Upload.LIST_IGNORE;
              }
              const excelData = await readerData(file, 0);
              setDataReturn(undefined);
              console.log("Data khi import vào là", excelData);
              onUploaded?.(excelData, setDataPosts);
              return false;
            }}
            onChange={(info) => {
              //reset data
              if (info.fileList.length == 0) {
                setErrorsLog([]);
                setDataPosts([]);
              }
            }}
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">{uploadText}</p>
          </Dragger>

          {/* Import Preview Module */}
          <ImportPreviewModule
            data={dataPosts}
            dataReturn={dataReturn}
            onValidateData={handleValidateData}
            duplicateCheckFields={["code", "name"]} // Kiểm tra trùng lặp mã nhà cung cấp và tên nhà cung cấp
            requiredFields={requiredFields}
            columns={previewColumns}
            title={dynamicTexts.previewTitle}
            previewButtonText={dynamicTexts.previewButtonText}
            onValidationStatusChange={handleValidationStatusChange}
          />
        </Spin>
        <Space
          style={{ width: "100%", justifyContent: "end", marginTop: "1em" }}
        ></Space>
      </Modal>
    );
  }
);

export default ImportProvider;
