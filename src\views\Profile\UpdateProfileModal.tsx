import { Col, Form, Input, message, Modal, Row, Space } from "antd";
import { Rule } from "antd/lib/form";
import FormItem from "antd/lib/form/FormItem";
import { authApi } from "api/auth.api";
import CustomButton from "components/Button/CustomButton";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import { forwardRef, useImperativeHandle, useState } from "react";
import { FileAttach } from "types/fileAttach";
import { ModalStatus } from "types/modal";
import { Staff } from "types/staff";
import { phoneNumberRule } from "utils/phoneInput";
const rules: Rule[] = [{ required: true }];

export interface UpdateProfileModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
}

export interface UpdateProfileModal {
  handleUpdate: (staff: Partial<Staff>) => void;
  handleCreate: () => void;
}

export const UpdateProfileModal = forwardRef(
  ({ onSubmitOk }: UpdateProfileModalProps, ref) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState<boolean>();
    const [selectedStaff, setSelectedStaff] = useState<Partial<Staff>>();
    const [status, setStatus] = useState<ModalStatus>("create");
    const [isView, setIsView] = useState(false);
    const onlinePaymentType = Form.useWatch("onlinePaymentType", form);

    useImperativeHandle(
      ref,
      () => ({
        handleUpdate,
      }),
      []
    );

    const handleUpdate = (staff: Partial<Staff>) => {
      console.log(staff);
      form.setFieldsValue({
        ...staff,
      });
      setSelectedStaff(staff);
      setStatus("update");
      setVisible(true);
    };

    const handleSubmitForm = async () => {
      await form.validateFields();
      const dataForm = form.getFieldsValue();
      console.log(dataForm);
      console.log(onlinePaymentType);
      const payload = {
        staff: dataForm,
      };

      try {
        setLoading(true);
        switch (status) {
          case "update":
            await authApi.updateProfile(payload);
            message.success("Cập nhật thông tin cá nhân thành công");
            break;
        }
        onSubmitOk();
      } finally {
        setLoading(false);
        setVisible(false);
        onSubmitOk();
      }
    };

    // const onChange = (e: RadioChangeEvent) => {
    //   setOnlinePaymentType(e.target.value);
    // };

    return (
      <Modal
        onCancel={() => {
          setVisible(false);
        }}
        visible={visible}
        centered
        title={
          <h1 className="mb-0 text-lg text-primary font-bold">
            {isView ? (
              "Chi tiết đơn vị thanh toán"
            ) : (
              <>
                {" "}
                {(status == "create" ? "Thêm" : "Cập nhật") +
                  " thông tin cá nhân"}
              </>
            )}
          </h1>
        }
        confirmLoading={loading}
        width={700}
        destroyOnClose
        afterClose={() => {
          form.resetFields();
        }}
        maskClosable={false}
        okText="Xác nhận"
        footer={
          <Space className={isView ? "none " : ""}>
            <CustomButton variant="outline" onClick={() => setVisible(false)}>
              Hủy
            </CustomButton>
            <CustomButton onClick={() => handleSubmitForm()}>
              Xác nhận
            </CustomButton>
          </Space>
        }
      >
        <Form
          disabled={isView}
          form={form}
          layout="vertical"
          initialValues={{ isEnabled: true }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item shouldUpdate={true} style={{ marginBottom: 0 }}>
                {() => {
                  return (
                    <Form.Item
                      rules={rules}
                      style={{ marginBottom: 0 }}
                      label={<div>Ảnh đại diện</div>}
                      name="avatar"
                    >
                      <SingleImageUpload
                        onUploadOk={(file: FileAttach) => {
                          form.setFieldsValue({
                            avatar: file.path,
                          });
                        }}
                        width={150}
                        height={150}
                        imageUrl={form.getFieldValue("avatar")}
                      />
                    </Form.Item>
                  );
                }}
              </Form.Item>
            </Col>

            <Col span={24}>
              <FormItem
                rules={rules}
                required
                label="Họ và tên"
                name={"fullName"}
              >
                <Input placeholder="Nhập vào họ và tên" />
              </FormItem>
            </Col>

            <Col span={24}>
              <FormItem
                name="phone"
                label="Số điện thoại"
                rules={[{ required: true }, phoneNumberRule]}
              >
                <Input placeholder="Nhập vào số điện thoại" />
              </FormItem>
            </Col>
            <Col span={24}>
              <FormItem
                rules={[{ required: true, type: "email" }]}
                required
                label="Email"
                name={"email"}
              >
                <Input placeholder="Nhập vào email" type="email" />
              </FormItem>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
);
