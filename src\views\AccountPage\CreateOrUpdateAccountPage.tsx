import {
  Card,
  Col,
  Form,
  Input,
  message,
  Row,
  Select,
  Spin,
  Modal,
} from "antd";
import { Rule } from "antd/lib/form";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import React, { useEffect, useMemo, useState } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { FileAttach } from "types/fileAttach";
import { ModalStatus } from "types/modal";
import { getTitle } from "utils";
import { accountApi } from "api/account.api";
import { Account } from "types/account";
import CustomButton from "components/Button/CustomButton";
import { useStaff } from "hooks/useStaff";
import { useRole } from "hooks/useRole";
import clsx from "clsx";
import PageTitle from "components/PageTitle/PageTitle";
import { isEmpty } from "lodash";
import { PermissionNames } from "types/PermissionNames";
import { observer } from "mobx-react";
import { checkRoles } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { useWatch } from "antd/es/form/Form";
import { StaffSelector } from "components/Selector/StaffSelector";
import { RoleSelector } from "components/Selector/RoleSelector";
import { CompanySelector } from "components/Selector/CompanySelector";
import { Staff } from "types/staff";

const rules: Rule[] = [{ required: true, message: "Bắt buộc nhập!" }];
const staffRules: Rule[] = [
  { required: true, message: "Bắt buộc chọn nhân viên!" },
];
interface Props {
  title: string;
  status: ModalStatus;
}

export const CreateOrUpdateAccountPage = observer(
  ({ title = "", status }: Props) => {
    const { haveEditPermission } = checkRoles(
      {
        add: PermissionNames.accountAdd,
        edit: PermissionNames.accountEdit,
      },
      permissionStore.permissions
    );

    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const navigate = useNavigate();
    const [selectedAccount, setSelectedAccount] = useState<Account>();
    const avatar = useWatch("avatar", form);
    const companyId = useWatch("companyId", form);
    const staff = useWatch("staff", form);
    const [searchParams, setSearchParams] = useSearchParams();
    const [readonly, setReadonly] = useState(true);
    const [loadingFetch, setLoadingFetch] = useState(false);
    const [existingAccount, setExistingAccount] = useState<Account | null>(
      null
    );
    const [selectedStaff, setSelectedStaff] = useState<Staff | undefined>();
    const params = useParams();

    const setDataToForm = (data: Account) => {
      form.setFieldsValue({
        ...data,
        staffId: data.staff?.id,
        roleId: data.role?.id,
        companyId: data.staff?.company?.id,
      });
    };

    // Kiểm tra staff đã có account chưa
    const checkExistingAccount = async (staffId: number) => {
      try {
        const { data: accounts } = await accountApi.findAll({ staffId });
        const existingAcc = accounts?.accounts?.find(
          (acc: Account) => acc.staff?.id === staffId
        );
        setExistingAccount(existingAcc || null);
        return existingAcc;
      } catch (error) {
        console.error("Error checking existing account:", error);
        return null;
      }
    };

    // Xử lý submit với xác nhận
    const handleSubmitWithConfirmation = (submitFunction: () => void) => {
      const staffId = form.getFieldValue("staffId");
      const currentUsername = form.getFieldValue("username");

      console.log("handleSubmitWithConfirmation called", {
        staffId,
        currentUsername,
        existingAccount,
      });

      if (
        staffId &&
        existingAccount &&
        existingAccount.id !== selectedAccount?.id &&
        existingAccount.username !== currentUsername
      ) {
        console.log("Setting pendingSubmit and showing modal");
        showUnlinkConfirmationModal(submitFunction);
      } else {
        console.log("Direct submit");
        submitFunction();
      }
    };

    // Hiển thị modal xác nhận với custom buttons
    const showUnlinkConfirmationModal = (submitFunction: () => void) => {
      Modal.confirm({
        title: "Xác nhận ngắt liên kết tài khoản",
        getContainer: () => {
          return document.getElementById("App") as HTMLElement;
        },
        icon: null,
        content: (
          <div className="mb-4">
            <p>
              Nhân viên <strong>{existingAccount?.staff?.fullName}</strong> đã
              có tài khoản liên kết:{" "}
              <strong>{existingAccount?.username}</strong>
            </p>
            <p className="text-red-600 mt-2">
              Bạn có chắc chắn muốn ngắt liên kết tài khoản cũ và tạo tài khoản
              mới cho nhân viên này?
            </p>
            <p className="text-sm text-gray-600 mt-2">
              Lưu ý: Tài khoản cũ sẽ bị ngắt liên kết với nhân viên này.
            </p>
          </div>
        ),
        footer: (_, { OkBtn, CancelBtn }) => (
          <>
            <CustomButton
              onClick={() => {
                Modal.destroyAll();
              }}
              className="cta-button"
            >
              Huỷ
            </CustomButton>
            <CustomButton
              variant="outline"
              className="cta-button"
              onClick={async () => {
                console.log("Modal confirm clicked, executing submitFunction");
                await submitFunction();
                setExistingAccount(null); // Xóa thông báo trùng sau khi xác nhận
                Modal.destroyAll();
              }}
            >
              Xác nhận
            </CustomButton>
          </>
        ),
      });
    };

    const getOneAccount = async (id: number) => {
      try {
        setLoadingFetch(true);
        // accountApi doesn't have findOne method, we'll fetch from the list
        const { data: account } = await accountApi.findOne(id);

        if (!account) {
          navigate("/404");
          return;
        }

        setSelectedAccount(account);
        setSelectedStaff(account?.staff);
        setDataToForm(account);
      } catch (e) {
        console.error("Error fetching account:", e);
        navigate("/404");
      } finally {
        setLoadingFetch(false);
      }
    };

    useEffect(() => {
      document.title = getTitle(title);

      if (status == "create") {
        setReadonly(false);
      } else {
        const accountId = params.id;
        if (accountId) {
          getOneAccount(+accountId);
          setReadonly(searchParams.get("update") != "1");
        } else {
          navigate("/404");
        }
      }
    }, []);

    // Kiểm tra account hiện tại khi staff thay đổi
    useEffect(() => {
      const staffId = form.getFieldValue("staffId");
      if (staffId && status === "create") {
        checkExistingAccount(staffId);
      }
    }, [staff]);

    const getDataSubmit = () => {
      const { staffId, roleId, ...data } = form.getFieldsValue();

      const payload = {
        account: {
          ...data,
        },
        staffId: staffId || 0,
        roleId: roleId || 0,
      };

      return payload;
    };

    const createData = async () => {
      try {
        await form.validateFields();
        setLoading(true);

        const formData = getDataSubmit();

        const res = await accountApi.create(formData);
        message.success("Tạo tài khoản thành công!");
        setExistingAccount(null); // Xóa thông báo trùng sau khi tạo thành công
        navigate(`/master-data/${PermissionNames.accountList}`);
      } catch (error) {
        // message.error("Có lỗi xảy ra khi tạo tài khoản");
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      try {
        await form.validateFields();
        setLoading(true);

        const formData = getDataSubmit();

        const res = await accountApi.update(selectedAccount?.id || 0, formData);
        message.success("Cập nhật tài khoản thành công!");
        setExistingAccount(null); // Xóa thông báo trùng sau khi cập nhật thành công
        // setReadonly(true);
      } catch (error) {
        // message.error("Có lỗi xảy ra khi cập nhật tài khoản");
      } finally {
        setLoading(false);
      }
    };

    const handleSubmit = () => {
      if (status == "create") {
        handleSubmitWithConfirmation(createData);
      } else {
        handleSubmitWithConfirmation(updateData);
      }
    };

    const titlePage = useMemo(
      () => (status == "create" ? "Tạo tài khoản" : "Chỉnh sửa tài khoản"),
      [status]
    );

    return (
      <div className="app-container">
        <PageTitle
          back
          breadcrumbs={[
            { label: "Dữ liệu nguồn" },
            {
              label: "Danh sách tài khoản",
              href: `/master-data/${PermissionNames.accountList}`,
            },
            { label: titlePage },
          ]}
          title={titlePage}
        />
        <Card>
          <Spin spinning={loadingFetch}>
            <Form
              layout="vertical"
              form={form}
              className={clsx(readonly ? "readonly" : "")}
              disabled={readonly}
            >
              <div
                style={{
                  display: "flex",
                  gap: 20,
                }}
              >
                <div
                  style={{
                    flex: 1,
                  }}
                >
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        label="Tên tài khoản"
                        name="username"
                        rules={[
                          { required: true },
                          {
                            pattern: /^[a-zA-Z0-9]+$/,
                            message:
                              "Sai định dạng (Không được chứa khoảng cách, ký tự đặc biệt, chữ có dấu)",
                          },
                          { max: 50, message: "Giới hạn 50 ký tự" },
                        ]}
                      >
                        <Input
                          //   disabled={status == "update"}
                          placeholder="Nhập tên tài khoản"
                        />
                      </Form.Item>
                    </Col>

                    {status == "create" && (
                      <Col span={12}>
                        <Form.Item
                          label="Mật khẩu"
                          name="password"
                          rules={rules}
                        >
                          <Input.Password placeholder="Nhập mật khẩu" />
                        </Form.Item>
                      </Col>
                    )}

                    <Col span={12}>
                      <Form.Item label="Công ty" name="companyId">
                        <CompanySelector
                          placeholder="Chọn công ty"
                          initOptionItem={selectedAccount?.staff?.company}
                          onChange={(value) => {
                            form.setFieldValue("staffId", null);
                            form.setFieldValue("staff", null);
                            setSelectedStaff(undefined);
                          }}
                        />
                      </Form.Item>
                    </Col>

                    <Col span={12}>
                      <Form.Item label="Nhân viên" name="staffId" rules={rules}>
                        <StaffSelector
                          placeholder="Chọn nhân viên"
                          initOptionItem={selectedStaff}
                          companyId={companyId}
                          valueIsOption
                          onChange={(value) => {
                            form.setFieldValue("staffId", value.id);
                            form.setFieldValue("staff", value);
                            // Kiểm tra account hiện tại khi chọn staff
                            checkExistingAccount(value.id);
                          }}
                          initQuery={{ isBlocked: false }}
                        />
                      </Form.Item>
                      {existingAccount &&
                        existingAccount.username !==
                          form.getFieldValue("username") && (
                          <div className="text-red-600 text-sm mt-1">
                            Nhân viên này đã có tài khoản liên kết:{" "}
                            <strong>{existingAccount.username}</strong>
                          </div>
                        )}
                    </Col>

                    <Col span={12}>
                      <Form.Item label="Vai trò" name="roleId" rules={rules}>
                        <RoleSelector
                          placeholder="Chọn vai trò"
                          initOptionItem={selectedAccount?.role}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </div>
              </div>
            </Form>
            <div className="flex gap-[16px] justify-end mt-4">
              {!readonly && (
                <CustomButton
                  variant="outline"
                  className="cta-button"
                  onClick={() => {
                    if (status == "create") {
                      navigate(`/master-data/${PermissionNames.accountList}`);
                    } else {
                      setDataToForm(selectedAccount!);
                      setReadonly(true);
                    }
                  }}
                >
                  Hủy
                </CustomButton>
              )}

              <CustomButton
                className="cta-button"
                loading={loading}
                onClick={() => {
                  if (!readonly) {
                    handleSubmit();
                  } else {
                    setReadonly(false);
                  }
                }}
                disabled={status == "update" && !haveEditPermission}
              >
                {status == "create"
                  ? "Tạo tài khoản"
                  : readonly
                  ? "Chỉnh sửa"
                  : "Lưu chỉnh sửa"}
              </CustomButton>
            </div>
          </Spin>
        </Card>
      </div>
    );
  }
);
