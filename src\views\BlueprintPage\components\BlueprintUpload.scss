.blueprint-upload {
  .upload-area {
    padding: 40px;
    text-align: center;

    .upload-icon {
      font-size: 48px;
      color: #1890ff;
      margin-bottom: 16px;
    }

    .upload-content {
      margin-bottom: 16px;

      .upload-title {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 8px;
      }

      .upload-description {
        color: #666;
        margin: 0;
      }
    }
  }

  .uploaded-files {
    margin-top: 16px;

    h4 {
      margin-bottom: 12px;
      font-weight: 500;
    }

    .file-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      margin-bottom: 8px;
      background: #fafafa;

      .file-info {
        display: flex;
        align-items: center;
        flex: 1;

        .file-thumbnail {
          width: 60px;
          height: 80px;
          margin-right: 12px;
          border: 1px solid #d9d9d9;
          border-radius: 4px;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: center;
          background: white;

          .thumbnail-loading,
          .thumbnail-error {
            font-size: 12px;
            color: #666;
          }
        }

        .file-details {
          .file-name {
            display: block;
            font-weight: 500;
            margin-bottom: 4px;
          }

          .file-size {
            font-size: 12px;
            color: #666;
          }
        }
      }

      .file-actions {
        display: flex;
        gap: 8px;
      }
    }
  }

  .pdf-preview {
    text-align: center;   
  }
}
