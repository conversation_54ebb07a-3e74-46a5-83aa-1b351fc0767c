import React, { forwardRef } from "react";
import logo from "../../../src/assets/images/logo.png";

const styles = {
  page: {
    padding: 24,
    fontSize: 15,
    fontFamily: "Roboto, Arial, sans-serif",
    background: "#fff",
  },
  headerRow: {
    display: "flex",
    flexDirection: "row" as const,
    alignItems: "center",
    marginBottom: 8,
  },
  logoBox: {
    border: "1.5px solid #0054A6",
    padding: "8px 18px",
    color: "red",
    fontWeight: "bold" as const,
    fontSize: 15,
    marginRight: 8,
    minWidth: 90,
    textAlign: "center" as const,
  },
  logo: { width: 70, height: 32, objectFit: "contain" as const },
  project: {
    flex: 1,
    textAlign: "center" as const,
    fontWeight: "bold" as const,
    fontFamily: "Roboto",
    fontSize: 15,
    letterSpacing: 1,
  },
  table: {
    borderCollapse: "collapse" as const,
    width: "100%",
    margin: 0,
    color: "#000",
  },
  th: {
    border: "1px solid #000",
    padding: "4px 6px",
    fontWeight: "bold" as const,
    fontSize: 15,
    background: "#fff",
    textAlign: "left" as const,
  },
  td: {
    border: "1px solid #000",
    padding: "4px 6px",
    fontSize: 15,
    background: "#fff",
    verticalAlign: "top" as const,
  },
  labelEn: {
    color: "#0054A6",
    fontStyle: "italic" as const,
    fontWeight: "bold" as const,
  },
  labelVi: {
    color: "#000",
    fontStyle: "normal" as const,
    fontWeight: "normal" as const,
  },
  sectionTitle: {
    fontWeight: "bold" as const,
    color: "#0054A6",
    fontSize: 20,
    textAlign: "center" as const,
    margin: "4px 0",
    fontFamily: "Roboto",
    letterSpacing: 1,
  },
  small: { fontSize: 10 },
  center: { textAlign: "center" as const },
};

export const ExportPdfRFIs = forwardRef<HTMLDivElement, { data?: any }>(
  function ExportPdfRFIs(props, ref) {
    const data = props.data || {};
    return (
      <div ref={ref} style={styles.page}>
        {/* Header */}
        <div style={styles.headerRow}>
          <div style={styles.logoBox}>Logo CDT</div>
          <div style={styles.project}>
            PROJECT: 
            {data.projectName || "Tên dự án"}
          </div>
          <div style={{ width: 120, textAlign: "right" }}>
            <img src={logo} alt="Logo" style={styles.logo} />
          </div>
        </div>
        {/* Table */}
        <table style={styles.table}>
          <tbody>
            {/* Title */}
            <tr>
              <td
                colSpan={8}
                style={{ ...styles.th, textAlign: "center", padding: 8 }}
              >
                <div style={styles.sectionTitle}>
                  <span style={styles.labelEn}>REQUEST FOR INFORMATION</span>
                  <br />
                  <span style={{ ...styles.labelVi, fontWeight: "bold" }}>
                    PHIẾU YÊU CẦU CUNG CẤP THÔNG TIN
                  </span>
                </div>
              </td>
            </tr>
            {/* Item, Date, No. */}
            <tr>
              <td style={styles.th} colSpan={2}>
                <span style={styles.labelEn}>Item:</span> <br />
                <span style={{ ...styles.labelVi, fontWeight: "bold" }}>
                  Hạng mục:
                </span>
              </td>
              <td style={styles.td} colSpan={4}>
                {data.item}
              </td>
              <td style={styles.th}>
                <span style={styles.labelEn}>Date:</span> <br />
                <span style={styles.labelVi}>Ngày:</span>
                <span style={{ marginLeft: 8, fontWeight: "normal" }}>
                  {data.date}
                </span>
              </td>
              <td style={styles.th}>
                <span style={styles.labelEn}>No.:</span> <br />
                <span style={styles.labelVi}>Số:</span>
                <span style={{ marginLeft: 8, fontWeight: "normal" }}>
                  {data.no}
                </span>
              </td>
            </tr>
            {/* From */}
            <tr>
              <td style={styles.th} rowSpan={2}>
                <span style={styles.labelEn}>From:</span> <br />
                <span style={{ ...styles.labelVi, fontWeight: "bold" }}>
                  Gửi từ:
                </span>
              </td>
              <td style={styles.th}>
                <span style={styles.labelEn}>Company:</span> <br />
                <span style={styles.labelVi}>Công ty</span>
              </td>
              <td style={styles.td} colSpan={6}>
                {data.fromCompany}
              </td>
            </tr>
            <tr>
              <td style={styles.th}>
                <span style={styles.labelEn}>Sent by:</span> <br />
                <span style={styles.labelVi}>Người gửi</span>
              </td>
              <td style={styles.td} colSpan={3}>
                {data.fromName}
              </td>
              <td style={styles.th}>
                <span style={styles.labelEn}>Position:</span> <br />
                <span style={styles.labelVi}>Chức vụ</span>
              </td>
              <td style={styles.td} colSpan={2}>
                {data.fromPosition}
              </td>
            </tr>

            {/* To */}
            <tr>
              <td style={styles.th} rowSpan={2}>
                <span style={styles.labelEn}>To:</span> <br />
                <span style={{ ...styles.labelVi, fontWeight: "bold" }}>
                  Gửi đến:
                </span>
              </td>
              <td style={styles.th}>
                <span style={styles.labelEn}>Company:</span> <br />
                <span style={styles.labelVi}>Công ty</span>
              </td>
              <td style={styles.td} colSpan={6}>
                {data.toCompany}
              </td>
            </tr>
            <tr>
              <td style={styles.th}>
                <span style={styles.labelEn}>Attention:</span> <br />
                <span style={styles.labelVi}>Người nhận</span>
              </td>
              <td style={styles.td} colSpan={3}>
                {data.toName}
              </td>
              <td style={styles.th}>
                <span style={styles.labelEn}>Position:</span> <br />
                <span style={styles.labelVi}>Chức vụ</span>
              </td>
              <td style={styles.td} colSpan={2}>
                {data.toPosition}
              </td>
            </tr>

            {/* CC */}
            <tr>
              <td style={styles.th}>
                <span style={styles.labelEn}>C.C.:</span> <br />
                <span style={styles.labelVi}>Đồng kính gửi:</span>
              </td>
              <td style={styles.th}>
                <span style={styles.labelEn}>Attention:</span> <br />
                <span style={styles.labelVi}>Người nhận</span>
              </td>
              <td style={styles.td} colSpan={3}>
                {data.ccName}
              </td>
              <td style={styles.th}>
                <span style={styles.labelEn}>Position:</span> <br />
                <span style={styles.labelVi}>Chức vụ</span>
              </td>
              <td style={styles.td} colSpan={2}>
                {data.ccPosition}
              </td>
            </tr>
            {/* Expected date of reply */}
            <tr>
              <td style={styles.th} colSpan={8}>
                <span style={styles.labelEn}>Expected date of reply:</span>{" "}
                <br />
                <span style={styles.labelVi}>Ngày yêu cầu phản hồi:</span>
                <span style={{ marginLeft: 8, fontWeight: "normal" }}>
                  {data.expectedReply}
                </span>
              </td>
            </tr>

            {/* Query/Thông tin yêu cầu */}
            <tr>
              <td style={styles.th} colSpan={8}>
                <span style={styles.labelEn}>Query. Information required/</span>{" "}
                <span style={{ ...styles.labelVi, fontWeight: "bold" }}>
                  Thông tin yêu cầu:
                </span>
              </td>
            </tr>
            <tr>
              <td style={styles.td} colSpan={8}>
                <div style={{ minHeight: 200 }}>{data.requestInfo}</div>
              </td>
            </tr>
            {/* Attachment */}
            <tr>
              <td style={styles.th} colSpan={2}>
                <span style={styles.labelEn}>Attachment:</span> <br />
                <span style={styles.labelVi}>Đính kèm</span>
              </td>
              <td style={styles.td} colSpan={6}>
                {data.attachment}
              </td>
            </tr>
            {/* Requested by */}
            <tr>
              <td style={styles.th} colSpan={2}>
                <span style={styles.labelEn}>Requested by:</span> <br />
                <span style={styles.labelVi}>Người yêu cầu</span>
              </td>
              <td style={styles.th} colSpan={3}>
                <span style={styles.labelEn}>Name</span>
                <br />
                <span style={styles.labelVi}>Tên</span>
              </td>
              <td style={styles.th} colSpan={1}>
                <span style={styles.labelEn}>Position</span>
                <br />
                <span style={styles.labelVi}>Chức vụ</span>
              </td>
              <td style={styles.th} colSpan={1}>
                <span style={styles.labelEn}>Signature</span>
                <br />
                <span style={styles.labelVi}>Chữ ký</span>
              </td>
              <td style={styles.th} colSpan={1}>
                <span style={styles.labelEn}>Date</span>
                <br />
                <span style={styles.labelVi}>Ngày</span>
              </td>
            </tr>

            {/* Replied by Consultant */}
            <tr>
              <td style={styles.th} colSpan={8}>
                <span style={styles.labelEn}>Replied by Consultant/</span>{" "}
                <span style={{ ...styles.labelVi, fontWeight: "bold" }}>
                  Ý kiến phản hồi
                </span>{" "}
              </td>
            </tr>
            <tr>
              <td style={styles.td} colSpan={8}>
                <div style={{ minHeight: 40 }}>{data.consultantReply}</div>
              </td>
            </tr>

            {/* Replied by */}
            <tr>
              <td style={styles.th} colSpan={2}>
                <span style={styles.labelEn}>Replied by:</span> <br />
                <span style={styles.labelVi}>Người phản hồi</span>
              </td>
              <td style={styles.th} colSpan={3}>
                <span style={styles.labelEn}>Name</span>
                <br />
                <span style={styles.labelVi}>Tên</span>
              </td>
              <td style={styles.th} colSpan={1}>
                <span style={styles.labelEn}>Position</span>
                <br />
                <span style={styles.labelVi}>Chức vụ</span>
              </td>
              <td style={styles.th} colSpan={1}>
                <span style={styles.labelEn}>Signature</span>
                <br />
                <span style={styles.labelVi}>Chữ ký</span>
              </td>
              <td style={styles.th} colSpan={1}>
                <span style={styles.labelEn}>Date</span>
                <br />
                <span style={styles.labelVi}>Ngày</span>
              </td>
            </tr>

            {/* Attachment cuối */}
            <tr>
              <td style={styles.th} colSpan={2}>
                <span style={styles.labelEn}>Attachment:</span> <br />
                <span style={styles.labelVi}>Đính kèm</span>
              </td>
              <td style={styles.td} colSpan={6}>
                {data.attachment2}
              </td>
            </tr>
          </tbody>
        </table>
        {/* Footer */}
        <div style={{ marginTop: 16, fontSize: 10 }}>MGD-QA-6</div>
      </div>
    );
  }
);
