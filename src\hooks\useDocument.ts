import { documentApi } from "api/document.api";
import { useState } from "react";
import { Document } from "types/document";
import { QueryParam } from "types/query";

export interface DocumentQuery extends QueryParam {
  path?: string;
}

interface UseDocumentProps {
  initQuery: DocumentQuery;
}

export const useDocument = ({ initQuery }: UseDocumentProps) => {
  const [data, setData] = useState<Document[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<DocumentQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await documentApi.findAll(query);

      setData(data.documents.map((doc: any) => ({
        ...doc, approvalLists: [
          {
            "id": 362,
            "createdAt": 1753256927,
            "updatedAt": 1753264683,
            "deletedAt": 0,
            "isDeleted": false,
            "name": "CREATE",
            "type": "INSTRUCTION",
            "position": 0,
            "note": "",
            "status": "PENDING",
            "version": 0,
            "approveAt": 0,
            "rejectAt": 0
          },
          {
            "id": 363,
            "createdAt": 1753256927,
            "updatedAt": 1753264683,
            "deletedAt": 0,
            "isDeleted": false,
            "name": "PUBLISH",
            "type": "INSTRUCTION",
            "position": 1,
            "note": "g",
            "status": "APPROVED",
            "version": 0,
            "approveAt": 1753262143,
            "rejectAt": 0
          }
        ]
      })));
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return { documents: data, total, fetchData, loading, setQuery, query };
};
