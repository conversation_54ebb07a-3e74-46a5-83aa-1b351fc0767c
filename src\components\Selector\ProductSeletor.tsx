import { Select } from "antd";
import { DefaultOptionType } from "antd/es/select";
import { SelectProps } from "antd/lib";
import { useColor } from "hooks/useColor";
import { useMaterial } from "hooks/useMaterial";
import { useMaterialGroup } from "hooks/useMaterialGroup";
import { useProduct } from "hooks/useProduct";
import { debounce, uniqBy } from "lodash";
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
} from "react";
import { Material } from "types/material";
import { MaterialGroup } from "types/materialGroup";
import { Product, ProductType } from "types/product";
import { QueryParams2 } from "types/query";

type CustomFormItemProps = {
  value?: number;
  initQuery?: QueryParams2;
  disabled?: boolean;
  selectedColor?: Material[];
  multiple?: boolean;
  onChange?: (value: any) => void;
  selectProps?: SelectProps;
  initOptionItem?: Product | Product[];
  valueIsOption?: boolean;
  allowClear?: boolean;
  placeholder?: string;
  isJustSingleProduct?: boolean;
};

export interface ProductSelector {
  refresh(): void;
}

/**
 * A demo that changes it's number value on click.
 * @param value initialValue passed by parent Form.Item.
 * @param onChange a callback for Form.Item to read its child component's value.
 * @constructor
 */
export const ProductSelector = forwardRef(
  (
    {
      value,
      onChange,
      initQuery,
      disabled,
      multiple = false,
      selectedColor,
      initOptionItem,
      valueIsOption,
      selectProps,
      allowClear = true,
      placeholder = "Chọn sản phẩm",
      isJustSingleProduct = false,
    }: CustomFormItemProps,
    ref
  ) => {
    const { products, total, loading, fetchData, query } = useProduct({
      initQuery: {
        page: 1,
        limit: 50,
        type: isJustSingleProduct ? ProductType.Single : undefined,
        ...initQuery,
      },
    });

    useImperativeHandle<any, ProductSelector>(
      ref,
      () => ({
        refresh() {
          fetchData();
        },
      }),
      []
    );

    useEffect(() => {
      fetchData();
    }, [selectedColor]);

    const debounceSearch = useCallback(
      debounce((keyword) => {
        query.search = keyword;
        fetchData();
      }, 300),
      [query]
    );

    const options = useMemo(() => {
      let data = [...products];
      if (initOptionItem) {
        if ((initOptionItem as Product[])?.length) {
          data = data.concat(initOptionItem);
        } else {
          data.push(initOptionItem as Product);
        }
      }

      return uniqBy(data, (data) => data.id);
    }, [products, initOptionItem]);

    return (
      <Select<any, DefaultOptionType>
        mode={multiple ? "multiple" : undefined}
        disabled={disabled}
        loading={loading}
        style={{ width: "100%" }}
        allowClear={allowClear}
        onSearch={debounceSearch}
        showSearch
        placeholder={placeholder}
        filterOption={false}
        value={value}
        onChange={(v, opts) => {
          if (v === undefined || v.length == 0) {
            debounceSearch(v);
          }
          if (valueIsOption) {
            if (opts instanceof Array) {
              onChange?.(opts?.map((v) => v.item));
            } else {
              onChange?.(opts?.item);
            }
          } else {
            onChange?.(v);
          }
        }}
        {...selectProps}
      >
        {options?.map((item) => (
          <Select.Option item={item} value={item.id} key={item.id}>
            <div className="flex items-center gap-2">
              <span>{item.name}</span>
            </div>
          </Select.Option>
        ))}
      </Select>
    );
  }
);
