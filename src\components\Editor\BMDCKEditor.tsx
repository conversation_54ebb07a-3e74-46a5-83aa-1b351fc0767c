import { UploadOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, <PERSON> } from "antd";
import React, {
  useImperativeHandle,
  useState,
  useRef,
  useEffect,
} from "react";
import { CKEditor } from "@ckeditor/ckeditor5-react";
import ClassicEditor from "@ckeditor/ckeditor5-build-classic";
import { $url } from "utils/url";
import { UploadImageModal } from "./components/UploadImageModal";
import "./BMDCKEditor.scss";

let editorRef: any = null;

interface IBMDCKEditor {
  onChange: (content: string) => void;
  onInit?: () => void;
  inputHeight?: number;
  uploadUrl?: string;
  placeholder?: string;
  disabled?: boolean;
  value?: string;
  label?: React.ReactNode;
}

export interface IBMDCKEditorRef {
  setContent(content: string): void;
  getContent(): string;
}

export const BMDCKEditor = React.forwardRef(
  (
    {
      onChange,
      onInit,
      inputHeight = 500,
      uploadUrl,
      placeholder = "Nhập nội dung...",
      disabled = false,
      value = "",
      label,
    }: IBMDCKEditor,
    ref
  ) => {
    const [initValue, setInitValue] = useState(value || "");
    const [isEditorReady, setIsEditorReady] = useState(false);
    const [visibleUploadModal, setVisibleUploadModal] = useState(false);
    const editorInstanceRef = useRef<any>(null);

    useEffect(() => {
      if (
        editorInstanceRef.current &&
        value !== editorInstanceRef.current.getData()
      ) {
        editorInstanceRef.current.setData(value || "");
      }
      setInitValue(value || "");
    }, [value]);

    useImperativeHandle(ref, () => ({
      setContent: (content: string) => {
        if (editorInstanceRef.current) {
          editorInstanceRef.current.setData(content || "");
          setInitValue(content || "");
        }
      },
      getContent: () => {
        return editorInstanceRef.current?.getData() || "";
      },
    }));

    const editorConfiguration = {
      placeholder: placeholder,
      height: inputHeight,
      toolbar: [
        "heading",
        "|",
        "bold",
        "italic",
        "underline",
        "strikethrough",
        "|",
        "fontSize",
        "fontColor",
        "fontBackgroundColor",
        "|",
        "alignment",
        "|",
        "numberedList",
        "bulletedList",
        "|",
        "indent",
        "outdent",
        "|",
        "link",
        "image",
        "blockQuote",
        "insertTable",
        "mediaEmbed",
        "|",
        "undo",
        "redo",
      ],
      table: {
        contentToolbar: ["tableColumn", "tableRow", "mergeTableCells"],
      },
      language: "vi",
      image: {
        upload: {
          types: ["jpeg", "png", "gif", "bmp", "webp", "tiff"],
        },
      },
    };

    return (
      <>
        {label && (
          <div style={{ fontWeight: 600, marginBottom: 8 }}>{label}</div>
        )}

        {!isEditorReady && (
          <Spin
            style={{
              width: "100%",
              height: 200,
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
            spinning={true}
          />
        )}

        <div
          className={`bmd-ckeditor ${disabled ? "disabled" : ""}`}
          style={{
            opacity: disabled ? 0.6 : 1,
            pointerEvents: disabled ? "none" : "auto",
            marginTop: 6,
          }}
        >
          <CKEditor
            editor={ClassicEditor}
            config={editorConfiguration}
            data={initValue || ""}
            onReady={(editor) => {
              editorRef = editor;
              editorInstanceRef.current = editor;
              setIsEditorReady(true);
              onInit?.();
            }}
            onChange={(event, editor) => {
              const data = editor.getData();
              onChange(data);
            }}
          />
        </div>

        {visibleUploadModal && (
          <UploadImageModal
            uploadUrl={uploadUrl}
            onClose={() => setVisibleUploadModal(false)}
            onSubmitOk={(imageUrl) => {
              setVisibleUploadModal(false);
              if (editorInstanceRef.current) {
                editorInstanceRef.current.model.change((writer: any) => {
                  const insertPosition =
                    editorInstanceRef.current.model.document.selection.getFirstPosition();
                  const imageElement = writer.createElement("image", {
                    src: imageUrl,
                    alt: "Uploaded image",
                  });
                  editorInstanceRef.current.model.insertContent(
                    imageElement,
                    insertPosition
                  );
                });
              }
            }}
            visible={visibleUploadModal}
          />
        )}
      </>
    );
  }
);

BMDCKEditor.displayName = "BMDCKEditor";
