import { Col, Form, Input, message, Modal, Row } from "antd";
import { Rule } from "antd/lib/form";
import { goodsApi } from "api/goods.api";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { FileAttach } from "types/fileAttach";
import { ModalStatus } from "types/modal";
// import { Goods } from "types/Goods";

const rules: Rule[] = [{ required: true }];

export interface GoodsModal {
  handleCreate: () => void;
  handleUpdate: (Goods: any) => void;
}
interface GoodsModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
}

export const GoodsModal = React.forwardRef(
  ({ onClose, onSubmitOk }: GoodsModalProps, ref) => {
    const [form] = Form.useForm<any>();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");

    useImperativeHandle<any, GoodsModal>(
      ref,
      () => ({
        handleCreate() {
          form.resetFields();
          setVisible(true);
          setStatus("create");
        },
        handleUpdate(Goods: any) {
          form.setFieldsValue({ ...Goods });
          setVisible(true);
          setStatus("update");
        },
      }),
      []
    );

    const createData = async () => {
      const valid = await form.validateFields();
      const data = { Goods: form.getFieldsValue() };

      setLoading(true);
      try {
        const res = await goodsApi.create(data);
        message.success("Create Goods successfully!");
        onClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const valid = await form.validateFields();
      const data = { Goods: form.getFieldsValue() };
      setLoading(true);
      try {
        const res = await goodsApi.update(data.Goods.id || 0, data);
        message.success("Update Goods successfully!");
        onClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    return (
      <Modal
        onCancel={() => {
          onClose?.();
          setVisible(false);
        }}
        visible={visible}
        title={status == "create" ? "Create Goods" : "Update Goods"}
        style={{ top: 20 }}
        width={700}
        confirmLoading={loading}
        onOk={() => {
          status == "create" ? createData() : updateData();
        }}
      >
        <Form layout="vertical" form={form}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="Username" name="username" rules={rules}>
                <Input placeholder="" />
              </Form.Item>
            </Col>

            {status == "create" && (
              <Col span={12}>
                <Form.Item label="Password" name="password" rules={rules}>
                  <Input placeholder="" />
                </Form.Item>
              </Col>
            )}

            <Col span={12}>
              <Form.Item label="Name" name="name" rules={rules}>
                <Input placeholder="" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item label="Phone" name="phone" rules={rules}>
                <Input placeholder="" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item label="Email" name="email">
                <Input placeholder="" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item shouldUpdate={true}>
            {() => {
              return (
                <Form.Item label="Avatar" name="avatar">
                  <SingleImageUpload
                    onUploadOk={(file: FileAttach) => {
                      console.log("onUploadOk:", file);
                      form.setFieldsValue({
                        avatar: file.path,
                      });
                    }}
                    imageUrl={form.getFieldValue("avatar")}
                  />
                </Form.Item>
              );
            }}
          </Form.Item>
        </Form>
      </Modal>
    );
  }
);
