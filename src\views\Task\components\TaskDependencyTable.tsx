import { useState, useEffect } from "react";
import { Input, Button, Space, Modal, message } from "antd";
import { PlusOutlined } from "@ant-design/icons";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import { Task } from "types/task";
import { taskApi } from "api/task.api";
import EditButton from "components/Button/EditButton";
import DeleteButton from "components/Button/DeleteButton";
import dayjs from "dayjs";
import CreateOrUpdateTaskPage from "../CreateOrUpdateTaskPage";
import CustomInput from "components/Input/CustomInput";
import CustomButton from "components/Button/CustomButton";
import QueryLabel from "components/QueryLabel/QueryLabel";
import ProgressLegend from "components/ProgressLegend/ProgressLegend";
import {
  getOverallApprovalStatus,
  ProgressInstructionStatus,
} from "types/instruction";
import { useTask } from "hooks/useTask";

interface TaskDependencyTableProps {
  parentId: number;
}

export const TaskDependencyTable = ({ parentId }: TaskDependencyTableProps) => {
  const [search, setSearch] = useState("");
  const [data, setData] = useState<Task[]>([]);
  const [loading, setLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);

  const { fetchDataDetail } = useTask({ initQuery: { page: 1, limit: 100 } });

  useEffect(() => {
    const fetchChildren = async () => {
      setLoading(true);
      try {
        const parentTask = await fetchDataDetail(parentId);
        setData(parentTask?.children || []);
      } finally {
        setLoading(false);
      }
    };
    fetchChildren();
  }, [parentId, search, showModal]);

  const handleDelete = async (id: number) => {
    await taskApi.delete(id);
    message.success("Đã xóa công việc");
    // Sau khi xóa, reload lại danh sách
    const parentTask = await fetchDataDetail(parentId);
    setData(parentTask?.children || []);
  };

  const columns: CustomizableColumn<Task>[] = [
    {
      title: "ID",
      dataIndex: "id",
      width: 60,
      key: "id",
    },
    {
      title: "Công việc",
      dataIndex: "title",
      key: "title",
    },
    {
      title: "Loại phụ thuộc",
      dataIndex: "dependencyType",
      render: (_, record) => record.dependencyType,
      key: "dependencyType",
    },
    {
      title: "Người phụ trách",
      dataIndex: "assigneeMemberShip",
      render: (_, record) => record.assigneeMemberShip?.staff?.fullName || "",
      key: "assigneeMemberShip",
    },
    {
      title: "Ngày đến hạn",
      dataIndex: "endDate",
      render: (_, record) => dayjs(record.endDate).format("DD/MM/YYYY"),
      key: "endDate",
    },
    {
      title: "Trạng thái",
      dataIndex: "status",
      key: "status",
      render: (_, record) => {
        // Sort approvalLists theo position trước khi xử lý
        const sortedApprovalLists = record.approvalLists
          ? record.approvalLists.slice().sort((a, b) => a.position - b.position)
          : [];
        return (
          <ProgressLegend
            status={
              ProgressInstructionStatus[
                getOverallApprovalStatus(sortedApprovalLists)
              ]
            }
            steps={sortedApprovalLists}
          />
        );
      },
    },
    {
      title: "Hành động",
      dataIndex: "actions",
      width: 100,
      render: (_, record) => (
        <Space>
          <EditButton
            onClick={() => {
              setShowModal(true);
            }}
          />
          <DeleteButton onClick={() => handleDelete(record.id)} />
        </Space>
      ),
      key: "actions",
    },
  ];

  return (
    <>
      <div className="flex flex-wrap gap-[16px] items-end max-w-full mb-4">
        <div className="w-[300px]">
          <CustomInput
            tooltipContent={"Tìm theo mã, tên công việc"}
            label="Tìm kiếm"
            placeholder="Tìm kiếm"
            value={search}
            onChange={(e) => setSearch(e)}
            onPressEnter={() => fetchDataDetail(parentId)}
            allowClear
          />
        </div>
        <CustomButton
          size="small"
          showPlusIcon
          onClick={() => setShowModal(true)}
        >
          Thêm công việc phụ thuộc
        </CustomButton>
      </div>
      <CustomizableTable
        columns={columns}
        dataSource={data}
        loading={loading}
        rowKey="id"
        scroll={{ x: 1000 }}
        tableId="task-dependency-table"
      />
      <Modal
        open={showModal}
        onCancel={() => setShowModal(false)}
        footer={null}
        width={1800}
        destroyOnClose
      >
        <CreateOrUpdateTaskPage
          title="Tạo công việc phụ thuộc"
          status="create"
          parentId={parentId}
        />
      </Modal>
    </>
  );
};
