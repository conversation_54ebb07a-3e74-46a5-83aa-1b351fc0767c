import { Route } from "./RouteType";
import { ReactComponent as ProjectIcon } from "assets/svgs/project.svg";
import { PermissionType } from "types/permission";
import { PermissionNames } from "types/PermissionNames";
import { lazy } from "react";

const CreateOrUpdateProjectPage = lazy(
  () => import("views/Project/CreateOrUpdateProjectPage")
);
const ProjectPage = lazy(() => import("views/Project/ProjectPage"));

export const projectRoutes: Route[] = [
  {
    title: "Dự án",
    breadcrumb: "project",
    path: "/project",
    aliasPath: "/project",
    name: "project",
    icon: <ProjectIcon />,
    // element: <ProjectPage />,
    permissionTypes: [PermissionType.Add, PermissionType.List],
    isCompact: true,
    children: [
      // {
      //   title: "Chi tiết dự án",
      //   breadcrumb: "Chi tiết dự án",
      //   path: "project-detail/:id",
      //   name: "project-detail",
      //   aliasPath: "/project/project-detail",
      //   element: <ProjectDetailPage />,
      //   permissionTypes: [PermissionType.List],
      //   hidden: true,
      //   isPublic: true,
      // },
      {
        title: "Tạo dự án",
        path: PermissionNames.projectAdd,
        name: PermissionNames.projectAdd,
        aliasPath: `/project/${PermissionNames.projectAdd}`,
        element: (
          <CreateOrUpdateProjectPage title="Tạo dự án" status="create" />
        ),
        permissionTypes: [PermissionType.Add],
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.projectViewAll,
        name: PermissionNames.projectViewAll,
        aliasPath: `/project/${PermissionNames.projectViewAll}`,
        hidden: true,
      },
      {
        title: "Danh sách dự án",
        breadcrumb: "project",
        path: PermissionNames.projectList,
        aliasPath: `/project/${PermissionNames.projectList}`,
        name: PermissionNames.projectList,
        element: <ProjectPage title="Danh sách dự án" />,
        permissionTypes: [PermissionType.List],
        isPublic: true,
      },
      {
        title: "Cập nhật trạng thái dự án",
        path: PermissionNames.projectEditStatus,
        name: PermissionNames.projectEditStatus,
        aliasPath: `/project/${PermissionNames.projectEditStatus}`,
        element: (
          <CreateOrUpdateProjectPage
            title="Cập nhật trạng thái dự án"
            status="update"
          />
        ),
        permissionTypes: [PermissionType.Edit],
        hidden: true,
      },
    ],
  },
];
