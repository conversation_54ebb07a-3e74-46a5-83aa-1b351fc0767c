import { staffApi } from "api/staff.api";
import { useState, useMemo } from "react";
import { Staff } from "types/staff";
import { QueryParam } from "types/query";
import { message } from "antd";
export interface StaffQuery extends QueryParam { }
interface UseStaffProps {
  initQuery: StaffQuery;
}
export const useStaff = ({ initQuery }: UseStaffProps) => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<Staff[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<StaffQuery>(initQuery);

  const isEmptyQuery = useMemo(() => {
    const defaultKeys = ["limit", "page", "queryObject", "companyType"];
    return (
      Object.keys(query).filter(
        (k) => query[k] != null && query[k] != "" && !defaultKeys.includes(k)
      ).length === 0
    );
  }, [query]);

  const fetchStaff = async (newQuery?: StaffQuery) => {
    setLoading(true);
    const { data } = await staffApi.findAll({ ...query, ...newQuery });

    setData(data.staffs);
    setTotal(data.total);
    setLoading(false);
  };

  const deleteStaff = async (id: number) => {
    setLoading(true);
    try {
      await staffApi.delete(id);
      message.success("Đã xóa người dùng này");
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  return {
    staffs: data,
    total,
    fetchStaff,
    loading,
    deleteStaff,
    query,
    setQuery,
    isEmptyQuery
  };
};
