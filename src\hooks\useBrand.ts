import { brandApi } from "api/brand.api";
import { useState } from "react";
import { Brand } from "types/brand";
import { QueryParam } from "types/query";

export interface BrandQuery extends QueryParam {}

interface UseBrandProps {
  initQuery: BrandQuery;
}

export const useBrand = ({ initQuery }: UseBrandProps) => {
  const [data, setData] = useState<Brand[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<BrandQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await brandApi.findAll(query);

      setData(data.brands);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return { brands: data, total, fetchData, loading, setQuery, query };
};
