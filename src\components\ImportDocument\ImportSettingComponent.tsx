import { DownloadOutlined, InboxOutlined } from "@ant-design/icons";
import {
  <PERSON><PERSON>,
  Button,
  Modal,
  Select,
  Space,
  Spin,
  Table,
  Upload,
  message,
} from "antd";
import { Rule } from "antd/es/form";
import FormItem from "antd/es/form/FormItem";
import { componentApi } from "api/component.api";
import { materialGroupApi } from "api/materialGroup.api";
import dayjs from "dayjs";
import { chunk } from "lodash";
import { toJS } from "mobx";
import { forwardRef, useEffect, useImperativeHandle, useState } from "react";
import { Link } from "react-router-dom";
import {
  Component,
  ComponentShowType,
  ComponentShowTypeTrans,
} from "types/component";
import { MaterialGroup } from "types/materialGroup";
import { readerData } from "utils/excel2";
const rules: Rule[] = [{ required: true }];

const { Dragger } = Upload;

export interface ImportSettingComponentModal {
  open: () => void;
  close: () => void;
}

export interface IValidate {
  index: number;
  message: string;
}

interface DataImportReturn {
  msg: string;
  rowNum: number;
  status: string;
}

export interface MaterialGroupImport extends Component {
  rowNum: number;
}

interface IProps {
  onSuccess?: () => void;
  createApi?: (data: any) => any;
  onUploaded?: (excelData: any, setData: (data: any) => any) => void;
  demoExcel?: string;
  guide?: React.ReactNode[];
  uploadText?: string;
  okText?: string;
  onClose?: () => void;
  titleText?: string;
  validateMessage?: IValidate[];
  onDownloadDemoExcel?: () => void;
  //   setMaterialGroupId: (MaterialGroupId: number) => void;
}

const ImportSettingComponent = forwardRef(
  (
    {
      onSuccess,
      createApi,
      onUploaded,
      onClose,
      validateMessage,
      guide,
      demoExcel,
      uploadText = "Kéo thả hoặc click vào đây để upload file",
      okText = "Nhập dữ liệu ngay",
      titleText = "Nhập excel dữ liệu",
      onDownloadDemoExcel,
    }: //   setMaterialGroupId,
    IProps,
    ref
  ) => {
    const [errorsLog, setErrorsLog] = useState<any[]>([]);
    const [dataPosts, setDataPosts] = useState<MaterialGroupImport[]>([]);
    const [visible, setVisible] = useState<boolean>(false);
    const [loading, setLoading] = useState(false);
    const [dataReturn, setDataReturn] = useState<{
      data: DataImportReturn[];
      successCount: number;
      errorCount: number;
    }>();

    useEffect(() => {
      if (validateMessage?.length) {
        setErrorsLog([]);
      }
    }, [validateMessage]);
    const getFeatureImageShowType = (
      label: string
    ): ComponentShowType | undefined => {
      const entry = Object.values(ComponentShowTypeTrans).find(
        (item) => item.label === label
      );
      return entry ? entry.value : undefined;
    };
    const handleOnImport = async () => {
      if (!dataPosts.length) return;
      let errors: any = [];

      try {
        const { data } = await componentApi.import({
          components: dataPosts.map((dataPost) => ({
            ...dataPost,
            featureImageShowType: getFeatureImageShowType(
              dataPost?.featureImageShowType
            ),
          })),
        });
        if (data.length) {
          const successCount = data.reduce(
            (acc: number, item: DataImportReturn) => {
              if (item.status == "ok") return acc + 1;
              return acc;
            },
            0
          );
          const errorCount = data.reduce(
            (acc: number, item: DataImportReturn) => {
              if (item.status == "error") return acc + 1;
              return acc;
            },
            0
          );
          setDataReturn({ data, successCount, errorCount });
          onSuccess?.();
          setDataPosts([]);
        }
      } catch (err) {
        console.log({ err });
      } finally {
        setLoading(false);
      }
    };

    const handleOnCancel = () => {
      setVisible(false);
      onClose?.();
    };
    useImperativeHandle(
      ref,
      () => ({
        open: () => setVisible(true),
        close: () => setVisible(false),
      }),
      []
    );

    return (
      <Modal
        maskClosable={false}
        width={1000}
        style={{ top: 50 }}
        visible={visible}
        onCancel={handleOnCancel}
        destroyOnClose={true}
        afterClose={() => {
          setDataPosts([]);
          setErrorsLog([]);
          setDataReturn(undefined);
        }}
        title={titleText}
        footer={[
          <Button
            type="primary"
            disabled={!dataPosts.length}
            onClick={() => {
              handleOnImport();
            }}
            style={{ color: "#fff" }}
          >
            {okText}
          </Button>,
          <Button
            onClick={() => {
              handleOnCancel();
            }}
          >
            Đóng
          </Button>,
        ]}
      >
        <Spin spinning={false}>
          {guide && (
            <Alert
              style={{ padding: "10px", marginBottom: "10px" }}
              message={<b>Lưu ý</b>}
              type="warning"
              description={
                <ul>
                  {guide.map((text, index) => (
                    <li key={index}>
                      <p>{text}</p>
                    </li>
                  ))}
                </ul>
              }
            />
          )}
          {demoExcel && (
            <Link to={demoExcel} target="_blank" download>
              <Space
                className={`flex gap-2 cursor-pointer`}
                // onClick={() => onDownloadDemoExcel(selectedMaterialGroupId!)}
              >
                <DownloadOutlined />
                Tải file import mẫu{" "}
              </Space>
            </Link>
          )}
          {onDownloadDemoExcel && (
            <a>
              <Space
                className={`flex gap-2 cursor-pointer`}
                onClick={() => {
                  onDownloadDemoExcel();
                }}
              >
                <DownloadOutlined />
                Tải file import mẫu
              </Space>
            </a>
          )}

          <Dragger
            style={{ marginTop: "0.5em" }}
            maxCount={1}
            multiple={false}
            beforeUpload={async (file) => {
              //Check file type
              const isCSVFile = file.name.includes("xlsx");
              if (isCSVFile === false) {
                message.error("Bạn chỉ có thể upload file excel!");
                return Upload.LIST_IGNORE;
              }
              // debugger;
              const excelData = await readerData(file, 0);
              setDataReturn(undefined);
              console.log("Data khi import vào là", excelData);
              onUploaded?.(excelData, setDataPosts);
              return false;
            }}
            onChange={(info) => {
              //reset data
              if (info.fileList.length == 0) {
                setErrorsLog([]);
                setDataPosts([]);
              }
            }}
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">{uploadText}</p>
          </Dragger>

          {dataReturn && (
            <Alert
              className="p-3 mt-2"
              type="warning"
              description={
                <div>
                  <div className="text-blue-600 font-bold">
                    Tổng dòng nhập: {dataReturn.data.length}
                  </div>
                  <div className="text-green-500">
                    Tổng dòng thành công: {dataReturn.successCount}
                  </div>
                  <div className="text-red-500">
                    Tổng dòng thất bại: {dataReturn.errorCount}
                  </div>
                  <div className="font-bold">Danh sách dòng thất bại</div>
                  <div className="border-[1px] border-red-300 border-solid rounded-md overflow-hidden">
                    <Table
                      columns={[
                        { title: "Dòng", dataIndex: "rowNum" },
                        { title: "Lỗi", dataIndex: "msg" },
                      ]}
                      dataSource={dataReturn.data.filter(
                        (it) => it.status == "error"
                      )}
                      pagination={false}
                    />
                  </div>
                </div>
              }
            ></Alert>
          )}
        </Spin>
        <Space
          style={{ width: "100%", justifyContent: "end", marginTop: "1em" }}
        ></Space>
      </Modal>
    );
  }
);

export default ImportSettingComponent;
