// =============================================================================
// LIGHT THEME COLORS
// =============================================================================

// Branding
$color-primary-light: #19345b;
$color-accent-light: #ed1c24;
$color-logo-light: #19345b;
$color-menu-text-selected-light: #ffffff;

// Neutral palette
$color-neutral-n0-light: #ffffff;
$color-neutral-n1-light: #f7f7f7;
$color-neutral-n2-light: #e4eced;
$color-neutral-n3-light: #b9c3c5;
$color-neutral-n4-light: #879699;
$color-neutral-n5-light: #636f73;
$color-neutral-n6-light: #4f5759;
$color-neutral-n7-light: #1f1d1e;
$color-neutral-n8-light: #050505;

// Status colors for projects
$color-project-planning-light: #3949ab;
$color-project-in-progress-light: #ff8300;
$color-project-hold-light: #9e9e9e;
$color-project-done-light: #43a047;
$color-project-draft-light: #050505;
$color-project-cancel-light: #f44336;
$color-project-pause-light: #9e9e9e;

// Task status colors
$color-task-normal-light: #3949ab;
$color-task-speedup-light: #43a047;
$color-task-warning-light: #fdd835;
$color-task-issue-light: #fb8c00;
$color-task-slow-light: #e53935;

// Button hover states
$color-primary-hover-light: #254578;
$color-primary-active-light: #122a48;
$color-secondary-hover-light: #d5e3f5;
$color-secondary-active-light: #c4d7f2;

// Overlay and disabled states
$color-primary-overlay-5-light: #19345b0d;
$color-primary-overlay-10-light: #19345b1a;
$color-primary-overlay-20-light: #4d74fc33;
$color-disabled-border-light: #19345b80;
$color-disabled-text-light: #ffffff99;

// NEW: Secondary disabled state
$color-secondary-disabled-bg-light: #e6eef880; // rgba(230, 238, 248, 0.5) -> hex

// Card header title
$color-card-header-title-light: $color-primary-light;

// =============================================================================
// DARK THEME COLORS
// =============================================================================

// Branding (adjusted for dark theme)
$color-primary-dark: #113872;
$color-accent-dark: #ed1c24;
$color-logo-dark: #ffffff;
$color-menu-text-selected-dark: #ffffff;

// Neutral palette (inverted)
$color-neutral-n0-dark: #050505;
$color-neutral-n1-dark: #1f1d1e;
$color-neutral-n2-dark: #2d3233;
$color-neutral-n3-dark: #586266;
$color-neutral-n4-dark: #788588;
$color-neutral-n5-dark: #b9c3c5;
$color-neutral-n6-dark: #dde4e5;
$color-neutral-n7-dark: #f7f7f7;
$color-neutral-n8-dark: #ffffff;

// Status colors (adjusted for dark theme)
$color-project-planning-dark: #283593;
$color-project-in-progress-dark: #ff8f00;
$color-project-hold-dark: #424242;
$color-project-done-dark: #2e7d32;
$color-project-draft-dark: #1e3a8a;
$color-project-cancel-dark: #f44336;
$color-project-pause-dark: #9e9e9e;

// Task status colors (adjusted for dark theme)
$color-task-normal-dark: #283593;
$color-task-speedup-dark: #2e7d32;
$color-task-warning-dark: #f9a825;
$color-task-issue-dark: #ef6c00;
$color-task-slow-dark: #c62828;

// Button hover states
$color-primary-hover-dark: #3a5d92;
$color-primary-active-dark: #1e3b66;
$color-secondary-hover-dark: #34485e;
$color-secondary-active-dark: #202e3c;

// Overlay states for dark theme
$color-primary-overlay-5-dark: #4d74fc0d;
$color-primary-overlay-10-dark: #4d74fc1a;
$color-primary-overlay-20-dark: #4d74fc33;

// Dark theme specific colors
$color-primary-hover-outline-dark: #6a8afc;
$color-primary-active-outline-dark: #3a5efc;
$color-primary-hover-dark-specific: #3a5d92;
$color-primary-active-dark-specific: #1e3b66;
$color-secondary-hover-dark-specific: #34485e;
$color-secondary-active-dark-specific: #202e3c;

// NEW: Disabled states for dark theme
$color-disabled-border-dark: #4d74fc80; // rgba(77, 116, 252, 0.5) -> hex
$color-disabled-text-dark: #ffffff99; // rgba(255, 255, 255, 0.6) -> hex
$color-secondary-disabled-bg-dark: #4f575980; // rgba(79, 87, 89, 0.5) -> hex

// Card header title
$color-card-header-title-dark: #113872;

// =============================================================================
// THEME SWITCHING VARIABLES (DEFAULT TO LIGHT)
// =============================================================================

// Current theme colors (can be overridden based on theme)
$color-primary: $color-primary-light !default;
$color-accent: $color-accent-light !default;
$color-logo: $color-logo-light !default;
$color-menu-text-selected: $color-menu-text-selected-light !default;

// Neutral colors
$color-neutral-n0: $color-neutral-n0-light !default;
$color-neutral-n1: $color-neutral-n1-light !default;
$color-neutral-n2: $color-neutral-n2-light !default;
$color-neutral-n3: $color-neutral-n3-light !default;
$color-neutral-n4: $color-neutral-n4-light !default;
$color-neutral-n5: $color-neutral-n5-light !default;
$color-neutral-n6: $color-neutral-n6-light !default;
$color-neutral-n7: $color-neutral-n7-light !default;
$color-neutral-n8: $color-neutral-n8-light !default;

// Status colors
$color-project-planning: $color-project-planning-light !default;
$color-project-in-progress: $color-project-in-progress-light !default;
$color-project-hold: $color-project-hold-light !default;
$color-project-done: $color-project-done-light !default;
$color-project-draft: $color-project-draft-light !default;
$color-project-cancel: $color-project-cancel-light !default;
$color-project-pause: $color-project-pause-light !default;

// Task colors
$color-task-normal: $color-task-normal-light !default;
$color-task-speedup: $color-task-speedup-light !default;
$color-task-warning: $color-task-warning-light !default;
$color-task-issue: $color-task-issue-light !default;
$color-task-slow: $color-task-slow-light !default;

// Card header title
$color-card-header-title: $color-card-header-title-light !default;

// =============================================================================
// MIXINS FOR THEME SWITCHING
// =============================================================================

@mixin light-theme {
  --color-primary: #{$color-primary-light};
  --color-accent: #{$color-accent-light};
  --color-logo: #{$color-logo-light};
  --color-text-selected: #{$color-menu-text-selected-light};
  --table-even-row: #f6f8ff;
  --table-even-row-2: #fcfcfc;

  --color-neutral-n0: #{$color-neutral-n0-light};
  --color-neutral-n1: #{$color-neutral-n1-light};
  --color-neutral-n2: #{$color-neutral-n2-light};
  --color-neutral-n3: #{$color-neutral-n3-light};
  --color-neutral-n4: #{$color-neutral-n4-light};
  --color-neutral-n5: #{$color-neutral-n5-light};
  --color-neutral-n6: #{$color-neutral-n6-light};
  --color-neutral-n7: #{$color-neutral-n7-light};
  --color-neutral-n8: #{$color-neutral-n8-light};

  --color-project-planning: #{$color-project-planning-light};
  --color-project-in-progress: #{$color-project-in-progress-light};
  --color-project-hold: #{$color-project-hold-light};
  --color-project-done: #{$color-project-done-light};
  --color-project-draft: #{$color-project-draft-light};
  --color-project-cancel: #{$color-project-cancel-light};
  --color-project-pause: #{$color-project-pause-light};

  --color-task-normal: #{$color-task-normal-light};
  --color-task-speedup: #{$color-task-speedup-light};
  --color-task-warning: #{$color-task-warning-light};
  --color-task-issue: #{$color-task-issue-light};
  --color-task-slow: #{$color-task-slow-light};

  --color-primary-hover: #{$color-primary-hover-light};
  --color-primary-active: #{$color-primary-active-light};
  --color-secondary-hover: #{$color-secondary-hover-light};
  --color-secondary-active: #{$color-secondary-active-light};

  --color-primary-overlay-5: #{$color-primary-overlay-5-light};
  --color-primary-overlay-10: #{$color-primary-overlay-10-light};
  --color-disabled-border: #{$color-disabled-border-light};
  --color-disabled-text: #{$color-disabled-text-light};

  // NEW: Additional variables for light theme
  --color-secondary-disabled-bg: #{$color-secondary-disabled-bg-light};
  --color-primary-hover-dark: #{$color-primary-hover-light};
  --color-primary-active-dark: #{$color-primary-active-light};
  --color-secondary-hover-dark: #{$color-secondary-hover-light};
  --color-secondary-active-dark: #{$color-secondary-active-light};
  --color-primary-overlay-10-dark: #{$color-primary-overlay-10-light};
  --color-primary-overlay-20-dark: #{$color-primary-overlay-20-light};
  --color-primary-hover-outline-dark: #{$color-primary-hover-light};
  --color-primary-active-outline-dark: #{$color-primary-active-light};

  // Card header title
  --color-card-header-title: #{$color-card-header-title-light};
}

@mixin dark-theme {
  --color-primary: #{$color-primary-dark};
  --color-accent: #{$color-accent-dark};
  --color-logo: #{$color-logo-dark};
  --color-text-selected: #{$color-menu-text-selected-dark};

  --color-neutral-n0: #{$color-neutral-n0-dark};
  --color-neutral-n1: #{$color-neutral-n1-dark};
  --color-neutral-n2: #{$color-neutral-n2-dark};
  --color-neutral-n3: #{$color-neutral-n3-dark};
  --color-neutral-n4: #{$color-neutral-n4-dark};
  --color-neutral-n5: #{$color-neutral-n5-dark};
  --color-neutral-n6: #{$color-neutral-n6-dark};
  --color-neutral-n7: #{$color-neutral-n7-dark};
  --color-neutral-n8: #{$color-neutral-n8-dark};

  --color-project-planning: #{$color-project-planning-dark};
  --color-project-in-progress: #{$color-project-in-progress-dark};
  --color-project-hold: #{$color-project-hold-dark};
  --color-project-done: #{$color-project-done-dark};
  --color-project-draft: #{$color-project-draft-dark};
  --color-project-cancel: #{$color-project-cancel-dark};
  --color-project-pause: #{$color-project-pause-dark};

  --color-task-normal: #{$color-task-normal-dark};
  --color-task-speedup: #{$color-task-speedup-dark};
  --color-task-warning: #{$color-task-warning-dark};
  --color-task-issue: #{$color-task-issue-dark};
  --color-task-slow: #{$color-task-slow-dark};

  --color-primary-hover: #{$color-primary-hover-dark};
  --color-primary-active: #{$color-primary-active-dark};
  --color-secondary-hover: #{$color-secondary-hover-dark};
  --color-secondary-active: #{$color-secondary-active-dark};

  --color-primary-overlay-5: #{$color-primary-overlay-5-dark};
  --color-primary-overlay-10: #{$color-primary-overlay-10-dark};
  --color-disabled-border: #{$color-disabled-border-dark};
  --color-disabled-text: #{$color-disabled-text-dark};

  // NEW: Dark theme specific variables
  --color-secondary-disabled-bg: #{$color-secondary-disabled-bg-dark};
  --color-primary-hover-dark: #{$color-primary-hover-dark-specific};
  --color-primary-active-dark: #{$color-primary-active-dark-specific};
  --color-secondary-hover-dark: #{$color-secondary-hover-dark-specific};
  --color-secondary-active-dark: #{$color-secondary-active-dark-specific};
  --color-primary-overlay-10-dark: #{$color-primary-overlay-10-dark};
  --color-primary-overlay-20-dark: #{$color-primary-overlay-20-dark};
  --color-primary-hover-outline-dark: #{$color-primary-hover-outline-dark};
  --color-primary-active-outline-dark: #{$color-primary-active-outline-dark};

  // Card header title
  --color-card-header-title: #{$color-card-header-title-dark};
}

// =============================================================================
// USAGE EXAMPLE
// =============================================================================

// Apply themes to root or specific selectors
#Body {
  @include light-theme;

  &[data-theme="dark"] {
    @include dark-theme;
  }
}

// Alternative class-based approach
.theme-light {
  @include light-theme;
}

.theme-dark {
  @include dark-theme;
}
