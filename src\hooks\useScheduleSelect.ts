import { scheduleApi } from "api/schedule.api";
import { useState } from "react";
import { Schedule } from "types/schedule";
import { QueryParam } from "types/query";

export interface ScheduleQuery extends QueryParam {}

interface UseScheduleProps {
  initQuery: ScheduleQuery;
}

export const useScheduleSelect = ({ initQuery }: UseScheduleProps) => {
  const [data, setData] = useState<Schedule[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<ScheduleQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await scheduleApi.findAllSelect(query);

      setData(data.schedules);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return {
    schedulesSelect: data,
    setScheduleSelect: setData,
    totalScheduleSelect: total,
    fetchScheduleSelect: fetchData,
    loadingScheduleSelect: loading,
    setQueryScheduleSelect: setQuery,
    queryScheduleSelect: query,
  };
};
