import {
  StopOutlined,
  LockOutlined,
  UnlockOutlined,
  ImportOutlined,
} from "@ant-design/icons";
import {
  Button,
  Card,
  Divider,
  Form,
  Input,
  Modal,
  Space,
  Table,
  Tag,
  Tooltip,
  message,
} from "antd";
import { accountApi } from "api/account.api";
import { Pagination } from "components/Pagination";
import { useAccount } from "hooks/useAccount";
import { observer } from "mobx-react-lite";
import { useEffect, useMemo, useState, useRef, useCallback } from "react";
import { QueryParam } from "types/query";
import { Account } from "types/account";
import { getTitle } from "utils";
import { MyExcelColumn, handleExport } from "utils/MyExcel";
import {
  checkRole,
  checkRoles,
  filterActionColumnIfNoPermission,
} from "utils/auth";
import { unixToDate, unixToFullDate } from "utils/dateFormat";
import { settings } from "settings";
import { Link, useNavigate } from "react-router-dom";
import { PermissionNames } from "types/PermissionNames";
import { permissionStore } from "store/permissionStore";
import PageTitle from "components/PageTitle/PageTitle";
import CustomButton from "components/Button/CustomButton";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import CustomInput from "components/Input/CustomInput";
import PencilIcon from "assets/svgs/PencilIcon";
import DeleteIcon from "assets/svgs/DeleteIcon";
import { useStaff } from "hooks/useStaff";
import { useTransition } from "hooks/useTransition";
import { removeSubstringFromKeys } from "utils/common";
import { TableProps } from "antd/lib";
import { roleApi } from "api/role.api";
import { Role } from "types/role";
import QueryLabel from "components/QueryLabel/QueryLabel";
import LockButton from "components/Button/LockButton";
import EditButton from "components/Button/EditButton";
import { ReactComponent as KeyIcon } from "assets/svgs/key.svg";

import { $url } from "utils/url";

const { Column } = Table;

const exportColumns: MyExcelColumn<Account>[] = [
  {
    header: "Ngày tạo",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "createdDateTime",
    columnKey: "createdDateTime",
    render: (record) => unixToFullDate(record.createdAt),
  },
  {
    header: "Tên tài khoản",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "username",
    columnKey: "username",
    render: (record) => record.username,
  },
  {
    header: "Nhân viên",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "staff",
    columnKey: "staff",
    render: (record) => record.staff?.fullName || "-",
  },
  {
    header: "Mã nhân viên",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "staffCode",
    columnKey: "staffCode",
    render: (record) => record.staff?.code || "-",
  },
  {
    header: "Email",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "email",
    columnKey: "email",
    render: (record) => record.staff?.email || "-",
  },
  {
    header: "Số điện thoại",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "phone",
    columnKey: "phone",
    render: (record) => record.staff?.phone || "-",
  },
  {
    header: "Vai trò",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "role",
    columnKey: "role",
    render: (record) => record.role?.name || "-",
  },
];

export const AccountPage = observer(
  ({ title = "", initQuery }: { title?: string; initQuery?: QueryParam }) => {
    const {
      haveAddPermission,
      haveBlockPermission,
      haveEditPermission,
      haveResetPermission,
      haveViewAllPermission,
    } = checkRoles(
      {
        add: PermissionNames.accountAdd,
        edit: PermissionNames.accountEdit,
        block: PermissionNames.accountBlock,
        reset: PermissionNames.accountResetPassword,
        viewAll: PermissionNames.accountViewAll,
      },
      permissionStore.permissions
    );
    const [loadingDelete, setLoadingDelete] = useState(false);
    const { isLoaded } = useTransition();
    const [formResetPassword] = Form.useForm<{ password: string }>();

    const {
      fetchAccount,
      Accounts,
      loadingAccount,
      totalAccount,
      setQueryAccount,
      queryAccount,
    } = useAccount({
      initQuery: {
        limit: 10,
        page: 1,
        isAdmin: haveViewAllPermission ? true : undefined,
      },
    });

    const navigate = useNavigate();

    useEffect(() => {
      document.title = getTitle(title);
    }, []);

    useEffect(() => {
      if (isLoaded) {
        fetchAccount();
      }
    }, [isLoaded]);

    const handleBlockAccount = async (id: number, isBlock: boolean) => {
      try {
        setLoadingDelete(true);
        const payload = {
          isBlock: !isBlock,
        };
        await accountApi.block(id, payload);
        message.success("Cập nhật tài khoản thành công");
        fetchAccount();
      } catch (error) {
        message.error("Có lỗi xảy ra");
      } finally {
        setLoadingDelete(false);
      }
    };

    const handleResetPassword = async (id: number) => {
      Modal.confirm({
        title: "Đặt lại mật khẩu",
        getContainer: () => {
          return document.getElementById("App") as HTMLElement;
        },
        width: 500,
        icon: null,
        content: (
          <>
            <div>
              Bạn có muốn đặt lại mật khẩu cho tài khoản này? Vui lòng nhập mật
              khẩu mới
            </div>
            <Form form={formResetPassword} layout="vertical">
              <Form.Item name="password" label="Mật khẩu">
                <Input.Password placeholder="Nhập mật khẩu" />
              </Form.Item>
            </Form>
          </>
        ),
        footer: (_, { OkBtn, CancelBtn }) => (
          <>
            <CustomButton
              variant="outline"
              className="cta-button"
              onClick={() => {
                Modal.destroyAll();
              }}
            >
              Không
            </CustomButton>
            <CustomButton
              onClick={async () => {
                await formResetPassword.validateFields();
                const { password } = formResetPassword.getFieldsValue(true);
                const dataSubmit = {
                  password,
                };
                await accountApi.resetPassword(id, dataSubmit);
                message.success("Đặt lại mật khẩu thành công");
                fetchAccount();
                Modal.destroyAll();
              }}
              className="cta-button"
            >
              Đặt lại
            </CustomButton>
          </>
        ),
      });
    };

    const handleTableChange: TableProps<any>["onChange"] = (
      pagination,
      filters,
      sorter
    ) => {
      if (!Array.isArray(sorter)) {
        const fieldMap: Record<string, string> = {
          username: "account.username",
          staff: "staff.fullName",
          role: "role.name",
          createdAt: "account.createdAt",
        };
        const columnKey = sorter.field || sorter.column?.key;

        if (!sorter.order) {
          queryAccount.queryObject = undefined;
          setQueryAccount({ ...queryAccount });
        } else {
          const order = sorter.order === "ascend" ? "ASC" : "DESC";
          const field = fieldMap[columnKey as string];

          const newQueryObject = JSON.stringify([
            {
              type: "sort",
              field,
              value: order,
            },
          ]);
          queryAccount.queryObject = newQueryObject;
          setQueryAccount({ ...queryAccount });
        }
        fetchAccount();
      } else {
        queryAccount.queryObject = undefined;
        setQueryAccount({ ...queryAccount });
        fetchAccount();
      }
    };

    const handleRowClick = (record: Account) => {
      navigate(
        `/master-data/${PermissionNames.accountEdit.replace(
          ":id",
          record!.id + ""
        )}`
      );
    };

    const columns: CustomizableColumn<Account>[] = [
      {
        title: "Tên tài khoản",
        dataIndex: "username",
        key: "username",
        width: 200,
        sorter: true,
        render: (_, record) => (
          <div className="flex items-center gap-2">
            {record.avatar && (
              <img
                width={40}
                height={40}
                style={{
                  objectFit: "cover",
                  borderRadius: "50%",
                }}
                src={$url(record.avatar)}
                alt=""
              />
            )}
            <div
              className="text-[#1677ff] cursor-pointer"
              onClick={() => handleRowClick(record)}
            >
              {record.username || "(Chưa có tên)"}
            </div>
          </div>
        ),
        defaultVisible: true,
      },
      {
        title: "Nhân viên",
        dataIndex: "staff",
        key: "staff",
        sorter: true,
        width: 200,
        render: (_, record) => (
          <>
            {record.staff ? (
              <div className="flex items-center gap-2">
                <div>
                  <div className="font-medium">{record.staff.fullName}</div>
                  <div className="text-gray-500 text-sm">
                    {record.staff.code}
                  </div>
                </div>
              </div>
            ) : (
              <span className="text-gray-400">(Chưa liên kết)</span>
            )}
          </>
        ),
        defaultVisible: true,
      },
      {
        title: "Email",
        dataIndex: "email",
        key: "email",
        width: 250,
        render: (_, record) => record.staff?.email || "-",
        defaultVisible: true,
      },
      {
        title: "SĐT",
        dataIndex: "phone",
        key: "phone",
        width: 150,
        render: (_, record) => record.staff?.phone || "-",
        defaultVisible: true,
      },
      {
        title: "Vai trò",
        dataIndex: "role",
        key: "role",
        width: 150,
        sorter: true,
        render: (_, record) => record.role?.name || "-",
        defaultVisible: true,
      },
      {
        title: "Ngày tạo",
        dataIndex: "createdAt",
        key: "createdAt",
        width: 150,
        sorter: true,
        render: (_, record) => unixToDate(record.createdAt),
        defaultVisible: true,
      },
      {
        key: "actions",
        title: "Xử lý",
        width: 150,
        align: "center",
        fixed: "right",
        alwaysVisible: true,
        render: (_, record) => (
          <Space size="small">
            {haveResetPermission && (
              <Tooltip title="Reset mật khẩu">
                <Button
                  type="text"
                  icon={<KeyIcon width={20} height={20} />}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleResetPassword(record.id);
                  }}
                />
              </Tooltip>
            )}
            {haveEditPermission && (
              <EditButton
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(
                    `/master-data/${PermissionNames.accountEdit.replace(
                      ":id",
                      record.id + ""
                    )}?update=1`
                  );
                }}
              />
            )}
            {haveBlockPermission && (
              <LockButton
                isActive={!record.isBlock}
                onAccept={() => handleBlockAccount(record.id, record.isBlock)}
                modalTitle={`${record.isBlock ? "Khóa" : "Mở khóa"} tài khoản ${
                  record.username
                }`}
                modalContent={
                  <>
                    <div>
                      {record.isBlock
                        ? `Khi mở khóa tài khoản thì tài khoản ${record.username} sẽ được đăng nhập trở lại.`
                        : `Khi khóa tài khoản thì tài khoản ${record.username} sẽ không được đăng nhập nữa.`}
                    </div>
                    <div>
                      Bạn có chắc chắn muốn{" "}
                      {record.isBlock ? "khóa" : "mở khóa"} tài khoản này?
                    </div>
                  </>
                }
              />
            )}
          </Space>
        ),
      },
    ];

    const isEmptyQuery = !queryAccount.search;

    return (
      <div className="account-page-container app-container">
        <PageTitle
          title={title}
          breadcrumbs={["Dữ liệu nguồn", title]}
          extra={
            <Space>
              <CustomButton
                size="small"
                showPlusIcon
                onClick={() => {
                  navigate(`/master-data/${PermissionNames.accountAdd}`);
                }}
              >
                Tạo tài khoản
              </CustomButton>
            </Space>
          }
        />

        <Card>
          <div className="flex gap-[16px] items-end pb-[12px] justify-between">
            <div className="flex gap-[16px] items-end flex-wrap">
              <div className="w-[300px]">
                <CustomInput
                  tooltipContent={"Tìm theo tên tài khoản, tên nhân viên"}
                  label="Tìm kiếm"
                  placeholder="Tìm kiếm"
                  value={queryAccount.search}
                  onChange={(value) => {
                    queryAccount.search = value;
                    setQueryAccount({ ...queryAccount });

                    if (!value) {
                      fetchAccount();
                    }
                  }}
                  onPressEnter={() => {
                    queryAccount.page = 1;
                    fetchAccount();
                  }}
                  allowClear
                />
              </div>
              <CustomButton
                onClick={() => {
                  queryAccount.page = 1;
                  fetchAccount();
                }}
              >
                Áp dụng
              </CustomButton>

              {!isEmptyQuery && (
                <CustomButton
                  variant="outline"
                  onClick={() => {
                    delete queryAccount.search;
                    setQueryAccount({ ...queryAccount });
                    fetchAccount();
                  }}
                >
                  Bỏ lọc
                </CustomButton>
              )}
            </div>

            <CustomButton
              onClick={() => {
                Modal.confirm({
                  title: `Bạn có muốn xuất file excel?`,
                  getContainer: () => {
                    return document.getElementById("App") as HTMLElement;
                  },
                  icon: null,
                  footer: (_, { OkBtn, CancelBtn }) => (
                    <>
                      <CustomButton
                        variant="outline"
                        className="cta-button"
                        onClick={() => {
                          handleExport({
                            onProgress(percent) {
                              console.log("What is percent", percent);
                            },
                            exportColumns,
                            fileType: "xlsx",
                            dataField: "accounts",
                            query: queryAccount,
                            api: accountApi.findAll,
                            fileName: "Danh sách tài khoản",
                            sheetName: "Danh sách tài khoản",
                          });
                          Modal.destroyAll();
                        }}
                      >
                        Có
                      </CustomButton>
                      <CustomButton
                        onClick={() => {
                          Modal.destroyAll();
                        }}
                        className="cta-button"
                      >
                        Không
                      </CustomButton>
                    </>
                  ),
                });
              }}
            >
              Xuất excel
            </CustomButton>
          </div>
          <CustomizableTable
            columns={filterActionColumnIfNoPermission(columns, [
              haveEditPermission,
              haveBlockPermission,
            ])}
            dataSource={Accounts}
            rowKey="id"
            loading={loadingAccount}
            pagination={false}
            scroll={{ x: "max-content" }}
            bordered
            displayOptions
            className="account-table-no-empty-hover"
            tableId="account-page"
            autoAdjustColumnWidth={true}
            //@ts-ignore
            onChange={handleTableChange}
            onRowClick={handleRowClick}
          />

          <Pagination
            currentPage={queryAccount.page}
            defaultPageSize={queryAccount.limit}
            total={totalAccount}
            onChange={({ limit, page }) => {
              queryAccount.page = page;
              queryAccount.limit = limit;
              setQueryAccount({ ...queryAccount });
              fetchAccount();
            }}
          />
        </Card>
      </div>
    );
  }
);
