import { Col, Image, Popconfirm, Spin, Tooltip } from "antd";
import React, { useState } from "react";
import { FiDownload } from "react-icons/fi";
import { IoTrashOutline } from "react-icons/io5";
import { downloadFile } from "utils/downloadFile";
import { FaFileAlt } from "react-icons/fa";
import { FileAttachType } from "types/fileAttach";
import FileIcon from "./RenderIconFile";
import { CloseOutlined, DeleteOutlined } from "@ant-design/icons";
import { formatVND } from "utils";

export interface FileCustomProps {
  id?: string;
  fileName: string;
  fileSize: number;
  fileUrl: string;
  fileType?: string;
  mimeType?: string;
}

export interface FileAttachPayload {
  name: string;
  size: number;
  type: FileAttachType;
  uid: string;
  url: string;
  path: string;
  destination: string;
}

interface FileUploadItemProps {
  file: FileCustomProps;
  loadingDelete?: boolean;
  onDelete?: (id: string) => void;
  variant?: "compact" | "detailed";
}

const FileUploadItem: React.FC<FileUploadItemProps> = ({
  file,
  onDelete,
  loadingDelete,
  variant = "compact",
}) => {
  const [loadingDownload, setLoadingDownload] = useState(false);
  if (variant == "detailed") {
    return (
      <div className="p-2 rounded-xl bg-[#FAFAFA] mt-3 w-full">
        <div className="flex items-center gap-5 flex-col">
          <div>
            <FileIcon mimeType={file.fileType} url={file.fileUrl} />
          </div>
          <div>{file.fileName}</div>
          <div className="flex flex-col gap-1 flex-1">
            <Tooltip title={file.fileName}>
              <Spin spinning={loadingDownload}>
                <span
                  className="text-sm font-bold break-all line-clamp-1 cursor-pointer"
                  onClick={() =>
                    downloadFile(
                      file.fileUrl,
                      file.fileName,
                      setLoadingDownload
                    )
                  }
                >
                  {file.fileName}
                </span>
              </Spin>
            </Tooltip>
            {/* <span className="text-sm text-gray">
              {file.fileSize >= 1024 ? (
                <span>{(file.fileSize / 1024 / 1024).toFixed(2)}Mb</span>
              ) : (
                <span>{(file.fileSize / 1024).toFixed(2)}Kb</span>
              )}
            </span> */}
            <div className="flex items-center justify-between">
              {/* <Spin spinning={loadingDownload}>
                <span
                  onClick={() =>
                    downloadFile(
                      file.fileUrl,
                      file.fileName,
                      setLoadingDownload
                    )
                  }
                  className="cursor-pointer text-[18px] flex items-center gap-1"
                >
                  <FiDownload className="text-primary text-sm" />
                  <span className="text-primary text-sm font-medium">
                    {t("downloadFile")}
                  </span>
                </span>
              </Spin> */}
              {onDelete && (
                <Popconfirm
                  title={"Bạn có chắc chắn muốn xóa?"}
                  onConfirm={() => {
                    onDelete(file.id || "");
                  }}
                  okButtonProps={{ loading: loadingDelete }}
                >
                  <CloseOutlined className="cursor-pointer text-red-500 text-base" />
                </Popconfirm>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className="relative w-full bg-white"
      style={{
        border: "1px solid var(--color-neutral-n3)",
      }}
    >
      <div className="flex items-center justify-start gap-2 w-full">
        <div className="bg-[#19345B]">
          <FileIcon
            mimeType={file.fileType}
            url={file.fileUrl}
            className="overflow-hidden !w-[56px] !h-[56px]"
          />
        </div>
        <div className="">
          <div
            className="w-full font-bold text-[#050505] cursor-pointer"
            onClick={() =>
              downloadFile(file.fileUrl, file.fileName, setLoadingDownload)
            }
          >
            {file.fileName}
          </div>
          <div className="w-full text-gray-500">
            {formatVND(file.fileSize)} Kb
          </div>
        </div>
        {onDelete && (
          <Popconfirm
            title={"Bạn có chắc chắn muốn xóa?"}
            onConfirm={() => {
              onDelete(file.id || "");
            }}
            okButtonProps={{ loading: loadingDelete }}
          >
            <div className="absolute -translate-y-1/2 top-1/2 right-2 flex justify-center items-center  p-[6px]">
              <CloseOutlined className="cursor-pointer  text-base" />
            </div>
          </Popconfirm>
        )}
      </div>
    </div>
  );

  // Detailed view
};

export default FileUploadItem;
