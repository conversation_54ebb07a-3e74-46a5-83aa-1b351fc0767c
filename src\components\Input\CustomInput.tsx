import React, { ReactNode, useState } from "react";
import { Input, Select, Form, Tooltip } from "antd";
import {
  DownOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
} from "@ant-design/icons";
import { useTheme } from "context/ThemeContext";
import { ReactComponent as SearchIcon } from "assets/svgs/search.svg";
import "./CustomInput.scss";
import clsx from "clsx";
import { formatVND } from "utils";
import { InputNumber } from "./InputNumber";

export type CustomInputProps = {
  label?: string;
  required?: boolean;
  value?: string | number;
  onChange?: (value: string) => void;
  placeholder?: string;
  error?: string;
  disabled?: boolean;
  type?: "text" | "select" | "textarea" | "email" | "phone";
  options?: { label: string; value: string | number | boolean }[];
  name?: string;
  className?: string;
  status?: "normal" | "error" | "disabled";
  rows?: number;
  classNameContainer?: string;
  inputSearch?: boolean;
  onPressEnter?: (value: string, isShiftKey?: boolean) => void;
  typeText?: string;
  suffix?: React.ReactNode;
  addonAfter?: ReactNode;
  tooltipContent?: string;
  defaultOpen?: boolean;
  inputStyle?: React.CSSProperties;
  allowClear?: boolean;
  readonly?: boolean;
  shouldPressEnter?: boolean;
};

const CustomInput: React.FC<CustomInputProps> = ({
  label = "",
  required = false,
  value,
  onChange,
  placeholder = "Value",
  error,
  disabled = false,
  type = "text",
  options = [],
  name,
  className = "",
  status: propStatus,
  rows = 4,
  classNameContainer,
  inputSearch = false,
  onPressEnter,
  typeText = "text",
  suffix,
  addonAfter,
  tooltipContent,
  defaultOpen,
  inputStyle,
  allowClear,
  readonly,
  shouldPressEnter,
  ...props
}) => {
  if (name == "code") {
    console.log("prop input ne", props);
  }

  const { darkMode } = useTheme();
  const [showPassword, setShowPassword] = useState(false);

  const getInputStatus = () => {
    if (propStatus) return propStatus;
    if (error) return "error";
    if (disabled) return "disabled";
    return "normal";
  };

  const status = getInputStatus();
  const inputClassName = `custom-input custom-input-${status} ${
    darkMode ? "dark" : ""
  } ${className}`;

  // Format phone number as user types
  const formatPhoneNumber = (value: string) => {
    // Remove all non-numeric characters
    const numbers = value.replace(/\D/g, "");

    // Limit to 10 digits for Vietnamese phone numbers
    const limitedNumbers = numbers.substring(0, 10);

    // Format as XXX XXX XXXX
    if (limitedNumbers.length >= 7) {
      return `${limitedNumbers.substring(0, 3)} ${limitedNumbers.substring(
        3,
        6
      )} ${limitedNumbers.substring(6)}`;
    } else if (limitedNumbers.length >= 4) {
      return `${limitedNumbers.substring(0, 3)} ${limitedNumbers.substring(3)}`;
    } else {
      return limitedNumbers;
    }
  };

  // Validate email format
  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // Validate phone number format
  const validatePhoneNumber = (phone: string) => {
    const phoneRegex = /^[0-9\s]{10,13}$/; // Vietnamese phone numbers
    return phoneRegex.test(phone.replace(/\s/g, ""));
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | string
  ) => {
    if (onChange) {
      let newValue: string;

      if (
        typeof e === "string" ||
        typeof e === "number" ||
        typeof e === "boolean"
      ) {
        newValue = String(e);
      } else {
        newValue = e.target.value;
      }

      // Apply formatting based on type
      if (type === "phone") {
        newValue = formatPhoneNumber(newValue);
      }

      onChange(newValue);
    }
  };

  // const handlePressEnter = (e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>) => {
  //   if (onPressEnter && e.key === 'Enter') {
  //     if (e.shiftKey) {
  //       e.preventDefault(); // Only prevent default for Shift+Enter
  //       onPressEnter(e.currentTarget.value, true);
  //     }
  //   }
  // };

  const handlePressEnter = (
    e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    if (onPressEnter && e.key === "Enter") {
      if (type === "textarea") {
        // For textarea, only trigger on Shift+Enter
        if (e.shiftKey) {
          e.preventDefault();
          onPressEnter(e.currentTarget.value, true);
        }
      } else {
        // For other input types, trigger on regular Enter
        e.preventDefault();
        onPressEnter(e.currentTarget.value, false);
      }
    }
  };

  const getInputType = () => {
    if (typeText === "password") {
      return showPassword ? "text" : "password";
    }
    if (type === "email") {
      return "email";
    }
    if (type === "phone") {
      return "tel";
    }
    return typeText;
  };

  const getInputSuffix = () => {
    if (typeText === "password") {
      return (
        <span
          onClick={() => setShowPassword(!showPassword)}
          style={{ cursor: "pointer", color: "var(--color-neutral-n5)" }}
        >
          {showPassword ? <EyeInvisibleOutlined /> : <EyeOutlined />}
        </span>
      );
    }

    if (suffix) return suffix;
    if (inputSearch) return <SearchIcon />;
    return undefined;
  };

  // Get validation error message
  const getValidationError = () => {
    if (!value || disabled) return error;

    if (type === "email" && value && !validateEmail(String(value))) {
      return "Vui lòng nhập địa chỉ email hợp lệ";
    }

    if (type === "phone" && value && !validatePhoneNumber(String(value))) {
      return "Vui lòng nhập số điện thoại hợp lệ (10 số)";
    }

    return error;
  };

  const validationError = getValidationError();

  const renderInput = () => {
    if (type === "select") {
      return (
        <Select
          getPopupContainer={() =>
            document.getElementById("App") as HTMLElement
          }
          size="large"
          //@ts-ignore
          value={value}
          defaultOpen={defaultOpen}
          onChange={handleChange}
          placeholder={placeholder}
          disabled={disabled}
          className={inputClassName}
          suffixIcon={<DownOutlined />}
          options={options}
          status={validationError ? "error" : undefined}
          style={inputStyle}
        />
      );
    }

    if (type === "textarea") {
      return (
        <Input.TextArea
          rows={rows}
          value={value}
          onChange={handleChange}
          placeholder={placeholder}
          disabled={disabled}
          className={inputClassName}
          style={inputStyle}
          status={validationError ? "error" : undefined}
          onKeyDown={handlePressEnter}
        />
      );
    }

    if (typeText == "number") {
      return (
        <InputNumber
          size="large"
          value={value}
          onChange={handleChange}
          placeholder={placeholder}
          disabled={disabled}
          className={clsx(inputClassName, "custom-input-number")}
          suffix={getInputSuffix()}
          status={validationError ? "error" : undefined}
          onPressEnter={handlePressEnter}
          style={inputStyle}
          addonAfter={addonAfter}
        />
      );
    }

    return (
      <Input
        size="large"
        value={value}
        onChange={handleChange}
        placeholder={placeholder}
        disabled={disabled}
        className={inputClassName}
        suffix={getInputSuffix()}
        status={validationError ? "error" : undefined}
        onPressEnter={handlePressEnter}
        type={getInputType()}
        addonAfter={addonAfter}
        style={inputStyle}
        allowClear={allowClear}
        maxLength={type === "phone" ? 13 : undefined}
        readOnly={readonly}
      />
    );
  };

  return (
    <div className={clsx("custom-input-container", classNameContainer)}>
      {label && (
        <div className="custom-input-label">
          {label} {required && <span className="custom-input-required">*</span>}
        </div>
      )}
      {tooltipContent ? (
        <Tooltip mouseEnterDelay={0.3} title={tooltipContent}>
          {renderInput()}
        </Tooltip>
      ) : (
        renderInput()
      )}

      {validationError && (
        <div className="custom-input-error-message">{validationError}</div>
      )}
    </div>
  );
};

export default CustomInput;
