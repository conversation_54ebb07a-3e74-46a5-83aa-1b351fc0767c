import React from "react";
import { <PERSON>, <PERSON>, But<PERSON>, Tooltip } from "antd";
import { Draw } from "types/draw";
import { unixToDate } from "utils/dateFormat";
import "./GridView.scss";
import { PDFPreview } from "components/Upload/PDFPreview";
import EditButton from "components/Button/EditButton";
import DeleteButton from "components/Button/DeleteButton";

interface GridViewProps {
  data: Draw[];
  onView: (record: Draw) => void;
  onEdit: (record: Draw) => void;
  onDelete: (id: number) => Promise<void>;
}

const GridView: React.FC<GridViewProps> = ({ data, onView, onEdit, onDelete }) => {
  return (
    <div className="grid-view">
      {data.map((item) => (
        <Card
          key={item.id}
          hoverable
          className="blueprint-card"
          onClick={() => onView(item)}
          cover={
            item.fileAttach?.url ? (
              item.fileAttach.mimetype?.startsWith("image/") ? (
                <img
                  src={item.fileAttach.url}
                  alt={item.fileAttach.name}
                  className="card-image"
                  style={{ width: "100%", height: 180, objectFit: "cover" }}
                />
              ) : item.fileAttach.mimetype === "application/pdf" ? (
                <PDFPreview
                  url={item.fileAttach.url}
                  width={320}
                  height={180}
                />
              ) : (
                <div className="card-cover pdf-cover">
                  <div className="placeholder-content">
                    <div className="placeholder-icon">📄</div>
                    <div className="placeholder-text">
                      {item.fileAttach.name}
                    </div>
                  </div>
                </div>
              )
            ) : (
              <div className="card-cover">
                <div className="image-placeholder">
                  <div className="placeholder-content">
                    <div className="placeholder-text">Ảnh bản vẽ</div>
                  </div>
                </div>
              </div>
            )
          }
          // actions={[
          //   <Tooltip title="Xem chi tiết">
          //     <Button 
          //       type="text" 
          //       size="small"
          //       onClick={(e) => {
          //         e.stopPropagation();
          //         onView(item);
          //       }}
          //     >
          //       Xem
          //     </Button>
          //   </Tooltip>,
          //   <Tooltip title="Chỉnh sửa">
          //     <EditButton
          //       onClick={(e) => {
          //         e.stopPropagation();
          //         onEdit(item);
          //       }}
          //     />
          //   </Tooltip>,
          //   <Tooltip title="Xóa">
          //     <DeleteButton
          //       onClick={(e) => {
          //         e.stopPropagation();
          //         onDelete(item.id);
          //       }}
          //     />
          //   </Tooltip>
          // ]}
        >
          <Card.Meta
            title={
              <div className="card-title">
                <div className="title-text">{item.title}</div>
              </div>
            }
            description={
              <div className="card-description">
                <div className="date-text">
                  {item.createdAt && item.createdAt > 0
                    ? unixToDate(item.createdAt)
                    : ""}
                </div>
              </div>
            }
          />
        </Card>
      ))}
    </div>
  );
};

export default GridView;
