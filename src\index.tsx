import React from "react";
import ReactDOM from "react-dom";
import "./index.css";
import App from "./App";
import reportWebVitals from "./reportWebVitals";
import { BrowserRouter } from "react-router-dom";
import OneSignalProvider from "context/OneSignalContext";
import { ThemeProvider } from "context/ThemeContext";

ReactDOM.render(
  <BrowserRouter>
    {/* <React.StrictMode> */}
    <ThemeProvider>
      <OneSignalProvider>
        <App />
      </OneSignalProvider>
    </ThemeProvider>
    {/* </React.StrictMode> */}
  </BrowserRouter>,
  document.getElementById("root")
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
