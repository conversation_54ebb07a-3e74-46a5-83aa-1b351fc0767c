.filter-bar {
  margin-bottom: 24px;
  
  .filter-section {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    align-items: flex-end;
    
    .filter-group {
      display: flex;
      flex-direction: column;
      min-width: 200px;
      flex: 1;
      max-width: 300px;
      
      label {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 8px;
        color: var(--color-n6, #4F5759);
      }
      
      .ant-input-affix-wrapper {
        border-radius: 4px;
        height: 40px;
        
        .ant-input {
          height: 38px;
        }
      }
      
      .ant-select {
        .ant-select-selector {
          height: 40px;
          display: flex;
          align-items: center;
          border-radius: 4px;
        }
        
        .select-arrow {
          font-size: 10px;
          color: var(--color-n5, #636F73);
        }
      }
    }
    
    .filter-actions {
      display: flex;
      gap: 8px;
      
      .ant-btn {
        height: 40px;
        border-radius: 4px;
        min-width: 100px;
      }
    }
  }
}

// Dark mode styles
.dark {
  .filter-bar {
    .filter-section {
      .filter-group {
        label {
          color: var(--color-n3, #B9C3C5);
        }
        
        .ant-input-affix-wrapper {
          background-color: var(--color-n2, #2D3233);
          border-color: var(--color-n3, #586266);
          
          .ant-input {
            background-color: var(--color-n2, #2D3233);
            color: var(--color-n7, #F7F7F7);
          }
          
          .ant-input-suffix {
            color: var(--color-n5, #B9C3C5);
          }
        }
        
        .ant-select {
          .ant-select-selector {
            background-color: var(--color-n2, #2D3233);
            border-color: var(--color-n3, #586266);
            color: var(--color-n7, #F7F7F7);
          }
          
          .ant-select-arrow {
            color: var(--color-n5, #B9C3C5);
          }
        }
      }
    }
  }
}