import {
  ColumnDirective,
  ColumnMenu,
  ColumnModel,
  ColumnsDirective,
  ContextMenu,
  CriticalPath,
  DayMarkers,
  Edit,
  EditDialogFieldDirective,
  EditDialogFieldsDirective,
  ExcelExport,
  Filter,
  GanttComponent,
  IGanttData,
  Inject,
  PdfExport,
  Reorder,
  Resize,
  RowDD,
  Selection,
  Sort,
  Toolbar,
  UndoRedo,
} from "@syncfusion/ej2-react-gantt";
import { L10n } from "@syncfusion/ej2-base";
import { cloneDeep, uniqueId } from "lodash";
import { useEffect, useMemo, useRef, useState } from "react";
import { projectNewData, resourceCollection } from "../dumbData";
import {
  editSettings,
  labelSettings,
  resourceFields,
  splitterSettings,
  taskFields,
  timelineSettings,
  toolBarSettings,
} from "./ProjectProgressGanttSetting";
import "../styles/GanttComponentStyle.scss";
import { message, Skeleton, Space, Spin, Tag } from "antd";
import { formatVND } from "utils";
import { useSchedule } from "hooks/useSchedule";
import { useMemberShip } from "hooks/useMemberShip";
import CustomButton from "components/Button/CustomButton";
import { Schedule } from "types/schedule";
import dayjs from "dayjs";
import { scheduleApi } from "api/schedule.api";

// Load Vietnamese localization
// L10n.load({
//   vi: {
//     gantt: {
//       emptyRecord: "Không có bản ghi để hiển thị",
//       id: "ID",
//       name: "Tên nhiệm vụ",
//       startDate: "Ngày bắt đầu",
//       endDate: "Ngày kết thúc",
//       duration: "Thời lượng",
//       progress: "Tiến độ",
//       dependency: "Phụ thuộc",
//       notes: "Ghi chú",
//       baselineStartDate: "Ngày bắt đầu cơ sở",
//       baselineEndDate: "Ngày kết thúc cơ sở",
//       taskMode: "Chế độ nhiệm vụ",
//       changeBaseline: "Thay đổi cơ sở",
//       subTasks: "Nhiệm vụ phụ",
//       expandAll: "Mở rộng tất cả",
//       collapseAll: "Thu gọn tất cả",
//       excelExport: "Xuất Excel",
//       csvExport: "Xuất CSV",
//     },
//     // Optionally include other modules like toolbar, grid, etc.
//     grid: {
//       EmptyRecord: "Không có dữ liệu để hiển thị",
//     },
//     toolbar: {
//       ExpandAll: "Mở rộng tất cả",
//       CollapseAll: "Thu gọn tất cả",
//       ExcelExport: "Xuất Excel",
//       CsvExport: "Xuất CSV",
//     },
//   },
// });

const GANTT_ID = "projectProgressGantt";

const ProjectProgressGantt = () => {
  const {
    fetchSchedule,
    loadingSchedule,
    querySchedule,
    schedules,
    setQuerySchedule,
    totalSchedule,
    setSchedule,
  } = useSchedule({ initQuery: { page: 1, limit: 0 } });

  const {
    fetchData: fetchMemberShip,
    memberShips,
    loading: loadingMemberShip,
  } = useMemberShip({ initQuery: { page: 1, limit: 0 } });

  const readOnly = false;

  const [loadingSave, setLoadingSave] = useState(false);
  const [loaded, setLoaded] = useState(false);
  const [ganttChanged, setGanttChanged] = useState(false);
  const [allowFilter, setAllowFilter] = useState(false);

  const ganttRef = useRef<GanttComponent>(null);

  useEffect(() => {
    initFetch();
  }, []);

  const initFetch = async () => {
    try {
      const schedules = await fetchSchedule();
      await fetchMemberShip();
      if (schedules.length > 0) {
        setAllowFilter(true);
      }
    } catch (error) {
      console.log({ error });
    } finally {
      setLoaded(true);
    }
  };

  // const [dataSource, setDataSource] =
  //   useState<GanttChartItem[]>(projectNewData);
  const projectStartDate: Date = new Date("03/24/2024");
  const projectEndDate: Date = new Date("07/06/2024");

  const handleToolbarClick = (args: any) => {
    if (args.item.id === `${GANTT_ID}_excelexport`) {
      ganttRef.current?.excelExport();
    } else if (args.item.id === `${GANTT_ID}_csvexport`) {
      ganttRef.current?.csvExport();
    } else if (args.item.id === `${GANTT_ID}_pdfexport`) {
      ganttRef.current?.pdfExport();
    }
  };

  // const handleActionBegin = (args: any) => {
  //   console.log("handleActionBegin", { args });
  //   // Check if the action is related to task drag-and-drop
  //   if (args.requestType === "taskbarEditing" && args.action === "move") {
  //     console.log("Drag and Drop Started:", args);
  //     // Optionally, perform custom logic here before the drag-and-drop operation completes.
  //   }
  // };

  const updateRecursiveData = (
    tasks: Schedule[],
    updatedTasks: any[]
  ): Schedule[] => {
    if (!tasks || tasks.length === 0) return [...updatedTasks];
    return tasks.map((task) => {
      const updatedTask = updatedTasks.find(
        (updated) => updated.id === task.id
      );

      if (updatedTask) {
        // Merge updated task properties
        task = { ...task, ...updatedTask };
      }

      if (task.subtasks && task.subtasks.length > 0) {
        // Recursively update subTasks
        task.subtasks = updateRecursiveData(task.subtasks, updatedTasks);
      }

      return task;
    });
  };

  const handleActionComplete = (args: any) => {
    console.log({ args, ref: ganttRef.current });
    // const updatedRecords = ganttRef.current?.updatedRecords || [];
    // if (updatedRecords.length > 0) {
    //   setAllowFilter(true);
    // } else {
    //   setAllowFilter(false);
    // }
    if (
      args.requestType === "save" ||
      args.requestType === "delete" ||
      args.requestType === "add"
    ) {
      setGanttChanged(true);
    }
  };

  const getPayload = () => {
    const updatedRecords = ganttRef.current?.updatedRecords || [];
    console.log({ updatedRecords });

    const getRecursivePayload = (records: any[]): any[] => {
      return records.map((record: any, index: number) => {
        const {
          TaskID,
          TaskName,
          StartDate,
          EndDate,
          Duration,
          work,
          Progress,
          Predecessor,
          Money,
          info,
          childRecords,
          taskData,
        } = record;

        return {
          // id:
          //   taskData.id && taskData.id == Number(taskData.TaskID)
          //     ? taskData.id
          //     : undefined,
          TaskID,
          TaskName,
          StartDate: dayjs(StartDate).format("YYYY-MM-DD"),
          EndDate: dayjs(EndDate).format("YYYY-MM-DD"),
          Duration,
          work,
          Progress,
          Predecessor,
          Money,
          info,
          resourceIds:
            taskData?.resources.map((r: any) => Number(r.resourceId)) || [],
          subTasks:
            childRecords.length > 0 ? getRecursivePayload(childRecords) : [],
          position: index,
        };
      });
    };

    return {
      schedules: getRecursivePayload(
        updatedRecords.filter((r) => !r.parentUniqueID)
      ),
    };
  };

  const handleSave = async () => {
    try {
      setLoadingSave(true);
      const payload = getPayload();
      console.log({ payload });
      await scheduleApi.create(payload);
      message.success("Lưu thành công");
    } catch (e) {
      console.log({ e });
    } finally {
      setLoadingSave(false);
    }
  };

  const transformDataSource = (schedules: Schedule[]): any => {
    return schedules.map((schedule) => {
      // debugger;
      const {
        id,
        TaskID,
        TaskName,
        StartDate,
        EndDate,
        Duration,
        work,
        Progress,
        Predecessor,
        Money,
        info,
        resources,
        subtasks,
      } = schedule;

      return {
        TaskID,
        // id,
        TaskName,
        StartDate: dayjs(StartDate).toDate(),
        EndDate: dayjs(EndDate).toDate(),
        Duration,
        work,
        Progress,
        Predecessor,
        Money,
        info,
        resources: (resources || []).map((r) => ({
          resourceId: r.id,
          resourceName: r.name,
        })),
        subtasks: transformDataSource(cloneDeep(subtasks)),
      };
    });
  };

  const scheduleRender = useMemo(() => {
    let transformedSchedules: any[] = [];
    if (loaded) {
      transformedSchedules = transformDataSource(schedules);
    }
    return transformedSchedules;
  }, [schedules, loaded]);

  const ganttColumns: ColumnModel[] = [
    {
      field: "TaskID",
      // headerText: "Id",
      width: "80",
      textAlign: "Center",
    },
    {
      field: "TaskName",
      // headerText: "Name",
      width: "250",
    },
    {
      field: "resources",
      // headerText: "Assignees",
      width: "200",
      template: (props: any) => {
        const assigneeNames =
          typeof props.resources == "string"
            ? props.resources?.split(",") || []
            : [];
        return (
          <Space className="w-full overflow-x-auto small-scrollbar-horizontal gap-1">
            {assigneeNames.map((name: string, index: number) => (
              <Tag className="!w-fit" key={index}>
                {name}
              </Tag>
            ))}
          </Space>
        );
      },
    },
    {
      field: "StartDate",
      format: "dd/MM/yyyy",
    },
    {
      field: "EndDate",
      format: "dd/MM/yyyy",
    },
    {
      field: "Money",
      headerText: "Tiền",
      textAlign: "Right",
      template: (props: any) => {
        return formatVND(props.Money);
      },
    },
    {
      field: "work",
    },
    {
      field: "Duration",
    },
    {
      field: "Progress",
    },
    {
      field: "Predecessor",
    },
  ];

  return (
    <div className="control-pane">
      <CustomButton
        disabled={!ganttChanged}
        loading={loadingSave}
        className="mb-2"
        onClick={handleSave}
      >
        Lưu
      </CustomButton>
      {!loadingMemberShip && !loadingSchedule && loaded ? (
        <GanttComponent
          id={GANTT_ID}
          ref={ganttRef}
          locale="vi"
          dataSource={scheduleRender}
          resources={memberShips.map((m) => ({
            resourceId: m.id,
            resourceName: m.name,
          }))}
          height={window.innerHeight - 200}
          treeColumnIndex={1}
          workUnit="Hour"
          toolbar={toolBarSettings}
          taskFields={taskFields}
          resourceFields={resourceFields}
          timelineSettings={timelineSettings}
          labelSettings={labelSettings}
          editSettings={editSettings}
          splitterSettings={splitterSettings}
          selectionSettings={{ type: "Multiple" }}
          gridLines="Both"
          highlightWeekends={true}
          toolbarClick={handleToolbarClick.bind(this)}
          actionComplete={handleActionComplete}
          readOnly={readOnly}
          // actionBegin={handleActionBegin}
          showColumnMenu
          enableUndoRedo
          // enableCriticalPath
          enableContextMenu
          allowFiltering
          allowSorting
          allowResizing
          allowReordering
          allowExcelExport
          allowPdfExport
          allowParentDependency
          allowSelection
          allowRowDragAndDrop
          allowTaskbarDragAndDrop
        >
          <ColumnsDirective>
            {ganttColumns.map((col, i) => (
              <ColumnDirective key={i} {...col} />
            ))}
          </ColumnsDirective>
          <EditDialogFieldsDirective>
            <EditDialogFieldDirective
              type="General"
              headerText="General"
            ></EditDialogFieldDirective>
            <EditDialogFieldDirective type="Dependency"></EditDialogFieldDirective>
            <EditDialogFieldDirective type="Resources"></EditDialogFieldDirective>
            <EditDialogFieldDirective type="Notes"></EditDialogFieldDirective>
          </EditDialogFieldsDirective>
          <Inject
            services={[
              Toolbar,
              Selection,
              UndoRedo,
              Edit,
              DayMarkers,
              ColumnMenu,
              Filter,
              Sort,
              ContextMenu,
              Resize,
              Reorder,
              RowDD,
              CriticalPath,
              ExcelExport,
              PdfExport,
            ]}
          />
        </GanttComponent>
      ) : (
        <Skeleton />
      )}
    </div>
  );
};

export default ProjectProgressGantt;
