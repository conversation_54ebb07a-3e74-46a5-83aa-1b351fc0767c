import { LoadingOutlined } from "@ant-design/icons";
import { Upload, message } from "antd";
import { RcFile, UploadChangeParam } from "antd/lib/upload";
import { useEffect, useRef, useState } from "react";
import { $url } from "utils/url";

import dayjs from "dayjs";
import { MdOutlineVideoCall } from "react-icons/md";
import { FileAttach } from "types/fileAttach";
import { getToken } from "utils/auth";

interface SingleImageUploadProps {
  uploadUrl?: string;
  videoUrl: string;
  width?: number | string;
  height?: number | string;
  recommendSize?: { width: number; height: number };
  recommendRatio?: string;
  onUploadOk: (data: FileAttach) => void;
  onChangeDuration?: (duration: number) => void;
  onBefore?: () => Promise<boolean>;
  onRemoveVideo?: () => void;
}

export const SingleVideoUploadS3 = ({
  uploadUrl = import.meta.env.VITE_API_URL + "/v1/admin/fileAttach/upload",
  videoUrl,
  height = 150,
  width = 150,
  recommendSize = { width: 400, height: 400 },
  recommendRatio,
  onUploadOk,
  onChangeDuration,
  onBefore,
  onRemoveVideo,
}: SingleImageUploadProps) => {
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const sourceVideoRef = useRef<HTMLVideoElement>(null);
  const [singleUploadId, setsingleUploadId] = useState(
    `single-upload-${dayjs().valueOf()}`
  );
  const beforeUpload = async (file: File) => {
    let isValid = true;
    if (onBefore) {
      isValid = await onBefore?.();
    }

    const extension = file.name.substring(file.name.lastIndexOf("."));
    let isVideo = true;
    if (extension != ".mp4") {
      isVideo = false;
      message.error("Chỉ hỗ trợ file MP4");
    }

    return isValid && isVideo ? true : Upload.LIST_IGNORE;
  };
  const handleChange = (info: UploadChangeParam<any>) => {
    if (info.file.status === "uploading") {
      setLoading(true);
      return;
    }
    if (info.file.status === "done") {
      console.log({ info });
      onUploadOk(info.file.response.data);
      setLoading(false);
    }
    if (info.file.status === "error") {
      message.error(info.file.response?.message);
      setLoading(false);
      return;
    }
  };

  const handleAction = async (file: RcFile) => {
    try {
      setLoading(true);
    } catch (error) {
      const formData = new FormData();

      console.log({ error });
    } finally {
      setLoading(false);
    }
  };

  const uploadButton = (
    <div className="w-full h-full flex flex-col justify-center items-center border-dashed border-[1px] border-gray-300 bg-[#fafafa] hover:border-blue-500 cursor-pointer transition-all">
      {loading ? (
        <LoadingOutlined />
      ) : (
        <MdOutlineVideoCall size={20} className="text-gray-500" />
      )}
      <div style={{ marginTop: 8 }}>Tải lên</div>
    </div>
  );

  useEffect(() => {
    const container = document.querySelector<HTMLDivElement>(
      `.${singleUploadId} .ant-upload-select`
    );
    if (container) {
      container.style.width = typeof width == "number" ? `${width}px` : width;
      container.style.height =
        typeof height == "number" ? `${height}px` : height;
    }
  }, []);

  return (
    <div className="flex flex-col justify-center items-center">
      <Upload
        name="file"
        accept={"video/mp4"}
        showUploadList={false}
        className={singleUploadId}
        action={uploadUrl}
        beforeUpload={beforeUpload}
        headers={{
          token: getToken() || "",
        }}
        onChange={handleChange}
      >
        {videoUrl ? (
          <video
            src={$url(videoUrl)}
            onError={(e) => {
              //@ts-ignore
              e.target.src = "/default-thumbnail.jpg";
            }}
            style={{ width: "100%", height: "100%", objectFit: "contain" }}
          />
        ) : (
          uploadButton
        )}
      </Upload>
      <p className="text-center !my-4 font-semibold">
        Kích thước đề xuất: {recommendSize.width} x {recommendSize.height}{" "}
        {recommendRatio && `(${recommendRatio})`}
      </p>
    </div>
  );
};
