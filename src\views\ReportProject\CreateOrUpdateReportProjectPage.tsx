import React, { useEffect, useState } from "react";
import { Card, Col, Row, Form, Select, Spin, Input, message } from "antd";
import PageTitle from "components/PageTitle/PageTitle";
import CustomInput from "components/Input/CustomInput";
import CustomDatePicker from "components/Input/CustomDatePicker";
import { ProjectSelector } from "components/Selector/ProjectSelector";
import { FileUploadMultiple2 } from "components/Upload/FileUploadMultiple2";
import {
  ApprovalStepsCard,
  ApproveData,
} from "components/ApproveProcess/ApprovalStepsCard";
import { FollowerSelector } from "components/Follower/FollowerSelector";
import CustomButton from "components/Button/CustomButton";
import { observer } from "mobx-react";
import { ReportType } from "types/report";
import { FileAttach } from "types/fileAttach";
import { Staff } from "types/staff";
import { useDailyLog } from "hooks/useDailyLog";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import { Pagination } from "components/Pagination";
import { getTitle } from "utils";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { Rule } from "antd/lib/form";
import { reportApi } from "api/report.api";
import { fileAttachApi } from "api/fileAttach.api";
import { $url } from "utils/url";
import TextArea from "antd/es/input/TextArea";
import { approvalListApi } from "api/approvalList.api";
import { ApprovalListType } from "types/approvalList";
import { formatDateTime } from "utils/date";
import { PermissionNames } from "types/PermissionNames";
import dayjs from "dayjs";
import clsx from "clsx";
import { ApprovalTemplateType } from "types/approvalTemplate";
import { useApprovalStep } from "hooks/useAppovalStep";
import { transformApproveData } from "components/ApproveProcess/approveUtil";
import { appStore } from "store/appStore";
import { userStore } from "store/userStore";
import { toJS } from "mobx";
import { MemberShip } from "types/memberShip";
import { isEmpty } from "lodash";
import { BMDTextArea } from "components/TextArea/BMDTextArea";
import { BMDCKEditor } from "components/Editor";

const rules: Rule[] = [{ required: true }];
const descriptionRules: Rule[] = [{ required: false }];

const REPORT_TYPE_OPTIONS = [
  { label: "Báo cáo ngày", value: ReportType.Daily },
  { label: "Báo cáo tuần", value: ReportType.Weekly },
  { label: "Báo cáo tháng", value: ReportType.Monthly },
];

interface Props {
  title: string;
  status: "create" | "update";
}

const CreateOrUpdateReportProjectPage = observer(
  ({ title = "", status }: Props) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [loadingFetch, setLoadingFetch] = useState(false);
    const [fileList, setFileList] = useState<FileAttach[]>([]);
    const [readonly, setReadonly] = useState(true);
    const [selectedReport, setSelectedReport] = useState<any>();
    const params = useParams();
    const [searchParams, setSearchParams] = useSearchParams();
    const navigate = useNavigate();

    // Approval steps and followers using the hook from IndicativePage
    const {
      followers,
      setFollowers,
      approvalSteps,
      setApprovalSteps,
      fetchApprovalTemplate,
    } = useApprovalStep();

    const [removeApprovalList, setRemoveApprovalList] = useState<number[]>([]);
    const [loadingApprove, setLoadingApprove] = useState(false);
    const [commentRefreshTrigger, setCommentRefreshTrigger] = useState(0);

    // Daily log state
    const [logFromDate, setLogFromDate] = useState<any>(null);
    const [logToDate, setLogToDate] = useState<any>(null);
    const [logStatus, setLogStatus] = useState<string | undefined>();
    const [logPage, setLogPage] = useState(1);
    const [logLimit, setLogLimit] = useState(10);
    const {
      dailyLogs,
      total,
      fetchData,
      loading: logLoading,
      setQuery,
      query,
    } = useDailyLog({
      initQuery: { limit: 10, page: 1 },
    });

    const setDataToForm = (data: any) => {
      form.setFieldsValue({
        ...data,
        projectId: data.project?.id,
        type: data.type,
        code: data.code,
        note: data.note,
        startAt: data.startAt ? dayjs.unix(data.startAt) : null,
        endAt: data.endAt ? dayjs.unix(data.endAt) : null,
        workVolume: data.workVolume,
        content: data.content,
        rain: data.rain,
        temperature: data.temperature,
      });
      setFileList(data.fileAttaches ? [...data.fileAttaches] : []);
      setFollowers(data.followMemberShips || []);
      const transformedApproveData = transformApproveData(
        data.approvalLists,
        data.createdBy
      );
      setApprovalSteps(transformedApproveData);
    };

    const getOneReport = async (id: number) => {
      try {
        setLoadingFetch(true);
        const { data } = await reportApi.findOne(id);

        if (isEmpty(data)) {
          navigate("/404");
          return;
        }

        setSelectedReport(data);
        setDataToForm(data);
        return data;
      } catch (e: any) {
        console.error("Error fetching report:", e);
      } finally {
        setLoadingFetch(false);
      }
    };

    // Fetch daily logs on filter change
    React.useEffect(() => {
      setQuery((prev) => ({
        ...prev,
        page: logPage,
        limit: logLimit,
        startAt: logFromDate ? logFromDate.valueOf() : undefined,
        endAt: logToDate ? logToDate.valueOf() : undefined,
        status: logStatus || undefined,
      }));
      fetchData(query);
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [logFromDate, logToDate, logStatus, logPage, logLimit]);

    // Columns for nhật ký công việc
    const dailyLogColumns: CustomizableColumn<any>[] = [
      {
        title: "Ngày",
        dataIndex: "startAt",
        key: "startAt",
        align: "center",
        width: 120,
        render: (value) => (value ? new Date(value).toLocaleDateString() : ""),
      },
      {
        title: "Số nhân công",
        dataIndex: "soNhanCong",
        key: "soNhanCong",
        align: "center",
        width: 120,
        render: () => "-",
      },
      {
        title: "Vai trò",
        dataIndex: "vaiTro",
        key: "vaiTro",
        align: "center",
        width: 120,
        render: () => "-",
      },
      {
        title: "Số giờ làm",
        dataIndex: "soGioLam",
        key: "soGioLam",
        align: "center",
        width: 120,
        render: () => "-",
      },
      {
        title: "Vật liệu sử dụng",
        dataIndex: "vatLieu",
        key: "vatLieu",
        align: "center",
        width: 150,
        render: () => "-",
      },
      {
        title: "Thiết bị sử dụng",
        dataIndex: "thietBi",
        key: "thietBi",
        align: "center",
        width: 150,
        render: () => "-",
      },
      {
        title: "Ghi chú",
        dataIndex: "note",
        key: "note",
        align: "left",
        width: 200,
      },
    ];

    useEffect(() => {
      document.title = getTitle(title);

      if (status === "create") {
        setReadonly(false);

        if (!appStore.currentProject) {
          return;
        }

        // Set projectId mặc định khi tạo mới
        form.setFieldsValue({
          projectId: appStore.currentProject.id,
        });

        fetchApprovalTemplate({
          projectId: appStore.currentProject.id,
          createdStaff: toJS(userStore.info) as Staff,
          type: ApprovalTemplateType.Report,
        });
      } else {
        const reportId = params.id;
        if (reportId) {
          getOneReport(+reportId);
          setReadonly(searchParams.get("update") != "1");
        } else {
          navigate("/404");
        }
      }
    }, []);

    const getDataSubmit = async () => {
      const values = await form.validateFields();
      const fileAttachIds: number[] = [];

      for (const file of fileList) {
        if (file.id) {
          fileAttachIds.push(file.id);
        } else if (file.originFile) {
          const { data } = await fileAttachApi.upload(file.originFile);
          const resFileAttach = await fileAttachApi.create({
            fileAttach: {
              ...file,
              url: $url(data.path),
            },
          });
          fileAttachIds.push(resFileAttach.data.id);
        }
      }

      // Xử lý projectId để chỉ lấy giá trị number
      const projectId =
        typeof values.projectId === "object"
          ? values.projectId.value
          : values.projectId;

      const approvalLists = approvalSteps.map((e, i) => ({
        id: e.id,
        name: e.name,
        type: ApprovalListType.Report,
        position: e.position,
        note: e.note,
        memberShipId: e.memberShipId,
        memberShip2Id: e.memberShip2Id,
        reportId: selectedReport?.id || 0,
        staffId: e.staffId,
      }));

      const payload = {
        report: {
          code: values.code,
          type: values.type,
          note: values.note,
          startAt: values.startAt
            ? Math.floor(values.startAt.valueOf() / 1000)
            : 0,
          endAt: values.endAt ? Math.floor(values.endAt.valueOf() / 1000) : 0,
          workVolume: Number(values.workVolume) || 0,
          content: values.content,
          rain: values.rain,
          temperature: values.temperature,
          isActive: selectedReport?.isActive,
        },
        projectId: projectId,
        followMemberShipIds: followers?.map((it) => it.id),
        fileAttachIds,
        approvalLists: approvalLists,
      };

      console.log("🔍 ReportProject payload:", payload);
      return payload;
    };

    const createData = async () => {
      setLoading(true);
      try {
        const payload = await getDataSubmit();
        const response = await reportApi.create(payload);

        message.success("Tạo báo cáo thành công!");
        navigate(`/report/${PermissionNames.projectReportList}`);
        setFileList([]);
      } catch (error) {
        console.error("Error creating report:", error);
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      setLoading(true);
      try {
        const payload = await getDataSubmit();
        const response = await reportApi.update(
          selectedReport!.id || 0,
          payload
        );
        setSelectedReport({ ...selectedReport, ...response.data });
        setRemoveApprovalList([]);

        message.success("Chỉnh sửa báo cáo thành công!");
      } catch (error) {
        console.error("Error updating report:", error);
      } finally {
        setLoading(false);
      }
    };

    const handleSubmit = () => {
      if (status === "create") {
        createData();
      } else {
        updateData();
      }
    };

    const handleApproveProcess = async (data: ApproveData) => {
      console.log("Approve process");
      try {
        setLoadingApprove(true);
        await reportApi.approve(selectedReport!.id || 0, data);
        message.success("Duyệt báo cáo thành công!");
        await getOneReport(selectedReport!.id || 0);
        // Trigger refresh CommentView
        setCommentRefreshTrigger((prev) => prev + 1);
      } catch (error) {
        console.error("Error approving report:", error);
      } finally {
        setLoadingApprove(false);
      }
    };

    const handleRejectProcess = async (data: ApproveData) => {
      console.log("Reject process");
      try {
        setLoadingApprove(true);
        await reportApi.reject(selectedReport!.id || 0, data);
        message.success("Từ chối báo cáo thành công!");
        await getOneReport(selectedReport!.id || 0);
        // Trigger refresh CommentView
        setCommentRefreshTrigger((prev) => prev + 1);
      } catch (error) {
        console.error("Error rejecting report:", error);
      } finally {
        setLoadingApprove(false);
      }
    };

    return (
      <div className="app-container">
        <PageTitle
          title={title}
          back
          breadcrumbs={[
            { label: "Báo cáo" },
            {
              label: "Báo cáo dự án",
              href: `/report/${PermissionNames.projectReportList}`,
            },
            { label: title },
          ]}
        />
        <Spin spinning={loadingFetch}>
          <Form
            form={form}
            layout="vertical"
            className={clsx(readonly ? "readonly" : "")}
            disabled={readonly}
            onFinish={handleSubmit}
          >
            <Row gutter={24}>
              {/* Left Side */}
              <Col span={18}>
                <Card className="content-card">
                  <Card title="Thông tin cơ bản" className="mb-0 form-card">
                    <Row gutter={16}>
                      {/* Row 1 */}
                      <Col span={8}>
                        <Form.Item name="code" label="Mã báo cáo">
                          <CustomInput
                            placeholder="Mã báo cáo"
                            disabled={readonly}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item name="projectId" label="Dự án" rules={rules}>
                          <ProjectSelector
                            placeholder="Chọn dự án"
                            disabled={readonly || status === "create"}
                            initOptionItem={form?.getFieldValue("project")}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item
                          name="type"
                          label="Loại báo cáo"
                          rules={rules}
                        >
                          <Select
                            options={REPORT_TYPE_OPTIONS}
                            placeholder="Chọn loại báo cáo"
                            disabled={readonly}
                          />
                        </Form.Item>
                      </Col>
                      {/* Row 2 */}
                      <Col span={8}>
                        <Form.Item
                          name="startAt"
                          label="Ngày bắt đầu"
                          rules={rules}
                        >
                          <CustomDatePicker
                            placeholder="Ngày bắt đầu"
                            disabled={readonly}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item
                          name="endAt"
                          label="Ngày kết thúc"
                          rules={rules}
                        >
                          <CustomDatePicker
                            placeholder="Ngày kết thúc"
                            disabled={readonly}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item name="workVolume" label="Mức độ hoàn thành">
                          <CustomInput
                            placeholder="Mức độ hoàn thành (%)"
                            type="text"
                            disabled={readonly}
                          />
                        </Form.Item>
                      </Col>
                      {/* Row 3 */}
                      <Col span={24}>
                        <Form.Item
                          name="note"
                          label="Nội dung báo cáo"
                          rules={descriptionRules}
                        >
                          <BMDCKEditor
                            placeholder="Nhập nội dung báo cáo"
                            disabled={readonly}
                            value={selectedReport?.note}
                            inputHeight={300}
                            onChange={(content) => {
                              form.setFieldsValue({ note: content });
                            }}
                          />
                        </Form.Item>
                      </Col>
                      {/* Row 4: File upload */}
                      <Col span={24}>
                        <Form.Item label="Tệp đính kèm" name="files">
                          <FileUploadMultiple2
                            fileList={fileList}
                            onUploadOk={(file) => {
                              setFileList([...fileList, file]);
                            }}
                            onDelete={(file) => {
                              setFileList(
                                fileList.filter((f) => f.uid !== file.uid)
                              );
                            }}
                            hideUploadButton={readonly}
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Card>
                  {/* Thời tiết */}
                  <Card title="Thời tiết" className="mb-0 form-card mt-4">
                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name="temperature"
                          label="Nhiệt độ"
                          rules={rules}
                        >
                          <CustomInput
                            placeholder="Nhiệt độ"
                            disabled={readonly}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item name="rain" label="Lượng mưa" rules={rules}>
                          <CustomInput
                            placeholder="Lượng mưa"
                            disabled={readonly}
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Card>
                  {/* Nhật ký công việc */}
                  <Card
                    title="Nhật ký công việc"
                    className="mb-0 form-card mt-4"
                  >
                    <div className="flex gap-[16px] items-end pb-[12px] flex-wrap">
                      <div className="w-[180px]">
                        <CustomDatePicker
                          value={logFromDate}
                          onChange={setLogFromDate}
                          placeholder="Ngày bắt đầu"
                        />
                      </div>
                      <div className="w-[180px]">
                        <CustomDatePicker
                          value={logToDate}
                          onChange={setLogToDate}
                          placeholder="Ngày kết thúc"
                        />
                      </div>
                      <div className="w-[200px]">
                        <Select
                          value={logStatus}
                          options={[
                            { label: "Hoạt động", value: "active" },
                            { label: "Bị khóa", value: "inactive" },
                          ]}
                          placeholder="Trạng thái"
                          allowClear
                          onChange={setLogStatus}
                        />
                      </div>
                      <CustomButton
                        onClick={() => {
                          setLogPage(1);
                          fetchData(query);
                        }}
                      >
                        Áp dụng
                      </CustomButton>
                      <CustomButton
                        variant="outline"
                        onClick={() => {
                          setLogFromDate(null);
                          setLogToDate(null);
                          setLogStatus(undefined);
                          setLogPage(1);
                          setLogLimit(10);
                        }}
                      >
                        Bỏ lọc
                      </CustomButton>
                    </div>
                    <CustomizableTable
                      columns={dailyLogColumns}
                      dataSource={dailyLogs}
                      rowKey="id"
                      loading={logLoading}
                      pagination={false}
                      scroll={{ x: 900 }}
                      bordered
                      tableId="daily-log-table"
                    />
                    <Pagination
                      currentPage={logPage}
                      defaultPageSize={logLimit}
                      total={total}
                      onChange={({ limit, page }) => {
                        setLogPage(page);
                        setLogLimit(limit);
                      }}
                      disabled={readonly}
                    />
                  </Card>
                  {/* Action Buttons */}
                  <div className="mt-[16px] flex justify-end gap-3">
                    {!readonly && (
                      <CustomButton
                        variant="outline"
                        className="cta-button"
                        onClick={() => {
                          if (status === "create") {
                            navigate(
                              `/report/${PermissionNames.projectReportList}`
                            );
                          } else {
                            setDataToForm(selectedReport!);
                            setReadonly(true);
                          }
                        }}
                      >
                        Hủy
                      </CustomButton>
                    )}
                    <CustomButton
                      className="cta-button"
                      loading={loading}
                      onClick={() => {
                        if (!readonly) {
                          handleSubmit();
                        } else {
                          setReadonly(false);
                        }
                      }}
                    >
                      {status === "create"
                        ? "Tạo báo cáo"
                        : readonly
                        ? "Chỉnh sửa"
                        : "Lưu chỉnh sửa"}
                    </CustomButton>
                  </div>
                </Card>
              </Col>
              {/* Right Side */}
              <Col span={6}>
                <ApprovalStepsCard
                  steps={approvalSteps}
                  loading={loadingApprove}
                  onSelectStep={setApprovalSteps}
                  onRemove={setRemoveApprovalList}
                  onApprove={handleApproveProcess}
                  onReject={handleRejectProcess}
                  templateType={ApprovalTemplateType.Report}
                  editable={true}
                />
                <FollowerSelector
                  followers={followers as MemberShip[]}
                  setFollowers={setFollowers}
                  readonly={readonly}
                  headerTitle={`Người theo dõi (${followers?.length})`}
                />
              </Col>
            </Row>
          </Form>
        </Spin>
      </div>
    );
  }
);

export default CreateOrUpdateReportProjectPage;
