import { Col, Form, Input, message, Modal, Row } from "antd";
import { Rule } from "antd/lib/form";
import { brandApi } from "api/brand.api";
import CustomInput from "components/Input/CustomInput";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { ModalStatus } from "types/modal";
import { Brand } from "types/brand";

const rules: Rule[] = [{ required: true }];

export interface BrandModalRef {
  handleCreate: () => void;
  handleUpdate: (brand: Brand) => void;
}
interface BrandModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
}

export const BrandModal = React.forwardRef(
  ({ onClose, onSubmitOk }: BrandModalProps, ref) => {
    const [form] = Form.useForm<Brand>();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");
    const [selectedBrand, setSelectedBrand] = useState<Brand>();

    useImperativeHandle<any, BrandModalRef>(
      ref,
      () => ({
        handleCreate() {
          form.resetFields();
          setVisible(true);
          setStatus("create");
        },
        handleUpdate(brand: Brand) {
          setSelectedBrand(brand);
          form.setFieldsValue({ ...brand });
          setVisible(true);
          setStatus("update");
        },
      }),
      []
    );

    const createData = async () => {
      const valid = await form.validateFields();
      const data = { brand: form.getFieldsValue() };

      setLoading(true);
      try {
        const res = await brandApi.create(data);
        message.success("Tạo thương hiệu thành công!");
        onClose();
        onSubmitOk();
        setVisible(false);
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const valid = await form.validateFields();
      const data = { brand: form.getFieldsValue() };
      setLoading(true);
      try {
        const res = await brandApi.update(selectedBrand?.id || 0, data);
        message.success("Cập nhật thương hiệu thành công!");
        onClose();
        onSubmitOk();
        setVisible(false);
      } finally {
        setLoading(false);
      }
    };

    return (
      <Modal
        className="footer-full"
        onCancel={() => {
          onClose?.();
          setVisible(false);
        }}
        centered
        visible={visible}
        title={status == "create" ? "Tạo thương hiệu" : "Cập nhật thương hiệu"}
        style={{ top: 20 }}
        width={550}
        confirmLoading={loading}
        cancelButtonProps={{ style: { display: "none" } }}
        okText={status == "create" ? "Tạo" : "Cập nhật"}
        onOk={() => {
          status == "create" ? createData() : updateData();
        }}
        getContainer={() => {
          return document.getElementById("App") as HTMLElement;
        }}
      >
        <Form layout="vertical" form={form}>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item name="code" label="Mã thương hiệu">
                <CustomInput placeholder="Mã thương hiệu nếu không nhập hệ thống sẽ tự tạo" />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item name="name" label="Tên thương hiệu" rules={rules}>
                <CustomInput placeholder="Nhập tên thương hiệu" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
);
