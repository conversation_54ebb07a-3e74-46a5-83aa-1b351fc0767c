import { <PERSON>, Avatar, Table, Tooltip, Button } from "antd";
import { CustomizableColumn } from "components/Table/CustomizableTable";
import EditButton from "components/Button/EditButton";
import DeleteButton from "components/Button/DeleteButton";
import LockButton from "components/Button/LockButton";
import ActiveStatusTag from "components/ActiveStatus/ActiveStatusTag";
import ProgressLegend from "components/ProgressLegend/ProgressLegend";
import { Task } from "types/task";
import {
  getOverallApprovalStatus,
  ProgressInstructionStatus,
} from "types/instruction";
import { $url } from "utils/url";
import { useNavigate, useLocation } from "react-router-dom";
import { useTheme } from "context/ThemeContext";
import { PermissionNames } from "types/PermissionNames";
import { checkRoles, filterActionColumnIfNoPermission } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { observer } from "mobx-react";
import { ReactComponent as Copy } from "assets/svgs/copy.svg";
import { ArrowDownIcon } from "assets/svgs/ArrowDownIcon";
import dayjs from "dayjs";
import { settings } from "settings";

interface TaskSubTableProps {
  tasks: Task[];
  onEdit: (task: Task) => void;
  onDelete: (task: Task) => void;
  canEditRecord: (task: Task) => boolean;
  canDeleteRecord: (task: Task) => boolean;
  renderStaffRow: (staff: any) => JSX.Element; // Adjusted to 'any' to match Staff or undefined
}

export const TaskSubTable: React.FC<TaskSubTableProps> = observer(
  ({
    tasks,
    onEdit,
    onDelete,
    canEditRecord,
    canDeleteRecord,
    renderStaffRow,
  }) => {
    const { haveBlockPermission, haveEditPermission, haveAddPermission } =
      checkRoles(
        {
          edit: PermissionNames.taskEdit,
          block: PermissionNames.taskDelete,
          add: PermissionNames.taskAdd,
        },
        permissionStore.permissions
      );

    const { darkMode } = useTheme();
    const navigate = useNavigate();
    const location = useLocation();

    const handleViewTask = (taskId: number) => {
      navigate(
        `/task/${PermissionNames.taskEdit.replace(
          ":id",
          taskId.toString()
        )}`
      );
    };

    const handleEditTask = (taskId: number) => {
      navigate(
        `/task/${PermissionNames.taskEdit.replace(
          ":id",
          taskId.toString()
        )}?update=1`
      );
    };

    const handleCopyTask = (task: Task) => {
      const isInProgressManagement = location.pathname.includes(
        "/task/"
      );
      navigate(`/task/${PermissionNames.taskAdd}`, {
        state: {
          copyData: {
            id: task.id.toString(),
            code: task.code,
            title: task.title,
            department: task.department?.name,
            createdBy: task.createdBy,
            assigneeMemberShip: task.assigneeMemberShip,
            endDate: task.endDate,
            //   isActive: task.isActive,
            taskCategoryId: task.taskCategory?.id,
            taskCategoryName: task.taskCategory?.name,
          },
          fromProgressManagement: isInProgressManagement,
        },
      });
    };

    const taskColumns: CustomizableColumn<Task>[] = [
      {
        key: "id",
        title: "",
        dataIndex: "code",
        width: 100,
        render: (_, record) => (
          <div
            className="text-[#1677ff] cursor-pointer"
            onClick={() => handleViewTask(record.id)}
          >
            {record.code}
          </div>
        ),
        defaultVisible: true,
        alwaysVisible: true,
      },
      {
        key: "title",
        title: "",
        dataIndex: "title",
        width: 160,
        defaultVisible: true,
        alwaysVisible: true,
      },
      {
        key: "project",
        title: "",
        dataIndex: "project",
        width: 150,
        render: (_, record) => record.project?.name || "N/A",
        defaultVisible: true,
        alwaysVisible: true,
      },
      {
        key: "department",
        title: "",
        dataIndex: "department",
        width: 170,
        render: (_, record) => record.department?.name || "N/A",
        defaultVisible: true,
        alwaysVisible: true,
      },
      {
        key: "createdBy",
        title: "",
        dataIndex: "createdBy",
        width: 160,
        render: (_, record) => renderStaffRow(record.createdBy),
        defaultVisible: true,
        alwaysVisible: true,
      },
      {
        key: "assignee",
        title: "",
        dataIndex: "assigneeMemberShip",
        width: 160,
        render: (_, record) =>
          record.assigneeMemberShip?.staff
            ? renderStaffRow(record.assigneeMemberShip.staff)
            : "N/A",
        defaultVisible: true,
        alwaysVisible: true,
      },
      {
        key: "endDate",
        title: "",
        dataIndex: "endDate",
        width: 100,
        defaultVisible: true,
        alwaysVisible: true,
        render: (endDate) => {
          return endDate ? dayjs(endDate).format(settings.dateFormat) : "";
        },
      },
      {
        key: "status",
        title: "",
        dataIndex: "isActive",
        align: "center",
        width: 130,
        render: (isActive, record) => {
          // Sort approvalLists theo position trước khi xử lý
          const sortedApprovalLists = record.approvalLists
            ? record.approvalLists
                .slice()
                .sort(
                  (a: { position: number }, b: { position: number }) =>
                    a.position - b.position
                )
            : [];
          return (
            <ProgressLegend
              status={
                ProgressInstructionStatus[
                  getOverallApprovalStatus(sortedApprovalLists)
                ]
              }
              steps={sortedApprovalLists}
            />
          );
        },
        defaultVisible: true,
        alwaysVisible: true,
      },
      {
        key: "actions",
        title: "",
        align: "center",
        width: 80,
        render: (_, record) => (
          <Space size="small">
            {haveAddPermission && (
              <Tooltip title="Tạo nhanh từ bản này">
                <Button
                  type="text"
                  icon={<Copy />}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCopyTask(record);
                  }}
                />
              </Tooltip>
            )}
            {canEditRecord(record) && (
              <EditButton
                onClick={(e) => {
                  e.stopPropagation();
                  handleEditTask(record.id);
                }}
              />
            )}
            {canDeleteRecord(record) && (
              <DeleteButton
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete(record);
                }}
              />
            )}
          </Space>
        ),
        defaultVisible: true,
        alwaysVisible: true,
      },
    ];

    return (
      <div className="">
        <Table
          columns={filterActionColumnIfNoPermission(taskColumns, [
            haveAddPermission,
            haveEditPermission,
            haveBlockPermission,
          ])}
          dataSource={tasks}
          rowKey="id"
          loading={false}
          pagination={false}
          bordered={false}
          size="small"
          showHeader={false}
          className="role-sub-table"
          onRow={(record) => ({
            onDoubleClick: () => {
              handleViewTask(record.id);
            },
          })}
          expandable={{
            expandedRowRender: () => <></>,
            rowExpandable: () => false,
            expandIcon: ({ expanded, onExpand, record }) => (
              <Button
                type="text"
                size="small"
                icon={
                  <ArrowDownIcon
                    className={`transition-transform duration-200 ${
                      expanded ? "rotate-0" : "-rotate-90"
                    }`}
                    fill={darkMode ? "#ffffff" : "#6b7280"}
                  />
                }
                onClick={(e) => onExpand(record, e)}
                className="!border-0 !shadow-none hover:!bg-gray-100 invisible"
              />
            ),
          }}
        />
      </div>
    );
  }
);
