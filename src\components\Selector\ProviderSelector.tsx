import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
} from "react";
import { debounce, isEmpty, uniqBy } from "lodash";
import { useProvider } from "hooks/useProvider";
import { Provider } from "types/provider";
import { QueryParams2 } from "types/query";
import CustomSelect from "components/Input/CustomSelect";
import { Select, Tooltip } from "antd";
import { providerApi } from "api/provider.api";

type CustomFormItemProps = {
  value?: number;
  initQuery?: QueryParams2;
  disabled?: boolean;
  selectedProvider?: Provider[];
  multiple?: boolean;
  onChange?: (value: any) => void;
  initOptionItem?: Provider | Provider[];
  valueIsOption?: boolean;
  allowClear?: boolean;
  placeholder?: string;
  label?: string;
  addonOptions?: any[];
  inputStyle?: React.CSSProperties;
  tooltipContent?: string;
};

export interface ProviderSelector {
  refresh(): void;
}

export const ProviderSelector = forwardRef(
  (
    {
      value,
      onChange,
      initQuery,
      disabled,
      multiple = false,
      selectedProvider,
      initOptionItem,
      valueIsOption,
      allowClear = true,
      placeholder = "Chọn NCC",
      label = "",
      addonOptions = [],
      inputStyle,
      tooltipContent,
    }: CustomFormItemProps,
    ref
  ) => {
    const { providers, loading, fetchData, query, setData, isFetched } =
      useProvider({
        initQuery: { page: 1, limit: 0, ...initQuery },
      });

    useImperativeHandle<any, ProviderSelector>(
      ref,
      () => ({
        refresh() {
          fetchData();
        },
      }),
      []
    );

    useEffect(() => {
      fetchData();
    }, [selectedProvider]);

    const debounceSearch = useCallback(
      debounce((keyword) => {
        query.search = keyword;
        fetchData();
      }, 300),
      [query]
    );

    //xử lý nếu trong projects thiếu value hiện tại thì add vào
    useEffect(() => {
      if (value && isFetched) {
        const find = providers.find((e) => e.id == value);
        if (!find) {
          providerApi.findOne(value).then((res) => {
            if (!isEmpty(res.data)) {
              setData((prev) => [res.data, ...prev]);
            }
          });
        }
      }
    }, [value, providers, isFetched]);

    const options = useMemo(() => {
      let data = [...providers];
      if (initOptionItem) {
        if ((initOptionItem as Provider[])?.length) {
          data = data.concat(initOptionItem);
        } else {
          data.push(initOptionItem as Provider);
        }
      }
      return uniqBy([...addonOptions, ...data], (item) => item.id).map(
        (item) => ({
          label: item.name,
          value: item.id,
          item, // lưu nguyên object nếu cần dùng sau
        })
      );
    }, [providers, initOptionItem, addonOptions]);

    const handleChange = (v: any, option: any) => {
      if (valueIsOption) {
        if (v == undefined) {
          onChange?.(null);
        } else if (option instanceof Array) {
          onChange?.(option.map((opt) => opt.item));
        } else {
          onChange?.(option.item);
        }
      } else {
        onChange?.(v);
      }
    };

    return (
      <>
        {tooltipContent ? (
          <Tooltip mouseEnterDelay={0.3} title={tooltipContent}>
            <Select
              value={value}
              onChange={handleChange}
              disabled={disabled}
              options={options}
              mode={multiple ? "multiple" : undefined}
              allowClear={allowClear}
              placeholder={placeholder}
              onSearch={debounceSearch}
              loading={loading}
              showSearch
              filterOption={false}
              style={inputStyle}
            />
          </Tooltip>
        ) : (
          <Select
            value={value}
            onChange={handleChange}
            disabled={disabled}
            options={options}
            mode={multiple ? "multiple" : undefined}
            allowClear={allowClear}
            placeholder={placeholder}
            onSearch={debounceSearch}
            loading={loading}
            showSearch
            filterOption={false}
            style={inputStyle}
          />
        )}
      </>
    );
  }
);
