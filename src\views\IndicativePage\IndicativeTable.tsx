import {
  Spin,
  Button,
  Space,
  Tooltip,
  Modal,
  Tag,
  Avatar,
  message,
} from "antd";
import PencilIcon from "assets/svgs/PencilIcon";
import CustomButton from "components/Button/CustomButton";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import { Pagination } from "components/Pagination";
import { useTheme } from "context/ThemeContext";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { PermissionNames } from "types/PermissionNames";
import { useInstruction } from "hooks/useInstruction";
import {
  getOverallApprovalStatus,
  Instruction,
  InstructionStatus,
  InstructionType,
  InstructionTypeTrans,
  ProgressInstructionStatus,
} from "types/instruction";
import { LockOutlined, UnlockOutlined } from "@ant-design/icons";
import {
  checkDeletePermissionByCreator,
  checkRoles,
  filterActionColumnIfNoPermission,
} from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { observer } from "mobx-react";
import { BMDImage } from "components/Image/BMDImage";
import { $url } from "utils/url";
import logoImage from "assets/images/logo.png";
import { Staff } from "types/staff";
import { QueryParam } from "types/query";
import LockButton from "components/Button/LockButton";
import { instructionApi } from "api/instruction.api";
import EditButton from "components/Button/EditButton";
import ProgressLegend from "components/ProgressLegend/ProgressLegend";
import { checkEditPermissionByCreator } from "utils/auth";
import { userStore } from "store/userStore";
import {
  findLastApprovalStep,
  transformApproveData,
} from "components/ApproveProcess/approveUtil";
import { StepItem } from "components/ApproveProcess/ApprovalStepsCard";

interface IndicativeTableProps {
  type: InstructionType;
  query: QueryParam;
}

function IndicativeTable({ type, query }: IndicativeTableProps) {
  const { haveBlockPermission, haveEditPermission, haveViewAllPermission } =
    checkRoles(
      {
        edit: PermissionNames.indicativeEdit,
        block: PermissionNames.indicativeBlock,
        viewAll: PermissionNames.indicativeViewAll,
      },
      permissionStore.permissions
    );

  const canEditRecord = (record: Instruction) => {
    return checkEditPermissionByCreator(
      userStore.info.id,
      record.createdBy?.id,
      haveEditPermission,
      haveViewAllPermission
    );
  };

  const canDeleteRecord = (record: Instruction) => {
    return checkDeletePermissionByCreator(
      userStore.info.id,
      record.createdBy?.id,
      haveBlockPermission,
      haveViewAllPermission
    );
  };

  const {
    fetchInstruction,
    instructions,
    loadingInstruction,
    queryInstruction,
    setQueryInstruction,
    totalInstruction,
  } = useInstruction({ initQuery: { ...query, type } });

  useEffect(() => {
    // delete (query as any).limit;
    const newQuery = {
      ...queryInstruction,
      ...query,
      // Keep pagination values from queryInstruction
      page: queryInstruction.page,
      limit: queryInstruction.limit,
    };

    setQueryInstruction(newQuery);
    fetchInstruction(newQuery);
  }, [query]);

  const { darkMode } = useTheme();
  const navigate = useNavigate();

  const handleActiveInstruction = async (id: number, value: boolean) => {
    try {
      await instructionApi.update(id, { instruction: { isActive: !value } });
      message.success(value ? "Khóa thành công" : "Mở khóa thành công");
      fetchInstruction();
    } catch (error) {
    } finally {
    }
  };

  // Update the pagination handler
  const handlePaginationChange = ({
    limit,
    page,
  }: {
    limit: number;
    page: number;
  }) => {
    const newQuery = {
      ...queryInstruction,
      page,
      limit,
    };
    setQueryInstruction(newQuery);
    fetchInstruction(newQuery); // Make sure to call fetch with new query
  };

  const handleRowClick = (record: Instruction) => {
    navigate(
      `/report/${PermissionNames.indicativeEdit.replace(
        ":id",
        record!.id + ""
      )}`
    );
  };

  const columns: CustomizableColumn<any>[] = [
    {
      key: "code",
      title: "Mã",
      dataIndex: "code",
      width: 100,
      defaultVisible: true,
      // alwaysVisible: true,
      align: "center",
      render: (_, record: Instruction) => (
        <div
          onClick={() => handleRowClick(record)}
          className="text-[#1677ff] cursor-pointer"
        >
          {record.code}
        </div>
      ),
    },
    {
      key: "name",
      title: "Tiêu đề",
      dataIndex: "name",
      width: 100,
      defaultVisible: true,
      // alwaysVisible: true,
    },
    {
      key: "type",
      title: "Loại chỉ thị",
      dataIndex: "type",
      width: 100,
      defaultVisible: true,
      // alwaysVisible: true,
      render: (_, record: Instruction) =>
        InstructionTypeTrans[record.type]?.label || "-",
    },
    // {
    //   key: "indicator",
    //   title: "Người phát hành",
    //   dataIndex: "indicator",
    //   width: 100,
    //   defaultVisible: true,
    //   alwaysVisible: true,
    //   render: (_, record: Instruction) =>
    //     record.createdMemberShipBy?.staff
    //       ? _renderStaffRow(record.createdMemberShipBy.staff)
    //       : "",
    // },
    {
      key: "receiver",
      title: "Người nhận",
      dataIndex: "receiver",
      width: 100,
      defaultVisible: true,
      // alwaysVisible: true,
      render: (_, record: Instruction) =>
        record.receivedMemberShipBy?.staff
          ? _renderStaffRow(record.receivedMemberShipBy.staff)
          : "",
    },
    {
      key: "projectName",
      title: "Tên công trình",
      dataIndex: "projectName",
      width: 100,
      defaultVisible: true,
      // alwaysVisible: true,
      render: (_, record: Instruction) => record.project?.name || "-",
    },
    // {
    //   key: "level",
    //   title: "Cấp duyệt",
    //   dataIndex: "level",
    //   width: 100,
    //   defaultVisible: true,
    //   alwaysVisible: true,
    // },
    {
      title: "Trạng thái",
      dataIndex: "isActive",
      key: "isActive",
      align: "center",
      width: 100,
      // sorter: true,
      render: (isActive, record) => {
        const lastApprovalStep: StepItem | undefined = findLastApprovalStep(
          transformApproveData(record.approvalLists, record.createdBy)
        );
        // Sort approvalLists theo position trước khi xử lý
        // const sortedApprovalLists = record.approvalLists
        //   ? record.approvalLists
        //       .slice()
        //       .sort(
        //         (a: { position: number }, b: { position: number }) =>
        //           a.position - b.position
        //       )
        //   : [];
        return (
          <ProgressLegend
            // status={
            //   ProgressInstructionStatus[
            //     getOverallApprovalStatus(sortedApprovalLists)
            //   ]
            // }
            statusColor={lastApprovalStep?.statusColor}
            statusText={lastApprovalStep?.statusText}
            steps={record.approvalLists}
          />
        );
      },
    },
    {
      key: "actions",
      title: "Xử lý",
      width: 100,
      align: "center",
      fixed: "right",
      render: (_, record) => (
        <Space size="small">
          {canEditRecord(record) && (
            <EditButton
              onClick={(e) => {
                e.stopPropagation();
                navigate(
                  `/report/${PermissionNames.indicativeEdit.replace(
                    ":id",
                    record!.id + ""
                  )}?update=1`
                );
              }}
            />
          )}
          {canDeleteRecord(record) && (
            <LockButton
              isActive={record.isActive}
              onAccept={() =>
                handleActiveInstruction(record.id, record.isActive)
              }
              modalTitle={`${record.isActive ? "Khóa" : "Mở khóa"} chỉ thị: ${
                record.name
              }`}
              modalContent={
                <>
                  <div>
                    Khi {record.isActive ? "khóa" : "mở khóa"} chỉ thị này,
                    trạng thái của chỉ thị sẽ được thay đổi.
                    <br />
                    Bạn có chắc chắn muốn {record.isActive
                      ? "khóa"
                      : "mở khóa"}{" "}
                    chỉ thị này?
                  </div>
                </>
              }
            />
          )}
        </Space>
      ),
    },
  ];

  const _renderStaffRow = (staff: Staff) => {
    return (
      <div className="flex items-center gap-[8px]">
        <Avatar
          size={28}
          src={staff?.avatar ? $url(staff?.avatar) : undefined}
          style={{ backgroundColor: "#1890ff", flexShrink: 0 }}
        >
          {staff?.fullName?.charAt(0)}
        </Avatar>
        <label htmlFor="" className="text-neutral-800 text-bold">
          {staff?.fullName}
        </label>
      </div>
    );
  };

  return (
    <div>
      <div className="pb-[16px]">
        <Spin spinning={loadingInstruction}>
          <CustomizableTable
            columns={filterActionColumnIfNoPermission(columns, [
              haveEditPermission,
              haveBlockPermission,
            ])}
            dataSource={instructions}
            rowKey="id"
            // loading={loadingInstruction}
            pagination={false}
            scroll={{ x: 1200 }}
            bordered
            displayOptions
            onRowClick={handleRowClick}
          />
        </Spin>
      </div>

      <Pagination
        defaultPageSize={queryInstruction.limit}
        currentPage={queryInstruction.page}
        total={totalInstruction}
        onChange={handlePaginationChange}
      />
    </div>
  );
}

export default observer(IndicativeTable);
