import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const projectItemApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/projectItem",
      params,
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/projectItem/${id}`,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/projectItem",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/projectItem/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/projectItem/${id}`,
      method: "delete",
    }),
};
