import { Col, Form, Input, message, Modal, Row } from "antd";
import { Rule } from "antd/lib/form";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { ModalStatus } from "types/modal";
import CustomInput from "components/Input/CustomInput";
import { BMDTextArea } from "components/TextArea/BMDTextArea";

const rules: Rule[] = [
  { required: true, message: "Vui lòng nhập tên thư mục" },
];

export interface FolderCreateModal {
  handleCreate: () => void;
  handleUpdate: (name: string, description?: string) => void;
}

interface FolderCreateModalProps {
  onClose: () => void;
  onSubmitOk: (name: string, description?: string) => void;
  enterDescription?: boolean;
}

export const FolderCreateModal = React.forwardRef(
  ({ onClose, onSubmitOk, enterDescription = false }: FolderCreateModalProps, ref) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");

    useImperativeHandle<any, FolderCreateModal>(
      ref,
      () => ({
        handleCreate() {
          form.resetFields();
          setVisible(true);
          setStatus("create");
        },
        handleUpdate(name: string, description?: string) {
          form.setFieldsValue({ name, description });
          setVisible(true);
          setStatus("update");
        },
      }),
      []
    );

    const handleClose = () => {
      onClose?.();
      setVisible(false);
      form.resetFields();
      setStatus("create");
    };

    const handleSubmit = async () => {
      try {
        setLoading(true);
        const values = await form.validateFields();
        if (!!enterDescription) {
          onSubmitOk(values.name.trim(), values.description.trim());
        } else {
          onSubmitOk(values.name.trim());
        }

        handleClose();
      } catch (error) {
        console.error("Validation failed:", error);
      } finally {
        setLoading(false);
      }
    };

    return (
      <Modal
        className="footer-full"
        onCancel={handleClose}
        open={visible}
        title={status === "create" ? "Tạo thư mục" : "Sửa tên thư mục"}
        style={{ top: 20 }}
        width={500}
        confirmLoading={loading}
        onOk={handleSubmit}
        cancelButtonProps={{ style: { display: "none" } }}
      >
        <Form layout="vertical" form={form}>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item label="Tên thư mục" name="name" rules={rules}>
                <CustomInput placeholder="Nhập tên thư mục" />
              </Form.Item>
            </Col>

            {!!enterDescription && (
              <Col span={24}>
                <Form.Item label="Mô tả" name="description" rules={rules}>
                  <BMDTextArea placeholder="Nhập mô tả" />
                </Form.Item>
              </Col>
            )}
          </Row>
        </Form>
      </Modal>
    );
  }
);
