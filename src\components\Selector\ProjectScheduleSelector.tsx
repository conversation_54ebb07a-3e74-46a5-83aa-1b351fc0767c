import { Select } from "antd";
import { DefaultOptionType } from "antd/es/select";
import { SelectProps } from "antd/lib";
import { debounce, isEmpty, uniqBy } from "lodash";
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
} from "react";
import { QueryParams2 } from "types/query";
import { useScheduleSelect } from "hooks/useScheduleSelect";
import { Schedule } from "types/schedule";
import { scheduleApi } from "api/schedule.api";

type CustomFormItemProps = {
  value?: number;
  initQuery?: QueryParams2;
  disabled?: boolean;
  selectedColor?: any[];
  multiple?: boolean;
  onChange?: (value: any) => void;
  selectProps?: SelectProps;
  initOptionItem?: Schedule | Schedule[];
  valueIsOption?: boolean;
  allowClear?: boolean;
  placeholder?: string;
  addonOptions?: any[];
};

export interface ProjectItemSelector {
  refresh(): void;
}

/**
 * A demo that changes it's number value on click.
 * @param value initialValue passed by parent Form.Item.
 * @param onChange a callback for Form.Item to read its child component's value.
 * @constructor
 */
export const ProjectScheduleSelector = forwardRef(
  (
    {
      value,
      onChange,
      initQuery,
      disabled,
      multiple = false,
      selectedColor,
      initOptionItem,
      valueIsOption,
      selectProps,
      allowClear = true,
      placeholder = "Chọn hạng mục",
      addonOptions = [],
    }: CustomFormItemProps,
    ref
  ) => {
    const {
      schedulesSelect,
      loadingScheduleSelect,
      fetchScheduleSelect,
      queryScheduleSelect,
      setScheduleSelect,
    } = useScheduleSelect({
      initQuery: {
        page: 1,
        limit: 50,
        ...initQuery,
      },
    });

    useImperativeHandle<any, ProjectItemSelector>(
      ref,
      () => ({
        refresh() {
          fetchScheduleSelect();
        },
      }),
      []
    );

    useEffect(() => {
      fetchScheduleSelect();
    }, [selectedColor]);

    const debounceSearch = useCallback(
      debounce((keyword) => {
        queryScheduleSelect.search = keyword;
        fetchScheduleSelect();
      }, 300),
      [queryScheduleSelect]
    );

    //xử lý nếu trong projects thiếu value hiện tại thì add vào
    // useEffect(() => {
    //   if (value && loadingScheduleSelect === false) {
    //     const find = schedulesSelect.find((e) => e.id == value);
    //     if (!find) {
    //       scheduleApi.findOne(value).then((res) => {
    //         if (!isEmpty(res.data)) {
    //           setScheduleSelect((prev) => [res.data, ...prev]);
    //         }
    //       });
    //     }
    //   }
    // }, [value, schedulesSelect, loadingScheduleSelect]);

    const options = useMemo(() => {
      let data = [...schedulesSelect];
      if (initOptionItem) {
        if ((initOptionItem as Schedule[])?.length) {
          data = data.concat(initOptionItem);
        } else {
          data.push(initOptionItem as Schedule);
        }
      }

      return uniqBy([...addonOptions, ...data], (data) => data.id);
    }, [schedulesSelect, initOptionItem]);

    return (
      <Select<any, DefaultOptionType>
        mode={multiple ? "multiple" : undefined}
        disabled={disabled}
        loading={loadingScheduleSelect}
        style={{ width: "100%" }}
        allowClear={allowClear}
        onSearch={debounceSearch}
        showSearch
        placeholder={placeholder}
        filterOption={false}
        value={value}
        onChange={(v, opts) => {
          if (v === undefined || v.length == 0) {
            debounceSearch(v);
          }
          if (valueIsOption) {
            if (opts instanceof Array) {
              onChange?.(opts?.map((v) => v.item));
            } else {
              onChange?.(opts?.item);
            }
          } else {
            onChange?.(v);
          }
        }}
        {...selectProps}
      >
        {options?.map((item) => (
          <Select.Option item={item} value={item.id} key={item.id}>
            <div className="flex items-center gap-2">
              <span>{item.TaskName}</span>
            </div>
          </Select.Option>
        ))}
      </Select>
    );
  }
);
