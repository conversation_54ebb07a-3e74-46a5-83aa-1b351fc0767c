import { permissionApi } from "api/permission.api";
import { useRef, useState } from "react";
import { Permission } from "types/permission";
import { QueryParam } from "types/query";

export interface PermissionQuery extends QueryParam {}

interface UsePermissionProps {
  initQuery: PermissionQuery;
}

const checkIsChildren = (permission: Permission, parent: string) => {
  return permission?.name.split("/")?.[1] == parent;
};

export const usePermission = ({ initQuery }: UsePermissionProps) => {
  const [data, setData] = useState<Permission[]>([]);
  const [total, setTotal] = useState(0);
  // const [query, setQuery] = useState<PermissionQuery>(initQuery);
  const query = useRef<PermissionQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await permissionApi.findAll(query.current);

      setData(data.permissions);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return { permissions: data, total, fetchData, loading, query: query.current };
};
