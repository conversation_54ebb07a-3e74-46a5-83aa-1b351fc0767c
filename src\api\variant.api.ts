import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const variantApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/variant",
      params,
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/variant/${id}`,
      method: "get",
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/variant",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/variant/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/variant/${id}`,
      method: "delete",
    }),
  deleteMany: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/variant/batch`,
      method: "delete",
      data,
    }),
  deleteAll: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/variant/all`,
      method: "delete",
      data,
    }),
  import: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/variant/import`,
      method: "post",
      data,
    }),
};
