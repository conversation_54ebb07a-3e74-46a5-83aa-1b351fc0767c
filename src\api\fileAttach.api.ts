import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const fileAttachApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/fileAttach?path=/",
      params,
    }),
  findByPath: (path: string, params?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/fileAttach?path=${path}`,
      params,
    }),
  findById: (id: number, params?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/fileAttach/${id}`,
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/fileAttach",
      data,
      method: "post",
    }),
  createMany: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/fileAttach/batch",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/fileAttach/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/fileAttach/${id}`,
      method: "delete",
    }),
  getFileUpload: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/fileAttach/upload",
      data,
      method: "post",
    }),
  upload: (file: File): AxiosPromise<any> => {
    const data = new FormData();
    data.append("file", file);

    return request({
      url: "/v1/admin/fileAttach/upload",
      data,
      method: "post",
    });
  },
};
