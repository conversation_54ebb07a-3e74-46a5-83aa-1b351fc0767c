import { materialTypeApi } from "api/materialType";

import { useState } from "react";
import { MaterialType } from "types/materialType";

import { QueryParam } from "types/query";

export interface MaterialTypeQuery extends QueryParam {}

interface UseMaterialTypeProps {
  initQuery: MaterialTypeQuery;
}

export const useMaterialType = ({ initQuery }: UseMaterialTypeProps) => {
  const [data, setData] = useState<MaterialType[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<MaterialTypeQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await materialTypeApi.findAll(query);

      setData(data.materialTypes);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return { materialTypes: data, total, fetchData, loading, setQuery, query };
};
