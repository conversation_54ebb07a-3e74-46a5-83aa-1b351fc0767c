import { FileAttach, FileAttachType } from "types/fileAttach";
import { fileAttachApi } from "api/fileAttach.api";
import { useState } from "react";
import { QueryParam } from "types/query";
import { $url } from "utils/url";
import dayjs from "dayjs";
import { message } from "antd";
import { appStore } from "store/appStore";
import { formatDate } from "utils/date";

export interface FileAttachQuery extends QueryParam {
  path?: string;
}

interface UseFileAttachProps {
  initQuery: FileAttachQuery;
}

interface UploadResponse {
  data: {
    fieldname: string;
    originalname: string;
    encoding: string;
    mimetype: string;
    destination: string;
    filename: string;
    path: string;
    size: number;
  };
  message: string;
  status: boolean;
}

interface CreateFileAttachPayload {
  projectId: number;
  fileAttachCategoryId: number;
  fileAttach: {
    name: string;
    type: string;
    url: string;
    path: string;
    size: number;
    desc?: string;
    isActive: boolean;
    mimetype: string;
    uid: string;
    date?: string;
    address?: string;
    tag?: string;
  };
}

// New interface for update operations
interface UpdateFileAttachPayload {
  projectId?: number;
  fileAttachCategoryId?: number;
  fileAttach?: Partial<CreateFileAttachPayload['fileAttach']>;
}

export const useFileAttach = ({ initQuery }: UseFileAttachProps) => {
  const [data, setData] = useState<FileAttach[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<FileAttachQuery>(initQuery);
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);

  const fetchData = async (customPath?: string): Promise<FileAttach[]> => {
    setLoading(true);
    try {
      const pathToUse = customPath || query.path || "/";

      // Remove path from query params to avoid conflict
      const { path, ...queryParams } = query;

      const { data } = await fileAttachApi.findByPath(pathToUse, queryParams);
      const fileAttaches = data.fileAttaches || [];
      setData(fileAttaches);
      setTotal(data.total || 0);
      return fileAttaches;
    } catch (error) {
      console.error("Error fetching file attaches:", error);
      setData([]);
      setTotal(0);
      return [];
    } finally {
      setLoading(false);
    }
  };

  // Upload file to server
  const uploadFile = async (file: File): Promise<any | null> => {
    try {
      setUploading(true);
      const response = await fileAttachApi.upload(file);
      return response;
    } catch (error) {
      console.error("Error uploading file:", error);
      message.error("Lỗi khi tải file lên server");
      return null;
    } finally {
      setUploading(false);
    }
  };

  // Create file attachment record
  const createFileAttach = async (
    payload: CreateFileAttachPayload
  ): Promise<FileAttach | null> => {
    try {
      const response = await fileAttachApi.create(payload);
      return response.data;
    } catch (error) {
      console.error("Error creating file attachment:", error);
      message.error("Lỗi khi tạo bản ghi file");
      return null;
    }
  };

  // Upload and create file attachment in one go
  const uploadAndCreateFileAttach = async (
    file: File,
    additionalData: {
      projectId: number;
      fileAttachCategoryId?: number;
      captureDate?: string;
      location?: string;
      tags?: string;
      type?: FileAttachType;
      path?: string;
      description?: string;
    }
  ): Promise<FileAttach | null> => {
    try {
      setUploading(true);

      console.log("Uploading file:", file);
      console.log("Additional data:", additionalData);

      // Step 1: Upload file
      const uploadResponse = await uploadFile(file);
      if (!uploadResponse) {
        return null;
      }

      // Step 2: Create file attachment record
      const createPayload: CreateFileAttachPayload = {
        projectId: additionalData.projectId,
        fileAttachCategoryId: additionalData.fileAttachCategoryId || 0,
        fileAttach: {
          name: uploadResponse.data.originalname,
          type: additionalData.type || FileAttachType.Image,
          url: $url(uploadResponse.data.path),
          path: additionalData.path || "/",
          size: uploadResponse.data.size,
          desc: additionalData.description || "",
          isActive: true,
          mimetype: uploadResponse.data.mimetype,
          uid: uploadResponse.data.filename, // Use server-generated filename as uid
          date: additionalData.captureDate,
          address: additionalData.location,
          tag: additionalData.tags,
        },
      };

      const fileAttach = await createFileAttach(createPayload);
      if (fileAttach) {
        fetchData();
      }

      return fileAttach;
    } catch (error) {
      console.error("Error in uploadAndCreateFileAttach:", error);
      message.error("Lỗi khi tải file");
      return null;
    } finally {
      setUploading(false);
    }
  };

  // Update file attachment
  const updateFileAttach = async (
    id: number,
    updateData: {
      projectId?: number;
      fileAttachCategoryId?: number;
      captureDate?: string;
      location?: string;
      tags?: string;
      description?: string;
      // Add new fields for file info
      name?: string;
      type?: FileAttachType;
      url?: string;
      path?: string;
      size?: number;
      mimetype?: string;
      uid?: string;
      isActive?: boolean;
    }
  ): Promise<FileAttach | null> => {
    console.log("🚀 ~ useFileAttach ~ updateData:", updateData)
    try {
      setLoading(true);

      // Build update payload with complete structure
      const updatePayload: UpdateFileAttachPayload = {
        projectId: updateData.projectId,
        fileAttachCategoryId: updateData.fileAttachCategoryId,
      };

      // // Only include fileAttach if we have any file-related updates
      // const fileAttachUpdates: Partial<CreateFileAttachPayload['fileAttach']> = {};
      
      // if (updateData.name) fileAttachUpdates.name = updateData.name;
      // if (updateData.type) fileAttachUpdates.type = updateData.type;
      // if (updateData.url) fileAttachUpdates.url = updateData.url;
      // if (updateData.path) fileAttachUpdates.path = updateData.path;
      // if (updateData.size) fileAttachUpdates.size = updateData.size;
      // if (updateData.description) fileAttachUpdates.desc = updateData.description;
      // if (updateData.mimetype) fileAttachUpdates.mimetype = updateData.mimetype;
      // if (updateData.uid) fileAttachUpdates.uid = updateData.uid;
      // if (updateData.captureDate) fileAttachUpdates.date = updateData.captureDate;
      // if (updateData.location) fileAttachUpdates.address = updateData.location;
      // if (updateData.tags) fileAttachUpdates.tag = updateData.tags;
      
      // // Only set isActive if explicitly provided
      // if (typeof updateData.isActive === 'boolean') {
      //   fileAttachUpdates.isActive = updateData.isActive;
      // }

      // // Only include fileAttach in payload if we have updates
      // if (Object.keys(fileAttachUpdates).length > 0) {
      //   updatePayload.fileAttach = fileAttachUpdates;
      // }

      // console.log("🚀 ~ useFileAttach ~ updatePayload:", updatePayload)
      const response = await fileAttachApi.update(id, updateData);
      message.success("Cập nhật file thành công!");
      fetchData(); // Refresh data
      return response.data;
    } catch (error) {
      console.error("Error updating file attachment:", error);
      message.error("Lỗi khi cập nhật file");
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Delete file attachment
  const deleteFileAttach = async (id: number): Promise<boolean> => {
    try {
      setLoading(true);
      await fileAttachApi.delete(id);
      message.success("Xóa file thành công!");
      fetchData(); // Refresh data
      return true;
    } catch (error) {
      console.error("Error deleting file attachment:", error);
      message.error("Lỗi khi xóa file");
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Transform API data to card format
  const transformFileAttachToCard = (item: FileAttach) => {
    const isFolder = item.type === FileAttachType.Folder;
    const isImage = item.type === FileAttachType.Image;

    return {
      id: item.id.toString(),
      folderName: item.name || "",
      imageSrc: isImage
        ? item.url
        : isFolder && item.totalImage > 0 && item.thumbnail
        ? item.thumbnail
        : "",
      location: item.address || "",
      date: formatDate(item.createdAt),
      categoryLabel: item.tag || "",
      projectName: appStore.currentProject?.name || "",
      type: isFolder ? FileAttachType.Folder : FileAttachType.Image,
      totalImage: isFolder ? item.totalImage || 0 : 0,
    };
  };

  // Get transformed data based on current view mode
  const getTransformedData = (currentFolder: string | null) => {
    if (!data || data.length === 0) return [];

    if (currentFolder) {
      // Inside folder view - show non-folder items
      return data
        .filter((item: FileAttach) => item.type !== FileAttachType.Folder)
        .map((item: FileAttach) => transformFileAttachToCard(item));
    } else {
      // Main folder view - show only folders
      return data
        .filter(
          (item: FileAttach) =>
            item.path === "/" && item.type === FileAttachType.Folder
        )
        .map((item: FileAttach) => transformFileAttachToCard(item));
    }
  };

  return {
    fileAttaches: data,
    total,
    fetchData,
    loading,
    uploading,
    setQuery,
    query,
    getTransformedData,
    transformFileAttachToCard,
    // Upload functions
    uploadFile,
    createFileAttach,
    uploadAndCreateFileAttach,
    updateFileAttach,
    deleteFileAttach,
  };
};

export const uploadFileAttach = async (fileList: FileAttach[]) => {
  const fileAttachIds: number[] = [];

  for (const file of fileList) {
    if (file.id) {
      fileAttachIds.push(file.id);
    } else if (file.originFile) {
      const { data } = await fileAttachApi.upload(file.originFile);

      const resFileAttach = await fileAttachApi.create({
        fileAttach: {
          ...file,
          url: $url(data.path),
        },
      });

      fileAttachIds.push(resFileAttach.data.id);
    }
  }

  return fileAttachIds;
};
