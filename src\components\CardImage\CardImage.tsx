import clsx from "clsx";
import { settings } from "settings";
import locationIcon from "assets/svgs/location.svg";
import { Dropdown, Menu, MenuProps } from "antd";
import {
  MoreOutlined,
  FolderFilled,
  FileImageOutlined,
} from "@ant-design/icons";
import UploadImageIcon from "assets/svgs/UploadImageIcon";
import EmptyIcon from "assets/svgs/EmptyIcon";
import "./CardImage.scss";
import FolderIcon from "assets/svgs/FolderIcon";
import CalendarIcon from "assets/svgs/CalendarIcon";
import DisplayImage from "views/ImagePage/components/DisplayImage";
import { FileAttachType } from "types/fileAttach";
import { eventPropTypes } from "@tinymce/tinymce-react/lib/cjs/main/ts/components/EditorPropTypes";
import LocationIcon from "assets/svgs/LocationIcon";

interface Props {
  className?: string;
  imageSrc?: string;
  folderName?: string;
  projectName?: string;
  location?: string;
  date?: string;
  imageCount?: number;
  showImageCount?: boolean;
  categoryLabel?: string;
  type?: FileAttachType;
  totalImage?: number;
  onMenuClick?: (key: string, type: FileAttachType) => void;
  onClick?: () => void;
}

const CardImage = ({
  className,
  imageSrc,
  folderName = "Thư mục",
  projectName = "<Tên dự án>",
  location = "<Địa điểm>",
  date = "<Ngày>",
  categoryLabel = "<Báo cáo tiến độ>",
  type = FileAttachType.Folder,
  totalImage = 0,
  onMenuClick,
  onClick,
}: Props) => {
  const menuItems = type !== FileAttachType.Folder ? [
    {
      key: "edit",
      label: "Chỉnh sửa",
    },
    {
      key: "delete",
      label: "Xóa",
    },
  ] : [
    {
      key: "delete",
      label: "Xoá",
    },
  ];

  const handleMenuClick: MenuProps['onClick'] = (e) => {
    e.domEvent.stopPropagation(); // Stop event from bubbling up
    if (onMenuClick) {
      onMenuClick(e.key, type);
    }
  };

  const handleDropdownClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Stop click from reaching the card
  };

  // const images = [
  //   { id: "1", src: "https://images.pexels.com/photos/93400/pexels-photo-93400.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", alt: "Công trình 1" },
  //   { id: "2", src: "https://images.pexels.com/photos/93400/pexels-photo-93400.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", alt: "Công trình 2" },
  //   { id: "3", src: "https://images.pexels.com/photos/93400/pexels-photo-93400.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", alt: "Công trình 3" },
  //   { id: "4", src: "https://images.pexels.com/photos/93400/pexels-photo-93400.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", alt: "Công trình 4" },
  //   { id: "5", src: "https://images.pexels.com/photos/93400/pexels-photo-93400.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", alt: "Công trình 5" },
  // ];

  const images = [
    {
      id: "1",
      src: imageSrc || settings.logo,
      alt: categoryLabel,
    },
  ];

  return (
    <div className={clsx("card-image", className)} onClick={onClick}>
      <div className="card-image__image-container">
        {type === FileAttachType.Folder && totalImage === 0 ? (
          <div className="card-image__empty-state">
            <EmptyIcon width={50} height={50} />
            <span className="card-image__empty-state-text">Thư mục trống</span>
          </div>
        ) : type === FileAttachType.Image && totalImage > 1 ? (
          <div className="card-image__multiple-images">
            <img src={imageSrc || settings.logo} className="card-image__image" />
            {/* <div className="card-image__multiple-images-overlay">
              <div className="card-image__multiple-images-indicators">
                <div className="card-image__multiple-images-indicators-dot card-image__multiple-images-indicators-dot--opacity-60"></div>
                <div className="card-image__multiple-images-indicators-dot card-image__multiple-images-indicators-dot--opacity-80"></div>
                <div className="card-image__multiple-images-indicators-dot card-image__multiple-images-indicators-dot--opacity-100"></div>
              </div>
            </div> */}
          </div>
        ) : (
          <img src={imageSrc || settings.logo} className="card-image__image" />
        )}

        {type === FileAttachType.Image && categoryLabel && (
          <div className="card-image__category-label">
            <div
              className="card-image__category-label-background"
              style={{ backgroundColor: "var(--color-logo)" }}
            ></div>
            <span
              className="card-image__category-label-text"
              style={{ color: "var(--color-neutral-n0)" }}
            >
              {categoryLabel}
            </span>
          </div>
        )}

        {/* {type === FileAttachType.Folder && totalImage > 1 && (
          <div className="card-image__count-overlay">+{totalImage - 1}</div>
        )} */}
      </div>

      {/* <div className="card-image__image-container">
        <DisplayImage images={images} />
      </div> */}

      <div className="card-image__content">
        <div className="card-image__header">
          <div className="card-image__title">
            {type === FileAttachType.Folder ? (
              <FolderIcon />
            ) : (
              <UploadImageIcon width={20} height={20} />
            )}
            <span className="card-image__title-text">{folderName}</span>
          </div>

          <Dropdown
            menu={{ items: menuItems, onClick: handleMenuClick }}
            trigger={["click"]}
            placement="bottomRight"
          >
            <div className="card-image__menu" onClick={handleDropdownClick}>
              <MoreOutlined className="card-image__menu-icon" />
            </div>
          </Dropdown>
        </div>

        {/* Image Count - only for folders */}
        {type === FileAttachType.Folder && (
          <div className="card-image__info">
            <UploadImageIcon width={16} height={16} />
            <span>{totalImage} hình ảnh</span>
          </div>
        )}
        {/* Location  - only for images */}
        {type !== FileAttachType.Folder && (
          <div className="card-image__info">
            <LocationIcon />
            <span>{location}</span>
          </div>
        )}

        {/* Date */}
        <div className="card-image__info">
          <CalendarIcon />
          <span>{date}</span>
        </div>
      </div>
    </div>
  );
};

export default CardImage;
