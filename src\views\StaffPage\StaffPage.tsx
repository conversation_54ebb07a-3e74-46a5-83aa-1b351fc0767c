import {
  StopOutlined,
  LockOutlined,
  UnlockOutlined,
  ImportOutlined,
} from "@ant-design/icons";
import {
  Button,
  Card,
  Divider,
  Modal,
  Select,
  Space,
  Table,
  Tag,
  Tooltip,
  message,
} from "antd";
import { staffApi } from "api/staff.api";
import { Pagination } from "components/Pagination";
import { useStaff } from "hooks/useStaff";
import { observer } from "mobx-react-lite";
import { useEffect, useMemo, useState, useRef, useCallback } from "react";
import { QueryParam } from "types/query";
import {
  CustomerShiftTrans,
  GenderTrans,
  MaritalStatusTrans,
  Staff,
  StaffType,
} from "types/staff";
import { getTitle } from "utils";
import { MyExcelColumn, handleExport } from "utils/MyExcel";
import {
  checkRole,
  checkRoles,
  filterActionColumnIfNoPermission,
} from "utils/auth";
import { unixToDate, unixToFullDate } from "utils/dateFormat";
// import { AreaSelector } from "components/AreaSelector/AreaSelector";
import { settings } from "settings";
import { Link, useNavigate } from "react-router-dom";
import { PermissionNames } from "types/PermissionNames";
import { permissionStore } from "store/permissionStore";
import PageTitle from "components/PageTitle/PageTitle";
import CustomButton from "components/Button/CustomButton";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import CustomInput from "components/Input/CustomInput";
import PencilIcon from "assets/svgs/PencilIcon";
import DeleteIcon from "assets/svgs/DeleteIcon";
import { useDepartment } from "hooks/useDepartment";
import dayjs from "dayjs";
import { WorkStatusTrans } from "types/workStatus";
import { DictionarySelector } from "components/Selector/DictionarySelector";
import { DictionaryType } from "types/dictionary";
import { StaffStatusSelector } from "components/Selector/StaffStatusSelector";
import { useTransition } from "hooks/useTransition";
import ImportStaff, {
  ImportStaffModal,
} from "components/ImportDocument/ImportStaff";
import { removeSubstringFromKeys } from "utils/common";
import { TableProps } from "antd/lib";
import ExcelJS from "exceljs";
import { saveAs } from "file-saver";
import { dictionaryApi } from "api/dictionary.api";
import { Provider } from "types/provider";
import { addNewSheet } from "utils/excel";
import {
  getListNameByApi,
  getListNameByTypeDictionary,
} from "hooks/useDictionary";
import { exportTemplateWithValidation } from "utils/TemplateExcel";
import { roleApi } from "api/role.api";
import { getListByKey } from "utils/data";
import { Role } from "types/role";
import { excelDateToDayjs } from "utils/date";
import QueryLabel from "components/QueryLabel/QueryLabel";
import LockButton from "components/Button/LockButton";
import EditButton from "components/Button/EditButton";
import { accountApi } from "api/account.api";
import { CompanySelector } from "components/Selector/CompanySelector";
import { BMDImage } from "components/Image/BMDImage";
import { $url } from "utils/url";
import logoImage from "assets/images/logo.png";

const { Column } = Table;

const exportColumns: MyExcelColumn<Staff>[] = [
  {
    header: "Ngày tạo",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "createdDateTime",
    columnKey: "createdDateTime",
    render: (record) => unixToFullDate(record.createdAt),
  },
  {
    header: "Mã NV",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "code",
    columnKey: "code",
    render: (record) => record.code,
  },
  {
    header: "Họ tên",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "name",
    columnKey: "name",
    render: (record) => record.fullName,
  },
  // {
  //   header: "Username",
  //   headingStyle: {
  //     font: {
  //       bold: true,
  //     },
  //   },
  //   key: "username",
  //   columnKey: "username",
  // },
  {
    header: "Email",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "email",
    columnKey: "email",
    render: (record) => record.email,
  },
  {
    header: "Số điện thoại",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "phone",
    columnKey: "phone",
    render: (record) => record.phone,
  },
  {
    header: "Giới tính",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "gender",
    columnKey: "gender",
    render: (record: Staff) => GenderTrans[record.gender]?.label,
  },
  {
    header: "Ngày sinh",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "dob",
    columnKey: "dob",
    render: (record) =>
      record.dob ? dayjs(record.dob).format(settings.dateFormat) : "",
  },
  {
    header: "CCCD/Hộ chiếu",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "identificationCard",
    columnKey: "identificationCard",
    render: (record) => record.identificationCard,
  },
  {
    header: "Ngày cấp CCCD",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "identificationDate",
    columnKey: "identificationDate",
    render: (record) =>
      record.identificationDate
        ? dayjs(record.identificationDate).format(settings.dateFormat)
        : "",
  },
  {
    header: "Nơi cấp CCCD",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "identificationPlace",
    columnKey: "identificationPlace",
    render: (record) => record.identificationPlace,
  },
  {
    header: "Địa chỉ thường trú",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "permanentAddress",
    columnKey: "permanentAddress",
    render: (record) => record.permanentAddress,
  },
  {
    header: "Địa chỉ tạm trú",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "temporaryAddress",
    columnKey: "temporaryAddress",
    render: (record) => record.temporaryAddress,
  },
  {
    header: "Tình trạng hôn nhân",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "maritalStatus",
    columnKey: "maritalStatus",
    render: (record: Staff) => MaritalStatusTrans[record.maritalStatus]?.label,
  },
  {
    header: "Phòng ban",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "department",
    columnKey: "department",
    render: (record) => record.department?.name,
  },
  {
    header: "Chức vụ",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "jobTitle",
    columnKey: "jobTitle",
    render: (record) => record.jobTitle?.name,
  },
  {
    header: "Cấp bậc",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "level",
    columnKey: "level",
    render: (record) => record.level?.name,
  },
  {
    header: "Mã số thuế cá nhân",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "personalTax",
    columnKey: "personalTax",
    render: (record) => record.personalTax,
  },
  {
    header: "Hợp đồng lao động",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "contractType",
    columnKey: "contractType",
    render: (record) => record.contractType,
  },
  {
    header: "Ngày vào làm",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "startAt",
    columnKey: "startAt",
    render: (record) => (record.startAt > 0 ? unixToDate(record.startAt) : ""),
  },
  {
    header: "Ngày chính thức",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "officialStartDate",
    columnKey: "officialStartDate",
    render: (record) =>
      record.officialStartDate
        ? dayjs(record.officialStartDate).format(settings.dateFormat)
        : "",
  },
  {
    header: "Tình trạng làm việc",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "workStatus",
    columnKey: "workStatus",
    render: (record: Staff) => WorkStatusTrans[record.workStatus]?.label,
  },
  {
    header: "Mã chấm công",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "timekeepingCode",
    columnKey: "timekeepingCode",
    render: (record) => record.timekeepingCode,
  },
  {
    header: "Ca làm việc",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "shift",
    columnKey: "shift",
    render: (record: Staff) => CustomerShiftTrans[record.shift]?.label,
  },
  {
    header: "Địa chỉ làm việc",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "workAddress",
    columnKey: "workAddress",
    render: (record) => record.workAddress,
  },
  {
    header: "Mức lương cơ bản",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "baseSalary",
    columnKey: "baseSalary",
    style: { numFmt: "###,##" },

    render: (record) => record.baseSalary,
  },
  {
    header: "Phụ cấp",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "allowances",
    columnKey: "allowances",
    style: { numFmt: "###,##" },

    render: (record) => record.allowances,
  },
  {
    header: "Tài khoản ngân hàng",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "bankAccount",
    columnKey: "bankAccount",
    render: (record) => record.bankAccount,
  },
  {
    header: "Trình độ học vấn",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "educationLevel",
    columnKey: "educationLevel",
    render: (record) => record.educationLevel,
  },
  {
    header: "Bằng cấp chuyên môn",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "professionalQualification",
    columnKey: "professionalQualification",
    render: (record) => record.professionalQualification,
  },
  {
    header: "Kỹ năng nghề nghiệp",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "professionalSkills",
    columnKey: "professionalSkills",
    render: (record) => record.professionalSkills,
  },
  {
    header: "Người liên hệ khẩn cấp",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "contactPerson",
    columnKey: "contactPerson",
    render: (record) => record.contactPerson,
  },
  {
    header: "Ghi chú",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "notes",
    columnKey: "notes",
    render: (record) => record.notes,
  },
  // {
  //   header: "Chức vụ",
  //   headingStyle: {
  //     font: {
  //       bold: true,
  //     },
  //   },
  //   key: "position",
  //   columnKey: "position",
  //   render: (record) => record.position,
  // },
  {
    header: "Trạng thái",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "status",
    columnKey: "status",
    render: (record: Staff) =>
      WorkStatusTrans[record.workStatus]?.label || "Không xác định",
  },
];

const exportStaffOtherColumn: MyExcelColumn<Staff>[] = [
  {
    header: "Ngày tạo",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "createdDateTime",
    columnKey: "createdDateTime",
    render: (record) => unixToFullDate(record.createdAt),
  },
  {
    header: "Mã đối tượng",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "code",
    columnKey: "code",
    render: (record) => record.code,
  },
  {
    header: "Họ tên",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "name",
    columnKey: "name",
    render: (record) => record.fullName,
  },
  {
    header: "Email",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "email",
    columnKey: "email",
    render: (record) => record.email,
  },
  {
    header: "Số điện thoại",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "phone",
    columnKey: "phone",
    render: (record) => record.phone,
  },
  {
    header: "Chức vụ",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "jobTitle",
    columnKey: "jobTitle",
    render: (record) => record.jobTitle?.name,
  },
  {
    header: "Công ty",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "company",
    columnKey: "company",
    render: (record) => record.company?.name,
  },
  {
    header: "Trạng thái",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "isBlocked",
    columnKey: "isBlocked",
    render: (record: Staff) => (record.isBlocked ? "Bị khóa" : "Hoạt động"),
  },
];

export const StaffPage = observer(
  ({
    title = "",
    initQuery,
    companyType = undefined,
  }: {
    title?: string;
    initQuery?: QueryParam & {
      storeId: number;
    };
    companyType?: string;
  }) => {
    const isStaffOther = companyType === StaffType.Other;
    const {
      haveAddPermission,
      haveBlockPermission,
      haveEditPermission,
      haveViewAllPermission,
    } = checkRoles(
      {
        add: isStaffOther
          ? PermissionNames.staffOtherAdd
          : PermissionNames.staffAdd,
        edit: isStaffOther
          ? PermissionNames.staffOtherEdit
          : PermissionNames.staffEdit,
        block: isStaffOther
          ? PermissionNames.staffOtherBlock
          : PermissionNames.staffBlock,
        viewAll: isStaffOther
          ? PermissionNames.staffOtherViewAll
          : PermissionNames.staffViewAll,
      },
      permissionStore.permissions
    );

    const [loadingDelete, setLoadingDelete] = useState(false);
    const { isLoaded } = useTransition();
    const importModal = useRef<ImportStaffModal>();
    const [loadingDownloadDemo, setLoadingDownloadDemo] = useState(false);

    const {
      fetchStaff,
      staffs,
      loading,
      deleteStaff,
      total,
      setQuery,
      query,
      isEmptyQuery,
    } = useStaff({
      initQuery: {
        limit: 10,
        page: 1,
        companyType,
        isAdmin: haveViewAllPermission ? true : undefined,
      },
    });

    const {
      fetchData: fetchDataDepartment,
      loading: loadingDepartment,
      departments,
    } = useDepartment({
      initQuery: {
        limit: 100,
        page: 1,
        isAdmin: haveViewAllPermission ? true : undefined,
      },
    });

    const navigate = useNavigate();

    useEffect(() => {
      document.title = getTitle(title);
    }, []);

    useEffect(() => {
      if (isLoaded) {
        fetchStaff();
        fetchDataDepartment();
      }
    }, [isLoaded]);

    const handleBlockStaff = async (id: number, value: boolean) => {
      try {
        setLoadingDelete(false);
        await staffApi.block(id, { isBlocked: !value });
        message.success(value ? "Mở khóa thành công" : "Khóa thành công");
        fetchStaff();
      } catch (error) {
      } finally {
        setLoadingDelete(true);
      }
    };

    const departmentOptions = useMemo(() => {
      return [
        {
          value: "",
          label: "Tất cả phòng ban",
        },
        ...departments.map((department) => ({
          value: department.id,
          label: department.name,
        })),
      ];
    }, [departments]);

    // Client-side filtering for status and department - removed to prevent auto-filtering
    const filteredStaffs = useMemo(() => {
      return staffs;
    }, [staffs]);

    const handleOnUploadedFile = async (excelData: any, setData: any) => {
      const { results } = excelData;

      console.log("results", results);

      const importData = results?.map((item: any) => {
        const refineRow = removeSubstringFromKeys(item, " *");

        const code = String(refineRow["Mã NV"] || "");
        const fullName = String(refineRow["Họ tên"] || "");
        // const username = String(refineRow["Username"] || "");
        // const password = String(refineRow["Mật khẩu"] || "");
        // const account = String(refineRow["Tài khoản"] || "");

        // const roleName = String(refineRow["Vai trò"] || "");
        const email = String(refineRow["Email"] || "");
        const phone = String(refineRow["Số điện thoại"] || "");
        const gender = String(refineRow["Giới tính"] || "");
        const dob = refineRow["Ngày sinh"]
          ? excelDateToDayjs(refineRow["Ngày sinh"])
          : "";

        const identificationCard = String(refineRow["CCCD/Hộ chiếu"] || "");
        const identificationDate = refineRow["Ngày cấp CCCD"]
          ? excelDateToDayjs(refineRow["Ngày cấp CCCD"])
          : "";
        const identificationPlace = String(refineRow["Nơi cấp CCCD"] || "");
        const permanentAddress = String(refineRow["Địa chỉ thường trú"] || "");
        const temporaryAddress = String(refineRow["Địa chỉ tạm trú"] || "");
        const maritalStatus = String(refineRow["Tình trạng hôn nhân"] || "");
        const departmentName = String(refineRow["Phòng ban"] || "");
        const jobTitleName = String(refineRow["Chức vụ"] || "");
        const levelName = String(refineRow["Cấp bậc"] || "");
        const personalTax = String(refineRow["Mã số thuế cá nhân"] || "");
        const contractType = String(refineRow["Hợp đồng lao động"] || "");
        const startDate = refineRow["Ngày vào làm"]
          ? excelDateToDayjs(refineRow["Ngày vào làm"])
          : "";
        const officialStartDate = refineRow["Ngày chính thức"]
          ? excelDateToDayjs(refineRow["Ngày chính thức"])
          : "";
        const workStatus = String(
          refineRow["Tình trạng làm việc"] || "Khởi tạo" // Mặc định là Khởi tạo
        );
        const timekeepingCode = String(refineRow["Mã chấm công"] || "");
        const shiftRaw = String(refineRow["Ca làm việc"] || "");
        const shift =
          Object.values(CustomerShiftTrans).find((it) => it.label == shiftRaw)
            ?.value || "";
        const workAddress = String(refineRow["Địa chỉ làm việc"] || "");

        // Parse number fields thành number
        const baseSalary = refineRow["Mức lương cơ bản"];
        const allowances = refineRow["Phụ cấp"];

        const bankAccount = String(refineRow["Số tài khoản ngân hàng"] || "");
        const bankName = String(refineRow["Tên ngân hàng"] || "");
        const educationLevel = String(refineRow["Trình độ học vấn"] || "");
        const professionalQualification = String(
          refineRow["Bằng cấp chuyên môn"] || ""
        );
        const professionalSkills = String(
          refineRow["Kỹ năng nghề nghiệp"] || ""
        );
        const contactPerson = String(refineRow["Người liên hệ khẩn cấp"] || "");
        const emergencyContactPhone = String(
          refineRow["Số điện thoại người liên hệ khẩn cấp"] || ""
        );
        const emergencyContactRelationship = String(
          refineRow["Mối quan hệ người liên hệ khẩn cấp"] || ""
        );
        const notes = String(refineRow["Ghi chú"] || "");

        return {
          code,
          // password,
          fullName,
          // username,
          // roleName,
          email,
          phone,
          gender,
          dob,
          identificationCard,
          identificationDate,
          identificationPlace,
          permanentAddress,
          temporaryAddress,
          maritalStatus,
          departmentName,
          jobTitleName,
          levelName,
          personalTax,
          contractType,
          startDate,
          officialStartDate,
          workStatus,
          timekeepingCode,
          shift,
          workAddress,
          baseSalary,
          allowances,
          bankAccount,
          bankName,
          educationLevel,
          professionalQualification,
          professionalSkills,
          contactPerson,
          emergencyContactPhone,
          emergencyContactRelationship,
          notes,
          // account,
          rowNum: item.__rowNum__,
        };
      });
      console.log("importData", importData);

      setData(importData);
    };

    const handleDownloadDemoExcel = async () => {
      try {
        setLoadingDownloadDemo(true);
        const [
          departmentNames,
          jobTitleNames,
          levelNames,
          // roleNames,
          // accountNames,
        ] = await Promise.all([
          getListNameByTypeDictionary(DictionaryType.Department), // lay ds phong ban
          getListNameByTypeDictionary(DictionaryType.JobTitle), // lay ds chuc danh
          getListNameByTypeDictionary(DictionaryType.Level), // lay ds cap bac
          // getListNameByApi({ api: roleApi.findAll, dataKey: "roles" }), // lay ds role
          // getListNameByApi({ api: accountApi.findAll, dataKey: "accounts" }), // lay ds account
        ]);

        const customerShifts = Object.values(CustomerShiftTrans)
          .map((item) => item.label)
          .join(",");

        const result = await exportTemplateWithValidation({
          templatePath: "/exportFile/file_mau_nhap_staff.xlsx",
          outputFileName: "file_mau_nhap_staff.xlsx",
          sheetsToAdd: [
            { name: "Department", data: departmentNames },
            { name: "JobTitle", data: jobTitleNames },
            { name: "Level", data: levelNames },
            // { name: "Role", data: roleNames },
            // { name: "Account", data: accountNames },
          ],
          validations: [
            {
              headerName: "Phòng ban",
              type: "list",
              formulae: [`'Department'!$A$1:$A$${departmentNames.length}`],
              error: "Vui lòng chọn một giá trị từ danh sách.",
            },
            {
              headerName: "Chức vụ",
              type: "list",
              formulae: [`'JobTitle'!$A$1:$A$${jobTitleNames.length}`],
              error: "Vui lòng chọn một giá trị từ danh sách.",
            },
            {
              headerName: "Cấp bậc",
              type: "list",
              formulae: [`'Level'!$A$1:$A$${levelNames.length}`],
              error: "Vui lòng chọn một giá trị từ danh sách.",
            },
            {
              headerName: "Tình trạng làm việc",
              type: "list",
              formulae: [`'WorkStatus'!$A$1:$A$7`],
              error: "Vui lòng chọn một giá trị từ danh sách.",
            },
            {
              headerName: "Tình trạng hôn nhân",
              type: "list",
              formulae: [`'Marial'!$A$1:$A$2`],
              error: "Vui lòng chọn một giá trị từ danh sách.",
            },
            {
              headerName: "Giới tính",
              type: "list",
              formulae: [`'Gender'!$A$1:$A$2`],
              error: "Vui lòng chọn một giá trị từ danh sách.",
            },
            {
              headerName: "Ca làm việc",
              type: "list",
              formulae: [`"${customerShifts}"`],
              error: "Vui lòng chọn một giá trị từ danh sách.",
            },
          ],
        });
      } catch (error) {
        message.error(
          `Có lỗi xảy ra: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      } finally {
        setLoadingDownloadDemo(false);
      }
    };

    const handleTableChange: TableProps<any>["onChange"] = (
      pagination,
      filters,
      sorter
    ) => {
      if (!Array.isArray(sorter)) {
        const fieldMap: Record<string, string> = {
          fullName: "staff.fullName",

          code: "staff.code",
          phone: "staff.phone",
          email: "staff.email",
          department: "department.name",
          jobTitle: "jobTitle.name",
          level: "level.name",
          company: "company.name",
        };
        const columnKey = sorter.field || sorter.column?.key;

        if (!sorter.order) {
          // setSortField(null);
          // setSortOrder(null);
          query.queryObject = undefined;
          setQuery({ ...query });
        } else {
          const order = sorter.order === "ascend" ? "ASC" : "DESC";
          // setSortField("jobCategory.name");
          // setSortOrder(order);
          const field = fieldMap[columnKey as string];

          const newQueryObject = JSON.stringify([
            {
              type: "sort",
              field,
              value: order,
            },
          ]);
          query.queryObject = newQueryObject;
          setQuery({ ...query });
        }
        fetchStaff();
      } else {
        query.queryObject = undefined;
        setQuery({ ...query });
        fetchStaff();
      }
    };

    const handleRowClick = (record: Staff) => {
      if (isStaffOther) {
        navigate(
          `/master-data/${PermissionNames.staffOtherEdit.replace(
            ":id",
            record!.id + ""
          )}`
        );
      } else {
        navigate(
          `/master-data/${PermissionNames.staffEdit.replace(
            ":id",
            record!.id + ""
          )}`
        );
      }
    };

    const columns: CustomizableColumn<Staff>[] = [
      {
        title: "Mã",
        dataIndex: "code",
        key: "code",
        width: 70,
        sorter: true,

        render: (_, record) => (
          <div
            className="text-[#1677ff] cursor-pointer"
            onClick={() => handleRowClick(record)}
          >
            {record.code}
          </div>

          // <Link
          //   to={`/master-data/${PermissionNames.staffEdit.replace(
          //     ":id",
          //     record.id + ""
          //   )}`}
          // >
          //   {record.code}
          // </Link>
        ),
        defaultVisible: true,
      },
      {
        title: isStaffOther ? "Đối tượng" : "Nhân viên",
        dataIndex: "fullName",
        key: "fullName",
        sorter: true,
        width: 200,
        render: (_, record) => (
          <>
            <div className="flex items-center gap-2">
              <BMDImage
                src={
                  record.avatar && record.avatar.trim()
                    ? $url(record.avatar)
                    : logoImage
                }
                className="size-[34px] object-cover"
                width={34}
                height={34}
                fallback={logoImage}
              />
              {record.isBlocked ? (
                <div className="flex items-center gap-2">
                  <div>
                    <div className="font-medium text-red-500">
                      {record?.fullName}
                    </div>
                  </div>
                  <StopOutlined style={{ color: "red" }} />
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <div>
                    <div className="font-medium">{record?.fullName}</div>
                  </div>
                </div>
              )}
            </div>
          </>
        ),
        defaultVisible: true,
      },
      {
        title: "SĐT",
        dataIndex: "phone",
        key: "phone",
        width: 150,
        sorter: true,

        render: (_, record) => record.phone || "-",
        defaultVisible: true,
      },
      {
        title: "Email",
        dataIndex: "email",
        key: "email",
        width: 250,
        sorter: true,

        render: (_, record) => record.email || "-",
        defaultVisible: true,
      },
      {
        title: "Phòng ban",
        dataIndex: "department",
        key: "department",
        width: 180,
        sorter: true,

        render: (_, record) => record.department?.name || "-",
        defaultVisible: true,
        showColumn: !isStaffOther,
      },
      {
        title: "Chức vụ",
        dataIndex: "jobTitle",
        key: "jobTitle",
        width: 150,
        sorter: true,

        render: (_, record) => record.jobTitle?.name || "-",
        defaultVisible: true,
      },
      {
        title: "Cấp bậc",
        dataIndex: "level",
        key: "level",
        width: 120,
        sorter: true,

        render: (_, record) => record.level?.name || "-",
        defaultVisible: true,
        showColumn: !isStaffOther,
      },
      {
        title: "Công ty",
        dataIndex: "company",
        key: "company",
        width: 120,
        sorter: true,

        render: (_, record) => record.company?.name || "-",
        defaultVisible: true,
        showColumn: isStaffOther,
      },
      {
        title: "Trạng thái",
        dataIndex: "workStatus",
        key: "staff.workStatus",
        align: "center",
        width: 120,
        render: (_, record) =>
          isStaffOther ? (
            <div className="flex justify-center">
              <Tag
                className="status-tag !mr-0"
                color={record.isBlocked ? "red" : "green"}
              >
                {record.isBlocked ? "Bị khóa" : "Hoạt động"}
              </Tag>
            </div>
          ) : (
            <div className="flex justify-center">
              <Tag
                className="status-tag !mr-0"
                color={WorkStatusTrans[record.workStatus]?.color || "gray"}
              >
                {WorkStatusTrans[record.workStatus]?.label || "Không xác định"}
              </Tag>
            </div>
          ),
        defaultVisible: true,
      },
      {
        key: "actions",
        title: "Xử lý",
        width: 100,
        align: "center",
        fixed: "right",
        alwaysVisible: true,
        render: (_, record) => {
          const moduleName = isStaffOther ? "đối tượng" : "nhân viên";
          return (
            <Space size="small">
              {haveEditPermission && (
                <EditButton
                  onClick={(e) => {
                    e.stopPropagation();
                    if (isStaffOther) {
                      navigate(
                        `/master-data/${PermissionNames.staffOtherEdit.replace(
                          ":id",
                          record.id + ""
                        )}?update=1`
                      );
                    } else {
                      navigate(
                        `/master-data/${PermissionNames.staffEdit.replace(
                          ":id",
                          record.id + ""
                        )}?update=1`
                      );
                    }
                  }}
                />
              )}
              {haveBlockPermission && (
                <LockButton
                  isActive={!record.isBlocked}
                  onAccept={() => handleBlockStaff(record.id, record.isBlocked)}
                  modalTitle={`${
                    record.isBlocked ? "Mở khóa" : "Khóa"
                  } ${moduleName} ${record.fullName}`}
                  modalContent={
                    <>
                      <div>
                        {record.isBlocked
                          ? `Khi mở khóa ${moduleName} thì ${moduleName} ${record.fullName} sẽ được đăng nhập trở lại.`
                          : `Khi khóa ${moduleName} thì ${moduleName} ${record.fullName} sẽ không được đăng nhập nữa.`}
                      </div>
                      <div>
                        Bạn có chắc chắn muốn{" "}
                        {record.isBlocked ? "mở khóa" : "khóa"} {moduleName}{" "}
                        này?
                      </div>
                    </>
                  }
                />
              )}
            </Space>
          );
        },
      },
    ];

    return (
      <div className="staff-page-container app-container">
        <PageTitle
          title={title}
          breadcrumbs={["Dữ liệu nguồn", title]}
          extra={
            haveAddPermission && (
              <Space>
                <CustomButton
                  size="small"
                  showPlusIcon
                  onClick={() => {
                    if (isStaffOther) {
                      navigate(`/master-data/${PermissionNames.staffOtherAdd}`);
                    } else {
                      navigate(`/master-data/${PermissionNames.staffAdd}`);
                    }
                  }}
                >
                  {isStaffOther ? "Tạo đối tượng" : "Tạo nhân viên"}
                </CustomButton>
                {!isStaffOther && (
                  <CustomButton
                    size="small"
                    icon={<ImportOutlined />}
                    onClick={() => {
                      importModal.current?.open();
                    }}
                  >
                    Nhập excel
                  </CustomButton>
                )}
              </Space>
            )
          }
        />

        <Card>
          <div className="flex gap-[16px] items-end pb-[12px] justify-between">
            <div className="flex gap-[16px] items-end flex-wrap">
              <div className="w-[300px]">
                <CustomInput
                  tooltipContent={"Tìm theo mã, tên đối tượng"}
                  label="Tìm kiếm"
                  placeholder="Tìm kiếm"
                  value={query.search}
                  onChange={(value) => {
                    query.search = value;
                    setQuery({ ...query });

                    if (!value) {
                      fetchStaff({ ...query });
                    }
                  }}
                  onPressEnter={() => {
                    // Pass all parameters including filters to API
                    query.page = 1;
                    fetchStaff({ ...query });
                  }}
                  allowClear
                />
              </div>
              {!isStaffOther && (
                <>
                  <div>
                    <QueryLabel>Phòng ban</QueryLabel>
                    <DictionarySelector
                      label="Phòng ban"
                      value={query.departmentId ?? ""}
                      onChange={(value) => {
                        setQuery({ ...query, departmentId: value });
                      }}
                      initQuery={{ type: DictionaryType.Department }}
                      addonOptions={[
                        {
                          id: "",
                          name: "Tất cả phòng ban",
                        },
                      ]}
                    />
                  </div>
                  <div>
                    <QueryLabel>Trạng thái</QueryLabel>
                    <StaffStatusSelector
                      label="Trạng thái"
                      value={query.workStatus || ""}
                      onChange={(value) => {
                        setQuery({ ...query, workStatus: value });
                      }}
                      placeholder="Chọn trạng thái"
                      includeBlockedStatus={false}
                      includeAllOption={true}
                    />
                  </div>
                </>
              )}

              {isStaffOther && (
                <>
                  <div>
                    <QueryLabel>Công ty</QueryLabel>
                    <CompanySelector
                      value={query.companyId || ""}
                      onChange={(value) => {
                        setQuery({ ...query, companyId: value });
                      }}
                      placeholder="Chọn công ty"
                      addonOptions={[
                        {
                          id: "",
                          name: "Tất cả công ty",
                        },
                      ]}
                    />
                  </div>
                  <div>
                    <QueryLabel>Trạng thái</QueryLabel>
                    <Select
                      allowClear={true}
                      placeholder="Chọn trạng thái"
                      value={query.isBlocked ?? ""}
                      onChange={(value) => {
                        query.isBlocked = value || "";
                        setQuery({ ...query });
                      }}
                      style={{ minWidth: 150 }}
                      options={[
                        {
                          value: "",
                          label: "Tất cả trạng thái",
                        },
                        {
                          value: "false",
                          label: "Hoạt động",
                        },
                        {
                          value: "true",
                          label: "Bị khóa",
                        },
                      ]}
                    />
                  </div>
                </>
              )}

              <CustomButton
                onClick={() => {
                  // Pass all filter parameters to API
                  query.page = 1;
                  fetchStaff({ ...query });
                }}
              >
                Áp dụng
              </CustomButton>

              {!isEmptyQuery && (
                <CustomButton
                  variant="outline"
                  onClick={() => {
                    delete query.workStatus;
                    delete query.departmentId;
                    delete query.search;
                    delete query.companyId;
                    delete query.isBlocked;
                    setQuery({ ...query });
                    fetchStaff();
                  }}
                >
                  Bỏ lọc
                </CustomButton>
              )}
            </div>

            <CustomButton
              onClick={() => {
                Modal.confirm({
                  title: `Bạn có muốn xuất file excel?`,
                  getContainer: () => {
                    return document.getElementById("App") as HTMLElement;
                  },
                  icon: null,

                  footer: (_, { OkBtn, CancelBtn }) => (
                    <>
                      <CustomButton
                        variant="outline"
                        className="cta-button"
                        onClick={() => {
                          handleExport({
                            onProgress(percent) {
                              console.log("What is percent", percent);
                            },
                            exportColumns: isStaffOther
                              ? exportStaffOtherColumn
                              : exportColumns,
                            fileType: "xlsx",
                            dataField: "staffs",
                            query: query,
                            api: staffApi.findAll,
                            fileName: isStaffOther
                              ? "Danh sách đối tượng"
                              : "Danh sách nhân viên",
                            sheetName: isStaffOther
                              ? "Danh sách đối tượng"
                              : "Danh sách nhân viên",
                          });
                          Modal.destroyAll();
                        }}
                      >
                        Có
                      </CustomButton>
                      <CustomButton
                        onClick={() => {
                          Modal.destroyAll();
                        }}
                        className="cta-button"
                      >
                        Không
                      </CustomButton>
                    </>
                  ),
                });
              }}
            >
              Xuất excel
            </CustomButton>
          </div>
          <CustomizableTable
            columns={filterActionColumnIfNoPermission(columns, [
              haveEditPermission,
              haveBlockPermission,
            ])}
            dataSource={filteredStaffs}
            rowKey="id"
            loading={loading}
            pagination={false}
            scroll={{ x: 1200 }}
            bordered
            displayOptions
            className="staff-table-no-empty-hover"
            tableId="staff-page"
            autoAdjustColumnWidth={true}
            //@ts-ignore
            onChange={handleTableChange}
            onRowClick={handleRowClick}
          />

          <Pagination
            currentPage={query.page}
            defaultPageSize={query.limit}
            total={total}
            onChange={({ limit, page }) => {
              query.page = page;
              query.limit = limit;
              setQuery({ ...query });
              fetchStaff({ ...query });
            }}
          />

          {useMemo(
            () => (
              <ImportStaff
                guide={[
                  "Vui lòng tải và sử dụng file mẫu để import dữ liệu đúng cách",
                  "Không được thay đổi tiêu đề trong file csv/excel mẫu để tránh import thiếu dữ liệu",
                  "Chi tiết xem tại sheet Hướng dẫn của file import mẫu",
                  "Đảm bảo các trường bắt buộc như Họ tên, Email được điền đầy đủ",
                ]}
                onSuccess={() => {
                  query.page = 1;
                  fetchStaff();
                }}
                ref={importModal}
                createApi={staffApi.create}
                onUploaded={(excelData, setData) => {
                  console.log("up gì lên vậy", excelData);
                  handleOnUploadedFile(excelData, setData);
                }}
                okText={`Nhập nhân viên ngay`}
                // demoExcel="/exportFile/file_mau_nhap_staff.xlsx"
                onDownloadDemoExcel={handleDownloadDemoExcel}
                loadingDownloadDemo={loadingDownloadDemo}
              />
            ),
            [loadingDownloadDemo]
          )}
        </Card>
      </div>
    );
  }
);
