import { userStore } from "store/userStore";
import settingsData from "./settings.json";
import user from "../src/assets/images/user.png";
import logo from "assets/images/logo.png";
import logoWhite from "assets/images/logo-white.png";

export const settings = {
  logo,
  logoWhite,
  defaultAvatar: user,
  checkPermission: true,
  version: settingsData.version,
  dateFormat: "DD/MM/YYYY",
  fullDateFormat: "HH:mm, DD/MM/YYYY",
  isDev: import.meta.env.VITE_IS_DEV == "true",
  isProduction: import.meta.env.VITE_IS_PRODUCTION == "true",
  qrUrl: `https://302broker.bmdapp.store/cai-dat`,
  mode: import.meta.env.VITE_MODE,
  baseUrl: import.meta.env.VITE_API_URL,
};
