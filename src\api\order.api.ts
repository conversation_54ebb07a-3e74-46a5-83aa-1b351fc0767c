import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const orderApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/order",
      params,
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/order/${id}`,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/order",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/order/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/order/${id}`,
      method: "delete",
    }),
};
