import { MutableRefObject } from "react";

export type WithCustomRef<T> = {
  myRef: MutableRefObject<any>;
} & T;

export const ActiveStatusTrans = {
  true: {
    value: true,
    label: "Đang hoạt động",
    color: "green",
    oppositeLabel: "Dừng hoạt động",
  },
  false: {
    value: false,
    label: "Ngưng hoạt động",
    color: "red",
    oppositeLabel: "Mở hoạt động",
  },
};
export const BlockedStatusTrans = {
  false: {
    value: false,
    label: "Không khóa",
    color: "green",
    oppositeLabel: "Khóa",
  },
  true: {
    value: true,
    label: "Đã bị khóa",
    color: "red",
    oppositeLabel: "Mở khóa",
  },
};
export const HighLightStatusTrans = {
  true: {
    value: true,
    label: "Nổi bật",
    color: "green",
    oppositeLabel: "Không nổi bật",
  },
  false: {
    value: false,
    label: "Không nổi bật",
    color: "red",
    oppositeLabel: "Nổi bật",
  },
};

export const PublicStatusTrans = {
  true: {
    value: true,
    label: "Công khai",
    color: "green",
    oppositeLabel: "Không công khai",
  },
  false: {
    value: false,
    label: "Không công khai",
    color: "red",
    oppositeLabel: "Công khai",
  },
};
export const VisibleStatusTrans = {
  true: {
    value: true,
    label: "Hiển thị",
    color: "green",
    oppositeLabel: "Ẩn",
  },
  false: {
    value: false,
    label: "Ẩn",
    color: "red",
    oppositeLabel: "Hiển thị",
  },
};
