import React from "react";
import { Button, Tooltip } from "antd";
import "./CustomButton.scss";
import DeleteIcon from "assets/svgs/DeleteIcon";

interface CustomButtonProps {
  onClick?: React.MouseEventHandler<HTMLElement> | undefined;
  toolTipContent?: string;
  className?: string;
  iconColor?: string;
}

const DeleteButton: React.FC<CustomButtonProps> = ({
  onClick,
  toolTipContent = "Xóa",
  className,
  iconColor,
}) => {
  return (
    <Tooltip title={toolTipContent}>
      <Button
        type="text"
        danger
        className={className}
        icon={<DeleteIcon width={16} height={16} fill={iconColor} />}
        onClick={(e) => {
          e.stopPropagation();
          onClick?.(e);
        }}
      />
    </Tooltip>
  );
};

export default DeleteButton;
