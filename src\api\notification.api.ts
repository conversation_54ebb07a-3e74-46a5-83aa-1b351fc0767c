import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const notificationApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/notification",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/notification",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/notification/${id}`,
      method: "patch",
      data,
    }),
  handleReadNotification: (notificationId: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/notification/${notificationId}/read`,
      method: "patch",
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/notification/${id}`,
      method: "delete",
    }),
};
