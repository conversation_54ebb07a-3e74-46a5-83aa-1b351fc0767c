import { useTheme } from "context/ThemeContext";
import * as React from "react";

const CalendarIcon = ({ fill = "#050505", width = 16, height = 16 }) => {
  const { darkMode } = useTheme();
  if (darkMode) {
    fill = "#ffffff";
  }
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 16 16"
      fill={fill}
    >
      <path
        fill={fill}
        fillRule="evenodd"
        d="M4.667 1.167a.5.5 0 0 1 .5.5v.508c.441-.008.927-.008 1.462-.008h2.742c.535 0 1.021 0 1.463.008v-.508a.5.5 0 0 1 1 0v.551c.173.013.337.03.492.05.782.106 1.414.327 1.913.826.5.499.72 1.132.826 1.913a10 10 0 0 1 .071.82.5.5 0 0 1 .013.306c.018.535.018 1.142.018 1.83V9.37c0 1.225 0 2.195-.102 2.955-.105.782-.327 1.414-.826 1.913-.499.499-1.131.72-1.913.825-.76.103-1.73.103-2.955.103H6.63c-1.225 0-2.195 0-2.955-.102-.781-.105-1.414-.327-1.913-.826-.499-.499-.72-1.131-.825-1.913-.103-.76-.103-1.73-.103-2.955V7.962c0-.687 0-1.294.019-1.829a.5.5 0 0 1 .012-.306c.015-.297.038-.57.072-.82.105-.781.326-1.414.825-1.913.499-.499 1.132-.72 1.913-.825.155-.021.32-.038.493-.051v-.551a.5.5 0 0 1 .5-.5ZM1.842 6.5c-.008.435-.009.93-.009 1.5v1.333c0 1.271.002 2.175.094 **********.259 1.057.541 1.339.282.282.669.451 1.34.541.684.093 1.588.094 2.859.094h2.667c1.27 0 2.174-.001 2.859-.094.67-.09 1.057-.259 1.34-.541.281-.282.45-.668.54-1.34.093-.684.094-1.588.094-2.859V8c0-.57 0-1.065-.009-1.5H1.842Zm12.27-1H1.888c.01-.126.023-.246.039-.36.09-.67.259-1.057.541-1.339.282-.282.669-.451 1.34-.541.685-.092 1.588-.093 2.859-.093h2.667c1.27 0 2.174 0 2.859.093.67.09 1.057.26 1.34.541.281.282.45.669.54 ************.029.233.04.359Z"
        clipRule="evenodd"
      />
    </svg>
  );
};
export default CalendarIcon;
