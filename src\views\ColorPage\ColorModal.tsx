import { Col, Form, Input, message, Modal, Row } from "antd";
import { Rule } from "antd/lib/form";
import { colorApi } from "api/color.api";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { ModalStatus } from "types/modal";
import { Color } from "types/color";
import { useWatch } from "antd/es/form/Form";
import { HexColorInput, HexColorPicker } from "react-colorful";
import { requiredRule } from "utils/validateRule";

export interface ColorModal {
  handleCreate: () => void;
  handleUpdate: (color: Color) => void;
}
interface ColorModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
}

export const ColorModal = React.forwardRef(
  ({ onClose, onSubmitOk }: ColorModalProps, ref) => {
    const [form] = Form.useForm<Color>();

    const hex = useWatch("hex", form);

    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");

    useImperativeHandle<any, ColorModal>(
      ref,
      () => ({
        handleCreate() {
          form.resetFields();
          setVisible(true);
          setStatus("create");
        },
        handleUpdate(color: Color) {
          form.setFieldsValue({ ...color });
          setVisible(true);
          setStatus("update");
        },
      }),
      []
    );

    const createData = async () => {
      const valid = await form.validateFields();
      const data = { color: form.getFieldsValue() };

      setLoading(true);
      try {
        const res = await colorApi.create(data);
        message.success("Tạo thành công");
        handleClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const valid = await form.validateFields();
      const data = { color: form.getFieldsValue() };
      setLoading(true);
      try {
        const res = await colorApi.update(data.color.id || 0, data);
        message.success("Cập nhật thành công");
        handleClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    const handleClose = () => {
      setVisible(false);
      onClose();
    };

    return (
      <Modal
        onCancel={() => {
          onClose?.();
          setVisible(false);
        }}
        open={visible}
        title={status == "create" ? "Tạo màu" : "Cập nhật màu"}
        style={{ top: 20 }}
        width={500}
        confirmLoading={loading}
        onOk={() => {
          status == "create" ? createData() : updateData();
        }}
      >
        <Form layout="vertical" form={form} initialValues={{ hex: "000000" }}>
          <Form.Item name="id" hidden></Form.Item>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item label="Tên" name="name" rules={[requiredRule]}>
                <Input placeholder="" />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="Mã màu (HEX)" name="hex" rules={[requiredRule]}>
                <div className="flex flex-col gap-2 items-center">
                  <HexColorPicker
                    color={hex}
                    onChange={(newHex) => {
                      form.setFieldValue("hex", newHex);
                    }}
                  />
                  <HexColorInput
                    className="w-[180px] p-2 border-gray-300 rounded-md border-[1px] border-solid"
                    alpha
                    prefixed={true}
                    color={hex}
                    onChange={(newHex) => {
                      form.setFieldValue("hex", newHex);
                    }}
                  />
                </div>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
);
