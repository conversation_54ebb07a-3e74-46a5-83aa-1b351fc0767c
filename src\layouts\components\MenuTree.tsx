import { Divider, <PERSON>u } from "antd";
import SubMenu from "antd/es/menu/SubMenu";
import clsx from "clsx";
import { $isShowGameRouter } from "constant";
import { observer } from "mobx-react";
import { IoMdArrowDropdown } from "react-icons/io";
import { Link } from "react-router-dom";
import { settings } from "settings";
import { permissionStore } from "store/permissionStore";
import { userStore } from "store/userStore";

interface Props {
  collapsed: boolean;
}

export const MenuTree = observer(({ collapsed }: Props) => {
  return (
    <>
      {permissionStore.accessRoutes
        .filter((route) => {
          if (
            !route.hidden &&
            (route.isPublic || route.isAccess || !settings.checkPermission)
          ) {
            if ($isShowGameRouter) {
              // console.log($isShowGameRouter);
              return true;
            } else {
              if (route?.isGameRouter) {
                return false;
              } else {
                return true;
              }
            }
          }
        })

        .map((route) => {
          if (route.children?.length) {
            const filterElementRoutes = route.children.filter(
              (item) => item.element && item.isAccess && !item.hidden
            );
            const routeJs = JSON.parse(JSON.stringify(route));

            if (filterElementRoutes.length == 1 && route.isCompact) {
              // debugger;
              const element = filterElementRoutes[0];
              const path = `${route.path!}/${element.path}`.trim() || "";
              return (
                <>
                  <Menu.Item
                    icon={route.icon}
                    key={path}
                    // onClick={() => console.log("path ne", route.path)}
                  >
                    <Link to={path || ""} className="uppercase">
                      {route.title}
                    </Link>
                  </Menu.Item>
                  <Divider
                    className={clsx(
                      "my-2 mx-auto min-w-0",
                      collapsed ? "w-1/2" : "w-[90%]"
                    )}
                  />
                </>
              );
            }

            return (
              <>
                <SubMenu
                  key={route.path}
                  icon={route.icon}
                  expandIcon={
                    <IoMdArrowDropdown className="ant-menu-submenu-arrow" />
                  }
                  title={
                    <span className="-title-content uppercase">
                      {route.title}
                    </span>
                  }
                  popupClassName="custom-sub-menu-popup"
                >
                  {route.children
                    ?.filter(
                      (child) => child.isAccess || !settings.checkPermission
                    )
                    .filter((item) => !item.hidden)
                    .map((item) => {
                      return (
                        <Menu.Item key={route.path + "/" + item.path}>
                          <div className="menu-dot"></div>
                          {route.path && item.path && (
                            <Link to={route.path + "/" + item.path}>
                              {item.title}{" "}
                            </Link>
                          )}
                        </Menu.Item>
                      );
                    })}
                </SubMenu>
                <Divider
                  className={clsx(
                    "my-2 mx-auto min-w-0",
                    collapsed ? "w-1/2" : "w-[90%]"
                  )}
                />
              </>
            );
          }
          return (
            <>
              <Menu.Item
                icon={route.icon}
                key={route.path}
                // onClick={() => console.log("path ne", route.path)}
              >
                <Link to={route.path || ""} className="uppercase">
                  {route.title}
                </Link>
              </Menu.Item>
              <Divider
                className={clsx(
                  "my-2 mx-auto min-w-0",
                  collapsed ? "w-1/2" : "w-[90%]"
                )}
              />
            </>
          );
        })}
    </>
  );
});
