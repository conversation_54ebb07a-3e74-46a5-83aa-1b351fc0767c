import { match } from "path-to-regexp";
import { Route } from "./RouteType";

export function findMatchedRoute(
  pathname: string,
  routeList: Route[]
): Route | null {
  for (const route of routeList) {
    if (route.path) {
      const matcher = match(route.path, { decode: decodeURIComponent });
      if (matcher(pathname)) {
        return route;
      }
    }
    if (route.children) {
      const child = findMatchedRoute(pathname, route.children);
      if (child) {
        return child;
      }
    }
  }

  return null;
}
