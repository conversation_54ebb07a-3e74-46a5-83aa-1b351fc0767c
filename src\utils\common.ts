import { DictionaryType } from "types/dictionary";

export const uniqueArrayByKey = (array: any[], key: string) => {
  return [...new Map(array.map((item) => [item[key], item])).values()];
};

type AType = Record<string, any>;

export const removeSubstringFromKeys = (
  obj: AType,
  substring: string
): AType => {
  return Object.fromEntries(
    Object.entries(obj).map(([key, value]) => [
      key.replace(substring, ""),
      value,
    ])
  );
};

export const DictionaryTreeTypes = [
  DictionaryType.ProductGroup,
  DictionaryType.MaterialGroup,
  DictionaryType.DeviceCategory,
  DictionaryType.MachineCategory,
  DictionaryType.WorkType,
];

// Kiểm tra type có phải là type tree data không
export const isTypeTreeData = (type: DictionaryType) => {
  return DictionaryTreeTypes.includes(type)
}
