import { useTheme } from "context/ThemeContext";
import * as React from "react";

const SvgComponent = ({
  fill = "#19345B",
  width = 20,
  height = 20,
}: {
  fill?: string;
  width?: number;
  height?: number;
}) => {
  const { darkMode } = useTheme();

  if (darkMode) {
    fill = "#e4eced";
  }

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      fill={fill}
    >
      <path
        fill={fill}
        fillRule="evenodd"
        d="M11.899 1.793c-.309-.126-.7-.126-1.482-.126-.783 0-1.174 0-1.483.126-.411.17-.738.494-.909.902-.077.187-.108.404-.12.72a1.346 1.346 0 0 1-.663 1.127 1.367 1.367 0 0 1-1.316.007c-.282-.148-.486-.23-.688-.257a1.689 1.689 0 0 0-1.241.33c-.265.202-.46.539-.852 1.211-.391.673-.587 1.009-.63 1.338-.059.438.061.881.332 1.232.124.16.298.294.568.463.397.247.652.669.652 1.134 0 .465-.255.887-.652 1.134-.27.168-.444.303-.568.463-.271.35-.39.794-.333 1.232.044.329.24.665.63 1.338.392.672.588 1.008.853 1.21.353.27.8.388 1.241.33.202-.026.406-.108.688-.256.414-.217.91-.226 1.316.006.406.233.646.663.663 1.128.012.316.043.533.12.72.17.408.498.732.91.901.308.127.7.127 1.482.127s1.173 0 1.482-.127c.412-.169.738-.493.909-.902.078-.186.108-.403.12-.72.018-.464.258-.894.663-1.127a1.367 1.367 0 0 1 1.316-.006c.282.148.486.23.688.256.442.058.888-.06 1.242-.33.265-.202.46-.538.851-1.21.392-.673.587-1.01.63-1.338a1.658 1.658 0 0 0-.332-1.232c-.124-.16-.297-.295-.567-.463A1.346 1.346 0 0 1 16.766 10c0-.465.256-.887.653-1.134.27-.168.444-.303.567-.463.271-.35.391-.794.333-1.232-.044-.329-.24-.665-.63-1.338-.392-.672-.587-1.009-.852-1.21-.354-.27-.8-.388-1.242-.33-.202.026-.406.108-.688.256-.414.217-.91.226-1.315-.007a1.346 1.346 0 0 1-.664-1.127c-.012-.316-.042-.533-.12-.72a1.673 1.673 0 0 0-.909-.902ZM10.417 12.5a2.51 2.51 0 0 0 2.519-2.5 2.51 2.51 0 0 0-2.52-2.5A2.51 2.51 0 0 0 7.899 10a2.51 2.51 0 0 0 2.519 2.5Z"
        clipRule="evenodd"
      />
    </svg>
  );
};
export default SvgComponent;
