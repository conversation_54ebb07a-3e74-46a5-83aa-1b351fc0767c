import * as XLSX from "xlsx";
import { MyTableColumn } from "./excel";
import { MyExcelColumn } from "./MyExcel";
import { getValueFromArrOfKeys } from "./array";

export const readerData = (
  file: File,
  sheetName: number = 0,
  range = 0,
  opts?: XLSX.ParsingOptions
) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e: any) => {
      const data = e.target.result;
      const workbook = XLSX.read(data, { type: "array", ...opts });

      //   const sheetName = this.sheetName || "0";
      const firstSheetName = workbook.SheetNames[sheetName];
      const worksheet = workbook.Sheets[firstSheetName];
      const header = getHeaderRow(worksheet);
      const results = XLSX.utils.sheet_to_json(worksheet, { range });
      console.log({ results });
      const newResults = results.map((item: any) => {
        const rowNum = item.__rowNum__ + 1;
        return { ...item, __rowNum__: rowNum };
      });

      //   generateData({ header, results });
      resolve({ header, results: newResults });
    };
    reader.readAsArrayBuffer(file);
  });
};

const getHeaderRow = (sheet: any) => {
  const headers = [];
  const range = XLSX.utils.decode_range(sheet["!ref"]);
  let C;
  const R = range.s.r;
  /* start in the first row */
  for (C = range.s.c; C <= range.e.c; ++C) {
    /* walk every column in the range */
    const cell = sheet[XLSX.utils.encode_cell({ c: C, r: R })];
    /* find the cell in the first row */
    let hdr = "UNKNOWN " + C; // <-- replace with your desired default
    if (cell && cell.t) hdr = XLSX.utils.format_cell(cell);
    headers.push(hdr);
  }
  return headers;
};

export const parsedAntdTableToExcelColumns = <T>(arr: MyTableColumn<T>[]) => {
  const excelCols = arr
    .filter((c) => !c.antdTableOnly)
    .map((col) => {
      return {
        ...col,
        width: col.excelWidth ?? col.width,
        header: col.excelHeader ?? col.title,
        key: col.dataIndex ?? col.key,
        render: (data: T & { errorMessage: string }, index) => {
          let value = data?.[col.key as keyof T] ?? "";
          if (col.dataIndex) {
            if (typeof col.dataIndex == "object") {
              value = getValueFromArrOfKeys(data, col.dataIndex as string[]);
            } else {
              value = data?.[col.dataIndex as keyof T] ?? "";
            }
          }

          return (
            col?.excelRender?.(data) ??
            col?.render?.(value, data, index) ??
            value
          );
        },
      } as MyExcelColumn<T>;
    });
  return excelCols;
};
