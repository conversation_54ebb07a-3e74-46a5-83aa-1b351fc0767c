import { AxiosPromise } from "axios";
import { request } from "utils/request";

export const materialTypeApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/materialType",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/materialType",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/materialType/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/materialType/${id}`,
      method: "delete",
    }),
};
