.card-image {
  background-color: var(--color-neutral-n0);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.2);
  transition: box-shadow 0.3s ease;
  cursor: pointer;
  width: 100%;
  height: 280px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-radius: 5px;

  &:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.3);
  }

  &__image-container {
    width: 100%;
    height: 153px;
    position: relative;
    flex-shrink: 0;
    overflow: hidden;
  }

  &__image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  &__empty-state {
    width: 100%;
    height: 100%;
    background-color: var(--color-neutral-n1);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    &-text {
      font-size: 0.875rem;
      color: var(--color-neutral-n4);
    }
  }

  &__multiple-images {
    width: 100%;
    height: 100%;
    position: relative;

    &-overlay {
      position: absolute;
      inset: 0;
      background-color: rgba(0, 0, 0, 0.1);
    }

    &-indicators {
      position: absolute;
      top: 0.5rem;
      right: 0.5rem;
      display: flex;
      gap: 0.25rem;

      &-dot {
        width: 0.5rem;
        height: 0.5rem;
        background-color: var(--color-neutral-n0);
        border-radius: 50%;

        &--opacity-60 {
          opacity: 0.6;
        }
        &--opacity-80 {
          opacity: 0.8;
        }
        &--opacity-100 {
          opacity: 1;
        }
      }
    }
  }

  &__category-label {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    padding: 0.25rem 0.625rem;

    &-background {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      width: 100%;
      opacity: 0.7;
    }

    &-text {
      position: relative;
      font-size: 0.75rem;
      font-weight: bold;
    }
  }

  &__count-overlay {
    position: absolute;
    bottom: 0.5rem;
    left: 0.5rem;
    background-color: rgba(0, 0, 0, 0.6);
    color: var(--color-neutral-n0);
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    font-weight: bold;
  }

  &__content {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    flex: 1;
    height: 127px;
    overflow: hidden;
  }

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
  }

  &__title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
    min-width: 0;

    &-icon {
      font-size: 1rem;
      flex-shrink: 0;

      &--folder {
        color: #eab308;
      }
    }

    &-text {
      font-weight: bold;
      font-size: 0.875rem;
      color: var(--color-neutral-n8);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      line-height: 1.2;
    }
  }

  &__menu {
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.3s ease;
    cursor: pointer;
    flex-shrink: 0;

    &:hover {
      background-color: var(--color-neutral-n2);
    }

    &-icon {
      color: var(--color-neutral-n8);
      font-size: 1.5625rem;
    }
  }

  &__info {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.875rem;
    color: var(--color-neutral-n5);
    flex-shrink: 0;
    line-height: 1.2;

    &-icon {
      width: 1rem;
      height: 1rem;
      flex-shrink: 0;
    }

    span {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.ant-image {
  overflow: hidden;
  width: 100%;
  height: 100%;

  .ant-image-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.display-image__single,
.display-image__double,
.display-image__triple,
.display-image__multiple {
  .ant-image {
    .ant-image-img {
    }
  }
}
