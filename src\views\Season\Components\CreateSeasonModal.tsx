import {
  Button,
  Checkbox,
  Col,
  Form,
  Image,
  Input,
  message,
  Modal,
  Row,
  Select,
  Tag,
  Upload,
  UploadProps,
} from "antd";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { ModalStatus } from "types/modal";
import { useWatch } from "antd/lib/form/Form";
import { requiredRule } from "utils/validateRule";
import { $url } from "utils/url";
import { useComponent } from "hooks/useComponent";
import { CreatingVariant, Variant } from "types/variant";
import { useMaterial } from "hooks/useMaterial";
import { variantApi } from "api/variant.api";
import ChooseFileFromMenu from "components/Upload/ChooseImageFromMenu";
import { Unit } from "types/unit";
import { unitApi } from "api/unit.api";
import { seasonApi } from "api/season.api";
import { SeasonType } from "types/season";

export interface UnitModal {
  handleCreate: () => void;
  handleUpdate: (season: SeasonType) => void;
}
interface UnitModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
}
export const CreateSeasonModal = React.forwardRef(
  ({ onClose, onSubmitOk }: UnitModalProps, ref) => {
    const [form] = Form.useForm<SeasonType>();
    const fileAttachAvatar = useWatch("fileAttachAvatar", form);

    const materialType = useWatch("materialType", form);
    const fileAttachBackImage = useWatch("fileAttachBackImage", form);
    const fileAttachFrontImage = useWatch("fileAttachFrontImage", form);
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");
    useImperativeHandle<any, UnitModal>(
      ref,
      () => ({
        handleCreate() {
          form.resetFields();
          setVisible(true);
          setStatus("create");
        },
        handleUpdate(season) {
          form.setFieldsValue({
            ...season,
            fileAttachAvatar: season.fileAttachAvatar,
          });

          setVisible(true);
          setStatus("update");
        },
      }),
      []
    );
    const createData = async () => {
      const data = form.getFieldsValue();
      setLoading(true);
      try {
        const res = await seasonApi.create({
          season: { ...data },
          fileAttachAvatarId: fileAttachAvatar?.id,
        });
        message.success("Tạo thành công");
        handleClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const valid = await form.validateFields();
      const data = form.getFieldsValue();
      setLoading(true);
      console.log("What is in form", data);
      const { id, ...restData } = data;
      console.log("Rest data là", restData);
      try {
        console.log("Data when update", data);
        const res = await seasonApi.update(id, {
          season: { ...data },
          fileAttachAvatarId: fileAttachAvatar?.id,
        });
        message.success("Cập nhật thành công");
        handleClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    const handleClose = () => {
      onClose();
      setVisible(false);
      form.resetFields();
    };
    const {
      components,
      fetchData,
      query,
      loading: componentLoading,
      setQuery,
      total,
    } = useComponent({
      initQuery: {
        page: 1,
        limit: 10,
      },
    });
    const {
      fetchData: fetchMaterials,
      loading: loadingMaterials,
      materials,
      query: queryMaterial,
      setQuery: setQueryMaterial,
    } = useMaterial({
      initQuery: {
        page: 1,
        limit: 10,
      },
    });
    useEffect(() => {
      fetchData();
      fetchMaterials();
    }, []);
    useEffect(() => {
      queryMaterial.type = materialType;
      fetchMaterials();
    }, [materialType]);
    return (
      <Modal
        onCancel={() => {
          onClose?.();
          setVisible(false);
        }}
        open={visible}
        title={status == "create" ? "Tạo season" : "Sửa season"}
        style={{ top: 20 }}
        confirmLoading={loading}
        onOk={() => {
          status == "create" ? createData() : updateData();
        }}
      >
        <Form layout="vertical" form={form}>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="Avatar"
                name="fileAttachAvatar"
                rules={[requiredRule]}
              >
                <ChooseFileFromMenu
                  fileUrl={fileAttachAvatar?.url}
                  onSelectOk={(url, file) => {
                    console.log("File lấy đc là", file);
                    form.setFieldValue("fileAttachAvatar", file);
                  }}
                  ratioText="Tỉ lệ 1x1"
                  fileName={fileAttachAvatar?.name}
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="Tên season" name="name" rules={[requiredRule]}>
                <Input placeholder="" disabled />
              </Form.Item>
            </Col>
            {[
              status === "update" && (
                <Col span={8}>
                  <Form.Item
                    className="hidden"
                    label="id"
                    name="id"
                    rules={[requiredRule]}
                  >
                    <Input placeholder="" />
                  </Form.Item>
                </Col>
              ),
            ]}
          </Row>
        </Form>
      </Modal>
    );
  }
);
