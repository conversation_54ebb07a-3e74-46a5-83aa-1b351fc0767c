import { PlusOutlined } from "@ant-design/icons";
import { message, Modal, Upload } from "antd";
import { UploadFile } from "antd/lib/upload/interface";
import { find } from "lodash";
import React, { useImperativeHandle } from "react";
import { useEffect, useState } from "react";
import { getImageSize } from "utils";
import { getToken } from "utils/auth";
import { $url } from "utils/url";

export interface MultiImageUploadProps {
  uploadUrl?: string;
  onUploadOk: (fileList: Array<string>) => void;
  recommendSize?: { width: number; height: number };
  fileListProp: UploadFile<any>[];
}

export interface MultiImageUpload {
  handleUpdatePosition: (image: string[]) => void;
}

export const MultiImageUpload = React.forwardRef(
  (
    {
      uploadUrl = import.meta.env.VITE_API_URL + "/v1/admin/fileAttach/upload",
      fileListProp,
      recommendSize,
      onUploadOk,
    }: MultiImageUploadProps,
    ref
  ) => {
    const [previewVisible, setPreviewVisible] = useState(false);
    const [previewImage, setPreviewImage] = useState("");
    const [previewTitle, setPreviewTitle] = useState("");
    const [fileList, setFileList] = useState<UploadFile<any>[]>();
    const [imageSize, setImageSize] = useState<{
      width: number;
      height: number;
    }>({ width: 0, height: 0 });

    useImperativeHandle(
      ref,
      () => {
        return {
          handleUpdatePosition(fileList: UploadFile<any>[]) {
            setFileList([...fileList]);

            const filePathList: string[] = [];

            fileList.forEach((file) => {
              if (file.status !== "error") {
                filePathList.push($url(file.response?.data?.path || file.url));
              }
            });

            onUploadOk(filePathList);
          },
        };
      },
      []
    );

    const handleChange = ({ fileList }: { fileList: UploadFile<any>[] }) => {
      const filePathList: string[] = [];

      fileList.forEach((file) => {
        if (file.status === "error") {
          // message.config({ maxCount: 1 });
          message.error(file.response?.message);
        } else {
          filePathList.push($url(file.response?.data?.path || file.url));
        }
      });

      setFileList(fileList.filter((e) => e.status != "error"));

      onUploadOk(filePathList);

      if (recommendSize) {
        if (fileList.length == 0) {
          setImageSize({ width: 0, height: 0 });
        }
        filePathList.every((path) => {
          const imageSize = getImageSize(setImageSize, path);
          if (
            imageSize.width > recommendSize.width ||
            imageSize.height > recommendSize.height
          ) {
            return false;
          } else return true;
        });
      }
    };

    const handlePreview = (file: UploadFile) => {
      setPreviewImage(
        file.url || import.meta.env.VITE_IMG_URL + "/" + file.response.data.path
      );
      setPreviewVisible(true);
    };

    const checkSize = async (file: File): Promise<boolean> => {
      return new Promise((resolve, reject) => {
        if (recommendSize) {
          const reader = new FileReader();
          reader.readAsDataURL(file);
          reader.addEventListener("load", (event) => {
            //@ts-ignore
            const _loadedImageUrl = event.target.result;
            const image = document.createElement("img");

            //@ts-ignore
            image.src = _loadedImageUrl;
            image.addEventListener("load", () => {
              const { width, height } = image;
              if (
                width > recommendSize.width ||
                height > recommendSize.height
              ) {
                message.error(
                  `Kích thước quá lớn (Đề xuất: ${recommendSize.width}x${recommendSize.height})`
                );

                return resolve(false);
              } else {
                resolve(true);
              }
            });
          });
        } else {
          return resolve(true);
        }
      });
    };

    const beforeUpload = async (file: File) => {
      const notLargeSize = await checkSize(file);

      const isImg = file.type.includes("image");

      if (!isImg) {
        message.error("Only accept image format");
      }

      return (notLargeSize && isImg) || Upload.LIST_IGNORE;
    };

    useEffect(() => {
      setFileList(() => fileListProp.map((e) => ({ ...e })));

      if (recommendSize) {
        for (let i = 0; i < fileListProp.length; i++) {
          // debugger;
          const file = fileListProp[i];
          if (file.url) {
            getImageSize(({ width, height }) => {
              if (
                width > recommendSize.width ||
                height > recommendSize.height
              ) {
                return setImageSize({ width, height });
              }
            }, file.url);
          }
        }
      }
    }, [fileListProp]);

    const uploadButton = (
      <div>
        <PlusOutlined />
        <div style={{ marginTop: 8 }}>Chọn ảnh</div>
      </div>
    );

    return (
      <>
        {/* {recommendSize &&
          (imageSize.width > recommendSize?.width ||
            imageSize.height > recommendSize.height) && (
            <p style={{ color: "red" }}>
              Kích thước hình ảnh quá lớn cần chỉnh sửa lại
            </p>
          )} */}
        <Upload
          accept="image/png, image/jpeg"
          multiple
          headers={{ token: getToken() || "" }}
          action={uploadUrl}
          listType="picture-card"
          fileList={fileList}
          onPreview={handlePreview}
          onChange={handleChange}
          beforeUpload={beforeUpload}
        >
          {uploadButton}
        </Upload>
        <Modal
          visible={previewVisible}
          title={previewTitle}
          footer={null}
          onCancel={() => setPreviewVisible(false)}
        >
          <img alt="example" style={{ width: "100%" }} src={previewImage} />
        </Modal>
      </>
    );
  }
);
