import "./styles/AuthLayout.scss";
import React, { useState } from "react";
import loginBg from "assets/images/login-bg.png";
import { settings } from "settings";
import { Image, Select, Space } from "antd";
import { useMediaQuery } from "@mui/material";
import unitedKingdomIcon from "assets/svgs/united-kingdom.svg";
import { IoLanguageOutline } from "react-icons/io5";
import dayjs from "dayjs";
import clsx from "clsx";

interface Props {
  children?: React.ReactNode;
}

export const AuthLayout = ({ children }: Props) => {
  return (
    <div className="h-[100dvh] flex overflow-hidden justify-center">
      <div className="relative hidden md:!flex auth-layout-bg flex-1 h-auto justify-center items-center">
        <img
          src={"https://www.minhglobal.com/public/upload/image/17442192802.jpg"}
          className="size-full object-cover z-10"
        />

        <div className="z-10 absolute flex justify-center flex-col items-center w-full h-full backdrop-blur-sm ">
          <div className="text-[46px] font-bold uppercase tracking-wider ">
            Minh Global
          </div>
          <div className="bg-red-500 h-[2px] w-[200px] my-2"></div>
          <div className="text-[24px] italic">Design, Build & Development</div>
          <div className="text-[34px] font-bold uppercase tracking-wider">
            Quest for BEST
          </div>
        </div>
      </div>
      <div
        className={clsx(
          "relative w-[500px] h-full bg-white flex flex-col overflow-y-auto"
        )}
      >
        <div
          className={clsx(
            "flex-1 flex flex-col md:p-[64px] md:justify-between md:mt-0 md:gap-0 px-[16px] mt-[24px] gap-[64px]"
          )}
        >
          <div className="relative h-[60px] flex-shrink-0 ">
            {/* <div className={clsx("md:absolute md:top-0 md:right-0 w-full")}>
              <Space className={clsx("w-full md:justify-end justify-between")}>
                <img
                  src={settings.logo}
                  className="block md:hidden w-[119px] h-[440x] object-contain"
                />
              </Space>
            </div> */}
          </div>
          <div className={clsx("relative")}>{children}</div>

          <div className="relative h-[60px] flex-shrink-0 "></div>
        </div>
      </div>
    </div>
  );
};
