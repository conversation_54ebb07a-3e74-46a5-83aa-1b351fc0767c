import { Col, Form, Input, message, Modal, Row } from "antd";
import { Rule } from "antd/lib/form";
import { patternApi } from "api/pattern.api";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { ModalStatus } from "types/modal";
import { Pattern } from "types/pattern";
import { useWatch } from "antd/es/form/Form";
import { requiredRule } from "utils/validateRule";
import { FileAttach } from "types/fileAttach";

export interface PatternModal {
  handleCreate: () => void;
  handleUpdate: (pattern: Pattern) => void;
}
interface PatternModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
}

export const PatternModal = React.forwardRef(
  ({ onClose, onSubmitOk }: PatternModalProps, ref) => {
    const [form] = Form.useForm<Pattern>();

    const hex = useWatch("hex", form);

    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");

    useImperativeHandle<any, PatternModal>(
      ref,
      () => ({
        handleCreate() {
          form.resetFields();
          setVisible(true);
          setStatus("create");
        },
        handleUpdate(pattern: Pattern) {
          form.setFieldsValue({ ...pattern });
          setVisible(true);
          setStatus("update");
        },
      }),
      []
    );

    const createData = async () => {
      const valid = await form.validateFields();
      const data = { pattern: form.getFieldsValue() };

      setLoading(true);
      try {
        const res = await patternApi.create(data);
        message.success("Tạo thành công");
        handleClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const valid = await form.validateFields();
      const data = { pattern: form.getFieldsValue() };
      setLoading(true);
      try {
        const res = await patternApi.update(data.pattern.id || 0, data);
        message.success("Cập nhật thành công");
        handleClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    const handleClose = () => {
      setVisible(false);
      onClose();
    };

    return (
      <Modal
        onCancel={() => {
          onClose?.();
          setVisible(false);
        }}
        open={visible}
        title={status == "create" ? "Tạo pattern" : "Cập nhật pattern"}
        style={{ top: 20 }}
        width={500}
        confirmLoading={loading}
        onOk={() => {
          status == "create" ? createData() : updateData();
        }}
      >
        <Form layout="vertical" form={form}>
          <Form.Item name="id" hidden></Form.Item>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item shouldUpdate={true} style={{ marginBottom: 0 }}>
                {() => {
                  return (
                    <Form.Item
                      style={{ marginBottom: 0 }}
                      label={<div>Icon</div>}
                      name="icon"
                    >
                      <SingleImageUpload
                        onUploadOk={(file: FileAttach) => {
                          form.setFieldsValue({
                            icon: file.path,
                          });
                        }}
                        imageUrl={form.getFieldValue("icon")}
                      />
                    </Form.Item>
                  );
                }}
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="Tên" name="name" rules={[requiredRule]}>
                <Input placeholder="" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
);
