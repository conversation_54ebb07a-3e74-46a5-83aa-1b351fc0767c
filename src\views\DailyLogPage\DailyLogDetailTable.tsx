import { Button, Tooltip, Modal, Input } from "antd";
import { PlusOutlined } from "@ant-design/icons";
import DeleteIcon from "assets/svgs/DeleteIcon";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import CustomButton from "components/Button/CustomButton";
import React from "react";

interface DailyLogDetailTableProps {
  title: string;
  columns: CustomizableColumn<any>[];
  data: any[];
  setData: React.Dispatch<React.SetStateAction<any[]>>;
  readonly: boolean;
  loading?: boolean;
  getNewRow: () => any;
  onAfterDeleteRow?: (newData: any[]) => void;
}

export const DailyLogDetailTable: React.FC<DailyLogDetailTableProps> = ({
  title,
  columns,
  data,
  setData,
  readonly,
  loading,
  getNewRow,
  onAfterDeleteRow,
}) => {
  const handleAddRow = () => {
    setData((prev) => [...prev, getNewRow()]);
  };

  const handleCellChange = (value: any, record: any, dataIndex: string) => {
    const newData = [...data];
    const index = newData.findIndex((item) => item.id === record.id);
    if (index > -1) {
      newData[index][dataIndex] = value;
      setData(newData);
    }
  };

  const handleDeleteRow = (record: any) => {
    const newData = data.filter((item) => item.id !== record.id);
    setData(newData);
    if (onAfterDeleteRow) onAfterDeleteRow(newData);
    Modal.destroyAll();
  };

  

  // Thêm cột xóa vào cuối
  const columnsWithDelete: CustomizableColumn<any>[] = [
    ...columns,
    {
      key: "actions",
      title: "Xử lý",
      width: 100,
      align: "center",
      fixed: "right" as "right",
      render: (_: any, record: any) => (
        <Button
          type="text"
          danger
          icon={<DeleteIcon />}
          onClick={(e) => {
            e.stopPropagation();
            Modal.confirm({
              title: `Xóa dòng này?`,
              getContainer: () => document.getElementById("App") as HTMLElement,
              icon: null,
              content: (
                <div>
                  Bạn sẽ không thể khôi phục dữ liệu này sau khi xóa.
                  <br />
                  Bạn có chắc chắn muốn xóa dữ liệu này?
                </div>
              ),
              footer: (_, { OkBtn, CancelBtn }) => (
                <>
                  <CustomButton
                    variant="outline"
                    className="cta-button"
                    onClick={() => handleDeleteRow(record)}
                  >
                    Có
                  </CustomButton>
                  <CustomButton
                    onClick={() => Modal.destroyAll()}
                    className="cta-button"
                  >
                    Không
                  </CustomButton>
                </>
              ),
            });
          }}
        />
      ),
    } as CustomizableColumn<any>,
  ];


  // Thay thế render input cho từng cột nếu cần
  const finalColumns = columnsWithDelete.map((col) =>
    col.render || !("dataIndex" in col)
      ? col
      : {
          ...col,
          render: (text: any, record: any) => (
            <Input
              value={
                record[(col as CustomizableColumn<any>).dataIndex as string]
              }
              onChange={(e) =>
                handleCellChange(
                  e.target.value,
                  record,
                  (col as CustomizableColumn<any>).dataIndex as string
                )
              }
              disabled={readonly}
              size="small"
            />
          ),
        }
  );

  return (
    <div style={{ marginBottom: 16 }}>
      <div style={{ display: "flex", alignItems: "center", marginBottom: 8 }}>
        <span style={{ fontWeight: 600, fontSize: 16 }}>{title}</span>
        <Tooltip title={readonly ? "Bạn không thể thêm mới" : "Thêm dòng"}>
          <PlusOutlined
            style={{
              fontSize: 18,
              color: readonly ? "#ccc" : "#1677ff",
              cursor: readonly ? "not-allowed" : "pointer",
              marginLeft: 8,
            }}
            onClick={(e) => {
              e.stopPropagation();
              if (!readonly) handleAddRow();
            }}
          />
        </Tooltip>
      </div>
      <CustomizableTable
        columns={finalColumns}
        dataSource={data}
        loading={loading}
        pagination={false}
        rowKey="id"
        scroll={{ x: 400 }}
        size="small"
        bordered
      />
    </div>
  );
};
