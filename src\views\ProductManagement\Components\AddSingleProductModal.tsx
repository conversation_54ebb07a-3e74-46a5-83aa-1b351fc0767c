import { Col, Form, Input, message, Modal, Row } from "antd";
import { materialApi } from "api/material.api";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { ModalStatus } from "types/modal";
import { Material } from "types/material";
import { useWatch } from "antd/lib/form/Form";
import { InputNumber } from "components/Input/InputNumber";
import { requiredRule } from "utils/validateRule";
import { cloneDeep, uniqueId } from "lodash";
import { BOM, MainComponent, Product } from "types/product";
import SingleProductTab from "../Tabs/SingleProductTab";
export interface addSingleProductRef {
  handleCreate: (products?: Product[]) => void;
  handleUpdate: (mainComponent: MainComponent) => void;
}
interface MainComponentModalProps {
  onClose: () => void;
  onSubmitOk: (item: Product[]) => void;
  onUpdate: (item: MainComponent) => void;
}

export const AddSingleProductModal = React.forwardRef<
  addSingleProductRef,
  MainComponentModalProps
>(({ onClose, onSubmitOk, onUpdate }, ref) => {
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [status, setStatus] = useState<ModalStatus>("create");
  const [singleProducts, setSingleProducts] = useState<Product[]>([]);
  useImperativeHandle(
    ref,
    () => ({
      handleCreate(products?: Product[]) {
        if (products) {
          setSingleProducts(products);
        }
        setStatus("create");
        setVisible(true);
      },
      handleUpdate(mainComponent) {
        // debugger;
        setStatus("update");
        setVisible(true);
      },
    }),
    []
  );

  const handleOk = async () => {
    try {
      setLoading(true);

      if (status === "create") {
        onSubmitOk(singleProducts);
      }
      //  else {
      //   onUpdate();
      // }
      // message.success(
      //   status === "create" ? "Thêm mới thành công" : "Cập nhật thành công"
      // );
      handleClose();
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setVisible(false);
    onClose();
  };

  return (
    <Modal
      open={visible}
      onCancel={handleClose}
      onOk={handleOk}
      width={1200}
      confirmLoading={loading}
      title={
        status === "create" ? "Thêm sản phẩm đơn" : "Cập nhật sản phẩm đơn"
      }
      style={{ top: 20 }}
    >
      <SingleProductTab
        isChooseMode
        onHandleChooseSingleProduct={(products) => {
          console.log("Product nhận đc là", products);
          // singleProducts.push(...products);

          setSingleProducts(cloneDeep(products));
        }}
        singleProducts={singleProducts}
      />
    </Modal>
  );
});
