import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const roleApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/role",
      params,
    }),
  findOne: (roleId: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/role/${roleId}`,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/role",
      data,
      method: "post",
    }),
  getPermission: (): AxiosPromise<any> =>
    request({
      url: `/v1/admin/role/permissions`,
    }),
  importPermission: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/role/permissions/import`,
      method: "post",
      data,
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/role/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/role/${id}`,
      method: "delete",
    }),
};
