import { materialGroupApi } from "api/materialGroup.api";
import { useState } from "react";
import { MaterialGroup } from "types/materialGroup";
import { QueryParam } from "types/query";

export interface MaterialGroupQuery extends QueryParam {}

interface UseMaterialGroupProps {
  initQuery: MaterialGroupQuery;
}

export const useMaterialGroup = ({ initQuery }: UseMaterialGroupProps) => {
  const [data, setData] = useState<MaterialGroup[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<MaterialGroupQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await materialGroupApi.findAll(query);

      setData(data.materialGroups);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return { materialGroups: data, total, fetchData, loading, setQuery, query };
};
