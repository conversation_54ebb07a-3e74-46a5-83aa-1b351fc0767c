import { Project } from "./project";
import { FileAttach } from "./fileAttach";
import { Dictionary } from "./dictionary";
import { Staff } from "./staff";
import { ApprovalHistory } from "./approvalHistory";

export interface Submittal {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  code: string; // mã
//   status: SubmittalStatus; // trạng thái
  content: string; // nội dung
  plannedSubmitAt: number; // Ngày đệ trình dự kiến
  plannedOrderAt: number; // Ngày đặt hàng dự kiến
  transportationTime: string; // Thời gian vận chuyển
  transferToSiteAt: number; // Ngày chuyến tới công trường
  installationAt: number; // Ngày lắp đặt
  approvalSubmitAt: number; // Ngày đệ trình phê duyệt
  actualApprovalAt: number; // <PERSON><PERSON><PERSON> thực tế phê duyệt
  note: string; // nội dung
  version: number;
  isAllApprove: boolean;
  project: Project;
  taskCategory: Dictionary; // loại công việc
  fileAttaches: FileAttach[];
  parent: Submittal;
  children: Submittal[];
  followStaffs: Staff[];
  approveStaffs: Staff[];
  approvalHistories: ApprovalHistory[];
  // info create update
  createdBy: Staff;
  updatedBy: Staff;
}
