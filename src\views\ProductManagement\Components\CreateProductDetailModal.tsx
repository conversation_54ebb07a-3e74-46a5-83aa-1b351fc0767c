import { Col, Form, Input, Modal, Row, Select } from "antd";
import { ComponentsSelector } from "components/Selector/ComponentsSelector";
import React, { forwardRef, useImperativeHandle, useState } from "react";
import {
  ProductDetail,
  ProductDetailType,
  ProductDetailTypeTrans,
} from "types/product";
import { requiredRule } from "utils/validateRule";
export interface ProductDetailModal {
  handleCreate: () => void;
  handleUpdate: (product: ProductDetail) => void;
}
interface ComponentModalProps {
  onClose: () => void;
  onSubmitOk: (item: ProductDetail) => void;
}

const CreateProductDetailModal = forwardRef(
  ({ onClose, onSubmitOk }: ComponentModalProps, ref) => {
    const [form] = Form.useForm<ProductDetail>();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<"update" | "create">("create");
    const handleClose = () => {
      setVisible(false);
      form.resetFields();
      onClose();
    };

    useImperativeHandle<any, ProductDetailModal>(
      ref,
      () => ({
        handleCreate() {
          form.resetFields();
          setVisible(true);
          setStatus("create");
        },
        handleUpdate(product) {
          console.log("Component là", product);
          form.setFieldsValue({
            ...product,
          });

          setVisible(true);
          setStatus("update");
        },
      }),
      []
    );
    const createData = async () => {
      const valid = await form.validateFields();
      const data = form.getFieldsValue();
      // const { parentId, boms, fileAttachIcon, ...restData } = data;
      // console.log("When create data", fileAttachIcon);
      try {
        //   const res = await productApi.create({
        //     product: {
        //       ...restData,
        //     },
        //     parentId,
        //     fileAttachIconId: fileAttachIcon.id,
        //     boms:
        //       billOfMaterial.map((item) => ({
        //         material: item.material,
        //         quantity: item.quantity,
        //       })) || [],
        //   });
        //   message.success("Tạo thành công");
        handleClose();
        console.log(data);
        onSubmitOk(data);
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const valid = await form.validateFields();
      const data = form.getFieldsValue();
      //   setLoading(true);
      //   console.log("What is in form", data);
      //   const { id, parentId, boms, ...restData } = data;
      //   console.log("Rest data là", restData);
      //   try {
      //     console.log("Data when update", data);
      //     const res = await productApi.update(id, {
      //       product: { ...restData },
      //       boms: boms.map((item) => ({
      //         materialCode: item.material.code,
      //         quantity: item.quantity,
      //       })),
      //       parentId: data.parentId,
      //     });
      //     message.success("Cập nhật thành công");
      //     handleClose();
      //     onSubmitOk();
      //   } finally {
      //     setLoading(false);
      //   }
    };
    return (
      <Modal
        onCancel={() => {
          onClose?.();
          setVisible(false);
        }}
        open={visible}
        title={
          status == "create" ? "Tạo chi tiết sản phẩm" : "Sửa chi tiết sản phẩm"
        }
        style={{ top: 20 }}
        width={1200}
        confirmLoading={loading}
        onOk={() => {
          status == "create" ? createData() : updateData();
        }}
      >
        <Form layout="vertical" form={form}>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label="Tên" name="name" rules={[requiredRule]}>
                <Input placeholder="" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="Tên nhóm"
                name="groupName"
                rules={[requiredRule]}
              >
                <Input placeholder="" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="Lớp" name="layer" rules={[requiredRule]}>
                <Input placeholder="" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="Loại" name="type">
                <Select
                  allowClear
                  options={Object.values(ProductDetailTypeTrans).map(
                    (item) => ({
                      label: item.label,
                      value: item.value,
                    })
                  )}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="componentIds" label="Thành phần">
                <ComponentsSelector multiple />
              </Form.Item>
            </Col>
            {[
              status === "update" && (
                <Col span={8}>
                  <Form.Item
                    className="hidden"
                    label="id"
                    name="id"
                    rules={[requiredRule]}
                  >
                    <Input placeholder="" />
                  </Form.Item>
                </Col>
              ),
            ]}
          </Row>
        </Form>
      </Modal>
    );
  }
);

export default CreateProductDetailModal;
