import { Col, Form, Input, message, Modal, Row } from "antd";
import { Rule } from "antd/lib/form";
import { departmentApi } from "api/department.api";
import CustomInput from "components/Input/CustomInput";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { ModalStatus } from "types/modal";
import { Department } from "types/department";

const rules: Rule[] = [{ required: true }];

export interface DepartmentModalRef {
  handleCreate: () => void;
  handleUpdate: (department: Department) => void;
}
interface DepartmentModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
}

export const DepartmentModal = React.forwardRef(
  ({ onClose, onSubmitOk }: DepartmentModalProps, ref) => {
    const [form] = Form.useForm<Department>();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");
    const [selectedDepartment, setSelectedDepartment] = useState<Department>();

    useImperativeHandle<any, DepartmentModalRef>(
      ref,
      () => ({
        handleCreate() {
          form.resetFields();
          setVisible(true);
          setStatus("create");
        },
        handleUpdate(department: Department) {
          setSelectedDepartment(department);
          form.setFieldsValue({ ...department });
          setVisible(true);
          setStatus("update");
        },
      }),
      []
    );

    const createData = async () => {
      const valid = await form.validateFields();
      const data = { department: form.getFieldsValue() };

      setLoading(true);
      try {
        const res = await departmentApi.create(data);
        message.success("Tạo phòng ban thành công!");
        onClose();
        onSubmitOk();
        setVisible(false);
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const valid = await form.validateFields();
      const data = { department: form.getFieldsValue() };
      setLoading(true);
      try {
        const res = await departmentApi.update(
          selectedDepartment?.id || 0,
          data
        );
        message.success("Cập nhật phòng ban thành công!");
        onClose();
        onSubmitOk();
        setVisible(false);
      } finally {
        setLoading(false);
      }
    };

    return (
      <Modal
        className="footer-full"
        onCancel={() => {
          onClose?.();
          setVisible(false);
        }}
        centered
        visible={visible}
        title={status == "create" ? "Tạo phòng ban" : "Cập nhật phòng ban"}
        style={{ top: 20 }}
        width={550}
        confirmLoading={loading}
        cancelButtonProps={{ style: { display: "none" } }}
        okText={status == "create" ? "Tạo" : "Cập nhật"}
        onOk={() => {
          status == "create" ? createData() : updateData();
        }}
        getContainer={() => {
          return document.getElementById("App") as HTMLElement;
        }}
      >
        <Form layout="vertical" form={form}>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item name="code" label="Mã phòng ban">
                <CustomInput placeholder="Mã phòng ban nếu không nhập hệ thống sẽ tự tạo" />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item name="name" label="Tên phòng ban" rules={rules}>
                <CustomInput placeholder="Nhập tên phòng ban" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
);
