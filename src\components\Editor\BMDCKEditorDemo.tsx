import React, { useState } from 'react';
import { Card, Button, Space } from 'antd';
import { BMDCKEditor, IBMDCKEditorRef } from './BMDCKEditor';

const BMDCKEditorDemo: React.FC = () => {
  const [content, setContent] = useState('<p>Nội dung mẫu cho CKEditor</p>');
  const [disabled, setDisabled] = useState(false);
  const editorRef = React.useRef<IBMDCKEditorRef>(null);

  const handleSetContent = () => {
    editorRef.current?.setContent('<p>Nội dung mới được set từ bên ngoài</p>');
  };

  const handleGetContent = () => {
    const currentContent = editorRef.current?.getContent();
    console.log('Current content:', currentContent);
    alert('Nội dung hiện tại: ' + currentContent);
  };

  return (
    <div style={{ padding: '20px' }}>
      <Card title="BMDCKEditor Demo" style={{ marginBottom: '20px' }}>
        <Space style={{ marginBottom: '16px' }}>
          <Button onClick={handleSetContent}>Set Content</Button>
          <Button onClick={handleGetContent}>Get Content</Button>
          <Button onClick={() => setDisabled(!disabled)}>
            {disabled ? 'Enable' : 'Disable'} Editor
          </Button>
        </Space>

        <BMDCKEditor
          ref={editorRef}
          value={content}
          onChange={setContent}
          placeholder="Nhập nội dung..."
          disabled={disabled}
          inputHeight={400}
          label="Rich Text Editor"
        />

        <div style={{ marginTop: '20px' }}>
          <h4>Preview:</h4>
          <div 
            style={{ 
              border: '1px solid #d9d9d9', 
              padding: '16px', 
              borderRadius: '6px',
              minHeight: '100px'
            }}
            dangerouslySetInnerHTML={{ __html: content }}
          />
        </div>
      </Card>
    </div>
  );
};

export default BMDCKEditorDemo; 