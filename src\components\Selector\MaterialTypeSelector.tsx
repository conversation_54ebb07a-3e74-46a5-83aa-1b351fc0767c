import { Select } from "antd";
import { DefaultOptionType } from "antd/es/select";
import { SelectProps } from "antd/lib";
import { useColor } from "hooks/useColor";
import { useMaterial } from "hooks/useMaterial";
import { useMaterialGroup } from "hooks/useMaterialGroup";
import { useMaterialType } from "hooks/useMaterialType";
import { debounce, uniqBy } from "lodash";
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
} from "react";
import { Material } from "types/material";
import { MaterialGroup } from "types/materialGroup";
import { MaterialType } from "types/materialType";
import { QueryParams2 } from "types/query";

type CustomFormItemProps = {
  value?: number;
  initQuery?: QueryParams2;
  disabled?: boolean;
  selectedColor?: Material[];
  multiple?: boolean;
  onChange?: (value: any) => void;
  selectProps?: SelectProps;
  initOptionItem?: MaterialType | MaterialType[];
  valueIsOption?: boolean;
  allowClear?: boolean;
  placeholder?: string;
  isJustMaterialType?: boolean;
};

export interface MaterialTypeSelector {
  refresh(): void;
}

/**
 * A demo that changes it's number value on click.
 * @param value initialValue passed by parent Form.Item.
 * @param onChange a callback for Form.Item to read its child component's value.
 * @constructor
 */
export const MaterialTypeSelector = forwardRef(
  (
    {
      value,
      onChange,
      initQuery,
      disabled,
      multiple = false,
      selectedColor,
      initOptionItem,
      valueIsOption,
      selectProps,
      allowClear = true,
      placeholder = "Chọn loại NVL",
      isJustMaterialType = false,
    }: CustomFormItemProps,
    ref
  ) => {
    const { materialTypes, total, loading, fetchData, query } = useMaterialType(
      {
        initQuery: {
          page: 1,
          limit: 50,
          ...initQuery,
        },
      }
    );

    useImperativeHandle<any, MaterialTypeSelector>(
      ref,
      () => ({
        refresh() {
          fetchData();
        },
      }),
      []
    );
    useEffect(() => {
      fetchData();
    }, []);
    const debounceSearch = useCallback(
      debounce((keyword) => {
        query.search = keyword;
        fetchData();
      }, 300),
      [query]
    );

    const options = useMemo(() => {
      let data = [...materialTypes];
      if (initOptionItem) {
        if ((initOptionItem as MaterialType[])?.length) {
          data = data.concat(initOptionItem);
        } else {
          data.push(initOptionItem as MaterialType);
        }
      }

      return uniqBy(data, (data) => data.id);
    }, [materialTypes, initOptionItem]);

    return (
      <Select<any, DefaultOptionType>
        mode={multiple ? "multiple" : undefined}
        disabled={disabled}
        loading={loading}
        style={{ width: "100%", minWidth: 200 }}
        allowClear={allowClear}
        onSearch={debounceSearch}
        showSearch
        placeholder={placeholder}
        filterOption={false}
        value={value}
        onChange={(v, opts) => {
          if (v === undefined || v.length == 0) {
            debounceSearch(v);
          }
          if (valueIsOption) {
            if (opts instanceof Array) {
              onChange?.(opts?.map((v) => v.item));
            } else {
              onChange?.(opts?.item);
            }
          } else {
            onChange?.(v);
          }
        }}
        {...selectProps}
      >
        {options?.map((item) => (
          <Select.Option
            item={item}
            value={isJustMaterialType ? item.name : item.id}
            key={item.id}
          >
            <div className="flex items-center gap-2">
              <span>{item.name}</span>
            </div>
          </Select.Option>
        ))}
      </Select>
    );
  }
);
