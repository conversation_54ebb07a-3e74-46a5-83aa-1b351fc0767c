import dayjs from "dayjs";

export function formatDate(timestamp: number, format = "DD/MM/YYYY") {
  return dayjs.unix(timestamp).format(format);
}

export function formatDateTime(timestamp: number) {
  return dayjs.unix(timestamp).format("HH:mm, DD/MM/YYYY");
}
export function formatFullDateTime(timestamp: number) {
  return dayjs.unix(timestamp).format("HH:mm:ss, DD/MM/YYYY");
}

export function formatDateDay(timestamp: number) {
  return dayjs.unix(timestamp).format("dddd, MM-DD-YYYY");
}

export function generateTimeSeries(
  step: number,
  addHour = 0,
  format = "h:mm a"
) {
  const dt = new Date(1970, 0, 1);
  const rc = [];
  while (dt.getDate() === 1) {
    rc.push(dayjs(dt).add(addHour, "hour").format(format));
    // rc.push(dt.toLocaleTimeString('en-US'))
    dt.setMinutes(dt.getMinutes() + step);
  }
  return rc;
}

export function generateDuration() {
  let seconds = 300;
  const durations = [];
  while (seconds <= 60 * 60 * 12) {
    const h = Math.floor(seconds / 3600);
    const m = (seconds % 3600) / 60;
    let label = secondToMinuteString(seconds);
    durations.push({
      label,
      value: seconds,
    });
    seconds += 300;
  }
  return durations;
}

export function secondToMinuteString(second: number) {
  const h = Math.floor(second / 3600);
  const m = (second % 3600) / 60;
  let label = "";
  if (h == 0) {
    label = `${m}m`;
  } else {
    if (m == 0) {
      label = `${h}h`;
    } else {
      label = `${h}h ${m}m`;
    }
  }
  return label;
}

export function handleDateRange(startAt: number, endAt: number) {
  return [dayjs(startAt * 1000), dayjs(endAt * 1000)];
}

export const dateRanges: any = {
  "Hôm nay": [dayjs().startOf("day"), dayjs().endOf("day")],
  "Hôm qua": [
    dayjs().subtract(1, "days").startOf("day"),
    dayjs().subtract(1, "days").endOf("day"),
  ],
  "Tuần này": [dayjs().startOf("week"), dayjs().endOf("week")],
  "Tuần trước": [
    dayjs().subtract(1, "weeks").startOf("week"),
    dayjs().subtract(1, "weeks").endOf("week"),
  ],
  "Tháng này": [dayjs().startOf("month"), dayjs().endOf("month")],
  "Tháng trước": [
    dayjs().subtract(1, "months").startOf("month"),
    dayjs().subtract(1, "months").endOf("month"),
  ],
};

/**
 * Chuyển đổi ngày từ Excel thành ngày trong hệ thống
 * @param excelDate
 * @returns
 */
export function excelDateToDayjs(excelDate: number | string): string {
  if (typeof excelDate === "string") {
    return dayjs(excelDate, "DD/MM/YYYY").format("YYYY-MM-DD");
  }
  const baseDate = new Date(1900, 0, 1);
  const daysToAdd = excelDate - (excelDate > 60 ? 1 : 0);
  baseDate.setDate(baseDate.getDate() + daysToAdd - 1);
  return dayjs(baseDate).format("YYYY-MM-DD");
}
