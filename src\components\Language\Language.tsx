import { Dropdown } from "antd";
import { langResources } from "./langResource";
import { IoMdArrowDropdown } from "react-icons/io";

const Language = () => {
  return (
    <Dropdown
      menu={{
        items: Object.values(langResources).map((it) => ({
          key: it.value,
          label: (
            <div className="flex gap-2 items-center">
              <img src={it.icon} className="h-[20px] rounded-full" />
              <span>{it.text2}</span>
            </div>
          ),
        })),
      }}
      trigger={["click"]}
    >
      <div className="nav-bar-item-wrapper flex gap-1 items-center h-[44px] p-[10px] cursor-pointer">
        <img
          src={langResources.vi.icon}
          className="h-full object-contain rounded-full"
        />
        <span className="language-text hidden md:block">
          {langResources.vi.text}
        </span>
        <IoMdArrowDropdown className="language-icon hidden md:block" />
      </div>
    </Dropdown>
  );
};

export default Language;
