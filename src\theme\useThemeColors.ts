import { useTheme } from 'context/ThemeContext';
import { getColor } from './colors';

/**
 * Hook to get theme colors based on current theme mode
 * @returns Object with color getter function
 * @example
 * const { color } = useThemeColors();
 * const primaryColor = color('branding.primary');
 * const neutralColor = color('neutral.n3');
 */
export const useThemeColors = () => {
  const { darkMode } = useTheme();
  
  const color = (colorPath: string): string => {
    return getColor(colorPath, darkMode);
  };
  
  return { color, isDark: darkMode };
};

export default useThemeColors;