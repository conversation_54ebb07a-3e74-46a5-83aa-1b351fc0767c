import { AxiosPromise } from "axios";
import { request } from "utils/request";

export const seasonApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/season",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/season",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/season/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/season/${id}`,
      method: "delete",
    }),
};
