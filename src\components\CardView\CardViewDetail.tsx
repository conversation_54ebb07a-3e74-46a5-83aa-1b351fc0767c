import { Collapse, Divider } from "antd";
import { ReactComponent as ArrowIcon } from "assets/svgs/arrowUp.svg";
import calendarIcon from "assets/svgs/calendar.svg";
import camera360Icon from "assets/svgs/camera360.svg";
import cameraTimeLapseIcon from "assets/svgs/cameraTimeLapse.svg";
import manageByIcon from "assets/svgs/manageBy.svg";
import clsx from "clsx";
import ReactECharts from "echarts-for-react";
import { settings } from "settings";
import "./CardViewStyle.scss";
import { formatNumber, formatVND } from "utils";
import { EChartsOption } from "echarts";
import { useTheme } from "context/ThemeContext";
import { ColorThemes } from "utils/theme";
import { Project, ProjectStatusTrans } from "types/project";
import { useNavigate } from "react-router-dom";
import { useProject } from "hooks/useProject";
import Camera360Icon from "assets/svgs/Camera360";
import CameraTimeLapseIcon from "assets/svgs/CameratTimeLapse";
import CalendarIcon from "assets/svgs/CalendarIcon";
import ManageByIcon from "assets/svgs/ManageBy";
import { formatDateTime } from "utils/date";

interface CardViewProps {
  project: Project;
}

const CardViewDetail = ({ project }: CardViewProps) => {
  const navigate = useNavigate();
  const { fetchDataDetail } = useProject({
    initQuery: { page: 1, limit: 10, search: "" },
  });

  const rings = [
    {
      value: 205,
      total: 300,
      name: "Công việc",
      color: ["#4F46E5", "#7C3AED"],
    },
    {
      value: 150,
      total: 200,
      name: "Hoàn thành",
      color: ["#10B981", "#059669"],
    },
    { value: 75, total: 100, name: "Đang chờ", color: ["#F59E0B", "#D97706"] },
  ];

  const { darkMode } = useTheme();

  const platformOptions: EChartsOption = {
    series: [
      {
        type: "pie",
        radius: ["85%", "100%"],
        center: ["50%", "50%"],
        startAngle: 90,
        data: [
          {
            value: 82,
            itemStyle: {
              color: darkMode
                ? ColorThemes.dark.task.normal
                : ColorThemes.light.task.normal,
            },
          },
          {
            value: 18,
            itemStyle: {
              color: darkMode
                ? ColorThemes.dark.neutral.n1
                : ColorThemes.light.neutral.n1,
            },
          },
        ],
        label: {
          show: false,
        },
        labelLine: {
          show: false,
        },
      },
      {
        type: "pie",
        radius: ["67%", "80%"],
        center: ["50%", "50%"],
        startAngle: 90,
        data: [
          {
            value: 12,
            itemStyle: {
              color: darkMode
                ? ColorThemes.dark.task.speedup
                : ColorThemes.light.task.speedup,
            },
          },
          {
            value: 88,
            itemStyle: {
              color: darkMode
                ? ColorThemes.dark.neutral.n1
                : ColorThemes.light.neutral.n1,
            },
          },
        ],
        label: {
          show: false,
        },
        labelLine: {
          show: false,
        },
      },
    ],
    graphic: [
      {
        type: "text",
        style: {
          text: "205",
          fontSize: 20,
          fontWeight: "bold",
          fill: darkMode
            ? ColorThemes.dark.neutral.n8
            : ColorThemes.light.neutral.n8,
          y: 32,
          x: 32,
        },
      },
      {
        type: "text",
        style: {
          text: "Công việc",
          fontSize: 12,
          fontWeight: "lighter",
          fill: darkMode
            ? ColorThemes.dark.neutral.n8
            : ColorThemes.light.neutral.n8,
          y: 57,
          x: 22,
        },
      },
    ],
  };

  const handleNavigateToDetail = async () => {
    try {
      // Call API findOne để fetch detail (chỉ call API, chưa cần gắn data)
      await fetchDataDetail(project.id);

      // Navigate sang ProjectDetailPage
      navigate(`/project-detail/${project.id}`);
    } catch (error) {
      console.error("Error navigating to project detail:", error);
    }
  };

  return (
    <div className="card-view">
      <div className="flex justify-between gap-2">
        <div className="flex flex-col md:flex-row gap-4 w-full">
          <div className="w-full">
            <div
              className="font-bold text-[20px] md:mt-2 uppercase"
              // onClick={handleNavigateToDetail}
            >
              Tổng quan dự án
            </div>
            <div className="flex gap-2 items-center w-full">
              <span className="flex-shrink-0">
                Thống kê lúc: {formatDateTime(project.updatedAt)}
              </span>
            </div>
          </div>
        </div>
      </div>
      <div className="mt-4">
        <div className="flex flex-col md:flex-row justify-between md:items-center gap-4 md:gap-2">
          <div className="flex items-center gap-[20px]">
            <ReactECharts
              option={platformOptions}
              style={{ width: "100px", height: "100px" }}
              opts={{ renderer: "canvas" }}
            />
            <div className="flex flex-col md:flex-row gap-2 md:gap-[20px]">
              <div className="relative w-[160px]">
                <div className="font-bold">Tiến độ kế hoạch</div>
                <div className="flex gap-2 items-center w-[calc(100%-30px)]">
                  <div
                    className="h-2 transition-all"
                    style={{
                      width: `${82}%`,
                      backgroundColor: "var(--color-task-normal)",
                    }}
                  ></div>
                  <div
                    className="font-bold w-[0px]"
                    style={{
                      color: "var(--color-neutral-n4)",
                    }}
                  >
                    82%
                  </div>
                </div>
              </div>
              <div className="relative w-[160px]">
                <div className="font-bold">Tiến độ thực tế</div>
                <div className="flex gap-2 items-center w-[calc(100%-30px)]">
                  <div
                    className="h-2 transition-all"
                    style={{
                      width: `${12}%`,
                      backgroundColor: "var(--color-task-speedup)",
                    }}
                  ></div>
                  <div
                    className="font-bold w-[0px]"
                    style={{
                      color: "var(--color-neutral-n4)",
                    }}
                  >
                    12%
                  </div>
                </div>
              </div>
              <div>
                <div className="font-bold">Tình trạng</div>
                <div className="task-status">
                  <div
                    className="absolute left-0 top-0 w-full h-full opacity-15"
                    style={{
                      backgroundColor: `var(${
                        ProjectStatusTrans[project.status].color
                      })`,
                    }}
                  ></div>
                  <div
                    className="task-status-label"
                    style={{
                      color: `var(${ProjectStatusTrans[project.status].color})`,
                    }}
                  >
                    {ProjectStatusTrans[project.status].label}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="w-full md:w-[200px] flex flex-col gap-2">
            <div className="flex gap-2 justify-between">
              <span className="whitespace-nowrap">Ngân sách</span>
              <span className="text-right font-bold">
                {formatVND(project?.budget ?? 0)} đ
              </span>
            </div>
            <div className="flex gap-2 justify-between">
              <span>Chi</span>
              <span
                className="text-right font-bold"
                style={{ color: "var(--color-task-slow)" }}
              >
                --
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CardViewDetail;
