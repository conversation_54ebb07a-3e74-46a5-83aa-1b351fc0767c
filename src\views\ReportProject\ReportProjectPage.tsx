import React from "react";
import { Card, Select, Space, Button, Tooltip, DatePicker } from "antd";
import CustomButton from "components/Button/CustomButton";
import PageTitle from "components/PageTitle/PageTitle";
import CustomInput from "components/Input/CustomInput";
import CustomDatePicker from "components/Input/CustomDatePicker";
import { StaffSelector } from "components/Selector/StaffSelector";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import { Pagination } from "components/Pagination";
import { observer } from "mobx-react";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { getTitle } from "utils";
import { usereport } from "hooks/useReport";
import { Report, ReportType } from "types/report";
import dayjs from "dayjs";
import { PermissionNames } from "types/PermissionNames";
import { checkRoles } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import ActiveStatusTag from "components/ActiveStatus/ActiveStatusTag";
import LockButton from "components/Button/LockButton";
import PencilIcon from "assets/svgs/PencilIcon";
import { message } from "antd";
import { reportApi } from "api/report.api";
import EditButton from "components/Button/EditButton";
import QueryLabel from "components/QueryLabel/QueryLabel";

const REPORT_TYPE_LABELS: Record<ReportType, string> = {
  [ReportType.Daily]: "Báo cáo ngày",
  [ReportType.Weekly]: "Báo cáo tuần",
  [ReportType.Monthly]: "Báo cáo tháng",
};

export const ReportProjectPage = observer(({ title = "" }) => {
  const navigate = useNavigate();

  const { haveEditPermission, haveBlockPermission, haveViewAllPermission } =
    checkRoles(
      {
        edit: PermissionNames.projectReportEdit,
        block: PermissionNames.projectReportEdit,
        viewAll: PermissionNames.projectReportViewAll,
      },
      permissionStore.permissions
    );

  // Filter state
  const [search, setSearch] = React.useState("");
  const [inCharge, setInCharge] = React.useState<number | undefined>();
  const [status, setStatus] = React.useState<boolean | undefined>();
  const [fromDate, setFromDate] = React.useState<any>(null);
  const [toDate, setToDate] = React.useState<any>(null);

  // Query state for API
  const { reports, total, fetchData, loading, setQuery } = usereport({
    initQuery: {
      limit: 20,
      page: 1,
      isAdmin: haveViewAllPermission ? true : undefined,
    },
  });
  const [page, setPage] = React.useState(1);
  const [limit, setLimit] = React.useState(20);

  useEffect(() => {
    document.title = getTitle(title);
  }, [title]);

  // Fetch data on mount and when filters change
  useEffect(() => {
    setQuery((prev) => ({
      ...prev,
      page,
      limit,
      search: search || undefined,
      createdBy: inCharge || undefined,
      isDeleted: typeof status === "boolean" ? status : undefined,
      startAt: fromDate ? dayjs(fromDate).startOf("day").valueOf() : undefined,
      endAt: toDate ? dayjs(toDate).endOf("day").valueOf() : undefined,
    }));
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [search, inCharge, status, fromDate, toDate, page, limit]);

  const handleActiveReport = async (id: number, value: boolean) => {
    try {
      await reportApi.update(id, { report: { isDeleted: !value } });
      message.success(value ? "Khóa thành công" : "Mở khóa thành công");
      fetchData();
    } catch (error) {
      message.error("Có lỗi xảy ra khi thay đổi trạng thái");
    }
  };

  const handleRowClick = (record: Report) => {
    navigate(
      `/report/${PermissionNames.projectReportEdit.replace(
        ":id",
        record.id + ""
      )}`
    );
  };

  // Table columns
  const columns: CustomizableColumn<Report>[] = [
    {
      title: "Mã",
      dataIndex: "id",
      key: "id",
      align: "center",
      width: 80,
      render: (id, record) => (
        <div
          className="text-[#1677ff] cursor-pointer"
          onClick={(e) => {
            e.stopPropagation();
            handleRowClick(record);
          }}
        >
          {id}
        </div>
      ),
    },
    {
      title: "Mô tả",
      dataIndex: "note",
      key: "note",
      width: 250,
    },
    {
      title: "Người phụ trách",
      dataIndex: ["createdBy", "fullName"],
      key: "inCharge",
      width: 180,
      render: (_, record) => record.createdBy?.fullName || "",
    },
    {
      title: "Ngày bắt đầu",
      dataIndex: "startAt",
      key: "startAt",
      align: "center",
      width: 140,
      render: (value) => (value ? dayjs(value).format("DD/MM/YYYY") : ""),
    },
    {
      title: "Ngày kết thúc",
      dataIndex: "endAt",
      key: "endAt",
      align: "center",
      width: 140,
      render: (value) => (value ? dayjs(value).format("DD/MM/YYYY") : ""),
    },
    {
      title: "Trạng thái",
      dataIndex: "isDeleted",
      key: "status",
      align: "center",
      width: 130,
      render: (isDeleted) => <ActiveStatusTag isActive={!isDeleted} />,
    },
    {
      title: "Loại báo cáo",
      dataIndex: "type",
      key: "reportType",
      width: 160,
      render: (type: ReportType) => REPORT_TYPE_LABELS[type] || type,
    },
    {
      title: "Xử lý",
      key: "actions",
      fixed: "right",
      width: 100,
      align: "center",
      alwaysVisible: true,
      render: (_, record) => (
        <Space>
          {haveEditPermission && (
            <EditButton
              onClick={(e) => {
                e.stopPropagation();
                navigate(
                  `/report/${PermissionNames.projectReportEdit.replace(
                    ":id",
                    record!.id + ""
                  )}?update=1`
                );
              }}
            />
          )}
          {haveBlockPermission && (
            <LockButton
              isActive={!record.isDeleted}
              onAccept={() => handleActiveReport(record.id, !record.isDeleted)}
              modalTitle={`${!record.isDeleted ? "Khóa" : "Mở khóa"} báo cáo: ${
                record.note
              }`}
              modalContent={
                <>
                  <div>
                    Khi {!record.isDeleted ? "khóa" : "mở khóa"} báo cáo này,
                    trạng thái của báo cáo sẽ được thay đổi.
                    <br />
                    Bạn có chắc chắn muốn{" "}
                    {!record.isDeleted ? "khóa" : "mở khóa"} báo cáo này?
                  </div>
                </>
              }
            />
          )}
        </Space>
      ),
    },
  ];

  // Filter bar and table UI
  return (
    <div>
      <PageTitle
        title={title}
        breadcrumbs={["Báo cáo", title]}
        extra={
          <Space>
            <CustomButton
              size="small"
              showPlusIcon
              onClick={() => {
                navigate(`/report/${PermissionNames.projectReportAdd}`);
              }}
            >
              Tạo báo cáo
            </CustomButton>
            <CustomButton size="small" icon={null} onClick={() => {}}>
              Nhập excel
            </CustomButton>
          </Space>
        }
      />
      <div className="app-container">
        <Card>
          <div className="flex gap-[16px] items-end pb-[12px] justify-between flex-wrap">
            <div className="flex flex-wrap gap-[16px] items-end max-w-full">
              <div className="w-[200px]">
                <QueryLabel>Tìm kiếm</QueryLabel>
                <CustomInput
                  placeholder="Tìm kiếm"
                  value={search}
                  onChange={setSearch}
                  allowClear
                />
              </div>
              <div className="w-[200px]">
                <QueryLabel>Người phụ trách</QueryLabel>
                <StaffSelector
                  value={inCharge}
                  onChange={setInCharge}
                  allowClear
                  placeholder="Người phụ trách"
                />
              </div>
              <div className="w-[200px]">
                <QueryLabel>Trạng thái</QueryLabel>
                <Select
                  value={status}
                  options={[
                    { label: "Hoạt động", value: false },
                    { label: "Bị khóa", value: true },
                  ]}
                  placeholder="Trạng thái"
                  allowClear
                  onChange={setStatus}
                />
              </div>
              <div className="w-[200px]">
                <QueryLabel>Từ ngày</QueryLabel>
                <DatePicker
                  className="w-full"
                  value={fromDate}
                  onChange={setFromDate}
                  placeholder="Từ ngày"
                  format="DD/MM/YYYY"
                />
              </div>
              <div className="w-[200px]">
                <QueryLabel>Đến ngày</QueryLabel>
                <DatePicker
                  className="w-full"
                  value={toDate}
                  onChange={setToDate}
                  placeholder="Đến ngày"
                  format="DD/MM/YYYY"
                />
              </div>
              <CustomButton
                onClick={() => {
                  setPage(1);
                  fetchData();
                }}
              >
                Áp dụng
              </CustomButton>
              <CustomButton
                variant="outline"
                onClick={() => {
                  setSearch("");
                  setInCharge(undefined);
                  setStatus(undefined);
                  setFromDate(null);
                  setToDate(null);
                  setPage(1);
                  setLimit(20);
                }}
              >
                Bỏ lọc
              </CustomButton>
            </div>
            <CustomButton onClick={() => {}}>Xuất excel</CustomButton>
          </div>
          <CustomizableTable
            columns={columns}
            dataSource={reports}
            rowKey="id"
            loading={loading}
            pagination={false}
            scroll={{ x: 1200 }}
            bordered
            displayOptions
            tableId="report-project-page"
            onChange={() => {}}
            onRowClick={handleRowClick}
          />
          <Pagination
            currentPage={page}
            defaultPageSize={limit}
            total={total}
            onChange={({ limit, page }) => {
              setPage(page);
              setLimit(limit);
            }}
          />
        </Card>
      </div>
    </div>
  );
});
