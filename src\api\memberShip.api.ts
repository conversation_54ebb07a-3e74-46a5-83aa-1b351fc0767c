import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const memberShipApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/memberShip",
      params,
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/memberShip/${id}`,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/memberShip",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/memberShip/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/memberShip/${id}`,
      method: "delete",
    }),
  isActive: (id: number, isActive: boolean): AxiosPromise<any> =>
    request({
      url: `/v1/admin/memberShip/${id}/isActive`,
      method: "patch",
      data: { isActive },
    }),
};
