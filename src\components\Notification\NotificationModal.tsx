import React from "react";
import { ReactComponent as BellIcon } from "assets/svgs/bell.svg";
import "./NotificationModal.scss";
import { Notification } from "types/notification";
import { formatDate } from "utils/date";
import CalendarIcon from "assets/svgs/CalendarIcon";
import { Empty } from "antd";
import BellNotificationIcon from "assets/svgs/BellNotification";
import { Project } from "types/project";

interface NotificationModalProps {
  notifications: Notification[];
  handleClickReadNotification: (
    notificationId: number,
    url: string,
    project?: Project
  ) => void;
}

const NotificationModal = ({
  notifications,
  handleClickReadNotification,
}: NotificationModalProps) => {
  return (
    <div className="notification-modal">
      {/* <div className="notification-title">Thông báo</div> */}
      {notifications.length === 0 ? (
        <div className="notification-empty">
          <Empty description="Không có thông báo" />
        </div>
      ) : (
        <div className="notification-list">
          {notifications.map((noti) => (
            <div
              className={`notification-item cursor-pointer ${
                noti.isRead
                  ? "notification-item--read"
                  : "notification-item--unread"
              }`}
              key={noti.id}
              onClick={() =>
                handleClickReadNotification(noti.id, noti.url, noti.project)
              }
            >
              <div className="notification-item-header">
                <div className="notification-item-icon">
                  <BellNotificationIcon
                    isRead={noti.isRead}
                    width={30}
                    height={30}
                  />
                </div>
                <div className="notification-item-info">
                  <div className="notification-item-title">{noti.title}</div>
                  <div className="notification-item-content">
                    {noti.content}
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2 mt-2">
                <CalendarIcon width={14} height={14} />
                <div className="text-[10px]">
                  {formatDate(noti.createdAt, "DD/MM/YYYY HH:mm")}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default NotificationModal;
