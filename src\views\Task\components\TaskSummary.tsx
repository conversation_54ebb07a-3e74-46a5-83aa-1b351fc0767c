import { taskApi } from "api/task.api";
import clsx from "clsx";
import { useEffect, useState } from "react";
import { TaskStatus, TaskStatusTrans } from "types/task";

type TaskType = "assign" | "create" | "follow" | "department";

interface Props {
  type: TaskType;
}

export const TaskSummary = ({ type }: Props) => {
  const [summaryData, setSummaryData] = useState([
    {
      status: TaskStatus.NotStarted,
      total: 0,
      className: "status-todo",
    },
    {
      status: TaskStatus.InProgress,
      total: 0,
      className: "status-doing",
    },
    {
      status: TaskStatus.Completed,
      total: 0,
      className: "status-done",
    },
    {
      status: TaskStatus.Reopen,
      total: 0,
      className: "status-reopen",
    },
  ]);

  const [myFollowingTasks, setMyFollowingTasks] = useState(0);
  const [myAssignedTasks, setMyAssignedTasks] = useState(0);
  const [myApprovedTasks, setMyApprovedTasks] = useState(0);

  // State cho department summary
  const [departmentSummary, setDepartmentSummary] = useState<
    Array<{ departmentName: string; total: number }>
  >([]);

  const fetchSummary = async () => {
    const { data } = await taskApi.findSummary({
      type,
    });

    if (type === "department") {
      // Nếu type là department, data sẽ là array chứa thông tin phòng ban
      setDepartmentSummary(data || []);
    } else {
      // Nếu không phải department, xử lý như cũ
      for (const item of summaryData) {
        const find = data.find((e: any) => e.status == item.status);

        item.total = find?.total || 0;
      }

      setSummaryData([...summaryData]);
    }
  };

  const fetchSummaryMe = async () => {
    const { data } = await taskApi.findSummaryMe();

    setMyFollowingTasks(data.followCount || 0);
    setMyAssignedTasks(data.assigneeCount || 0);
    setMyApprovedTasks(data.approvalCount || 0);
  };

  useEffect(() => {
    fetchSummary();
    fetchSummaryMe();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [type]); // Thêm type vào dependency array

  return (
    <>
      {type !== "department" && (
        <div className="task-summary-row mb-4">
          <div className="task-summary-box">
            <span className="task-summary-label">Tôi thực hiện:</span>
            <span className="task-summary-badge">{myAssignedTasks}</span>
          </div>
          <div className="task-summary-box">
            <span className="task-summary-label">Tôi theo dõi:</span>
            <span className="task-summary-badge">{myFollowingTasks}</span>
          </div>
          <div className="task-summary-box">
            <span className="task-summary-label">Tôi duyệt:</span>
            <span className="task-summary-badge">{myApprovedTasks}</span>
          </div>
        </div>
      )}

      {/* Hiển thị department summary chỉ khi type là department */}
      {type === "department" && departmentSummary.length > 0 && (
        <div className="department-summary-row mb-6">
          <h4 className="mb-2">Thống kê theo phòng ban:</h4>
          <div className="department-grid">
            {departmentSummary.map((item, index) => (
              <div key={index} className="department-summary-box">
                <div className="department-name">{item.departmentName}</div>
                <div className="department-total">{item.total}</div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Chỉ hiển thị task status row khi không phải type department */}
      {type !== "department" && (
        <div className="task-status-row mb-4">
          {summaryData.map((item, index) => (
            <div
              key={index}
              className={clsx("task-status-box", item.className)}
            >
              <div className="task-status-label">
                {TaskStatusTrans[item.status]}
              </div>
              <div className="task-status-value">{item.total}</div>
            </div>
          ))}
        </div>
      )}
    </>
  );
};
