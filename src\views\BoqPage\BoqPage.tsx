import { Card, Space, Spin, TableProps, Tag, Select, Col } from "antd";
import { useBoq } from "hooks/useBoq";
import React, { useEffect, useState } from "react";
import { BOQ, BOQDetail, BOQStatus, BOQStatusTrans, BOQType } from "types/boq";
import { getTitle } from "utils";
import { useNavigate } from "react-router-dom";
import PageTitle from "components/PageTitle/PageTitle";
import CustomButton from "components/Button/CustomButton";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import CustomInput from "components/Input/CustomInput";
import { Pagination } from "components/Pagination";
import { unixToDate } from "utils/dateFormat";
import { formatVND } from "utils";
import { PermissionNames } from "types/PermissionNames";
import { DictionaryDetail, DictionaryType } from "types/dictionary";
import { Project, ProjectStatus } from "types/project";
import { Unit, UnitType } from "types/unit";
import { Dictionary } from "types/dictionary";
import { ProjectCategory } from "types/projectCategory";
import { PlusOutlined } from "@ant-design/icons";
import { ProjectItem } from "types/projectItem";
import { checkRoles } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { formatFullDateTime } from "utils/date";
import BoqTable from "./BoqTable";
import { boqApi } from "api/boq.api";

interface BoqPageProps {
  title: string;
  projectId?: number;
  hidePageTitle?: boolean;
}

export const BoqPage = ({
  title = "",
  hidePageTitle = false,
}: BoqPageProps) => {
  const {
    haveAddPermission,
    haveDeletePermission,
    haveEditPermission,
    haveBlockPermission,
    haveViewAllPermission,
  } = checkRoles(
    {
      add: PermissionNames.boqAdd,
      edit: PermissionNames.boqEdit,
      delete: PermissionNames.boqDelete,
      viewAll: PermissionNames.boqViewAll,
    },
    permissionStore.permissions
  );

  const { boqs, totalBoq, fetchBoq, loadingBoq, setQueryBoq, queryBoq } =
    useBoq({
      initQuery: {
        limit: 10,
        page: 1,
        isAdmin: haveViewAllPermission ? true : undefined,
      },
    });

  const navigate = useNavigate();

  // State cho version selector (chỉ UI, chưa có logic)
  const [selectedVersion, setSelectedVersion] = useState<number>(1);
  const [versionLength, setVersionLength] = useState<number>(1); // default 1
  const [lastUpdateTime, setLastUpdateTime] = useState<number>(Date.now()); // Mock timestamp
  const [boqDetails, setBoqDetails] = useState<BOQ["boqDetails"]>([]);
  const [isEdit, setIsEdit] = useState(false);
  const [loadingBoqDetails, setLoadingBoqDetails] = useState(false);
  const [originalBoqDetails, setOriginalBoqDetails] = useState<
    BOQ["boqDetails"]
  >([]);

  // Add assignLevel function from CreateOrUpdateBoqPage
  const assignLevel = (boqDetails: BOQDetail[], level = 0): BOQDetail[] => {
    return boqDetails.map((it) => {
      if (it.children) {
        return { ...it, level, children: assignLevel(it.children, level + 1) };
      } else {
        return { ...it, level };
      }
    });
  };

  const getMaxVersion = (details: BOQDetail[]): number => {
    let max = 1;
    for (const item of details) {
      if (item.version && item.version > max) max = item.version;
      if (item.children && item.children.length > 0) {
        const childMax = getMaxVersion(item.children);
        if (childMax > max) max = childMax;
      }
    }
    return max;
  };

  // Add function to fetch BOQ details
  const fetchBoqDetails = async (
    version?: number,
    updateVersionLength = false
  ) => {
    try {
      setLoadingBoqDetails(true);
      const params = version ? { version } : undefined;
      const { data } = await boqApi.findBoqDetail(params);

      // Chỉ cập nhật versionLength khi updateVersionLength = true (tức là lần đầu hoặc khi cần reset)
      if (updateVersionLength) {
        const maxVersion = getMaxVersion(data.boqDetails || []);
        setVersionLength(maxVersion);
        setSelectedVersion(maxVersion);
      }

      setBoqDetails(assignLevel(data.boqDetails));
    } catch (error) {
      console.error("Error fetching BOQ details:", error);
      setBoqDetails([]);
    } finally {
      setLoadingBoqDetails(false);
    }
  };

  useEffect(() => {
    document.title = getTitle(title);
  }, []);

  useEffect(() => {
    fetchBoq();
    // Lần đầu tiên, cần lấy versionLength
    fetchBoqDetails(undefined, true);
  }, []);

  const handleTableChange: TableProps<any>["onChange"] = (
    pagination,
    filters,
    sorter
  ) => {
    if (!Array.isArray(sorter)) {
      const fieldMap: Record<string, string> = {
        code: "boq.code",
        name: "boq.name",
        createdAt: "boq.createdAt",
      };
      const columnKey = sorter.field || sorter.column?.key;

      if (!sorter.order) {
        queryBoq.queryObject = undefined;
        setQueryBoq({ ...queryBoq });
      } else {
        const order = sorter.order === "ascend" ? "ASC" : "DESC";
        const field = fieldMap[columnKey as string];

        const newQueryObject = JSON.stringify([
          {
            type: "sort",
            field,
            value: order,
          },
        ]);
        queryBoq.queryObject = newQueryObject;
        setQueryBoq({ ...queryBoq });
      }
      fetchBoq();
    }
  };

  const handleRowClick = (record: BOQ) => {
    navigate(`/boq/${PermissionNames.boqEdit.replace(":id", record.id + "")}`);
  };

  const handleVersionChange = (value: number) => {
    setSelectedVersion(value);
    fetchBoqDetails(value, false); // Không cập nhật versionLength nữa
  };

  // Helper function to transform BOQ details for API payload
  const transformBoqDetailsForApi = (details: BOQDetail[]): any[] => {
    return details.map((item) => {
      const {
        unit,
        unitConvert,
        projectItem,
        workType,
        children,
        ...restItem
      } = item;

      // Xử lý đặc biệt cho workType để tránh bị bọc trong mảng
      let workTypeId = null;
      if (workType) {
        if (
          typeof workType === "object" &&
          !Array.isArray(workType) &&
          workType !== null
        ) {
          workTypeId = workType.id;
        } else if (typeof workType === "number") {
          workTypeId = workType;
        } else if (Array.isArray(workType) && workType.length > 0) {
          // Nếu workType là mảng, lấy giá trị đầu tiên
          const firstItem = workType[0];
          if (firstItem && typeof firstItem === "object") {
            workTypeId = firstItem.id;
          } else if (typeof firstItem === "number") {
            workTypeId = firstItem;
          }
        }
      }

      const transformedItem: any = {
        ...restItem,
        // Transform object fields to ID fields
        unitId: unit?.id || (typeof unit === "number" ? unit : null),
        unitConvertId:
          unitConvert?.id ||
          (typeof unitConvert === "number" ? unitConvert : null),
        projectItemId:
          projectItem?.id ||
          (typeof projectItem === "number" ? projectItem : null),
        workTypeId: workTypeId,
      };

      // Transform children recursively if they exist
      if (children && children.length > 0) {
        transformedItem.children = transformBoqDetailsForApi(children);
      }

      // Đảm bảo không có trường nào là undefined
      Object.keys(transformedItem).forEach((key) => {
        if (transformedItem[key] === undefined) {
          transformedItem[key] = null;
        }
      });

      return transformedItem;
    });
  };

  const handleSaveBoq = async () => {
    try {
      const transformedBoqDetails = transformBoqDetailsForApi(boqDetails);
      const data = {
        boqDetails: transformedBoqDetails,
      };
      await boqApi.createBoqDetail(data);
      setIsEdit(false);
      fetchBoqDetails(undefined, true);
    } catch (error) {
      console.error("Error saving BOQ details:", error);
    }
  };

  const handleEditBoq = () => {
    setOriginalBoqDetails(JSON.parse(JSON.stringify(boqDetails))); // deep clone
    setIsEdit(true);
  };

  const handleCancelEdit = () => {
    setBoqDetails(originalBoqDetails);
    setIsEdit(false);
  };

  const columns: CustomizableColumn<BOQ>[] = [
    {
      title: "Mã BOQ",
      dataIndex: "code",
      key: "code",
      width: 120,
      sorter: true,
      defaultVisible: true,
      align: "center",
      render: (value: string, record: BOQ) => (
        <div
          onClick={() => handleRowClick(record)}
          className="text-[#1677ff] cursor-pointer"
        >
          {value}
        </div>
      ),
    },
    {
      title: "Tiêu đề",
      dataIndex: "name",
      key: "name",
      width: 200,
      sorter: true,
      defaultVisible: true,
    },
    {
      title: "Hạng mục",
      dataIndex: "projectItem",
      key: "projectItem",
      width: 250,
      defaultVisible: true,
      render: (value: ProjectItem) => value?.name || "-",
    },
    {
      title: "Thành tiền",
      dataIndex: "amount",
      key: "amount",
      width: 150,
      defaultVisible: true,
      render: (value: number) => formatVND(value),
    },
    {
      title: "Trạng thái",
      dataIndex: "isActive",
      key: "isActive",
      width: 120,
      defaultVisible: true,
      render: (value: boolean) => (
        <div className="justify-left flex">
          {value ? (
            <Tag color="green" className="status-tag !mr-0">
              Hoạt động
            </Tag>
          ) : (
            <Tag color="red" className="status-tag !mr-0">
              Bị khóa
            </Tag>
          )}
        </div>
      ),
    },
    {
      title: "Ngày tạo",
      dataIndex: "createdAt",
      key: "createdAt",
      width: 150,
      sorter: true,
      defaultVisible: true,
      render: (value: number) => unixToDate(value),
    },
  ];

  const isEmptyQuery = !queryBoq.search;

  const handleCreateBoq = () => {
    navigate(`/boq/${PermissionNames.boqAdd}`);
  };

  return (
    <div className="boq-page-container app-container">
      {!hidePageTitle && (
        <PageTitle
          title={title}
          breadcrumbs={["Báo cáo", title]}
          extra={
            <div className="flex gap-[16px] items-end">
              {/* {haveAddPermission && (
                <CustomButton
                  size="small"
                  showPlusIcon
                  onClick={handleCreateBoq}
                >
                  Tạo BOQ
                </CustomButton>
              )} */}

              {!isEdit ? (
                <CustomButton onClick={handleEditBoq}>Điều chỉnh</CustomButton>
              ) : (
                <>
                  <CustomButton size="small" onClick={handleSaveBoq}>
                    Lưu
                  </CustomButton>

                  <CustomButton
                    size="small"
                    variant="outline"
                    onClick={handleCancelEdit}
                  >
                    Hủy
                  </CustomButton>
                </>
              )}

              <div>
                <div className="w-[200px]">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Phiên bản
                  </label>
                  <Select
                    placeholder="Chọn phiên bản"
                    options={Array.from(
                      { length: versionLength },
                      (_, index) => ({
                        label: `Phiên bản ${versionLength - index}`,
                        value: versionLength - index,
                      })
                    )}
                    onChange={handleVersionChange}
                    value={selectedVersion}
                    style={{ width: "100%" }}
                  />
                </div>
              </div>

              {/* <div>
                <span className="text-xs text-gray-500 whitespace-nowrap">
                  Cập nhật: {formatFullDateTime(lastUpdateTime)}
                </span>
              </div> */}
            </div>
          }
        />
      )}

      <div className={hidePageTitle ? "" : "app-container"}>
        <Card>
          {/* <div className="flex gap-[16px] items-end pb-[12px] justify-between">
            <div className="flex gap-[16px] items-end flex-wrap">
              <div className="w-[300px]">
                <CustomInput
                  tooltipContent={"Tìm theo mã BOQ, tiêu đề"}
                  label="Tìm kiếm"
                  placeholder="Tìm kiếm"
                  value={queryBoq.search}
                  onChange={(value) => {
                    queryBoq.search = value;
                    setQueryBoq({ ...queryBoq });

                    if (!value) {
                      fetchBoq();
                    }
                  }}
                  onPressEnter={() => {
                    queryBoq.page = 1;
                    fetchBoq();
                  }}
                  allowClear
                />
              </div>

              <CustomButton
                onClick={() => {
                  queryBoq.page = 1;
                  fetchBoq();
                }}
              >
                Áp dụng
              </CustomButton>

              {!isEmptyQuery && (
                <CustomButton
                  variant="outline"
                  onClick={() => {
                    delete queryBoq.search;
                    setQueryBoq({ ...queryBoq });
                    fetchBoq();
                  }}
                >
                  Bỏ lọc
                </CustomButton>
              )}
            </div>

            {hidePageTitle && haveAddPermission && (
              <CustomButton
                size="small"
                onClick={handleCreateBoq}
                className="whitespace-nowrap"
              >
                + Tạo BOQ
              </CustomButton>
            )}
          </div> */}

          <Spin spinning={loadingBoq || loadingBoqDetails}>
            {/* <CustomizableTable
              columns={columns}
              dataSource={boqs}
              rowKey="id"
              loading={loadingBoq}
              pagination={false}
              scroll={{ x: "max-content" }}
              bordered
              displayOptions
              className="boq-table-no-empty-hover"
              tableId="boq-page"
              autoAdjustColumnWidth={true}
              onChange={handleTableChange}
              onRowClick={handleRowClick}
              size="middle"
            /> */}

            <BoqTable
              isEdit={isEdit}
              boqDetails={boqDetails}
              setBoqDetails={setBoqDetails}
              setIsEdit={setIsEdit}
            />
          </Spin>

          {/* <Pagination
            currentPage={queryBoq.page}
            defaultPageSize={queryBoq.limit}
            total={totalBoq}
            onChange={({ limit, page }) => {
              queryBoq.page = page;
              queryBoq.limit = limit;
              setQueryBoq({ ...queryBoq });
              fetchBoq();
            }}
          /> */}
        </Card>
      </div>
    </div>
  );
};
