import { Col, Form, Input, message, Modal, Row } from "antd";
import { Rule } from "antd/lib/form";
import { staffApi } from "api/staff.api";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import React, { useEffect, useState } from "react";
import { ModalStatus } from "types/modal";
import { Staff } from "types/staff";

const rules: Rule[] = [{ required: true }];

export const ResetPasswordModal = ({
  visible,
  status,
  staffId,
  onClose,
  onSubmitOk,
}: {
  visible: boolean;
  status: ModalStatus;
  staffId: number;
  onClose: () => void;
  onSubmitOk: () => void;
}) => {
  const [form] = Form.useForm<{ password: string }>();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (status == "create" && visible) {
      form.resetFields();
    }
  }, [visible, status]);

  const handleSubmit = async () => {
    await form.validateFields();
    const data = { password: form.getFieldValue("password") };

    setLoading(true);
    try {
      const res = await staffApi.resetPassword(staffId, data);
      message.success("Reset mật khẩu thành công");
      onClose();
      onSubmitOk();
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      maskClosable={false}
      onCancel={onClose}
      visible={visible}
      title={"Reset mật khẩu"}
      style={{ top: 20 }}
      width={500}
      confirmLoading={loading}
      cancelText="Đóng"
      onOk={handleSubmit}
    >
      <Form layout="vertical" form={form} onFinish={handleSubmit}>
        <Form.Item label="Mật khẩu mới" required name="password" rules={rules}>
          <Input.Password placeholder="" />
        </Form.Item>
      </Form>
    </Modal>
  );
};
