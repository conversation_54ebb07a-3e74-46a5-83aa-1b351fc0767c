// Color palette for light and dark modes
export const colors = {
  branding: {
    primary: {
      light: '#19345B',
      dark: '#113872'
    },
    accent: {
      light: '#ED1C24',
      dark: '#ED1C24'
    },
    logo: {
      light: '#19345B',
      dark: '#FFFFFF'
    }
  },
  neutral: {
    n0: {
      light: '#FFFFFF',
      dark: '#050505'
    },
    n1: {
      light: '#F7F7F7',
      dark: '#1F1D1E'
    },
    n2: {
      light: '#E4ECED',
      dark: '#2D3233'
    },
    n3: {
      light: '#B9C3C5',
      dark: '#586266'
    },
    n4: {
      light: '#879699',
      dark: '#768588'
    },
    n5: {
      light: '#636F73',
      dark: '#B9C3C5'
    },
    n6: {
      light: '#4F5759',
      dark: '#DDE4E5'
    },
    n7: {
      light: '#1F1D1E',
      dark: '#F7F7F7'
    },
    n8: {
      light: '#050505',
      dark: '#FFFFFF'
    }
  }
};

// Helper function to get color based on current theme
export const getColor = (
  colorPath: string, 
  isDarkMode: boolean = false
): string => {
  const parts = colorPath.split('.');
  let result: any = colors;
  
  for (const part of parts) {
    if (result[part]) {
      result = result[part];
    } else {
      console.warn(`Color path "${colorPath}" is invalid`);
      return '';
    }
  }
  
  return isDarkMode ? result.dark : result.light;
};

// CSS variables for use in styled-components or direct CSS
export const generateCssVariables = (isDarkMode: boolean = false): string => {
  let cssVars = '';
  
  // Process branding colors
  Object.entries(colors.branding).forEach(([key, value]) => {
    const colorValue = isDarkMode ? value.dark : value.light;
    cssVars += `--color-${key}: ${colorValue};\n`;
  });
  
  // Process neutral colors
  Object.entries(colors.neutral).forEach(([key, value]) => {
    const colorValue = isDarkMode ? value.dark : value.light;
    cssVars += `--color-${key}: ${colorValue};\n`;
  });
  
  return cssVars;
};

// Tailwind CSS compatible color object
export const tailwindColors = {
  primary: {
    DEFAULT: '#19345B',
    dark: '#113872'
  },
  accent: {
    DEFAULT: '#ED1C24',
    dark: '#ED1C24'
  },
  n0: {
    DEFAULT: '#FFFFFF',
    dark: '#050505'
  },
  n1: {
    DEFAULT: '#F7F7F7',
    dark: '#1F1D1E'
  },
  n2: {
    DEFAULT: '#E4ECED',
    dark: '#2D3233'
  },
  n3: {
    DEFAULT: '#B9C3C5',
    dark: '#586266'
  },
  n4: {
    DEFAULT: '#879699',
    dark: '#768588'
  },
  n5: {
    DEFAULT: '#636F73',
    dark: '#B9C3C5'
  },
  n6: {
    DEFAULT: '#4F5759',
    dark: '#DDE4E5'
  },
  n7: {
    DEFAULT: '#1F1D1E',
    dark: '#F7F7F7'
  },
  n8: {
    DEFAULT: '#050505',
    dark: '#FFFFFF'
  }
};