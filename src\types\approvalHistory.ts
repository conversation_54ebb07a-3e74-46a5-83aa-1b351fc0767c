import { ChangeEvent } from "./changeEvent";
import { Draw } from "./draw";
import { Instruction } from "./instruction";
import { Plan } from "./plan";
import { Punch } from "./punch";
import { RFI } from "./rfi";
import { Staff } from "./staff";
import { Submittal } from "./submittal";
import { Task } from "./task";

export interface ApprovalHistory {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  signImage: string; // hình ảnh chữ ký
  signType: number;
  type: string; // Loại
  status: string;
  note: string;
  version: number; // phiên bản
  task: Task;
  staff: Staff;
  instruction: Instruction;
  rfi: RFI;
  submittal: Submittal;
  plan: Plan;
  punch: Punch;
  draw: Draw;
  changeEvent: ChangeEvent;
}
