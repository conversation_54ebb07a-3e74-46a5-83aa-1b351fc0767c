import { message } from "antd";
import { projectApi } from "api/project.api";
import { AppLoading } from "components/App/AppLoading";
import { AppSuspense } from "components/App/AppSuspense";
import { useRouter } from "hooks/useRouter";
import { toJS } from "mobx";
import React, { useEffect, useLayoutEffect, useState } from "react";
import { useLocation, useNavigate, useSearchParams } from "react-router-dom";
import { adminRoutes } from "router";
import { settings } from "settings";
import { appStore } from "store/appStore";
import { permissionStore } from "store/permissionStore";
import { userStore } from "store/userStore";
import { PermissionNames } from "types/PermissionNames";
import { getToken, setToken } from "utils/auth";

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const navigation = useNavigate();
  const location = useLocation();
  const [searchParams, setSearchParams] = useSearchParams();
  const [isLoaded, setIsLoaded] = useState(false);
  useRouter(isLoaded);

  useEffect(() => {
    if (isLoaded) {
      handleAuth();
    }
  }, [navigation, isLoaded]);

  useLayoutEffect(() => {
    if (permissionStore.permissions.length) {
      checkRoutePathname();
    }
  }, [location.pathname, isLoaded, permissionStore.permissions]);

  useEffect(() => {
    initCheck();
    return () => {};
  }, []);

  const initCheck = async () => {
    try {
      await checkInitParams();
      await fetchPermission();
    } catch (error) {
      console.log({ error });
      userStore.logout();
      navigation("/login");
    } finally {
      setIsLoaded(true);
    }
  };

  const checkInitParams = async () => {
    const logProjectId = searchParams.get("logProjectId");
    console.log({ logProjectId });
    if (logProjectId) {
      const { data } = await projectApi.findOne(Number(logProjectId));
      appStore.setCurrentProject(data);
      searchParams.delete("logProjectId");
      setSearchParams(searchParams);
    }
  };

  const fetchPermission = async () => {
    const token = getToken();
    if (!token) {
      navigation("/login");
      return;
    }

    await userStore.getProfile();

    permissionStore.accessRoutes = [...adminRoutes];
    if (settings.checkPermission && userStore.info.role) {
      await permissionStore.fetchPermissions(userStore.info.role.id);
      // if (!permissionStore.permissions.length) {
      //   message.error("Không có quyền truy cập");
      //   throw new Error("");
      // }
      permissionStore.setAccessRoutes();

      if (window.location.pathname == "/") {
        let firstRouteAccess;
        // if (appStore.currentProject) {
        //   firstRouteAccess = permissionStore.accessRoutes.find(
        //     (r) =>
        //       (r.needProject && (r.isPublic || r.isAccess)) ||
        //       !settings.checkPermission
        //   );
        // } else {
        firstRouteAccess = permissionStore.accessRoutes.find(
          (r) => r.isPublic || r.isAccess || !settings.checkPermission
        );
        // }

        let firstPath = "/";

        if (firstRouteAccess) {
          if (firstRouteAccess.children) {
            const firstChildRouteAccess = firstRouteAccess.children.find(
              (cr) => cr.isPublic || cr.isAccess || !settings.checkPermission
            );
            if (firstChildRouteAccess) {
              firstPath =
                firstRouteAccess.path + "/" + firstChildRouteAccess.path;
            } else {
              firstPath = firstRouteAccess.path || "";
            }
          } else {
            firstPath = firstRouteAccess.path || "";
          }
        }
        // if (
        //   firstPath.includes("/project-detail/:id") &&
        //   appStore.currentProject
        // ) {
        //   firstPath = firstPath.replace(
        //     ":id",
        //     appStore.currentProject.id.toString()
        //   );
        // }

        navigation(firstPath);
      }
    }
  };

  const checkRoutePathname = () => {
    const currentPath = location.pathname;
    if (settings.checkPermission && currentPath === "/") {
      const test = toJS(permissionStore.permissions);
      if (
        permissionStore.permissions.find(
          (p) => p.name == PermissionNames.projectList
        )
      ) {
        navigation("/project/project/list");
      }
    }
  };

  const handleAuth = async () => {
    try {
      await userStore.getProfile();
    } catch (error) {
      userStore.logout();
      navigation("/login");
    }
  };

  if (!isLoaded) {
    return <AppLoading />;
  }

  return <>{children}</>;
};
