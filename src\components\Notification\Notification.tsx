import { Badge, Popover } from "antd";
import { useEffect, useState } from "react";
import { ReactComponent as BellI<PERSON> } from "assets/svgs/bell.svg";
import NotificationModal from "./NotificationModal";
import { useNotification } from "hooks/useNotification";
import { userStore } from "store/userStore";
import { notificationApi } from "api/notification.api";
import { useNavigate } from "react-router-dom";
import { Project } from "types/project";
import { appStore } from "store/appStore";

const Notification = () => {
  const [unreadNoti, setUnreadNoti] = useState(10);
  const navigate = useNavigate();
  const {
    notifications,
    totalNotification,
    fetchNotification,
    loadingNotification,
    setQueryNotification,
    queryNotification,
  } = useNotification({
    initQuery: {
      limit: 100,
      page: 1,
      staffId: userStore.info?.id,
      // isAdmin: haveViewAllPermission ? true : undefined,
    },
  });

  useEffect(() => {
    fetchNotification();
    // eslint-disable-next-line
  }, []);

  // Cập nhật số lượng chưa đọc mỗi khi notifications thay đổi
  useEffect(() => {
    setUnreadNoti(notifications.filter((it) => !it.isRead).length);
    appStore.setUnreadNotificationCount(
      notifications.filter((it) => !it.isRead).length
    );
  }, [notifications]);

  const handleClickReadNotification = async (
    notificationId: number,
    url: string,
    project?: Project
  ) => {
    try {
      const res = await notificationApi.handleReadNotification(notificationId);
      // if (res.status === 200) {
      //   fetchNotification();
      // }
      if (project) {
        appStore.setCurrentProject(project);
      }
      if (url) {
        window.open(url, "_blank");
      }
      setTimeout(() => {
        if (window.location.pathname.includes("project-detail")) {
          window.location.replace(`/project-detail/${project?.id}`);
        } else {
          window.location.reload();
        }
      }, 200);
      fetchNotification();
    } catch (error) {
      console.log("~ handleClickReadNotification ~ error:", error);
    }
  };

  return (
    <Popover
      placement="bottomRight"
      content={
        <NotificationModal
          notifications={notifications}
          handleClickReadNotification={handleClickReadNotification}
        />
      }
      trigger="click"
      overlayClassName="notification-popover"
    >
      <div className="nav-bar-item-wrapper flex gap-1 items-center h-[44px] p-[10px] cursor-pointer">
        <Badge
          count={
            appStore.unreadNotificationCount > 9 ? (
              <div>
                <span>{appStore.unreadNotificationCount}</span>
                <span>+</span>
              </div>
            ) : unreadNoti > 0 ? (
              <div>{unreadNoti}</div>
            ) : (
              <></>
            )
          }
          classNames={{
            indicator: "!text-[10px] italic !px-[2px]",
          }}
          size="small"
          offset={[2, -2]}
        >
          <BellIcon className="bell-icon w-[24px] h-[24px] object-contain" />
        </Badge>
      </div>
    </Popover>
  );
};

export default Notification;
