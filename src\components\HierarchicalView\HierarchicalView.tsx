import React, { useState, useCallback } from "react";
import { Button, Space, Tooltip } from "antd";
import {
  DownOutlined,
  RightOutlined,
  PlusCircleOutlined,
  EditOutlined,
  DeleteOutlined,
  FolderOpenOutlined,
  FolderOutlined,
  FileTextOutlined,
  BookOutlined,
  TagsOutlined,
} from "@ant-design/icons";
import EditButton from "components/Button/EditButton";
import DeleteButton from "components/Button/DeleteButton";
import CustomButton from "components/Button/CustomButton";
import "./HierarchicalView.scss";

export interface HierarchicalItem {
  id: number;
  name: string;
  children: HierarchicalItem[];
  description?: string;
  level?: number;
  isActive?: boolean;
  code?: string;
}

export interface HierarchicalViewProps {
  data: HierarchicalItem[];
  onAdd?: (parentId?: number) => void;
  onEdit?: (item: HierarchicalItem, parentId?: number) => void;
  onDelete?: (item: HierarchicalItem) => void;
  canAdd?: boolean;
  canEdit?: boolean;
  canDelete?: boolean;
  maxDepth?: number;
  showDescription?: boolean;
  expandedKeys?: Set<number>;
  onExpandedKeysChange?: (expandedKeys: Set<number>) => void;
  className?: string;
  showGroupCode?: boolean; // New prop to control group code visibility
}

export interface HierarchicalItemProps {
  item: HierarchicalItem;
  level: number;
  expandedKeys: Set<number>;
  onToggleExpand: (id: number) => void;
  onAdd?: (parentId?: number) => void;
  onEdit?: (item: HierarchicalItem, parentId?: number) => void;
  onDelete?: (item: HierarchicalItem) => void;
  canAdd?: boolean;
  canEdit?: boolean;
  canDelete?: boolean;
  maxDepth?: number;
  showDescription?: boolean;
  showGroupCode?: boolean;
  parentId?: number;
}

const HierarchicalItem: React.FC<HierarchicalItemProps> = ({
  item,
  level,
  expandedKeys,
  onToggleExpand,
  onAdd,
  onEdit,
  onDelete,
  canAdd,
  canEdit,
  canDelete,
  maxDepth = 5,
  showDescription = false,
  showGroupCode = true,
  parentId,
}) => {
  const hasChildren = item.children && item.children.length > 0;
  const canAddChild = canAdd && (!maxDepth || level < maxDepth);
  const isExpanded = expandedKeys.has(item.id);

  const handleToggleExpand = useCallback(() => {
    if (hasChildren) {
      onToggleExpand(item.id);
    }
  }, [hasChildren, onToggleExpand, item.id]);

  const getIcon = () => {
    if (hasChildren) {
      return <BookOutlined style={{ color: "#1890ff" }} />;
    }
    return <TagsOutlined style={{ color: "#52c41a" }} />;
  };

  const getExpandIcon = () => {
    if (!hasChildren)
      return <span className="hierarchical-item__expand-placeholder" />;
    return isExpanded ? (
      <DownOutlined className="hierarchical-item__expand-icon" />
    ) : (
      <RightOutlined className="hierarchical-item__expand-icon" />
    );
  };

  return (
    <div className="hierarchical-item">
      <div
        className={`hierarchical-item__content level-${level} ${
          !item.isActive ? "inactive" : ""
        }`}
        style={{ paddingLeft: `${level * 24}px` }}
      >
        {/* Group Code Column */}
        {showGroupCode && (
          <div className="hierarchical-item__group-code">
            <Tooltip title={`Mã nhóm: ${item.code}`}>
              <span className="group-code-badge">#{item.code}</span>
            </Tooltip>
          </div>
        )}

        <div className="hierarchical-item__left" onClick={handleToggleExpand}>
          {getExpandIcon()}
          <span className="hierarchical-item__icon">{getIcon()}</span>
          <div className="hierarchical-item__info">
            <span className="hierarchical-item__name">
              {/* {item.code ? `${item.code} - ` : ""} */}
              {item.name}
            </span>
          </div>
        </div>

        <div className="hierarchical-item__actions">
          <Space size="small">
            {canAddChild && (
              <Tooltip title="Thêm con">
                <Button
                  type="text"
                  size="small"
                  icon={<PlusCircleOutlined />}
                  className="hierarchical-item__action-btn add-btn"
                  onClick={(e) => {
                    e.stopPropagation();
                    onAdd?.(item.id);
                  }}
                />
              </Tooltip>
            )}
            {canEdit && (
              <EditButton
                onClick={(e) => {
                  e.stopPropagation();
                  onEdit?.(item, parentId);
                }}
              />
            )}
            {canDelete && (
              <DeleteButton
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete?.(item);
                }}
              />
            )}
          </Space>
        </div>
      </div>

      {hasChildren && isExpanded && (
        <div className="hierarchical-item__children">
          {item.children.map((child) => (
            <HierarchicalItem
              key={child.id}
              item={child}
              level={level + 1}
              expandedKeys={expandedKeys}
              onToggleExpand={onToggleExpand}
              onAdd={onAdd}
              parentId={item.id}
              onEdit={onEdit}
              onDelete={onDelete}
              canAdd={canAdd}
              canEdit={canEdit}
              canDelete={canDelete}
              maxDepth={maxDepth}
              showDescription={showDescription}
              showGroupCode={showGroupCode}
            />
          ))}
        </div>
      )}
    </div>
  );
};

const HierarchicalView: React.FC<HierarchicalViewProps> = ({
  data,
  onAdd,
  onEdit,
  onDelete,
  canAdd = false,
  canEdit = false,
  canDelete = false,
  maxDepth = 5,
  showDescription = false,
  showGroupCode = true,
  expandedKeys: controlledExpandedKeys,
  onExpandedKeysChange,
  className = "",
}) => {
  const [internalExpandedKeys, setInternalExpandedKeys] = useState<Set<number>>(
    new Set()
  );

  const isControlled = controlledExpandedKeys !== undefined;
  const expandedKeys = isControlled
    ? controlledExpandedKeys
    : internalExpandedKeys;

  const handleToggleExpand = useCallback(
    (id: number) => {
      const newExpandedKeys = new Set(expandedKeys);
      if (newExpandedKeys.has(id)) {
        newExpandedKeys.delete(id);
      } else {
        newExpandedKeys.add(id);
      }

      if (isControlled) {
        onExpandedKeysChange?.(newExpandedKeys);
      } else {
        setInternalExpandedKeys(newExpandedKeys);
      }
    },
    [expandedKeys, isControlled, onExpandedKeysChange]
  );

  const expandAll = useCallback(() => {
    const getAllIds = (items: HierarchicalItem[]): number[] => {
      return items.reduce((acc, item) => {
        acc.push(item.id);
        if (item.children?.length > 0) {
          acc.push(...getAllIds(item.children));
        }
        return acc;
      }, [] as number[]);
    };

    const allIds = new Set(getAllIds(data));
    if (isControlled) {
      onExpandedKeysChange?.(allIds);
    } else {
      setInternalExpandedKeys(allIds);
    }
  }, [data, isControlled, onExpandedKeysChange]);

  const collapseAll = useCallback(() => {
    const emptySet = new Set<number>();
    if (isControlled) {
      onExpandedKeysChange?.(emptySet);
    } else {
      setInternalExpandedKeys(emptySet);
    }
  }, [isControlled, onExpandedKeysChange]);

  if (!data || data.length === 0) {
    return (
      <div className={`hierarchical-view empty ${className}`}>
        <div className="hierarchical-view__empty">
          <FileTextOutlined className="hierarchical-view__empty-icon" />
          <span>Không có dữ liệu để hiển thị</span>
          {canAdd && (
            <CustomButton size="small" showPlusIcon onClick={() => onAdd?.()}>
              Thêm mới
            </CustomButton>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={`hierarchical-view ${className}`}>
      <div className="hierarchical-view__header">
        <div className="hierarchical-view__controls">
          <Space size="small">
            <Button size="small" onClick={expandAll}>
              Mở rộng tất cả
            </Button>
            <Button size="small" onClick={collapseAll}>
              Thu gọn tất cả
            </Button>
            {/* {canAdd && (
              <CustomButton size="small" showPlusIcon onClick={() => onAdd?.()}>
                Thêm gốc
              </CustomButton>
            )} */}
          </Space>
        </div>
      </div>

      {/* Header with column labels */}
      {showGroupCode && (
        <div className="hierarchical-view__column-headers">
          <div className="column-header group-code-header">
            <span>Mã nhóm</span>
          </div>
          <div className="column-header name-header">
            <span>Tên nhóm</span>
          </div>
          <div className="column-header actions-header">
            <span>Thao tác</span>
          </div>
        </div>
      )}

      <div className="hierarchical-view__content">
        {data.map((item) => (
          <HierarchicalItem
            key={item.id}
            item={item}
            level={0}
            expandedKeys={expandedKeys}
            onToggleExpand={handleToggleExpand}
            onAdd={onAdd}
            onEdit={onEdit}
            onDelete={onDelete}
            canAdd={canAdd}
            canEdit={canEdit}
            canDelete={canDelete}
            maxDepth={maxDepth}
            showDescription={showDescription}
            showGroupCode={showGroupCode}
          />
        ))}
      </div>
    </div>
  );
};

export default HierarchicalView;
