import { Staff } from "types/staff";

export enum UnitType {
  Length = "LENGTH",
  Mass = "MASS",
  Quantity = "QUANTITY",
  Volume = "VOLUME",
}

export const UnitTypeTrans = {
  [UnitType.Length]: {
    value: UnitType.Length,
    label: "<PERSON>ều dài",
  },
  [UnitType.Mass]: {
    value: UnitType.Mass,
    label: "Khối lượng",
  },
  [UnitType.Quantity]: {
    value: UnitType.Quantity,
    label: "Số lượng",
  },
  [UnitType.Volume]: {
    value: UnitType.Volume,
    label: "Volume",
  },
};
export interface Unit {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  name: string;
  code: string;
  description: string;
  symbol: string;
  type: UnitType;
  isActive: boolean;
}
