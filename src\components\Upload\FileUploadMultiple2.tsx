import { ReactNode, useEffect, useState } from "react";
import { PaperClipOutlined } from "@ant-design/icons";
import type { UploadFile, UploadProps } from "antd";
import { Button, message, Row, Space, Upload } from "antd";
import { $url } from "utils/url";
import { getToken } from "utils/auth";
import { useTheme } from "@mui/material";
import clsx from "clsx";
import FileUploadItem, { FileCustomProps } from "./FileUploadItem";
import CustomButton from "components/Button/CustomButton";
import { uniqueId } from "lodash";
import { FileIcon } from "assets/svgs/FileIcon";
import { PCIcon } from "assets/svgs/PCIcon";
import { ArrowDownIcon } from "assets/svgs/ArrowDownIcon";
import { UploadFileIcon } from "assets/svgs/UploadFileIcon";
import { FileAttach } from "types/fileAttach";
import { FileListTable } from "./components/FileListTable";
import dayjs from "dayjs";
import { RcFile } from "antd/es/upload";
const { Dragger } = Upload;

interface Props {
  fileList: FileAttach[];
  fileTypeText?: string;
  fileTypes?: string[];
  placeholder?: string;
  uploadUrl?: string;
  acceptType?: string;
  readOnly?: boolean;
  uploadFileComment?: boolean;
  disabled?: boolean;
  id?: string;
  className?: string;
  draggerContent?: ReactNode;
  onUploadOk: (file: FileAttach) => void;
  onDelete?: (file: FileAttach) => void;
  onBefore?: () => Promise<boolean>;
  variant?: "compact" | "detailed";
  maxFile?: number;
  hideUploadButton?: boolean;
  uploadDescription?: string;
  showSearch?: boolean;
}

const getBase64 = (img: RcFile): Promise<string> => {
  const reader = new FileReader();

  return new Promise((resolve, reject) => {
    reader.addEventListener("load", () => resolve(reader.result as string));
    reader.readAsDataURL(img);
  });
};

export const FileUploadMultiple2 = ({
  onUploadOk,
  onDelete,
  onBefore,
  fileList,
  fileTypes,
  fileTypeText = "PDF",
  placeholder,
  acceptType,
  readOnly = false,
  uploadFileComment,
  uploadUrl = import.meta.env.VITE_API_URL + "/v1/admin/fileAttach/upload",
  id,
  disabled,
  className,
  draggerContent,
  variant = "compact",
  maxFile = 5,
  hideUploadButton = false,
  uploadDescription = "Hỗ trợ lưu trữ tất cả các định dạng tập tin.",
  showSearch = false,
}: Props) => {
  const theme = useTheme();
  const [duplicateImage, setDuplicateImage] = useState(false);
  const [uidDragger, setUidDragger] = useState(uniqueId("FileUploadMultiple"));

  const props: UploadProps = {
    accept: acceptType,
    // id: id,
    fileList,
    name: "file",
    multiple: true,
    action: uploadUrl,
    className: clsx("inline-block w-full", className),
    disabled,
    async beforeUpload(info) {
      console.log("beforeUpload", info);

      setDuplicateImage(false);
      let isOK = true;
      if (fileList?.length > 0 && info) {
        const value = fileList.find(
          (item) => item.name === info.name && item.size === info.size
        );

        if (value) {
          setDuplicateImage(true);
          message.error("Tệp đã tồn tại");
          isOK = false;
        }
      }

      if (isOK) {
        const url = await getBase64(info);

        onUploadOk({
          url,
          name: info.name,
          size: info.size,
          updatedAt: dayjs().unix(),
          uid: info.uid,
          mimetype: info.type,
          originFile: info,
        } as FileAttach);
      }

      return false;
    },
    // onChange(info) {
    //   console.log("onChange upload file", { duplicateImage, info });

    //   if (duplicateImage) return;
    //   const currentFiles = info.fileList;

    //   if (currentFiles.length > maxFile) {
    //     message.warning(`Số file tối đa là: ${maxFile}`);
    //     return;
    //   }

    //   // console.log("fileListOrigin", fileListOrigin);
    //   if (info.file.status === "uploading") {
    //     // info.file.status = "done";
    //     // setLoading(true); // Đang trong quá trình tải file lên
    //   }

    //   if (info.file.status === "done") {
    //     console.log("upload file ok", info);

    //     // const fileAttach = {
    //     //   originFile: info.file,
    //     // } as FileAttach;

    //     // Khi file tải lên thành công
    //     // const updatedFileList = info.fileList.map((file) => {
    //     //   if (file.uid === info.file.uid && info.file.response) {
    //     //     // Gán URL từ response vào file item
    //     //     return {
    //     //       ...file,
    //     //     };
    //     //   }
    //     //   return file;
    //     // });
    //     // setFileListPaste(updatedFileList);

    //     // onUploadOk(updatedFileList);
    //     // setFileListOrigin(updatedFileList); // Cập nhật danh sách file với URL mới
    //     // onUploadOk(updatedFileList); // Gọi hàm onUploadOk sau khi tải xong với URL đã được gán

    //     // setLoading(false); // Dừng loading
    //   }
    // },
    onDrop(e) {
      console.log("Dropped files", e.dataTransfer.files);
    },
  };
  const handleDelete = (id: string) => {
    // console.log("fileListOrigin", fileListOrigin);
    const filterFile = fileList?.filter((item) => {
      return item.uid !== id;
    });
    // setFileListOrigin(filterFile);
    // @ts-ignore
    onUploadOk(filterFile);
  };

  const uploadFileToServer = async (file: File) => {
    const formData = new FormData();
    formData.append("file", file); // Thêm file vào FormData
    const response = await fetch(uploadUrl, {
      method: "POST",
      body: formData,
      headers: {
        token: getToken() || "", // Nếu cần thêm header
      },
    });

    if (!response.ok) {
      throw new Error("Upload failed");
    }

    return response.json(); // Giả sử server trả về JSON
  };
  const handleUpload = (file: File, responseData: { path: string }) => {
    const newFile: UploadFile<any> = {
      uid: Math.floor(Math.random() * 1000).toString(),
      name: file.name,
      size: file.size,
      url: `${import.meta.env.VITE_IMG_URL}${responseData.path}`,
      type: file.type,
    };
    // setFileListPaste((prevFileList) => {
    //   const updatedFileList = [...(prevFileList || []), newFile];
    //   onUploadOk(updatedFileList);
    //   return updatedFileList;
    // });
  };

  const handlePasteImage = async (e: ClipboardEvent) => {
    const target = e.target as HTMLElement;

    // Kiểm tra nếu sự kiện xảy ra bên trong textarea hoặc input
    if (target.tagName === "TEXTAREA" || target.tagName === "INPUT") {
      return; // Bỏ qua xử lý sự kiện paste
    }
    const items = e.clipboardData?.items;
    console.log(items);
    if (items) {
      const fileArray: File[] = Array.from(items)
        .map((item) => item.getAsFile())
        .filter((file) => file) as File[];

      console.log(fileArray);
      // Xử lý tất cả các file ảnh dán
      for (const file of fileArray) {
        const response = await uploadFileToServer(file); // Upload ảnh lên server
        handleUpload(file, response.data); // Thêm ảnh vào danh sách
      }
    }
  };

  useEffect(() => {
    const pasteHandler = handlePasteImage as unknown as EventListener;
    window.addEventListener("paste", pasteHandler);

    return () => {
      window.removeEventListener("paste", pasteHandler);
    };
  }, [handlePasteImage]);

  return (
    <div className="h-full upload-file-container">
      {uploadFileComment ? (
        <>
          <Upload
            fileList={fileList}
            headers={{ token: getToken() || "" }}
            showUploadList={false}
            {...props}
          >
            <Button icon={<PaperClipOutlined />}></Button>
          </Upload>
          <Row gutter={[6, 6]}>
            {fileList &&
              fileList.map((item, index) => {
                const file: FileCustomProps = {
                  id: item.uid,
                  fileName: item.name,
                  fileSize: item.size,
                  fileUrl: item.url,
                };
                return (
                  <FileUploadItem
                    key={index}
                    file={file}
                    onDelete={handleDelete}
                  />
                );
              })}
          </Row>
        </>
      ) : (
        <>
          {fileList?.length > 0 ? (
            <FileListTable
              fileAttaches={fileList}
              onDelete={onDelete}
              onUploadFromLocal={() => {
                document.getElementById(uidDragger)?.click();
              }}
              showSearch={showSearch}
            />
          ) : (
            <div
              className={clsx(
                "hover:shadow-md rounded-md transition-all h-full",
                fileList?.length > 0 ? "invisible h-0" : ""
              )}
            >
              <Dragger
                headers={{ token: getToken() || "" }}
                showUploadList={false}
                {...props}
                id={uidDragger}
                className="!h-full"
              >
                {draggerContent ? (
                  draggerContent
                ) : (
                  <p className="ant-upload-text flex flex-col gap-2 items-center">
                    <UploadFileIcon />
                    <div
                      className="font-bold"
                      style={{ fontSize: 13, color: "var(--color-neutral-n8)" }}
                    >
                      {"Tải lên tệp đính kèm"}
                    </div>
                    <div
                      style={{ fontSize: 13, color: "var(--color-neutral-n6)" }}
                    >
                      {uploadDescription}
                    </div>

                    <div>
                      <Space>
                        <CustomButton
                          variant="outline"
                          icon={<FileIcon />}
                          onClick={(e) => {
                            e.stopPropagation();
                            message.warning("Tính năng đang phát triển");
                          }}
                          disabled={hideUploadButton}
                          style={
                            hideUploadButton
                              ? {
                                  pointerEvents: "none",
                                  cursor: "not-allowed",
                                  opacity: 0.5,
                                }
                              : {}
                          }
                        >
                          <span>Chọn tệp có sẵn</span>
                          <ArrowDownIcon />
                        </CustomButton>

                        <CustomButton
                          icon={<PCIcon />}
                          onClick={(e) => {
                            e.stopPropagation();
                            document.getElementById(uidDragger)?.click();
                          }}
                          disabled={hideUploadButton}
                          style={
                            hideUploadButton
                              ? {
                                  pointerEvents: "none",
                                  cursor: "not-allowed",
                                  opacity: 0.5,
                                }
                              : {}
                          }
                        >
                          Chọn tệp từ máy tính
                        </CustomButton>
                      </Space>
                    </div>
                  </p>
                )}
              </Dragger>
            </div>
          )}
        </>
      )}
    </div>
  );
};
