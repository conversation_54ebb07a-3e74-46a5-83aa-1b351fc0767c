import { FacebookFilled } from "@ant-design/icons";
import { Modal } from "antd";
import React, { forwardRef, useImperativeHandle, useState } from "react";
import { FileAttach } from "types/fileAttach";
import DocumentPage from "views/ImageManagement/DocumentPage";

export type ChooseImageMode = "image" | "video";

export interface ChooseImageModalProps {
  onChoose: (file: FileAttach) => void;
  chooseMode?: ChooseImageMode;
}
export interface ChooseImageModalRef {
  onOpen: () => void;
}
const ChooseImageModal = forwardRef(
  ({ onChoose, chooseMode = "image" }: ChooseImageModalProps, ref) => {
    const [visible, setVisible] = useState(false);
    const handleChoose = (file: FileAttach) => {
      onChoose(file); // Gọi callback truyền từ component cha
      setVisible(false); // Đóng modal sau khi chọn
    };
    const onOpenModal = () => {
      setVisible(true);
    };
    const handleCloseModal = () => {
      setVisible(false);
    };
    useImperativeHandle(
      ref,
      () => ({
        onOpen: () => {
          onOpenModal();
        },
      }),
      []
    );
    return (
      <Modal
        title="Chọn ảnh"
        closable
        onCancel={handleCloseModal}
        open={visible}
        style={{
          top: 20,
        }}
        width={1200}
        footer={null}
        destroyOnClose
      >
        <DocumentPage
          chooseMode={chooseMode}
          title="Chọn ảnh"
          onChoose={handleChoose}
        />
      </Modal>
    );
  }
);

export default ChooseImageModal;
