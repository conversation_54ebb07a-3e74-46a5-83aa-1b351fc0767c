import {
  ImportOutlined,
  LockOutlined,
  UnlockOutlined,
} from "@ant-design/icons";
import {
  Button,
  Card,
  message,
  Modal,
  Select,
  Space,
  Table,
  Tag,
  Tooltip,
} from "antd";
import { TableProps } from "antd/lib";
import { unitApi } from "api/unit.api";
import PencilIcon from "assets/svgs/PencilIcon";
import CustomButton from "components/Button/CustomButton";
import EditButton from "components/Button/EditButton";
import LockButton from "components/Button/LockButton";
import ImportUnit, {
  ImportUnitModal,
} from "components/ImportDocument/ImportUnit";
import CustomInput from "components/Input/CustomInput";
import PageTitle from "components/PageTitle/PageTitle";
import { Pagination } from "components/Pagination";
import QueryLabel from "components/QueryLabel/QueryLabel";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import { useUnit } from "hooks/useUnit";
import { observer } from "mobx-react";
import { useEffect, useMemo, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import { permissionStore } from "store/permissionStore";
import { PermissionNames } from "types/PermissionNames";
import { Unit, UnitTypeTrans } from "types/unit";
import { getTitle } from "utils";
import { handleExport, MyExcelColumn } from "utils/MyExcel";
import { exportTemplateWithValidation } from "utils/TemplateExcel";
import {
  checkRole,
  checkRoles,
  filterActionColumnIfNoPermission,
} from "utils/auth";
import { removeSubstringFromKeys } from "utils/common";

export const UnitPage = observer(({ title = "" }) => {
  const exportColumns: MyExcelColumn<Unit>[] = [
    {
      header: "Mã đơn vị",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "code",
      columnKey: "code",
    },
    {
      header: "Tên đơn vị",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "name",
      columnKey: "name",
    },
    {
      header: "Loại đơn vị",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "type",
      columnKey: "type",
      render: (record: Unit) => UnitTypeTrans[record.type]?.label,
    },
    {
      header: "Ký hiệu",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "symbol",
      columnKey: "symbol",
      render: (record: Unit) => record.symbol,
    },
    {
      header: "Mô tả",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "description",
      columnKey: "description",
      render: (record: Unit) => record.description,
    },
    {
      header: "Trạng thái",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "isActive",
      columnKey: "isActive",
      render: (record: Unit) => (record.isActive ? "Hoạt động" : "Bị khóa"),
    },
  ];
  const importModal = useRef<ImportUnitModal>();

  const {
    haveAddPermission,
    haveBlockPermission,
    haveEditPermission,
    haveViewAllPermission,
  } = checkRoles(
    {
      add: PermissionNames.unitAdd,
      edit: PermissionNames.unitEdit,
      block: PermissionNames.unitBlock,
      viewAll: PermissionNames.unitViewAll,
    },
    permissionStore.permissions
  );

  const [loadingDelete, setLoadingDelete] = useState(false);
  const { units, fetchData, loading, query, setQuery, total, isEmptyQuery } =
    useUnit({
      initQuery: {
        limit: 10,
        page: 1,
        isAdmin: haveViewAllPermission ? true : undefined,
      },
    });
  const navigate = useNavigate();
  useEffect(() => {
    document.title = getTitle(title);
    fetchData();
  }, []);

  // useEffect(() => {
  //   setQuery({ ...query, isAdmin: haveViewAllPermission ? true : undefined });
  // }, [haveViewAllPermission]);

  const handleDeleteUnit = async (id: number) => {
    try {
      setLoadingDelete(false);
      await unitApi.delete(id);
      message.success("Xóa thành công");
      fetchData();
    } catch (error) {
    } finally {
      setLoadingDelete(true);
    }
  };

  const handleOnUploadedFile = async (excelData: any, setData: any) => {
    const { results } = excelData;

    console.log("results", results);

    const importData = results?.map((item: any) => {
      const refineRow = removeSubstringFromKeys(item, " *");

      const code = refineRow["Mã đơn vị"];
      const name = refineRow["Tên đơn vị"];
      const type = refineRow["Loại đơn vị"];
      const symbol = refineRow["Ký hiệu"];
      const description = refineRow["Mô tả"];
      const isActive =
        refineRow["Trạng thái"] === "Hoạt động"
          ? true
          : refineRow["Trạng thái"] === "Bị khóa"
          ? false
          : undefined;

      return {
        code,
        name,
        type,
        symbol,
        description,
        isActive,
        rowNum: item.__rowNum__,
      };
    });
    console.log("importData", importData);

    setData(importData);
  };

  const handleActiveUnit = async (id: number, value: boolean) => {
    try {
      setLoadingDelete(false);
      await unitApi.update(id, { unit: { isActive: !value } });
      message.success(value ? "Khóa thành công" : "Mở khóa thành công");
      fetchData();
    } catch (error) {
    } finally {
      setLoadingDelete(true);
    }
  };

  const handleRowClick = (record: Unit) => {
    navigate(
      `/master-data/${PermissionNames.unitEdit.replace(":id", record!.id + "")}`
    );
  };

  const handleDownloadDemoExcel = async () => {
    try {
      const unitTypeOptions = Object.values(UnitTypeTrans)
        .map((item) => item.label)
        .join(",");

      const result = await exportTemplateWithValidation({
        templatePath: "/exportFile/file_mau_nhap_don_vi_tinh.xlsx",
        outputFileName: "file_mau_nhap_don_vi_tinh.xlsx",
        sheetsToAdd: [],
        validations: [
          {
            headerName: "Loại đơn vị",
            type: "list",
            formulae: [`"${unitTypeOptions}"`],
          },
          {
            headerName: "Trạng thái",
            type: "list",
            allowBlank: true,
            formulae: [`"Hoạt động,Bị khóa"`],
          },
        ],
      });
    } catch (error) {
      message.error(
        `Có lỗi xảy ra: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    } finally {
    }
  };

  const handleTableChange: TableProps<any>["onChange"] = (
    pagination,
    filters,
    sorter
  ) => {
    if (!Array.isArray(sorter)) {
      const fieldMap: Record<string, string> = {
        name: "unit.name",
        code: "unit.code",
        type: "unit.type",
        description: "unit.description",
      };
      const columnKey = sorter.field || sorter.column?.key;

      if (!sorter.order) {
        // setSortField(null);
        // setSortOrder(null);
        query.queryObject = undefined;
        setQuery({ ...query });
      } else {
        const order = sorter.order === "ascend" ? "ASC" : "DESC";
        // setSortField("jobCategory.name");
        // setSortOrder(order);
        const field = fieldMap[columnKey as string];

        const newQueryObject = JSON.stringify([
          {
            type: "sort",
            field,
            value: order,
          },
        ]);
        query.queryObject = newQueryObject;
        setQuery({ ...query });
      }
      fetchData();
    } else {
      query.queryObject = undefined;
      setQuery({ ...query });
      fetchData();
    }
  };

  const columns: CustomizableColumn<Unit>[] = [
    // {
    //   key: "code",
    //   title: "Mã đơn vị tính",
    //   dataIndex: "code",
    //   width: 70,
    //   sorter: true,
    //   render: (_, record) => {
    //     return (
    //       <div
    //         className="text-[#1677ff] cursor-pointer"
    //         onClick={() => handleRowClick(record)}
    //       >
    //         {record.code}
    //       </div>
    //     );
    //   },
    //   defaultVisible: true,
    //   alwaysVisible: true,
    // },
    {
      key: "name",
      title: "Đơn vị tính",
      dataIndex: "name",
      width: 150,
      sorter: true,
      render: (_, record) => (
        <div
          onClick={() => handleRowClick(record)}
          className="text-[#1677ff] cursor-pointer"
        >
          {record.name}
        </div>
      ),
      // render: (_, record) => (
      // 	<div className="service-cell">

      // 		<div className="service-info">
      // 			<div className="service-name">{record.name}</div>
      // 		</div>
      // 	</div>
      // ),
      defaultVisible: true,
    },
    {
      key: "type",
      title: "Loại đơn vị tính",
      width: 150,
      dataIndex: "type",
      sorter: true,

      render: (_, record) => (
        <div className="service-cell">
          <div className="service-info">
            <div className="service-name">
              {UnitTypeTrans[record.type]?.label}
            </div>
          </div>
        </div>
      ),
      defaultVisible: true,
    },
    // {
    //   sorter: true,

    //   key: "description",
    //   title: "Mô tả",
    //   dataIndex: "description",
    //   width: 150,
    //   render: (_, record) => (
    //     <div className="service-cell text-ellipsis-2">{record.description}</div>
    //   ),
    // },
    {
      key: "status",
      title: "Trạng thái",
      align: "center",
      width: 150,
      render: (_, record) => (
        <div className="flex justify-center">
          {record.isActive ? (
            <Tag color="green" className="status-tag !mr-0">
              Hoạt động
            </Tag>
          ) : (
            <Tag color="red" className="status-tag !mr-0">
              Bị khóa
            </Tag>
          )}
        </div>
      ),
      defaultVisible: true,
    },
    {
      key: "actions",
      title: "Xử lý",
      align: "center",
      width: 100,
      fixed: "right",
      render: (_, record) => (
        <Space size="small">
          {haveEditPermission && (
            <EditButton
              onClick={(e) => {
                e.stopPropagation();
                navigate(
                  `/master-data/${PermissionNames.unitEdit.replace(
                    ":id",
                    record!.id + ""
                  )}?update=1`
                );
              }}
            />
          )}

          {haveBlockPermission && (
            <LockButton
              isActive={record.isActive}
              onAccept={() => handleActiveUnit(record.id, record.isActive)}
              modalTitle={`${
                record.isActive ? "Khóa" : "Mở khóa"
              } đơn vị tính ${record.name}`}
              modalContent={
                <>
                  <div>
                    Khi {record.isActive ? "khóa" : "mở khóa"} đơn vị tính các
                    thông tin của đơn vị tính này cũng sẽ được{" "}
                    {record.isActive ? "khóa" : "mở khóa"}.
                  </div>
                  <div>
                    Bạn có chắc chắn muốn {record.isActive ? "khóa" : "mở khóa"}{" "}
                    đơn vị tính này?
                  </div>
                </>
              }
            />
          )}
        </Space>
      ),
      defaultVisible: true,
      alwaysVisible: true,
    },
  ];

  const pagination = {
    current: 1,
    pageSize: 10,
    total: units.length,
    showSizeChanger: true,
  };

  return (
    <div className="app-container">
      <PageTitle
        title={title}
        breadcrumbs={["Dữ liệu nguồn", title]}
        extra={
          <Space>
            {haveAddPermission ? (
              <>
                <CustomButton
                  size="small"
                  showPlusIcon
                  onClick={() => {
                    // modalRef.current?.handleCreate();
                    navigate(`/master-data/${PermissionNames.unitAdd}`);
                  }}
                >
                  Tạo đơn vị tính
                </CustomButton>
                <CustomButton
                  size="small"
                  icon={<ImportOutlined />}
                  onClick={() => {
                    importModal.current?.open();
                  }}
                >
                  Nhập excel
                </CustomButton>
              </>
            ) : null}
          </Space>
        }
      />
      <Card>
        <div className="flex gap-[16px] items-end pb-[12px] justify-between">
          <div className="flex gap-[16px] items-end flex-wrap">
            <div className="w-[300px]">
              <CustomInput
                tooltipContent={"Tìm theo mã, tên đơn vị tính"}
                label="Tìm kiếm"
                placeholder="Tìm kiếm"
                onPressEnter={() => {
                  query.page = 1;
                  setQuery({ ...query });
                  fetchData();
                }}
                value={query.search}
                onChange={(value) => {
                  query.search = value;
                  setQuery({ ...query });

                  if (!value) {
                    fetchData();
                  }
                }}
                allowClear
              />
            </div>
            <div>
              <QueryLabel>Loại</QueryLabel>
              <Select
                allowClear={true}
                placeholder="Chọn loại đơn vị"
                value={query.type ?? ""}
                onChange={(value) => {
                  query.type = value || "";
                  setQuery({ ...query });
                }}
                style={{ minWidth: 180 }}
                options={[
                  {
                    value: "",
                    label: "Tất cả các loại",
                  },
                  ...Object.values(UnitTypeTrans).map((item) => ({
                    value: item.value,
                    label: item.label,
                  })),
                ]}
              />
            </div>
            <div>
              <QueryLabel>Trạng thái</QueryLabel>
              <Select
                allowClear={true}
                placeholder="Chọn trạng thái"
                value={query.isActive ?? ""}
                onChange={(value) => {
                  query.isActive = value || "";
                  setQuery({ ...query });
                }}
                style={{ minWidth: 150 }}
                options={[
                  {
                    value: "",
                    label: "Tất cả trạng thái",
                  },
                  {
                    value: "true",
                    label: "Hoạt động",
                  },
                  {
                    value: "false",
                    label: "Bị khóa",
                  },
                ]}
              />
            </div>
            <CustomButton
              onClick={() => {
                query.page = 1;
                setQuery({ ...query });
                fetchData();
              }}
            >
              Áp dụng
            </CustomButton>

            {!isEmptyQuery && (
              <CustomButton
                variant="outline"
                onClick={() => {
                  delete query.type;
                  delete query.isActive;
                  delete query.search;
                  setQuery({ ...query });
                  fetchData();
                }}
              >
                Bỏ lọc
              </CustomButton>
            )}
          </div>

          <CustomButton
            onClick={() => {
              Modal.confirm({
                title: `Bạn có muốn xuất file excel?`,
                getContainer: () => {
                  return document.getElementById("App") as HTMLElement;
                },
                icon: null,

                footer: (_, { OkBtn, CancelBtn }) => (
                  <>
                    <CustomButton
                      variant="outline"
                      className="cta-button"
                      onClick={() => {
                        handleExport({
                          onProgress(percent) {
                            console.log("What is percent", percent);
                          },
                          exportColumns,
                          fileType: "xlsx",
                          dataField: "units",
                          query: query,
                          api: unitApi.findAll,
                          fileName: "Danh sách đơn vị tính",
                          sheetName: "Danh sách đơn vị tính",
                        });
                        Modal.destroyAll();
                      }}
                    >
                      Có
                    </CustomButton>
                    <CustomButton
                      onClick={() => {
                        Modal.destroyAll();
                      }}
                      className="cta-button"
                    >
                      Không
                    </CustomButton>
                  </>
                ),
              });
            }}
          >
            Xuất excel
          </CustomButton>
        </div>
        <CustomizableTable
          columns={filterActionColumnIfNoPermission(columns, [
            haveEditPermission,
            haveBlockPermission,
          ])}
          dataSource={units}
          rowKey="id"
          loading={loading}
          pagination={false}
          scroll={{ x: 1200 }}
          bordered
          displayOptions
          //@ts-ignore
          onChange={handleTableChange}
          onRowClick={handleRowClick}
        />

        <Pagination
          currentPage={query.page}
          defaultPageSize={query.limit}
          total={total}
          onChange={({ limit, page }) => {
            query.page = page;
            query.limit = limit;
            setQuery({ ...query });
            fetchData();
          }}
        />
        {useMemo(
          () => (
            <ImportUnit
              guide={[
                "Vui lòng tải và sử dụng file mẫu để import dữ liệu đúng cách",
                "Không được thay đổi tiêu đề trong file csv/excel mẫu để tránh import thiếu dữ liệu",
                "Chi tiết xem tại sheet Hướng dẫn của file import mẫu",
                "Đảm bảo các trường bắt buộc như Tên đơn vị, Loại đơn vị được điền đầy đủ",
              ]}
              onSuccess={() => {
                query.page = 1;
                fetchData();
              }}
              ref={importModal}
              createApi={unitApi.create}
              onUploaded={(excelData, setData) => {
                console.log("up gì lên vậy", excelData);
                handleOnUploadedFile(excelData, setData);
              }}
              okText={`Nhập đơn vị tính ngay`}
              // demoExcel="/exportFile/file_mau_nhap_don_vi_tinh.xlsx"
              onDownloadDemoExcel={handleDownloadDemoExcel}
            />
          ),
          []
        )}
      </Card>
    </div>
  );
});
