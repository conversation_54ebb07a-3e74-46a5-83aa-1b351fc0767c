import { Checkbox, Radio } from "antd";
import { ReactComponent as CheckIcon } from "assets/svgs/check.svg";
import CustomizableTable, {
  CustomizableColumn,
  CustomizableTableProps,
} from "components/Table/CustomizableTable";
import { cloneDeep, uniq } from "lodash";
import { observer } from "mobx-react";
import React from "react";
import { Route } from "router/RouteType";
import { permissionStore } from "store/permissionStore";
import { PermissionType, PermissionTypeTrans } from "types/permission";
import { Role } from "types/role";
import { isStringPartOfEachOther } from "utils/data";
import {
  permissionDataForMenuRole,
  permissionForStandardRole,
} from "../utils/permissionData";

interface RoleColumnBase {
  feature: string;
  routeChildren?: Route[];
  path?: string;
  key?: string;
  name?: string;
  isPublic?: boolean;
}

export type RoleColumn = RoleColumnBase & {
  [key in PermissionType]?: { isDisabled?: boolean }; // Make the properties from the enum optional
};

interface Props {
  checkedNames: string[]; // Danh sách permission names đã được chọn
  readOnly?: boolean; // Chế độ chỉ đọc (không cho phép edit)
  loading?: boolean; // Trạng thái loading
  setCheckedNames: React.Dispatch<React.SetStateAction<string[]>>; // Callback khi thay đổi selection
  tableProps?: CustomizableTableProps<RoleColumn>;
}

enum RoleRadioType {
  None = "NONE",
  ReadOnly = "READ_ONLY",
  Standard = "STANDARD",
  Admin = "ADMIN",
}

const RoleTable: React.FC<Props> = observer(
  ({ checkedNames, readOnly, loading, tableProps, setCheckedNames }) => {
    /**
     * Kiểm tra một permission có được chọn hay không
     * @param parentName - Tên của parent route
     * @param permissionType - Loại permission (list, create, edit, delete, etc.)
     * @returns Object chứa trạng thái checked và tên permission đầy đủ
     */
    const checkIsChecked = (
      parentName: string,
      permissionType: PermissionType
    ) => {
      // Tìm parent route trong cấu hình permission
      const findParent = permissionDataForMenuRole.find(
        (it) => it.name == parentName
      );

      if (findParent) {
        // Tạo tên permission đầy đủ theo format: parentName/permissionType
        // Với edit permission thêm "/:id" vào cuối
        const fullName = `${parentName}/${permissionType}${
          permissionType == PermissionType.Edit ? "/:id" : ""
        }`;

        // Kiểm tra permission có tồn tại trong danh sách cho phép hay không
        if (!findParent.permissions.includes(fullName as any)) {
          return { isChecked: null, checkedName: fullName }; // null = disabled
        }

        // Trả về trạng thái checked dựa trên checkedNames prop
        return {
          isChecked: checkedNames.includes(fullName),
          checkedName: fullName,
        };
      }
      return { isChecked: null, checkedName: "" };
    };
    /**
     * Render các checkbox permission cho một hàng trong bảng
     * @param route - Dữ liệu của hàng hiện tại
     * @returns JSX Element chứa các checkbox permissionß
     */
    const renderPermissionCheckboxes = (
      route: RoleColumn,
      isChildren = false
    ) => {
      // debugger
      // Lấy tên parent từ route name (bỏ phần sau dấu "/")
      const parentName = route.name?.split("/")[0] || "";
      const permissionAvailable =
        permissionDataForMenuRole.find((it) => it.name == parentName)
          ?.permissions || [];
      return (
        <div className="flex flex-col gap-2">
          {/* Duyệt qua tất cả các loại permission (List, Create, Edit, Delete, v.v.) */}
          {Object.values(PermissionTypeTrans)
            .filter((it) =>
              permissionAvailable.some((_it) => _it.includes(it.value as any))
            )
            .map((permType) => {
              const { isChecked, checkedName } = checkIsChecked(
                parentName || "",
                permType.value
              );

              // Chế độ chỉ đọc: chỉ hiển thị icon check hoặc dấu gạch ngang
              if (readOnly) {
                return (
                  <div key={permType.value} className="flex items-center gap-2">
                    <span className="min-w-[100px] text-sm">
                      {permType.label}:
                    </span>
                    {isChecked ? <CheckIcon /> : <div>-</div>}
                  </div>
                );
              }

              // Chế độ chỉnh sửa: hiển thị checkbox có thể tương tác
              return (
                <div key={permType.value} className="flex items-center gap-2">
                  <span className="min-w-[100px] text-sm">
                    {permType.label}:
                  </span>
                  <Checkbox
                    // disabled={
                    //   // Disable checkbox nếu:
                    //   // 1. Route là public và permission type là List (luôn có quyền)
                    //   // 2. Permission không tồn tại (isChecked === null)
                    //   (route.isPublic &&
                    //     permType.value == PermissionType.List) ||
                    //   isChecked == null
                    // }
                    disabled={true}
                    checked={
                      // Checked nếu:
                      // 1. Route là public và permission type là List (luôn checked)
                      // 2. Permission được chọn trong checkedNames
                      // (route.isPublic &&
                      //   permType.value == PermissionType.List) ||
                      !!isChecked
                    }
                    onChange={(e) => {
                      const checked = e.target.checked;
                      if (checked) {
                        // Thêm permission vào danh sách (sử dụng uniq để tránh duplicate)
                        setCheckedNames(uniq([...checkedNames, checkedName]));
                      } else {
                        // Xóa permission khỏi danh sách
                        setCheckedNames(
                          checkedNames.filter((k) => k !== checkedName)
                        );
                      }
                    }}
                  />
                </div>
              );
            })}
        </div>
      );
    };

    /**
     * Cấu hình columns mặc định cho table
     */
    const parentColumns: CustomizableColumn<RoleColumn>[] = [
      {
        title: "Chức năng",
        dataIndex: "feature",
        key: "feature",
        render: (text: string) => <div className="font-medium">{text}</div>,
      },
      {
        title: "None",
        key: "none",
        width: 100,
        align: "center",
      },
      {
        title: "Read only",
        key: "readOnly",
        width: 100,
        align: "center",
      },
      {
        title: "Standard",
        key: "standard",
        width: 100,
        align: "center",
      },
      {
        title: "Admin",
        key: "admin",
        width: 100,
        align: "center",
      },
      // {
      //   title: "Quyền",
      //   key: "permissions",
      //   render: (_: unknown, record: RoleColumn) => {
      //     // Chỉ hiển thị permissions cho các hàng không có children (leaf nodes)
      //     // Parent rows chỉ hiển thị tên chức năng, permissions hiển thị ở child rows
      //     if (
      //       record.isCompact &&
      //       record.routeChildren &&
      //       record.routeChildren.length == 1
      //     ) {
      //       return renderPermissionCheckboxes(record);
      //     }
      //     return <></>;
      //   },
      // },
    ];

    const checkRoleTypeChecked = (childRoute: RoleColumn): RoleRadioType => {
      // if (childRoute.name?.includes("project-phonebook")) debugger;
      // debugger;

      const parentName = childRoute.name?.split("/")[0] || "";

      //Các permission liên quan tới parentName của role hiện tại
      const permissionsRelated =
        checkedNames.filter(
          (p) => {
            const _p = p.split("/")[0];
            return _p == parentName;
          }
          // isStringPartOfEachOther(p, parentName || "")
        ) || [];

      //Các permission có thể có của parentName
      const permissionsOfRoute =
        permissionDataForMenuRole.find((p) => p.name == parentName)
          ?.permissions || [];

      if (permissionsRelated.length >= permissionsOfRoute.length) {
        return RoleRadioType.Admin;
      } else if (
        permissionsRelated.length > 1 &&
        permissionForStandardRole
          .filter((p) =>
            permissionsOfRoute.find((_p) => isStringPartOfEachOther(p, _p))
          )
          .every((p) =>
            permissionsRelated.find((_p) => isStringPartOfEachOther(_p, p))
          )
      ) {
        return RoleRadioType.Standard;
      } else if (
        permissionsRelated.length == 1 &&
        isStringPartOfEachOther(permissionsRelated[0], PermissionType.List)
      ) {
        return RoleRadioType.ReadOnly;
      }
      return RoleRadioType.None;
    };

    const handleCheckedRadio = (
      type: RoleRadioType,
      checked: boolean,
      childRoute: RoleColumn
    ) => {
      const parentName = childRoute.name?.split("/")[0] || "";

      if (checked) {
        switch (type) {
          //Bỏ các permissionName thuộc route này
          case RoleRadioType.None:
            setCheckedNames((prev) =>
              prev.filter((it) => {
                const _it = it.split("/")[0];

                return !(_it == parentName);
              })
            );
            break;

          //Lấy list permissionName thuộc route này
          case RoleRadioType.ReadOnly:
            const permissionName =
              permissionDataForMenuRole
                .find((p) => p.name == parentName)
                ?.permissions.find((p) =>
                  isStringPartOfEachOther(p, PermissionType.List)
                ) || "";
            if (permissionName) {
              let _checkedNames = checkedNames.filter((it) => {
                const _it = it.split("/")[0];

                return !(_it == parentName);
              });
              setCheckedNames(uniq([..._checkedNames, permissionName]));
            }
            break;

          //Lấy các standard permissionName thuộc route này
          case RoleRadioType.Standard:
            const permissionNames =
              permissionDataForMenuRole
                .find((p) => p.name == parentName)
                ?.permissions.filter((p) =>
                  permissionForStandardRole.find((_p) =>
                    isStringPartOfEachOther(_p, p)
                  )
                ) || [];
            let _checkedNames = checkedNames.filter((it) => {
              const _it = it.split("/")[0];

              return !(_it == parentName);
            });
            setCheckedNames(uniq([..._checkedNames, ...permissionNames]));
            break;

          //Lấy hết permissionName thuộc route này
          case RoleRadioType.Admin:
            const allPermissionNames =
              permissionDataForMenuRole.find((p) => p.name == parentName)
                ?.permissions || [];
            setCheckedNames((prev) => uniq([...prev, ...allPermissionNames]));
            break;
          default:
            break;
        }
      } else {
      }
    };

    const childrenColumns: CustomizableColumn<RoleColumn>[] = [
      {
        title: "Chức năng",
        dataIndex: "feature",
        key: "feature",
        render: (text: string) => <div className="font-medium">{text}</div>,
      },
      {
        title: "None",
        key: "none",
        width: 100,
        align: "center",
        render: (_, childRoute) => {
          const roleType = checkRoleTypeChecked(childRoute);
          if (readOnly) {
            return roleType == RoleRadioType.None ? <CheckIcon /> : "-";
          }
          return (
            <Radio
              checked={roleType == RoleRadioType.None}
              onChange={(e) => {
                handleCheckedRadio(
                  RoleRadioType.None,
                  e.target.checked,
                  childRoute
                );
              }}
            ></Radio>
          );
        },
      },
      {
        title: "Read only",
        key: "readOnly",
        width: 100,
        align: "center",
        render: (_, childRoute) => {
          const roleType = checkRoleTypeChecked(childRoute);
          if (readOnly) {
            return roleType == RoleRadioType.ReadOnly ? <CheckIcon /> : "-";
          }
          return (
            <Radio
              checked={roleType == RoleRadioType.ReadOnly}
              onChange={(e) => {
                handleCheckedRadio(
                  RoleRadioType.ReadOnly,
                  e.target.checked,
                  childRoute
                );
              }}
            ></Radio>
          );
        },
      },
      {
        title: "Standard",
        key: "standard",
        width: 100,
        align: "center",
        render: (_, childRoute) => {
          const roleType = checkRoleTypeChecked(childRoute);
          if (readOnly) {
            return roleType == RoleRadioType.Standard ? <CheckIcon /> : "-";
          }
          return (
            <Radio
              checked={roleType == RoleRadioType.Standard}
              onChange={(e) => {
                handleCheckedRadio(
                  RoleRadioType.Standard,
                  e.target.checked,
                  childRoute
                );
              }}
            ></Radio>
          );
        },
      },
      {
        title: "Admin",
        key: "admin",
        width: 100,
        align: "center",
        render: (_, childRoute) => {
          const roleType = checkRoleTypeChecked(childRoute);
          if (readOnly) {
            return roleType == RoleRadioType.Admin ? <CheckIcon /> : "-";
          }
          return (
            <Radio
              checked={roleType == RoleRadioType.Admin}
              onChange={(e) => {
                handleCheckedRadio(
                  RoleRadioType.Admin,
                  e.target.checked,
                  childRoute
                );
              }}
            ></Radio>
          );
        },
      },
      ...(false
        ? [
            {
              title: "Quyền",
              key: "permissions",
              render: (_: unknown, record: RoleColumn) => {
                // Chỉ hiển thị permissions cho các hàng không có children (leaf nodes)
                // Parent rows chỉ hiển thị tên chức năng, permissions hiển thị ở child rows
                return renderPermissionCheckboxes(record);
                return <></>;
              },
            },
          ]
        : []),
    ];

    /**
     * Render nội dung mở rộng khi click vào arrow expand
     * Hiển thị các submenu/child routes dưới dạng table con
     * @param route - Parent record được expand
     * @returns JSX Element chứa sub-table
     */
    const expandedRowRender = (route: RoleColumn) => {
      // Chuyển đổi child routes thành format phù hợp cho table
      const childData =
        route.routeChildren
          ?.filter((childRoute) => !childRoute.hidden) // Lọc bỏ hidden routes
          .map((childRoute) => ({
            feature: childRoute.title || "",
            key: childRoute.name, // Tạo key unique
            path: childRoute.path,
            name: childRoute.name,
            parentName: route.name,
            isPublic: childRoute.isPublic,
            routeChildren: childRoute.children, // Hỗ trợ nested children
          })) || [];

      return (
        <CustomizableTable<RoleColumn>
          columns={childrenColumns}
          dataSource={childData}
          pagination={false}
          className="role-sub-table"
          rowKey="key"
          expandable={{
            expandedRowRender: () => <></>, // Tạm thời không hỗ trợ expand level 3
            rowExpandable: () => false,
            expandIcon: () => null,
          }}
          showHeader={false}
        />
      );
    };

    /**
     * Chuẩn bị dữ liệu cho table từ permissionStore
     * Lọc và chuyển đổi accessRoutes thành format phù hợp
     */
    const tableData: RoleColumn[] = cloneDeep(permissionStore.accessRoutes)
      .filter((r): r is NonNullable<typeof r> => {
        // Chỉ hiển thị routes không bị ẩn và không phải public routes
        if (!r.hidden && !r.isPublic) return true;
        return false;
      })
      .map((r) => {
        return {
          feature: r.title || "", // Tên hiển thị của chức năng
          routeChildren: r.children, // Các route con
          key: r.name, // Key unique cho table row
          name: r.name, // Route name để tạo permission path
          permissionTypes: r.permissionTypes, // Các loại permission hỗ trợ
          path: r.path, // Đường dẫn route
          isCompact: r.isCompact, // Compact route
        };
      });

    return (
      <CustomizableTable<RoleColumn>
        {...tableProps}
        className="role-table"
        columns={parentColumns}
        expandable={{
          expandedRowKeys: cloneDeep(permissionStore.accessRoutes)
            .filter((r): r is NonNullable<typeof r> => {
              // Chỉ hiển thị routes không bị ẩn và không phải public routes
              if (!r.hidden && !r.isPublic) return true;
              return false;
            })
            .map((r) => r.name || ""),
          expandedRowRender, // Hàm render nội dung expand
          rowExpandable: (record) =>
            !!record.routeChildren && record.routeChildren.length > 1, // Chỉ cho expand nếu có children
          expandIcon: ({ expanded, onExpand, record, expandable }) => null,
          // expandable ? (
          //   // Custom expand icon với animation
          //   <div onClick={(e) => onExpand(record, e)}>
          //     <ArrowDownIcon
          //       className={clsx(
          //         "transition-all cursor-pointer",
          //         expanded ? "" : "-rotate-90" // Rotate icon khi collapse
          //       )}
          //     />
          //   </div>
          // ) : null,
        }}
        dataSource={tableData}
        loading={loading}
        pagination={false} // Không sử dụng pagination
        bordered
        rowKey="key"
      />
    );
  }
);

export default RoleTable;
