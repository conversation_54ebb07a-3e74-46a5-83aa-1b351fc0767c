import {
  Button,
  Checkbox,
  Col,
  Form,
  Image,
  Input,
  message,
  Modal,
  Popconfirm,
  Radio,
  Row,
  Select,
  Space,
  Switch,
  Table,
  Upload,
  UploadProps,
} from "antd";

import React, { useEffect, useImperativeHandle, useState } from "react";
import { ModalStatus } from "types/modal";
import { Material } from "types/material";

import { useWatch } from "antd/lib/form/Form";

import { DeleteOutlined } from "@ant-design/icons";
import { InputNumber } from "components/Input/InputNumber";

import { requiredRule } from "utils/validateRule";

import { cloneDeep, debounce, fromPairs, uniqueId } from "lodash";

import { $url } from "utils/url";
import { useComponent } from "hooks/useComponent";
import ChooseFileFromMenu from "components/Upload/ChooseImageFromMenu";
import ChooseImageModal, {
  ChooseImageModalRef,
} from "components/Modal/ChooseImageModal";
import {
  BOM,
  MainComponent,
  Product,
  ProductCreating,
  ProductDetail,
  ProductStatus,
  ProductTypeTrans,
} from "types/product";
import { productApi } from "api/product.api";
import { IoAddCircleOutline, IoAddOutline } from "react-icons/io5";
import {
  AddBillOfMaterialModal,
  BillOfMaterialCreating,
  BillOfMaterialRef,
  BillOfMaterialUpdate,
} from "./AddBillOfMaterialModal";
import Column from "antd/es/table/Column";
import {
  AddSingleProductModal,
  addSingleProductRef,
} from "./AddSingleProductModal";
import DraggableSubProductTable from "./DraggableSubProductTable";
import { subProductApi } from "api/subProduct.api";
import { SubProduct } from "types/subProduct";
import { useMaterial } from "hooks/useMaterial";
import { BMDTextArea } from "components/TextArea/BMDTextArea";

export interface ComponentModal {
  handleCreate: () => void;
  handleUpdate: (product: Product) => void;
}
interface ComponentModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
  isCombo?: boolean;
}

interface MaterialForm extends Material {
  colorId: number;
  materialGroupId: number;
}
export const CreateComboProductModal = React.forwardRef(
  ({ onClose, onSubmitOk, isCombo = false }: ComponentModalProps, ref) => {
    const [form] = Form.useForm<ProductCreating>();
    const icon = useWatch("fileAttachIcon", form);
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");
    const [sortModalVisible, setSortModalVisible] = useState(false);
    const [selectedProduct, setSelectedProduct] = useState<Product>();
    const [billOfMaterial, setBillOfMaterial] = useState<
      BillOfMaterialCreating[]
    >([]);
    const [currentSubProducts, setCurrentSubProducts] = useState<SubProduct[]>(
      []
    );
    const [sortedProductDraft, setSortedProductDraft] = useState<SubProduct[]>(
      []
    );
    const {
      fetchData: fetchMaterials,
      loading: loadingMaterials,
      materials,
      query: queryMaterial,
      setQuery: setQueryMaterial,
      setData: setMaterial,
    } = useMaterial({
      initQuery: {
        page: 1,
        limit: 100,
      },
    });

    const handleSearch = debounce((value: string) => {
      queryMaterial.search = value;
      query.page = 1;
      setQueryMaterial({ ...queryMaterial });
      fetchMaterials();
    }, 300);
    // const applyNewPosition = async () => {
    //   console.log({ sortedProductDraft });
    //   try {
    //     const subProducts = sortedProductDraft.map((item, index) => ({
    //       id: item.id,
    //       position: index,
    //     }));

    //     await subProductApi.updatePos({ subProducts });

    //     setCurrentSingleProduct(cloneDeep(sortedProductDraft));
    //     setSortModalVisible(false);
    //     message.success("Cập nhật vị trí thành công");
    //   } catch (error) {
    //     console.error("Cập nhật vị trí thất bại", error);
    //   }
    // };

    useImperativeHandle<any, ComponentModal>(
      ref,
      () => ({
        handleCreate() {
          form.resetFields();
          setCurrentSubProducts([]);
          setVisible(true);
          setStatus("create");
          form.setFieldValue("type", isCombo ? "COMBO" : "SINGLE");
        },
        handleUpdate(product) {
          console.log("Component là", product);
          // setSelectedProduct(product);
          // setCurrentSubProducts(product.subProducts);
          // form.setFieldsValue({
          //   ...product,
          //   boms: product.boms || [],
          // });
          setVisible(true);
          setStatus("update");
          handleGetOneProduct(product.id);
        },
      }),
      [materials]
    );
    const createData = async () => {
      const data = form.getFieldsValue();
      const {
        defaultMaterialIds,
        parentId,
        boms,
        mainComponents,
        fileAttachIcon,
        ...restData
      } = data;
      console.log("When create data", fileAttachIcon);
      try {
        const res = await productApi.create({
          product: {
            ...restData,
          },
          fileAttachIconId: fileAttachIcon?.id,
          boms: [],
          mainComponent: [],
          subProducts: currentSubProducts?.map((item) => ({
            productSingleId: item.productSingle?.id,
            isRequired: item.isRequired ?? false,
          })),
          defaultMaterialIds,
        });
        message.success("Tạo thành công");
        handleClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const data = form.getFieldsValue();
      setLoading(true);
      console.log("What is in form", data);
      const {
        id,
        parentId,
        boms,
        mainComponents,
        fileAttachIcon,
        defaultMaterialIds,

        ...restData
      } = data;
      console.log("Rest data là", restData);
      try {
        console.log("Data when update", data);
        await changeProductStatus(data);
        const res = await productApi.update(id, {
          product: { ...restData },
          fileAttachIconId: fileAttachIcon?.id,
          boms:
            billOfMaterial?.map((item) => ({
              materialCode: item.materialCode,
              quantity: item.quantity,
            })) || [],
          parentId: data?.parentId,
          subProducts: currentSubProducts?.map((item) => ({
            productSingleId: item.productSingle?.id,
            isRequired: item.isRequired ?? false,
          })),
          defaultMaterialIds,
          mainComponent: [],
        });
        message.success("Cập nhật thành công");
        handleClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };
    const handleGetOneProduct = async (id: number) => {
      try {
        const { data } = await productApi.findOne(id);
        // data?.subProducts.map((item: any) => {
        //   console.log("Data in find one", item);
        // });
        // setCurrentSubProducts(
        //   data?.subProducts?.map((item: any) => item.productSingle) || []
        // );
        setSelectedProduct(data);
        setCurrentSubProducts(data.subProducts);
        form.setFieldsValue({
          ...data,
          boms: data.boms || [],
          defaultMaterialIds: data.defaultMaterials?.map(
            (it: Material) => it.id
          ),
        });
        if (data.defaultMaterials && Array.isArray(data.defaultMaterials)) {
          const newMaterials = data.defaultMaterials.filter(
            (m: Material) => !materials.find((e) => e.id === m.id)
          );
          // debugger;
          if (newMaterials.length > 0) {
            setMaterial([...materials, ...newMaterials]);
          }
        }
        console.log("What is data", data);
      } catch (error) {}
    };
    console.log("Current single product là", currentSubProducts);
    const handleClose = () => {
      onClose();
      setVisible(false);
      form.resetFields();
    };
    const handleUpdateItem = (updatedItem: BillOfMaterialCreating) => {
      // const currentBoms: BillOfMaterialUpdate[] =
      //   form.getFieldValue("boms") || [];

      // const newBoms = currentBoms.map((item) =>
      //   item.id === updatedItem.id ? updatedItem : item
      // );

      // form.setFieldValue("boms", newBoms);
      const current = billOfMaterial.find((item) => item.id == updatedItem.id);
      if (current) {
        Object.assign(current, updatedItem);
        setBillOfMaterial(cloneDeep(billOfMaterial));
      }
    };
    const handleUpdateMainComponentItem = (updatedItem: MainComponent) => {
      // debugger
      const current = currentSubProducts?.find(
        (item) => item.id == updatedItem.id
      );
      if (current) {
        Object.assign(current, updatedItem);
        setCurrentSubProducts(cloneDeep(currentSubProducts));
      }
      // const current = form.getFieldValue("mainComponents") || [];
      // const updated = current.map((item: any) =>
      //   item.id === updatedItem?.id ? updatedItem : item
      // );
      // form.setFieldValue("mainComponents", updated);
    };
    const {
      components,
      fetchData,
      query,
      loading: componentLoading,
      setQuery,
      total,
    } = useComponent({
      initQuery: {
        page: 1,
        limit: 10,
      },
    });
    useEffect(() => {
      fetchData();
      fetchMaterials();
    }, []);
    const changeProductStatus = async (product: Product) => {
      let response;
      if (product.status == ProductStatus.Active) {
        response = await productApi.active(product.id);
      } else {
        response = await productApi.inactive(product.id);
      }
    };
    const modalRef = React.useRef<BillOfMaterialRef>(null);
    const mainComponentModalRef = React.useRef<addSingleProductRef>(null);

    return (
      <Modal
        onCancel={() => {
          onClose?.();
          setVisible(false);
        }}
        open={visible}
        title={
          status == "create"
            ? "Tạo bộ sản phẩm"
            : `Sửa bộ sản phẩm - ID: ${selectedProduct?.id}`
        }
        style={{ top: 20 }}
        width={1200}
        confirmLoading={loading}
        onOk={() => {
          form.submit();
        }}
      >
        <Form
          layout="vertical"
          form={form}
          onFinish={() => {
            status == "create" ? createData() : updateData();
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="Avatar"
                name="fileAttachIcon"
                rules={[requiredRule]}
              >
                <ChooseFileFromMenu
                  fileUrl={icon?.url}
                  onSelectOk={(url, file) => {
                    console.log("File lấy đc là", file);
                    form.setFieldValue("fileAttachIcon", file);
                  }}
                  ratioText="Tỉ lệ 1x1"
                  fileName={icon?.name}
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <h2>Thông tin bộ sản phẩm</h2>
            </Col>
            <Col span={8}>
              <Form.Item label="Tên nội bộ" name="name" rules={[requiredRule]}>
                <Input placeholder="" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="Tên hiển thị"
                name="nameVi"
                rules={[requiredRule]}
              >
                <Input placeholder="" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="Mã sản phẩm" name="code" rules={[requiredRule]}>
                <Input placeholder="" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="Loại sản phẩm" name="type">
                <Select
                  disabled
                  allowClear
                  options={Object.values(ProductTypeTrans).map((item) => ({
                    label: item.label,
                    value: item.value,
                  }))}
                />
              </Form.Item>
            </Col>
            {/* <Col span={8}>
              <Form.Item
                label="Giá"
                name="price"
                // rules={[requiredRule]}
              >
                <InputNumber />
              </Form.Item>
            </Col> */}
            <Col span={8}>
              <Form.Item
                label="WooCommerce Code"
                name="syncId"
                // rules={[requiredRule]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="NVL mặc định"
                name="defaultMaterialIds"
                rules={[requiredRule]}
              >
                <Select
                  mode="multiple"
                  allowClear
                  filterOption={false}
                  showSearch
                  onSearch={handleSearch}
                  options={materials.map((item) => ({
                    label: item.code,
                    value: item.id,
                  }))}
                />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item
                label="Trạng thái"
                name="status"
                valuePropName="checked"
                getValueProps={(value) => ({
                  checked: value === ProductStatus.Active,
                })}
              >
                <Switch
                  checkedChildren="Hiện" // Khi switch đang bật (true)
                  unCheckedChildren="Ẩn"
                  onChange={(value) => {
                    console.log({ value });
                    if (value) {
                      form.setFieldValue("status", ProductStatus.Active);
                    } else {
                      form.setFieldValue("status", ProductStatus.Inactive);
                    }
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                label="Ghi chú"
                name="note"
                //  rules={[requiredRule]}
              >
                <BMDTextArea placeholder="" />
              </Form.Item>
            </Col>
            {[
              status === "update" && (
                <Col span={8}>
                  <Form.Item
                    className="hidden"
                    label="id"
                    name="id"
                    rules={[requiredRule]}
                  >
                    <Input placeholder="" />
                  </Form.Item>
                </Col>
              ),
            ]}
            {/* <Col span={24}>
              <h2>Thông tin nhóm</h2>
            </Col>
            <Col span={24}>
              <Form.Item
                label="Nhóm cha"
                name="parentId"
                // rules={[requiredRule]}
              >
                <Select
                  allowClear
                  options={components.map((component) => ({
                    label: component.code,
                    value: component.id,
                  }))}
                />
              </Form.Item>
            </Col> */}
            <Col span={24}>
              <div className="flex items-center gap-2 justify-between">
                <Space>
                  <h2>Chi tiết bộ sản phẩm</h2>
                  <span
                    className="flex items-center cursor-pointer hover:text-green-500"
                    onClick={() => {
                      mainComponentModalRef.current?.handleCreate(
                        currentSubProducts.map((it) => it.productSingle)
                      );
                    }}
                  >
                    <IoAddCircleOutline className="text-[25px]" />
                  </span>
                </Space>
                <Button
                  type="primary"
                  size="small"
                  onClick={() => {
                    setSortedProductDraft(
                      cloneDeep(
                        currentSubProducts.map((item) => ({
                          ...item,
                          // posId: item.posId,
                        }))
                      )
                    );

                    setSortModalVisible(true);
                  }}
                >
                  Sắp xếp
                </Button>
              </div>
            </Col>
            <Col span={24}>
              {/* <Form.Item
                label=""
                name="mainComponents"
                // rules={[requiredRule]}
              > */}
              <Table
                pagination={false}
                rowKey="id"
                dataSource={currentSubProducts}
                scroll={{ x: "max-content" }}
              >
                <Column
                  title="Mã sản phẩm"
                  dataIndex="name"
                  align="left"
                  key="name"
                  width={100}
                  render={(text, record: SubProduct) => {
                    return <div>{record?.productSingle?.code}</div>;
                  }}
                />
                <Column
                  title="Sản phẩm đơn"
                  dataIndex="name"
                  align="left"
                  key="name"
                  width={500}
                  render={(text, record: SubProduct) => {
                    return (
                      <div>
                        {/* <Image
                          src={$url(record.fileAttachIcon?.url)}
                          width={50}
                          height={50}
                        /> */}
                        {record.productSingle?.name}
                      </div>
                    );
                  }}
                />
                <Column
                  title="Bắt buộc"
                  dataIndex="isRequired"
                  align="left"
                  key="isRequired"
                  width={100}
                  render={(text, record: SubProduct) => {
                    return (
                      <div>
                        <Switch
                          checkedChildren="Bắt buộc"
                          unCheckedChildren="Không bắt buộc"
                          checked={record.isRequired}
                          onChange={(e) => {
                            setCurrentSubProducts((prev) =>
                              prev.map((item) =>
                                item.id === record.id
                                  ? { ...item, isRequired: e }
                                  : item
                              )
                            );
                          }}
                        />
                      </div>
                    );
                  }}
                />
                {/* <Column
                  title="Layer"
                  dataIndex="code"
                  align="left"
                  key="code"
                  render={(text, record: MainComponent) => {
                    return <div>{record.layer}</div>;
                  }}
                /> */}

                <Column
                  fixed="right"
                  width={100}
                  title="Thao tác"
                  align="center"
                  key="action"
                  render={(text, record: any) => (
                    <div className="flex gap-2 justify-center">
                      <Popconfirm
                        onConfirm={() => {
                          // const currentMainComponents =
                          //   form.getFieldValue("mainComponents") || [];
                          // const newCurrentMainComponents =
                          //   currentMainComponents.filter(
                          //     (item: MainComponent) => item.id !== record.id
                          //   );
                          // form.setFieldValue(
                          //   "mainComponents",
                          //   newCurrentMainComponents
                          // );
                          setCurrentSubProducts(
                            cloneDeep(
                              currentSubProducts?.filter(
                                (item) => item.id !== record.id
                              )
                            )
                          );
                        }}
                        title="Xác nhận xóa"
                      >
                        <Button danger>
                          <DeleteOutlined />
                        </Button>
                      </Popconfirm>
                      {/* <Button
                        type="primary"
                        onClick={() => {
                          mainComponentModalRef.current?.handleUpdate(record);
                        }}
                      >
                        <EditOutlined />
                      </Button> */}
                    </div>
                  )}
                />
              </Table>
              {/* </Form.Item> */}
            </Col>
          </Row>
        </Form>
        <Modal
          width={1000}
          open={sortModalVisible}
          title="Sắp xếp danh sách sản phẩm"
          cancelText="Đóng"
          onCancel={() => {
            setSortModalVisible(false);
          }}
          okText="Áp dụng"
          onOk={() => {
            setCurrentSubProducts(cloneDeep(sortedProductDraft));
            setSortModalVisible(false);
          }}
          // footer={(originFooter) => {
          //   return [
          //     originFooter,
          //     <Button
          //       type="primary"
          //       onClick={() => {
          //         // setProductionProcedures(
          //         //   cloneDeep(productionProceduresReorder)
          //         // );
          //         setOpenReorderJob(false);
          //         handleSubmit({ productionProceduresReorder });
          //       }}
          //     >
          //       Áp dụng và cập nhật
          //     </Button>,
          //   ];
          // }}
        >
          {sortedProductDraft && (
            <DraggableSubProductTable
              dataSource={sortedProductDraft}
              onChangeOrder={(newList) => {
                setSortedProductDraft([...newList]);
              }}
              columns={[
                {
                  title: "Mã sản phẩm",
                  dataIndex: "code",
                  key: "code",
                  width: 100,
                  render: (_, record) => (
                    <div>{record.productSingle?.code}</div>
                  ),
                },
                {
                  title: "Sản phẩm đơn",
                  dataIndex: "name",
                  key: "name",
                  width: 500,
                  render: (_, record) => (
                    <div>{record.productSingle?.name}</div>
                  ),
                },
              ]}
            />
          )}
        </Modal>

        <AddSingleProductModal
          onSubmitOk={(items: Product[]) => {
            console.log({ items });
            const merged = [
              ...currentSubProducts,
              ...items
                .filter(
                  (it) =>
                    !currentSubProducts.find(
                      (sit) => sit.productSingle.id == it.id
                    )
                )
                .map((it, index) => {
                  return {
                    isRequired: true,
                    position: currentSubProducts.length + index,
                    productSingle: it,
                    id: uniqueId(),
                  };
                }),
            ];

            // Dùng Map để loại trùng theo id
            const uniqueById = Array.from(
              new Map(
                merged.map((product) => [product.productSingle?.id, product])
              ).values()
            );

            setCurrentSubProducts(cloneDeep(uniqueById as SubProduct[]));
          }}
          onClose={() => {}}
          ref={mainComponentModalRef}
          onUpdate={handleUpdateMainComponentItem}
        />
      </Modal>
    );
  }
);
