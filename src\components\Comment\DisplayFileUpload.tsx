import FileItem from "components/Upload/CommentUploadFile/FileItem";
import React, { useState } from "react";
import { Progress, Image, Spin, Popconfirm } from "antd";
import { $url } from "utils/url";
import noImg from "assets/images/No-Image.png";
import { UploadFile } from "antd/es/upload/interface";
import { IoTrashOutline } from "react-icons/io5";
import { FiDownload } from "react-icons/fi";
import FileIcon from "components/Upload/CommentUploadFile/RenderIconFile";
import { downloadFile } from "utils/downloadFile";

interface DisplayFileUploadProps {
  fileList: UploadFile[];
  fileProgress: { [key: string]: number };
  onDelete?: (uid: string) => void;
}

const isImageFile = (type?: string) => {
  return type?.startsWith("image/");
};

const formatFileSize = (size: number = 0) => {
  if (size < 1024 * 1024) {
    return `${Math.round(size / 1024)} KB`;
  }
  return `${(size / 1024 / 1024).toFixed(2)} MB`;
};

const DisplayFileUpload: React.FC<DisplayFileUploadProps> = ({
  fileList,
  fileProgress,
  onDelete,
}) => {
  const [loadingDownload, setLoadingDownload] = useState<{
    [key: string]: boolean;
  }>({});

  return (
    <div className="grid grid-cols-4 gap-2">
      {fileList &&
        fileList.map((file) => {
          const isImage = isImageFile(file.type);
          return (
            <div
              key={file.uid}
              className="bg-[var(--color-neutral-n2)] rounded p-2 flex flex-col"
            >
              <div className="display-file-upload flex items-center gap-2 overflow-hidden justify-evenly">
                {isImage ? (
                  <Image
                    src={$url(file.url)}
                    alt={file.name}
                    className="w-6 h-6 object-cover rounded flex-shrink-0"
                    preview={{
                      mask: null,
                      maskClassName: "rounded-none",
                      toolbarRender: () => null,
                    }}
                    fallback={noImg}
                  />
                ) : (
                  <div className="w-6 h-6 flex items-center justify-center flex-shrink-0">
                    <FileIcon mimeType={file.type} url={file.url} />
                  </div>
                )}
                <div className="overflow-hidden">
                  <div className="font-medium text-xs truncate">
                    {file.name}
                  </div>
                  <div className="text-[10px] text-neutral-n4">
                    {formatFileSize(file.size)}
                  </div>
                </div>
                <div className="flex items-center justify-between cursor-pointer">
                  <div className="flex items-center gap-2">
                    {fileProgress[file.uid] &&
                      Math.round(fileProgress[file.uid]) > 0 && (
                        <Progress
                          size="small"
                          percent={fileProgress[file.uid]}
                          status="active"
                          strokeColor="#1fa6aa"
                          className="!w-12 !min-w-12"
                          showInfo={false}
                        />
                      )}
                    <Spin spinning={loadingDownload[file.uid] || false}>
                      <span
                        onClick={() =>
                          downloadFile(file.url || "", file.name, (loading) => {
                            setLoadingDownload((prev) => ({
                              ...prev,
                              [file.uid]: loading,
                            }));
                          })
                        }
                        className="cursor-pointer text-[18px] flex items-center gap-1"
                      >
                        <FiDownload className="text-primary text-lg cursor-pointer" />
                        {/* <span className="text-primary text-xs font-medium">
                        Tải file
                      </span> */}
                      </span>
                    </Spin>
                  </div>
                </div>

                {/* {onDelete && (
                  <Popconfirm
                    placement="topLeft"
                    title={`Xác nhận xóa file này?`}
                    onConfirm={() => onDelete(file.uid)}
                    okText="Xóa"
                    cancelText="Không"
                  >
                    <IoTrashOutline className="cursor-pointer text-red-500 text-sm"></IoTrashOutline>
                  </Popconfirm >
                )} */}
                {onDelete && (
                  <IoTrashOutline
                    className="cursor-pointer text-red-500 text-sm flex-shrink-0"
                    onClick={() => onDelete(file.uid)}
                  />
                )}
              </div>
            </div>
          );
        })}
    </div>
  );
};

export default DisplayFileUpload;
