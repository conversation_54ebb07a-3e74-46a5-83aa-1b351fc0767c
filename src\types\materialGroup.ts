import { Dictionary } from "./dictionary";
import { Material } from "./material";
import { MaterialType } from "./materialType";
import { Provider } from "./provider";

export interface MaterialGroup {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  name: string;
  image: string;
  materials: Material[];
  materialType: MaterialType;
  position: number;
  materialGroupDetails: MaterialGroupDetail[];
  isActive: boolean;
  providers: Provider[];
}
export interface MaterialGroupForm extends Partial<MaterialGroup> {
  materialTypeId: number;
}

export interface MaterialGroupDetail {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  materialGroup: MaterialGroup;
  dictionary: Dictionary;
}
