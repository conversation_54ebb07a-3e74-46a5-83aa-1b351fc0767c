import { DownloadOutlined, InboxOutlined } from "@ant-design/icons";
import {
  <PERSON><PERSON>,
  Button,
  Modal,
  Select,
  Space,
  Spin,
  Table,
  Upload,
  message,
  Form,
  Col,
  Row,
} from "antd";
import { Rule } from "antd/es/form";
import FormItem from "antd/es/form/FormItem";
import { componentApi } from "api/component.api";
import { dictionaryApi } from "api/dictionary.api";
import { materialGroupApi } from "api/materialGroup.api";
import { variantApi } from "api/variant.api";
import CustomButton from "components/Button/CustomButton";
import dayjs from "dayjs";
import { chunk } from "lodash";
import { toJS } from "mobx";
import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
  useRef,
  useMemo,
} from "react";
import { Link } from "react-router-dom";
import { MaterialGroup } from "types/materialGroup";
import { readerData } from "utils/excel2";
import { handleConfirmImportExcel } from "utils/function";
import ImportPreviewModule from "./ImportPreviewModule";
import { DictionaryType } from "types/dictionary";
import { DictionaryTreeTypes, isTypeTreeData } from "utils/common";
import { DictionarySelector } from "components/Selector/DictionarySelector";

const rules: Rule[] = [{ required: true }];

const { Dragger } = Upload;

export interface ImportDictionaryModal {
  open: () => void;
  close: () => void;
}

export interface IValidate {
  index: number;
  message: string;
}

interface DataImportReturn {
  msg: string;
  rowNum: number;
  status: string;
}

export interface DictionaryImport extends MaterialGroup {
  rowNum: number;
  parentName?: string; // From Excel for tree types
  // parentId?: number; // From UI selection
}

interface IProps {
  onSuccess?: () => void;
  createApi?: (data: any) => any;
  onUploaded?: (excelData: any, setData: (data: any) => any) => void;
  demoExcel?: string;
  guide?: React.ReactNode[];
  uploadText?: string;
  okText?: string;
  onClose?: () => void;
  titleText?: string;
  validateMessage?: IValidate[];
  onDownloadDemoExcel?: () => void;
  dictionaryType?: DictionaryType; // Add type prop to determine validation
}

const ImportDictionary = forwardRef(
  (
    {
      onSuccess,
      createApi,
      onUploaded,
      onClose,
      validateMessage,
      guide,
      demoExcel,
      uploadText = "Kéo thả hoặc click vào đây để upload file",
      okText = "Nhập dữ liệu ngay",
      titleText = "Nhập excel dữ liệu",
      onDownloadDemoExcel,
      dictionaryType,
    }: IProps,
    ref
  ) => {
    const [form] = Form.useForm();
    const [errorsLog, setErrorsLog] = useState<any[]>([]);
    const [dataPosts, setDataPosts] = useState<DictionaryImport[]>([]);
    const [visible, setVisible] = useState<boolean>(false);
    const [loading, setLoading] = useState(false);
    const [hasValidationErrors, setHasValidationErrors] = useState(false);
    const [selectedParentId, setSelectedParentId] = useState<
      number | undefined
    >();
    const [dataReturn, setDataReturn] = useState<{
      data: DataImportReturn[];
      successCount: number;
      errorCount: number;
    }>();

    useEffect(() => {
      if (validateMessage?.length) {
        setErrorsLog([]);
      }
    }, [validateMessage]);

    // Callback to receive validation status from ImportPreviewModule
    const handleValidationStatusChange = (hasErrors: boolean) => {
      console.log("🚨 Dictionary validation status changed:", hasErrors);
      setHasValidationErrors(hasErrors);
    };

    // Determine if this dictionary type supports tree structure
    const isTreeType = useMemo(() => {
      if (!dictionaryType) return false;

      return isTypeTreeData(dictionaryType);
    }, [dictionaryType]);

    const handleOnImport = async () => {
      if (!dataPosts.length) return;
      let errors: any = [];

      try {
        setLoading(true);
        await handleConfirmImportExcel();

        // Add parentId to all items if selected
        const dataWithParent = dataPosts.map((dataPost) => ({
          ...dataPost,
          parentId: selectedParentId,
        }));

        const { data } = await dictionaryApi.import({
          dictionaries: dataWithParent,
        });

        if (data.length) {
          const successCount = data.reduce(
            (acc: number, item: DataImportReturn) => {
              if (item.status == "ok") return acc + 1;
              return acc;
            },
            0
          );
          const errorCount = data.reduce(
            (acc: number, item: DataImportReturn) => {
              if (item.status == "error") return acc + 1;
              return acc;
            },
            0
          );
          if (errorCount == 0) {
            handleOnCancel();
          }
          setDataReturn({ data, successCount, errorCount });
          onSuccess?.();
          setDataPosts([]);
        }
      } catch (err) {
        console.log({ err });
      } finally {
        setLoading(false);
      }
    };

    const handleOnCancel = () => {
      setVisible(false);
      form.resetFields();
      setSelectedParentId(undefined);
      onClose?.();
    };

    useImperativeHandle(
      ref,
      () => ({
        open: () => setVisible(true),
        close: () => setVisible(false),
      }),
      []
    );

    // Define preview columns for the table
    const previewColumns = [
      {
        key: "rowNum",
        title: "Dòng excel",
        dataIndex: "rowNum",
        width: 100,
        render: (text: number) => <span>{text}</span>,
      },
      {
        key: "name",
        title: "Tên *",
        dataIndex: "name",
        render: (text: string, record: any) => (
          <span className={!text ? "text-red-500" : ""}>{text || "Thiếu"}</span>
        ),
      },
      {
        key: "errorMessage",
        title: "Lỗi",
        dataIndex: "errorMessage",
        width: 300,
        render: (text: string) => (
          <span className="text-red-500 whitespace-pre-line text-xs">
            {text}
          </span>
        ),
      },
    ];

    // Define required fields based on dictionary type
    const requiredFields: (keyof DictionaryImport)[] = [
      "name", // Tên là bắt buộc
    ];

    const handleValidateData = (
      data: DictionaryImport[]
    ): DictionaryImport[] => {
      console.log("🔍 handleValidateData called with:", data);
      return data.map((item) => {
        const additionalErrors: string[] = [];

        // Name validation (required)
        if (!item.name || item.name.trim() === "") {
          additionalErrors.push("Tên không được để trống");
        }

        // Additional custom validations can be added here
        // For example, checking if parentName exists for tree types

        console.log(
          "🔍 Additional validation for item:",
          item,
          "additional errors:",
          additionalErrors
        );

        // Return item with additional errors
        return {
          ...item,
          errorMessage:
            additionalErrors.length > 0
              ? additionalErrors.join("; ")
              : undefined,
        };
      });
    };

    const handleParentChange = (value: number | undefined) => {
      setSelectedParentId(value);
      form.setFieldValue("parentId", value);
    };

    return (
      <Modal
        maskClosable={false}
        width={1000}
        style={{ top: 50 }}
        open={visible}
        onCancel={handleOnCancel}
        destroyOnClose={true}
        afterClose={() => {
          setDataPosts([]);
          setErrorsLog([]);
          setDataReturn(undefined);
          setSelectedParentId(undefined);
          form.resetFields();
        }}
        title={titleText}
        footer={[
          <CustomButton
            key="import"
            loading={loading}
            variant="primary"
            disabled={!dataPosts.length || hasValidationErrors}
            onClick={() => {
              handleOnImport();
            }}
          >
            {okText}
          </CustomButton>,
          <CustomButton
            key="close"
            variant="outline"
            className="cta-button"
            onClick={() => {
              handleOnCancel();
            }}
          >
            Đóng
          </CustomButton>,
        ]}
      >
        <Spin spinning={false}>
          {guide && (
            <Alert
              style={{ padding: "10px", marginBottom: "10px" }}
              message={<b>Lưu ý</b>}
              type="warning"
              description={
                <ul>
                  {guide.map((text, index) => (
                    <li key={index}>
                      <p>{text}</p>
                    </li>
                  ))}
                </ul>
              }
            />
          )}

          {/* Parent Selection Form - Only show for tree types */}
          {isTreeType && (
            <Form
              form={form}
              layout="vertical"
              style={{ marginBottom: "16px" }}
            >
              <Row>
                <Col span={12}>
                  <Form.Item label="Lựa chọn cấp cha" name="parentId">
                    <DictionarySelector
                      initQuery={{ type: dictionaryType }}
                      placeholder="Lựa chọn cấp cha"
                      value={selectedParentId}
                      onChange={handleParentChange}
                      allowClear
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          )}

          {demoExcel && (
            <Link to={demoExcel} target="_blank" download>
              <Space className={`flex gap-2 cursor-pointer`}>
                <DownloadOutlined />
                Tải file import mẫu{" "}
              </Space>
            </Link>
          )}
          {onDownloadDemoExcel && (
            <a>
              <Space
                className={`flex gap-2 cursor-pointer`}
                onClick={() => {
                  onDownloadDemoExcel();
                }}
              >
                <DownloadOutlined />
                Tải file import mẫu
              </Space>
            </a>
          )}

          <Dragger
            style={{ marginTop: "0.5em" }}
            maxCount={1}
            multiple={false}
            beforeUpload={async (file) => {
              //Check file type
              const isCSVFile = file.name.includes("xlsx");
              if (isCSVFile === false) {
                message.error("Bạn chỉ có thể upload file excel!");
                return Upload.LIST_IGNORE;
              }
              const excelData = await readerData(file, 0);
              setDataReturn(undefined);
              console.log("Data khi import vào là", excelData);
              onUploaded?.(excelData, setDataPosts);
              return false;
            }}
            onChange={(info) => {
              //reset data
              if (info.fileList.length == 0) {
                setErrorsLog([]);
                setDataPosts([]);
              }
            }}
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">{uploadText}</p>
          </Dragger>

          {/* Import Preview Module */}
          <ImportPreviewModule
            data={dataPosts}
            dataReturn={dataReturn}
            onValidateData={handleValidateData}
            duplicateCheckFields={["name"]} // Check for duplicate names
            requiredFields={requiredFields}
            columns={previewColumns}
            title="Xem danh sách cấu hình"
            previewButtonText="Xem danh sách cấu hình"
            onValidationStatusChange={handleValidationStatusChange}
          />
        </Spin>
        <Space
          style={{ width: "100%", justifyContent: "end", marginTop: "1em" }}
        ></Space>
      </Modal>
    );
  }
);

export default ImportDictionary;
