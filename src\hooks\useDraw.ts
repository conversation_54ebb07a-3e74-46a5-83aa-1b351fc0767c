import { drawApi } from "api/draw.api";
import { useMemo, useState, useCallback } from "react";
import { Draw } from "types/draw";
import { QueryParam } from "types/query";

export interface DrawQuery extends QueryParam {}

interface UseDrawProps {
  initQuery: DrawQuery;
}

export const useDraw = ({ initQuery }: UseDrawProps) => {
  const [data, setData] = useState<Draw[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<DrawQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const isEmptyQuery = useMemo(
    () =>
      Object.keys(query).filter(
        (k) =>
          query[k] && !["limit", "page", "queryObject", "projectId"].includes(k)
      ).length == 0,
    [query]
  );

  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const { data } = await drawApi.findAll(query);

      setData(data.draws);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  }, [query]);

  return {
    draws: data,
    totalDraw: total,
    fetchDraw: fetchData,
    loadingDraw: loading,
    setQueryDraw: setQuery,
    queryDraw: query,
    isEmptyQueryDraw: isEmptyQuery,
  };
};
