import {
  LockOutlined,
  UnlockOutlined,
  ImportOutlined,
} from "@ant-design/icons";
import {
  Button,
  Card,
  message,
  Modal,
  Select,
  Space,
  Table,
  Tag,
  Tooltip,
} from "antd";
import { Pagination } from "components/Pagination";
import React, { useEffect, useState, useRef, useMemo } from "react";
import { ProjectGroup } from "types/projectGroup";
import { getTitle } from "utils";
import { useProjectGroup } from "hooks/useProjectGroup";
import { useNavigate } from "react-router-dom";
import { projectGroupApi } from "api/projectGroup.api";
import { useProjectCategory } from "hooks/useProjectCategory";
import {
  ProjectGroupModal,
  ProjectGroupModalRef,
} from "./components/Modal/ProjectGroupModal";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import CustomButton from "components/Button/CustomButton";
import CustomInput from "components/Input/CustomInput";
import PencilIcon from "assets/svgs/PencilIcon";
import PageTitle from "components/PageTitle/PageTitle";
import { handleExport, MyExcelColumn } from "utils/MyExcel";
import QueryLabel from "components/QueryLabel/QueryLabel";
import { removeSubstringFromKeys } from "utils/common";
import { exportTemplateWithValidation } from "utils/TemplateExcel";
import ImportProjectGroup, {
  ImportProjectGroupModal,
} from "components/ImportDocument/ImportProjectGroup";
import { TableProps } from "antd/lib";
import { observer } from "mobx-react";
import { checkRoles, filterActionColumnIfNoPermission } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { PermissionNames } from "types/PermissionNames";
import LockButton from "components/Button/LockButton";
import EditButton from "components/Button/EditButton";

const { ColumnGroup, Column } = Table;

export const ProjectGroupPage = observer(({ title = "" }) => {
  const {
    haveAddPermission,
    haveBlockPermission,
    haveEditPermission,
    haveViewAllPermission,
  } = checkRoles(
    {
      add: PermissionNames.projectGroupAdd,
      edit: PermissionNames.projectGroupEdit,
      block: PermissionNames.projectGroupBlock,
      viewAll: PermissionNames.projectGroupViewAll,
    },
    permissionStore.permissions
  );

  const modalRef = useRef<ProjectGroupModalRef>();
  const importModal = useRef<ImportProjectGroupModal>();
  const exportColumns: MyExcelColumn<ProjectGroup>[] = [
    {
      header: "Mã nhóm dự án",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "code",
      columnKey: "code",
    },
    {
      header: "Tên nhóm dự án",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "name",
      columnKey: "name",
    },

    {
      header: "Trạng thái",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "isActive",
      columnKey: "isActive",
      render: (record: ProjectGroup) =>
        record.isActive ? "Hoạt động" : "Bị khóa",
    },
  ];
  const [loadingDelete, setLoadingDelete] = useState(false);
  const [loadingDownloadDemo, setLoadingDownloadDemo] = useState(false);
  // const [isSelectStatus , ]
  const {
    projectGroups,
    fetchData,
    loading,
    query,
    setQuery,
    total,
    isEmptyQuery,
  } = useProjectGroup({
    initQuery: {
      limit: 10,
      page: 1,
      isAdmin: haveViewAllPermission ? true : undefined,
    },
  });
  const { fetchData: fetchProjectCate, projectCategories } = useProjectCategory(
    { initQuery: { limit: 100, page: 1 } }
  );
  const navigate = useNavigate();
  useEffect(() => {
    document.title = getTitle(title);
    fetchData();
    fetchProjectCate();
  }, []);

  const handleDeleteProjectGroup = async (id: number) => {
    try {
      setLoadingDelete(false);
      await projectGroupApi.delete(id);
      message.success("Xóa thành công");
      fetchData();
    } catch (error) {
    } finally {
      setLoadingDelete(true);
    }
  };
  const handleActiveProjectGroup = async (id: number, value: boolean) => {
    try {
      setLoadingDelete(false);
      await projectGroupApi.update(id, { projectGroup: { isActive: !value } });
      message.success(value ? "Khóa thành công" : "Mở khóa thành công");
      fetchData();
    } catch (error) {
    } finally {
      setLoadingDelete(true);
    }
  };

  const typeStatus = [
    {
      value: "",
      label: "Tất cả trạng thái",
    },
    {
      value: true,
      label: "Hoạt động",
    },
    {
      value: false,
      label: "Bị khóa",
    },
  ];
  const handleTableChange: TableProps<any>["onChange"] = (
    pagination,
    filters,
    sorter
  ) => {
    if (!Array.isArray(sorter)) {
      const fieldMap: Record<string, string> = {
        name: "projectGroup.name",

        code: "projectGroup.code",
      };
      const columnKey = sorter.field || sorter.column?.key;

      if (!sorter.order) {
        // setSortField(null);
        // setSortOrder(null);
        query.queryObject = undefined;
        setQuery({ ...query });
      } else {
        const order = sorter.order === "ascend" ? "ASC" : "DESC";
        // setSortField("jobCategory.name");
        // setSortOrder(order);
        const field = fieldMap[columnKey as string];

        const newQueryObject = JSON.stringify([
          {
            type: "sort",
            field,
            value: order,
          },
        ]);
        query.queryObject = newQueryObject;
        setQuery({ ...query });
      }
      fetchData();
    } else {
      query.queryObject = undefined;
      setQuery({ ...query });
      fetchData();
    }
  };

  const handleRowClick = (record: ProjectGroup) => {
    modalRef?.current?.handleUpdate(record);
  };

  const columns: CustomizableColumn<ProjectGroup>[] = [
    {
      key: "code",

      title: "Mã",
      dataIndex: "code",
      render: (_, record) => (
        <div
          className="text-[#1677ff] cursor-pointer"
          onClick={(e) => {
            e.stopPropagation();
            handleRowClick(record);
          }}
        >
          {record?.code || "-"}
        </div>
      ),
      width: 150,
      defaultVisible: true,
      sorter: true,

      alwaysVisible: true,
    },
    {
      key: "name",
      title: "Tên nhóm dự án",
      dataIndex: "name",
      width: 700,

      // render: (_, record) => (
      // 	<div className="service-cell">

      // 		<div className="service-info">
      // 			<div className="service-name">{record.name}</div>
      // 		</div>
      // 	</div>
      // ),
      defaultVisible: true,
      sorter: true,
    },

    {
      key: "status",
      title: "Trạng thái",
      align: "center",
      width: 150,
      render: (_, record) => (
        <div className="flex justify-center">
          {record.isActive ? (
            <Tag color="green" className="status-tag m-0">
              Hoạt động
            </Tag>
          ) : (
            <Tag color="red" className="status-tag m-0">
              Bị khóa
            </Tag>
          )}
        </div>
      ),
      defaultVisible: true,
    },
    {
      key: "actions",
      title: "Xử lý",
      width: 100,
      align: "center",
      fixed: "right",
      render: (_, record) => (
        <Space size="small">
          {haveEditPermission && (
            <EditButton
              onClick={(e) => {
                e.stopPropagation();
                modalRef?.current?.handleUpdate(record);
              }}
            />
          )}
          {haveBlockPermission && (
            <LockButton
              isActive={record.isActive}
              onAccept={() =>
                handleActiveProjectGroup(record.id, record.isActive)
              }
              modalTitle={`${record.isActive ? "Khóa" : "Mở khóa"} nhóm dự án ${
                record.name
              }`}
              modalContent={
                <>
                  <div>
                    Khi {record.isActive ? "khóa" : "mở khóa"} nhóm dự án các
                    thông tin của nhóm dự án này cũng sẽ được{" "}
                    {record.isActive ? "khóa" : "mở khóa"}.
                  </div>
                  <div>
                    Bạn có chắc chắn muốn {record.isActive ? "khóa" : "mở khóa"}{" "}
                    nhóm dự án này?
                  </div>
                </>
              }
            />
          )}
        </Space>
      ),
      defaultVisible: true,
      alwaysVisible: true,
    },
  ];

  const pagination = {
    current: 1,
    pageSize: 10,
    total: projectGroups.length,
    showSizeChanger: true,
  };

  const handleOnUploadedFile = async (excelData: any, setData: any) => {
    const { results } = excelData;

    console.log("results", results);

    const importData = results?.map((item: any) => {
      const refineRow = removeSubstringFromKeys(item, " *");

      const code = refineRow["Mã nhóm dự án"];
      const name = refineRow["Tên nhóm dự án"];
      const statusName = refineRow["Trạng thái"];
      let isActive = undefined;
      if (statusName) {
        isActive = statusName === "Hoạt động" ? true : false;
      }

      return {
        code,
        name,
        isActive,
        rowNum: item.__rowNum__,
      };
    });
    console.log("importData", importData);

    setData(importData);
  };

  const handleDownloadDemoExcel = async () => {
    try {
      setLoadingDownloadDemo(true);

      const result = await exportTemplateWithValidation({
        templatePath: "/exportFile/file_mau_nhap_nhom_du_an.xlsx",
        outputFileName: "file_mau_nhap_nhom_du_an.xlsx",
        sheetsToAdd: [{ name: "Status", data: ["Hoạt động", "Bị khóa"] }],
        validations: [
          {
            column: "C",
            type: "list",
            formulae: [`'Status'!$A$1:$A$2`],
          },
        ],
      });

      // Xử lý kết quả trả về
      if (result.success) {
        message.success(result.message);
      } else {
        message.error(result.message);
      }
    } catch (error) {
      console.error("Download demo error:", error);
      message.error(
        `Có lỗi xảy ra: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    } finally {
      setLoadingDownloadDemo(false);
    }
  };

  return (
    <div className="app-container">
      <PageTitle
        title={title}
        breadcrumbs={["Dữ liệu nguồn", title]}
        extra={
          haveAddPermission && (
            <Space>
              <CustomButton
                size="small"
                showPlusIcon
                onClick={() => {
                  modalRef.current?.handleCreate();
                }}
              >
                Tạo nhóm dự án
              </CustomButton>
              <CustomButton
                size="small"
                icon={<ImportOutlined />}
                onClick={() => {
                  importModal.current?.open();
                }}
              >
                Nhập excel
              </CustomButton>
            </Space>
          )
        }
      />

      <Card>
        <div className="flex gap-[16px] items-end pb-[16px] justify-between">
          <div className="flex gap-[16px] items-end flex-wrap">
            <div className="w-[300px]">
              <CustomInput
                tooltipContent={"Tìm theo mã, tên nhóm dự án"}
                label="Tìm kiếm"
                placeholder="Tìm kiếm"
                onPressEnter={() => {
                  query.page = 1;
                  setQuery({ ...query });
                  fetchData();
                }}
                value={query.search}
                onChange={(value) => {
                  query.search = value;
                  setQuery({ ...query });

                  if (!value) {
                    fetchData();
                  }
                }}
                allowClear
              />
            </div>
            <div className="w-[200px]">
              <QueryLabel>Trạng thái</QueryLabel>
              <Select
                placeholder="Chọn trạng thái"
                value={query.isActive}
                onChange={(value) => {
                  query.isActive = value;
                  setQuery({ ...query });
                }}
                allowClear
                options={[
                  {
                    value: true,
                    label: "Hoạt động",
                  },
                  {
                    value: false,
                    label: "Bị khóa",
                  },
                ]}
              />
            </div>

            <CustomButton
              onClick={() => {
                query.page = 1;
                setQuery({ ...query });
                fetchData();
              }}
            >
              Áp dụng
            </CustomButton>

            {(query.search || query.isActive !== undefined) && (
              <CustomButton
                variant="outline"
                onClick={() => {
                  delete query.search;
                  delete query.isActive;
                  query.page = 1;
                  setQuery({ ...query });
                  fetchData();
                }}
              >
                Bỏ lọc
              </CustomButton>
            )}
          </div>

          <CustomButton
            onClick={() => {
              Modal.confirm({
                title: `Bạn có muốn xuất file excel?`,
                getContainer: () => {
                  return document.getElementById("App") as HTMLElement;
                },
                icon: null,

                footer: (_, { OkBtn, CancelBtn }) => (
                  <>
                    <CustomButton
                      variant="outline"
                      className="cta-button"
                      onClick={() => {
                        handleExport({
                          onProgress(percent) {
                            console.log("What is percent", percent);
                          },
                          exportColumns,
                          fileType: "xlsx",
                          dataField: "projectGroups",
                          query: query,
                          api: projectGroupApi.findAll,
                          fileName: "Danh sách nhóm dự án",
                          sheetName: "Danh sách nhóm dự án",
                        });
                        Modal.destroyAll();
                      }}
                    >
                      Có
                    </CustomButton>
                    <CustomButton
                      onClick={() => {
                        Modal.destroyAll();
                      }}
                      className="cta-button"
                    >
                      Không
                    </CustomButton>

                    {/* <OkBtn />
                      <CancelBtn /> */}
                  </>
                ),
              });
            }}
          >
            Xuất excel
          </CustomButton>
        </div>
        <CustomizableTable
          columns={filterActionColumnIfNoPermission(columns, [
            haveEditPermission,
            haveBlockPermission,
          ])}
          dataSource={projectGroups}
          rowKey="id"
          loading={loading}
          pagination={false}
          scroll={{ x: 1200 }}
          bordered
          displayOptions
          onRowClick={handleRowClick}
          //@ts-ignore
          onChange={handleTableChange}
        />

        <Pagination
          currentPage={query.page}
          defaultPageSize={query.limit}
          total={total}
          onChange={({ limit, page }) => {
            query.page = page;
            query.limit = limit;
            setQuery({ ...query });
            fetchData();
          }}
        />
      </Card>
      <ProjectGroupModal
        ref={modalRef}
        onClose={() => {}}
        onSubmitOk={() => {
          fetchData();
        }}
      />

      {useMemo(
        () => (
          <ImportProjectGroup
            guide={[
              "Vui lòng tải và sử dụng file mẫu để import dữ liệu đúng cách",
              "Không được thay đổi tiêu đề trong file csv/excel mẫu để tránh import thiếu dữ liệu",
              "Chi tiết xem tại sheet Hướng dẫn của file import mẫu",
            ]}
            onSuccess={() => {
              query.page = 1;
              fetchData();
            }}
            ref={importModal}
            createApi={projectGroupApi.import}
            onUploaded={(excelData, setData) => {
              console.log("up gì lên vậy", excelData);
              handleOnUploadedFile(excelData, setData);
            }}
            okText={`Nhập nhóm dự án ngay`}
            onDownloadDemoExcel={handleDownloadDemoExcel}
            loadingDownloadDemo={loadingDownloadDemo}
            titleText="Nhập excel dữ liệu"
          />
        ),
        [loadingDownloadDemo]
      )}
    </div>
  );
});
