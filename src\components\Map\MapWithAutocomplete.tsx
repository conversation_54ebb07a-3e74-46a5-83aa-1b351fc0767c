import { Libraries, StandaloneSearchBox } from "@react-google-maps/api";
import { $googleApiKey } from "constant";
import GoogleMapReact from "google-map-react";
import { cloneDeep, debounce } from "lodash";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { Marker } from "./components/Marker";
import { CoordAddress } from "types/address";
import { Select } from "antd";
import { googleMapsApi } from "api/googleMaps.api";
import { addBoldToStringAtIndex } from "utils/data";
import PinIMG from "assets/images/pin.png";

interface MapWithAutocompleteProps {
  defaultCenter?: {
    lat: number;
    lng: number;
  };
  defaultZoom?: number;
  coords?: GoogleMapReact.Coords[];
  onPlaceSelected?: (coordAddress: CoordAddress) => void;
  address?: string;
  noInput?: boolean;
  draggable?: boolean;
  draggableMarker?: boolean; // Thêm prop để bật/tắt kéo thả marker
}

interface MarkerProps {
  lat: number;
  lng: number;
}

interface PlaceOptionMatchedSubString {
  length: number;
  offset: number;
}

interface PlaceOption {
  description: string;
  matched_substrings: PlaceOptionMatchedSubString[];
  place_id: string;
  reference: string;
  structured_formatting: {
    main_text: string;
    main_text_matched_substrings: PlaceOptionMatchedSubString[];
    secondary_text: string;
  };
  terms: { offset: number; value: string }[];
  types: string[];
}

// //@ts-ignore
// if (typeof HTMLDialogElement === "undefined") {
//   //@ts-ignore
//   window.HTMLDialogElement = class {}; // Fake class to prevent errors
// }

const libraries: Libraries = ["places"];
export const DEFAULT_LAT = 10.7964726;
export const DEFAULT_LNG = 106.6486002;

const MapWithAutocomplete: React.FC<MapWithAutocompleteProps> = ({
  defaultCenter = { lat: DEFAULT_LAT, lng: DEFAULT_LNG },
  defaultZoom = 12,
  onPlaceSelected,
  coords,
  address,
  noInput = false,
  draggable = true,
  draggableMarker = true, // Mặc định bật kéo thả marker
}) => {
  //   const { t } = useTranslation();
  const [center, setCenter] = useState(defaultCenter);
  const [zoom, setZoom] = useState(defaultZoom);
  // const [marker, setMarker] = useState<MarkerProps>({
  //   lat: DEFAULT_LAT,
  //   lng: DEFAULT_LNG,
  // });
  const [marker, setMarker] = useState<MarkerProps>();
  const [searchBox, setSearchBox] =
    useState<google.maps.places.SearchBox | null>(null);
  const [placeOptions, setPlaceOptions] = useState<PlaceOption[]>([]);
  const [currentPlaceId, setCurrentPlaceId] = useState("");
  const [currentAddress, setCurrentAddress] = useState("");
  const [isInitialized, setIsInitialized] = useState(false);
  const [googleMap, setGoogleMap] = useState<google.maps.Map | null>(null);
  const [googleMarker, setGoogleMarker] = useState<google.maps.Marker | null>(null);
  const [markerCreated, setMarkerCreated] = useState(false);

  const inputRef = useRef<HTMLInputElement>(null);
  const mapRef = useRef<GoogleMapReact>(null);

  // Hàm tạo marker có thể kéo thả
  const createDraggableMarker = useCallback((lat: number, lng: number, isInitialCreate = false) => {
    if (!googleMap) return;

    // Nếu đã có marker và không phải tạo mới, chỉ cập nhật vị trí
    if (googleMarker && !isInitialCreate) {
      googleMarker.setPosition({ lat, lng });
      return;
    }

    // Xóa marker cũ nếu có
    if (googleMarker) {
      googleMarker.setMap(null);
    }

    // Tạo marker mới
    const newMarker = new google.maps.Marker({
      position: { lat, lng },
      map: googleMap,
      draggable: draggableMarker,
      icon: {
        url: PinIMG, // Sử dụng import thay vì đường dẫn tuyệt đối
        scaledSize: new google.maps.Size(40, 40),
        anchor: new google.maps.Point(20, 40), // Điểm neo ở giữa dưới của icon
      },
    });

    // Thêm event listener cho dragend
    if (draggableMarker) {
      newMarker.addListener('dragend', async () => {
        const position = newMarker.getPosition();
        if (position) {
          const lat = position.lat();
          const lng = position.lng();
          
          // Chỉ cập nhật state, không gọi createDraggableMarker để tránh nháy
          setMarker({ lat, lng });
          setCenter({ lat, lng });

          // Lấy địa chỉ từ tọa độ mới
          const { result } = await getGeocode(lat, lng);
          if (result) {
            const detailedAddress = createDetailedAddress(result);
            setCurrentAddress(detailedAddress);
            if (result.place_id) {
              setCurrentPlaceId(result.place_id);
            }
            
            // Gọi callback
            onPlaceSelected?.({
              address: detailedAddress,
              lat: lat,
              lng: lng,
              placeId: result.place_id || "",
              name: result.name || "",
            });
          }
        }
      });
    }

    setGoogleMarker(newMarker);
    setMarkerCreated(true);
  }, [googleMap, draggableMarker, onPlaceSelected, googleMarker]);

  // Cập nhật marker khi marker state thay đổi (chỉ khi đã tạo marker)
  useEffect(() => {
    if (marker && googleMap && markerCreated && googleMarker) {
      // Chỉ cập nhật vị trí, không tạo marker mới
      googleMarker.setPosition({ lat: marker.lat, lng: marker.lng });
    }
  }, [marker, googleMap, markerCreated, googleMarker]);

  // Tạo marker ban đầu khi map được load
  useEffect(() => {
    if (googleMap && marker && !markerCreated) {
      createDraggableMarker(marker.lat, marker.lng, true);
    }
  }, [googleMap, marker, markerCreated, createDraggableMarker]);



  // Cleanup marker khi component unmount
  useEffect(() => {
    return () => {
      if (googleMarker) {
        googleMarker.setMap(null);
      }
    };
  }, [googleMarker]);

  const handleSearchBoxLoad = (ref: google.maps.places.SearchBox | null) => {
    setSearchBox(ref);
  };

  const handlePlacesChanged = async ({ _place }: { _place?: any }) => {
    if (_place && _place.result) {
      const place = _place.result;
      const location = place?.geometry?.location;
      if (location) {
        console.log("zo location nè", { location });
        setMarker({ lat: location.lat, lng: location.lng });
        setCenter({ lat: location.lat, lng: location.lng });
        
        // Tạo địa chỉ chi tiết bao gồm thành phố
        const detailedAddress = createDetailedAddress(place);
        
        // Cập nhật địa chỉ hiện tại
        setCurrentAddress(detailedAddress);
        
        onPlaceSelected?.({
          address: detailedAddress,
          lat: location.lat,
          lng: location.lng,
          placeId: place.place_id,
        });
      }
    }
  };

  const handleMapClick = async ({ lat, lng }: { lat: number; lng: number }) => {
    // Cập nhật marker và center ngay lập tức để tránh nhấp nháy
    setMarker({ lat, lng });
    setCenter({ lat, lng });

    // 2. Reverse geocoding - lấy địa chỉ từ tọa độ
    const { address, result } = await getGeocode(lat, lng);

    if (result) {
      // 3. Parse thông tin chi tiết từ address_components
      const placeDetail = getPlaceDetailFromMap(
        result.address_components,
        result.formatted_address
      );
      
      // 4. Cập nhật currentPlaceId để đồng bộ với Select
      if (result.place_id) {
        setCurrentPlaceId(result.place_id);
      }
      
      // 5. Tạo địa chỉ chi tiết từ geocoding result
      const detailedAddress = createDetailedAddress(result);
      
      // 6. Cập nhật địa chỉ hiện tại
      setCurrentAddress(detailedAddress);
      
      // 7. Gọi callback với thông tin địa điểm (giống như search)
      onPlaceSelected?.({
        address: detailedAddress,
        lat: lat,
        lng: lng,
        placeId: result.place_id || "",
        name: result.name || "",
      });
      
    }
  };

  const getPlaceDetailFromMap = (
    address_components: any[],
    formatted_address: string
  ) => {
    const tmpCity = getCityFromGeocode(address_components);
    const tmpDistrict = getDistrictFromGeocode(address_components);
    const tmpWard = getWardFromFullAddress(
      formatted_address,
      address_components
    );
    const tmpStreet = getStreetFromGeocode(address_components);
    const tmpCountry = getCountryFromGeocode(address_components);
    const tmpPostalCode = getPostalCodeFromGeocode(address_components);
    return {
      tmpCountry,
      tmpCity,
      tmpDistrict,
      tmpWard,
      tmpStreet,
      tmpPostalCode,
    };
  };

  const getPostalCodeFromGeocode = (array: any[]): string => {
    const _array = cloneDeep(array);
    const result = _array
      .reverse()
      .filter((o: { types: string | string[] }) => {
        return o.types.includes("postal_code");
      });

    if (result.length > 0) {
      return result[0].short_name;
    }

    return "";
  };
  const getCountryFromGeocode = (array: any[]): string => {
    const _array = cloneDeep(array);
    const result = _array
      .reverse()
      .filter((o: { types: string | string[] }) => {
        return o.types.includes("country");
      });

    if (result.length > 0) {
      return result[0].short_name;
    }

    return "";
  };
  const getCityFromGeocode = (array: any[]): string => {
    const _array = cloneDeep(array);
    const result = _array
      .reverse()
      .filter((o: { types: string | string[] }) => {
        return o.types.includes("administrative_area_level_1");
      });

    if (result.length > 0) {
      return result[0].long_name;
    }

    return "";
  };

  const getDistrictFromGeocode = (array: any[]): string => {
    const _array = cloneDeep(array);
    const result = _array
      .reverse()
      .filter((o: { types: string | string[] }) => {
        return (
          o.types.includes("administrative_area_level_2") ||
          o.types.includes("locality")
        );
      });

    if (result.length > 0) {
      return result[0].long_name;
    }

    return "";
  };
  const getWardFromFullAddress = (address: string, array: any[]) => {
    const _array = cloneDeep(array);

    const result = _array
      .reverse()
      .filter((o: { types: string | string[] }) => {
        return o.types.includes("administrative_area_level_3");
      });
    const split = address.split(", ");
    let ward = "";

    const regex = /(phường|xã|thị trấn).+/gm;
    const regexName = /(phường|xã|thị trấn)/gm;
    for (const item of split) {
      if (regex.test(item.toLowerCase())) {
        ward = item.trim();
      }
    }
    if (!ward) {
      if (result.length > 0) {
        ward = result[0].long_name;
      } else {
        ward = split[1] || "";
      }
    }

    return ward;
  };
  const getStreetFromGeocode = (array: any[]): string => {
    let tmpStreet = "";
    const route = array.filter((o: { types: string | string[] }) => {
      return o.types.includes("route");
    });
    const streetNum = array.filter((o: { types: string | string[] }) => {
      return o.types.includes("street_number");
    });

    if (streetNum.length > 0) {
      tmpStreet += streetNum[0].long_name;
    }

    if (route.length > 0) {
      tmpStreet += " " + route[0].long_name;
    }

    return tmpStreet;
  };

  // Tạo địa chỉ chi tiết từ place object
  const createDetailedAddress = useCallback((place: any): string => {
    const addressComponents = place.address_components || [];
    
    // Lấy các thành phần địa chỉ
    const streetNumber = addressComponents.find((comp: any) => 
      comp.types.includes("street_number")
    )?.long_name || "";
    
    const route = addressComponents.find((comp: any) => 
      comp.types.includes("route")
    )?.long_name || "";
    
    const ward = addressComponents.find((comp: any) => 
      comp.types.includes("administrative_area_level_3")
    )?.long_name || "";
    
    const district = addressComponents.find((comp: any) => 
      comp.types.includes("administrative_area_level_2") || 
      comp.types.includes("locality")
    )?.long_name || "";
    
    const city = addressComponents.find((comp: any) => 
      comp.types.includes("administrative_area_level_1")
    )?.long_name || "";
    
    const country = addressComponents.find((comp: any) => 
      comp.types.includes("country")
    )?.long_name || "";
    
    // Tạo địa chỉ chi tiết
    const parts = [];
    
    // Số nhà + đường
    if (streetNumber && route) {
      parts.push(`${streetNumber} ${route}`);
    } else if (route) {
      parts.push(route);
    }
    
    // Phường/Xã
    if (ward) {
      parts.push(ward);
    }
    
    // Quận/Huyện
    if (district) {
      parts.push(district);
    }
    
    // Thành phố/Tỉnh
    if (city) {
      parts.push(city);
    }
    
    // Quốc gia
    if (country) {
      parts.push(country);
    }
    
    // Nếu không có địa chỉ chi tiết, sử dụng formatted_address
    if (parts.length === 0) {
      return place.formatted_address || place.name || "";
    }
    
    return parts.join(", ");
  }, []);

  const getGeocode = useCallback(async (lat: number, lng: number) => {
    const response = await fetch(
      `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${$googleApiKey}`
    );
    const data = await response.json();
    if (data.status === "OK") {
      return {
        address: data.results[0]?.formatted_address || "",
        result: data.results[0],
      };
    } else {
      return { address: "", result: undefined };
    }
  }, []);

  // Khởi tạo ban đầu chỉ chạy 1 lần khi component mount
  useEffect(() => {
    if (coords && coords.length > 0 && !isInitialized) {
      const item = coords[0];
      
      // Tạo địa chỉ ban đầu từ coords
      getGeocode(item.lat, item.lng).then(({ result }) => {
        if (result) {
          const detailedAddress = createDetailedAddress(result);
          setCurrentAddress(detailedAddress);
          if (result.place_id) {
            setCurrentPlaceId(result.place_id);
          }
        }
      });
      
      setIsInitialized(true);
    }
  }, [coords, isInitialized, createDetailedAddress, getGeocode]); // Chạy khi coords thay đổi và chưa khởi tạo

  useEffect(() => {
    setZoom(defaultZoom);
  }, []);

  // Handle coords prop changes (chỉ khi đã khởi tạo)
  useEffect(() => {
    if (coords && coords.length > 0 && isInitialized) {
      const item = coords[0];
      // Chỉ cập nhật nếu khác với vị trí hiện tại
      if (marker?.lat !== item.lat || marker?.lng !== item.lng) {
        setMarker({ lat: item.lat, lng: item.lng });
        setCenter({ lat: item.lat, lng: item.lng });
      }
    }
  }, [coords, isInitialized, marker]);

  // Xử lý coords ban đầu khi map đã sẵn sàng
  useEffect(() => {
    if (googleMap && coords && coords.length > 0 && isInitialized && !markerCreated) {
      const item = coords[0];
      setMarker({ lat: item.lat, lng: item.lng });
      setCenter({ lat: item.lat, lng: item.lng });
      
      // Tạo địa chỉ ban đầu từ coords nếu chưa có
      if (!currentAddress) {
        getGeocode(item.lat, item.lng).then(({ result }) => {
          if (result) {
            const detailedAddress = createDetailedAddress(result);
            setCurrentAddress(detailedAddress);
            if (result.place_id) {
              setCurrentPlaceId(result.place_id);
            }
          }
        });
      }
    }
  }, [googleMap, coords, isInitialized, markerCreated, currentAddress, getGeocode, createDetailedAddress]);

  // useEffect(() => {
  //   console.log({ marker });
  // }, [marker]);

  const debounceSearch = useCallback(
    debounce(async (search) => {
      if (search) {
        const { data } = await googleMapsApi.searchPlace({
          search,
          lat: center.lat,
          long: center.lng,
        });
        setPlaceOptions(data.predictions || []);
      }
    }, 300),
    [center]
  );

  return (
    <>
      {!noInput && (
        // <StandaloneSearchBox
        //   onLoad={handleSearchBoxLoad}
        //   onPlacesChanged={handlePlacesChanged}
        // >
        //   <input
        //     ref={inputRef}
        //     type="text"
        //     placeholder={t("search")}
        //     style={{
        //       boxSizing: "border-box",
        //       border: "1px solid #ccc",
        //       width: "100%",
        //       height: "40px",
        //       padding: "0 12px",
        //       borderRadius: "8px",
        //       fontSize: "14px",
        //       outline: "none",
        //       marginBottom: 10,
        //     }}
        //   />
        // </StandaloneSearchBox>
        <div className="mb-2">
          <Select
            value={currentPlaceId}
            className="w-full"
            showSearch
            placeholder="Tìm kiếm địa điểm..."
            onSearch={debounceSearch}
            filterOption={false}
            onChange={async (value, option) => {
              const { data } = await googleMapsApi.getPlaceDetail({
                placeId: value,
              });
              setCurrentPlaceId(value);
              handlePlacesChanged({ _place: data });
            }}
            options={[
              // Option cho địa chỉ hiện tại (nếu có)
              ...(currentAddress && currentPlaceId ? [{
                label: currentAddress,
                value: currentPlaceId,
                key: 'current',
              }] : []),
              // Options từ search
              ...placeOptions.map((p, i) => ({
                label: addBoldToStringAtIndex({
                  str: p.description,
                  matchs: p.matched_substrings,
                }),
                value: p.place_id,
                place: p,
                key: i,
              }))
            ]}
          />
        </div>
      )}
      <div style={{ height: "400px", width: "100%" }}>
        <GoogleMapReact
          draggable={draggable}
          center={center}
          zoom={zoom}
          bootstrapURLKeys={{
            key: $googleApiKey || "",
          }}
          // onClick={handleMapClick}
          onGoogleApiLoaded={({ map, maps }) => {
            setGoogleMap(map);
          }}
        >
          {/* Không cần render Marker component nữa vì đã sử dụng Google Maps Marker API */}
        </GoogleMapReact>
      </div>
    </>
  );
};

export default MapWithAutocomplete;
