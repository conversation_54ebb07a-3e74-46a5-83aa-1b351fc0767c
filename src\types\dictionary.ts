import { File } from "./../../node_modules/react-pdf/dist/shared/types.d";
import { Material } from "./material";
import { MaterialGroupDetail } from "./materialGroup";

export interface Dictionary {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  name: string;
  isActive: boolean;
  type: DictionaryType;
  children: Dictionary[];
  code: string
}

export interface DictionaryDetail {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  position: number;
  parentDictionary: Dictionary;
  childrenDictionary: Dictionary;
}

export enum DictionaryType {
  ServiceType = "SERVICE",
  Document = "DOCUMENT",
  JobTitle = "JOB_TITLE",
  Level = "LEVEL",
  AccountGroup = "ACCOUNT_GROUP",
  MaterialGroup = "MATERIAL_GROUP", // Nhóm hàng
  ProductGroup = "PRODUCT_GROUP",
  ProviderCategory = "PROVIDER_CATEGORY",
  Department = "DEPARTMENT",
  Brand = "BRAND",
  PunchCategory = "PUNCH_CATEGORY",
  DeviceCategory = "DEVICE_CATEGORY",
  MachineCategory = "MACHINE_CATEGORY",
  RfiCategory = "RFI_CATEGORY",
  TaskCategory = "TASK_CATEGORY", // hạng mục công việc, tiến độ
  SubContractorCategory = "SUB_CONTRACT_CATEGORY", // loại thầu phụ
  Country = "COUNTRY",
  DrawCategory = "DRAW_CATEGORY",
  MemberShipCategory = "MEMBER_SHIP_CATEGORY",
  WorkType = "WORK_TYPE", // loại công tác
  InstructionCategory = "INSTRUCTION_CATEGORY", // Hạng mục
  BOQCategory = "BOQ_CATEGORY", // Hạng mục
  BOQGroup = "BOQ_GROUP", // nhom
  ChangeEventCategory = "CHANGE_EVENT_CATEGORY",
  DocumentCategory = "DOCUMENT_CATEGORY", // loại tài liệu
  DocumentItem = "DOCUMENT_ITEM", // Hạng mục
  FileAttachCategory = "FILE_ATTACH_CATEGORY", // loại tài liệu
  Floor = "FLOOR", // Tầng
}

export const DictionaryTypeForRoleCheck = {
  [DictionaryType.ServiceType]: "service-type",
  [DictionaryType.Document]: "DOCUMENT",
  [DictionaryType.JobTitle]: "job-title",
  [DictionaryType.Level]: "level",
  [DictionaryType.AccountGroup]: "account-group",
  [DictionaryType.MaterialGroup]: "material-group",
  [DictionaryType.ProductGroup]: "product-group",
  [DictionaryType.ProviderCategory]: "provider-category",
  [DictionaryType.Department]: "department",
  [DictionaryType.Brand]: "brand",
  [DictionaryType.PunchCategory]: "PUNCH_CATEGORY",
  [DictionaryType.DeviceCategory]: "device-group",
  [DictionaryType.MachineCategory]: "machine-group",
  [DictionaryType.RfiCategory]: "rfi-category",
  [DictionaryType.TaskCategory]: "TASK_CATEGORY",
  [DictionaryType.SubContractorCategory]: "subcontractor-category", // loại thầu phụ
  [DictionaryType.Country]: "country",
  [DictionaryType.DrawCategory]: "DRAW_CATEGORY",
  [DictionaryType.MemberShipCategory]: "contacts",
  [DictionaryType.WorkType]: "work-type", // loại công tác
  [DictionaryType.InstructionCategory]: "instruction-category", // Hạng mục
  [DictionaryType.BOQCategory]: "boq-category", // Danh mục boq
  [DictionaryType.BOQGroup]: "boq-group", // Nhóm boq
  [DictionaryType.ChangeEventCategory]: "classify", // phân loại sự kiện thay đổi
  // [DictionaryType.Contacts]: "contacts", // phân loại sự kiện thay đổi
  [DictionaryType.FileAttachCategory]: "file-attach-category", // loại file đính kèm
  [DictionaryType.DocumentCategory]: "document-category", // loại tài liệu
  [DictionaryType.DocumentItem]: "document-item", // Hạng mục tài liệu
  [DictionaryType.Floor]: "floor", // Tầng
};

export const DictionaryTypeTrans = {
  [DictionaryType.ServiceType]: {
    value: DictionaryType.ServiceType,
    label: "Loại dịch vụ",
  },
  [DictionaryType.Document]: {
    value: DictionaryType.Document,
    label: "Loại tài liệu",
  },
  [DictionaryType.JobTitle]: {
    value: DictionaryType.JobTitle,
    label: "Chức vụ",
  },
  [DictionaryType.Level]: {
    value: DictionaryType.Level,
    label: "Cấp bậc",
  },
  [DictionaryType.AccountGroup]: {
    value: DictionaryType.AccountGroup,
    label: "Nhóm tài khoản",
  },
  [DictionaryType.MaterialGroup]: {
    value: DictionaryType.MaterialGroup,
    label: "Nhóm nguyên vật liệu",
  },
  [DictionaryType.ProductGroup]: {
    value: DictionaryType.ProductGroup,
    label: "Nhóm hàng hóa",
  },
  [DictionaryType.ProviderCategory]: {
    value: DictionaryType.ProviderCategory,
    label: "Loại nhà cung cấp",
  },
  [DictionaryType.Department]: {
    value: DictionaryType.Department,
    label: "Phòng ban",
  },
  [DictionaryType.Brand]: {
    value: DictionaryType.Brand,
    label: "Thương hiệu",
  },
  [DictionaryType.PunchCategory]: {
    value: DictionaryType.PunchCategory,
    label: "Hạng mục Punch",
  },
  [DictionaryType.DeviceCategory]: {
    value: DictionaryType.DeviceCategory,
    label: "Nhóm thiết bị",
  },
  [DictionaryType.MachineCategory]: {
    value: DictionaryType.MachineCategory,
    label: "Nhóm máy",
  },
  [DictionaryType.RfiCategory]: {
    value: DictionaryType.RfiCategory,
    label: "Loại RFIs",
  },
  [DictionaryType.TaskCategory]: {
    value: DictionaryType.TaskCategory,
    label: "loại công việc",
  },
  [DictionaryType.SubContractorCategory]: {
    value: DictionaryType.SubContractorCategory,
    label: "Loại thầu phụ",
  },
  [DictionaryType.Country]: {
    value: DictionaryType.Country,
    label: "Quốc gia",
  },
  [DictionaryType.DrawCategory]: {
    value: DictionaryType.DrawCategory,
    label: "Loại bản vẽ",
  },
  [DictionaryType.MemberShipCategory]: {
    value: DictionaryType.MemberShipCategory,
    label: "Loại danh bạ",
  },
  [DictionaryType.WorkType]: {
    value: DictionaryType.WorkType,
    label: "Loại công tác",
  },
  [DictionaryType.InstructionCategory]: {
    value: DictionaryType.InstructionCategory,
    label: "Hạng mục",
  },
  [DictionaryType.BOQCategory]: {
    value: DictionaryType.BOQCategory,
    label: "Loại BOQ",
  },
  [DictionaryType.BOQGroup]: {
    value: DictionaryType.BOQGroup,
    label: "Nhóm BOQ",
  },
  [DictionaryType.ChangeEventCategory]: {
    value: DictionaryType.ChangeEventCategory,
    label: "Loại sự kiện thay đổi",
  },
  [DictionaryType.DocumentCategory]: {
    value: DictionaryType.DocumentCategory,
    label: "Loại tài liệu",
  },
  [DictionaryType.DocumentItem]: {
    value: DictionaryType.DocumentItem,
    label: "Hạng mục tài liệu",
  },
  [DictionaryType.FileAttachCategory]: {
    value: DictionaryType.FileAttachCategory,
    label: "Loại hình ảnh",
  },
  [DictionaryType.Floor]: {
    value: DictionaryType.Floor,
    label: "Tầng",
  },
};
