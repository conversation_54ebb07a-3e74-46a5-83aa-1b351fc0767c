import { Col, Form, Input, message, Modal, Row } from "antd";
import { Rule } from "antd/lib/form";
import { projectGroupApi } from "api/projectGroup.api";
import CustomInput from "components/Input/CustomInput";
import { TextInput } from "components/Input/TextInput";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { ModalStatus } from "types/modal";
import { ProjectGroup } from "types/projectGroup";

const rules: Rule[] = [{ required: true }];

export interface ProjectGroupModalRef {
  handleCreate: () => void;
  handleUpdate: (projectGroup: ProjectGroup) => void;
}
interface ProjectGroupModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
}

export const ProjectGroupModal = React.forwardRef(
  ({ onClose, onSubmitOk }: ProjectGroupModalProps, ref) => {
    const [form] = Form.useForm<ProjectGroup>();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");
    const [selectedProjectGroup, setSelectedProjectGroup] =
      useState<ProjectGroup>();

    useImperativeHandle<any, ProjectGroupModalRef>(
      ref,
      () => ({
        handleCreate() {
          form.resetFields();
          setVisible(true);
          setStatus("create");
        },
        handleUpdate(projectGroup: ProjectGroup) {
          setSelectedProjectGroup(projectGroup);
          form.setFieldsValue({ ...projectGroup });
          setVisible(true);
          setStatus("update");
        },
      }),
      []
    );

    const createData = async () => {
      const valid = await form.validateFields();
      const data = { projectGroup: form.getFieldsValue() };

      setLoading(true);
      try {
        const res = await projectGroupApi.create(data);
        message.success("Tạo nhóm dự án thành công!");
        onClose();
        onSubmitOk();
        setVisible(false);
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const valid = await form.validateFields();
      const data = { projectGroup: form.getFieldsValue() };
      setLoading(true);
      try {
        const res = await projectGroupApi.update(
          selectedProjectGroup?.id || 0,
          data
        );
        message.success("Cập nhật nhóm dự án thành công!");
        onClose();
        onSubmitOk();
        setVisible(false);
      } finally {
        setLoading(false);
      }
    };

    return (
      <Modal
        className="footer-full"
        onCancel={() => {
          onClose?.();
          setVisible(false);
        }}
        centered
        visible={visible}
        title={status == "create" ? "Tạo nhóm dự án" : "Cập nhật nhóm dự án"}
        style={{ top: 20 }}
        width={550}
        confirmLoading={loading}
        cancelButtonProps={{ style: { display: "none" } }}
        okText={status == "create" ? "Tạo" : "Cập nhật"}
        onOk={() => {
          status == "create" ? createData() : updateData();
        }}
        getContainer={() => {
          return document.getElementById("App") as HTMLElement;
        }}
      >
        <Form layout="vertical" form={form}>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item name="code" label="Mã nhóm dự án">
                <TextInput
                  disabled={status == "update"}
                  placeholder={status == "create" ? "Để trống sẽ tự sinh" : ""}
                />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item name="name" label="Tên nhóm dự án" rules={rules}>
                <CustomInput placeholder="Nhập tên nhóm dự án" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
);
