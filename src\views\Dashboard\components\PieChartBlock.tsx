import React from "react";
import { Paper, Typography } from "@mui/material";
import { Pie<PERSON><PERSON> } from "@mui/x-charts/PieChart";
import { divide } from "lodash";
import { ProductSelector } from "components/Selector/ProductSeletor";
import { Spin } from "antd";

type Props = {
  title: string;
  data: { label: string; value: number }[];
  width?: number;
  height?: number;
  query: any;
  setQuery: (query: any) => void;
  refetchData?: () => void;
  loading: boolean;
};

export const PieChartBlock = ({
  title,
  data,
  width,
  height = 250,
  query,
  setQuery,
  refetchData,
  loading = false,
}: Props) => {
  const total = data.reduce((sum, item) => sum + item.value, 0);
  const formatPercent = (value: number) =>
    `${((value / total) * 100).toFixed(1)}%`;
  return (
    <div className="!shadow-none w-full rounded-md p-4 bg-white lg:w-[49%]">
      <Typography variant="h6" gutterBottom className="!font-bold">
        {title}
      </Typography>
      <div className="w-full max-w-[300px]">
        <ProductSelector
          onChange={(value) => {
            if (value) {
              const newQuery = {
                ...query,
                productId: value || undefined,
              };
              setQuery(newQuery);
            } else {
              const newQuery = {
                ...query,
                productId: undefined,
              };
              setQuery(newQuery);
            }
          }}
        />
      </div>
      <div className="flex-1 my-4 flex justify-center items-center min-h-[400px]">
        {data.length < 2 ? (
          <div className="h-full flex justify-center items-center">
            Chưa có dữ liệu
          </div>
        ) : (
          <Spin spinning={loading}>
            <PieChart
              series={[
                {
                  data,
                  arcLabel: (item) =>
                    `${((item.value / total) * 100).toFixed(1)}%`, // ✅ đúng phần trăm
                  valueFormatter: (item) =>
                    `${((item.value / total) * 100).toFixed(1)}%`,
                  arcLabelMinAngle: 10,
                },
              ]}
              width={width}
              height={height}
              slotProps={{
                legend: {
                  direction: "horizontal",
                  position: {
                    vertical: "bottom",
                    horizontal: "center",
                  },
                },
              }}
            />
          </Spin>
        )}
      </div>
    </div>
  );
};
