import { PermissionNames } from "./PermissionNames";

export enum SystemConfigGroup {
  LevelAndDepartment = "level-and-department",
  GoodsAndDevice = "goods-and-device",
  Categories = "categories",
  Others = "others",
}

export enum ServiceTypePermission {
  List = PermissionNames.serviceTypeList,
  Add = PermissionNames.serviceTypeAdd,
  Edit = PermissionNames.serviceTypeEdit,
  Delete = PermissionNames.serviceTypeDelete,
}

export enum JobTitlePermission {
  List = PermissionNames.jobTitleList,
  Add = PermissionNames.jobTitleAdd,
  Edit = PermissionNames.jobTitleEdit,
  Delete = PermissionNames.jobTitleDelete,
}

export enum LevelPermission {
  List = PermissionNames.levelList,
  Add = PermissionNames.levelAdd,
  Edit = PermissionNames.levelEdit,
  Delete = PermissionNames.levelDelete,
}

export enum DepartmentPermission {
  List = PermissionNames.departmentList,
  Add = PermissionNames.departmentAdd,
  Edit = PermissionNames.departmentEdit,
  Delete = PermissionNames.departmentDelete,
}

export enum BrandPermission {
  List = PermissionNames.brandList,
  Add = PermissionNames.brandAdd,
  Edit = PermissionNames.brandEdit,
  Delete = PermissionNames.brandDelete,
}

export enum AccountGroupPermission {
  List = PermissionNames.accountGroupList,
  Add = PermissionNames.accountGroupAdd,
  Edit = PermissionNames.accountGroupEdit,
  Delete = PermissionNames.accountGroupDelete,
}

export enum MaterialGroupPermission {
  List = PermissionNames.materialGroupList,
  Add = PermissionNames.materialGroupAdd,
  Edit = PermissionNames.materialGroupEdit,
  Delete = PermissionNames.materialGroupDelete,
}

export enum ProductGroupPermission {
  List = PermissionNames.productGroupList,
  Add = PermissionNames.productGroupAdd,
  Edit = PermissionNames.productGroupEdit,
  Delete = PermissionNames.productGroupDelete,
}

export enum DeviceGroupPermission {
  List = PermissionNames.deviceGroupList,
  Add = PermissionNames.deviceGroupAdd,
  Edit = PermissionNames.deviceGroupEdit,
  Delete = PermissionNames.deviceGroupDelete,
}

export enum MachineGroupPermission {
  List = PermissionNames.machineGroupList,
  Add = PermissionNames.machineGroupAdd,
  Edit = PermissionNames.machineGroupEdit,
  Delete = PermissionNames.machineGroupDelete,
}

export enum ProviderCategoryPermission {
  List = PermissionNames.providerCategoryList,
  Add = PermissionNames.providerCategoryAdd,
  Edit = PermissionNames.providerCategoryEdit,
  Delete = PermissionNames.providerCategoryDelete,
}

export enum CountryPermission {
  List = PermissionNames.countryList,
  Add = PermissionNames.countryAdd,
  Edit = PermissionNames.countryEdit,
  Delete = PermissionNames.countryDelete,
}

export enum SubcontractorCategoryPermission {
  List = PermissionNames.subcontractorCategoryList,
  Add = PermissionNames.subcontractorCategoryAdd,
  Edit = PermissionNames.subcontractorCategoryEdit,
  Delete = PermissionNames.subcontractorCategoryDelete,
}

export enum WorkTypePermission {
  List = PermissionNames.workTypeList,
  Add = PermissionNames.workTypeAdd,
  Edit = PermissionNames.workTypeEdit,
  Delete = PermissionNames.workTypeDelete,
}

export enum InstructionCategoryPermission {
  List = PermissionNames.instructionCategoryList,
  Add = PermissionNames.instructionCategoryAdd,
  Edit = PermissionNames.instructionCategoryEdit,
  Delete = PermissionNames.instructionCategoryDelete,
}

export enum RfiCategoryPermission {
  List = PermissionNames.rfiCategoryList,
  Add = PermissionNames.rfiCategoryAdd,
  Edit = PermissionNames.rfiCategoryEdit,
  Delete = PermissionNames.rfiCategoryDelete,
}

export enum FileAttachCategoryPermission {
  List = PermissionNames.fileAttachCategoryList,
  Add = PermissionNames.fileAttachCategoryAdd,
  Edit = PermissionNames.fileAttachCategoryEdit,
  Delete = PermissionNames.fileAttachCategoryDelete,
}

export enum ClassifyPermission {
  List = PermissionNames.classifyList,
  Add = PermissionNames.classifyAdd,
  Edit = PermissionNames.classifyEdit,
  Delete = PermissionNames.classifyDelete,
}

export enum ContactsPermission {
  List = PermissionNames.contactsList,
  Add = PermissionNames.contactsAdd,
  Edit = PermissionNames.contactsEdit,
  Delete = PermissionNames.contactsDelete,
}

export enum CompanyPermission {
  List = PermissionNames.companyList,
  Add = PermissionNames.companyAdd,
  Edit = PermissionNames.companyEdit,
  Delete = PermissionNames.companyDelete,
}

// Nhóm tất cả các permission của system-config
export enum SystemConfigPermission {
  // Service Type
  ServiceTypeList = ServiceTypePermission.List,
  ServiceTypeAdd = ServiceTypePermission.Add,
  ServiceTypeEdit = ServiceTypePermission.Edit,
  ServiceTypeDelete = ServiceTypePermission.Delete,

  // Job Title
  JobTitleList = JobTitlePermission.List,
  JobTitleAdd = JobTitlePermission.Add,
  JobTitleEdit = JobTitlePermission.Edit,
  JobTitleDelete = JobTitlePermission.Delete,

  // Level
  LevelList = LevelPermission.List,
  LevelAdd = LevelPermission.Add,
  LevelEdit = LevelPermission.Edit,
  LevelDelete = LevelPermission.Delete,

  // Department
  DepartmentList = DepartmentPermission.List,
  DepartmentAdd = DepartmentPermission.Add,
  DepartmentEdit = DepartmentPermission.Edit,
  DepartmentDelete = DepartmentPermission.Delete,

  // Brand
  BrandList = BrandPermission.List,
  BrandAdd = BrandPermission.Add,
  BrandEdit = BrandPermission.Edit,
  BrandDelete = BrandPermission.Delete,

  // Account Group
  AccountGroupList = AccountGroupPermission.List,
  AccountGroupAdd = AccountGroupPermission.Add,
  AccountGroupEdit = AccountGroupPermission.Edit,
  AccountGroupDelete = AccountGroupPermission.Delete,

  // Material Group
  MaterialGroupList = MaterialGroupPermission.List,
  MaterialGroupAdd = MaterialGroupPermission.Add,
  MaterialGroupEdit = MaterialGroupPermission.Edit,
  MaterialGroupDelete = MaterialGroupPermission.Delete,

  // Product Group
  ProductGroupList = ProductGroupPermission.List,
  ProductGroupAdd = ProductGroupPermission.Add,
  ProductGroupEdit = ProductGroupPermission.Edit,
  ProductGroupDelete = ProductGroupPermission.Delete,

  // Device Group
  DeviceGroupList = DeviceGroupPermission.List,
  DeviceGroupAdd = DeviceGroupPermission.Add,
  DeviceGroupEdit = DeviceGroupPermission.Edit,
  DeviceGroupDelete = DeviceGroupPermission.Delete,

  // Machine Group
  MachineGroupList = MachineGroupPermission.List,
  MachineGroupAdd = MachineGroupPermission.Add,
  MachineGroupEdit = MachineGroupPermission.Edit,
  MachineGroupDelete = MachineGroupPermission.Delete,

  // Provider Category
  ProviderCategoryList = ProviderCategoryPermission.List,
  ProviderCategoryAdd = ProviderCategoryPermission.Add,
  ProviderCategoryEdit = ProviderCategoryPermission.Edit,
  ProviderCategoryDelete = ProviderCategoryPermission.Delete,

  // Country
  CountryList = CountryPermission.List,
  CountryAdd = CountryPermission.Add,
  CountryEdit = CountryPermission.Edit,
  CountryDelete = CountryPermission.Delete,

  // Subcontractor Category
  SubcontractorCategoryList = SubcontractorCategoryPermission.List,
  SubcontractorCategoryAdd = SubcontractorCategoryPermission.Add,
  SubcontractorCategoryEdit = SubcontractorCategoryPermission.Edit,
  SubcontractorCategoryDelete = SubcontractorCategoryPermission.Delete,

  // Work Type
  WorkTypeList = WorkTypePermission.List,
  WorkTypeAdd = WorkTypePermission.Add,
  WorkTypeEdit = WorkTypePermission.Edit,
  WorkTypeDelete = WorkTypePermission.Delete,

  // Instruction Category
  InstructionCategoryList = InstructionCategoryPermission.List,
  InstructionCategoryAdd = InstructionCategoryPermission.Add,
  InstructionCategoryEdit = InstructionCategoryPermission.Edit,
  InstructionCategoryDelete = InstructionCategoryPermission.Delete,

  // RFI Category
  RfiCategoryList = RfiCategoryPermission.List,
  RfiCategoryAdd = RfiCategoryPermission.Add,
  RfiCategoryEdit = RfiCategoryPermission.Edit,
  RfiCategoryDelete = RfiCategoryPermission.Delete,

  // File Attach Category
  FileAttachCategoryList = FileAttachCategoryPermission.List,
  FileAttachCategoryAdd = FileAttachCategoryPermission.Add,
  FileAttachCategoryEdit = FileAttachCategoryPermission.Edit,
  FileAttachCategoryDelete = FileAttachCategoryPermission.Delete,

  // Classify
  ClassifyList = ClassifyPermission.List,
  ClassifyAdd = ClassifyPermission.Add,
  ClassifyEdit = ClassifyPermission.Edit,
  ClassifyDelete = ClassifyPermission.Delete,

  // Contacts
  ContactsList = ContactsPermission.List,
  ContactsAdd = ContactsPermission.Add,
  ContactsEdit = ContactsPermission.Edit,
  ContactsDelete = ContactsPermission.Delete,

  // Company
  CompanyList = CompanyPermission.List,
  CompanyAdd = CompanyPermission.Add,
  CompanyEdit = CompanyPermission.Edit,
  CompanyDelete = CompanyPermission.Delete,
}

// Thêm interface để định nghĩa cấu trúc nhóm
export interface ISystemConfigGroup {
  key: SystemConfigGroup;
  title: string;
  permissions: string[];
}

// Định nghĩa các nhóm và permission tương ứng
export const SYSTEM_CONFIG_GROUPS: ISystemConfigGroup[] = [
  {
    key: SystemConfigGroup.LevelAndDepartment,
    title: "Cấp bậc - Phòng ban",
    permissions: [
      PermissionNames.jobTitleList,
      PermissionNames.levelList,
      PermissionNames.departmentList,
      PermissionNames.companyList,
      PermissionNames.accountGroupList,
    ],
  },
  {
    key: SystemConfigGroup.GoodsAndDevice,
    title: "Hàng hoá - Thiết bị",
    permissions: [
      PermissionNames.productGroupList,
      PermissionNames.materialGroupList,
      PermissionNames.deviceGroupList,
      PermissionNames.machineGroupList,
      PermissionNames.brandList,
      PermissionNames.boqGroupList,
    ],
  },
  {
    key: SystemConfigGroup.Categories,
    title: "Các Loại cấu hình",
    permissions: [
      PermissionNames.serviceTypeList,
      PermissionNames.providerCategoryList,
      PermissionNames.workTypeList,
      PermissionNames.contactsList,
      PermissionNames.subcontractorCategoryList,
    ],
  },
  {
    key: SystemConfigGroup.Others,
    title: "Khác",
    permissions: [
      PermissionNames.countryList,
      PermissionNames.fileAttachCategoryList,
      PermissionNames.rfiCategoryList,
      PermissionNames.classifyList,
    ],
  },
];
