import { ApprovalListType } from "./approvalList";
import { MemberShip } from "./memberShip";
import { MemberShipApproval } from "./memberShipApproval";
import { Staff } from "./staff";

export enum ApprovalTemplateType {
  RFI = "RFI",
  ChangeEvent = "CHANGE_EVENT",
  Draw = "DRAW",
  Task = "TASK",
  Instruction = "INSTRUCTION",
  BOQ = "BOQ",
  Report = "REPORT",
  Document = "DOCUMENT",
}

export type ApprovalTemplateComplex = "simple" | "complex";

export enum ApprovalTemplateName {
  Create = "CREATE",
  Publish = "PUBLISH",
}

export const ApprovalTemplateTypeOptions = [
  { value: ApprovalTemplateType.RFI, label: "RFI" },
  { value: ApprovalTemplateType.ChangeEvent, label: "Sự kiện thay đổi" },
  { value: ApprovalTemplateType.Draw, label: "Bản vẽ" },
  { value: ApprovalTemplateType.Task, label: "Công việc" },
  { value: ApprovalTemplateType.Instruction, label: "Chỉ thị công trường" },
  { value: ApprovalTemplateType.Report, label: "Báo cáo" },
  { value: ApprovalTemplateType.Document, label: "Tài liệu dự án" },
];

export enum ApprovalTemplateMode {
  Simple = "SIMPLE", //đơn giản
  Complex = "COMPLEX", //phức tạp
}

export const ApprovalTemplateModeTrans = {
  [ApprovalTemplateMode.Simple]: {
    label: "Đơn giản",
    value: ApprovalTemplateMode.Simple,
  },
  [ApprovalTemplateMode.Complex]: {
    label: "Phức tạp",
    value: ApprovalTemplateMode.Complex,
  },
};

export interface ApprovalTemplate {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  name: string;
  description: string;
  type: ApprovalTemplateType;
  isActive: boolean;
  isDefault: boolean;
  approvalTemplateDetails: ApprovalTemplateDetail[];
  respondentStaffs: Staff[];
  followStaffs: Staff[];
  followMemberShips: MemberShip[];
  responderMemberShips: MemberShip[];
  mode: ApprovalTemplateMode;
}

export interface ApprovalTemplateDetail {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  isAllApproval: boolean;
  name: string;
  actionText: string;
  statusText: string;
  statusColor: string;
  type: ApprovalListType;
  position: number;
  note: string;
  approvalTemplate: ApprovalTemplate;
  staff?: Staff;
  staff2?: Staff;
  memberShip?: MemberShip;
  memberShip2?: MemberShip;
  memberShipApprovals: MemberShipApproval[];
}
