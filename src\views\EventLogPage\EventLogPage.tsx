import {
  Button,
  Card,
  Image,
  message,
  Modal,
  Select,
  Space,
  Table,
  Tooltip,
} from "antd";
import { Pagination } from "components/Pagination";
import { useEffect, useState, useRef, useMemo } from "react";
import { getTitle } from "utils";
import { $url } from "utils/url";
import { ServiceModal, ServiceModalRef } from "./components/Modal/ServiceModal";
import { handleExport, MyExcelColumn } from "../../utils/MyExcel";
import { Link, useNavigate } from "react-router-dom";
import {
  checkDeletePermissionByCreator,
  checkEditPermissionByCreator,
  checkRoles,
} from "utils/auth";
import { PermissionNames } from "types/PermissionNames";
import { permissionStore } from "store/permissionStore";
import PageTitle from "components/PageTitle/PageTitle";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import PencilIcon from "assets/svgs/PencilIcon";
import CustomButton from "components/Button/CustomButton";
import DeleteIcon from "assets/svgs/DeleteIcon";
import CustomInput from "components/Input/CustomInput";
import { DictionaryType } from "types/dictionary";
import { TableProps } from "antd/lib";
import { ImportOutlined } from "@ant-design/icons";
import { useTransition } from "hooks/useTransition";
import { removeSubstringFromKeys } from "utils/common";
import { providerApi } from "api/provider.api";
import { exportTemplateWithValidation } from "utils/TemplateExcel";
import {
  getListNameByApi,
  getListNameByTypeDictionary,
} from "hooks/useDictionary";
import { unitApi } from "api/unit.api";
import QueryLabel from "components/QueryLabel/QueryLabel";
import { observer } from "mobx-react";
import { useChangeEvent } from "hooks/useChangeEvent";
import {
  ChangeEvent,
  getOverallApprovalStatus,
  ProgressChangeEventStatus,
  TypeLabel,
} from "types/changeEvent";
import { unixToFullDate } from "utils/dateFormat";
import ProgressLegend from "components/ProgressLegend/ProgressLegend";
import { changeEventApi } from "api/changeEvent.api";
import ImportChangeEvent, {
  ImportChangeEventModal,
} from "components/ImportDocument/ImportChangeEvent";
import { appStore } from "store/appStore";
import EditButton from "components/Button/EditButton";
import DeleteButton from "components/Button/DeleteButton";
import { userStore } from "store/userStore";

export const EventLogPage = observer(({ title = "" }) => {
  const {
    haveAddPermission,
    haveDeletePermission,
    haveEditPermission,
    haveViewAllPermission,
  } = checkRoles(
    {
      add: PermissionNames.eventLogAdd,
      edit: PermissionNames.eventLogEdit,
      delete: PermissionNames.eventLogDelete,
      viewAll: PermissionNames.eventLogViewAll,
    },
    permissionStore.permissions
  );

  const modalRef = useRef<ServiceModalRef>(null);
  const importModal = useRef<ImportChangeEventModal>();
  const [loadingExport, setLoadingExport] = useState(false);
  const [loadingDelete, setLoadingDelete] = useState(false);
  const [loadingDownloadDemo, setLoadingDownloadDemo] = useState(false);
  const { isLoaded } = useTransition();

  const exportColumns: MyExcelColumn<ChangeEvent>[] = [
    {
      header: "Mã",
      key: "code",
      columnKey: "code",
      headingStyle: { font: { bold: true } },
    },
    {
      header: "Tiêu đề",
      key: "title",
      columnKey: "title",
      headingStyle: { font: { bold: true } },
    },
    {
      header: "Lý do",
      key: "description",
      columnKey: "description",
      headingStyle: { font: { bold: true } },
    },
    {
      header: "Nội dung thay đổi",
      key: "contentChange",
      columnKey: "contentChange",
      headingStyle: { font: { bold: true } },
    },
    {
      header: "Phân loại",
      key: "type",
      columnKey: "type",
      headingStyle: { font: { bold: true } },
      render: (record: ChangeEvent) => TypeLabel[record.type]?.label || "",
    },
    {
      header: "Ngày tạo",
      key: "createdAt",
      columnKey: "createdAt",
      headingStyle: { font: { bold: true } },
      render: (record: ChangeEvent) => unixToFullDate(record.createdAt),
    },
    {
      header: "Ngày duyệt",
      key: "updatedAt",
      columnKey: "updatedAt",
      headingStyle: { font: { bold: true } },
      render: (record: ChangeEvent) => unixToFullDate(record.updatedAt),
    },
    {
      header: "Trạng thái",
      headingStyle: { font: { bold: true } },
      key: "statusapproval",
      columnKey: "statusapproval",
      render: (record: ChangeEvent) => {
        const sortedApprovalLists = record.approvalLists
          ? record.approvalLists.slice().sort((a, b) => a.position - b.position)
          : [];
        const statusKey = getOverallApprovalStatus(sortedApprovalLists);
        const statusInfo = ProgressChangeEventStatus[statusKey];
        return statusInfo?.label || "";
      },
    },
  ];

  const navigate = useNavigate();
  const {
    fetchData,
    loading,
    query,
    changeEvents,
    setQuery,
    total,
    isEmptyQuery,
  } = useChangeEvent({
    initQuery: {
      limit: 20,
      page: 1,
      isAdmin: haveViewAllPermission ? true : undefined,
      projectId: appStore.currentProject?.id,
    },
  });

  useEffect(() => {
    document.title = getTitle(title);
  }, []);

  useEffect(() => {
    if (isLoaded) {
      fetchData();
    }
  }, [isLoaded]);

  const handleTableChange: TableProps<any>["onChange"] = (
    pagination,
    filters,
    sorter
  ) => {
    if (!Array.isArray(sorter)) {
      const fieldMap: Record<string, string> = {
        name: "changeEvent.name",
        title: "changeEvent.title",
        description: "changeEvent.description",
        type: "changeEvent.type",
        createdAt: "changeEvent.createdAt",
        updatedAt: "changeEvent.updatedAt",
        code: "changeEvent.code",
      };
      const columnKey = sorter.field || sorter.column?.key;

      if (!sorter.order) {
        // setSortField(null);
        // setSortOrder(null);
        query.queryObject = undefined;
        setQuery({ ...query });
      } else {
        const order = sorter.order === "ascend" ? "ASC" : "DESC";
        // setSortField("jobCategory.name");
        // setSortOrder(order);
        const field = fieldMap[columnKey as string];

        const newQueryObject = JSON.stringify([
          {
            type: "sort",
            field,
            value: order,
          },
        ]);
        query.queryObject = newQueryObject;
        setQuery({ ...query });
      }
      fetchData();
    } else {
      query.queryObject = undefined;
      setQuery({ ...query });
      fetchData();
    }
  };

  const handleDeleteEvent = async (id: number) => {
    try {
      await changeEventApi.delete(id);
      message.success("Xóa sự kiện thay đổi thành công");
      fetchData();
    } catch (e) {
      console.log({ e });
    } finally {
    }
  };

  const handleRowClick = (record: ChangeEvent) => {
    navigate(
      `/report/${PermissionNames.eventLogEdit.replace(":id", record!.id + "")}`
    );
  };

  const canEditRecord = (record: ChangeEvent) => {
    return checkEditPermissionByCreator(
      userStore.info.id,
      record.createdBy?.id,
      haveEditPermission,
      haveViewAllPermission
    );
  };

  const canDeleteRecord = (record: ChangeEvent) => {
    return checkDeletePermissionByCreator(
      userStore.info.id,
      record.createdBy?.id,
      haveDeletePermission,
      haveViewAllPermission
    );
  };

  const columns: CustomizableColumn<ChangeEvent>[] = [
    {
      title: "Mã",
      dataIndex: "code",
      key: "code",
      width: 100,
      sorter: true,

      align: "center",
      render: (_, record) => {
        return (
          <div
            className="text-[#1677ff] cursor-pointer"
            onClick={() => handleRowClick(record)}
          >
            {record.code}
          </div>
        );

        // return (
        //   <Link
        //     to={`/master-data/${PermissionNames.serviceEdit.replace(
        //       ":id",
        //       record.id + ""
        //     )}`}
        //   >
        //     {record.code}
        //   </Link>
        // );
      },
    },
    {
      title: "Tiêu đề",
      dataIndex: "title",
      key: "title",
      sorter: true,

      width: 300,
      render: (_, record) => (
        <div className="flex items-center gap-[6px] text-ellipsis-2">
          <span>{record.title}</span>
        </div>
      ),
    },
    // {
    //   title: "Lý do",
    //   dataIndex: "description",
    //   key: "description",
    //   sorter: true,

    //   width: 300,
    //   render: (_, record) => (
    //     <div className="flex items-center gap-[6px] text-ellipsis-2">
    //       <span>{record.description}</span>
    //     </div>
    //   ),
    // },
    {
      title: "Loại",
      dataIndex: "type",
      key: "type",
      sorter: true,
      width: 150,
      render: (_, record) => TypeLabel[record.type]?.label,
    },
    {
      title: "Ngày tạo",
      dataIndex: "createdAt",
      key: "createdAt",
      sorter: true,
      width: 150,
      render: (_, record) => (
        <div className="flex items-center gap-[6px]">
          <span>{unixToFullDate(record.createdAt)}</span>
        </div>
      ),
    },
    {
      title: "Ngày duyệt",
      dataIndex: "updatedAt",
      key: "updatedAt",
      sorter: true,

      width: 150,
      render: (_, record) => (
        <div className="flex items-center gap-[6px]">
          <span>{unixToFullDate(record.updatedAt)}</span>
        </div>
      ),
    },
    {
      title: "Trạng thái",
      dataIndex: "isActive",
      key: "isActive",
      align: "center",
      width: 130,
      render: (isActive, record) => {
        // Sort approvalLists theo position trước khi xử lý
        const sortedApprovalLists = record.approvalLists
          ? record.approvalLists.slice().sort((a, b) => a.position - b.position)
          : [];
        return (
          <ProgressLegend
            status={
              ProgressChangeEventStatus[
                getOverallApprovalStatus(sortedApprovalLists)
              ]
            }
            steps={sortedApprovalLists}
          />
        );
      },
    },
    {
      title: "Xử lý",
      key: "action",
      fixed: "right",
      width: 100,
      align: "center",
      alwaysVisible: true,
      render: (_, record) => (
        <Space>
          {canEditRecord(record) && (
            <EditButton
              onClick={(e) => {
                e.stopPropagation();
                navigate(
                  `/report/${PermissionNames.eventLogEdit.replace(
                    ":id",
                    record!.id + ""
                  )}?update=1`
                );
              }}
            />
          )}
          {canDeleteRecord(record) && (
            <DeleteButton
              onClick={(e) => {
                e.stopPropagation();
                Modal.confirm({
                  title: `Xóa Sự kiện thay đổi "${record.title}"`,
                  getContainer: () => {
                    return document.getElementById("App") as HTMLElement;
                  },
                  icon: null,
                  content: (
                    <>
                      <div>
                        Bạn sẽ không thể khôi phục dữ liệu này sau khi xóa.
                        <br />
                        Bạn có chắc chắn muốn xóa dữ liệu này?
                      </div>
                    </>
                  ),
                  footer: (_, { OkBtn, CancelBtn }) => (
                    <>
                      <CustomButton
                        variant="outline"
                        className="cta-button"
                        onClick={() => {
                          handleDeleteEvent(record.id);
                          Modal.destroyAll();
                        }}
                      >
                        Có
                      </CustomButton>
                      <CustomButton
                        onClick={() => {
                          Modal.destroyAll();
                        }}
                        className="cta-button"
                      >
                        Không
                      </CustomButton>
                    </>
                  ),
                });
              }}
            />
          )}
        </Space>
      ),
    },
  ];

  const handleOnUploadedFile = async (excelData: any, setData: any) => {
    const { results } = excelData;

    console.log("results", results);

    const importData = results?.map((item: any) => {
      const refineRow = removeSubstringFromKeys(item, " *");

      const code = refineRow["Mã SK"]; // Change Event Code
      const title = refineRow["Tiêu đề"]; // Title
      const description = refineRow["Lý do"]; // Description/Reason
      const typeName = refineRow["Loại"]; // Type
      const statusName = refineRow["Trạng thái"]; // Status

      // Map type name to type value
      let type = undefined;
      if (typeName) {
        const typeEntry = Object.entries(TypeLabel).find(
          ([key, value]) => value.label === typeName
        );
        if (typeEntry) {
          type = typeEntry[0];
        }
      }

      // Map status name to status value
      let status = undefined;
      if (statusName) {
        const statusEntry = Object.entries(ProgressChangeEventStatus).find(
          ([key, value]) => value.label === statusName
        );
        if (statusEntry) {
          status = statusEntry[0];
        }
      }

      return {
        code,
        title,
        description,
        type,
        status,
        rowNum: item.__rowNum__,
      };
    });

    console.log("importData", importData);
    setData(importData);
  };

  const handleDownloadDemoExcel = async () => {
    try {
      setLoadingDownloadDemo(true);

      // Get the status options for change events
      const statusOptions = Object.values(ProgressChangeEventStatus).map(
        (item) => item.label
      );
      const typeOptions = Object.values(TypeLabel).map((item) => item.label);

      const result = await exportTemplateWithValidation({
        templatePath: "/exportFile/file_mau_nhap_change_event.xlsx",
        outputFileName: "file_mau_nhap_change_event.xlsx",
        sheetsToAdd: [
          { name: "Status", data: statusOptions },
          { name: "Type", data: typeOptions },
        ],
        validations: [
          {
            cell: "D2",
            type: "list",
            formulae: [`'Type'!$A$1:$A$${typeOptions.length}`],
          },
          {
            cell: "G2",
            type: "list",
            formulae: [`'Status'!$A$1:$A$${statusOptions.length}`],
          },
        ],
      });
    } catch (error) {
      message.error(
        `Có lỗi xảy ra: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    } finally {
      setLoadingDownloadDemo(false);
    }
  };

  return (
    <div>
      <PageTitle
        title={title}
        breadcrumbs={["Báo cáo", title]}
        extra={
          <Space>
            {haveAddPermission && (
              <>
                <CustomButton
                  size="small"
                  showPlusIcon
                  onClick={() => {
                    navigate(`/report/${PermissionNames.eventLogAdd}`);
                  }}
                >
                  Tạo sự kiện thay đổi
                </CustomButton>
                {/* <CustomButton
                  size="small"
                  icon={<ImportOutlined />}
                  onClick={() => {
                    importModal.current?.open();
                  }}
                >
                  Nhập excel
                </CustomButton> */}
              </>
            )}
          </Space>
        }
      />

      <div className="app-container">
        <Card>
          <div className="flex gap-[16px] items-end pb-[12px] justify-between flex-wrap">
            <div className="flex gap-[16px] items-end">
              <div className="w-[300px]">
                <CustomInput
                  tooltipContent={"Tìm theo mã, tên sự kiện thay đổi"}
                  label="Tìm kiếm"
                  placeholder="Tìm kiếm"
                  onPressEnter={() => {
                    console.log("onPressEnter:");
                    query.page = 1;
                    setQuery({ ...query });
                    fetchData();
                  }}
                  value={query.search}
                  onChange={(value) => {
                    console.log("change search value:", value);
                    query.search = value;
                    setQuery({ ...query });

                    if (!value) {
                      fetchData();
                    }
                  }}
                  allowClear
                />
              </div>
              {/* <div>
                <QueryLabel>Trạng thái</QueryLabel>
                <Select
                  value={query.status}
                  options={Object.values(ProgressChangeEventStatus).map(
                    (item) => ({
                      label: item.label,
                      value: item.value,
                    })
                  )}
                  placeholder="Tất cả trạng thái"
                  allowClear
                  onChange={(value) => {
                    setQuery({ ...query, status: value });
                  }}
                />
              </div> */}
              <CustomButton
                onClick={() => {
                  query.page = 1;
                  query.projectId = appStore.currentProject?.id;
                  setQuery({ ...query });
                  fetchData();
                }}
              >
                Áp dụng
              </CustomButton>

              {!isEmptyQuery && (
                <CustomButton
                  variant="outline"
                  onClick={() => {
                    delete query.providerId;
                    delete query.serviceTypeId;
                    delete query.search;
                    query.projectId = appStore.currentProject?.id;
                    setQuery({ ...query });
                    fetchData();
                  }}
                >
                  Bỏ lọc
                </CustomButton>
              )}
            </div>

            <CustomButton
              onClick={() => {
                Modal.confirm({
                  title: `Bạn có muốn xuất file excel?`,
                  getContainer: () => {
                    return document.getElementById("App") as HTMLElement;
                  },
                  icon: null,
                  footer: (_, { OkBtn, CancelBtn }) => (
                    <>
                      <CustomButton
                        variant="outline"
                        className="cta-button"
                        onClick={async () => {
                          handleExport({
                            onProgress(percent) {
                              console.log("What is percent", percent);
                            },
                            exportColumns,
                            fileType: "xlsx",
                            dataField: "changeEvents",
                            query: query,
                            // Bọc lại API ở đây:
                            api: (params) => {
                              return changeEventApi.findAll(params);
                            },
                            fileName: "Danh sách sự kiện thay đổi",
                            sheetName: "Danh sách sự kiện thay đổi",
                          });
                          Modal.destroyAll();
                        }}
                      >
                        Có
                      </CustomButton>
                      <CustomButton
                        onClick={() => {
                          Modal.destroyAll();
                        }}
                        className="cta-button"
                      >
                        Không
                      </CustomButton>
                    </>
                  ),
                });
              }}
            >
              Xuất excel
            </CustomButton>
          </div>
          <CustomizableTable
            columns={columns}
            dataSource={changeEvents}
            rowKey="id"
            loading={loading}
            pagination={false}
            scroll={{ x: 1200 }}
            bordered
            displayOptions
            tableId="service-page"
            //@ts-ignore
            onChange={handleTableChange}
            onRowClick={handleRowClick}
          />

          <Pagination
            currentPage={query.page}
            defaultPageSize={query.limit}
            total={total}
            onChange={({ limit, page }) => {
              query.page = page;
              query.limit = limit;
              query.projectId = appStore.currentProject?.id;
              setQuery({ ...query });
              fetchData();
            }}
          />
          <ServiceModal
            onSubmitOk={fetchData}
            onClose={() => {}}
            ref={modalRef}
          />
          {useMemo(
            () => (
              <ImportChangeEvent
                guide={[
                  "Vui lòng tải và sử dụng file mẫu để import dữ liệu đúng cách",
                  "Không được thay đổi tiêu đề trong file csv/excel mẫu để tránh import thiếu dữ liệu",
                  "Chi tiết xem tại sheet Hướng dẫn của file import mẫu",
                  "Các trường bắt buộc: Mã SK, Tiêu đề, Lý do, Loại, Trạng thái",
                ]}
                onSuccess={() => {
                  query.page = 1;
                  fetchData();
                }}
                ref={importModal}
                createApi={changeEventApi.create}
                onUploaded={(excelData, setData) => {
                  console.log("Uploaded change event data:", excelData);
                  handleOnUploadedFile(excelData, setData);
                }}
                okText={`Nhập sự kiện thay đổi ngay`}
                onDownloadDemoExcel={handleDownloadDemoExcel}
                loadingDownloadDemo={loadingDownloadDemo}
              />
            ),
            [loadingDownloadDemo]
          )}
        </Card>
      </div>
    </div>
  );
});
