import { Select } from "antd";
import { DefaultOptionType } from "antd/es/select";
import { SelectProps } from "antd/lib";
import { debounce, isEmpty, uniqBy } from "lodash";
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
} from "react";
import { QueryParams2 } from "types/query";
import { ProjectItem } from "types/projectItem";
import { useProjectItem } from "hooks/useProjectItem";
import { projectItemApi } from "api/projectItem.api";

type CustomFormItemProps = {
  value?: number;
  initQuery?: QueryParams2;
  disabled?: boolean;
  selectedColor?: any[];
  multiple?: boolean;
  onChange?: (value: any) => void;
  selectProps?: SelectProps;
  initOptionItem?: ProjectItem | ProjectItem[];
  valueIsOption?: boolean;
  allowClear?: boolean;
  placeholder?: string;
  addonOptions?: any[];
};

export interface ProjectItemSelector {
  refresh(): void;
}

/**
 * A demo that changes it's number value on click.
 * @param value initialValue passed by parent Form.Item.
 * @param onChange a callback for Form.Item to read its child component's value.
 * @constructor
 */
export const ProjectItemSelector = forwardRef(
  (
    {
      value,
      onChange,
      initQuery,
      disabled,
      multiple = false,
      selectedColor,
      initOptionItem,
      valueIsOption,
      selectProps,
      allowClear = true,
      placeholder = "Chọn hạng mục",
      addonOptions = [],
    }: CustomFormItemProps,
    ref
  ) => {
    const {
      projectItems,
      loading: loadingProjectItem,
      fetchData: fetchProjectItems,
      query: queryProjectItem,
      setData: setProjectItems,
      isFetched,
    } = useProjectItem({
      initQuery: {
        page: 1,
        limit: 50,
        ...initQuery,
      },
    });

    useImperativeHandle<any, ProjectItemSelector>(
      ref,
      () => ({
        refresh() {
          fetchProjectItems();
        },
      }),
      []
    );

    useEffect(() => {
      fetchProjectItems();
    }, [selectedColor]);

    const debounceSearch = useCallback(
      debounce((keyword) => {
        queryProjectItem.search = keyword;
        fetchProjectItems();
      }, 300),
      [queryProjectItem]
    );

    //xử lý nếu trong projects thiếu value hiện tại thì add vào
    useEffect(() => {
      if (value && isFetched) {
        const find = projectItems.find((e) => e.id == value);
        if (!find) {
          projectItemApi.findOne(value).then((res) => {
            if (!isEmpty(res.data)) {
              setProjectItems((prev) => [res.data, ...prev]);
            }
          });
        }
      }
    }, [value, projectItems, isFetched]);

    const options = useMemo(() => {
      let data = [...projectItems];
      if (initOptionItem) {
        if ((initOptionItem as ProjectItem[])?.length) {
          data = data.concat(initOptionItem);
        } else {
          data.push(initOptionItem as ProjectItem);
        }
      }

      return uniqBy([...addonOptions, ...data], (data) => data.id);
    }, [projectItems, initOptionItem]);

    return (
      <Select<any, DefaultOptionType>
        mode={multiple ? "multiple" : undefined}
        disabled={disabled}
        loading={loadingProjectItem}
        style={{ width: "100%" }}
        allowClear={allowClear}
        onSearch={debounceSearch}
        showSearch
        placeholder={placeholder}
        filterOption={false}
        value={value}
        onChange={(v, opts) => {
          if (v === undefined || v.length == 0) {
            debounceSearch(v);
          }
          if (valueIsOption) {
            if (opts instanceof Array) {
              onChange?.(opts?.map((v) => v.item));
            } else {
              onChange?.(opts?.item);
            }
          } else {
            onChange?.(v);
          }
        }}
        {...selectProps}
      >
        {options?.map((item) => (
          <Select.Option item={item} value={item.id} key={item.id}>
            <div className="flex items-center gap-2">
              <span>{item.name}</span>
            </div>
          </Select.Option>
        ))}
      </Select>
    );
  }
);
