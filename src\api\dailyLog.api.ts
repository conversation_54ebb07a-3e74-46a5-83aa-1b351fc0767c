import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const dailyLogApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dailyLog",
      params,
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/dailyLog/${id}`,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dailyLog",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/dailyLog/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/dailyLog/${id}`,
      method: "delete",
    }),
};
