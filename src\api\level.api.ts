import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const levelApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/level",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/level",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/level/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/level/${id}`,
      method: "delete",
    }),
};
