import { Card, Checkbox, Col, Form, message, Row } from "antd";
import { Rule } from "antd/lib/form";
import { roleApi } from "api/role.api";
import CustomButton from "components/Button/CustomButton";
import CustomInput from "components/Input/CustomInput";
import PageTitle from "components/PageTitle/PageTitle";
import { CustomizableColumn } from "components/Table/CustomizableTable";
import { usePermission } from "hooks/usePermission";
import { isEmpty, uniq } from "lodash";
import { observer } from "mobx-react";
import { useEffect, useMemo, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { PermissionNames } from "types/PermissionNames";
import { ModalStatus } from "types/modal";
import {
  Permission,
  PermissionType,
  PermissionTypeTrans,
} from "types/permission";
import { Role } from "types/role";
import { getTitle } from "utils";
import RoleTableNew, { RoleColumn } from "./components/RoleTable";
import { BMDTextArea } from "components/TextArea/BMDTextArea";
import { BMDCKEditor } from "components/Editor";

const rules: Rule[] = [{ required: true }];
const descriptionRules: Rule[] = [{ required: false }];

interface ITreeData {
  title: string;
  key: string;
  children?: ITreeData[];
}

interface IPermission {
  key: string;
  title: string;
  children?: IPermission[];
  permissionTypes?: PermissionType[];
}

interface EditRolePageProps {
  title: string;
  status: ModalStatus;
}

interface RoleForm extends Role {}

export const CreateOrUpdateRolePage = observer(
  ({ title = "", status }: EditRolePageProps) => {
    const params = useParams();
    const navigate = useNavigate();
    const { permissions, fetchData } = usePermission({
      initQuery: { page: 1, limit: 0 },
    });

    const [form] = Form.useForm<RoleForm>();
    const [loading, setLoading] = useState(false);
    const [selectedRole, setSelectedRole] = useState<Role>();
    const [checkedNames, setCheckedNames] = useState<string[]>([]);

    useEffect(() => {
      document.title = getTitle(title);
    }, []);

    // console.log({ checkedNames, permissions });

    const generateSelectedKeys = async (role: Role) => {
      if (role.id) {
        const permissions: Permission[] = role.permissions;

        const _checkedNames = permissions.map((e) => e.name);
        // adminRoutes.forEach((r) => {
        //   if (r.children) {
        //     r.children.forEach((rc) => {
        //       if (rc.isPublic && rc.name?.includes(PermissionType.List)) {
        //         _checkedNames.push(rc.name);
        //       }
        //     });
        //   } else if (r.isPublic && r.name?.includes(PermissionType.List)) {
        //     _checkedNames.push(r.name);
        //   }
        // });

        setCheckedNames(uniq(_checkedNames));
      }
    };

    const getOneRole = async (id: number) => {
      try {
        setLoading(true);
        const { data } = await roleApi.findOne(id);

        if (isEmpty(data)) {
          navigate("/404");

          return;
        }

        setSelectedRole(data);
        form.setFieldsValue({
          ...data,
        });
        generateSelectedKeys(data);
        fetchData();

        return data as Role;
      } catch (e: any) {
      } finally {
        setLoading(false);
      }
    };

    useEffect(() => {
      document.title = getTitle(title);

      if (status == "update") {
        const roleId = params.id;
        if (roleId) {
          getOneRole(+roleId);

          // searchParams.delete("RoleId");
          // setSearchParams(searchParams);
        }
      }
    }, []);

    const getDataSubmit = () => {
      const { ...data } = form.getFieldsValue();

      const payload = {
        role: {
          ...data,
        },
        permissionIds: checkedNames
          .map((e) => {
            const find = permissions.find((p) => p.name == e);
            return find?.id;
          })
          .filter((e) => e),
      };

      return payload;
    };

    const createData = async () => {
      const valid = await form.validateFields();

      setLoading(true);
      try {
        const res = await roleApi.create(getDataSubmit());
        message.success("Tạo vai trò thành công!");
        navigate(`/master-data/${PermissionNames.roleList}`);
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const valid = await form.validateFields();

      setLoading(true);
      try {
        const payload = getDataSubmit();
        const res = await roleApi.update(selectedRole!?.id || 0, payload);
        message.success("Chỉnh sửa vai trò thành công!");
        navigate(`/master-data/${PermissionNames.roleList}`);
      } finally {
        setLoading(false);
      }
    };

    const handleSubmit = () => {
      if (status == "create") {
        createData();
      } else {
        updateData();
      }
    };

    const pageTitle = useMemo(
      () => (status == "create" ? "Tạo vai trò" : "Chỉnh sửa vai trò"),
      [status]
    );

    const columns: CustomizableColumn<RoleColumn>[] = useMemo(() => {
      const cols: CustomizableColumn<RoleColumn>[] = [
        {
          title: "Chức năng",
          key: "feature",
          dataIndex: "feature",
        },
      ];

      Object.values(PermissionTypeTrans).forEach((it) => {
        cols.push({
          title: it.label,
          key: it.value,
          dataIndex: it.value,
          align: "center",
          width: 100,
          render: (_, record) => {
            return record.routeChildren ? null : (
              <Checkbox
                className="py-[7.25px]"
                disabled={it.value != PermissionType.List}
                checked={
                  checkedNames.includes(record.name as any) &&
                  it.value == PermissionType.List
                }
                onChange={(e) => {
                  const checked = e.target.checked;
                  const checkedName = record.name || "";
                  if (checked) {
                    setCheckedNames(uniq([...checkedNames, checkedName]));
                  } else {
                    setCheckedNames(
                      checkedNames.filter((k) => k !== checkedName)
                    );
                  }
                }}
              />
            );
          },
        });
      });

      return cols;
    }, [checkedNames]);

    return (
      <div className="app-container">
        <PageTitle
          back
          breadcrumbs={[
            { label: "Dữ liệu nguồn" },
            {
              label: "Danh mục vai trò",
              href: `/master-data/${PermissionNames.roleList}`,
            },
            { label: pageTitle },
          ]}
          title={pageTitle}
        />
        <Card>
          <Form layout="vertical" form={form}>
            <Row gutter={16}>
              <Col span={14}>
                <Form.Item label="Tên vai trò" name="name" rules={rules}>
                  <CustomInput
                    placeholder="Tên vai trò"
                    disabled={selectedRole?.isAdmin}
                  />
                </Form.Item>
                <Form.Item
                  label="Mô tả"
                  name="description"
                  rules={descriptionRules}
                >
                  {/* <BMDTextArea
                    placeholder="Mô tả"
                    disabled={selectedRole?.isAdmin}
                  /> */}
                  <BMDCKEditor
                    placeholder="Mô tả"
                    disabled={selectedRole?.isAdmin}
                    inputHeight={300}
                    value={selectedRole?.description}
                    onChange={(content) => {
                      form.setFieldsValue({ description: content });
                    }}
                  />
                </Form.Item>
              </Col>

              {status == "update" && (
                <Col span={24}>
                  <Form.Item label="Phân quyền" name="permissions" required>
                    <RoleTableNew
                      tableProps={{ scroll: { y: 600 } }}
                      loading={loading}
                      checkedNames={checkedNames}
                      setCheckedNames={setCheckedNames}
                      // columns={columns}
                    />
                  </Form.Item>
                </Col>
              )}
            </Row>
          </Form>
          <div className="flex gap-[16px] mt-2">
            <CustomButton
              // variant="outline"
              className="cta-button"
              loading={loading}
              onClick={() => {
                handleSubmit();
              }}
            >
              {status == "create" ? "Tạo vai trò" : "Chỉnh sửa vai trò"}
            </CustomButton>
            <CustomButton
              variant="outline"
              className="cta-button"
              onClick={() => {
                navigate(`/master-data/${PermissionNames.roleList}`);
              }}
            >
              Hủy
            </CustomButton>
          </div>
        </Card>
      </div>
    );
  }
);
