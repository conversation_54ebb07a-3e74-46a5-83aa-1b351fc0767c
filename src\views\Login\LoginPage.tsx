import { LockOutlined, UserOutlined } from "@ant-design/icons";
import { Button, Form, Input, message } from "antd";
import logo from "assets/images/logo.png";
import { useEffect, useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { adminRoutes } from "router";
import { settings } from "settings";
import { permissionStore } from "store/permissionStore";
import { userStore } from "store/userStore";
import { getTitle } from "utils";
import "./styles/LoginPage.scss";
import { RiLockPasswordLine } from "react-icons/ri";
import CustomButton from "components/Button/CustomButton";
import { AuthLayout } from "layouts/AuthLayout";
const { Item: FormItem } = Form;

function LoginPage({ title = "" }) {
  const [form] = Form.useForm<{ username: string; password: string }>();
  const [loading, setLoading] = useState(false);
  const navigation = useNavigate();
  useEffect(() => {
    document.title = getTitle(title);
  }, []);

  const handleSubmit = async () => {
    setLoading(true);
    const { username, password } = form.getFieldsValue();
    try {
      await userStore.login(username, password);
      await userStore.getProfile();
      permissionStore.accessRoutes = [...adminRoutes];
      if (settings.checkPermission && userStore.info.role) {
        await permissionStore.fetchPermissions(userStore.info.role.id);
        // if (!permissionStore.permissions.length || username == "bct") {
        //   message.error("Không có quyền truy cập");
        //   throw new Error("");
        // }
        permissionStore.setAccessRoutes();

        let firstRouteAccess = permissionStore.accessRoutes.find(
          (r) => r.isPublic || r.isAccess || !settings.checkPermission
        );

        let firstPath = "/";

        if (firstRouteAccess) {
          if (firstRouteAccess.children) {
            const firstChildRouteAccess = firstRouteAccess.children.find(
              (cr) => cr.isPublic || cr.isAccess || !settings.checkPermission
            );
            if (firstChildRouteAccess) {
              firstPath =
                firstRouteAccess.path + "/" + firstChildRouteAccess.path;
            } else {
              firstPath = firstRouteAccess.path || "";
            }
          }
        }
        // debugger;

        // navigation(firstPath);
        navigation("/project/project/list");
      } else {
        navigation("/");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthLayout>
      <Form
        className="login-form"
        onFinish={handleSubmit}
        form={form}
        layout={"vertical"}
      >
        <div className="flex flex-col gap-6">
          <div className="flex justify-center flex-col items-center">
            <img
              src={settings.logo}
              className="block mx-auto w-[150px] object-contain"
            />
          </div>
          <FormItem
            label="Tên đăng nhập"
            name="username"
            rules={[{ required: true, message: "Bắt buộc" }]}
          >
            <Input
              prefix={<UserOutlined />}
              size="large"
              className="!h-[40px]"
            />
          </FormItem>
          <FormItem
            label="Mật khẩu"
            name="password"
            rules={[{ required: true, message: "Bắt buộc" }]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              size="large"
              className="!h-[40px]"
            />
          </FormItem>
          <FormItem>
            <CustomButton
              htmlType="submit"
              style={{
                width: "100%",
                marginTop: 10,
              }}
              loading={loading}
              size="large"
              className="login-btn"
            >
              Đăng nhập
            </CustomButton>
            <div className="z-10 opacity-50 text-right text-base">
              v{settings.version}
            </div>
          </FormItem>
        </div>
      </Form>
    </AuthLayout>
  );

  return (
    <div className="login-page">
      <div style={{ paddingTop: 120 }}>
        <div className="login-container">
          <div className="logo text-center">
            <span style={{ fontSize: 22 }}>
              <img src={logo} width={140} alt="" />
            </span>
          </div>

          <Form onFinish={handleSubmit} form={form} layout={"vertical"}>
            <FormItem
              label="Tên đăng nhập"
              name="username"
              rules={[{ required: true, message: "Bắt buộc" }]}
            >
              <Input prefix={<UserOutlined />} size="large" />
            </FormItem>

            <FormItem
              label="Mật khẩu"
              name="password"
              rules={[{ required: true, message: "Bắt buộc" }]}
            >
              <Input.Password prefix={<LockOutlined />} size="large" />
            </FormItem>

            <div className=" flex cursor-pointer items-center justify-end gap-1 text-primary">
              {/* <RiLockPasswordLine /> */}

              {/* <Link
                // onClick={() => closeModal()}
                to={"/forgot-password"}
                className="text-primary "
              >
                Quên mật khẩu
              </Link> */}
            </div>
            <FormItem>
              <CustomButton
                htmlType="submit"
                style={{ width: "100%", marginTop: 10 }}
                loading={loading}
                size="large"
              >
                Đăng nhập
              </CustomButton>
            </FormItem>
          </Form>
          <div>Version: {settings.version}</div>
        </div>
      </div>
    </div>
  );
}

export { LoginPage };
