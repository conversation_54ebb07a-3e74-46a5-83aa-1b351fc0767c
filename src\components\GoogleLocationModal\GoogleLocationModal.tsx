import { Modal } from "antd";

import { useEffect, useState } from "react";
import "./styles/googleLocationModal.scss";
import MapWithAutocomplete from "components/Map/MapWithAutocomplete";
import { googleMapsApi } from "api/googleMaps.api";
import { CoordAddress } from "types/address";

export const GoogleLocationModal = ({
  coord,
  visible,
  onClose,
  onSubmitOk,
}: {
  visible?: boolean;
  onClose: () => void;
  onSubmitOk: (addressInfo: CoordAddress) => void;
  coord: CoordAddress;
}) => {
  const [loading, setLoading] = useState(false);
  const [addressInfo, setAddressInfo] = useState<CoordAddress>();
  const [originAddress, setOriginAddress] = useState<string | undefined>("");

  useEffect(() => {
    if (coord.lat) {
      setOriginAddress(coord.address);
      setAddressInfo({
        address: coord.address,
        lat: coord.lat,
        lng: coord.lng,
        placeId: coord.placeId,
      });
    }
  }, [coord]);

  const handleSubmitForm = async () => {
    if (addressInfo) {
      console.log(addressInfo);
      onSubmitOk(addressInfo);
      onClose();
    }
  };

  const handleCheckLocation = async (address: CoordAddress) => {
    try {
      setLoading(true);
      const { data } = await googleMapsApi.getPlaceDetail({
        placeId: address.placeId,
      });

      if (data && data.result) {
        setAddressInfo({
          ...address,
          rate: data.result.rating || 0,
          totalRate: data.result.user_ratings_total || 0,
          rateWant: (data.result.rating ?? 0) < 4.9 ? 4.9 : 5,
          address: data.result.formatted_address || address.address,
          name: data.result.name || address.name,
          mapUrl: data.result.url,
          countReviews: data.result.countReviews,
        });
      }
    } catch (error) {
      console.log({ error });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      className="address-modal"
      onCancel={onClose}
      open={true}
      title={"Chọn địa điểm"}
      confirmLoading={loading}
      onOk={handleSubmitForm}
      centered
      width={600}
      okText={"Lưu"}
      okButtonProps={{
        disabled: addressInfo?.address == originAddress || !addressInfo,
      }}
      cancelText={"Đóng"} //   style={}
    >
      <MapWithAutocomplete
        coords={
          addressInfo ? [{ lat: addressInfo.lat, lng: addressInfo.lng }] : []
        }
        onPlaceSelected={(address: CoordAddress) => {
          console.log({ address });
          // setAddressInfo(address);
          handleCheckLocation(address);
        }}
      />
    </Modal>
  );
};
