import { Col } from "antd";
import { FormInstance, useWatch } from "antd/es/form/Form";
import CustomSelect from "components/Input/CustomSelect";
import { useAddress } from "hooks/useAddress";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { AddressData, City, District, Ward } from "types/address";

export interface AddressParam {
  parentCode?: string;
}

export interface AddressSelectRef {
  setValue: (data: IAddress) => void;
}

export interface IAddress {
  district: District;
  city: City;
  ward: Ward;
}

export const AddressSelect = React.forwardRef(
  (
    {
      form,
      onChange,
      disabled,
      required = true,
    }: {
      form: FormInstance<any>;
      onChange: (data: any) => void;
      disabled?: boolean;
      required?: boolean;
    },
    ref
  ) => {
    const [queryWard, setQueryWard] = useState<AddressParam>();
    const [queryDistrict, setQueryDistrict] = useState<AddressParam>();
    const cityId = useWatch("cityId", form);
    const districtId = useWatch("districtId", form);

    useImperativeHandle(ref, () => ({
      setValue(data: IAddress) {
        if (data && data.ward) updateWard([...wards, data.ward]);
        if (data && data.city) updateCity([...cities, data.city]);
        if (data && data.district)
          updateDistrict([...districts, data.district]);

        form.setFieldsValue({
          cityId: data?.city?.id,
          wardId: data?.ward?.id,
          districtId: data?.district?.id,
        });
      },
    }));

    const {
      cities,
      districts,
      loading,
      wards,
      fetchCity,
      fetchDistrict,
      fetchWard,
      clearDistrict,
      clearWard,
      updateCity,
      updateDistrict,
      updateWard,
    } = useAddress();

    useEffect(() => {
      if (queryDistrict?.parentCode) fetchDistrict(queryDistrict);
    }, [queryDistrict?.parentCode]);

    useEffect(() => {
      if (queryWard?.parentCode) fetchWard(queryWard);
    }, [queryWard?.parentCode]);

    useEffect(() => {
      const data = form.getFieldsValue();
      if (
        cities &&
        data &&
        Array.isArray(cities) &&
        data.districtId &&
        data.cityId
      ) {
        const code = cities.find((e) => e.id === data.cityId)?.code;
        setQueryDistrict({ parentCode: code });
      }
    }, [cityId, cities]);

    useEffect(() => {
      const data = form.getFieldsValue();
      if (
        districts &&
        data &&
        Array.isArray(districts) &&
        data.districtId &&
        data.wardId
      ) {
        const code = districts.find((e) => e.id === data.districtId)?.code;
        setQueryWard({ parentCode: code });
      }
    }, [districtId, districts]);

    useEffect(() => {
      fetchCity();
    }, []);

    const handleChangeCity = (cityId: number) => {
      form.setFieldValue("cityId", cityId);
      form.setFieldValue("districtId", undefined);
      form.setFieldValue("wardId", undefined);
      if (cityId) {
        const code = cities.find((e) => e.id === cityId)?.code;
        setQueryDistrict({ parentCode: code });
      } else {
        clearDistrict();
        clearWard();
      }
      handleSubmit();
    };

    const handleChangeDistrict = (districtId: number) => {
      form.setFieldValue("districtId", districtId);
      form.setFieldValue("wardId", undefined);
      if (districtId) {
        const parentCode = districts.find((e) => e.id === districtId)?.code;
        setQueryWard({ parentCode });
      } else {
        clearWard();
      }
      handleSubmit();
    };

    const handleChangeWard = (wardId: number) => {
      form.setFieldValue("wardId", wardId);
      handleSubmit();
    };

    const handleSubmit = () => {
      const { cityId, districtId, wardId } = form.getFieldsValue();
      if (cityId && districtId && wardId) {
        const data: AddressData = {
          city: cities.find((e) => e.id === cityId),
          district: districts.find((e) => e.id === districtId),
          ward: wards.find((e) => e.id === wardId),
        };
        onChange(data);
      } else {
        onChange(undefined);
      }
    };

    return (
      <>
        <Col span={6}>
          <CustomSelect
            className="mt-[5px]"
            label="Tỉnh/Thành phố"
            required={required}
            value={form.getFieldValue("cityId")}
            onChange={handleChangeCity}
            placeholder="Nhập tên tỉnh/thành phố"
            options={cities.map((item) => ({
              label: item.nameWithType,
              value: item.id,
            }))}
            allowClear
            showSearch
            filterOption={(input, option) =>
              option?.label.toLowerCase().includes(input.toLowerCase())
            }
            disabled={disabled}
          />
        </Col>

        <Col span={6}>
          <CustomSelect
            className="mt-[5px]"
            label="Quận/Huyện"
            required={required}
            value={form.getFieldValue("districtId")}
            onChange={handleChangeDistrict}
            disabled={!districts.length || disabled}
            placeholder="Nhập tên Quận/Huyện"
            options={districts.map((item) => ({
              label: item.nameWithType,
              value: item.id,
            }))}
            allowClear
            showSearch
            filterOption={(input, option) =>
              option?.label.toLowerCase().includes(input.toLowerCase())
            }
          />
        </Col>

        <Col span={6}>
          <CustomSelect
            className="mt-[5px]"
            label="Xã/Phường"
            required={required}
            value={form.getFieldValue("wardId")}
            onChange={handleChangeWard}
            disabled={!wards.length || disabled}
            placeholder="Nhập tên Xã/Phường"
            options={wards.map((item) => ({
              label: item.nameWithType,
              value: item.id,
            }))}
            allowClear
            showSearch
            filterOption={(input, option) =>
              option?.label.toLowerCase().includes(input.toLowerCase())
            }
          />
        </Col>
      </>
    );
  }
);
