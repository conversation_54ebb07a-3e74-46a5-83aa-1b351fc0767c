import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const documentApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/document",
      params,
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/document/${id}`,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/document",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/document/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/document/${id}`,
      method: "delete",
    }),
};
