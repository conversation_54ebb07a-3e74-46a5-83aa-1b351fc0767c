import { Company } from "types/company";
import { MemberShip } from "types/memberShip";
import { Role } from "types/role";

const getCompanyOfProject = (memberShip: MemberShip[]): Company[] => {
    const companies = memberShip
        .map((member) => member.company)
        .filter((company): company is Company => company != null); // Filter out null/undefined companies
    
    // Remove duplicates by company id
    const uniqueCompaniesMap = new Map<number, Company>();
    companies.forEach((company) => {
        if (company.id && !uniqueCompaniesMap.has(company.id)) {
            uniqueCompaniesMap.set(company.id, company);
        }
    });
    
    return Array.from(uniqueCompaniesMap.values());
}

const getRoleOfProject = (memberShip: MemberShip[]): { label: string; value: number|string }[] => {
    const roles = memberShip
        .map((member) => member.role)
        .filter((role): role is Role => role != null); // Filter out null/undefined roles

    // Remove duplicates by role id
    const uniqueRole = roles.filter((role, index) => roles.findIndex((r) => r.id === role.id) === index);
    // Remove duplicates
    const roleForSelect = [
        {
            label: "Tất cả vai trò",
            value: "all"
        },
        ...uniqueRole.map((role) => ({
            label: `${role.id} - ${role.name}`,
            value: role.id
        }))
    ]

    return roleForSelect;
};

export { getCompanyOfProject, getRoleOfProject };