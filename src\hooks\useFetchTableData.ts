import { debounce } from "lodash";
import React, { useEffect, useRef, useState } from "react";
import { QueryParam } from "types/query";

interface Props<S> {
  initQuery: S;
  queryFunc: (query: S) => Promise<{ data: any; total: number }>;
  deleteFunc?: (id: number) => Promise<any>;
  createFunc?: (data: any) => Promise<any>;
  editFunc?: (id: number, data: any) => Promise<any>;
}

export function useFetchTableData<T = any, S = QueryParam>({
  initQuery,
  queryFunc,
  deleteFunc,
  createFunc,
  editFunc,
}: Props<S>) {
  const query = useRef<S>({ ...initQuery });
  const [data, setData] = useState<T[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);

  const fetchData = async (newQuery?: Partial<S>) => {
    try {
      setLoading(true);
      const queryParams = { ...query.current, ...newQuery };
      const res = await queryFunc(queryParams);
      query.current = queryParams;
      setData(res.data);
      setTotal(res.total);
      return res.data;
    } finally {
      setLoading(false);
    }
  };

  const deleteData = async (id: number) => {
    try {
      setLoading(true);
      await deleteFunc?.(id);
    } finally {
      fetchData();
      setLoading(false);
    }
  };

  const createData = async (dataCreate?: any) => {
    try {
      setLoading(true);
      await createFunc?.(dataCreate);
    } finally {
      setLoading(false);
    }
  };

  const editData = async (id: number, dataEdit?: any) => {
    try {
      setLoading(true);
      await editFunc?.(id, dataEdit);
    } finally {
      setLoading(false);
    }
  };

  const filterData = debounce((newQuery: Partial<S>) => {
    // Object.assign(query, { page: 1, ...newQuery });
    //@ts-ignore
    query.current = { page: 1, ...newQuery };
    fetchData();
  }, 200);

  return {
    data,
    total,
    loading,
    setLoading,
    query: query.current,
    fetchData,
    deleteData,
    createData,
    editData,
    setData,
    filterData,
  };
}
