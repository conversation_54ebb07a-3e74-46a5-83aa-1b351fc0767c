import {
  Card,
  Col,
  Form,
  Input,
  message,
  Row,
  Space,
  Spin,
  DatePicker,
  Select,
  Button,
} from "antd";
import { Rule } from "antd/lib/form";
import { projectApi } from "api/project.api";
import {
  ProjectGroupSelector,
  ProjectGroupSelectorRef,
} from "components/Selector/ProjectGroupSelector";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import { useEffect, useMemo, useRef, useState } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { FileAttach } from "types/fileAttach";
import { getTitle } from "utils";
import { useWatch } from "antd/es/form/Form";
import { Project, ProjectStatusTrans } from "types/project";
import CustomButton from "components/Button/CustomButton";
import PageTitle from "components/PageTitle/PageTitle";
import ActiveStatusTagSelect from "components/ActiveStatus/ActiveStatusTagSelect";
import { ModalStatus } from "types/modal";
import { get, isEmpty } from "lodash";
import { PermissionNames } from "types/PermissionNames";
import clsx from "clsx";
import { InputNumber } from "components/Input/InputNumber";
import TextArea from "antd/es/input/TextArea";
import { TextInput } from "components/Input/TextInput";
import { observer } from "mobx-react";
import { checkRoles } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import dayjs from "dayjs";
import { GoogleLocationModal } from "components/GoogleLocationModal/GoogleLocationModal";
import MapWithAutocomplete from "components/Map/MapWithAutocomplete";
import { CoordAddress } from "types/address";
import { settings } from "settings";
import { CompanySelector } from "components/Selector/CompanySelector";
import { CompanyModal } from "views/CompanyPage/components/CompanyModal";
import {
  ProjectGroupModal,
  ProjectGroupModalRef,
} from "views/ProjectGroup/components/Modal/ProjectGroupModal";
import { PlusOutlined } from "@ant-design/icons";
import { BMDTextArea } from "components/TextArea/BMDTextArea";

const rules: Rule[] = [{ required: true }];

interface EditProjectPageProps {
  title: string;
  status: ModalStatus;
  viewContent?: boolean;
  onSubmitSuccess?: () => void;
}

interface ProjectForm extends Omit<Project, "startAt" | "endAt"> {
  projectGroupId: number;
  // avatar?: string; // project image
  licenseNumber?: string; // số giấy phép
  totalAcr?: number; // tổng diện tích sàn
  startAt?: any; // ngày khởi công
  endAt?: any; // ngày dự kiến hoàn thành
  constructionSite?: string; // địa điểm thi công
  timeLapseCameraUrl?: string; // site camera time lapse
  vr360Url?: string; // VR 360 URL
  // Google Maps fields
  lat?: number;
  long?: number;
  placeId?: string;
  mapUrl?: string;
  addressName?: string;
  investorId?: number;
}

export const CreateOrUpdateProjectPage = observer(
  ({
    title = "",
    status,
    viewContent = false,
    onSubmitSuccess,
  }: EditProjectPageProps) => {
    const { haveEditPermission } = checkRoles(
      {
        edit: PermissionNames.projectEditStatus,
      },
      permissionStore.permissions
    );

    const [form] = Form.useForm<ProjectForm>();
    const [loading, setLoading] = useState(false);
    const navigate = useNavigate();

    const avatar = useWatch("avatar", form);
    const [searchParams, setSearchParams] = useSearchParams();
    const [selectedProject, setSelectedProject] = useState<Project>();
    const [readonly, setReadonly] = useState(viewContent);
    const [loadingFetch, setLoadingFetch] = useState(false);
    const params = useParams();

    // Google Maps states
    const [chooseMapVisible, setChooseMapVisible] = useState(false);
    const [placeInfo, setPlaceInfo] = useState<CoordAddress>();

    const companyModalRef = useRef<CompanyModal>(null);
    const companySelectorRef = useRef<CompanySelector>(null);
    const projectGroupModalRef = useRef<ProjectGroupModalRef>(null);
    const projectGroupSelectorRef = useRef<ProjectGroupSelectorRef>(null);
    useEffect(() => {
      document.title = getTitle(title);
    }, [title]);

    const setDataToForm = (data: Project) => {
      form.setFieldsValue({
        ...data,
        projectGroupId: data.projectCategory?.id, // Using projectCategory as projectGroup
        startAt: data.startAt ? dayjs.unix(data.startAt) : undefined,
        endAt: data.endAt ? dayjs.unix(data.endAt) : undefined,
        investorId: data.investor?.id,
        // Add other fields as needed
      });

      // Set place info if available
      if (data.location) {
        setPlaceInfo({
          address: data.location,
          lat: (data as any).lat || 0,
          lng: (data as any).long || 0,
          placeId: (data as any).placeId || "",
          name: (data as any).location || "",
          mapUrl: (data as any).mapUrl || "",
        });
      }
    };

    const getOneProject = async (id: number) => {
      try {
        setLoadingFetch(true);
        const { data } = await projectApi.findOne(id);

        if (isEmpty(data)) {
          navigate("/404");
          return;
        }

        setSelectedProject(data);
        setDataToForm(data);

        return data as Project;
      } catch (e: any) {
        console.error(e);
      } finally {
        setLoadingFetch(false);
      }
    };

    useEffect(() => {
      if (viewContent) {
        setReadonly(true);
        const projectId = params.id;
        if (projectId) {
          getOneProject(+projectId);
        }
        return;
      }
      if (status === "update") {
        const projectId = params.id;
        if (projectId) {
          getOneProject(+projectId);
          setReadonly(searchParams.get("update") !== "1");
        }
      } else {
        setReadonly(false);
      }
    }, [status, params.id, searchParams, viewContent]);

    const getDataSubmit = async () => {
      const {
        projectGroupId,
        startAt,
        endAt,
        timeLapseCameraUrl,
        vr360Url,
        lat,
        long,
        placeId,
        mapUrl,
        addressName,
        investorId,
        ...data
      } = form.getFieldsValue(true);

      const payload = {
        project: {
          ...data,
          startAt: startAt ? startAt.startOf("day").unix() : 0,
          endAt: endAt ? endAt.startOf("day").unix() : 0,
          // Google Maps fields
          lat: placeInfo?.lat || lat,
          long: placeInfo?.lng || long,
          placeId: placeInfo?.placeId || placeId,
          mapUrl: placeInfo?.mapUrl || mapUrl,
          addressName: placeInfo?.name || addressName,
          // Add other custom fields as needed
          isActive: selectedProject?.isActive,
        },
        projectCategoryId: projectGroupId, // Mapping to projectCategoryId
        investorId: investorId || 0,
        // Add other IDs as needed based on API structure
      };

      return payload;
    };

    const createData = async () => {
      try {
        await form.validateFields();
        setLoading(true);

        const res = await projectApi.create(await getDataSubmit());
        message.success("Tạo dự án thành công!");
        navigate(`/project/${PermissionNames.projectList}`);
      } catch (error) {
        console.error(error);
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      try {
        await form.validateFields();
        setLoading(true);

        const payload = await getDataSubmit();
        const res = await projectApi.update(selectedProject!?.id || 0, payload);
        message.success("Chỉnh sửa dự án thành công!");
        onSubmitSuccess?.();

        // Fetch lại dữ liệu để cập nhật form
        // await getOneProject(selectedProject!?.id || 0);
        setReadonly(true); // Chuyển về chế độ readonly sau khi lưu
      } catch (error) {
        console.error(error);
      } finally {
        setLoading(false);
      }
    };

    const handleSubmit = () => {
      if (status === "create") {
        createData();
      } else {
        updateData();
      }
    };

    const handleMapChoosen = async (addressInfo: CoordAddress) => {
      try {
        form.setFieldsValue({
          location: addressInfo.address,
          lat: addressInfo.lat,
          long: addressInfo.lng,
          placeId: addressInfo.placeId,
        });

        setPlaceInfo(addressInfo);
        setChooseMapVisible(false);
      } catch (error) {
        console.log({ error });
      }
    };

    const pageTitle = useMemo(
      () => (status === "create" ? "Tạo dự án" : "Chỉnh sửa dự án"),
      [status]
    );

    return (
      <div className="app-container">
        {!viewContent && (
          <PageTitle
            back
            breadcrumbs={[
              { label: "Dữ liệu nguồn" },
              {
                label: "Danh sách dự án",
                href: `/project/${PermissionNames.projectList}`,
              },
              { label: pageTitle },
            ]}
            title={pageTitle}
            extra={
              selectedProject &&
              status === "update" && (
                <Space>
                  <ActiveStatusTagSelect
                    disabled={readonly}
                    isActive={selectedProject?.isActive}
                    onChange={(value) => {
                      setSelectedProject({
                        ...selectedProject,
                        isActive: value,
                      } as Project);
                    }}
                  />
                </Space>
              )
            }
          />
        )}
        <Card>
          <Spin spinning={loadingFetch}>
            <Form
              layout="vertical"
              form={form}
              className={clsx(readonly ? "readonly" : "")}
              disabled={readonly}
            >
              <Form.Item name="isActive" hidden />
              <Form.Item name="lat" hidden />
              <Form.Item name="long" hidden />
              <Form.Item name="placeId" hidden />
              <Form.Item name="mapUrl" hidden />
              <Form.Item name="addressName" hidden />

              <div style={{ display: "flex" }}>
                {/* Upload avatar/image */}
                <Form.Item
                  style={{
                    marginBottom: 0,
                    marginRight: 20,
                  }}
                  label={""}
                  name="avatar"
                  className="form-height-full"
                >
                  <SingleImageUpload
                    onUploadOk={(file: FileAttach) => {
                      form.setFieldsValue({
                        avatar: file.path || "",
                      });
                    }}
                    imageUrl={avatar || ""}
                    height={"100%"}
                    width={"100%"}
                    className="h-full upload-avatar"
                    hideUploadButton={readonly}
                    disabled={readonly}
                  />
                </Form.Item>

                <div className="project-info" style={{ flex: 1 }}>
                  {/* Row 1: Mã dự án, Tên dự án, Nhóm dự án */}
                  <Row gutter={12}>
                    <Col span={8}>
                      <Form.Item label="Mã dự án" name="code">
                        <TextInput
                          disabled={status === "update"}
                          placeholder={
                            status === "create" ? "Để trống sẽ tự sinh" : ""
                          }
                        />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label="Tên dự án" name="name" rules={rules}>
                        <Input placeholder="Nhập tên dự án" />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        label={
                          <div className="flex items-center justify-between w-full">
                            <span className="leading-none">
                              Nhóm dự án{" "}
                              <span className="text-red-500 text-xs">*</span>
                            </span>
                            {!readonly && (
                              <Button
                                type="primary"
                                size="small"
                                icon={
                                  <PlusOutlined className="translate-x-[0.5px] translate-y-[-2px]" />
                                }
                                className="!h-[20px] !min-w-0 !py-0 flex items-center"
                                onClick={() => {
                                  projectGroupModalRef.current?.handleCreate();
                                }}
                              />
                            )}
                          </div>
                        }
                        name="projectGroupId"
                        className="custom-label-no-required"
                        rules={rules}
                        required={false}
                      >
                        <ProjectGroupSelector
                          placeholder="Chọn nhóm dự án"
                          initQuery={{ isActive: true }}
                          ref={projectGroupSelectorRef}
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  {/* Row 2: Ngân sách dự kiến, Chủ đầu tư, Số giấy phép */}
                  <Row gutter={12}>
                    <Col span={8}>
                      <Form.Item
                        label="Ngân sách dự kiến"
                        name="budget"
                        rules={rules}
                      >
                        <InputNumber
                          suffix="VNĐ"
                          placeholder="Nhập ngân sách"
                        />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        label={
                          <div className="flex items-center justify-between w-full">
                            <span className="leading-none">
                              Chủ đầu tư{" "}
                              <span className="text-red-500 text-xs">*</span>
                            </span>
                            {!readonly && (
                              <Button
                                type="primary"
                                size="small"
                                icon={
                                  <PlusOutlined className="translate-x-[0.5px] translate-y-[-2px]" />
                                }
                                className="!h-[20px] !min-w-0 !py-0 flex items-center"
                                onClick={() => {
                                  companyModalRef.current?.handleCreate(
                                    getOneProject,
                                    params.id
                                  );
                                }}
                              />
                            )}
                          </div>
                        }
                        name="investorId"
                        className="custom-label-no-required"
                        rules={rules}
                        required={false}
                      >
                        <CompanySelector
                          placeholder="Nhập tên chủ đầu tư"
                          initOptionItem={selectedProject?.investor}
                          ref={companySelectorRef}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        label="Số giấy phép"
                        name="licenseNumber"
                        rules={rules}
                      >
                        <Input placeholder="Nhập số giấy phép" />
                      </Form.Item>
                    </Col>
                  </Row>

                  {/* Row 3: Tổng diện tích sàn, Ngày khởi công, Ngày dự kiến hoàn thành */}
                  <Row gutter={16}>
                    <Col span={8}>
                      <Form.Item
                        label="Tổng diện tích sàn"
                        name="totalAcr"
                        rules={rules}
                      >
                        <InputNumber suffix="m²" placeholder="Nhập diện tích" />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        label="Ngày khởi công"
                        name="startAt"
                        rules={rules}
                      >
                        <DatePicker
                          style={{ width: "100%" }}
                          placeholder="Chọn ngày khởi công"
                          format={settings.dateFormat}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        label="Ngày dự kiến hoàn thành"
                        name="endAt"
                        dependencies={["startAt"]}
                        rules={[
                          {
                            required: true,
                            message: "Vui lòng chọn ngày hoàn thành",
                          },
                          ({ getFieldValue }) => ({
                            validator(_, value) {
                              const start = getFieldValue("startAt");
                              if (!start || !value) return Promise.resolve();

                              const startDate = dayjs(start).startOf("day");
                              const endDate = dayjs(value).startOf("day");

                              if (endDate.isAfter(startDate, "day"))
                                return Promise.resolve();
                              return Promise.reject(
                                new Error(
                                  "Ngày hoàn thành phải lớn hơn hoặc bằng ngày khởi công"
                                )
                              );
                            },
                          }),
                        ]}
                      >
                        <DatePicker
                          style={{ width: "100%" }}
                          placeholder="Chọn ngày hoàn thành"
                          format={settings.dateFormat}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={8} className="mt-5">
                      <Form.Item label="Tình trạng" name="status" rules={rules}>
                        <Select
                          disabled={readonly}
                          placeholder="Chọn tình trạng"
                          options={Object.entries(ProjectStatusTrans).map(
                            ([key, value]) => ({
                              value: key,
                              label: value.label,
                            })
                          )}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </div>
              </div>

              {/* Địa điểm thi công */}
              <Row gutter={16}>
                <Col span={24}>
                  <Form.Item
                    label="Địa điểm thi công"
                    name="location"
                    rules={[
                      {
                        required: true,
                        message: "Vui lòng chọn địa điểm thi công",
                      },
                    ]}
                  >
                    <div>
                      {placeInfo?.lat && placeInfo?.lng && (
                        <div className="my-2">
                          <MapWithAutocomplete
                            coords={[
                              { lat: placeInfo.lat, lng: placeInfo.lng },
                            ]}
                            draggable={false}
                            defaultZoom={15}
                            noInput
                          />
                        </div>
                      )}
                      <CustomButton
                        variant="outline"
                        className="w-full mt-2"
                        onClick={() => setChooseMapVisible(true)}
                        disabled={readonly}
                      >
                        {placeInfo?.placeId
                          ? "Thay đổi địa điểm"
                          : "Chọn địa điểm thi công"}
                      </CustomButton>
                      {placeInfo?.address && (
                        <div className="mt-2 p-2 bg-gray-50 rounded">
                          <div className="font-medium">{placeInfo.name}</div>
                          <div className="text-sm text-gray-600">
                            {placeInfo.address}
                          </div>
                        </div>
                      )}
                    </div>
                  </Form.Item>
                </Col>
              </Row>

              {/* Mô tả dự án */}
              <Row gutter={16}>
                <Col span={24}>
                  <Form.Item label="Mô tả dự án" name="description">
                    <BMDTextArea placeholder="Nhập mô tả dự án" />
                  </Form.Item>
                </Col>
              </Row>

              {/* Site camera time lapse và VR 360 */}
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item label="Site camera time lapse" name="siteCamera">
                    <Input placeholder="Nhập URL camera time lapse" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="VR 360" name="vr360">
                    <Input placeholder="Nhập URL VR 360" />
                  </Form.Item>
                </Col>
              </Row>
            </Form>

            <div className="flex gap-[16px] justify-end mt-2">
              {!readonly && (
                <CustomButton
                  variant="outline"
                  className="cta-button"
                  onClick={() => {
                    if (status === "create") {
                      navigate(`/project/${PermissionNames.projectList}`);
                    } else {
                      setReadonly(true);
                      setDataToForm(selectedProject!);
                    }
                  }}
                >
                  Hủy
                </CustomButton>
              )}

              <CustomButton
                className="cta-button"
                loading={loading}
                onClick={() => {
                  if (!readonly) {
                    handleSubmit();
                  } else {
                    setReadonly(false);
                  }
                }}
                disabled={status === "update" && !haveEditPermission}
              >
                {status === "create"
                  ? "Tạo dự án"
                  : readonly
                  ? "Chỉnh sửa"
                  : "Lưu chỉnh sửa"}
              </CustomButton>
            </div>
          </Spin>
        </Card>
        {chooseMapVisible && (
          <GoogleLocationModal
            coord={{
              address: placeInfo?.address || "",
              lat: placeInfo?.lat || 0,
              lng: placeInfo?.lng || 0,
              placeId: placeInfo?.placeId || "",
            }}
            onClose={() => setChooseMapVisible(false)}
            onSubmitOk={handleMapChoosen}
          />
        )}
        <CompanyModal
          onClose={() => {}}
          onSubmitOk={(data) => {
            companySelectorRef.current?.refresh();
            form.setFieldsValue({
              investorId: data.id,
            });
          }}
          ref={companyModalRef}
        />
        <ProjectGroupModal
          onClose={() => {}}
          onSubmitOk={() => {
            projectGroupSelectorRef.current?.refresh();
            // Có thể thêm logic để set giá trị mới tạo vào form nếu cần
          }}
          ref={projectGroupModalRef}
        />
      </div>
    );
  }
);

export default CreateOrUpdateProjectPage;
