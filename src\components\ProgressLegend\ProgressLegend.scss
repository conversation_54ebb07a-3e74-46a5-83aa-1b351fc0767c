.status-column {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: center;
  gap: 4px;

  &__undefined {
    color: #666;
    font-size: 12px;
  }

  &__info {
    display: flex;
    align-items: flex-start;
    gap: 0px;
    flex-direction: column;
  }

  &__indicator {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    margin-top: 4px;
  }

  &__label {
    font-size: 13px;
    color: var(--color-neutral-n8);
    font-weight: 300;
  }

  &__progress {
    width: 96px;
    display: flex;
    gap: 4px;
  }

  &__progress-step {
    flex: 1;
    height: 6px;
    transition: background-color 0.2s ease;

    &--active {
      background-color: var(--color-task-normal);
    }

    &--inactive {
      background-color: var(--color-neutral-n3);
    }
  }
}
