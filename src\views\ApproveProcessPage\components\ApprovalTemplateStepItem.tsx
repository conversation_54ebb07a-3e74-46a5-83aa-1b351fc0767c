import {
  But<PERSON>,
  ColorPicker,
  Form,
  FormListFieldData,
  Input,
  Select,
  Space,
} from "antd";
import useFormInstance from "antd/es/form/hooks/useFormInstance";
import DeleteIcon from "assets/svgs/DeleteIcon";
import { PlusIcon } from "assets/svgs/PlusIcon";
import { MembershipSelector } from "components/Selector/MembershipSelector";
import { StepType, StepTypeTrans } from "types/approvalStep";
import { TemplateCompact } from "./EditApprovalTemplateModal";
import { useMemo, useState } from "react";
import { RoleSelector } from "components/Selector/RoleSelector";
import CustomButton from "components/Button/CustomButton";
import { useWatch } from "antd/es/form/Form";
import { ApprovalTemplateMode } from "types/approvalTemplate";
import { MemberShip } from "types/memberShip";
import { Role } from "types/role";

interface Props {
  field: FormListFieldData;
  index: number;
  isEditMode: boolean;
  fields: FormListFieldData[];
  indexStartShowDeleteStep?: number; // Chỉ dùng cho bước đầu tiên, để hiển thị nút xóa
  // localType: TemplateCompact;
  onRemove?: (name: number) => void;
}

export interface Approver {
  id?: number;
  memberShipId?: number;
  memberShip?: MemberShip;
  roleId?: number;
  role?: Role;
}

const ApprovalTemplateStepItem = ({
  field,
  fields,
  index,
  isEditMode,
  // localType,
  indexStartShowDeleteStep = 1,
  onRemove,
}: Props) => {
  const form = useFormInstance();

  const mode = useWatch("mode", form);

  const stepName = form.getFieldValue(["steps", field.name, "name"]);
  const approvers: Approver[] = useWatch(
    ["steps", field.name, "approvers"],
    form
  );

  const approversRender = useMemo(() => {
    return (
      approvers &&
      approvers.length > 0 && (
        <>
          <div className="flex gap-2 w-full font-bold mb-[-4px]">
            <div className="w-[200px]">Vai trò</div>
            <div className="">
              Nhân viên <span className="text-red-500 font-semibold">*</span>
            </div>
          </div>
          {approvers.map((approver, index) => {
            return (
              <div key={index} className="flex gap-2 w-full">
                <RoleSelector
                  value={approver.roleId}
                  key={index}
                  selectProps={{
                    className: "w-[200px] flex-shrink-0",
                  }}
                  placeholder="Chọn vai trò"
                  disabled={!isEditMode}
                  valueIsOption
                  onChange={(role) => {
                    const updatedApprovers = [...approvers];
                    updatedApprovers[index].roleId = role.id;
                    updatedApprovers[index].role = role;
                    updatedApprovers[index].memberShipId = undefined;
                    form.setFieldValue(
                      ["steps", field.name, "approvers"],
                      updatedApprovers
                    );
                  }}
                />
                <MembershipSelector
                  value={approver.memberShipId}
                  key={index}
                  disabled={!isEditMode}
                  placeholder="Chọn nhân viên"
                  query={{ roleId: approver.roleId }}
                  valueIsOption
                  onChange={(memberShip) => {
                    const updatedApprovalTemplateDetails = [...approvers];
                    updatedApprovalTemplateDetails[index].memberShip =
                      memberShip;
                    updatedApprovalTemplateDetails[index].memberShipId =
                      memberShip.id;
                    form.setFieldValue(
                      ["steps", field.name, "approvers"],
                      updatedApprovalTemplateDetails
                    );
                  }}
                />
                {index > 0 && (
                  <Button
                    type="text"
                    danger
                    disabled={!isEditMode}
                    icon={<DeleteIcon />}
                    onClick={() => {
                      const updatedApproverList = [...approvers];
                      updatedApproverList.splice(index, 1);
                      form.setFieldValue(
                        ["steps", field.name, "approvers"],
                        updatedApproverList
                      );
                    }}
                  />
                )}
              </div>
            );
          })}
        </>
      )
    );
  }, [approvers, isEditMode, form]);

  return (
    <div key={field.key} className="approve-process-container">
      <div className="process-icon">
        <span>{index + 1}</span>
      </div>
      <div className="process-content">
        <div
          className="process-content-header"
          style={{
            display: "flex",
            justifyContent: "space-between",
          }}
        >
          <div className="flex gap-2 flex-wrap">
            {StepTypeTrans[stepName as StepType] ? (
              <span className="title self-start">
                {StepTypeTrans[stepName as StepType]}
              </span>
            ) : (
              <>
                <Form.Item
                  label="Bước"
                  name={[field.name, "name"]}
                  rules={[
                    {
                      required: true,
                      message: "Nhập tên bước duyệt",
                    },
                  ]}
                  style={{ marginBottom: 0 }}
                >
                  <Input
                    className="w-[200px]"
                    placeholder="Nhập tên bước duyệt"
                    disabled={!isEditMode}
                  />
                </Form.Item>
                <Form.Item
                  label="Thao tác"
                  name={[field.name, "actionText"]}
                  rules={[
                    {
                      required: true,
                      message: "Nhập thao tác",
                    },
                  ]}
                  style={{ marginBottom: 0 }}
                >
                  <Input
                    className="w-[200px]"
                    placeholder="Nhập thao tác"
                    disabled={!isEditMode}
                  />
                </Form.Item>
                <Form.Item
                  label="Trạng thái"
                  name={[field.name, "statusText"]}
                  rules={[
                    {
                      required: true,
                      message: "Nhập trạng thái",
                    },
                  ]}
                  style={{ marginBottom: 0 }}
                >
                  <Input
                    className="w-[200px]"
                    placeholder="Nhập trạng thái"
                    disabled={!isEditMode}
                  />
                </Form.Item>
                <Form.Item name={[field.name, "statusColor"]} label="Màu">
                  <ColorPicker
                    value={form.getFieldValue([
                      "steps",
                      field.name,
                      "statusColor",
                    ])}
                    className="translate-y-[1px]"
                    format="hex"
                    onChange={(value, css) => {
                      console.log({ value });
                      form.setFieldValue(
                        ["steps", field.name, "statusColor"],
                        value
                      );
                    }}
                  />
                </Form.Item>
                <Form.Item
                  label="Cách duyệt"
                  name={[field.name, "isAllApproval"]}
                  rules={[
                    {
                      required: true,
                      message: "Chọn cách duyệt",
                    },
                  ]}
                  style={{ marginBottom: 0 }}
                >
                  <Select
                    className="!w-[200px]"
                    disabled={!isEditMode}
                    options={[
                      { label: "Một người duyệt", value: false },
                      { label: "Tất cả duyệt", value: true },
                    ]}
                  />
                </Form.Item>
                <CustomButton
                  // icon={<PlusIcon size={20} fill="#1677ff" />}
                  disabled={!isEditMode}
                  className="translate-y-[25px]"
                  onClick={() => {
                    const updatedApprovers = [...approvers];
                    updatedApprovers.push({
                      memberShipId: undefined,
                      roleId: undefined,
                    });
                    form.setFieldValue(
                      ["steps", field.name, "approvers"],
                      updatedApprovers
                    );
                  }}
                >
                  Thêm nhân viên
                </CustomButton>
              </>
            )}
          </div>
          {mode == ApprovalTemplateMode.Complex &&
            index > indexStartShowDeleteStep &&
            isEditMode && (
              <Button
                type="text"
                className="self-end"
                icon={<DeleteIcon />}
                onClick={() => onRemove?.(field.name)}
              />
            )}
        </div>
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "flex-start",
            marginBottom: "4px",
            marginTop: "6px",
            width: "100%",
          }}
        >
          <div className="approve-process-form-row">
            <div className="approve-process-staff-row">
              <Form.Item hidden name={"approvers"} />
              {approversRender}

              {/* <Form.Item
                name={[field.name, "memberShipId"]}
                // Chỉ required nếu KHÔNG phải bước "Tạo mới"
                className="approve-process-staff1"
              >
                <MembershipSelector
                  // disabled={!isEditMode}

                  disabled={stepName === StepType.Create || !isEditMode}

                  // valueIsOption
                />
              </Form.Item>
              {localType === "complex" && index == 1 ? (
                <Button
                  type="text"
                  icon={<PlusIcon size={20} fill="#1677ff" />}
                  onClick={() => {
                    form.setFieldValue(
                      ["steps", field.name, "memberShip2Id"],
                      null
                    );
                    form.setFieldsValue({
                      steps: [...form.getFieldValue("steps")],
                    });
                  }}
                />
              ) : (
                <span className="approve-process-placeholder" />
              )} */}
            </div>
            {/* {form.getFieldValue(["steps", field.name, "memberShip2Id"]) !==
              undefined && (
              <div className="approve-process-staff-row">
                <Form.Item
                  name={[field.name, "memberShip2Id"]}
                  className="approve-process-staff2"
                >
                  <MembershipSelector
                    disabled={!isEditMode}
                    //   valueIsOption
                    placeholder="Chọn nhân viên 2"
                  />
                </Form.Item>
                <Button
                  type="text"
                  danger
                  icon={<DeleteIcon />}
                  onClick={() => {
                    form.setFieldValue(
                      ["steps", field.name, "memberShip2Id"],
                      undefined
                    );
                    form.setFieldsValue({
                      steps: [...form.getFieldValue("steps")],
                    });
                  }}
                />
              </div>
            )} */}
          </div>
        </div>
      </div>
      <div className="line" />
    </div>
  );
};

export default ApprovalTemplateStepItem;
