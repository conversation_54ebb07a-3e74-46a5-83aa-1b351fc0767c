import { Image, Popconfirm, Spin, Tooltip } from "antd";
import React, { useState } from "react";
import { FiDownload } from "react-icons/fi";
import { IoTrashOutline } from "react-icons/io5";
import { downloadFile } from "utils/downloadFile";
import FileIcon from "./RenderIconFile";

interface FileCustomProps {
  id?: number;
  fileName: string;
  fileSize: number;
  fileUrl: string;
  fileType?: string;
}

interface FileItemProps {
  file: FileCustomProps;
  loadingDelete?: boolean;
  onDelete: (id?: number) => void;
  loading?: number;
  hiddenDelete?: boolean;
}

const FileItem: React.FC<FileItemProps> = ({
  file,
  onDelete,
  loadingDelete,
  loading = false,
  hiddenDelete = false,
}) => {
  const [loadingDownload, setLoadingDownload] = useState(false);
  console.log(file);
  return (
    <div
      className={`px-3 py-2 mb-4 bg-slate-100 rounded-lg ${
        loading ? "opacity-50" : ""
      }`}
    >
      <div className="flex items-center gap-3">
        <div className="">
          <FileIcon mimeType={file.fileType} url={file.fileUrl} />
        </div>

        <div className="flex flex-col gap-1 flex-1">
          <Tooltip title={file.fileName}>
            <span className="text-sm font-bold line-clamp-1 break-all">
              {file.fileName}
            </span>
          </Tooltip>
          <span className="text-xs text-gray">
            {(file.fileSize / 1024 / 1024).toFixed(2)} MB
          </span>
          {!loading && (
            <div className="flex items-center justify-between">
              <Spin spinning={loadingDownload}>
                <span
                  onClick={() =>
                    downloadFile(
                      file.fileUrl,
                      file.fileName,
                      setLoadingDownload
                    )
                  }
                  className="cursor-pointer text-[18px] flex items-center gap-1 justify-between"
                >
                  <div className="">
                    <FiDownload className="text-primary text-sm" />
                  </div>
                  <span className="text-primary text-sm font-medium">
                    Tải file
                  </span>
                </span>
              </Spin>
              {!hiddenDelete && (
                <Popconfirm
                  placement="topLeft"
                  title={`Xác nhận xóa file này?`}
                  onConfirm={() => onDelete(file.id)}
                  okText="Xóa"
                  cancelText="Không"
                  okButtonProps={{ loading: loadingDelete }}
                >
                  <IoTrashOutline className="cursor-pointer text-red-500 text-base"></IoTrashOutline>
                </Popconfirm>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FileItem;
