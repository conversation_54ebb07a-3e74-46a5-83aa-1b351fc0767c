import { unitApi } from "api/unit.api";
import { useMemo, useState } from "react";
import { Unit } from "types/unit";
import { QueryParam } from "types/query";

export interface UnitQuery extends QueryParam {}

interface UseUnitProps {
  initQuery: UnitQuery;
}

export const useUnit = ({ initQuery }: UseUnitProps) => {
  const [data, setData] = useState<Unit[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<UnitQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const isEmptyQuery = useMemo(
    () =>
      Object.keys(query).filter(
        (k) => query[k] && !["limit", "page", "queryObject"].includes(k)
      ).length == 0,
    [query]
  );

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await unitApi.findAll(query);

      setData(data.units);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return { units: data, total, fetchData, loading, setQuery, query, isEmptyQuery };
};
