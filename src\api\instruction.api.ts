import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const instructionApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/instruction",
      params,
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/instruction/${id}`,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/instruction",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/instruction/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/instruction/${id}`,
      method: "delete",
    }),
  approve: (id: number, data?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/instruction/${id}/approve`,
      method: "patch",
      data,
    }),
  reject: (id: number, data?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/instruction/${id}/reject`,
      method: "patch",
      data,
    }),
};
