import { Input, InputProps, InputRef } from "antd";
import clsx from "clsx";
import React, { useEffect, useRef, useState } from "react";
import { formatVND } from "utils";

interface InputNumber extends InputProps {}

export const InputNumber = ({ suffix, ...props }: InputNumber) => {
  const [currentValue, setCurrentValue] = useState("");
  const prevValue = useRef("");
  const inputRef = useRef<InputRef>(null);
  const [isDisabled, setIsDisabled] = useState(false);

  useEffect(() => {
    setIsDisabled(
      !!inputRef.current?.nativeElement?.classList.contains(
        "ant-input-disabled"
      )
    );
  });

  const handleChange = (ev: React.ChangeEvent<HTMLInputElement>) => {
    const rawInput = ev.currentTarget.value;

    // Thay dấu chấm phân ngàn để xử lý
    const normalized = rawInput.replaceAll(".", "").replace(",", ".");

    const reg = /^\d*\.?\d*$/; // Cho phép số thực

    if (reg.test(normalized)) {
      prevValue.current = rawInput;
      setCurrentValue(formatVND(normalized));
      props.onChange?.({
        ...ev,
        currentTarget: {
          ...ev.currentTarget,
          value: normalized, // truyền dạng chuẩn (JS hiểu được)
        },
        target: {
          ...ev.target,
          value: normalized,
        },
      });
    } else {
      setCurrentValue(prevValue.current);
    }
  };

  useEffect(() => {
    const val = props.value;
    if (
      val === 0 ||
      (val &&
        (typeof val === "string" || typeof val === "number") &&
        !isNaN(Number(val)))
    ) {
      setCurrentValue(formatVND(val.toString()));
    } else {
      setCurrentValue("");
    }
  }, [props.value]);

  return (
    <Input
      {...props}
      ref={inputRef}
      suffix={isDisabled ? null : suffix}
      onChange={handleChange}
      value={isDisabled ? currentValue + ` ${suffix || ""} ` : currentValue}
      className={clsx(
        props.className,
        !!props.value && "has-value",
        "input-number"
      )}
    ></Input>
  );
};
