import { componentApi } from "./../api/component.api";
import { useState } from "react";
import { Component } from "types/component";
import { QueryParam } from "types/query";
export interface ComponentApiQuery extends QueryParam {}

interface UseComponentApiProps {
  initQuery: ComponentApiQuery;
}

export const useComponent = ({ initQuery }: UseComponentApiProps) => {
  const [data, setData] = useState<Component[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<ComponentApiQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await componentApi.findAll(query);

      setData(data.components);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return { components: data, total, fetchData, loading, setQuery, query,setData };
};
