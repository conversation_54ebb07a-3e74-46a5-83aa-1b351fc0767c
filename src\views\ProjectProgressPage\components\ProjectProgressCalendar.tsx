import {
  Calendar,
  dateFnsLocalizer,
  momentLocalizer,
  Views,
} from "react-big-calendar";
import withDragAndDrop from "react-big-calendar/lib/addons/dragAndDrop";
import "react-big-calendar/lib/addons/dragAndDrop/styles.css";
import "react-big-calendar/lib/css/react-big-calendar.css";
import moment from "moment";
import { useCallback, useEffect, useState, useRef } from "react";
import { useSchedule } from "hooks/useSchedule";
import { Modal, message } from "antd";
import dayjs, { Dayjs } from "dayjs";
import { TaskModal, TaskModalRef } from "./TaskModal";
import CustomInput from "components/Input/CustomInput";
import CustomButton from "components/Button/CustomButton";
import { DatePicker, Select } from "antd";
import QueryLabel from "components/QueryLabel/QueryLabel";
import { MembershipSelector } from "components/Selector/MembershipSelector";
import { scheduleApi } from "api/schedule.api";
import { ScheduleModal, ScheduleModalRef } from "./ScheduleModal";
import "../styles/CalendarComponentStyle.scss";

const DnDCalendar = withDragAndDrop(Calendar);

moment.updateLocale("en", {
  week: {
    dow: 1, // 0 = Sunday, 1 = Monday
  },
});

const localizer = momentLocalizer(moment);

interface CalendarEvent {
  id: number;
  title: string;
  start: Date;
  end: Date;
  allDay: boolean;
  resource?: any;
}

// Thêm hàm tính số ngày làm việc (bỏ T7, CN)
function getWorkingDays(start: Date, end: Date): number {
  let count = 0;
  let current = dayjs(start);
  while (current.isBefore(end, "day") || current.isSame(end, "day")) {
    const day = current.day(); // 0 = Sunday, 6 = Saturday
    if (day !== 0 && day !== 6) {
      count++;
    }
    current = current.add(1, "day");
  }
  return count;
}

export default function ProjectProgressCalendar() {
  const [myEvents, setMyEvents] = useState<CalendarEvent[]>([]);
  const [search, setSearch] = useState("");
  const [filteredEvents, setFilteredEvents] = useState<CalendarEvent[]>([]);
  const scheduleModalRef = useRef<ScheduleModalRef>();
  const [assigneeFilter, setAssigneeFilter] = useState<number | undefined>(
    undefined
  );
  const [fromDate, setFromDate] = useState<Dayjs | null>(null);
  const [toDate, setToDate] = useState<Dayjs | null>(null);
  const [filterQuery, setFilterQuery] = useState<any>({});

  const { schedules, fetchScheduleFlat, loadingSchedule } = useSchedule({
    initQuery: {
      limit: 50,
      page: 1,
    },
  });

  useEffect(() => {
    fetchScheduleFlat();
  }, []);

  useEffect(() => {
    const transformedEvents = schedules.map((schedule) => ({
      id: schedule.id,
      title: schedule.TaskName,
      start: dayjs(schedule.StartDate).toDate(),
      end: dayjs(schedule.EndDate).toDate(),
      allDay: false,
      resource: schedule, // Lưu schedule data để click hiển thị chi tiết
      color: "#0d6efd",
    }));
    setMyEvents(transformedEvents);
    setFilteredEvents(transformedEvents); // default: show all
  }, [schedules]);

  const dayPropGetter = (date: Date) => {
    const day = dayjs(date).day(); // 0 = Sunday, 6 = Saturday

    if (day === 0 || day === 6) {
      return {
        className: "weekend-column",
      };
    }

    return {};
  };

  const handleApplyFilter = () => {
    const query: any = {
      limit: 50,
      page: 1,
    };
    if (search) query.search = search;
    if (assigneeFilter) query.resourceIds = assigneeFilter;
    if (fromDate) query.fromDate = fromDate.format("YYYY-MM-DD");
    if (toDate) query.toDate = toDate.format("YYYY-MM-DD");

    setFilterQuery(query);
    fetchScheduleFlat(query);
  };

  const handleClearFilter = () => {
    setSearch("");
    setAssigneeFilter(undefined);
    setFromDate(null);
    setToDate(null);

    const query = {
      limit: 50,
      page: 1,
    };
    setFilterQuery(query);
    fetchScheduleFlat(query);
  };

  const moveEvent = useCallback(
    ({ event, start, end, isAllDay: droppedOnAllDaySlot = false }: any) => {
      Modal.confirm({
        title: "Xác nhận thay đổi",
        content: `Bạn có chắc muốn di chuyển task "${event.title}" không?`,
        onOk: async () => {
          try {
            const startDate = dayjs(start).format("YYYY-MM-DD");
            const endDate = dayjs(end).format("YYYY-MM-DD");
            const duration = getWorkingDays(start, end);
            await scheduleApi.update(event.id, {
              schedule: {
                StartDate: startDate,
                EndDate: endDate,
                Duration: duration,
              },
            });
            const updatedEvent = {
              ...event,
              start,
              end,
              allDay: droppedOnAllDaySlot,
            };
            setMyEvents((prev) =>
              prev.map((ev) => (ev.id === event.id ? updatedEvent : ev))
            );
            setFilteredEvents((prev) =>
              prev.map((ev) => (ev.id === event.id ? updatedEvent : ev))
            );
            message.success("Cập nhật thời gian task thành công!");
          } catch (error) {
            console.error("Error updating schedule:", error);
          }
        },
      });
    },
    []
  );

  const resizeEvent = useCallback(({ event, start, end }: any) => {
    Modal.confirm({
      title: "Xác nhận thay đổi",
      content: `Bạn có chắc muốn thay đổi thời gian cho task "${event.title}" không?`,
      onOk: async () => {
        try {
          const startDate = dayjs(start).format("YYYY-MM-DD");
          const endDate = dayjs(end).format("YYYY-MM-DD");
          const duration = getWorkingDays(start, end);
          await scheduleApi.update(event.id, {
            schedule: {
              StartDate: startDate,
              EndDate: endDate,
              Duration: duration,
            },
          });
          const updatedEvent = {
            ...event,
            start,
            end,
          };
          setMyEvents((prev) =>
            prev.map((ev) => (ev.id === event.id ? updatedEvent : ev))
          );
          setFilteredEvents((prev) =>
            prev.map((ev) => (ev.id === event.id ? updatedEvent : ev))
          );
          message.success("Cập nhật thời gian task thành công!");
        } catch (error) {
          console.error("Error updating schedule:", error);
        }
      },
    });
  }, []);

  // Click vào task để hiển thị chi tiết
  const handleSelectEvent = async (event: any) => {
    const schedule = event.resource;
    try {
      // Vì scheduleApi không có method findOne, ta sẽ sử dụng dữ liệu đã có
      scheduleModalRef.current?.handleUpdate(schedule);
    } catch (error) {
      console.error("Error fetching schedule detail:", error);
    }
  };

  // Click vào slot trống để mở modal tạo task mới
  const handleSelectSlot = useCallback(({ start, end }: any) => {
    // const adjustedEnd = dayjs(end).subtract(1, "day").toDate();
    scheduleModalRef.current?.handleCreate(start, end);
  }, []);

  return (
    <>
      <div className="flex flex-wrap gap-[16px] items-end pb-[12px]">
        <div className="w-[300px]">
          <CustomInput
            label="Tìm kiếm"
            placeholder="Tìm theo tên task"
            value={search}
            onChange={setSearch}
            onPressEnter={handleApplyFilter}
            allowClear
          />
        </div>
        <div className="w-[250px]">
          <QueryLabel>Người phụ trách</QueryLabel>
          <MembershipSelector
            value={assigneeFilter}
            onChange={setAssigneeFilter}
            allowClear={true}
            placeholder="Chọn người phụ trách"
          />
        </div>
        <div className="w-[180px]">
          <QueryLabel>Từ ngày</QueryLabel>
          <DatePicker
            className="w-full"
            placeholder="Từ ngày"
            value={fromDate}
            onChange={setFromDate}
            format="DD/MM/YYYY"
            allowClear
          />
        </div>
        <div className="w-[180px]">
          <QueryLabel>Đến ngày</QueryLabel>
          <DatePicker
            className="w-full"
            placeholder="Đến ngày"
            value={toDate}
            onChange={setToDate}
            format="DD/MM/YYYY"
            allowClear
          />
        </div>
        <CustomButton onClick={handleApplyFilter}>Áp dụng</CustomButton>
        {(search || assigneeFilter || fromDate || toDate) && (
          <CustomButton variant="outline" onClick={handleClearFilter}>
            Bỏ lọc
          </CustomButton>
        )}
      </div>
      <DnDCalendar
        localizer={localizer}
        defaultDate={new Date()}
        defaultView={Views.MONTH}
        events={myEvents}
        onEventDrop={moveEvent}
        onEventResize={resizeEvent}
        onSelectEvent={handleSelectEvent}
        onSelectSlot={handleSelectSlot}
        eventPropGetter={(event: any) => ({
          style: {
            backgroundColor: event.color || "#3b82f6",
            borderRadius: "6px",
            color: "#fff",
            border: "none",
            padding: "4px 8px",
          },
        })}
        style={{ height: 1000 }}
        popup
        resizable
        selectable
        dayPropGetter={dayPropGetter}
      />

      <ScheduleModal
        ref={scheduleModalRef}
        onClose={() => {}}
        onSubmitOk={() => {
          fetchScheduleFlat(); // Refresh data sau khi tạo/cập nhật task
        }}
      />
    </>
  );
}
