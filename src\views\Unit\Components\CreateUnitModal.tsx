import {
  Button,
  Checkbox,
  Col,
  Form,
  Image,
  Input,
  message,
  Modal,
  Row,
  Select,
  Tag,
  Upload,
  UploadProps,
} from "antd";

import React, { useEffect, useImperativeHandle, useState } from "react";
import { ModalStatus } from "types/modal";
import { useWatch } from "antd/lib/form/Form";
import { requiredRule } from "utils/validateRule";
import { useComponent } from "hooks/useComponent";
import { useMaterial } from "hooks/useMaterial";
import { variantApi } from "api/variant.api";
import { Unit } from "types/unit";
import { unitApi } from "api/unit.api";

export interface UnitModal {
  handleCreate: () => void;
  handleUpdate: (unit: Unit) => void;
}
interface UnitModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
}
export const CreateUnitModal = React.forwardRef(
  ({ onClose, onSubmitOk }: UnitModalProps, ref) => {
    const [form] = Form.useForm<Unit>();

    const materialType = useWatch("materialType", form);
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");
    useImperativeHandle<any, UnitModal>(
      ref,
      () => ({
        handleCreate() {
          form.resetFields();
          setVisible(true);
          setStatus("create");
        },
        handleUpdate(unit: Unit) {
          form.setFieldsValue({
            ...unit,
          });

          setVisible(true);
          setStatus("update");
        },
      }),
      []
    );
    const createData = async () => {
      const data = form.getFieldsValue();
      setLoading(true);
      try {
        const res = await unitApi.create({
          unit: { ...data },
        });
        message.success("Tạo thành công");
        handleClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const valid = await form.validateFields();
      const data = form.getFieldsValue();
      setLoading(true);
      console.log("What is in form", data);
      const { id, ...restData } = data;
      console.log("Rest data là", restData);
      try {
        console.log("Data when update", data);
        const res = await unitApi.update(id, {
          unit: { ...data },
        });
        message.success("Cập nhật thành công");
        handleClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    const handleClose = () => {
      onClose();
      setVisible(false);
      form.resetFields();
    };
    const {
      components,
      fetchData,
      query,
      loading: componentLoading,
      setQuery,
      total,
    } = useComponent({
      initQuery: {
        page: 1,
        limit: 10,
      },
    });
    const {
      fetchData: fetchMaterials,
      loading: loadingMaterials,
      materials,
      query: queryMaterial,
      setQuery: setQueryMaterial,
    } = useMaterial({
      initQuery: {
        page: 1,
        limit: 10,
      },
    });
    useEffect(() => {
      fetchData();
      fetchMaterials();
    }, []);
    useEffect(() => {
      queryMaterial.type = materialType;
      fetchMaterials();
    }, [materialType]);
    return (
      <Modal
        onCancel={() => {
          onClose?.();
          setVisible(false);
        }}
        open={visible}
        title={status == "create" ? "Tạo đơn vị" : "Cập nhật đơn vị"}
        style={{ top: 20 }}
        confirmLoading={loading}
        onOk={() => {
          status == "create" ? createData() : updateData();
        }}
      >
        <Form layout="vertical" form={form}>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item label="Tên đơn vị" name="name" rules={[requiredRule]}>
                <Input placeholder="" />
              </Form.Item>
            </Col>
            {[
              status === "update" && (
                <Col span={8}>
                  <Form.Item
                    className="hidden"
                    label="id"
                    name="id"
                    rules={[requiredRule]}
                  >
                    <Input placeholder="" />
                  </Form.Item>
                </Col>
              ),
            ]}
          </Row>
        </Form>
      </Modal>
    );
  }
);
