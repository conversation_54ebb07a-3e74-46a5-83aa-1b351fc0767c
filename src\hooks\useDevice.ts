import { deviceApi } from "api/device.api";
import { useMemo, useState } from "react";
import { Device } from "types/device";
import { QueryParam } from "types/query";

export interface DeviceQuery extends QueryParam {}

interface UseDeviceProps {
  initQuery: DeviceQuery;
}

export const useDevice = ({ initQuery }: UseDeviceProps) => {
  const [data, setData] = useState<Device[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<DeviceQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const isEmptyQuery = useMemo(
    () =>
      Object.keys(query).filter(
        (k) =>
          (query[k] != undefined || query[k] != null) &&
          !["limit", "page", "queryObject", "type"].includes(k)
      ).length == 0,
    [query]
  );

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await deviceApi.findAll(query);

      setData(data.devices);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return {
    devices: data,
    total,
    fetchData,
    loading,
    setQuery,
    query,
    isEmptyQuery,
  };
};
