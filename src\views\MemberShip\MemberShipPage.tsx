import { Card, Button, Select, Form, Space } from "antd";
import { ArrowDownIcon } from "assets/svgs/ArrowDownIcon";
import CustomButton from "components/Button/CustomButton";
import CustomInput from "components/Input/CustomInput";
import PageTitle from "components/PageTitle/PageTitle";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import { useTheme } from "context/ThemeContext";
import React, { useEffect, useState, useMemo, useCallback } from "react";
import { QueryParam } from "types/query";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { getTitle } from "utils";
import QueryLabel from "components/QueryLabel/QueryLabel";
import { DictionaryType } from "types/dictionary";
import { DictionarySelector } from "components/Selector/DictionarySelector";
import { MemberSubTable } from "./components/MemberSubTable";
import "./styles/MemberShipPage.scss";
import { useMemberShip, MemberShipQuery } from "hooks/useMemberShip";
import { MemberShip, MemberShipType } from "types/memberShip";
import { Company } from "types/company";
import { PermissionNames } from "types/PermissionNames";
import { memberShipApi } from "api/memberShip.api";
import { isEmpty, max } from "lodash";
import { ModalStatus } from "types/modal";
import { useTransition } from "hooks/useTransition";
import {
  checkRole,
  checkRoles,
  filterActionColumnIfNoPermission,
} from "utils/auth";
import {
  getCompanyOfProject,
  getRoleOfProject,
} from "utils/getCompanyOfProject";
import { permissionStore } from "store/permissionStore";
import { observer } from "mobx-react";
import { message } from "antd";
import { appStore } from "store/appStore";

interface MemberShipPageProps {
  title: string;
  projectId?: number;
  hidePageTitle?: boolean;
}

interface UserData {
  id: string;
  code: string; // Add code field
  companyCode: string;
  fullName: string;
  position: string;
  role: string;
  email: string;
  status: string;
  isActive: boolean;
  company?: string;
  phone: string;
  // Additional metadata for copy functionality
  memberShipCategoryId?: number;
  memberShipCategoryName?: string;
  jobTitleId?: number;
  jobTitleName?: string;
  companyId?: number;
  companyName?: string;
  roleId?: number;
  roleName?: string;
  staffId?: number;
  staffCode?: string;
  staffName?: string;
}

interface CategoryGroup {
  categoryName: string;
  categoryId: string;
  users: UserData[];
}

interface MemberShipForm extends MemberShip {
  companyId: number;
  staffId: number;
  roleId: number;
  jobTitleId: number;
  memberShipCategoryId: number;
}

function MemberShipPage({
  title,
  projectId,
  hidePageTitle = false,
}: MemberShipPageProps) {
  const {
    haveAddPermission,
    haveBlockPermission,
    haveEditPermission,
    haveViewAllPermission,
  } = checkRoles(
    {
      add: PermissionNames.projectPhoneBookAdd,
      edit: PermissionNames.projectPhoneBookEdit,
      block: PermissionNames.projectPhoneBookBlock,
      viewAll: PermissionNames.projectPhoneBookViewAll,
    },
    permissionStore.permissions
  );

  const [form] = Form.useForm<MemberShipForm>();
  const [searchParams] = useSearchParams();
  const [selectedMemberShip, setSelectedMemberShip] = useState<MemberShip>();
  const { isLoaded } = useTransition();

  // Store original companies list to prevent it from changing when filtering
  const [originalCompanies, setOriginalCompanies] = useState<Company[]>([]);
  const [originalRoles, setOriginalRoles] = useState<
    { label: string; value: number | string }[]
  >([]);
  const [hasInitializedCompanies, setHasInitializedCompanies] = useState(false);

  // Get projectId from appStore.currentProject first, then fallback to props or URL
  const projectIdFromQuery = searchParams.get("projectId");
  const effectiveProjectId =
    appStore.currentProject?.id ||
    projectId ||
    (projectIdFromQuery ? Number(projectIdFromQuery) : undefined);

  const {
    memberShips,
    total,
    fetchData,
    loading,
    query,
    setQuery,
    isEmptyQuery,
  } = useMemberShip({
    initQuery: {
      page: 1,
      limit: 0, // Set limit to 0 to get all records
      search: "",
      projectId: effectiveProjectId, // Use effective projectId from appStore
      isAdmin: haveViewAllPermission ? true : undefined,
    },
  });

  console.log("Membership: ", memberShips);

  // Update original companies list when membership data is first loaded
  useEffect(() => {
    if (memberShips.length > 0 && !hasInitializedCompanies) {
      const companies = getCompanyOfProject(memberShips);
      const roles = getRoleOfProject(memberShips);
      setOriginalRoles(roles);
      setOriginalCompanies(companies);
      setHasInitializedCompanies(true);
    }
  }, [memberShips, hasInitializedCompanies]);

  const { darkMode } = useTheme();
  const navigate = useNavigate();
  const [loadingFetch, setLoadingFetch] = useState(false);
  const params = useParams();
  const [readonly, setReadonly] = useState(true);
  // State for expanded rows (auto expand)
  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);

  // Update the columns to reflect CategoryGroup structure
  const getMainTableColumns = (): CustomizableColumn<CategoryGroup>[] => [
    {
      key: "categoryName",
      title: "Mã",
      dataIndex: "categoryName",
      width: 100,
      defaultVisible: true,
      alwaysVisible: true,
      render: (text, record) => {
        if (record.users && record.users.length > 0) {
          return {
            children: (
              <div className="flex items-center gap-2">
                <span className="font-medium">{text}</span>
              </div>
            ),
            props: {
              colSpan: 3,
            },
          };
        }
        return <span className="font-medium">{text}</span>;
      },
    },
    {
      key: "companyCodeHeader",
      title: "Mã công ty",
      width: 120,
      defaultVisible: true,
      alwaysVisible: true,
      render: (_, record) => ({
        props: {
          colSpan: record.users?.length > 0 ? 0 : 1,
        },
      }),
    },
    {
      key: "fullNameHeader",
      title: "Họ và tên",
      width: 150,
      defaultVisible: true,
      alwaysVisible: true,
      render: (_, record) => ({
        props: {
          colSpan: record.users?.length > 0 ? 0 : 1,
        },
      }),
    },
    {
      key: "companyHeader",
      title: "Công ty",
      width: 150,
      defaultVisible: true,
      alwaysVisible: true,
      render: () => null,
    },
    {
      key: "roleHeader",
      title: "Vai trò",
      width: 100,
      defaultVisible: true,
      alwaysVisible: true,
      render: () => null,
    },
    {
      key: "emailHeader",
      title: "Địa chỉ email",
      width: 200,
      defaultVisible: true,
      alwaysVisible: true,
      render: () => null,
    },
    {
      key: "phoneHeader",
      title: "Số điện thoại",
      width: 120,
      defaultVisible: true,
      alwaysVisible: true,
      render: () => null,
    },
    {
      key: "statusHeader",
      title: "Trạng thái",
      align: "center",
      width: 150,
      defaultVisible: true,
      alwaysVisible: true,
      render: () => null,
    },
    {
      key: "actions",
      title: "Xử lý",
      align: "center",
      width: 150,
      defaultVisible: true,
      alwaysVisible: true,
      render: () => null,
    },
  ];

  useEffect(() => {
    document.title = getTitle(title);
  }, []);

  useEffect(() => {
    if (isLoaded) {
      fetchData();
    }
  }, [isLoaded]);

  // Transform memberShips data to grouped by memberShipCategory using useMemo
  const categoryData = useMemo(() => {
    const transformMemberShipsToCategoryGroups = (
      memberShips: MemberShip[]
    ): CategoryGroup[] => {
      const groupedByCategory = memberShips.reduce((acc, memberShip) => {
        const categoryId =
          memberShip.memberShipCategory?.id?.toString() || "unknown";
        const categoryName =
          memberShip.memberShipCategory?.name || "Không xác định";

        if (!acc[categoryId]) {
          acc[categoryId] = {
            categoryName,
            categoryId,
            users: [],
          };
        }

        acc[categoryId].users.push({
          id: memberShip.id.toString(),
          code: memberShip.code || `MB${memberShip.id}`, // Use code, fallback to generated code
          companyCode: memberShip.company?.code || "N/A",
          fullName: memberShip.staff?.fullName || memberShip.name || "",
          position: memberShip.jobTitle?.name || "",
          role: memberShip.role?.name || "",
          email: memberShip.email,
          phone: memberShip.phone || "",
          status: memberShip.isActive ? "Hoạt động" : "Tạm khóa",
          isActive: memberShip.isActive,
          company: memberShip.company?.name || "Unknown Company",
          // Add additional metadata for copy functionality
          memberShipCategoryId: memberShip.memberShipCategory?.id,
          memberShipCategoryName: memberShip.memberShipCategory?.name,
          jobTitleId: memberShip.jobTitle?.id,
          jobTitleName: memberShip.jobTitle?.name,
          companyId: memberShip.company?.id,
          companyName: memberShip.company?.name,
          roleId: memberShip.role?.id,
          roleName: memberShip.role?.name,
          staffId: memberShip.staff?.id,
          staffCode: memberShip.staff?.code,
          staffName: memberShip.staff?.fullName,
        });

        return acc;
      }, {} as Record<string, CategoryGroup>);

      return Object.values(groupedByCategory);
    };

    return transformMemberShipsToCategoryGroups(memberShips);
  }, [memberShips]);

  // Auto expand all rows when data changes
  useEffect(() => {
    if (categoryData.length > 0) {
      const allKeys = categoryData.map((group) => group.categoryId);
      setExpandedRowKeys((prevKeys) => {
        // Only update if keys actually changed to prevent unnecessary re-renders
        if (
          JSON.stringify(prevKeys.sort()) !== JSON.stringify(allKeys.sort())
        ) {
          return allKeys;
        }
        return prevKeys;
      });
    }
  }, [categoryData]);

  const handleCreateMember = useCallback(() => {
    // Check if we're in ProjectDetailPage context
    const isInProjectDetail = !hidePageTitle; // hidePageTitle = true means we're in ProjectDetailPage

    if (effectiveProjectId) {
      navigate(
        `/report/${PermissionNames.projectPhoneBookAdd}?projectId=${effectiveProjectId}`,
        {
          state: {
            projectId: effectiveProjectId,
            fromProjectDetail: isInProjectDetail,
            fromReportSection: !isInProjectDetail,
          },
        }
      );
    } else {
      navigate(`/report/${PermissionNames.projectPhoneBookAdd}`, {
        state: {
          fromReportSection: true,
        },
      });
    }
  }, [effectiveProjectId, navigate, hidePageTitle]);

  // Update query when projectId changes from appStore
  useEffect(() => {
    if (isLoaded && effectiveProjectId !== query.projectId) {
      setOriginalCompanies([]);
      setOriginalRoles([]);
      setHasInitializedCompanies(false);
      setQuery((prevQuery) => ({
        ...prevQuery,
        projectId: effectiveProjectId,
      }));
    }
  }, [effectiveProjectId, isLoaded, query.projectId]);

  const handleActiveMemberShip = useCallback(
    async (id: number, isActive: boolean) => {
      try {
        await memberShipApi.isActive(id, !isActive);
        message.success(!isActive ? "Mở khóa thành công" : "Khóa thành công");
        fetchData();
      } catch (error) {
        message.error("Có lỗi xảy ra");
      }
    },
    [fetchData]
  );

  const expandedRowRender = useCallback(
    (record: CategoryGroup) => {
      return (
        <MemberSubTable
          users={record.users}
          projectId={effectiveProjectId}
          onToggleActive={handleActiveMemberShip}
          haveAddPermission={haveAddPermission}
        />
      );
    },
    [effectiveProjectId, handleActiveMemberShip, haveAddPermission]
  );

  const columns = useMemo(() => getMainTableColumns(), []);

  // Transform companies to options cho Select - use original companies list
  const companyOptions = useMemo(
    () => [
      {
        label: "Tất cả công ty",
        value: undefined,
      },
      ...originalCompanies.map((company) => ({
        label: `${company.code} - ${company.name}`,
        value: company.id,
      })),
    ],
    [originalCompanies]
  );

  return (
    <div>
      {!hidePageTitle && (
        <PageTitle
          title={title}
          breadcrumbs={["Báo cáo", title]}
          extra={
            <Space>
              {haveAddPermission && (
                <CustomButton
                  size="small"
                  showPlusIcon
                  onClick={handleCreateMember}
                >
                  Tạo danh bạ
                </CustomButton>
              )}
            </Space>
          }
        />
      )}

      <div className={hidePageTitle ? "" : "app-container"}>
        <Card>
          <div className="flex gap-[16px] items-end pb-[12px] justify-between flex-wrap">
            <div className="flex gap-[16px] items-end">
              <div className="w-[1/8]">
                <CustomInput
                  tooltipContent={
                    "Tìm kiếm theo tên danh bạ, mã, công ty, vai trò"
                  }
                  label="Tìm kiếm"
                  placeholder="Tìm kiếm"
                  onPressEnter={() => {
                    setQuery({ ...query });
                    fetchData();
                  }}
                  value={query.search}
                  onChange={(value) => {
                    query.search = value;
                    setQuery({ ...query });

                    if (!value) {
                      fetchData();
                    }
                  }}
                  allowClear
                />
              </div>
              <div>
                <QueryLabel>Loại danh bạ</QueryLabel>
                <DictionarySelector
                  placeholder="Chọn loại danh bạ"
                  allowClear={true}
                  initQuery={{
                    type: DictionaryType.MemberShipCategory,
                    isActive: true,
                  }}
                  addonOptions={[
                    {
                      id: "",
                      name: "Tất cả loại danh bạ",
                    },
                  ]}
                  value={query.memberShipCategoryId ?? undefined}
                  onChange={(value) => {
                    query.memberShipCategoryId = value || undefined;
                    setQuery({ ...query });
                  }}
                />
              </div>
              <div>
                <QueryLabel>Công ty</QueryLabel>
                <Select
                  placeholder="Chọn công ty"
                  allowClear={true}
                  options={companyOptions}
                  value={query.companyId || undefined}
                  onChange={(value) => {
                    query.companyId = value || undefined;
                    setQuery({ ...query });
                  }}
                  style={{
                    width: 200,
                    minWidth: 200,
                  }}
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? "")
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                />
              </div>

              <div className="!cursor-pointer">
                <QueryLabel>Vai trò</QueryLabel>
                <Select
                  placeholder="Chọn vai trò"
                  allowClear={true}
                  options={originalRoles}
                  value={query.roleId ?? undefined}
                  onChange={(value) => {
                    if (value === "all") {
                      query.roleId = undefined;
                    } else {
                      query.roleId = value;
                    }
                    setQuery({ ...query });

                    // blur sau khi đã chọn
                    setTimeout(() => {
                      if (document.activeElement instanceof HTMLElement) {
                        document.activeElement.blur();
                      }
                    }, 0);
                  }}
                  style={{
                    width: 200,
                    minWidth: 200,
                  }}
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? "")
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                />
              </div>

              <CustomButton
                onClick={() => {
                  if (!query.memberShipCategoryId)
                    delete query.memberShipCategoryId;
                  if (!query.companyId) delete query.companyId;
                  setQuery({ ...query });
                  fetchData();
                }}
              >
                Áp dụng
              </CustomButton>

              {!isEmptyQuery && (
                <CustomButton
                  variant="outline"
                  onClick={() => {
                    delete query.memberShipCategoryId;
                    delete query.companyId;
                    delete query.search;
                    delete query.queryObject;
                    setQuery({ ...query });
                    fetchData();
                  }}
                >
                  Bỏ lọc
                </CustomButton>
              )}
            </div>

            {hidePageTitle && (
              <CustomButton
                size="small"
                showPlusIcon
                onClick={handleCreateMember}
              >
                Tạo danh bạ
              </CustomButton>
            )}
          </div>

          <CustomizableTable
            columns={filterActionColumnIfNoPermission(columns, [
              haveEditPermission,
              haveBlockPermission,
            ])}
            dataSource={categoryData}
            rowKey="categoryId"
            loading={loading}
            pagination={false}
            scroll={{ x: 1200 }}
            bordered
            expandable={{
              expandedRowRender,
              expandedRowKeys: expandedRowKeys,
              onExpandedRowsChange: (keys) =>
                setExpandedRowKeys(keys as string[]),
              expandIcon: ({ expanded, onExpand, record }) => (
                <Button
                  type="text"
                  size="small"
                  icon={
                    <ArrowDownIcon
                      className={`transition-transform duration-200 ${
                        expanded ? "rotate-0" : "-rotate-90"
                      }`}
                      fill={darkMode ? "#ffffff" : "#6b7280"}
                    />
                  }
                  onClick={(e) => onExpand(record, e)}
                  className="!border-0 !shadow-none hover:!bg-gray-100"
                />
              ),
              expandIconColumnIndex: 0,
              rowExpandable: (record) =>
                record.users && record.users.length > 0,
            }}
            className="expandable-membership-table"
            tableId="membership-page"
          />
        </Card>
      </div>
    </div>
  );
}

export default observer(MemberShipPage);
