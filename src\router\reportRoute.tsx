import { PermissionType } from "types/permission";
import { ReactComponent as ReportIcon } from "assets/svgs/report.svg";
import { PermissionNames } from "types/PermissionNames";
import { lazy } from "react";
import { Route } from "./RouteType";

const MemberShipPage = lazy(() => import("views/MemberShip/MemberShipPage"));
const CreateOrUpdateMemberShipPage = lazy(
  () => import("views/MemberShip/CreateOrUpdateMemberShipPage")
);
const IndicativePage = lazy(
  () => import("views/IndicativePage/IndicativePage")
);
const CreateOrUpdateIndicativePage = lazy(
  () => import("views/IndicativePage/CreateOrUpdateIndicativePage")
);
const RFIsPage = lazy(() => import("views/RFIs/RFIsPage"));
const CreateOrUpdateRFIsPage = lazy(
  () => import("views/RFIs/CreateOrUpdateRFIsPage")
);
const CreateOrUpdateReportProjectPage = lazy(
  () => import("views/ReportProject/CreateOrUpdateReportProjectPage")
);
const ProjectItemPage = lazy(() => import("views/ProjectItem/ProjectItemPage"));
const CreateOrUpdateProjectItemPage = lazy(
  () => import("views/ProjectItem/CreateOrUpdateProjectItemPage")
);
const ApproveProcessPage = lazy(
  () => import("views/ApproveProcessPage/ApproveProcessPage")
);

const ReportProjectPage = lazy(() =>
  import("views/ReportProject/ReportProjectPage").then((m) => ({
    default: m.ReportProjectPage,
  }))
);
const EventLogPage = lazy(() =>
  import("views/EventLogPage/EventLogPage").then((m) => ({
    default: m.EventLogPage,
  }))
);
const CreateOrUpdateEventLogPage = lazy(() =>
  import("views/EventLogPage/CreateOrUpdateEventLogPage").then((m) => ({
    default: m.CreateOrUpdateEventLogPage,
  }))
);

export const reportRoutes: Route[] = [
  {
    title: "Báo cáo",
    breadcrumb: "report",
    path: "/report",
    name: "report",
    aliasPath: "/report",
    icon: <ReportIcon />,
    permissionTypes: [
      PermissionType.Add,
      PermissionType.Delete,
      PermissionType.Edit,
      PermissionType.List,
    ],
    needProject: true,
    children: [
      {
        title: "Xem tất cả",
        path: PermissionNames.projectPhoneBookViewAll,
        name: PermissionNames.projectPhoneBookViewAll,
        aliasPath: `/report/${PermissionNames.projectPhoneBookViewAll}`,
        hidden: true,
      },
      {
        title: "Danh bạ dự án",
        breadcrumb: "Danh bạ dự án",
        path: PermissionNames.projectPhoneBookList,
        name: PermissionNames.projectPhoneBookList,
        aliasPath: `/report/${PermissionNames.projectPhoneBookList}`,
        element: <MemberShipPage title="Danh bạ dự án" />,
        permissionTypes: [PermissionType.List],

        // icon: <TbPackages />,
      },
      {
        title: "Tạo danh bạ",
        breadcrumb: "Tạo danh bạ",
        path: PermissionNames.projectPhoneBookAdd,
        name: PermissionNames.projectPhoneBookAdd,
        aliasPath: `/report/${PermissionNames.projectPhoneBookAdd}`,
        element: (
          <CreateOrUpdateMemberShipPage title="Tạo danh bạ" status="create" />
        ),
        permissionTypes: [PermissionType.Add],
        hidden: true,
        isPublic: true,
      },
      {
        title: "Khóa danh bạ",
        breadcrumb: "Khóa danh bạ",
        path: PermissionNames.projectPhoneBookBlock,
        name: PermissionNames.projectPhoneBookBlock,
        aliasPath: `/report/${PermissionNames.projectPhoneBookBlock}`,
        hidden: true,
      },
      {
        title: "Chỉnh sửa danh bạ",
        path: PermissionNames.projectPhoneBookEdit,
        name: PermissionNames.projectPhoneBookEdit,
        aliasPath: `/report/${PermissionNames.projectPhoneBookEdit.replace(
          "/:id",
          ""
        )}`,
        element: (
          <CreateOrUpdateMemberShipPage
            title="Chỉnh sửa danh bạ"
            status="update"
          />
        ),
        permissionTypes: [PermissionType.Edit],
        hidden: true,
      },
      {
        title: "Chỉ thị công trường",
        breadcrumb: "Chỉ thị công trường",
        path: PermissionNames.indicativeList,
        name: PermissionNames.indicativeList,
        aliasPath: `/report/${PermissionNames.indicativeList}`,
        element: <IndicativePage title="Chỉ thị công trường" />,
        permissionTypes: [PermissionType.List],
        // icon: <TbPackages />,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.indicativeViewAll,
        name: PermissionNames.indicativeViewAll,
        aliasPath: `/report/${PermissionNames.indicativeViewAll}`,
        hidden: true,
      },
      {
        title: "Tạo chỉ thị công trường",
        breadcrumb: "Tạo chỉ thị công trường",
        path: PermissionNames.indicativeAdd,
        name: PermissionNames.indicativeAdd,
        aliasPath: `/report/${PermissionNames.indicativeAdd}`,
        element: (
          <CreateOrUpdateIndicativePage
            title="Tạo chỉ thị công trường"
            status="create"
          />
        ),
        permissionTypes: [PermissionType.Add],
        hidden: true,
        isPublic: true,
        // icon: <TbPackages />,
      },
      {
        title: "Chỉnh sửa chỉ thị công trường",
        breadcrumb: "Chỉnh sửa chỉ thị công trường",
        path: PermissionNames.indicativeEdit,
        name: PermissionNames.indicativeEdit,
        aliasPath: `/report/${PermissionNames.indicativeEdit.replace(
          "/:id",
          ""
        )}`,
        element: (
          <CreateOrUpdateIndicativePage
            title="Chỉnh sửa chỉ thị công trường"
            status="update"
          />
        ),
        permissionTypes: [PermissionType.Edit],
        hidden: true,
        isPublic: true,
      },
      {
        title: "Khóa chỉ thị công trường",
        breadcrumb: "Khóa chỉ thị công trường",
        path: PermissionNames.indicativeBlock,
        name: PermissionNames.indicativeBlock,
        aliasPath: `/report/${PermissionNames.indicativeBlock}`,
        hidden: true,
      },
      {
        title: "RFIs",
        breadcrumb: "RFIs",
        path: PermissionNames.rfisList,
        name: PermissionNames.rfisList,
        aliasPath: `/report/${PermissionNames.rfisList}`,
        element: <RFIsPage title="RFIs" />,
        permissionTypes: [PermissionType.List],

        // icon: <TbPackages />,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.rfisViewAll,
        name: PermissionNames.rfisViewAll,
        aliasPath: `/report/${PermissionNames.rfisViewAll}`,
        hidden: true,
      },
      {
        title: "Tạo RFIs",
        breadcrumb: "Tạo RFIs",
        path: PermissionNames.rfisAdd,
        name: PermissionNames.rfisAdd,
        aliasPath: `/report/${PermissionNames.rfisAdd}`,
        element: <CreateOrUpdateRFIsPage title="Tạo RFIs" status="create" />,
        permissionTypes: [PermissionType.Add],
        hidden: true,
        isPublic: true,
        // icon: <TbPackages />,
      },
      {
        title: "Chỉnh sửa RFIs",
        breadcrumb: "Chỉnh sửa RFIs",
        path: PermissionNames.rfisEdit,
        name: PermissionNames.rfisEdit,
        aliasPath: `/report/${PermissionNames.rfisEdit.replace("/:id", "")}`,
        element: (
          <CreateOrUpdateRFIsPage title="Chỉnh sửa RFIs" status="update" />
        ),
        permissionTypes: [PermissionType.Edit],
        hidden: true,
        isPublic: true,
      },
      {
        title: "Xóa RFIs",
        breadcrumb: "Xóa RFIs",
        path: PermissionNames.rfisDelete,
        name: PermissionNames.rfisDelete,
        aliasPath: `/report/${PermissionNames.rfisDelete}`,
        hidden: true,
      },

      {
        title: "Tạo báo cáo",
        path: PermissionNames.projectReportAdd,
        name: PermissionNames.projectReportAdd,
        aliasPath: `/report/${PermissionNames.projectReportAdd}`,
        element: (
          <CreateOrUpdateReportProjectPage
            title="Tạo báo cáo"
            status="create"
          />
        ),
        permissionTypes: [PermissionType.Add],
        hidden: true,
        isPublic: true,
      },
      {
        title: "Chỉnh sửa báo cáo",
        path: PermissionNames.projectReportEdit,
        name: PermissionNames.projectReportEdit,
        aliasPath: `/report/${PermissionNames.projectReportEdit.replace(
          "/:id",
          ""
        )}`,
        element: (
          <CreateOrUpdateReportProjectPage
            title="Chỉnh sửa báo cáo"
            status="update"
          />
        ),
        permissionTypes: [PermissionType.Edit],
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.projectReportViewAll,
        name: PermissionNames.projectReportViewAll,
        aliasPath: `/report/${PermissionNames.projectReportViewAll}`,
        hidden: true,
      },
      {
        title: "Báo cáo dự án",
        breadcrumb: "Báo cáo dự án",
        path: PermissionNames.projectReportList,
        name: PermissionNames.projectReportList,
        aliasPath: `/report/${PermissionNames.projectReportList}`,
        element: <ReportProjectPage title="Báo cáo dự án" />,
        permissionTypes: [PermissionType.List],

        // icon: <TbPackages />,
      },
      {
        title: "Sự kiện thay đổi",
        breadcrumb: "Sự kiện thay đổi",
        path: PermissionNames.eventLogList,
        name: PermissionNames.eventLogList,
        aliasPath: `/report/${PermissionNames.eventLogList}`,
        element: <EventLogPage title="Sự kiện thay đổi" />,
        permissionTypes: [PermissionType.List],

        // icon: <TbPackages />,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.eventLogViewAll,
        name: PermissionNames.eventLogViewAll,
        aliasPath: `/report/${PermissionNames.eventLogViewAll}`,
        hidden: true,
      },
      {
        title: "Tạo sự kiện thay đổi",
        breadcrumb: "Tạo sự kiện thay đổi",
        path: PermissionNames.eventLogAdd,
        name: PermissionNames.eventLogAdd,
        aliasPath: `/report/${PermissionNames.eventLogAdd}`,
        element: (
          <CreateOrUpdateEventLogPage
            status="create"
            title="Tạo sự kiện thay đổi"
          />
        ),
        permissionTypes: [PermissionType.Add],
        hidden: true,
        isPublic: true,
        // icon: <TbPackages />,
      },
      {
        title: "Chỉnh sửa sự kiện thay đổi",
        breadcrumb: "Chỉnh sửa sự kiện thay đổi",
        path: PermissionNames.eventLogEdit,
        name: PermissionNames.eventLogEdit,
        aliasPath: `/report/${PermissionNames.eventLogEdit.replace(
          "/:id",
          ""
        )}`,
        element: (
          <CreateOrUpdateEventLogPage
            status="update"
            title="Chỉnh sửa sự kiện thay đổi"
          />
        ),
        permissionTypes: [PermissionType.Edit],
        hidden: true,
        isPublic: true,
        // icon: <TbPackages />,
      },
      {
        title: "Xóa sự kiện thay đổi",
        breadcrumb: "Xóa sự kiện thay đổi",
        path: PermissionNames.eventLogDelete,
        name: PermissionNames.eventLogDelete,
        aliasPath: `/report/${PermissionNames.eventLogDelete}`,
        hidden: true,
      },
      {
        title: "Hạng mục",
        breadcrumb: "Hạng mục",
        path: PermissionNames.projectItemList,
        name: PermissionNames.projectItemList,
        aliasPath: `/report/${PermissionNames.projectItemList}`,
        element: <ProjectItemPage title="Hạng mục" />,
        permissionTypes: [PermissionType.List],

        // icon: <TbPackages />,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.projectItemViewAll,
        name: PermissionNames.projectItemViewAll,
        aliasPath: `/report/${PermissionNames.projectItemViewAll}`,
        hidden: true,
      },
      {
        title: "Tạo hạng mục",
        breadcrumb: "Tạo hạng mục",
        path: PermissionNames.projectItemAdd,
        name: PermissionNames.projectItemAdd,
        aliasPath: `/report/${PermissionNames.projectItemAdd}`,
        element: (
          <CreateOrUpdateProjectItemPage title="Tạo hạng mục" status="create" />
        ),
        permissionTypes: [PermissionType.Add],
        hidden: true,
        isPublic: true,
        // icon: <TbPackages />,
      },
      {
        title: "Chỉnh sửa hạng mục",
        breadcrumb: "Chỉnh sửa hạng mục",
        path: PermissionNames.projectItemEdit,
        name: PermissionNames.projectItemEdit,
        aliasPath: `/report/${PermissionNames.projectItemEdit.replace(
          "/:id",
          ""
        )}`,
        element: (
          <CreateOrUpdateProjectItemPage
            title="Chỉnh sửa hạng mục"
            status="update"
          />
        ),
        permissionTypes: [PermissionType.Edit],
        hidden: true,
        isPublic: true,
      },
      {
        title: "Xóa hạng mục",
        breadcrumb: "Xóa hạng mục",
        path: PermissionNames.projectItemDelete,
        name: PermissionNames.projectItemDelete,
        aliasPath: `/report/${PermissionNames.projectItemDelete}`,
        hidden: true,
      },
      {
        title: "Khóa hạng mục",
        breadcrumb: "Khóa hạng mục",
        path: PermissionNames.projectItemBlock,
        name: PermissionNames.projectItemBlock,
        aliasPath: `/report/${PermissionNames.projectItemBlock}`,
        hidden: true,
      },
      {
        title: "Mẫu quy trình duyệt",
        breadcrumb: "Mẫu quy trình duyệt",
        path: PermissionNames.approveProcessList,
        name: PermissionNames.approveProcessList,
        aliasPath: `/report/${PermissionNames.approveProcessList}`,
        element: <ApproveProcessPage title="Mẫu quy trình duyệt" />,
        permissionTypes: [PermissionType.List],

        // icon: <TbPackages />,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.approveProcessViewAll,
        name: PermissionNames.approveProcessViewAll,
        aliasPath: `/report/${PermissionNames.approveProcessViewAll}`,
        hidden: true,
      },
      {
        title: "Tạo mẫu quy trình duyệt",
        breadcrumb: "Tạo mẫu quy trình duyệt",
        path: PermissionNames.approveProcessAdd,
        name: PermissionNames.approveProcessAdd,
        aliasPath: `/report/${PermissionNames.approveProcessAdd}`,
        hidden: true,
      },
      {
        title: "Chỉnh sửa mẫu quy trình duyệt",
        breadcrumb: "Chỉnh sửa mẫu quy trình duyệt",
        path: PermissionNames.approveProcessEdit,
        name: PermissionNames.approveProcessEdit,
        aliasPath: `/report/${PermissionNames.approveProcessEdit}`,
        hidden: true,
      },
      {
        title: "Xóa mẫu quy trình duyệt",
        breadcrumb: "Xóa mẫu quy trình duyệt",
        path: PermissionNames.approveProcessDelete,
        name: PermissionNames.approveProcessDelete,
        aliasPath: `/report/${PermissionNames.approveProcessDelete}`,
        hidden: true,
      },
      {
        title: "Khóa mẫu quy trình duyệt",
        breadcrumb: "Khóa mẫu quy trình duyệt",
        path: PermissionNames.approveProcessBlock,
        name: PermissionNames.approveProcessBlock,
        aliasPath: `/report/${PermissionNames.approveProcessBlock}`,
        hidden: true,
      },
    ],
  },
];
