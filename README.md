# 302 Ichimoku [Web admin]

[![N|Solid](https://bmdsolutions.vn/wp-content/uploads/2020/01/bmd_logo.png)](https://nodesource.com/products/nsolid)

## Yêu cầu hệ thống

- Nodejs từ v16 trở lên
- Trình quản lý package node: yarn

## Cài đặt

Sửa đổi các trường cấu hình ở file .env

# Run Dev

Để run được ở môi trường development thì phải theo các bước sau:

- Mở thư mục dự án
- Cấu hình lại các trường ở file cấu hình .env
- Kiểm trả lại chính xác các trường trong file .env đã định sẵn ở trên

Sau đó run lệnh

```
yarn && yarn start
```

#### Build production

Trước tiên hãy run lệnh ở máy local chứa project

```
yarn && yarn build
```

- Upload tất cả các file lên server

## Li<PERSON>n kết tham khảo

|        | Link                           |
| ------ | ------------------------------ |
| NodeJs | [https://nodejs.org/en/][pldb] |

## License

Copyright BMD Solutions
