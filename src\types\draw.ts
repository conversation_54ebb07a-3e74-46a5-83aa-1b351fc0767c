import { MemberShip } from "./memberShip";
import { ApprovalHistory } from "./approvalHistory";
import { ApprovalList } from "./approvalList";
import { Dictionary } from "./dictionary";
import { FileAttach } from "./fileAttach";
import { Project } from "./project";
import { Staff } from "./staff";
import { ProjectItemDetail } from "./projectItemDetail";

export interface Draw {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  code: string;
  title: string;
  section: string; // hạng mục
  ax: string; // toa độ
  ay: string; // toa độ
  bx: string; // toa độ
  by: string; // toa độ
  cx: string; // toa độ
  cy: string; // toa độ
  dx: string; // toa độ
  dy: string; // toa độ
  note: string; // Đánh giá
  isActive: boolean;
  version: number;
  drawCategory: Dictionary;
  staff: Staff; // người phụ trách
  memberShip: MemberShip;
  followStaffs: Staff[];
  approveStaffs: Staff[];
  approvalHistories: ApprovalHistory[];
  approvalLists: ApprovalList[];
  project: Project;
  fileAttach: FileAttach;
  projectItemDetails: ProjectItemDetail[];
  // info create update
  createdBy: Staff;
  updatedBy: Staff;
}
