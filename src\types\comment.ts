import { RFI } from "./rfi";
import { Staff } from "./staff";
import { Task } from "./task";
import { ApprovalList, ApprovalListStatus } from "./approvalList";
import { Instruction } from "./instruction";
import { Draw } from "./draw";
import { ChangeEvent } from "./changeEvent";

interface LinkedModule {
  id: string;
  type: string;
  code: string;
  name: string;
}

export interface Comment {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  content: string;
  isConfirm: boolean;
  file: string;
  module: string;
  moduleId: number;
  task: Task;
  staff: Staff;
  // audit: Audit[];
  // ncr: NCR;
  rfi: RFI;
  instruction: Instruction;
  linkedModules?: LinkedModule[];
  approvalList: ApprovalList;
  draw: Draw;
  changeEvent: ChangeEvent;
  // dailyLog: DailyLog
  parent: Comment;
  children: Comment[];
  status: ApprovalListStatus;
}
