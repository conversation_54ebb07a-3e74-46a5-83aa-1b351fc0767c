import React, { useState } from "react";
import { Upload, message, <PERSON><PERSON>, Modal } from "antd";
import {
  UploadOutlined,
  FileImageOutlined,
  EyeOutlined,
} from "@ant-design/icons";
import { Document, Page, pdfjs } from "react-pdf";
import { FileAttach, FileAttachType } from "types/fileAttach";
import "./BlueprintUpload.scss";

// <PERSON><PERSON>u hình worker cho react-pdf
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

interface BlueprintUploadProps {
  fileList: FileAttach[];
  onUploadOk: (file: FileAttach) => void;
  onDelete: (file: FileAttach) => void;
  readonly?: boolean;
}

const BlueprintUpload: React.FC<BlueprintUploadProps> = ({
  fileList,
  onUploadOk,
  onDelete,
  readonly = false,
}) => {
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewFile, setPreviewFile] = useState<FileAttach | null>(null);
  const [numPages, setNumPages] = useState<number>();

  const acceptedFormats = ".pdf";

  const uploadProps = {
    name: "file",
    multiple: false,
    accept: acceptedFormats,
    beforeUpload: (file: any) => {
      const isValidFormat = file.name.toLowerCase().match(/\.pdf$/);
      if (!isValidFormat) {
        message.error("Chỉ hỗ trợ định dạng PDF");
        return false;
      }

      const isLt50M = file.size / 1024 / 1024 < 50;
      if (!isLt50M) {
        message.error("File phải nhỏ hơn 50MB!");
        return false;
      }

      // Convert to FileAttach format
      //@ts-ignore
      const fileAttach: FileAttach = {
        uid: file.uid,
        name: file.name,
        size: file.size,
        mimetype: file.type,
        url: URL.createObjectURL(file),
        originFile: file,
        id: 0,
        createdAt: 0,
        updatedAt: 0,
        deletedAt: 0,
        isDeleted: false,
        type: FileAttachType.Image,
        path: "",
        desc: "",
        isActive: false,
        staffs: [],
        taskTemplates: [],
        services: [],
        isError: false,
      };

      console.log("File uploaded:", fileAttach);
      onUploadOk(fileAttach);
      return false;
    },
  };

  const handlePreview = (file: FileAttach) => {
    setPreviewFile(file);
    setPreviewVisible(true);
  };

  // Component để render PDF thumbnail
  const PDFThumbnail: React.FC<{ file: FileAttach }> = ({ file }) => {
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(false);

    return (
      <div className="pdf-thumbnail">
        {loading && <div className="thumbnail-loading">Đang tải...</div>}
        {error && <div className="thumbnail-error">📄</div>}
        <Document
          file={file.url}
          onLoadSuccess={() => {
            setLoading(false);
            setError(false);
          }}
          onLoadError={(error) => {
            console.error("PDF load error:", error);
            setLoading(false);
            setError(true);
          }}
          loading=""
        >
          <Page
            pageNumber={1}
            width={60}
            height={80}
            renderTextLayer={false}
            renderAnnotationLayer={false}
          />
        </Document>
      </div>
    );
  };

  const renderPreview = () => {
    if (!previewFile) return null;

    return (
      <Document
        file={previewFile.url}
        onLoadSuccess={({ numPages }) => setNumPages(numPages)}
        className="pdf-preview"
      >
        <Page pageNumber={1} width={500} />
      </Document>
    );
  };

  return (
    <div className="blueprint-upload">
      {!readonly && (
        <Upload.Dragger {...uploadProps} showUploadList={false}>
          <div className="upload-area">
            <FileImageOutlined className="upload-icon" />
            <div className="upload-content">
              <p className="upload-title">Tải bản vẽ</p>
              <p className="upload-description">Hỗ trợ định dạng PDF</p>
            </div>
            <Button type="primary" className="upload-button">
              Tải file
            </Button>
          </div>
        </Upload.Dragger>
      )}

      {/* Display uploaded files */}
      {fileList && fileList.length > 0 && (
        <div className="uploaded-files">
          <h4>Files đã tải lên ({fileList.length}):</h4>
          {fileList.map((file, index) => (
            <div key={file.uid || index} className="file-item">
              <div className="file-info">
                {/* Hiển thị thumbnail PDF thay vì icon */}
                <div className="file-thumbnail">
                  <PDFThumbnail file={file} />
                </div>
                <div className="file-details">
                  <span className="file-name">{file.name}</span>
                  <span className="file-size">
                    {file.size
                      ? (file.size / 1024 / 1024).toFixed(2)
                      : "Unknown"}{" "}
                    MB
                  </span>
                </div>
              </div>

              <div className="file-actions">
                <Button
                  type="text"
                  size="small"
                  icon={<EyeOutlined />}
                  onClick={() => handlePreview(file)}
                >
                  Xem
                </Button>
                {!readonly && (
                  <Button
                    type="text"
                    danger
                    size="small"
                    onClick={() => onDelete(file)}
                  >
                    Xóa
                  </Button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Preview Modal */}
      <Modal
        title={`Xem trước: ${previewFile?.name}`}
        open={previewVisible}
        onCancel={() => {
          setPreviewVisible(false);
          setPreviewFile(null);
        }}
        footer={null}
        width={800}
        centered
      >
        {renderPreview()}
      </Modal>
    </div>
  );
};

export default BlueprintUpload;
