import { ApprovalList } from "types/approvalList";
import { ApprovalListStatus } from "types/approvalList";
import { ApprovalTemplateName } from "types/approvalTemplate";
import { Staff } from "types/staff";
import { formatDateTime } from "utils/date";
import { StepItem } from "./ApprovalStepsCard";
import { StepType } from "types/approvalStep";

/**
 * Transform approve data to show in card
 * @param data
 * @returns
 */
export const transformApproveData = (
  data: ApprovalList[],
  createdStaff: Staff
): StepItem[] => {
  return data
    .sort((a, b) => a.position - b.position)
    .map((item) => {
      if (item.name == ApprovalTemplateName.Create) {
        item.staff = createdStaff;
        //   item.memberShip = createdStaff.memberShip;
      }

      // debugger;

      let time = "";
      if (item.status == ApprovalListStatus.Approved) {
        time = item.approveAt ? formatDateTime(item.approveAt) : "";
      } else if (item.status == ApprovalListStatus.Rejected) {
        time = item.rejectAt ? formatDateTime(item.rejectAt) : "";
      }

      // if(item.approvalListDetails)

      return {
        ...item,
        // status: ApprovalListStatus.Pending,
        approvers:
          item.approvalListDetails?.map((it) => ({
            id: it.id,
            role: it.role,
            memberShip: it.memberShip,
            roleId: it.role?.id,
            memberShipId: it.memberShip.id,
            status: it.status,
            note: it.note,
            approveAt: it.approveAt,
            rejectAt: it.rejectAt,
          })) || [],
        time,
        //   staffId: item.staff?.id,
        //   staff2Id: item.staff2?.id,
        //memberShip
        //memberShip2
        //   memberShipId: item.memberShip!?.id,
        //   memberShip2Id: item.memberShip2?.id,
      };

      // return {
      //   ...item,
      //   staffId: item.staff?.id,
      //   memberShip: item.memberShip,
      //   memberShip2: item.memberShip2,
      //   memberShipId: item.memberShip?.id,
      //   memberShip2Id: item.memberShip2?.id,
      //   time,
      // };
    });
};

export const findCurrentApprovalStep = (
  steps: StepItem[]
): { curr: StepItem | undefined; isLastStep: boolean } => {
  let curr: StepItem | undefined = undefined;
  let isLastStep = false;
  steps
    .filter((st) => st.name !== StepType.Create)
    .forEach((step, idx) => {
      if (curr == undefined && step.status == ApprovalListStatus.Pending) {
        curr = step;
        if (idx == steps.length - 1) {
          isLastStep = true;
        }
      }
    });
  return { curr, isLastStep };
};
export const findLastApprovalStep = (
  steps: StepItem[]
): StepItem | undefined => {
  let curr: StepItem | undefined = undefined;
  steps
    .filter((st) => st.name !== StepType.Create)
    .reverse()
    .forEach((step, idx) => {
      if (curr == undefined && step.status == ApprovalListStatus.Approved) {
        curr = step;
      }
    });
  return curr;
};
