import { reportApi } from "api/report.api";
import { useState } from "react";
import { Report } from "types/report";
import { QueryParam } from "types/query";

export interface reportQuery extends QueryParam {}

interface UsereportProps {
  initQuery: reportQuery;
}

export const usereport = ({ initQuery }: UsereportProps) => {
  const [data, setData] = useState<Report[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<reportQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await reportApi.findAll(query);

      setData(data.reports);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return { reports: data, total, fetchData, loading, setQuery };
};
