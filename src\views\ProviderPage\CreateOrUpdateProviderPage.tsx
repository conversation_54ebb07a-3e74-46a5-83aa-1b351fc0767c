import {
  Card,
  Col,
  DatePicker,
  Form,
  Input,
  message,
  Row,
  Select,
  Space,
  Tabs,
} from "antd";
import { Rule } from "antd/lib/form";
import { serviceApi } from "api/service.api";
import { ProviderSelector } from "components/Selector/ProviderSelector";
import { UnitSelector } from "components/Selector/UnitSelector";
import { FileUploadMultiple2 } from "components/Upload/FileUploadMultiple2";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import { useEffect, useMemo, useRef, useState } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { FileAttach } from "types/fileAttach";
import { getTitle } from "utils";
import { FileAttachPayload } from "components/Upload/FileUploadItem";
import { $url } from "utils/url";
import { UploadFile } from "antd/lib";
import { useWatch } from "antd/es/form/Form";
import { Provider, ProviderModule } from "types/provider";
// import CustomSelect from "components/TextInput/CustomSelect";
import CustomButton from "components/Button/CustomButton";
import PageTitle from "components/PageTitle/PageTitle";
import ActiveStatusTagSelect from "components/ActiveStatus/ActiveStatusTagSelect";
import { ModalStatus } from "types/modal";
import { isEmpty } from "lodash";
import { PermissionNames } from "types/PermissionNames";
import { DictionarySelector } from "components/Selector/DictionarySelector";
import { Dictionary, DictionaryType } from "types/dictionary";
import { useDictionary } from "hooks/useDictionary";
import { MaterialGroup } from "types/materialGroup";
import { providerApi } from "api/provider.api";
import { ProviderTypeTrans } from "types/provider";
import { CountryTrans, CurrencyTrans } from "types/address";
import { AddressSelect } from "components/AddressSelect/AddressSelect";
import UploadImg from "assets/images/upload-img.png";
import { fileAttachApi } from "api/fileAttach.api";
import { TextInput } from "components/Input/TextInput";
import CustomSelect from "components/Input/CustomSelect";
import { settings } from "settings";
import dayjs from "dayjs";
import clsx from "clsx";
import { phoneValidate } from "utils/validateRule";
import { checkRoles } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { observer } from "mobx-react";

const rules: Rule[] = [{ required: true }];

interface EditProviderPageProps {
  title: string;
  status: ModalStatus;
  module?: ProviderModule;
}

interface ProviderForm extends Provider {
  cityId: number;
  districtId: number;
  wardId: number;
  accountGroupId: number;
  materialGroupIds: number[];
  providerCategoryId: number;
  countryId: number;
  workTypeIds: number[];
}

export const CreateOrUpdateProviderPage = observer(
  ({
    title = "",
    status,
    module = ProviderModule.Supplier,
  }: EditProviderPageProps) => {
    const isProvider = module === ProviderModule.Supplier;

    const { haveEditPermission } = checkRoles(
      {
        edit: isProvider
          ? PermissionNames.providerEdit
          : PermissionNames.subcontractorEdit,
      },
      permissionStore.permissions
    );

    const [form] = Form.useForm<ProviderForm>();
    const [loading, setLoading] = useState(false);
    const navigate = useNavigate();
    useEffect(() => {
      document.title = getTitle(title);
    }, []);
    const [fileList, setFileList] = useState<FileAttach[]>([]);
    const logo = useWatch("logo", form);
    const [searchParams, setSearchParams] = useSearchParams();
    const [selectedProvider, setSelectedProvider] = useState<Provider>();
    const [readonly, setReadonly] = useState(true);
    const addressSelectRef = useRef<any>();

    const params = useParams();

    const getOneProvider = async (id: number) => {
      try {
        setLoading(true);
        const { data } = await providerApi.findOne(id);

        if (isEmpty(data)) {
          navigate("/404");

          return;
        }

        setSelectedProvider(data);

        setDataToForm(data);

        return data as Provider;
      } catch (e: any) {
      } finally {
        setLoading(false);
      }
    };

    const setDataToForm = (data: Provider) => {
      form.setFieldsValue({
        ...data,
        accountGroupId: data.accountGroup?.id,
        materialGroupIds: data.materialGroups?.map(
          (item: MaterialGroup) => item.id
        ),
        workTypeIds: data?.workTypes?.map((item: Dictionary) => item.id),
        startDate: data?.startDate
          ? (dayjs(data?.startDate, "YYYY-MM-DD") as any)
          : undefined,
        cityId: data.city?.id,
        districtId: data.district?.id,
        wardId: data.ward?.id,
        providerCategoryId: data?.providerCategory?.id,
        countryId: data?.country?.id,
      });

      // Set files - convert to FileAttach format
      setFileList(data.fileAttaches ? [...data.fileAttaches] : []);
    };

    useEffect(() => {
      document.title = getTitle(title);

      if (status == "update") {
        const providerId = params.id;
        if (providerId) {
          getOneProvider(+providerId);
          setReadonly(searchParams.get("update") != "1");
        } else {
          navigate("/404");
        }
      } else {
        setReadonly(false);
      }
    }, []);

    const getDataSubmit = async () => {
      const {
        files,
        cityId,
        districtId,
        wardId,
        accountGroupId,
        materialGroupIds,
        workTypeIds,
        providerCategoryId,
        countryId,
        startDate,
        ...data
      } = form.getFieldsValue();

      const fileAttachIds: number[] = [];

      for (const file of fileList) {
        if (file.id) {
          fileAttachIds.push(file.id);
        } else if (file.originFile) {
          const { data } = await fileAttachApi.upload(file.originFile);

          const resFileAttach = await fileAttachApi.create({
            fileAttach: {
              ...file,
              url: $url(data.path),
            },
          });

          fileAttachIds.push(resFileAttach.data.id);
        }
      }

      const payload = {
        provider: {
          ...data,
          module: data.module || module,
          startDate: startDate ? dayjs(startDate).format("YYYY-MM-DD") : "",
          files: typeof files === "string" ? files : JSON.stringify(files),
          isActive: selectedProvider?.isActive,
        },
        providerCategoryId: providerCategoryId || 0,
        workTypeIds: workTypeIds || [],
        cityId: cityId || 0,
        districtId: districtId || 0,
        wardId: wardId || 0,
        accountGroupId: accountGroupId || 0,
        materialGroupIds: materialGroupIds || [],
        fileAttachIds: fileAttachIds || [],
        countryId: countryId || 0,
      };

      return payload;
    };

    const createData = async () => {
      const valid = await form.validateFields();

      setLoading(true);
      try {
        const res = await providerApi.create(await getDataSubmit());
        if (isProvider) {
          message.success("Tạo nhà cung cấp thành công!");
          navigate(`/master-data/${PermissionNames.providerList}`);
        } else {
          message.success("Tạo thầu phụ thành công!");
          navigate(`/master-data/${PermissionNames.subcontractorList}`);
        }
        setFileList([]);
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const valid = await form.validateFields();

      setLoading(true);
      try {
        const res = await providerApi.update(
          selectedProvider!?.id || 0,
          await getDataSubmit()
        );
        setSelectedProvider({ ...selectedProvider, ...res.data });
        if (isProvider) {
          message.success("Chỉnh sửa nhà cung cấp thành công!");
        } else {
          message.success("Chỉnh sửa thầu phụ thành công!");
        }
      } finally {
        setLoading(false);
      }
    };

    const handleSubmit = () => {
      if (status == "create") {
        createData();
      } else {
        updateData();
      }
    };

    const pageTitle = useMemo(() => {
      return isProvider
        ? status == "create"
          ? "Tạo nhà cung cấp"
          : "Chỉnh sửa nhà cung cấp"
        : status == "create"
        ? "Tạo thầu phụ"
        : "Chỉnh sửa thầu phụ";
    }, [status, isProvider]);

    return (
      <div className="app-container">
        <PageTitle
          back
          breadcrumbs={[
            { label: "Dữ liệu nguồn" },
            {
              label: isProvider ? "Danh mục nhà cung cấp" : "Danh mục thầu phụ",
              href: isProvider
                ? `/master-data/${PermissionNames.providerList}`
                : `/master-data/${PermissionNames.subcontractorList}`,
            },
            { label: pageTitle },
          ]}
          title={pageTitle}
          extra={
            selectedProvider &&
            status == "update" && (
              <Space>
                <ActiveStatusTagSelect
                  disabled={readonly}
                  isActive={selectedProvider?.isActive}
                  onChange={(value) => {
                    setSelectedProvider({
                      ...selectedProvider,
                      isActive: value,
                    } as Provider);
                  }}
                />
              </Space>
            )
          }
        />
        <Card>
          <Form
            layout="vertical"
            form={form}
            className={clsx(readonly ? "readonly" : "")}
            disabled={readonly}
          >
            {/* Top section: Image upload + 3 rows of basic info */}
            <div
              style={{
                display: "flex",
              }}
            >
              <div>
                <Form.Item
                  style={{
                    marginBottom: 0,
                    marginRight: 20,
                  }}
                  name="logo"
                  className="form-height-full"
                >
                  <SingleImageUpload
                    onUploadOk={(file: FileAttach) => {
                      console.log(file);
                      form.setFieldsValue({
                        logo: file.path,
                      });
                    }}
                    imageUrl={logo}
                    height={"100%"}
                    width={"100%"}
                    className="h-full upload-avatar"
                    hideUploadButton={readonly}
                    disabled={readonly}
                  />
                </Form.Item>
              </div>

              <div
                style={{
                  flex: 1,
                }}
              >
                {/* Row 1: Mã nhà cung cấp, Tên nhà cung cấp */}
                <Row gutter={16}>
                  <Col span={isProvider ? 12 : 24}>
                    <Form.Item
                      label={isProvider ? "Mã nhà cung cấp" : "Mã thầu phụ"}
                      name="code"
                    >
                      <TextInput
                        disabled={status == "update"}
                        placeholder="Nếu không điền hệ thống sẽ tự sinh mã"
                      />
                    </Form.Item>
                  </Col>

                  <Col span={isProvider ? 12 : 24}>
                    <Form.Item
                      label={
                        isProvider ? "Tên nhà cung cấp" : "Tên nhà thầu phụ"
                      }
                      name="name"
                      rules={rules}
                    >
                      <Input
                        placeholder={
                          isProvider ? "Tên nhà cung cấp" : "Tên nhà thầu phụ"
                        }
                      />
                    </Form.Item>
                  </Col>
                </Row>

                {/* Row 2: Loại nhà cung cấp */}
                <Row gutter={16}>
                  <Col span={isProvider ? 12 : 24}>
                    <Form.Item
                      label={isProvider ? "Loại nhà cung cấp" : "Loại thầu phụ"}
                      name="providerCategoryId"
                      rules={rules}
                    >
                      <DictionarySelector
                        placeholder="Chọn loại"
                        initQuery={{
                          type: isProvider
                            ? DictionaryType.ProviderCategory
                            : DictionaryType.SubContractorCategory,
                          isActive: true,
                        }}
                      />
                    </Form.Item>
                  </Col>

                  {isProvider && (
                    <Col span={12}>
                      <Form.Item
                        rules={rules}
                        label="Loại công tác"
                        name="workTypeIds"
                      >
                        <DictionarySelector
                          multiple
                          placeholder="Chọn loại công tác"
                          initQuery={{
                            type: DictionaryType.WorkType,
                            isActive: true,
                          }}
                        />
                      </Form.Item>
                    </Col>
                  )}
                </Row>
              </div>
            </div>

            {/* Thông tin chung */}
            <Card title="Thông tin chung" className="mb-0 form-card mt-[16px]">
              {/* Row 1: Tên khác, Nhóm tài khoản, Nhóm sản phẩm, Website */}
              <Row gutter={16}>
                {!isProvider && (
                  <>
                    <Col span={6}>
                      <Form.Item label="Website" name="website">
                        <Input placeholder="https://" />
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item
                        label="Lĩnh vực chuyên môn"
                        name="specialization"
                      >
                        <Input placeholder="Lĩnh vực chuyên môn" />
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item label="Số lượng nhân sự" name="staffCount">
                        <Input placeholder="Số lượng nhân sự" />
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item
                        label="Thời gian hoàn thành trung bình"
                        name="averageCompletionTime"
                      >
                        <Input placeholder="Thời gian hoàn thành trung bình" />
                      </Form.Item>
                    </Col>
                  </>
                )}
                {isProvider && (
                  <>
                    <Col span={6}>
                      <Form.Item label="Tên khác" name="aliasName">
                        <TextInput placeholder="Tên khác" />
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item label="Nhóm tài khoản" name="accountGroupId">
                        <DictionarySelector
                          placeholder="Chọn nhóm tài khoản"
                          initQuery={{
                            type: DictionaryType.AccountGroup,
                            isActive: true,
                          }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item label="Nhóm sản phẩm" name="materialGroupIds">
                        <DictionarySelector
                          multiple
                          placeholder="Chọn nhóm sản phẩm"
                          initQuery={{
                            type: DictionaryType.ProductGroup,
                            isActive: true,
                          }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item label="Website" name="website">
                        <TextInput placeholder="https://" />
                      </Form.Item>
                    </Col>
                  </>
                )}
              </Row>

              {/* Row 2: Quốc gia, Tỉnh/Thành phố, Quận/Huyện, Phường/Xã */}
              <Row gutter={16}>
                <Col span={6}>
                  <Form.Item label="Quốc gia" name="countryId">
                    <DictionarySelector
                      placeholder="Chọn quốc gia"
                      initQuery={{
                        type: DictionaryType.Country,
                        isActive: true,
                      }}
                    />
                  </Form.Item>
                </Col>
                {!isProvider && (
                  <>
                    <Col span={6}>
                      <Form.Item label="Tên khác" name="aliasName">
                        <TextInput placeholder="Tên khác" />
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item
                        rules={rules}
                        label="Ngày bắt đầu hợp tác"
                        name="startDate"
                      >
                        <DatePicker
                          placeholder="Ngày bắt đầu hợp tác"
                          format={settings.dateFormat}
                          style={{ width: "100%" }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item
                        rules={rules}
                        label="Loại công tác"
                        name="workTypeIds"
                      >
                        <DictionarySelector
                          multiple
                          placeholder="Chọn loại công tác"
                          initQuery={{
                            type: DictionaryType.WorkType,
                            isActive: true,
                          }}
                        />
                      </Form.Item>
                    </Col>
                  </>
                )}
                {isProvider && (
                  <>
                    <Form.Item name="cityId" />
                    <Form.Item name="wardId" />
                    <Form.Item name="districtId" />
                    <AddressSelect
                      ref={addressSelectRef}
                      form={form}
                      onChange={function (data: any): void {}}
                      disabled={readonly}
                      required={false}
                    />
                  </>
                )}
              </Row>

              {/* Row 3: Điện thoại 1, Điện thoại 2, Email, Fax */}
              {!isProvider && (
                <Row gutter={16}>
                  <Col span={6}>
                    <Form.Item
                      rules={phoneValidate}
                      label="Điện thoại 1"
                      name="phone1"
                    >
                      <TextInput placeholder="Điện thoại 1" />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      rules={phoneValidate}
                      label="Điện thoại 2"
                      name="phone2"
                    >
                      <TextInput placeholder="Điện thoại 2" />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      rules={[{ type: "email" }]}
                      label="Email"
                      name="email"
                    >
                      <TextInput placeholder="Email" />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item label="Fax" name="fax">
                      <TextInput placeholder="Fax" />
                    </Form.Item>
                  </Col>
                </Row>
              )}

              {/* Row 3: Địa chỉ, Mã vùng */}
              <Row gutter={16}>
                {!isProvider && (
                  <>
                    <Form.Item name="cityId" />
                    <Form.Item name="wardId" />
                    <Form.Item name="districtId" />
                    <AddressSelect
                      ref={addressSelectRef}
                      form={form}
                      onChange={function (data: any): void {}}
                      disabled={readonly}
                    />
                  </>
                )}
                <Col span={isProvider ? 12 : 6}>
                  <Form.Item label="Địa chỉ" name="address">
                    <TextInput placeholder="Địa chỉ" />
                  </Form.Item>
                </Col>
                {isProvider && (
                  <Col span={12}>
                    <Form.Item label="Mã vùng" name="areaCode">
                      <TextInput placeholder="Mã vùng" />
                    </Form.Item>
                  </Col>
                )}
              </Row>
            </Card>

            {/* Thông tin giao dịch */}
            <Card
              title="Thông tin giao dịch"
              className="mb-0 form-card mt-[16px]"
            >
              {/* Row 1: Tên ngân hàng 1, Số tài khoản 1, Tên ngân hàng 2, Số tài khoản 2 */}
              <Row gutter={16}>
                <Col span={6}>
                  <Form.Item label="Tên ngân hàng 1" name="bankName1">
                    <TextInput placeholder="Tên ngân hàng" />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="Số tài khoản 1" name="bankAccount1">
                    <TextInput placeholder="STK" />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="Tên ngân hàng 2" name="bankName2">
                    <TextInput placeholder="Tên ngân hàng" />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="Số tài khoản 2" name="bankAccount2">
                    <TextInput placeholder="STK" />
                  </Form.Item>
                </Col>
              </Row>

              {/* Row 2: Loại tiền tệ, Mã số thuế */}
              <Row gutter={16}>
                <Col span={6}>
                  <Form.Item label="Loại tiền tệ" name="currency" rules={rules}>
                    <Select
                      placeholder="Chọn loại tiền tệ"
                      options={Object.values(CurrencyTrans).map((item) => ({
                        label: item.label,
                        value: item.value,
                      }))}
                    />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="Mã số thuế" name="taxNumber">
                    <TextInput placeholder="MST" />
                  </Form.Item>
                </Col>
              </Row>
            </Card>

            {/* Thông tin người liên hệ */}
            <Card
              title="Thông tin người liên hệ"
              className="mb-0 form-card mt-[16px]"
            >
              {/* Row 1: Danh xưng, Tên người liên hệ, Điện thoại */}
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item label="Danh xưng" name="salutation">
                    <TextInput placeholder="Danh xưng" />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label="Tên người liên hệ" name="contactName">
                    <TextInput placeholder="Họ và tên" />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    rules={phoneValidate}
                    label="Điện thoại"
                    name="contactPhone"
                  >
                    <TextInput placeholder="SĐT" />
                  </Form.Item>
                </Col>
              </Row>

              {/* Row 2: Điện thoại di động, Email, Địa chỉ */}
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    rules={phoneValidate}
                    label="Điện thoại di động"
                    name="contactMobile"
                  >
                    <TextInput placeholder="SĐT DĐ" />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    rules={[{ type: "email" }]}
                    label="Email"
                    name="contactEmail"
                  >
                    <TextInput type="email" placeholder="<EMAIL>" />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label="Địa chỉ" name="contactAddress">
                    <TextInput placeholder="Địa chỉ" />
                  </Form.Item>
                </Col>
              </Row>
            </Card>

            {/* Tệp đính kèm - using Tabs like in StaffPage */}
            <Tabs defaultActiveKey="1" type="line" className="mt-[16px]">
              <Tabs.TabPane tab="Tệp đính kèm" key="1">
                <Form.Item
                  shouldUpdate={true}
                  style={{ marginBottom: 0, height: "100%" }}
                  className="form-height-full"
                >
                  {() => {
                    return (
                      <Form.Item
                        label={""}
                        noStyle
                        style={{ marginBottom: 0 }}
                        name="files"
                        className="h-full"
                      >
                        <FileUploadMultiple2
                          className="h-full"
                          fileList={fileList}
                          onUploadOk={(file) => {
                            fileList.push(file);
                            setFileList([...fileList]);
                          }}
                          onDelete={(file) => {
                            const findIndex = fileList.findIndex(
                              (e) => e.uid == file.uid
                            );

                            if (findIndex > -1) {
                              fileList.splice(findIndex, 1);
                              setFileList([...fileList]);
                            }
                          }}
                          hideUploadButton={readonly}
                        />
                      </Form.Item>
                    );
                  }}
                </Form.Item>
              </Tabs.TabPane>
            </Tabs>
          </Form>

          <div className="flex gap-[16px] justify-end mt-2">
            {!readonly && (
              <CustomButton
                variant="outline"
                className="cta-button"
                onClick={() => {
                  if (status == "create") {
                    if (isProvider) {
                      navigate(`/master-data/${PermissionNames.providerList}`);
                    } else {
                      navigate(
                        `/master-data/${PermissionNames.subcontractorList}`
                      );
                    }
                  } else {
                    setReadonly(true);
                    setDataToForm(selectedProvider!);
                  }
                }}
              >
                Hủy
              </CustomButton>
            )}

            <CustomButton
              className="cta-button"
              loading={loading}
              onClick={() => {
                if (!readonly) {
                  handleSubmit();
                } else {
                  setReadonly(false);
                }
              }}
              disabled={status == "update" && !haveEditPermission}
            >
              {useMemo(() => {
                const moduleName = isProvider ? "nhà cung cấp" : "thầu phụ";

                return status == "create"
                  ? "Tạo " + moduleName
                  : readonly
                  ? "Chỉnh sửa " + moduleName
                  : "Lưu chỉnh sửa " + moduleName;
              }, [status, isProvider, readonly])}
            </CustomButton>
          </div>
        </Card>
      </div>
    );
  }
);
