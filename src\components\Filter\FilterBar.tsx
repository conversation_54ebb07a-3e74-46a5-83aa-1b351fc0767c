import React from 'react';
import { Input, Select, Button, Space } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { useThemeColors } from 'theme/useThemeColors';
import './FilterBar.scss';

const { Option } = Select;

export interface FilterOption {
  value: string | number;
  label: string;
}

interface FilterBarProps {
  onSearch?: (value: string) => void;
  onFilter?: (filters: Record<string, any>) => void;
  onReset?: () => void;
  searchPlaceholder?: string;
  filterGroups?: {
    key: string;
    label: string;
    placeholder?: string;
    options: FilterOption[];
    value?: string | number | string[] | number[];
    mode?: 'multiple' | 'tags';
    allowClear?: boolean;
  }[];
  loading?: boolean;
  className?: string;
}

const FilterBar: React.FC<FilterBarProps> = ({
  onSearch,
  onFilter,
  onReset,
  searchPlaceholder = 'Tìm kiếm',
  filterGroups = [],
  loading = false,
  className = '',
}) => {
  const { color } = useThemeColors();
  const [searchValue, setSearchValue] = React.useState('');
  const [filters, setFilters] = React.useState<Record<string, any>>({});

  const handleSearch = (value: string) => {
    setSearchValue(value);
    onSearch?.(value);
  };

  const handleFilterChange = (key: string, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
  };

  const handleApplyFilters = () => {
    onFilter?.({ search: searchValue, ...filters });
  };

  const handleResetFilters = () => {
    setSearchValue('');
    setFilters({});
    onReset?.();
  };

  return (
    <div className={`filter-bar ${className}`}>
      <div className="filter-section">
        <div className="filter-group">
          <label>Tìm kiếm</label>
          <Input
            placeholder={searchPlaceholder}
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            onPressEnter={() => handleSearch(searchValue)}
            suffix={<SearchOutlined />}
            allowClear
          />
        </div>

        {filterGroups.map((group) => (
          <div key={group.key} className="filter-group">
            <label>{group.label}</label>
            <Select
              placeholder={group.placeholder || `Tất cả ${group.label.toLowerCase()}`}
              style={{ width: '100%' }}
              value={filters[group.key] || group.value}
              onChange={(value) => handleFilterChange(group.key, value)}
              mode={group.mode}
              allowClear={group.allowClear !== false}
              suffixIcon={<span className="select-arrow">▼</span>}
            >
              {group.options.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </div>
        ))}

        <Space className="filter-actions">
          <Button
            type="primary"
            onClick={handleApplyFilters}
            loading={loading}
            style={{ backgroundColor: color('branding.primary') }}
          >
            Áp dụng
          </Button>
          <Button onClick={handleResetFilters}>Bỏ lọc</Button>
        </Space>
      </div>
    </div>
  );
};

export default FilterBar;