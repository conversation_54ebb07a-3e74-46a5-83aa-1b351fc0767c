import React, { useState } from "react";
import { Button as <PERSON>t<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Mo<PERSON>, Too<PERSON><PERSON> } from "antd";
import { useTheme } from "context/ThemeContext";
import { useThemeColors } from "theme/useThemeColors";
import "./CustomButton.scss";
import { PlusIcon } from "assets/svgs/PlusIcon";
import { LockOutlined, UnlockOutlined } from "@ant-design/icons";
import CustomButton from "./CustomButton";
import { ReactComponent as LockIcon } from "assets/svgs/Lock.svg";
import { ReactComponent as UnlockIcon } from "assets/svgs/Unlock.svg";

interface CustomButtonProps {
  isActive: boolean;
  onAccept?: () => void;
  modalTitle?: string;
  modalContent?: React.ReactNode;
}

const LockButton: React.FC<CustomButtonProps> = ({
  isActive,
  onAccept,
  modalTitle,
  modalContent,
}) => {
  const { darkMode } = useTheme();
  const { color } = useThemeColors();
  const [isHovered, setIsHovered] = useState(false);

  // Xác định icon hiển thị dựa trên trạng thái hover
  const getIcon = () => {
    if (isHovered) {
      // Khi hover: nếu đang mở khóa thì hiển thị khóa, nếu đang khóa thì hiển thị mở khóa
      return isActive ? (
        <LockIcon width={20} height={20} />
      ) : (
        <UnlockIcon width={20} height={20} />
      );
    } else {
      // Khi không hover: hiển thị trạng thái hiện tại
      return isActive ? (
        <UnlockIcon width={20} height={20} />
      ) : (
        <LockIcon width={20} height={20} />
      );
    }
  };

  // Xác định tooltip dựa trên trạng thái hover
  const getTooltipTitle = () => {
    if (isHovered) {
      return isActive ? "Khóa" : "Mở khóa";
    } else {
      return isActive ? "Khóa" : "Mở khóa";
    }
  };

  return (
    <Tooltip title={getTooltipTitle()}>
      <Button
        type="text"
        danger={isActive}
        icon={getIcon()}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        style={{
          transition: "all 0.2s ease-in-out",
          transform: isHovered ? "scale(1.1)" : "scale(1)",
        }}
        onClick={(e) => {
          e.stopPropagation();
          Modal.confirm({
            title: modalTitle,
            getContainer: () => {
              return document.getElementById("App") as HTMLElement;
            },
            icon: null,
            content: modalContent,
            footer: (_, { OkBtn, CancelBtn }) => (
              <>
                <CustomButton
                  variant="outline"
                  className="cta-button"
                  onClick={() => {
                    onAccept?.();
                    Modal.destroyAll();
                  }}
                >
                  Có
                </CustomButton>
                <CustomButton
                  onClick={() => {
                    Modal.destroyAll();
                  }}
                  className="cta-button"
                >
                  Không
                </CustomButton>
              </>
            ),
          });
        }}
      />
    </Tooltip>
  );
};

export default LockButton;
