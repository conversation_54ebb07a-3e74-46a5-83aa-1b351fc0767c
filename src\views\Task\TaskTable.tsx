import { Spin, Space, Avatar, message, Modal, Button, Tooltip } from "antd";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import { Pagination } from "components/Pagination";
import { useTheme } from "context/ThemeContext";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { PermissionNames } from "types/PermissionNames";
import { useInstruction } from "hooks/useInstruction";
import {
  getOverallApprovalStatus,
  Instruction,
  InstructionType,
  InstructionTypeTrans,
  ProgressInstructionStatus,
} from "types/instruction";
import {
  checkDeletePermissionByCreator,
  checkEditPermissionByCreator,
  checkRoles,
  filterActionColumnIfNoPermission,
} from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { observer } from "mobx-react";
import { $url } from "utils/url";
import { Staff } from "types/staff";
import EditButton from "components/Button/EditButton";
import ProgressLegend from "components/ProgressLegend/ProgressLegend";
import { useTask } from "hooks/useTask";
import DeleteButton from "components/Button/DeleteButton";
import { Task } from "types/task";
import { formatDate } from "utils/date";
import CustomButton from "components/Button/CustomButton";
import { taskApi } from "api/task.api";
import { userStore } from "store/userStore";
import dayjs from "dayjs";
import { ArrowDownIcon } from "assets/svgs/ArrowDownIcon";
import { TaskSubTable } from "./components/TaskSubTable";
import { ReactComponent as Copy } from "assets/svgs/copy.svg";

interface IndicativeTableProps {
  type: "assign" | "create"; // Phân biệt tab
  userId: number; // ID của user hiện tại
  query?: any;
  onQueryChange?: (query: any) => void;
}

interface CategoryGroup {
  categoryName: string;
  categoryId: string;
  // users: UserData[];
  tasks: Task[];
  isStandaloneTask?: boolean;
}

function TaskTable({
  type,
  userId,
  query: parentQuery,
  onQueryChange,
}: IndicativeTableProps) {
  const {
    haveBlockPermission,
    haveEditPermission,
    haveViewAllPermission,
    haveAddPermission,
  } = checkRoles(
    {
      edit: PermissionNames.taskEdit,
      block: PermissionNames.taskDelete,
      viewAll: PermissionNames.taskViewAll,
      add: PermissionNames.taskAdd,
    },
    permissionStore.permissions
  );
  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);

  // Tạo query dựa trên type
  const buildQuery = (baseQuery: any) => {
    const filterParams =
      type === "assign"
        ? { assigneeMemberShipId: userId }
        : { createdById: userId };

    return {
      page: 1,
      limit: 0,
      ...baseQuery,
      ...filterParams,
    };
  };

  const { fetchData, tasks, loading, query, setQuery, total } = useTask({
    initQuery: buildQuery(parentQuery || {}),
  });

  // Sync with parent query changes
  useEffect(() => {
    if (parentQuery) {
      const newQuery = buildQuery(parentQuery);
      setQuery(newQuery);
    }
  }, [parentQuery, type, userId]);

  useEffect(() => {
    fetchData();
  }, [query, type, userId]); // Thêm type và userId vào dependency để fetch lại khi thay đổi

  const { darkMode } = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const [copyLoading, setCopyLoading] = useState(false);

  // Update the pagination handler
  const handlePaginationChange = ({
    limit,
    page,
  }: {
    limit: number;
    page: number;
  }) => {
    const newQuery = {
      ...query,
      limit: 0, // Luôn set limit = 0 để bỏ pagination
      page,
    };
    setQuery(newQuery);
    onQueryChange?.(newQuery);
  };

  const handleRowClick = (record: Task) => {
    navigate(
      `/progress-management/${PermissionNames.taskEdit.replace(
        ":id",
        record!.id + ""
      )}`
    );
  };

  const canEditRecord = (record: Task) => {
    return checkEditPermissionByCreator(
      userStore.info.id,
      record.createdBy?.id,
      haveEditPermission,
      haveViewAllPermission
    );
  };

  const canDeleteRecord = (record: Task) => {
    return checkDeletePermissionByCreator(
      userStore.info.id,
      record.createdBy?.id,
      haveBlockPermission,
      haveViewAllPermission
    );
  };

  // const columns: CustomizableColumn<any>[] = [
  //   {
  //     key: "code",
  //     title: "Mã",
  //     dataIndex: "code",
  //     width: 100,
  //     defaultVisible: true,
  //     alwaysVisible: true,
  //     align: "center",
  //     render: (_, record: Task) => (
  //       <div
  //         onClick={() => handleRowClick(record)}
  //         className="text-[#1677ff] cursor-pointer"
  //       >
  //         {record.code}
  //       </div>
  //     ),
  //   },
  //   {
  //     key: "name",
  //     title: "Công việc",
  //     dataIndex: "title",
  //     width: 100,
  //     defaultVisible: true,
  //     alwaysVisible: true,
  //   },
  //   {
  //     key: "type",
  //     title: "Phòng ban",
  //     dataIndex: "type",
  //     width: 100,
  //     defaultVisible: true,
  //     alwaysVisible: true,
  //     render: (_, record: Task) => record.department?.name,
  //   },
  //   {
  //     key: "indicator",
  //     title: "Người tạo",
  //     dataIndex: "indicator",
  //     width: 100,
  //     defaultVisible: true,
  //     alwaysVisible: true,
  //     render: (_, record: Task) => _renderStaffRow(record.createdBy),
  //   },
  //   {
  //     key: "receiver",
  //     title: "Người phụ trách",
  //     dataIndex: "receiver",
  //     width: 100,
  //     defaultVisible: true,
  //     alwaysVisible: true,
  //     render: (_, record: Task) =>
  //       record.assigneeMemberShip?.staff
  //         ? _renderStaffRow(record.assigneeMemberShip.staff)
  //         : "",
  //   },
  //   {
  //     key: "projectName",
  //     title: "Ngày đến hạn",
  //     dataIndex: "projectName",
  //     width: 100,
  //     defaultVisible: true,
  //     alwaysVisible: true,
  //     render: (_, record: Task) => record.endDate,
  //   },

  //   {
  //     title: "Trạng thái",
  //     dataIndex: "isActive",
  //     key: "isActive",
  //     align: "center",
  //     width: 130,
  //     sorter: true,
  //     render: (isActive, record) => {
  //       // Sort approvalLists theo position trước khi xử lý
  //       const sortedApprovalLists = record.approvalLists
  //         ? record.approvalLists
  //             .slice()
  //             .sort(
  //               (a: { position: number }, b: { position: number }) =>
  //                 a.position - b.position
  //             )
  //         : [];
  //       return (
  //         <ProgressLegend
  //           status={
  //             ProgressInstructionStatus[
  //               getOverallApprovalStatus(sortedApprovalLists)
  //             ]
  //           }
  //           steps={sortedApprovalLists}
  //         />
  //       );
  //     },
  //   },
  //   {
  //     key: "actions",
  //     title: "Hành động",
  //     width: 100,
  //     align: "center",
  //     fixed: "right",
  //     render: (_, record) => (
  //       <Space size="small">
  //         {canEditRecord(record) && (
  //           <EditButton
  //             onClick={(e) => {
  //               e.stopPropagation();
  //               navigate(
  //                 `/progress-management/${PermissionNames.taskEdit.replace(
  //                   ":id",
  //                   record!.id + ""
  //                 )}?update=1`
  //               );
  //             }}
  //           />
  //         )}
  //         {canDeleteRecord(record) && (
  //           <DeleteButton
  //             onClick={(e) => {
  //               e.stopPropagation();
  //               Modal.confirm({
  //                 title: `Xóa task "${record.title}"`,
  //                 getContainer: () => {
  //                   return document.getElementById("App") as HTMLElement;
  //                 },
  //                 icon: null,
  //                 content: (
  //                   <>
  //                     <div>
  //                       Bạn sẽ không thể khôi phục dữ liệu này sau khi xóa.
  //                       <br />
  //                       Bạn có chắc chắn muốn xóa dữ liệu này?
  //                     </div>
  //                   </>
  //                 ),
  //                 footer: (_, { OkBtn, CancelBtn }) => (
  //                   <>
  //                     <CustomButton
  //                       variant="outline"
  //                       className="cta-button"
  //                       onClick={() => {
  //                         handleDeleteDraw(record.id);
  //                         Modal.destroyAll();
  //                       }}
  //                     >
  //                       Có
  //                     </CustomButton>
  //                     <CustomButton
  //                       onClick={() => {
  //                         Modal.destroyAll();
  //                       }}
  //                       className="cta-button"
  //                     >
  //                       Không
  //                     </CustomButton>
  //                   </>
  //                 ),
  //               });
  //             }}
  //           />
  //         )}
  //       </Space>
  //     ),
  //   },
  // ];

  // const getMainTableColumns = (): CustomizableColumn<CategoryGroup>[] => [
  //   {
  //     key: "codeHeader",
  //     title: "Mã",
  //     width: 100,
  //     defaultVisible: true,
  //     alwaysVisible: true,
  //     render: (text, record) => ({
  //       props: {
  //         colSpan: record.tasks?.length > 0 ? 3 : 1, // Span 3 columns for categories with tasks
  //       },
  //       children: (
  //         <div className="flex items-center gap-2">
  //           <span className="font-medium">{record.categoryName}</span>
  //         </div>
  //       ),
  //     }),
  //   },

  //   {
  //     key: "titleHeader",
  //     title: "Công việc",
  //     width: 150,
  //     defaultVisible: true,
  //     alwaysVisible: true,
  //     render: (_, record) => ({
  //       props: {
  //         colSpan: record.tasks?.length > 0 ? 0 : 1,
  //       },
  //     }),
  //   },
  //   {
  //     key: "departmentHeader",
  //     title: "Phòng ban",
  //     width: 150,
  //     defaultVisible: true,
  //     alwaysVisible: true,
  //     render: (_, record) => ({
  //       props: {
  //         colSpan: record.tasks?.length > 0 ? 0 : 1,
  //       },
  //     }),
  //   },
  //   {
  //     key: "createdByHeader",
  //     title: "Người tạo",
  //     width: 150,
  //     defaultVisible: true,
  //     alwaysVisible: true,
  //     render: () => null,
  //   },
  //   {
  //     key: "assigneeHeader",
  //     title: "Người phụ trách",
  //     width: 150,
  //     defaultVisible: true,
  //     alwaysVisible: true,
  //     render: () => null,
  //   },
  //   {
  //     key: "endDateHeader",
  //     title: "Ngày đến hạn",
  //     width: 120,
  //     defaultVisible: true,
  //     alwaysVisible: true,
  //     render: () => null,
  //   },
  //   {
  //     key: "statusHeader",
  //     title: "Trạng thái",
  //     align: "center",
  //     width: 130,
  //     defaultVisible: true,
  //     alwaysVisible: true,
  //     render: () => null,
  //   },
  //   {
  //     key: "actions",
  //     title: "Hành động",
  //     align: "center",
  //     width: 100,
  //     defaultVisible: true,
  //     alwaysVisible: true,
  //     render: () => null,
  //   },
  // ];

  const handleCopyTask = async (task: Task) => {
    setCopyLoading(true);
    try {
      // Gọi API lấy chi tiết task
      const { data } = await taskApi.findOne(task.id);
      if (!data) {
        message.error("Không tìm thấy dữ liệu để sao chép");
        return;
      }
      const isInProgressManagement = location.pathname.includes("/task/");
      // Xóa id để tránh trùng lặp, để code rỗng để tự sinh mã mới
      const copyData = {
        ...data,
        id: undefined,
        code: "",
      };
      navigate(`/task/${PermissionNames.taskAdd}`, {
        state: {
          copyData,
          fromProgressManagement: isInProgressManagement,
        },
      });
    } catch (error) {
      message.error("Lỗi khi sao chép công việc");
    } finally {
      setCopyLoading(false);
    }
  };

  const getMainTableColumns = (): CustomizableColumn<CategoryGroup>[] => [
    {
      key: "codeHeader",
      title: "Mã",
      width: 100,
      defaultVisible: true,
      alwaysVisible: true,
      render: (text, record) => ({
        props: {
          colSpan: record.isStandaloneTask
            ? 1
            : record.tasks?.length > 0
            ? 2
            : 1,
        },
        children: (
          <div className="flex items-center gap-2">
            <span className="font-medium">
              {record.isStandaloneTask
                ? record.tasks[0].code
                : record.categoryName}
            </span>
          </div>
        ),
      }),
    },
    {
      key: "titleHeader",
      title: "Công việc",
      width: 150,
      defaultVisible: true,
      alwaysVisible: true,
      render: (_, record) => ({
        props: {
          colSpan: record.isStandaloneTask
            ? 1
            : record.tasks?.length > 0
            ? 0
            : 1,
        },
        children: record.isStandaloneTask ? (
          <span>{record.tasks[0].title}</span>
        ) : null,
      }),
    },
    {
      key: "projectHeader",
      title: "Dự án",
      width: 150,
      defaultVisible: true,
      alwaysVisible: true,
      render: (_, record) => ({
        props: {
          colSpan: record.isStandaloneTask
            ? 1
            : record.tasks?.length > 0
            ? 0
            : 1,
        },
        children: record.isStandaloneTask ? (
          <span>{record.tasks[0].project?.name || "N/A"}</span>
        ) : null,
      }),
    },
    {
      key: "departmentHeader",
      title: "Phòng ban",
      width: 150,
      defaultVisible: true,
      alwaysVisible: true,
      render: (_, record) => ({
        props: {
          colSpan: record.isStandaloneTask
            ? 1
            : record.tasks?.length > 0
            ? 0
            : 1,
        },
        children: record.isStandaloneTask ? (
          <span>{record.tasks[0].department?.name || "N/A"}</span>
        ) : null,
      }),
    },
    {
      key: "createdByHeader",
      title: "Người tạo",
      width: 150,
      defaultVisible: true,
      alwaysVisible: true,
      render: (_, record) => ({
        // props: {
        //   colSpan: record.isStandaloneTask ? 1 : 0,
        // },
        children: record.isStandaloneTask
          ? _renderStaffRow(record.tasks[0].createdBy)
          : null,
      }),
    },
    {
      key: "assigneeHeader",
      title: "Người phụ trách",
      width: 150,
      defaultVisible: true,
      alwaysVisible: true,
      render: (_, record) => ({
        // props: {
        //   colSpan: record.isStandaloneTask ? 1 : 0,
        // },
        children: record.isStandaloneTask
          ? _renderStaffRow(record.tasks[0].assigneeMemberShip?.staff)
          : null,
      }),
    },
    {
      key: "endDateHeader",
      title: "Ngày đến hạn",
      width: 120,
      defaultVisible: true,
      alwaysVisible: true,
      render: (_, record) => ({
        // props: {
        //   colSpan: record.isStandaloneTask ? 1 : 1,
        // },
        children: record.isStandaloneTask ? (
          <span>{record.tasks[0].endDate}</span>
        ) : null,
      }),
    },
    {
      key: "statusHeader",
      title: "Trạng thái",
      align: "center",
      width: 130,
      defaultVisible: true,
      alwaysVisible: true,
      render: (isActive, record) => {
        // Sort approvalLists theo position trước khi xử lý
        if (!record.isStandaloneTask) {
          return null;
        }
        const sortedApprovalLists = record.tasks[0].approvalLists
          ? record.tasks[0].approvalLists
              .slice()
              .sort(
                (a: { position: number }, b: { position: number }) =>
                  a.position - b.position
              )
          : [];
        return (
          <ProgressLegend
            status={
              ProgressInstructionStatus[
                getOverallApprovalStatus(sortedApprovalLists)
              ]
            }
            steps={sortedApprovalLists}
          />
        );
      },
    },
    {
      key: "actions",
      title: "Hành động",
      align: "center",
      width: 100,
      defaultVisible: true,
      alwaysVisible: true,
      render: (_, record) => ({
        props: {
          colSpan: record.isStandaloneTask ? 1 : 2,
        },
        children: record.isStandaloneTask ? (
          <Space size="small">
            {haveAddPermission && (
              <Tooltip title="Tạo nhanh từ bản này">
                <Button
                  type="text"
                  icon={<Copy />}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCopyTask(record.tasks[0]);
                  }}
                  loading={copyLoading}
                />
              </Tooltip>
            )}
            {canEditRecord(record.tasks[0]) && (
              <EditButton
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(
                    `/task/${PermissionNames.taskEdit.replace(
                      ":id",
                      record.tasks[0].id + ""
                    )}?update=1`
                  );
                }}
              />
            )}
            {canDeleteRecord(record.tasks[0]) && (
              <DeleteButton
                onClick={(e) => {
                  e.stopPropagation();
                  Modal.confirm({
                    title: `Xóa task "${record.tasks[0].title}"`,
                    getContainer: () => {
                      return document.getElementById("App") as HTMLElement;
                    },
                    icon: null,
                    content: (
                      <>
                        <div>
                          Bạn sẽ không thể khôi phục dữ liệu này sau khi xóa.
                          <br />
                          Bạn có chắc chắn muốn xóa dữ liệu này?
                        </div>
                      </>
                    ),
                    footer: (_, { OkBtn, CancelBtn }) => (
                      <>
                        <CustomButton
                          variant="outline"
                          className="cta-button"
                          onClick={() => {
                            handleDeleteDraw(record.tasks[0].id);
                            Modal.destroyAll();
                          }}
                        >
                          Có
                        </CustomButton>
                        <CustomButton
                          onClick={() => {
                            Modal.destroyAll();
                          }}
                          className="cta-button"
                        >
                          Không
                        </CustomButton>
                      </>
                    ),
                  });
                }}
              />
            )}
          </Space>
        ) : null,
      }),
    },
  ];
  const handleDeleteDraw = async (id: number) => {
    try {
      await taskApi.delete(id);
      message.success("Xóa thành công");
      fetchData();
    } finally {
    }
  };

  // Transform tasks data to grouped by taskCategory using useMemo
  const categoryData = useMemo(() => {
    const transformTasksToCategoryGroups = (tasks: Task[]): CategoryGroup[] => {
      const groupedByCategory = tasks.reduce((acc, task) => {
        if (task.parent) {
          // Tasks with parent: group by parent.id
          const categoryId = task.parent.id.toString();
          const categoryName = task.parent.title || "Công việc cha";

          if (!acc[categoryId]) {
            acc[categoryId] = {
              categoryName,
              categoryId,
              tasks: [],
            };
          }
          acc[categoryId].tasks.push(task);
        } else {
          // Tasks without parent: create a standalone group
          const categoryId = `task-${task.id}`; // Unique ID for standalone task
          acc[categoryId] = {
            categoryName: task.title || "Công việc đơn lẻ",
            categoryId,
            tasks: [task],
            isStandaloneTask: true,
          };
        }
        return acc;
      }, {} as Record<string, CategoryGroup>);

      // Bỏ pagination - trả về tất cả dữ liệu
      return Object.values(groupedByCategory);
    };

    return transformTasksToCategoryGroups(tasks);
  }, [tasks]);

  useEffect(() => {
    if (categoryData.length > 0) {
      const allKeys = categoryData
        .filter((group) => !group.isStandaloneTask)
        .map((group) => group.categoryId);
      setExpandedRowKeys((prevKeys) => {
        if (
          JSON.stringify(prevKeys.sort()) !== JSON.stringify(allKeys.sort())
        ) {
          return allKeys;
        }
        return prevKeys;
      });
    }
  }, [categoryData]);

  const _renderStaffRow = (staff: Staff) => {
    return (
      <div className="flex items-center gap-[8px]">
        <Avatar
          size={28}
          src={staff?.avatar ? $url(staff?.avatar) : undefined}
          style={{ backgroundColor: "#1890ff", flexShrink: 0 }}
        >
          {staff?.fullName?.charAt(0)}
        </Avatar>
        <label htmlFor="" className="text-neutral-800 text-bold">
          {staff?.fullName}
        </label>
      </div>
    );
  };

  // const expandedRowRender = useCallback(
  //   (record: CategoryGroup) => {
  //     return (
  //       <TaskSubTable
  //         tasks={record.tasks}
  //         onEdit={(task) =>
  //           navigate(
  //             `/progress-management/${PermissionNames.taskEdit.replace(
  //               ":id",
  //               task.id + ""
  //             )}?update=1`
  //           )
  //         }
  //         onDelete={(task) =>
  //           Modal.confirm({
  //             title: `Xóa task "${task.title}"`,
  //             getContainer: () => {
  //               return document.getElementById("App") as HTMLElement;
  //             },
  //             icon: null,
  //             content: (
  //               <>
  //                 <div>
  //                   Bạn sẽ không thể khôi phục dữ liệu này sau khi xóa.
  //                   <br />
  //                   Bạn có chắc chắn muốn xóa dữ liệu này?
  //                 </div>
  //               </>
  //             ),
  //             footer: (_, { OkBtn, CancelBtn }) => (
  //               <>
  //                 <CustomButton
  //                   variant="outline"
  //                   className="cta-button"
  //                   onClick={() => {
  //                     handleDeleteDraw(task.id);
  //                     Modal.destroyAll();
  //                   }}
  //                 >
  //                   Có
  //                 </CustomButton>
  //                 <CustomButton
  //                   onClick={() => {
  //                     Modal.destroyAll();
  //                   }}
  //                   className="cta-button"
  //                 >
  //                   Không
  //                 </CustomButton>
  //               </>
  //             ),
  //           })
  //         }
  //         canEditRecord={canEditRecord}
  //         canDeleteRecord={canDeleteRecord}
  //         renderStaffRow={_renderStaffRow}
  //       />
  //     );
  //   },
  //   [navigate, canEditRecord, canDeleteRecord]
  // );

  const expandedRowRender = useCallback(
    (record: CategoryGroup) => {
      if (record.isStandaloneTask) return null; // No sub-table for standalone tasks
      return (
        <TaskSubTable
          tasks={record.tasks}
          onEdit={(task) =>
            navigate(
              `/progress-management/${PermissionNames.taskEdit.replace(
                ":id",
                task.id + ""
              )}?update=1`
            )
          }
          onDelete={(task) =>
            Modal.confirm({
              title: `Xóa task "${task.title}"`,
              getContainer: () => {
                return document.getElementById("App") as HTMLElement;
              },
              icon: null,
              content: (
                <>
                  <div>
                    Bạn sẽ không thể khôi phục dữ liệu này sau khi xóa.
                    <br />
                    Bạn có chắc chắn muốn xóa dữ liệu này?
                  </div>
                </>
              ),
              footer: (_, { OkBtn, CancelBtn }) => (
                <>
                  <CustomButton
                    variant="outline"
                    className="cta-button"
                    onClick={() => {
                      handleDeleteDraw(task.id);
                      Modal.destroyAll();
                    }}
                  >
                    Có
                  </CustomButton>
                  <CustomButton
                    onClick={() => {
                      Modal.destroyAll();
                    }}
                    className="cta-button"
                  >
                    Không
                  </CustomButton>
                </>
              ),
            })
          }
          canEditRecord={canEditRecord}
          canDeleteRecord={canDeleteRecord}
          renderStaffRow={_renderStaffRow}
        />
      );
    },
    [navigate, canEditRecord, canDeleteRecord]
  );

  return (
    <div>
      <div className="pb-[16px]">
        <Spin spinning={loading}>
          <CustomizableTable
            columns={filterActionColumnIfNoPermission(getMainTableColumns(), [
              haveEditPermission,
              haveBlockPermission,
            ])}
            dataSource={categoryData}
            rowKey="categoryId"
            expandable={{
              expandedRowRender,
              expandedRowKeys: expandedRowKeys,
              onExpandedRowsChange: (keys) =>
                setExpandedRowKeys(keys as string[]),
              expandIcon: ({ expanded, onExpand, record }) => (
                <Button
                  type="text"
                  size="small"
                  icon={
                    <ArrowDownIcon
                      className={`transition-transform duration-200 ${
                        expanded ? "rotate-0" : "-rotate-90"
                      }`}
                      fill={darkMode ? "#ffffff" : "#6b7280"}
                    />
                  }
                  onClick={(e) => onExpand(record, e)}
                  className={`!border-0 !shadow-none hover:!bg-gray-100 ${
                    record.isStandaloneTask ? "invisible" : ""
                  }`}
                />
              ),
              expandIconColumnIndex: 0,
              rowExpandable: (record) =>
                !record.isStandaloneTask &&
                record.tasks &&
                record.tasks.length > 0,
            }}
            pagination={false}
            scroll={{ x: 1200 }}
            bordered
            displayOptions
            tableId="task-table"
            onRow={(record) => ({
              onClick: () => {
                if (record.isStandaloneTask) {
                  navigate(
                    `/progress-management/${PermissionNames.taskEdit.replace(
                      ":id",
                      record.tasks[0].id + ""
                    )}`
                  );
                }
              },
            })}
          />
        </Spin>
      </div>

      {/* Bỏ Pagination component vì đã set limit = 0 */}
    </div>
  );
}

export default observer(TaskTable);
