import { ApprovalHistory } from "./approvalHistory";
import { ApprovalList } from "./approvalList";
import { Dictionary } from "./dictionary";
import { FileAttach } from "./fileAttach";
import { MemberShip } from "./memberShip";
import { Project } from "./project";
import { Staff } from "./staff";

export interface ChangeEvent {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  code: string;
  type: ChangeEventType; // Loại sự kiện thay đổi
  description: string; // <PERSON>ô tả chi tiết (ví dụ: tăng chi phí, thêm công việc…)
  amount: number; // Số tiền thay đổi (nếu có)
  quantity: number; // Số lượng công việc / vật tư (nếu có)
  budgetImpact: number; // Ảnh hưởng đến ngân sách (có thể âm hoặc dương)
  status: ChangeEventStatus; // Trạng thái ghi chú
  files: string;
  title: string;
  contentChange: string;
  project: Project;
  createdBy: Staff;
  changeEventCategory: Dictionary;
  updatedBy: Staff;
  approvalLists: ApprovalList[];
  approvalHistories: ApprovalHistory[];
  fileAttaches: FileAttach[];
  followStaffs: Staff[];
  followMemberShips: MemberShip[];
  // if (followStaffIds?.length) {
  //     await changeEvent.assignFollowStaffs(followStaffIds);
  // } else if (followStaffIds?.length == 0) {
  //     changeEvent.followStaffs = [];
  // }
}

export enum ChangeEventType {
  CostAdjustment = "COST_ADJUSTMENT", // Điều chỉnh chi phí
  TaskAddition = "TASK_ADDITION", // Thêm công việc
  BudgetReplan = "BUDGET_REPLAN", // Điều chỉnh ngân sách
  Other = "OTHER", // Khác
}

export enum ChangeEventStatus {
  Draft = "DRAFT",
  Pending = "PENDING",
  Approved = "APPROVED",
  Rejected = "REJECTED",
  WaitingApprove = "WAITING_APPROVE",
}

export function getOverallApprovalStatus(
  approvalLists: any[]
): ChangeEventStatus {
  if (!approvalLists || approvalLists.length === 0)
    return ChangeEventStatus.Draft;
  if (approvalLists.some((item) => item.status === "REJECTED"))
    return ChangeEventStatus.Rejected;

  // Nếu tất cả đều APPROVED
  if (approvalLists.every((item) => item.status === "APPROVED"))
    return ChangeEventStatus.Approved;

  // Nếu tất cả đều PENDING
  if (approvalLists.every((item) => item.status === "PENDING"))
    return ChangeEventStatus.Draft;

  // Nếu chỉ có CREATE là APPROVED, các bước sau đều PENDING => WAITING_APPROVE
  const firstStep = approvalLists[0];
  const restSteps = approvalLists.slice(1);
  if (
    firstStep &&
    firstStep.name === "CREATE" &&
    firstStep.status === "APPROVED" &&
    restSteps.every((item) => item.status === "PENDING")
  ) {
    return ChangeEventStatus.WaitingApprove;
  }

  // Nếu có ít nhất 1 bước APPROVE đã duyệt => Đang thực hiện
  if (
    approvalLists.some(
      (item, idx) =>
        idx > 0 && item.name === "APPROVE" && item.status === "APPROVED"
    )
  ) {
    return ChangeEventStatus.Pending;
  }

  // Nếu có ít nhất 1 bước PENDING => Đang thực hiện
  if (approvalLists.some((item) => item.status === "PENDING"))
    return ChangeEventStatus.Pending;

  return ChangeEventStatus.Draft;
}
// export enum RFIStatus {
//   Pending = "PENDING",
//   InProgress = "IN_PROGRESS",
//   Completed = "COMPLETED",
// }

export const ProgressChangeEventStatus = {
  [ChangeEventStatus.Draft]: {
    progress: 10,
    label: "Chưa bắt đầu",
    color: "#3949AB",
    value: ChangeEventStatus.Draft,
  },
  [ChangeEventStatus.WaitingApprove]: {
    progress: 30,
    label: "Chờ duyệt",
    color: "#B9C3C5",
    value: ChangeEventStatus.WaitingApprove,
  },
  [ChangeEventStatus.Pending]: {
    progress: 50,
    label: "Đang thực hiện",
    color: "#FFB300",
    value: ChangeEventStatus.Pending,
  },
  [ChangeEventStatus.Approved]: {
    progress: 100,
    label: "Hoàn thành",
    color: "#43A047",
    value: ChangeEventStatus.Approved,
  },
  [ChangeEventStatus.Rejected]: {
    progress: 100,
    label: "Bị từ chối",
    color: "#E53935",
    value: ChangeEventStatus.Rejected,
    showProgressBar: false,
  },
};

export const TypeLabel = {
  [ChangeEventType.CostAdjustment]: {
    label: "Điều chỉnh chi phí",
    value: ChangeEventType.CostAdjustment,
  },
  [ChangeEventType.TaskAddition]: {
    label: "Thêm công việc",
    value: ChangeEventType.TaskAddition,
  },
  [ChangeEventType.BudgetReplan]: {
    label: "Điều chỉnh ngân sách",
    value: ChangeEventType.BudgetReplan,
  },
  [ChangeEventType.Other]: {
    label: "Khác",
    value: ChangeEventType.Other,
  },
};
