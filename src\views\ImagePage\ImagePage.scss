// Image Upload Overlay Styles
.upload-overlay {
  .ant-upload {
    border: none !important;
    background: transparent !important;
    padding: 0 !important;

    &:hover {
      border: none !important;
      background: transparent !important;
    }
  }
}

.image-preview-container {
  position: relative;
  border: 1px solid #d9d9d9;
  overflow: hidden;

  &:hover {
    .upload-overlay-button {
      opacity: 1;
    }
  }

  // Responsive image sizing
  .ant-image {
    width: 100%;
    height: 16rem; // h-64 equivalent
    object-fit: cover;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}

.upload-overlay-button {
  opacity: 0.9;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 1;
  }
}

// Additional styles for image upload area
.image-upload-section {
  position: relative;

  .ant-upload-drag {
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    background: #fafafa;
    transition: all 0.3s ease;

    &:hover {
      border-color: #1890ff;
      background: #f0f8ff;
    }
  }

  .ant-upload-drag-icon {
    margin-bottom: 16px;
  }

  .ant-upload-text {
    font-size: 16px;
    font-weight: 500;
    color: #666;
    margin-bottom: 8px;
  }

  .ant-upload-hint {
    font-size: 14px;
    color: #999;
    margin-bottom: 16px;
  }
}

// Preview image styles
.image-preview {
  .ant-image {
    border-radius: 8px;
    overflow: hidden;

    img {
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.02);
      }
    }
  }
}

// Form container styles
.create-update-image-form {
  .ant-form-item-label {
    font-weight: 500;

    label {
      font-size: 14px;
      color: #333;
    }
  }

  .ant-form-item-required {
    &::before {
      content: "*";
      color: #ff4d4f;
      margin-right: 4px;
    }
  }

  // Responsive form layout
  @media (max-width: 768px) {
    .ant-col-12 {
      flex: 0 0 100%;
      max-width: 100%;
    }
  }
}

// Button styles
.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1.5rem;

  .cta-button {
    min-width: 100px;
    height: 40px;
    border-radius: 6px;
    font-weight: 500;

    &.ant-btn-primary {
      background: #1890ff;
      border-color: #1890ff;

      &:hover {
        background: #40a9ff;
        border-color: #40a9ff;
      }
    }

    &.ant-btn-default {
      border-color: #d9d9d9;

      &:hover {
        border-color: #1890ff;
        color: #1890ff;
      }
    }
  }

  // Responsive button layout
  @media (max-width: 576px) {
    flex-direction: column;
    gap: 0.5rem;

    .cta-button {
      width: 100%;
      margin: 0;
    }
  }
}

// Utility classes for compatibility with Tailwind
.pb-\[16px\] {
  padding-bottom: 16px;
}

.h-64 {
  height: 16rem;
}

.object-cover {
  object-fit: cover;
}

.absolute {
  position: absolute;
}

.top-2 {
  top: 0.5rem;
}

.right-2 {
  right: 0.5rem;
}

.bg-white\/90 {
  background-color: rgba(255, 255, 255, 0.9);
}

.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

.shadow-md {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.hover\:shadow-lg:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.border-gray-300 {
  border-color: #d1d5db;
}

.hover\:bg-white:hover {
  background-color: #ffffff;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-200 {
  transition-duration: 200ms;
}

.upload-overlay-btn.ant-btn-variant-outlined:not(:disabled):not(
    .ant-btn-disabled
  ):hover,
.upload-overlay-btn.ant-btn-variant-dashed:not(:disabled):not(
    .ant-btn-disabled
  ):hover {
  color: inherit !important;
  border-color: inherit !important;
  background: inherit !important;
  box-shadow: none !important;
  height: 28px !important;
}
.ant-btn-variant-outlined:not(:disabled):not(.ant-btn-disabled):hover,
.ant-btn-variant-dashed:not(:disabled):not(.ant-btn-disabled):hover {
  color: inherit !important;
  border-color: inherit !important;
  background: inherit !important;
  box-shadow: none !important;
  // height: 28px !important;
}
