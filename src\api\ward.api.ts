import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const wardApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/ward",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/ward",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/ward/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/ward/${id}`,
      method: "delete",
    }),
};
