import { AxiosPromise } from "axios";
import { request } from "utils/request";

export const dashboardApi = {
  getDataComponent: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/component",
      params,
    }),
  getFavoriteDesigns: (params: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/component/top",
      params,
    }),
  getOptionalDesigns: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/component/order",
      params,
    }),
};
