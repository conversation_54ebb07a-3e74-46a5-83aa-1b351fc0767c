import {
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import {
  Button,
  Card,
  Divider,
  Input,
  message,
  Modal,
  Popconfirm,
  Select,
  Space,
  Spin,
  Table,
  Tag,
  Tooltip,
} from "antd";
import { Pagination } from "components/Pagination";
import React, { useEffect, useState, useRef } from "react";
import { ModalStatus } from "types/modal";
import { QueryParam } from "types/query";
import { Department } from "types/department";
import { getTitle } from "utils";
import { $url } from "utils/url";
import { useDepartment } from "hooks/useDepartment";
import { useNavigate } from "react-router-dom";
import { unixToDate } from "utils/dateFormat";
import { departmentApi } from "api/department.api";
import { useProjectCategory } from "hooks/useProjectCategory";
import { checkRole } from "utils/auth";
import { PermissionNames } from "types/PermissionNames";
import { permissionStore } from "store/permissionStore";

import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import CustomButton from "components/Button/CustomButton";
import CustomInput from "components/Input/CustomInput";
import PencilIcon from "assets/svgs/PencilIcon";
import DeleteIcon from "assets/svgs/DeleteIcon";
import PageTitle from "components/PageTitle/PageTitle";
import {
  DepartmentModal,
  DepartmentModalRef,
} from "./components/DepartmentModal";
import EditButton from "components/Button/EditButton";
import DeleteButton from "components/Button/DeleteButton";
// import { ReactComponent as PencilIcon } from "assets/svgs/pencil.svg";

const { ColumnGroup, Column } = Table;

export const DepartmentPage = ({ title = "" }) => {
  const [loadingDelete, setLoadingDelete] = useState(false);
  // const [isSelectStatus , ]
  const modalRef = useRef<DepartmentModalRef>();

  const { departments, fetchData, loading, query, setQuery, total } =
    useDepartment({
      initQuery: { limit: 10, page: 1 },
    });
  const { fetchData: fetchProjectCate, projectCategories } = useProjectCategory(
    { initQuery: { limit: 100, page: 1 } }
  );
  const navigate = useNavigate();
  useEffect(() => {
    document.title = getTitle(title);
    fetchData();
    fetchProjectCate();
  }, []);

  const handleDeleteDepartment = async (id: number) => {
    try {
      setLoadingDelete(false);
      await departmentApi.delete(id);
      message.success("Xóa thành công");
      fetchData();
    } catch (error) {
    } finally {
      setLoadingDelete(true);
    }
  };
  const handleActiveDepartment = async (id: number, value: boolean) => {
    try {
      setLoadingDelete(false);
      await departmentApi.update(id, { department: { isActive: !value } });
      message.success(value ? "Khóa thành công" : "Mở khóa thành công");
      fetchData();
    } catch (error) {
    } finally {
      setLoadingDelete(true);
    }
  };

  const typeStatus = [
    {
      value: "",
      label: "Tất cả trạng thái",
    },
    {
      value: true,
      label: "Hoạt động",
    },
    {
      value: false,
      label: "Bị khóa",
    },
  ];

  const columns: CustomizableColumn<Department>[] = [
    {
      key: "code",
      title: "Mã phòng ban",
      dataIndex: "code",
      width: 150,
      defaultVisible: true,
      alwaysVisible: true,
    },
    {
      key: "name",
      title: "Tên phòng ban",
      dataIndex: "name",

      // render: (_, record) => (
      // 	<div className="service-cell">

      // 		<div className="service-info">
      // 			<div className="service-name">{record.name}</div>
      // 		</div>
      // 	</div>
      // ),
      defaultVisible: true,
    },

    {
      key: "status",
      title: "Trạng thái",
      align: "center",
      width: 150,
      render: (_, record) => (
        <div className="flex justify-center">
          {record.isActive ? (
            <Tag color="green" className="status-tag m-0">
              Hoạt động
            </Tag>
          ) : (
            <Tag color="red" className="status-tag m-0">
              Bị khóa
            </Tag>
          )}
        </div>
      ),
      defaultVisible: true,
    },
    {
      key: "actions",
      title: "Xử lý",
      width: 100,
      align: "center",
      fixed: "right",
      render: (_, record) => (
        <Space size="small">
          <EditButton
            onClick={() => {
              modalRef?.current?.handleUpdate(record);
            }}
          />

          <DeleteButton
            toolTipContent={record.isActive ? "Khóa" : "Mở khóa"}
            onClick={() => {
              Modal.confirm({
                title: `${record.isActive ? "Khóa" : "Mở khóa"} phòng ban ${
                  record.name
                }`,
                getContainer: () => {
                  return document.getElementById("App") as HTMLElement;
                },
                icon: null,
                content: (
                  <>
                    <div>
                      Khi {record.isActive ? "khóa" : "mở khóa"} phòng ban các
                      thông tin của phòng ban này cũng sẽ được{" "}
                      {record.isActive ? "khóa" : "mở khóa"}.
                    </div>
                    <div>
                      Bạn có chắc chắn muốn{" "}
                      {record.isActive ? "khóa" : "mở khóa"} phòng ban này?
                    </div>
                  </>
                ),
                footer: (_, { OkBtn, CancelBtn }) => (
                  <>
                    <CustomButton
                      variant="outline"
                      className="cta-button"
                      onClick={() => {
                        handleActiveDepartment(record.id, record.isActive);
                        Modal.destroyAll();
                      }}
                    >
                      Có
                    </CustomButton>
                    <CustomButton
                      onClick={() => {
                        Modal.destroyAll();
                      }}
                      className="cta-button"
                    >
                      Không
                    </CustomButton>

                    {/* <OkBtn />
                          <CancelBtn /> */}
                  </>
                ),
              });
            }}
          />
        </Space>
      ),
      defaultVisible: true,
      alwaysVisible: true,
    },
  ];

  const pagination = {
    current: 1,
    pageSize: 10,
    total: departments.length,
    showSizeChanger: true,
  };

  return (
    <div>
      <PageTitle
        title={title}
        breadcrumbs={["Dữ liệu nguồn", title]}
        extra={
          <CustomButton
            size="small"
            showPlusIcon
            onClick={() => {
              modalRef.current?.handleCreate();
              // navigate("/project-group/create-project-group");
            }}
          >
            Tạo phòng ban
          </CustomButton>
        }
      />

      <Card>
        <div className="flex gap-[16px] items-end pb-[16px]">
          <div className="w-[200px]">
            <CustomInput
              label="Tìm kiếm"
              placeholder="Tìm kiếm"
              value={query.search}
              onChange={(value) => {
                query.search = value;
                setQuery({ ...query });
              }}
              inputSearch
            />
          </div>
          <div className="w-[200px]">
            <CustomInput
              label="Trạng thái"
              // placeholder="Tìm kiếm"
              onChange={(value) => {
                query.isActive = value;
                setQuery({ ...query });
              }}
              type="select"
              options={typeStatus}
              value={query.isActive ?? ""}
            />
          </div>
          <Divider
            type="vertical"
            className="h-[68px]"
            style={{ margin: 0, paddingBottom: 8 }}
          />
          <CustomButton onClick={fetchData}>Áp dụng</CustomButton>
        </div>
        <CustomizableTable
          columns={columns}
          dataSource={departments}
          rowKey="id"
          loading={loading}
          pagination={false}
          scroll={{ x: 1200 }}
          bordered
          displayOptions
        />

        <Pagination
          currentPage={query.page}
          defaultPageSize={query.limit}
          total={total}
          onChange={({ limit, page }) => {
            query.page = page;
            query.limit = limit;
            setQuery({ ...query });
          }}
        />
      </Card>
      <DepartmentModal
        ref={modalRef}
        onClose={() => {}}
        onSubmitOk={() => {
          fetchData();
        }}
      />
    </div>
  );
};
