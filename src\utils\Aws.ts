import {
  S3Client,
  PutObjectCommand,
  PutObjectCommandInput,
} from "@aws-sdk/client-s3";
import { CONFIG } from "config";
import { MyHttpHandler } from "plugins/MyHttpHandler";

//key account bmd (aws) <EMAIL>

const bucketName = CONFIG.AWS_S3_BUCKET_NAME;

const myHttpHandler = new MyHttpHandler();

const client = new S3Client({
  credentials: {
    accessKeyId: CONFIG.AWS_ACCESS_KEY,
    secretAccessKey: CONFIG.AWS_SECRET_KEY,
  },
  region: CONFIG.AWS_REGION,
  requestHandler: myHttpHandler,
  endpoint: "https://" + CONFIG.VULTR_HOSTNAME,
});

interface UploadParams {
  file: File;
  key: string;
  onProgress: (percent: number) => void;
  contentType: string;
}

export class AWS {
  static async uploadToS3({
    contentType,
    file,
    key,
    onProgress,
  }: UploadParams) {
    return new Promise((resolve, reject) => {
      const input: PutObjectCommandInput = {
        Bucket: bucketName,
        Key: key,
        ContentType: contentType,
        Body: file,
        ACL: "public-read",
      };
      const sub = myHttpHandler.onProgress$.subscribe((progress) => {
        const percentComplete =
          (progress.progressEvent.loaded / progress.progressEvent.total) * 100;
        onProgress(percentComplete);
      });
      client
        .send(new PutObjectCommand(input))
        .then((data) => {
          sub.unsubscribe();
          resolve(data);
        })
        .catch(() => {
          console.log("error: ");
          reject();
        });
    });
  }
}
