import { Col, Form, Input, message, Modal, Row, Tabs } from "antd";
import { Rule } from "antd/lib/form";
import React, { Key, useEffect, useImperativeHandle, useState } from "react";
import { ModalStatus } from "types/modal";
import { RequiredResource } from "types/requiredResource";
import { DevicePage } from "views/DevicePage/DevicePage";
import { Device, DeviceType } from "types/device";
import { TaskTemplatePage } from "../TaskTemplatePage";
import { TaskTemplate } from "types/taskTemplate";
import { TabRequiredResource } from "./RequiredResourceModal";

export interface ChildTaskTemplateModal {
  handleView: (selectedRow: TaskTemplate[]) => void;
}
interface ChildTaskTemplateModalProps {
  onSubmitOk: (selectedRow: TaskTemplate[]) => void;
  excludeIds?: number[];
  type: string;
}

export const ChildTaskTemplateModal = React.forwardRef(
  ({ onSubmitOk, excludeIds, type }: ChildTaskTemplateModalProps, ref) => {
    const [visible, setVisible] = useState(false);
    const [selectedRows, setSelectedRows] = useState<TaskTemplate[]>([]);

    useImperativeHandle<any, ChildTaskTemplateModal>(
      ref,
      () => ({
        handleView,
      }),
      []
    );

    const handleView = (selectedRows: TaskTemplate[]) => {
      setVisible(true);
      setSelectedRows(selectedRows || []);
    };

    const handleClose = () => {
      setVisible(false);
    };

    const handleChangeRowSelect = (
      selectedRowKeys: Key[],
      selectedRows: TaskTemplate[]
    ) => {
      setSelectedRows(selectedRows);
    };

    return (
      <Modal
        onCancel={handleClose}
        visible={visible}
        title="Chọn công việc con"
        style={{ top: 20 }}
        width={1300}
        onOk={() => {
          handleClose();
          onSubmitOk(selectedRows);
        }}
        cancelButtonProps={{ style: { display: "none" } }}
        okText="Xong"
      >
        <TaskTemplatePage
          showSelect
          rowSelection={{
            type: "checkbox",
            selectedRowKeys: selectedRows.map((e) => e.id),
            onChange: handleChangeRowSelect,
          }}
          excludeIds={excludeIds}
          type={type}
        />
      </Modal>
    );
  }
);
