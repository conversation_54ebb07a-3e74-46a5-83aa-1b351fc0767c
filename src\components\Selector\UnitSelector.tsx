import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
} from "react";
import { debounce, uniqBy } from "lodash";
import { useUnit } from "hooks/useUnit";
import { Unit } from "types/unit";
import { QueryParams2 } from "types/query";
import CustomSelect from "components/Input/CustomSelect";
import { Select } from "antd";

type CustomFormItemProps = {
  value?: number;
  initQuery?: QueryParams2;
  disabled?: boolean;
  selectedUnit?: Unit[];
  multiple?: boolean;
  onChange?: (value: any) => void;
  initOptionItem?: Unit | Unit[];
  valueIsOption?: boolean;
  allowClear?: boolean;
  placeholder?: string;
  style?: React.CSSProperties;
  className?: string;
  size?: "large" | "middle" | "small";
  dropdownStyle?: React.CSSProperties;
};

export interface UnitSelector {
  refresh(): void;
}

export const UnitSelector = forwardRef(
  (
    {
      value,
      onChange,
      initQuery,
      disabled,
      multiple = false,
      selectedUnit,
      initOptionItem,
      valueIsOption,
      allowClear = true,
      placeholder = "Chọn đơn vị tính",
      style,
      className,
      size = "middle",
      dropdownStyle,
    }: CustomFormItemProps,
    ref
  ) => {
    const { units, loading, fetchData, query } = useUnit({
      initQuery: { page: 1, limit: 10, ...initQuery },
    });

    useImperativeHandle<any, UnitSelector>(
      ref,
      () => ({
        refresh() {
          fetchData();
        },
      }),
      []
    );

    useEffect(() => {
      fetchData();
    }, [selectedUnit]);

    const debounceSearch = useCallback(
      debounce((keyword) => {
        query.search = keyword;
        fetchData();
      }, 300),
      [query]
    );

    const options = useMemo(() => {
      let data = [...units];
      if (initOptionItem) {
        if ((initOptionItem as Unit[])?.length) {
          data = data.concat(initOptionItem);
        } else {
          data.push(initOptionItem as Unit);
        }
      }
      return uniqBy(data, (item) => item.id).map((item) => ({
        label: item.name,
        value: item.id,
        item, // lưu nguyên object nếu cần dùng sau
      }));
    }, [units, initOptionItem]);

    const handleChange = (v: any, option: any) => {
      if (valueIsOption) {
        if (option instanceof Array) {
          onChange?.(option.map((opt) => opt.item));
        } else {
          onChange?.(option.item);
        }
      } else {
        onChange?.(v);
      }
    };

    return (
      <Select
        value={value}
        onChange={handleChange}
        disabled={disabled}
        options={options}
        mode={multiple ? "multiple" : undefined}
        allowClear={allowClear}
        placeholder={placeholder}
        onSearch={debounceSearch}
        loading={loading}
        showSearch
        filterOption={false}
        style={style}
        className={className}
        size={size}
        dropdownStyle={dropdownStyle}
      />
    );
  }
);
