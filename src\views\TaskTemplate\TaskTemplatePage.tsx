import { LockOutlined, UnlockOutlined } from "@ant-design/icons";
import {
  Button,
  Card,
  message,
  Modal,
  Select,
  Space,
  Table,
  Tag,
  Tooltip,
} from "antd";
import { TableRowSelection } from "antd/es/table/interface";
import { TableProps } from "antd/lib";
import { taskTemplateApi } from "api/taskTemplate.api";
import PencilIcon from "assets/svgs/PencilIcon";
import CustomButton from "components/Button/CustomButton";
import CustomInput from "components/Input/CustomInput";
import PageTitle from "components/PageTitle/PageTitle";
import { Pagination } from "components/Pagination";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import { useTaskTemplate } from "hooks/useTaskTemplate";
import { Fragment, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { permissionStore } from "store/permissionStore";
import { PermissionNames } from "types/PermissionNames";
import { TaskTemplate, TaskTypeTrans } from "types/taskTemplate";
import { formatVND, getTitle } from "utils";
import {
  checkRole,
  checkRoles,
  filterActionColumnIfNoPermission,
} from "utils/auth";
import { unixToDate } from "utils/dateFormat";
import { MyExcelColumn } from "../../utils/MyExcel";
import { observer } from "mobx-react";
import LockButton from "components/Button/LockButton";
import EditButton from "components/Button/EditButton";

const { ColumnGroup, Column } = Table;

interface TaskTemplatePageProps {
  title?: string;
  rowSelection?: TableRowSelection<TaskTemplate>; // Su dung khi muon select row
  showSelect?: boolean; // Su dung khi muon an page title va nut them moi
  excludeIds?: number[];
  type?: string;
}

export const TaskTemplatePage = observer(
  ({
    title = "",
    rowSelection,
    showSelect,
    excludeIds,
    type,
  }: TaskTemplatePageProps) => {
    const {
      haveAddPermission,
      haveBlockPermission,
      haveEditPermission,
      haveViewAllPermission,
    } = checkRoles(
      {
        add: PermissionNames.taskTemplateAdd,
        edit: PermissionNames.taskTemplateEdit,
        block: PermissionNames.taskTemplateBlock,
        viewAll: PermissionNames.taskTemplateViewAll,
      },
      permissionStore.permissions
    );

    const [loadingExport, setLoadingExport] = useState(false);
    const [loadingDelete, setLoadingDelete] = useState(false);
    // const modalRef = useRef<TaskTemplateModalRef>();

    const exportColumns: MyExcelColumn<TaskTemplate>[] = [
      {
        header: "Tên dịch vụ",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "name",
        columnKey: "name",
      },
      {
        header: "Loại dịch vụ",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "type",
        columnKey: "type",
        render: (record: TaskTemplate) => TaskTypeTrans[record.type]?.label,
      },

      {
        header: "Trạng thái",
        headingStyle: {
          font: {
            bold: true,
          },
        },
        key: "isActive",
        columnKey: "isActive",
        render: (record: TaskTemplate) =>
          record.isActive ? "Hoạt động" : "Bị khóa",
      },
    ];
    const navigate = useNavigate();
    const {
      fetchData,
      loading,
      query,
      taskTemplates,
      setQuery,
      total,
      isEmptyQuery,
    } = useTaskTemplate({
      initQuery: {
        limit: 20,
        page: 1,
        isAdmin: haveViewAllPermission ? true : undefined,
        isActive: showSelect ? true : undefined,
        excludeIds,
        type,
      },
    });

    useEffect(() => {
      document.title = getTitle(title);
      fetchData();
    }, []);

    useEffect(() => {
      if (type) {
        query.type = type;
        setQuery({ ...query });
        fetchData();
      }
    }, [type]);

    const handleDeleteTaskTemplate = async (id: number) => {
      try {
        setLoadingDelete(false);
        await taskTemplateApi.delete(id);
        message.success("Xóa thành công");
        fetchData();
      } catch (error) {
      } finally {
        setLoadingDelete(true);
      }
    };

    const handleActiveTaskTemplate = async (id: number, value: boolean) => {
      try {
        setLoadingDelete(false);
        await taskTemplateApi.update(id, {
          taskTemplate: { isActive: !value },
        });
        message.success(value ? "Khóa thành công" : "Mở khóa thành công");
        fetchData();
      } catch (error) {
      } finally {
        setLoadingDelete(true);
      }
    };

    const handleTableChange: TableProps<any>["onChange"] = (
      pagination,
      filters,
      sorter
    ) => {
      if (!Array.isArray(sorter)) {
        const fieldMap: Record<string, string> = {
          name: "taskTemplate.name",
          code: "taskTemplate.code",
          type: "taskTemplate.type",
          estimateAt: "taskTemplate.estimateAt",
          estimatedCost: "taskTemplate.estimatedCost",
        };
        const columnKey = sorter.field || sorter.column?.key;

        if (!sorter.order) {
          // setSortField(null);
          // setSortOrder(null);
          query.queryObject = undefined;
          setQuery({ ...query });
        } else {
          const order = sorter.order === "ascend" ? "ASC" : "DESC";
          // setSortField("jobCategory.name");
          // setSortOrder(order);
          const field = fieldMap[columnKey as string];

          const newQueryObject = JSON.stringify([
            {
              type: "sort",
              field,
              value: order,
            },
          ]);
          query.queryObject = newQueryObject;
          setQuery({ ...query });
        }
        fetchData();
      } else {
        query.queryObject = undefined;
        setQuery({ ...query });
        fetchData();
      }
    };

    const handleRowClick = (record: TaskTemplate) => {
      if (showSelect) {
        return;
      }
      navigate(
        `/master-data/${PermissionNames.taskTemplateEdit.replace(
          ":id",
          record!.id + ""
        )}`
      );
    };

    const columns: CustomizableColumn<TaskTemplate>[] = [
      {
        key: "code",
        title: "Mã",
        dataIndex: "code",
        width: 180,
        defaultVisible: true,
        alwaysVisible: true,
        sorter: true,
        render: (_, record) => {
          return (
            <div
              className="text-[#1677ff] cursor-pointer"
              onClick={() => handleRowClick(record)}
            >
              {record.code}
            </div>
          );
        },
      },
      {
        key: "name",
        title: "Tên công việc mẫu",
        dataIndex: "name",
        width: 400,

        // render: (_, record) => (
        // 	<div className="service-cell">

        // 		<div className="service-info">
        // 			<div className="service-name">{record.name}</div>
        // 		</div>
        // 	</div>
        // ),
        defaultVisible: true,
        sorter: true,
      },
      {
        key: "type",
        title: "Loại công việc mẫu",
        width: 170,
        dataIndex: "type",

        render: (_, record) => (
          <div className="service-cell">
            <div className="service-info">
              <div className="service-name">
                {TaskTypeTrans[record.type]?.label}
              </div>
            </div>
          </div>
        ),
        defaultVisible: true,
        sorter: true,
      },
      {
        key: "estimateAt",
        title: "Thời gian dự kiến hoàn thành",
        width: 240,
        dataIndex: "estimateAt",

        render: (_, record) => (
          <div className="service-cell">
            <div className="service-info">
              <div className="service-name">
                {record.estimateAt && record?.estimateAt > 0
                  ? unixToDate(record.estimateAt || 0)
                  : ""}
              </div>
            </div>
          </div>
        ),
        defaultVisible: true,
        sorter: true,
      },
      {
        key: "estimatedCost",
        title: "Chi phí dự kiến",
        width: 150,
        align: "right",
        dataIndex: "estimatedCost",

        render: (_, record) => (
          <div className="service-cell">
            <div className="service-info">
              <div className="service-name">
                {formatVND(record.estimatedCost || 0)}
              </div>
            </div>
          </div>
        ),
        defaultVisible: true,
        sorter: true,
      },
      {
        key: "status",
        title: "Trạng thái",
        align: "center",
        width: 150,
        render: (_, record) => (
          <div className="flex justify-center">
            {record.isActive ? (
              <Tag color="green" className="status-tag !mr-0">
                Hoạt động
              </Tag>
            ) : (
              <Tag color="red" className="status-tag !mr-0">
                Bị khóa
              </Tag>
            )}
          </div>
        ),
        defaultVisible: true,
      },
      {
        key: "actions",
        title: "Xử lý",
        align: "center",
        width: 100,
        fixed: "right",
        render: (_, record) => (
          <Space size="small">
            {haveEditPermission && (
              <EditButton
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(
                    `/master-data/${PermissionNames.taskTemplateEdit.replace(
                      ":id",
                      record!.id + ""
                    )}?update=1`
                  );
                }}
              />
            )}
            {haveBlockPermission && (
              <LockButton
                isActive={record.isActive}
                onAccept={() =>
                  handleActiveTaskTemplate(record.id, record.isActive)
                }
                modalTitle={`${
                  record.isActive ? "Khóa" : "Mở khóa"
                } công việc mẫu ${record.name}`}
                modalContent={
                  <>
                    <div>
                      Khi {record.isActive ? "khóa" : "mở khóa"} công việc mẫu
                      các thông tin của công việc mẫu này cũng sẽ được{" "}
                      {record.isActive ? "khóa" : "mở khóa"}.
                    </div>
                    <div>
                      Bạn có chắc chắn muốn{" "}
                      {record.isActive ? "khóa" : "mở khóa"} công việc mẫu này?
                    </div>
                  </>
                }
              />
            )}
          </Space>
        ),
        defaultVisible: true,
        alwaysVisible: true,
      },
    ];

    const pagination = {
      current: 1,
      pageSize: 10,
      total: taskTemplates.length,
      showSizeChanger: true,
    };

    const CardComponent = showSelect ? Fragment : Card; // Neu co select thi khong render card

    return (
      <div className="app-container">
        {!showSelect && (
          <PageTitle
            title={title}
            breadcrumbs={["Dữ liệu nguồn", title]}
            extra={
              haveAddPermission ? (
                <CustomButton
                  size="small"
                  showPlusIcon
                  onClick={() => {
                    // modalRef.current?.handleCreate();
                    navigate(`/master-data/${PermissionNames.taskTemplateAdd}`);
                  }}
                >
                  Tạo công việc mẫu
                </CustomButton>
              ) : null
            }
          />
        )}

        <CardComponent>
          <div className="flex gap-[16px] items-end pb-[12px]">
            <div className="flex gap-[16px] items-end flex-wrap">
              <div className="w-[200px] ">
                <CustomInput
                  tooltipContent="Tìm kiếm theo mã, tên"
                  label="Tìm kiếm"
                  placeholder="Tìm kiếm"
                  value={query.search}
                  onChange={(value) => {
                    query.search = value;
                    setQuery({ ...query });

                    if (!value) {
                      fetchData();
                    }
                  }}
                  onPressEnter={() => {
                    // Pass all parameters including filters to API
                    query.page = 1;
                    setQuery({ ...query });
                    fetchData();
                  }}
                  allowClear

                  // error="Có lỗi xãy ra"
                />
              </div>
              <div className="flex flex-col">
                <b>Loại</b>
                <Select
                  allowClear={true}
                  disabled={showSelect}
                  value={query.type ?? ""}
                  onChange={(value) => {
                    query.type = value || "";
                    setQuery({ ...query });
                  }}
                  options={[
                    { value: "", label: "Tất cả các loại" },
                    ...Object.values(TaskTypeTrans).map((item) => ({
                      label: item.label,
                      value: item.value,
                    })),
                  ]}
                />
              </div>
              <div className="flex flex-col">
                <b>Trạng thái</b>
                <Select
                  disabled={showSelect}
                  value={query.isActive ?? ""}
                  allowClear
                  options={[
                    {
                      value: "",
                      label: "Tất cả trạng thái",
                    },
                    {
                      value: true,
                      label: "Hoạt động",
                    },
                    {
                      value: false,
                      label: "Bị khóa",
                    },
                  ]}
                  onChange={(e) => {
                    if (e === "") {
                      delete query.isActive;
                      setQuery({ ...query });
                    } else {
                      setQuery({ ...query, isActive: e });
                    }
                  }}
                ></Select>
              </div>

              <CustomButton
                onClick={() => {
                  query.page = 1;
                  setQuery({ ...query });
                  fetchData();
                }}
              >
                Áp dụng
              </CustomButton>

              {!isEmptyQuery && (
                <CustomButton
                  variant="outline"
                  onClick={() => {
                    if (!showSelect) {
                      delete query.type;
                      delete query.isActive;
                    }
                    delete query.search;
                    setQuery({ ...query });
                    fetchData();
                  }}
                >
                  Bỏ lọc
                </CustomButton>
              )}
            </div>
          </div>
          <CustomizableTable
            rowSelection={rowSelection}
            columns={filterActionColumnIfNoPermission(columns, [
              haveEditPermission,
              haveBlockPermission,
            ])}
            dataSource={taskTemplates}
            rowKey="id"
            loading={loading}
            pagination={false}
            scroll={{ x: 1200 }}
            bordered
            displayOptions
            //@ts-ignore
            onChange={handleTableChange}
            onRowClick={handleRowClick}
          />

          <Pagination
            currentPage={query.page}
            defaultPageSize={query.limit}
            total={total}
            onChange={({ limit, page }) => {
              query.page = page;
              query.limit = limit;
              setQuery({ ...query });
              fetchData();
            }}
          />
        </CardComponent>
      </div>
    );
  }
);
