import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const goodsApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/goods",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/goods",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/goods/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/goods/${id}`,
      method: "delete",
    }),
};
