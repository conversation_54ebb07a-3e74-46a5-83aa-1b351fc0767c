import {
  Button,
  Checkbox,
  Col,
  Form,
  Image,
  Input,
  message,
  Modal,
  Row,
  Select,
  Tag,
  Upload,
  UploadProps,
} from "antd";
import { Rule } from "antd/lib/form";
import { materialApi } from "api/material.api";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { ModalStatus } from "types/modal";
import { Material } from "types/material";
import { SingleVideoUploadS3 } from "components/Upload/SingleVideoUploadS3";
import { useWatch } from "antd/lib/form/Form";
import { requiredRule } from "utils/validateRule";
import { fromPairs } from "lodash";
import { componentApi } from "api/component.api";
import {
  Component,
  ComponentCreating,
  ComponentShowTypeTrans,
} from "types/component";
import { fileAttachApi } from "api/fileAttach.api";
import ChooseImageModal, {
  ChooseImageModalProps,
  ChooseImageModalRef,
} from "./ChooseImageModal";
import { $url } from "utils/url";
import { useComponent } from "hooks/useComponent";
import { CreatingVariant, Variant } from "types/variant";
import { useMaterial } from "hooks/useMaterial";
import { variantApi } from "api/variant.api";
import ChooseFileFromMenu from "components/Upload/ChooseImageFromMenu";
import { MaterialTypeSelector } from "components/Selector/MaterialTypeSelector";
import { ComponentsSelector } from "components/Selector/ComponentsSelector";
import { MaterialSelector } from "components/Selector/MaterialSelector";

export interface VariantModal {
  handleCreate: () => void;
  handleUpdate: (variant: Variant) => void;
}
interface ComponentModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
  productId?: number;
}

interface MaterialForm extends Material {
  colorId: number;
  materialGroupId: number;
}

export const CreateComponentModal = React.forwardRef(
  ({ onClose, onSubmitOk, productId }: ComponentModalProps, ref) => {
    const [form] = Form.useForm<CreatingVariant>();

    const materialType = useWatch("materialType", form);
    const fileAttachBackImage = useWatch("fileAttachBackImage", form);
    const fileAttachFrontImage = useWatch("fileAttachFrontImage", form);
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");
    const [selectedMaterial, setSelectedMaterial] = useState<Material>();
    const [forFilterMaterial, setForFilterMaterial] = useState();
    const [initComponentOption, setInitComponentOption] = useState<Component>();

    const handleFetchOneComponent = async (id: number) => {
      try {
        const { data } = await componentApi.findOne(id);
        return data;
      } catch (error) {
        console.log(error);
      }
    };
    useImperativeHandle<any, VariantModal>(
      ref,
      () => ({
        handleCreate() {
          form.resetFields();
          setVisible(true);
          setStatus("create");
        },
        handleUpdate(variant: Variant) {
          console.log("variant là", variant);
          form.setFieldsValue({
            ...variant,
            componentId: variant.component?.id,
            materialId: variant.material?.id,
            featureImageShowType: variant.featureImageShowType,
          });
          // handleFetchOneComponent(variant.component?.id).then((res) => {
          setInitComponentOption(variant.component);
          // });
          setSelectedMaterial(variant.material);
          setVisible(true);
          setStatus("update");
        },
      }),
      []
    );
    const createData = async () => {
      const data = form.getFieldsValue();
      setLoading(true);
      try {
        const res = await variantApi.create({
          variant: {
            code: data.code,
            // name: data.name,
            privateName: data.privateName,
            frontImage: data.frontImage,
            backImage: data.backImage,
            featureImageShowType: data.featureImageShowType,
          },
          fileAttachFrontImageId: fileAttachFrontImage?.id,
          fileAttachBackImageId: fileAttachBackImage?.id,
          materialId: data.materialId,
          componentId: data.componentId,
        });
        message.success("Tạo thành công");
        handleClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const valid = await form.validateFields();
      const data = form.getFieldsValue();
      setLoading(true);
      console.log("What is in form", data);
      const { id, ...restData } = data;
      console.log("Rest data là", restData);
      try {
        console.log("Data when update", data);
        const res = await variantApi.update(id, {
          variant: {
            code: data.code,
            // name: data.name,
            privateName: data.privateName,
            frontImage: data.frontImage,
            backImage: data.backImage,
            featureImageShowType: data.featureImageShowType,
          },
          fileAttachFrontImageId: fileAttachFrontImage?.id,
          fileAttachBackImageId: fileAttachBackImage?.id,
          materialId: data.materialId || 0,
          componentId: data.componentId || 0,
        });
        message.success("Cập nhật thành công");
        handleClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    const handleClose = () => {
      onClose();
      setVisible(false);
      form.resetFields();
    };
    const {
      components,
      fetchData,
      query,
      loading: componentLoading,
      setQuery,
      total,
    } = useComponent({
      initQuery: {
        page: 1,
        limit: 10,
      },
    });
    const {
      fetchData: fetchMaterials,
      loading: loadingMaterials,
      materials,
      query: queryMaterial,
      setQuery: setQueryMaterial,
    } = useMaterial({
      initQuery: {
        page: 1,
        limit: 10,
      },
    });
    useEffect(() => {
      fetchData();
      fetchMaterials();
    }, []);
    useEffect(() => {
      console.log("What is material type now", materialType);
      queryMaterial.type = materialType;
      fetchMaterials();
    }, [materialType]);
    const componentShowTypeOptions = Object.values(ComponentShowTypeTrans).map(
      (item) => ({
        label: item.label,
        value: item.value,
      })
    );
    return (
      <Modal
        onCancel={() => {
          onClose?.();
          setVisible(false);
          setSelectedMaterial(undefined);
        }}
        open={visible}
        title={status == "create" ? "Tạo biến thể" : "Cập nhật biến thể"}
        style={{ top: 20 }}
        width={1200}
        confirmLoading={loading}
        onOk={() => {
          form.submit();
        }}
      >
        <Form
          layout="vertical"
          form={form}
          onFinish={() => {
            status == "create" ? createData() : updateData();
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="Ảnh mặt trước"
                name="fileAttachFrontImage"
                rules={[requiredRule]}
              >
                <ChooseFileFromMenu
                  fileUrl={fileAttachFrontImage?.url}
                  onSelectOk={(url, file) => {
                    form.setFieldValue("fileAttachFrontImage", file);
                  }}
                  ratioText="Tỉ lệ 4x5"
                  fileName={fileAttachFrontImage?.name}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Ảnh mặt sau"
                name="fileAttachBackImage"
                rules={[requiredRule]}
              >
                <ChooseFileFromMenu
                  fileUrl={fileAttachBackImage?.url}
                  onSelectOk={(url, file) => {
                    form.setFieldValue("fileAttachBackImage", file);
                  }}
                  ratioText="Tỉ lệ 4x5"
                  fileName={fileAttachBackImage?.name}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="Tên biến thể (Nội bộ)"
                name="privateName"
                rules={[requiredRule]}
              >
                <Input placeholder="Nhập tên biến thể" />
              </Form.Item>
            </Col>
            {/* <Col span={8}>
              <Form.Item
                label="Tên biến thể (Khách hàng xem)"
                name="name"
                rules={[requiredRule]}
              >
                <Input placeholder="" />
              </Form.Item>
            </Col> */}
            <Col span={8}>
              <Form.Item label="Mã biến thể" name="code" rules={[requiredRule]}>
                <Input placeholder="Nhập mã biến thể" />
              </Form.Item>
            </Col>
            {/* <Col span={8}>
              <Form.Item
                label="Loại nguyên vật liệu"
                name="materialType"
                // rules={[requiredRule]}
              >
                <MaterialTypeSelector
                  isJustMaterialType
                  onChange={(name) => {
                    console.log("Khi đổi loại nvl thì hiện", name);
                    setForFilterMaterial(name);
                  }}
                />
              </Form.Item>
            </Col> */}
            <Col span={8}>
              <Form.Item
                label="Mã nguyên vật liệu"
                name="materialId"
                // rules={[requiredRule]}
              >
                {/* <Select
                  allowClear
                  showSearch
                  options={materials.map((material) => ({
                    label: material.code + " - " + material.name,
                    value: material.id,
                  }))}
                /> */}
                <MaterialSelector
                  isGetMaterialCode
                  forFilterMaterial={forFilterMaterial}
                  allowClear
                  initOptionItem={selectedMaterial}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                // label="Ghi chú tiếng việt (dành cho thợ may)"

                label="Truy xuất thông tin mô tả"
                name="featureImageShowType"
                // rules={[requiredRule]}
              >
                <Select
                  allowClear
                  options={componentShowTypeOptions}
                  placeholder="Chọn truy xuất"
                ></Select>
              </Form.Item>
            </Col>

            {/* <Col span={24}>
              <Form.Item
                label="Truy xuất thông tin mô tả"
                name="componentId"
                // rules={[requiredRule]}
              >
                <Input.TextArea rows={2} placeholder="" />
              </Form.Item>
            </Col> */}
            <Col span={24}>
              <Form.Item
                label="Nhóm thành phần"
                name="componentId"
                rules={[requiredRule]}
              >
                <ComponentsSelector
                  initOptionItem={initComponentOption}
                  productId={productId}
                />
              </Form.Item>
            </Col>
            {[
              status === "update" && (
                <Col span={8}>
                  <Form.Item
                    className="hidden"
                    label="id"
                    name="id"
                    rules={[requiredRule]}
                  >
                    <Input placeholder="" />
                  </Form.Item>
                </Col>
              ),
            ]}
          </Row>
        </Form>
      </Modal>
    );
  }
);
