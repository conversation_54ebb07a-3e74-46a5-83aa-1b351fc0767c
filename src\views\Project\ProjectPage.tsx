import CustomButton from "components/Button/CustomButton";
import CardView from "components/CardView/CardView";
import { projectModal as ProjectModal } from "./components/ProjectModal";
import type { projectModal as ProjectModalRef } from "./components/ProjectModal";
import StatisticChart from "./components/StatisticChart";
import { useRef, useState, useEffect } from "react";
import { Card, Row, Col, Checkbox, Spin, DatePicker, Select } from "antd";
import CustomInput from "components/Input/CustomInput";
import CustomDatePicker from "components/Input/CustomDatePicker";
import { Pagination } from "components/Pagination";
import type { Dayjs } from "dayjs";
import { useProject } from "hooks/useProject";
import { userStore } from "store/userStore";
import { checkRole } from "utils/auth";
import { PermissionNames } from "types/PermissionNames";
import { permissionStore } from "store/permissionStore";
import { observer } from "mobx-react";
import { useNavigate } from "react-router-dom";
import { Project, ProjectStatusTrans } from "types/project";
import { appStore } from "store/appStore";
import dayjs from "dayjs";
import QueryLabel from "components/QueryLabel/QueryLabel";
import { getTitle } from "utils";

// ...existing code...
function ProjectPage({ title }: { title: string }) {
  const haveAddPermission = checkRole(
    PermissionNames.projectAdd,
    permissionStore.permissions
  );
  const haveViewAllPermission = checkRole(
    PermissionNames.projectViewAll,
    permissionStore.permissions
  );
  // ...existing code...

  const projectModalRef = useRef<ProjectModalRef>(null);
  const [searchValue, setSearchValue] = useState("");
  const [startDate, setStartDate] = useState<Dayjs | undefined>(undefined);
  const [endDate, setEndDate] = useState<Dayjs | undefined>(undefined);
  const [showCurrentStatus, setShowCurrentStatus] = useState(false);
  const navigate = useNavigate();
  const [status, setStatus] = useState<string | undefined>(undefined);

  const { projects, total, fetchData, loading, setQuery, query, isEmptyQuery } =
    useProject({
      initQuery: {
        page: 1,
        limit: 10,
        isAdmin: haveViewAllPermission ? true : undefined,
        search: "",
      },
    });

  // Watch for query changes and refetch data
  useEffect(() => {
    fetchData();
  }, [query]);

  useEffect(() => {
    document.title = getTitle(title);
  }, []);

  const handleCreateProject = () => {
    projectModalRef.current?.handleCreate();
  };

  const handleModalClose = () => {
    // Handle modal close if needed
  };

  const handleModalSubmitOk = () => {
    // Refresh project list after successful submission
    fetchData();
  };

  const handleSearch = (value: string) => {
    setSearchValue(value);
    // Only trigger search when value is empty (cleared)
    if (value === "") {
      setQuery({ ...query, search: "", page: 1 });
    }
  };

  const handleSearchOnEnter = (value: string) => {
    setQuery({ ...query, search: value, page: 1 });
  };

  const handleStatusChange = (value: string) => {
    setStatus(value);
  };

  const handleStartDateChange = (
    date: Dayjs | null,
    dateString: string | string[]
  ) => {
    setStartDate(date || undefined);
  };

  const handleEndDateChange = (
    date: Dayjs | null,
    dateString: string | string[]
  ) => {
    setEndDate(date || undefined);
  };

  const handleApplyFilter = () => {
    const newQuery: any = {
      ...query,
      search: searchValue,
      page: 1,
    };

    // Add date filters
    if (startDate) {
      // newQuery.fromAt = startDate.startOf("day").unix();
      newQuery.startDate = startDate.startOf("day").format("YYYY-MM-DD");
    } else {
      delete newQuery.startDate;
    }

    if (endDate) {
      // newQuery.toAt = endDate.endOf("day").unix();
      newQuery.endDate = endDate.endOf("day").format("YYYY-MM-DD");
    } else {
      delete newQuery.endDate;
    }

    if (status) {
      newQuery.status = status;
    } else {
      delete newQuery.status;
    }

    setQuery(newQuery);
  };

  const handleClearFilter = () => {
    setSearchValue("");
    setStartDate(undefined);
    setEndDate(undefined);
    setStatus(undefined);

    setShowCurrentStatus(false);

    const newQuery: any = {
      page: 1,
      limit: query.limit,
      search: "",
    };

    // Remove date filters
    delete newQuery.startDate;
    delete newQuery.endDate;

    setQuery(newQuery);
  };

  const pagination = {
    current: query.page,
    pageSize: query.limit,
    total: total,
    showSizeChanger: true,
  };

  const handleNavigateToDetail = async (project: Project) => {
    try {
      appStore.setCurrentProject(project);
      // Navigate sang ProjectDetailPage
      if (appStore.lastUrl) {
        navigate(appStore.lastUrl);
        appStore.lastUrl = "";
      } else {
        navigate(`/project-detail/${project.id}`);
      }
    } catch (error) {
      console.error("Error navigating to project detail:", error);
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-[20px]">
        <div className="font-bold text-2xl">Dự án</div>
        {haveAddPermission && (
          <CustomButton
            size="small"
            showPlusIcon
            onClick={() => {
              navigate(`/project/${PermissionNames.projectAdd}`);
            }}
          >
            Tạo dự án
          </CustomButton>
        )}
      </div>

      {/* Statistics Charts */}
      <StatisticChart className="mb-6" />

      {/* Main Card containing search section and project list */}
      <div className="card-box">
        {/* Search Section */}
        <div className="mb-6">
          {/* Mobile: Search first row */}
          <div className="flex flex-col md:flex-row md:justify-between md:items-center mb-4 gap-4">
            <div className="font-bold text-lg">DANH SÁCH DỰ ÁN</div>

            <div className="w-full md:w-[500px]">
              <CustomInput
                tooltipContent={"Tìm theo mã, tên dự án"}
                placeholder="Tìm kiếm"
                value={searchValue}
                onChange={handleSearch}
                onPressEnter={handleSearchOnEnter}
                allowClear
                shouldPressEnter
              />
            </div>
          </div>

          {/* Filter Section */}
          <div className="flex flex-wrap gap-[16px] items-end pb-[12px] justify-between">
            <div className="flex flex-wrap gap-[16px] items-end max-w-full">
              <div className="w-[200px]">
                <QueryLabel>Ngày bắt đầu</QueryLabel>
                <DatePicker
                  className="w-full"
                  placeholder="Từ ngày"
                  value={startDate}
                  onChange={handleStartDateChange}
                  format="DD/MM/YYYY"
                />
              </div>
              <div className="w-[200px]">
                <QueryLabel>Ngày kết thúc</QueryLabel>
                <DatePicker
                  className="w-full"
                  placeholder="Đến ngày"
                  value={endDate}
                  onChange={handleEndDateChange}
                  format="DD/MM/YYYY"
                />
              </div>
              <div className="w-[200px]">
                <QueryLabel>Tình trạng</QueryLabel>
                <Select
                  className="w-full"
                  placeholder="Chọn tình trạng"
                  allowClear
                  value={status}
                  onChange={handleStatusChange}
                  options={Object.entries(ProjectStatusTrans).map(
                    ([key, value]) => ({
                      value: key,
                      label: value.label,
                    })
                  )}
                />
              </div>

              <CustomButton onClick={handleApplyFilter}>Áp dụng</CustomButton>
              {!isEmptyQuery && (
                <CustomButton variant="outline" onClick={handleClearFilter}>
                  Bỏ lọc
                </CustomButton>
              )}
            </div>
          </div>
        </div>

        {/* Project Cards */}
        <Spin spinning={loading}>
          <div className="space-y-4 bg-[#f5f5f5]">
            {projects.map((project, index) => (
              <div key={project.id}>
                <CardView project={project} onDetail={handleNavigateToDetail} />
              </div>
            ))}
          </div>
        </Spin>

        <Pagination
          currentPage={query.page}
          defaultPageSize={query.limit}
          total={total}
          onChange={({ limit, page }) => {
            query.page = page;
            query.limit = limit;
            setQuery({ ...query });
            fetchData();
          }}
        />
      </div>

      <ProjectModal
        ref={projectModalRef}
        onClose={handleModalClose}
        onSubmitOk={handleModalSubmitOk}
      />
    </div>
  );
}

export default observer(ProjectPage);
