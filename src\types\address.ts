export interface District {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  priority: number;
  parentCode: string;
  code: string;
  pathWithType: string;
  path: string;
  nameWithType: string;
  type: string;
  slug: string;
  name: string;
  otherName: string;
  isBlock: boolean;
  shipFee: number;
  deliveryDay: number; // số ngày ước tính giao hàng
}

export interface Ward {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  priority: number;
  parentCode: string;
  code: string;
  pathWithType: string;
  path: string;
  nameWithType: string;
  type: string;
  slug: string;
  name: string;
}

export interface City {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  name: string;
  slug: string;
  type: string;
  nameWithType: string;
  code: string;
  image: string;
  priority: number;
  isHighlight: boolean;
  shipFee: number;
  deliveryDay: number; // số ngày ước t<PERSON>h giao hàng
  // custom
}

export enum Country {
  Vn = "VN",
}

export enum Currency {
  Usd = "USD",
  Vnd = "VND",
}

export const CurrencyTrans = {
  [Currency.Usd]: {
    label: "$",
    value: Currency.Usd,
  },
  [Currency.Vnd]: {
    label: "VNĐ",
    value: Currency.Vnd,
  },
};

export const CountryTrans = {
  [Country.Vn]: {
    label: "Việt Nam",
    value: Country.Vn,
  },
};

export interface AddressParam {
  parentCode?: string;
}

export interface AddressData {
  district?: District;
  city?: City;
  ward?: Ward;
}

export interface CoordAddress {
  lat: number;
  lng: number;
  address?: string;
  tmpCity?: string;
  tmpDistrict?: string;
  tmpWard?: string;
  tmpStreet?: string;
  placeId?: string;
  rate?: number;
  totalRate?: number;
  rateWant?: number;
  name?: string;
  mapUrl?: string;
  countReviews?: { count: number; star: number }[];
}
