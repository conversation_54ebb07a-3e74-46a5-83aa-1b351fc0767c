import { dictionaryApi } from "api/dictionary.api";
import { AxiosPromise } from "axios";
import { useState } from "react";
import { Dictionary, DictionaryType } from "types/dictionary";
import { QueryParam } from "types/query";
import { isTypeTreeData } from "utils/common";
import { getListByKey } from "utils/data";

export interface DictionaryQuery extends QueryParam {
  type?: DictionaryType;
}

interface UseDictionaryProps {
  initQuery: DictionaryQuery;
}

export const useDictionary = ({ initQuery }: UseDictionaryProps) => {
  const [data, setData] = useState<Dictionary[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<DictionaryQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await dictionaryApi.findAll(query);

      setData(data.dictionaries);
      setTotal(data.total);

      return data.dictionaries as Dictionary[];
    } finally {
      setLoading(false);
    }
  };

  return { dictionaries: data, total, fetchData, loading, setQuery, query, setData, setLoading };
};

/**
 * Lấy danh sách tên từ API
 * @param api API
 * @param query Query
 * @param dataKey Key của data
 * @returns Danh sách tên
 */
export const getListNameByApi = async ({
  api,
  query,
  dataKey,
  propKey = 'name'
}: {
  api: (params?: any) => AxiosPromise<any>,
  query?: Partial<QueryParam>,
  dataKey: string,
  propKey?: string
}) => {
  const list: any[] = [];
  let total = 0;

  do {
    const { data } = await api({ limit: 100, page: 1, isActive: true, ...query });
    list.push(...data[dataKey]);
    total = data.total;
  } while (total > list.length);

  if (isTypeTreeData(query?.type)) {
    const newData = flatChildrenList(list)
    return getListByKey(newData, propKey) as string[];
  }

  return getListByKey(list, propKey) as string[];
};

/**
 * Lấy danh sách tên từ API theo loại Dictionary
 * @param type Loại Dictionary
 * @returns Danh sách tên
 */
export const getListNameByTypeDictionary = async (type: DictionaryType) => {
  return getListNameByApi({
    api: dictionaryApi.findAll,
    query: { type },
    dataKey: "dictionaries"
  })
};

export function flatChildrenList(items: any[]): any[] {
  return items.flatMap((item) => {
    const node = {
      ...item
    };

    const children = item.children?.length
      ? flatChildrenList(item.children)
      : [];

    return [node, ...children,];
  });
}