import { City, Country, Currency, District, Ward } from "./address";
import { Dictionary } from "./dictionary";
import { FileAttach } from "./fileAttach";
import { MaterialGroup } from "./materialGroup";

export enum ProviderModule {
  Supplier = "SUPPLIER", // là nhà cung cấp
  SubContractor = "SUBCONTRACTOR", // thầu phụ
}

export enum ProviderType {
  Company = "COMPANY", // công ty
  Person = "PERSON", // cá nhân
}

export const ProviderTypeTrans = {
  [ProviderType.Company]: {
    label: "Công ty",
    value: ProviderType.Company,
  },
  [ProviderType.Person]: {
    label: "Cá nhân",
    value: ProviderType.Person,
  },
};

export interface AccountGroup {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  name: string;
  isActive: boolean;
  parent: AccountGroup;
  children: AccountGroup[];
}

export interface Provider {
  id: number;
  files: string;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  code: string; // Mã NCC
  module: ProviderModule;
  name: string; // Tên NCC (*)
  aliasName?: string; // Tên khác
  type: ProviderType; // Loại NCC (*)
  accountGroup: AccountGroup; // Nhóm tài khoản (*)
  materialGroups: MaterialGroup[];
  productGroup?: string; // Nhóm sản phẩm
  phone1?: string; // Điện thoại 1
  phone2?: string; // Điện thoại 2
  fax?: string; // Fax
  email?: string; // Email
  address: string; // Địa chỉ (*)
  website?: string; // Website
  logo: string; // Logo
  country: Dictionary; // Quốc gia
  areaCode?: string; // Mã vùng
  paymentMethod?: string; // Phương thức thanh toán
  paymentTerms?: string; // Điều khoản thanh toán
  currency: Currency; // Loại tiền tệ
  taxNumber?: string; // Mã số thuế
  bankName1?: string; // Tên ngân hàng
  bankName2?: string; // Tên ngân hàng

  bankAccount1?: string; // Số tài khoản 1
  bankAccount2?: string; // Số tài khoản 2
  salutation?: string; // Danh xưng
  contactName?: string; // Tên người liên hệ
  contactPhone?: string; // Điện thoại
  contactMobile?: string; // Điện thoại DĐ
  contactEmail?: string; // Email (liên hệ)
  contactAddress?: string; // Địa chỉ (liên hệ)
  isActive: boolean;
  ward: Ward;
  district: District;
  city: City;
  description: string;
  workTypes?: Dictionary[]
  specialization?: string
  staffCount?: number
  averageCompletionTime?: number
  startDate?: string
  providerCategory?: Dictionary
  fileAttaches: FileAttach[]
}
