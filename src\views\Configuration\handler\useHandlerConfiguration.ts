import { Form, message } from "antd";
import { configurationApi } from "api/configuration.api";
import { useFetchTableData } from "hooks/useFetchTableData";
import { useState } from "react";
import { Configuration } from "types/configuration";
import { QueryParam } from "types/query";

interface Props {
  initQuery?: QueryParam
}

export function useHandlerConfiguration({ initQuery = {} as QueryParam }: Props) {
  const [form] = Form.useForm<Configuration>();
  const [dataDetail, setDataDetail] = useState<Configuration>({} as Configuration)
  const fetch = useFetchTableData<Configuration>({
    initQuery,
    queryFunc: async (query) => {
      const res = await configurationApi.findAll(query);
      return { data: res.data.configurations, total: res.data.total }
    },
    createFunc: async () => {
      await form!.validateFields();
      const data = {
        configuration: form!.getFieldsValue(),
      };
      await configurationApi.create(data);
      message.success("Tạo mới thành công!");
    },
    editFunc: async (id) => {
      await form!.validateFields();
      const data = {
        configuration: form!.getFieldsValue(),
      };
      await configurationApi.update(id, data);
      message.success("Cập nhật thành công!");
    }
  })

  const findOne = async (param: string) => {
    try {
      const res = await configurationApi.findOne({ param })
      setDataDetail(res.data)
    } finally {
    }
  }

  return { ...fetch, form, findOne, dataDetail }
}
