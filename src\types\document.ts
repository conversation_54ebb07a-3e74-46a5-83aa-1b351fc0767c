import { ApprovalList } from "./approvalList";
import { Department } from "./department";
import { Dictionary } from "./dictionary";
import { FileAttach } from "./fileAttach";
import { Project } from "./project";
import { Staff } from "./staff";

export interface Document {
  id: number
  createdAt: number
  updatedAt: number
  deletedAt: number
  isDeleted: boolean
  name: string
  isActive: boolean
  code: string
  path: string
  module: DocumentModule
  description: string
  type: DocumentType
  createdDate: string // y-m-d 
  releaseDate: string // y-m-d 
  fileAttaches: FileAttach[]
  documentCategory: Dictionary
  DocumentItem: Dictionary
  department: Dictionary | null
  project: Project | null
  createdBy: Staff
  updatedBy: Staff
  category: Dictionary | null // sai cho master data;
  approvalLists: ApprovalList[];
}


export interface DocumentCategory {
  id: number;
  name: string;
  code: string;
  description: string;
  isActive: boolean;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
}

export enum DocumentType {
  Project = "PROJECT", // dùng cho dự án
  Document = "DOCUMENT", // Loại tài liệu dùng cho master data
}


export enum DocumentModule {
  File = 'FILE',
  Folder = 'FOLDER',
}

export enum DocumentStatus {
  Draft = "DRAFT",
  Pending = "PENDING",
  Approved = "APPROVED",
  Rejected = "REJECTED",
  WaitingApprove = "WAITING_APPROVE",
}

export function getOverallApprovalStatus(
  approvalLists: any[]
): DocumentStatus {
  if (!approvalLists || approvalLists.length === 0)
    return DocumentStatus.Draft;
  if (approvalLists.some((item) => item.status === "REJECTED"))
    return DocumentStatus.Rejected;

  // Nếu tất cả đều APPROVED
  if (approvalLists.every((item) => item.status === "APPROVED"))
    return DocumentStatus.Approved;

  // Nếu tất cả đều PENDING
  if (approvalLists.every((item) => item.status === "PENDING"))
    return DocumentStatus.Draft;

  // Nếu chỉ có CREATE là APPROVED, các bước sau đều PENDING => WAITING_APPROVE
  const firstStep = approvalLists[0];
  const restSteps = approvalLists.slice(1);
  if (
    firstStep &&
    firstStep.name === "CREATE" &&
    firstStep.status === "APPROVED" &&
    restSteps.every((item) => item.status === "PENDING")
  ) {
    return DocumentStatus.WaitingApprove;
  }

  // Nếu có ít nhất 1 bước APPROVE đã duyệt => Đang thực hiện
  if (
    approvalLists.some(
      (item, idx) =>
        idx > 0 && item.name === "APPROVE" && item.status === "APPROVED"
    )
  ) {
    return DocumentStatus.Pending;
  }

  // Nếu có ít nhất 1 bước PENDING => Đang thực hiện
  if (approvalLists.some((item) => item.status === "PENDING"))
    return DocumentStatus.Pending;

  return DocumentStatus.Draft;
}

export const ProgressDocumentStatus = {
  [DocumentStatus.Draft]: {
    progress: 10,
    label: "Chưa bắt đầu",
    color: "#3949AB",
    value: DocumentStatus.Draft,
  },
  [DocumentStatus.WaitingApprove]: {
    progress: 30,
    label: "Chờ duyệt",
    color: "#B9C3C5",
    value: DocumentStatus.WaitingApprove,
  },
  [DocumentStatus.Pending]: {
    progress: 50,
    label: "Đang thực hiện",
    color: "#FFB300",
    value: DocumentStatus.Pending,
  },
  [DocumentStatus.Approved]: {
    progress: 100,
    label: "Hoàn thành",
    color: "#43A047",
    value: DocumentStatus.Approved,
  },
  [DocumentStatus.Rejected]: {
    progress: 100,
    label: "Bị từ chối",
    color: "#E53935",
    value: DocumentStatus.Rejected,
    showProgressBar: false,
  },
};

export enum FileFormat {
  Image = "image/png",
  Pdf = "application/pdf"
}
