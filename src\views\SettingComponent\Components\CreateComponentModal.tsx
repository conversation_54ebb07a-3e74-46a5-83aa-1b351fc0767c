import {
  Button,
  Checkbox,
  Col,
  Form,
  Image,
  Input,
  message,
  Modal,
  Row,
  Select,
  Upload,
  UploadProps,
} from "antd";
import { Rule } from "antd/lib/form";
import { materialApi } from "api/material.api";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import React, { useEffect, useImperativeHandle, useRef, useState } from "react";
import { ModalStatus } from "types/modal";
import { Material } from "types/material";
import { SingleVideoUploadS3 } from "components/Upload/SingleVideoUploadS3";
import { useWatch } from "antd/lib/form/Form";
import { FileUpload } from "components/Upload/FileUpload";
import { CiImageOn } from "react-icons/ci";
import { FileImageOutlined } from "@ant-design/icons";
import { InputNumber } from "components/Input/InputNumber";
import { SeasonTrans } from "types/season";
import { MaterialType } from "types/materialType";
import { requiredRule } from "utils/validateRule";
import { ColorSelector } from "components/Selector/ColorSelector";
import { fromPairs } from "lodash";
import { componentApi } from "api/component.api";
import {
  Component,
  ComponentCreating,
  ComponentShowType,
  ComponentShowTypeTrans,
  DisplayImageTypeTrans,
} from "types/component";
import { fileAttachApi } from "api/fileAttach.api";
import ChooseImageModal, {
  ChooseImageModalProps,
  ChooseImageModalRef,
} from "./ChooseImageModal";
import { $url } from "utils/url";
import { useComponent } from "hooks/useComponent";
import ChooseFileFromMenu from "components/Upload/ChooseImageFromMenu";
import { ProductSelector } from "components/Selector/ProductSeletor";
import { Product } from "types/product";
import {
  ComponentsSelector,
  ComponentsSelectorRef,
} from "components/Selector/ComponentsSelector";
import { MainComponentSelector } from "components/Selector/MainComponentSelector ";
import { useMaterialType } from "hooks/useMaterialType";
import { MaterialTypeSelector } from "components/Selector/MaterialTypeSelector";
import { BMDTextArea } from "components/TextArea/BMDTextArea";

export interface ComponentModal {
  handleCreate: (productId: number) => void;
  handleUpdate: (component: Component, productId: number) => void;
}
interface ComponentModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
}

interface MaterialForm extends Material {
  colorId: number;
  materialGroupId: number;
}

export const CreateComponentModal = React.forwardRef(
  ({ onClose, onSubmitOk }: ComponentModalProps, ref) => {
    const [form] = Form.useForm<ComponentCreating>();

    const video = useWatch("video", form);
    const fileAttachAvatar = useWatch("fileAttachAvatar", form);
    const fileAttachAvatarBack = useWatch("fileAttachAvatarBack", form);
    const fileAttachFeatureImage = useWatch("fileAttachFeatureImage", form);

    const thumbnail = useWatch("thumbnail", form);

    const [loading, setLoading] = useState(false);
    const [loadingForInitOption, setLoadingForInitOption] =
      useState<boolean>(false);
    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");
    const isEmbroideryForm = useWatch("isEmbroidery", form);
    const [isFrontOrBack, setIsFrontOrBack] = useState(false);
    const [productIdForGetMaiComponent, setProductIdForGetMaiComponent] =
      useState<number>();
    const [whatIsImageFor, setWhatIsImageFor] = useState<
      "front" | "back" | "feature"
    >();
    const [componentForGrouping, setComponentForGrouping] =
      useState<Component[]>();
    const [depGroupInitOption, setDepGroupInitOption] = useState<Component>();
    const [exGroupInitOption, setExGroupInitOption] = useState<Component[]>();
    const [parentGroupInitOption, setParentGroupInitOption] =
      useState<Component>();
    const [componentGroupInitOption, setComponentGroupInitOption] =
      useState<Component>();

    const [materialTypeInitOption, setMaterialTypeInitOption] =
      useState<MaterialType>();

    const parentComponentsSelectorRef = useRef<ComponentsSelectorRef>();

    const handleFetchOneComponent = async (id: number) => {
      try {
        setLoadingForInitOption(true);
        if (id) {
          const { data } = await componentApi.findOne(id);
          return data;
        } else {
          return {};
        }
      } catch (error) {
        console.log(error);
      } finally {
        setLoadingForInitOption(false);
      }
    };
    useImperativeHandle<any, ComponentModal>(
      ref,
      () => ({
        handleCreate(productId) {
          form.resetFields();
          form.setFieldValue("productId", productId);
          setVisible(true);
          setStatus("create");
          setProductIdForGetMaiComponent(productId);
        },
        handleUpdate(component: Component, productId) {
          console.log("Component là", component);
          form.setFieldsValue({
            ...component,
            depComponents:
              component?.depComponents.map((item) => item.component2?.id) || [],
            excludeComponents:
              component?.excludeComponents.map((item) => item.component2?.id) ||
              [],
            parentId: component?.parent?.id,
            productId,
            componentGroupId: component.componentGroup?.id || undefined,
            materialTypeId: component?.materialType?.id,
          });
          setProductIdForGetMaiComponent(productId);
          // handleFetchOneComponent(component.componentGroup?.id).then((res) => {
          setComponentGroupInitOption(component?.componentGroup!);
          // });
          component.depComponents?.map((item) => {
            // handleFetchOneComponent(item.component2?.id).then((res) => {
            //   console.log("What is in this ", res);
            setDepGroupInitOption(item.component2);
            //   });
          });
          component.excludeComponents?.map((item) => {
            // handleFetchOneComponent(item.component2?.id).then((res) => {
            //   console.log("What is in this ", res);
            // });
            setExGroupInitOption((prev) => [...(prev || []), item.component2]);
          });
          // handleFetchOneComponent(component.parent?.id).then((res) => {
          //   setParentGroupInitOption(res);
          // });
          setParentGroupInitOption(component?.parent!);
          setVisible(true);
          setStatus("update");
        },
      }),
      []
    );
    const createData = async () => {
      const valid = await form.validateFields();
      const data = form.getFieldsValue();

      console.log("What is in form", data);
      //   setLoading(true);
      console.log(
        "Data excldudes and deops",
        data.excludeComponents,
        data.depComponents
      );

      const {
        parentId,
        excludeComponents,
        depComponents,
        fileAttachAvatar,
        fileAttachAvatarBack,
        fileAttachFeatureImage,
        componentGroupId,
        productId,
        materialTypeId,
        ...restData
      } = data;

      try {
        const res = await componentApi.create({
          component: {
            // code: data.code,
            // name: data.name,
            // privateName: data.privateName,
            // desc: data.desc,
            // avatar: data.avatar,
            // avatarBack: data.avatarBack,
            // extraPrice: data.extraPrice,
            // isEmbroidery: data.isEmbroidery,
            // isDefault: data.isDefault,
            // featureImage: data.featureImage,
            ...restData,
          },
          fileAttachAvatarId: fileAttachAvatar?.id,
          fileAttachAvatarBackId: fileAttachAvatarBack?.id,
          fileAttachFeatureImageId: fileAttachFeatureImage?.id,
          parentId: null,
          componentExcludeIds: data.excludeComponents || [],
          componentDepIds: data.depComponents || [],
          productId,
          componentGroupId: componentGroupId || undefined,
          materialTypeId: materialTypeId,
        });

        message.success("Tạo thành công");

        handleClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const valid = await form.validateFields();
      const data = form.getFieldsValue();
      setLoading(true);
      console.log("What is in form", data);
      const {
        excludeComponents,
        depComponents,
        parentId,
        id,
        fileAttachAvatar,
        fileAttachAvatarBack,
        fileAttachFeatureImage,
        componentGroupId,
        materialTypeId,
        ...restData
      } = data;
      console.log("Rest data là", restData);
      try {
        console.log("Data when update", data);
        const res = await componentApi.update(id, {
          component: { ...restData },
          fileAttachAvatarId: fileAttachAvatar?.id,
          fileAttachAvatarBackId: fileAttachAvatarBack?.id,
          fileAttachFeatureImageId: fileAttachFeatureImage?.id,
          componentExcludeIds: data.excludeComponents || [],
          componentDepIds: data?.depComponents || [],
          parentId: data.parentId || 0,
          componentGroupId: componentGroupId || 0,
          materialTypeId: materialTypeId,
        });
        message.success("Cập nhật thành công");
        handleClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    const handleClose = () => {
      onClose();
      setVisible(false);
      form.resetFields();
    };
    const chooseImageModalRef = React.useRef<ChooseImageModalRef>(null);
    const {
      components,
      fetchData,
      query,
      loading: componentLoading,
      setQuery,
      total,
    } = useComponent({
      initQuery: {
        page: 1,
        limit: 10,
      },
    });
    useEffect(() => {
      fetchData();
    }, []);
    // useEffect(()=>{
    const componentShowTypeOptions = Object.values(ComponentShowTypeTrans).map(
      (item) => ({
        label: item.label,
        value: item.value,
      })
    );

    // },[])

    return (
      <Modal
        onCancel={() => {
          onClose?.();
          setVisible(false);
          setDepGroupInitOption(undefined);
          setExGroupInitOption([]);
          setParentGroupInitOption(undefined);
          setComponentGroupInitOption(undefined);
        }}
        destroyOnClose
        open={visible}
        title={status == "create" ? "Tạo thành phần" : "Cập nhật thành phần"}
        style={{ top: 20 }}
        width={1200}
        confirmLoading={loading}
        onOk={() => {
          status == "create" ? createData() : updateData();
        }}
      >
        <Form layout="vertical" form={form}>
          <Row gutter={16}>
            {/* <Col span={12}>
              <Form.Item
                label="Ảnh mặt trước"
                name="fileAttachAvatar"
                rules={[requiredRule]}
              >
                <ChooseFileFromMenu
                  fileUrl={fileAttachAvatar?.url}
                  onSelectOk={(url, file) => {
                    form.setFieldValue("fileAttachAvatar", file);
                  }}
                  ratioText="Tỉ lệ 1x1"
                  fileName={fileAttachAvatar?.name}
                />
              </Form.Item>
            </Col> */}
            {/* <Col span={12}>
              <Form.Item
                label="Ảnh mặt sau"
                name="fileAttachAvatarBack"
                rules={[requiredRule]}
              >
                <ChooseFileFromMenu
                  fileUrl={fileAttachAvatarBack?.url}
                  onSelectOk={(url, file) => {
                    form.setFieldValue("fileAttachAvatarBack", file);
                  }}
                  ratioText="Tỉ lệ 1x1"
                  fileName={fileAttachAvatarBack?.name}
                />
              </Form.Item>
            </Col> */}
            <Col span={24}>
              <Form.Item
                label="Ảnh feature"
                name="fileAttachFeatureImage"
                rules={[requiredRule]}
              >
                <ChooseFileFromMenu
                  fileUrl={fileAttachFeatureImage?.url || ""}
                  onSelectOk={(url, file) => {
                    form.setFieldValue("fileAttachFeatureImage", file);
                  }}
                  ratioText="Tỉ lệ 1x1"
                  fileName={fileAttachFeatureImage?.name}
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <h2>Thông tin thành phần</h2>
            </Col>
            <Col span={8}>
              <Form.Item
                label="Sản phẩm"
                name="productId"
                rules={[requiredRule]}
              >
                <ProductSelector disabled allowClear />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="Tên nội bộ"
                name="privateName"
                rules={[requiredRule]}
              >
                <Input placeholder="" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="Tên hiển thị"
                name="name"
                rules={[requiredRule]}
              >
                <Input placeholder="" />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item
                label="Mã thành phần"
                name="code"
                rules={[requiredRule]}
              >
                <Input placeholder="" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="Giá cộng thêm ($)"
                name="extraPrice"
                // rules={[requiredRule]}
              >
                <InputNumber />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                // label="Ghi chú tiếng việt (dành cho thợ may)"
                label="Truy xuất thông tin mô tả"
                name="featureImageShowType"
                rules={[requiredRule]}
              >
                <Select allowClear options={componentShowTypeOptions}></Select>
              </Form.Item>
            </Col>
            <Col span={8} className="w-full flex">
              <Form.Item
                className="w-1/7"
                label="Thêu"
                name="isEmbroidery"
                valuePropName="checked"
              >
                <Checkbox />
              </Form.Item>
              <Form.Item
                className="flex-1"
                label="Số ký tự tối đa khi thêu"
                name="maxLength"
                // rules={[requiredRule]}
              >
                <Input
                  placeholder=""
                  disabled={!isEmbroideryForm}
                  type="number"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="Vị trí" name="position" rules={[requiredRule]}>
                <InputNumber />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="Định mức nguyên vật liệu" name="standard">
                <InputNumber />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="Nguyên vật liệu sử dụng"
                name="materialTypeId"
                rules={[requiredRule]}
              >
                <MaterialTypeSelector
                  initOptionItem={materialTypeInitOption}
                  valueIsOption
                  onChange={(materialType) => {
                    console.log("Select material:", materialType);
                    form.setFieldValue("materialTypeId", materialType?.id);
                  }}
                />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item
                label="Thành phần mặc định của nhóm"
                name="isDefault"
                valuePropName="checked"
              >
                <Checkbox />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item
                label="Mô tả"
                name="desc"
                //  rules={[requiredRule]}
              >
                <BMDTextArea placeholder="" />
              </Form.Item>
            </Col>
            {[
              status === "update" && (
                <Col span={8}>
                  <Form.Item
                    className="hidden"
                    label="id"
                    name="id"
                    rules={[requiredRule]}
                  >
                    <Input placeholder="" />
                  </Form.Item>
                </Col>
              ),
            ]}
            <Col span={24}>
              <h2>Thông tin nhóm</h2>
            </Col>
            <Col span={12}>
              <Form.Item label="Nhóm thành phần chính" name="componentGroupId">
                <MainComponentSelector
                  placeholder="Chọn nhóm thành phần chính"
                  isMainComponent
                  productId={productIdForGetMaiComponent}
                  initOptionItem={componentGroupInitOption}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Nhóm điều kiện" name="depComponents">
                {/* <Select
                  allowClear
                  options={components.map((component) => ({
                    label: component.code,
                    value: component.id,
                  }))}
                /> */}
                <ComponentsSelector
                  key={"depComponents"}
                  placeholder="Chọn nhóm điều kiện"
                  initOptionItem={depGroupInitOption}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Nhóm loại trừ" name="excludeComponents">
                {/* <Select
                  allowClear
                  mode="multiple"
                  options={components.map((component) => ({
                    label: component.code,
                    value: component.id,
                  }))}
                /> */}
                <ComponentsSelector
                  key={"excludeComponents"}
                  placeholder="Chọn nhóm loại trừ"
                  multiple
                  initOptionItem={exGroupInitOption}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Nhóm cha"
                name="parentId"
                // rules={[requiredRule]}
              >
                {/* <Select
                  allowClear
                  options={components.map((component) => ({
                    label: component.code,
                    value: component.id,
                  }))}
                /> */}
                <ComponentsSelector
                  key={"parentId"}
                  ref={parentComponentsSelectorRef}
                  loading={loadingForInitOption}
                  placeholder="Chọn nhóm cha"
                  initOptionItem={parentGroupInitOption}
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
        <ChooseImageModal
          onChoose={(file) => {
            console.log("File nhận ở create modal là", file);
            switch (whatIsImageFor) {
              case "front":
                form.setFieldsValue({
                  avatar: $url(file.url),
                });
                break;

              case "back":
                form.setFieldsValue({
                  avatarBack: $url(file.url),
                });
                break;
              case "feature":
                form.setFieldsValue({
                  featureImage: $url(file.url),
                });
                break;
              default:
                break;
            }
            message.success("Chọn ảnh thành công");
          }}
          ref={chooseImageModalRef}
        />
      </Modal>
    );
  }
);
