import { Device } from "./device";
import { Dictionary } from "./dictionary";
import { FileAttach } from "./fileAttach";
import { Material } from "./material";
import { MemberShip } from "./memberShip";
import { Staff } from "./staff";
import { Task } from "./task";
import { Unit } from "./unit";

export enum DailyLogDetailType {
  Task = "TASK",
  Material = "MATERIAL",
  Staff = "STAFF",
  Machine = "MACHINE",
}

export enum DailyLogType {
  Daily = "DAILY",
  Weekly = "WEEKLY",
  Monthly = "MONTHLY",
}

export const DailyLogTypeOptions = [
  { value: DailyLogType.Daily, label: "Báo cáo ngày" },
  { value: DailyLogType.Weekly, label: "Báo cáo tuần" },
  { value: DailyLogType.Monthly, label: "Báo cáo tháng" },
];

export interface DailyLogDetail {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  [DailyLogDetailType.Task]: "CV";
  code: string;
  labor: string; // nhân công
  type: DailyLogDetailType;
  volume: number; // khoi luong
  percent: number; // phần trăm
  quantity: number;
  workHour: number;
  dailyLog: DailyLog;
  dailyMaterialLog: DailyLog;
  dailyStaffLog: DailyLog;
  dailyMachineLog: DailyLog;
  task: Task;
  materialGroup: Dictionary | null;
  deviceCategory: Dictionary | null;
  material: Material;
  device: Device;
  unit: Unit;
}

export interface DailyLog {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  // DailyLogStatus dùng cho quy trình duyệt (nếu cần)
  type: DailyLogType;
  startAt: number;
  endAt: number;
  // thời tiết
  morningTemp: number;
  morningRain: string;
  noonTemp: number;
  noonRain: string;
  eveningTemp: number;
  eveningRain: string;
  note: string;
  incidentWarning: string;
  // RELATION
  createdBy: Staff;
  reportMemberShip: MemberShip; // người báo cáo
  fileAttaches: FileAttach[];
  dailyLogTaskDetails: DailyLogDetail[]; // dùng cho công việc
  dailyLogMaterialDetails: DailyLogDetail[]; // dùng cho nguyên vật liệu
  dailyLogStaffDetails: DailyLogDetail[]; // dùng cho nhân công
  dailyLogMachineDetails: DailyLogDetail[]; // dùng cho máy móc
  updatedBy: Staff;
}
