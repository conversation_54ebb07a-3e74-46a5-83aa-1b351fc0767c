import { Config<PERSON>rovider, theme } from "antd";
import { useOneSignalContext } from "context/OneSignalContext";
import { toJS } from "mobx";
import { observer } from "mobx-react";
import { useEffect } from "react";
import { useRoutes } from "react-router-dom";
import { settings } from "settings";
import { userStore } from "store/userStore";
import { initUUID } from "utils/devide";
import { routes } from "./router";
import "./styles/Theme.scss";
import "./styles/App.scss";
import "./styles/AntDesign.scss";
import "./styles/Syncfusion.scss";
import "./styles/Bootstrap5.scss";
import "./styles/TextAreaAnt.scss";
import "dayjs/locale/vi";
import { viVNLocale } from "utils/locale";
import viVn from "antd/locale/vi_VN";
import dayjs from "dayjs";
import advancedFormat from "dayjs/plugin/advancedFormat";
import customParseFormat from "dayjs/plugin/customParseFormat";
import localeData from "dayjs/plugin/localeData";
import weekday from "dayjs/plugin/weekday";
import weekOfYear from "dayjs/plugin/weekOfYear";
import weekYear from "dayjs/plugin/weekYear";
import { useTheme } from "context/ThemeContext";
import { ColorThemes } from "utils/theme";
import { registerLicense } from "@syncfusion/ej2-base";
import { ErrorBoundary, FallbackProps } from "react-error-boundary";

registerLicense(
  "Ngo9BigBOggjHTQxAR8/V1JEaF5cWWNCf1FpRmJGdld5fUVHYVZUTXxaS00DNHVRdkdmWXhfeHRSRGJYUE10XkdWYEk="
);

dayjs.locale("vi");
dayjs.extend(customParseFormat);
dayjs.extend(advancedFormat);
dayjs.extend(weekday);
dayjs.extend(localeData);
dayjs.extend(weekOfYear);
dayjs.extend(weekYear);

const App = observer(() => {
  const account = toJS(userStore);

  const { runOneSignal } = useOneSignalContext();

  const { darkMode } = useTheme();

  // useEffect(() => {
  //   localStorage.setItem("isAuthGG", "");
  //   if (account.token) {
  //     if (!$isDev) {
  //       oneSignal.run();
  //     }
  //   }
  // }, [account.token]);

  useEffect(() => {
    initUUID();
    if (account.token) {
      if (!settings.isDev) {
        console.log("runOneSignal");
        
        runOneSignal?.();
      }
      // menuDataStore.countNewContact();
      // menuDataStore.countNewChangeEmployee();
      // menuDataStore.countNewChangeRank();
      // menuDataStore.countNewOrder();
      // menuDataStore.countNewCourseOrder();
      // menuDataStore.countNewCustomer();
      // menuDataStore.countNewContactPremiumService();
    }
  }, [account.token]);

  const element = useRoutes(routes);

  useEffect(() => {
    if (darkMode) {
      document.body.setAttribute("data-theme", "dark");
    } else {
      document.body.setAttribute("data-theme", "");
    }
  }, [darkMode]);

  function fallbackRender({ error, resetErrorBoundary }: FallbackProps) {
    // Call resetErrorBoundary() to reset the error boundary and retry the render.
    if (error.message.includes("Failed to fetch dynamically imported module")) {
      window.location.reload();
    }

    return (
      <div
        role="alert"
        className="w-[100dvw] h-[100dvh] gap-4 flex items-center justify-center flex-col"
      >
        <img src={settings.logo} />
        <p>Đã có lỗi gì đó xảy ra:</p>
        <pre style={{ color: "red" }}>{error.message}</pre>
      </div>
    );
  }

  return (
    <ErrorBoundary fallbackRender={fallbackRender} onReset={() => {}}>
      <ConfigProvider
        locale={viVNLocale}
        theme={{
          algorithm: darkMode ? theme.darkAlgorithm : theme.defaultAlgorithm,
          token: {
            colorPrimary: darkMode
              ? ColorThemes.dark.logo
              : ColorThemes.light.logo,
            borderRadius: 0,
          },
          hashed: false,
          components: {
            Select: {
              borderRadius: 0,
            },
            Tooltip: {
              borderRadius: 5,
            },
          },
        }}
      >
        <div
          id="App"
          data-theme={darkMode ? "dark" : ""}
          className={`App ${darkMode ? "dark" : ""}`}
        >
          {element}
        </div>
      </ConfigProvider>
    </ErrorBoundary>
  );
});

export default App;
