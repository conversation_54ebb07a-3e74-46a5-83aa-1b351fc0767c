import {
  Card,
  Col,
  DatePicker,
  Form,
  Input,
  message,
  Row,
  Select,
  Space,
  Spin,
  Tabs,
} from "antd";
import { Rule } from "antd/lib/form";
import { deviceApi } from "api/device.api";
import { ProviderSelector } from "components/Selector/ProviderSelector";
import { UnitSelector } from "components/Selector/UnitSelector";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import { useEffect, useMemo, useState } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { FileAttach } from "types/fileAttach";
import { getTitle } from "utils";
import { $url } from "utils/url";
import { useWatch } from "antd/es/form/Form";
import { Device, DeviceTypeTrans, ProductOriginTrans } from "types/device";
import CustomSelect from "components/Input/CustomSelect";
import CustomButton from "components/Button/CustomButton";
import PageTitle from "components/PageTitle/PageTitle";
import ActiveStatusTagSelect from "components/ActiveStatus/ActiveStatusTagSelect";
import { ModalStatus } from "types/modal";
import { isEmpty } from "lodash";
import { PermissionNames } from "types/PermissionNames";
import { DictionarySelector } from "components/Selector/DictionarySelector";
import { DictionaryType } from "types/dictionary";
import { DeviceType } from "types/device";
import { useDeviceCategory } from "hooks/useDeviceCategory";
import { DeviceCategorySelector } from "components/Selector/DeviceCategorySelector";
import CustomDatePicker from "components/Input/CustomDatePicker";
import { settings } from "settings";
import dayjs from "dayjs";
import { FileUploadMultiple2 } from "components/Upload/FileUploadMultiple2";
import { fileAttachApi } from "api/fileAttach.api";
import clsx from "clsx";
import TextArea from "antd/es/input/TextArea";
import { InputNumber } from "components/Input/InputNumber";
import { checkRoles } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { observer } from "mobx-react";
import { BMDTextArea } from "components/TextArea/BMDTextArea";
import { BMDCKEditor } from "components/Editor";

const rules: Rule[] = [{ required: true }];
const descriptionRules: Rule[] = [{ required: false }];

interface EditDevicePageProps {
  title: string;
  status: ModalStatus;
  type: DeviceType;
}

interface DeviceForm extends Omit<Device, "purchaseAt"> {
  unitId: number;
  providerId: number;
  deviceCategoryId: number;
  projectId: number;
  departmentId: number;
  staffId: number;
  brandId: number;
  purchaseAt?: dayjs.Dayjs;
}

export const CreateOrUpdateDevicePage = observer(
  ({
    title = "",
    status,
    type = DeviceType.Equipment,
  }: EditDevicePageProps) => {
    const { haveEditPermission: haveEditPermissionDevice } = checkRoles(
      {
        edit: PermissionNames.deviceEdit,
      },
      permissionStore.permissions
    );
    const { haveEditPermission: haveEditPermissionMachine } = checkRoles(
      {
        edit: PermissionNames.machineEdit,
      },
      permissionStore.permissions
    );

    const [form] = Form.useForm<DeviceForm>();
    const [loading, setLoading] = useState(false);
    const navigate = useNavigate();
    const [fileList, setFileList] = useState<FileAttach[]>([]);
    const avatar = useWatch("avatar", form);
    const [searchParams, setSearchParams] = useSearchParams();
    const [selectedDevice, setSelectedDevice] = useState<Device>();
    const [readonly, setReadonly] = useState(true);
    const [loadingFetch, setLoadingFetch] = useState(false);

    const {
      deviceCategories,
      fetchData: fetchDeviceCategories,
      setData: setDeviceCategories,
    } = useDeviceCategory({
      initQuery: { page: 1, limit: 100 },
    });
    const params = useParams();

    useEffect(() => {
      document.title = getTitle(title);
    }, []);

    const setDataToForm = (data: Device) => {
      form.setFieldsValue({
        ...data,
        productOrigin: data.productOrigin || undefined,
        unitId: data.unit?.id,
        providerId: data.provider?.id,
        deviceCategoryId: data.deviceCategory?.id,
        projectId: data.project?.id,
        departmentId: data.department?.id,
        staffId: data.staff?.id,
        brandId: data.brand?.id,
        purchaseAt: data.purchaseAt ? dayjs.unix(data.purchaseAt) : undefined,
      });

      setFileList(data.fileAttaches ? [...data.fileAttaches] : []);
    };

    const getOneDevice = async (id: number) => {
      try {
        setLoadingFetch(true);
        const { data } = await deviceApi.findOne(id);

        if (isEmpty(data)) {
          navigate("/404");
          return;
        }

        setSelectedDevice(data);
        setDataToForm(data);
      } catch (e: any) {
      } finally {
        setLoadingFetch(false);
      }
    };

    useEffect(() => {
      if (status == "update") {
        const deviceId = params.id;
        if (deviceId) {
          getOneDevice(+deviceId);
          setReadonly(searchParams.get("update") != "1");
        }
      } else {
        fetchDeviceCategories();
        setReadonly(false);
      }
    }, []);

    const getDataSubmit = async () => {
      const {
        files,
        unitId,
        providerId,
        deviceCategoryId,
        projectId,
        departmentId,
        staffId,
        purchaseAt,
        brandId,
        ...data
      } = form.getFieldsValue();

      const fileAttachIds: number[] = [];

      for (const file of fileList) {
        if (file.id) {
          fileAttachIds.push(file.id);
        } else if (file.originFile) {
          const { data } = await fileAttachApi.upload(file.originFile);

          const resFileAttach = await fileAttachApi.create({
            fileAttach: {
              ...file,
              url: $url(data.path),
            },
          });

          fileAttachIds.push(resFileAttach.data.id);
        }
      }

      const payload = {
        device: {
          ...data,
          files: typeof files === "string" ? files : JSON.stringify(files),
          type,
          purchaseAt: purchaseAt
            ? purchaseAt?.startOf("day")?.unix()
            : undefined,
          isActive: selectedDevice?.isActive,
        },
        unitId: unitId ? unitId : 0,
        providerId: providerId ? providerId : 0,
        deviceCategoryId,
        projectId: projectId ? projectId : 0,
        departmentId: departmentId ? departmentId : 0,
        staffId: staffId ? staffId : 0,
        brandId: brandId ? brandId : 0,
        fileAttachIds,
      };

      return payload;
    };

    const createData = async () => {
      const valid = await form.validateFields();

      setLoading(true);
      try {
        const res = await deviceApi.create(await getDataSubmit());
        message.success(
          `Tạo ${type == DeviceType.Equipment ? "thiết bị" : "máy"} thành công!`
        );
        navigate(
          type == DeviceType.Equipment
            ? `/master-data/${PermissionNames.deviceList}`
            : `/master-data/${PermissionNames.machineList}`
        );
        setFileList([]);
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const valid = await form.validateFields();

      setLoading(true);
      try {
        const res = await deviceApi.update(
          selectedDevice!?.id || 0,
          await getDataSubmit()
        );
        setSelectedDevice({ ...selectedDevice, ...res.data });
        message.success(
          `Chỉnh sửa ${
            type == DeviceType.Equipment ? "thiết bị" : "máy"
          } thành công!`
        );
      } finally {
        setLoading(false);
      }
    };

    const handleSubmit = () => {
      if (status == "create") {
        createData();
      } else {
        updateData();
      }
    };

    const pageTitle = useMemo(() => {
      if (status === "create") {
        return type === DeviceType.Equipment
          ? "Tạo thiết bị"
          : "Tạo máy thi công";
      } else {
        return type === DeviceType.Equipment
          ? "Chỉnh sửa thiết bị"
          : "Chỉnh sửa máy thi công";
      }
    }, [status, type]);

    return (
      <div className="app-container">
        <PageTitle
          back
          breadcrumbs={[
            { label: "Dữ liệu nguồn" },
            {
              label:
                type == DeviceType.Equipment
                  ? "Danh mục thiết bị"
                  : "Danh mục máy thi công",
              href: `/master-data/${PermissionNames.deviceList}`,
            },
            { label: pageTitle },
          ]}
          title={pageTitle}
          extra={
            selectedDevice &&
            status == "update" && (
              <Space>
                <ActiveStatusTagSelect
                  disabled={readonly}
                  isActive={selectedDevice?.isActive}
                  onChange={(value) => {
                    setSelectedDevice({
                      ...selectedDevice,
                      isActive: value,
                    } as Device);
                  }}
                />
              </Space>
            )
          }
        />
        <Card>
          <Spin spinning={loadingFetch}>
            <Form
              layout="vertical"
              form={form}
              className={clsx(readonly ? "readonly" : "")}
              disabled={readonly}
            >
              <Form.Item name="isActive" hidden />

              <div
                style={{
                  display: "flex",
                  gap: 20,
                }}
              >
                <div>
                  <Form.Item
                    style={{ marginBottom: 0, height: "100%" }}
                    label={
                      <div>
                        Hình ảnh{" "}
                        {type == DeviceType.Equipment ? "thiết bị" : " máy"}
                      </div>
                    }
                    name="avatar"
                    className="form-height-full"
                  >
                    <SingleImageUpload
                      onUploadOk={(file: FileAttach) => {
                        console.log(file);
                        form.setFieldsValue({
                          avatar: file.path,
                        });
                      }}
                      imageUrl={avatar}
                      height={170}
                      width={"100%"}
                      className="h-full upload-avatar"
                      hideUploadButton={readonly}
                    />
                  </Form.Item>
                </div>

                <div
                  style={{
                    flex: 1,
                  }}
                >
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        label={
                          type == DeviceType.Equipment
                            ? "Mã thiết bị"
                            : "Mã máy"
                        }
                        name="code"
                      >
                        <Input
                          disabled={status == "update"}
                          placeholder={
                            status == "create"
                              ? "Nếu không điền hệ thống sẽ tự sinh mã"
                              : ""
                          }
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label={
                          type == DeviceType.Equipment
                            ? "Tên thiết bị"
                            : "Tên máy"
                        }
                        name="name"
                        rules={rules}
                      >
                        <Input placeholder="" />
                      </Form.Item>
                    </Col>

                    <Col span={12}>
                      <Form.Item
                        label={
                          type == DeviceType.Equipment
                            ? "Loại thiết bị"
                            : "Loại máy"
                        }
                        name="deviceCategoryId"
                        rules={rules}
                      >
                        <DictionarySelector
                          initQuery={{
                            type:
                              type == DeviceType.Equipment
                                ? DictionaryType.DeviceCategory
                                : DictionaryType.MachineCategory,
                            isActive: true,
                          }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label="Mã hệ thống cũ" name="oldCode">
                        <Input placeholder="Mã hệ thống cũ" />
                      </Form.Item>
                    </Col>

                    <Col span={12}>
                      <Form.Item
                        label="Đơn vị tính"
                        name="unitId"
                        rules={rules}
                      >
                        <UnitSelector
                          initQuery={{ isActive: true }}
                          initOptionItem={selectedDevice?.unit}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label="Thương hiệu" name="brandId">
                        <DictionarySelector
                          placeholder="Chọn thương hiệu"
                          initQuery={{
                            type: DictionaryType.Brand,
                            isActive: true,
                          }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </div>
              </div>

              <Card title="Thông tin chung" className="mb-4 form-card mt-4">
                <Row gutter={16}>
                  <Col span={6}>
                    <Form.Item label="Nhà cung cấp" name="providerId">
                      <ProviderSelector
                        initQuery={{ isActive: true }}
                        initOptionItem={selectedDevice?.provider}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item label="Mã SP NCC" name="supplierProductCode">
                      <Input placeholder="Nhập mã SP NCC" />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label="Số tháng bảo hành"
                      name="warrantyPeriodMonths"
                    >
                      <InputNumber placeholder="Số tháng bảo hành" />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label="Điều kiện bảo hành"
                      name="warrantyConditions"
                    >
                      <Input placeholder="Điều kiện bảo hành" />
                    </Form.Item>
                  </Col>

                  <Col span={6}>
                    <Form.Item label="Nguồn gốc sản phẩm" name="productOrigin">
                      <Select
                        placeholder="Nguồn gốc sản phẩm"
                        options={Object.values(ProductOriginTrans).map(
                          (item) => ({
                            label: item.label,
                            value: item.value,
                          })
                        )}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item label="Xuất xứ" name="placeOfManufacture">
                      <DictionarySelector
                        placeholder="Chọn xuất xứ"
                        initQuery={{
                          type: DictionaryType.Country,
                          isActive: true,
                        }}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item label="Ngày mua" name="purchaseAt">
                      <DatePicker
                        allowClear={false}
                        format={settings.dateFormat}
                        className="w-full"
                      />
                    </Form.Item>
                  </Col>

                  <Col span={24}>
                    <Form.Item
                      label="Ghi chú"
                      name="notes"
                      rules={descriptionRules}
                    >
                      <BMDCKEditor
                        placeholder="Ghi chú"
                        disabled={readonly}
                        inputHeight={300}
                        onChange={(content) => {
                          form.setFieldsValue({ notes: content });
                        }}
                        value={selectedDevice?.notes}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </Card>

              <Card title="Thông tin định danh" className="mb-4 form-card">
                <Row gutter={16}>
                  <Col span={6}>
                    <Form.Item label="Mã định danh" name="identificationCode">
                      <Input placeholder="Mã định danh" />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item label="Mã thuộc tính" name="attributeCode">
                      <Input placeholder="Mã thuộc tính" />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item label="Số hiệu" name="serialNumber">
                      <Input placeholder="Số hiệu" />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label="Thuộc tính định danh"
                      name="identificationAttribute"
                    >
                      <Input placeholder="Thuộc tính định danh" />
                    </Form.Item>
                  </Col>
                </Row>
              </Card>

              <Card title="Thông tin bổ sung" className="mb-4 form-card">
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item label="Chi phí thuê" name="rentalCost">
                      <InputNumber placeholder="Chi phí thuê" suffix="VNĐ" />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label="Chi phí mua" name="purchaseCost">
                      <InputNumber placeholder="Chi phí mua" suffix="VNĐ" />
                    </Form.Item>
                  </Col>
                </Row>
              </Card>

              {/* <Form.Item label="Mô tả" name="description">
              <Input.TextArea placeholder="Mô tả" rows={4} />
            </Form.Item> */}

              <Tabs defaultActiveKey="1" type="line">
                <Tabs.TabPane tab="Tệp đính kèm" key="1">
                  <Form.Item
                    shouldUpdate={true}
                    style={{ marginBottom: 0, height: "100%" }}
                    className="form-height-full"
                  >
                    {() => {
                      return (
                        <Form.Item
                          label={""}
                          noStyle
                          style={{ marginBottom: 0 }}
                          name="files"
                          className="h-full "
                        >
                          <FileUploadMultiple2
                            className="h-full"
                            fileList={fileList}
                            onUploadOk={(file) => {
                              fileList.push(file);
                              setFileList([...fileList]);
                            }}
                            onDelete={(file) => {
                              const findIndex = fileList.findIndex(
                                (e) => e.uid == file.uid
                              );

                              if (findIndex > -1) {
                                fileList.splice(findIndex, 1);
                                setFileList([...fileList]);
                              }
                            }}
                            hideUploadButton={readonly}
                          />
                        </Form.Item>
                      );
                    }}
                  </Form.Item>
                </Tabs.TabPane>
              </Tabs>
            </Form>
            <div className="flex gap-[16px] justify-end mt-2">
              {!readonly && (
                <CustomButton
                  variant="outline"
                  className="cta-button"
                  onClick={() => {
                    if (status == "create") {
                      navigate(
                        type == DeviceType.Equipment
                          ? `/master-data/${PermissionNames.deviceList}`
                          : `/master-data/${PermissionNames.machineList}`
                      );
                    } else {
                      setReadonly(true);
                      setDataToForm(selectedDevice!);
                    }
                  }}
                >
                  Hủy
                </CustomButton>
              )}

              <CustomButton
                className="cta-button"
                loading={loading}
                onClick={() => {
                  if (!readonly) {
                    handleSubmit();
                  } else {
                    setReadonly(false);
                  }
                }}
                disabled={
                  status == "update" &&
                  ((type == DeviceType.Equipment &&
                    !haveEditPermissionDevice) ||
                    (type == DeviceType.Machine && !haveEditPermissionMachine))
                }
              >
                {status == "create"
                  ? type == DeviceType.Equipment
                    ? "Tạo thiết bị"
                    : "Tạo máy thi công"
                  : readonly
                  ? "Chỉnh sửa"
                  : "Lưu chỉnh sửa"}
              </CustomButton>
            </div>
          </Spin>
        </Card>
      </div>
    );
  }
);
