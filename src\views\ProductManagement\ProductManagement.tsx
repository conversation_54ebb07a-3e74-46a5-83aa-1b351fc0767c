import {
  CheckOutlined,
  CloseOutlined,
  DownOutlined,
  ExportOutlined,
  ImportOutlined,
  LockOutlined,
  PlusOutlined,
  SearchOutlined,
  UnlockOutlined,
} from "@ant-design/icons";
import {
  Button,
  Checkbox,
  Input,
  message,
  Popconfirm,
  Space,
  Spin,
  Table,
  Tabs,
  TabsProps,
  Tag,
} from "antd";
import Column from "antd/es/table/Column";
import { Pagination } from "components/Pagination";
import { useComponent } from "hooks/useComponent";
import React, { useEffect, useMemo, useRef, useState } from "react";
import {
  Component,
  ComponentShowTypeTrans,
  RelationComponent,
} from "types/component";
import { formatVND, getTitle } from "utils";
import { $url } from "utils/url";
import { ComponentModal } from "./Components/CreateProductModal";
import { componentApi } from "api/component.api";
import { handleExport, MyExcelColumn } from "utils/MyExcel";
import ImportSettingComponent, {
  ImportSettingComponentModal,
} from "components/ImportDocument/ImportSettingComponent";
import { removeSubstringFromKeys } from "utils/common";
import DropdownCell from "components/Table/DropdownCell";
import SingleProductTab from "./Tabs/SingleProductTab";
import { ComboProductTab } from "./Tabs/ComboProductTab";
const exportColumns: MyExcelColumn<Component>[] = [
  {
    header: "Nhóm cha",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "nameGroup",
    columnKey: "nameGroup",
    render: (record) => record?.name,
  },
  {
    header: "TP mặc định của nhóm",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "isDefault",
    columnKey: "isDefault",
    render: (record) => (record?.isDefault ? "Phải" : "Không"),
  },
  {
    header: "Mã nhóm",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "code",
    columnKey: "code",
  },
  {
    header: "Tên",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "name",
    columnKey: "name",
  },
  {
    header: "Nhóm điều kiện",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "depComponents",
    columnKey: "depComponents",
    render: (record) =>
      record.depComponents
        ?.map((item: any) => item.component2?.name)
        .join(", "),
  },
  {
    header: "Nhóm loai trừ",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "excludeComponents",
    columnKey: "excludeComponents",
    render: (record) =>
      record.excludeComponents
        ?.map((item: any) => item.component2?.name)
        .join(", "),
  },
  {
    header: "Tên nội bộ",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "privateName",
    columnKey: "privateName",
  },
  {
    header: "Mô tả",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "desc",
    columnKey: "desc",
  },
  {
    header: "Truy xuất thông tin mô tả",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "featureImageShowType",
    columnKey: "featureImageShowType",
    render: (record: Component) =>
      ComponentShowTypeTrans[record?.featureImageShowType]?.label || "-",
  },
  {
    header: "Giá cộng thêm ($)",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "extraPrice",
    columnKey: "extraPrice",
    render: (record) => formatVND(record.extraPrice),
  },
  {
    header: "Định mức",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "standard",
    columnKey: "standard",
  },

  {
    header: "Thêu tên",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "isEmbroidery",
    columnKey: "isEmbroidery",
    render: (record) => (record.isEmbroidery == true ? "Có" : "Không"),
  },
  {
    header: "Số ký tự tối đa",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "maxLength",
    columnKey: "maxLength",
  },
  {
    header: "Trạng thái",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "isBlocked",
    columnKey: "isBlocked",
    render: (record) => (record.isBlocked == true ? "Ẩn" : "Hiện"),
  },
  // {
  //   header: "Hình mặt trước",
  //   headingStyle: {
  //     font: {
  //       bold: true,
  //     },
  //   },
  //   key: "avatar",
  //   columnKey: "avatar",
  //   render(record) {
  //     return $url(record.fileAttachAvatar?.url);
  //   },
  // },
  // {
  //   header: "Hình mặt sau",
  //   headingStyle: {
  //     font: {
  //       bold: true,
  //     },
  //   },
  //   key: "avatarBack",
  //   columnKey: "avatarBack",
  //   render(record) {
  //     return $url(record.fileAttachAvatarBack?.url);
  //   },
  // },

  {
    header: "Hình feature",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "featureImage",
    columnKey: "featureImage",
    render(record) {
      return $url(record.fileAttachFeatureImage?.url);
    },
  },
];
export const ProductManagement = ({ title = "" }) => {
  useEffect(() => {
    document.title = getTitle(title);
  }, []);
  const [loadingDelete, setLoadingDelete] = useState(false);
  const { components, fetchData, query, loading, setQuery, total } =
    useComponent({
      initQuery: {
        page: 1,
        limit: 10,
        search: "",
      },
    });
  useEffect(() => {
    fetchData();
  }, [query]);
  const modalRef = React.useRef<ComponentModal>(null);
  const tagColors = [
    "magenta",
    "volcano",
    "orange",
    "gold",
    "lime",
    "green",
    "cyan",
    "blue",
    "geekblue",
    "purple",
  ];

  const getColor = (index: number) => {
    return tagColors[index % tagColors.length];
  };
  const handleDelete = async (id: number) => {
    try {
      setLoadingDelete(true);
      await componentApi.delete(id);
      message.success("Xóa thành công");
      fetchData();
    } catch (e) {
      console.log({ e });
    } finally {
      setLoadingDelete(false);
    }
  };
  const importModal = useRef<ImportSettingComponentModal>();
  const handleOnUploadedFile = async (excelData: any, setData: any) => {
    const { results } = excelData;

    console.log("results", results);

    const importData = results?.map((item: any) => {
      const refineRow = removeSubstringFromKeys(item, " (*)");

      const name = refineRow["TÊN"] || "";
      const isDefault = refineRow["TP MẶC ĐỊNH CỦA NHÓM"] || "";
      const privateName = refineRow["TÊN NỘI BỘ"] || "";
      const code = refineRow["MÃ NHÓM"] || "";
      const avatar = refineRow["HÌNH MẶT TRƯỚC"] || "";
      const avatarBack = refineRow["HÌNH MẶT SAU"] || "";
      const featureImage = refineRow["HÌNH FEATURE"] || "";
      const desc = refineRow["MÔ TẢ"] || "";
      const extraPrice = refineRow["GIÁ CỘNG THÊM"] || 0;
      const isEmbroidery = refineRow["THÊU TÊN"] || false;
      const maxLength = refineRow["SỐ KÝ TỰ TỐI ĐA"] || 0;
      const componentDepCodes = refineRow["NHÓM ĐIỀU KIỆN"] || "";
      const componentExcludeCodes = refineRow["NHÓM LOẠI TRỪ"] || "";
      const parentCode = refineRow["NHÓM CHA"] || "";
      // const note = refineRow["GHI CHÚ TIẾNG VIỆT (DÀNH CHO THỢ MAY)"] || "";
      const featureImageShowType = refineRow["TRUY XUẤT THÔNG TIN MÔ TẢ"];

      const standard = refineRow["ĐỊNH MỨC"] || "";

      return {
        name,
        privateName,
        code,
        avatar,
        avatarBack,
        featureImage,
        isDefault,
        desc,
        extraPrice,
        isEmbroidery,
        maxLength,
        componentDepCodes,
        componentExcludeCodes,
        parentCode: parentCode,
        featureImageShowType,
        standard,
        rowNum: item.__rowNum__,
      };
    });
    console.log("importData", importData);

    setData(importData);
  };
  const blockComponent = async (component: Component) => {
    await componentApi.block(component.id, {
      isBlocked: !component.isBlocked,
    });

    message.success(
      `${component.isBlocked ? "Mở khóa" : "Khóa"} sản phẩm thành công!`
    );
    fetchData();
  };
  const onChange = (key: string) => {
    console.log(key);
  };

  const items: TabsProps["items"] = [
    {
      key: "1",
      label: "Sản phẩm đơn",
      children: <SingleProductTab />,
    },
    {
      key: "2",
      label: "Bộ sản phẩm",
      children: <ComboProductTab />,
    },
  ];
  return (
    <div>
      <Tabs defaultActiveKey="1" items={items} onChange={onChange} />
    </div>
  );
};

export default ProductManagement;
