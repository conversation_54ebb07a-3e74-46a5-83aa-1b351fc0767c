import React, { useState } from "react";
import {
  Modal,
  Space,
  Button,
  Switch,
  Input,
  Form,
  Select,
  Tooltip,
  message,
} from "antd";
import {
  ApprovalTemplate,
  ApprovalTemplateDetail,
  ApprovalTemplateMode,
  ApprovalTemplateModeTrans,
  ApprovalTemplateName,
  ApprovalTemplateType,
  ApprovalTemplateTypeOptions,
} from "types/approvalTemplate";
import { ApprovalTemplateSelector } from "components/Selector/ApprovalTemplateSelector";
import { MembershipSelector } from "components/Selector/MembershipSelector";
import { PlusIcon } from "assets/svgs/PlusIcon";
import DeleteIcon from "assets/svgs/DeleteIcon";
import { StepType, StepTypeTrans } from "types/approvalStep";
import { ApprovalListStatus } from "types/approvalList";
import { memberShipApi } from "api/memberShip.api";
import "./ApproveTemplate.scss";
import { userStore } from "store/userStore";
import CustomButton from "components/Button/CustomButton";
import ApprovalTemplateStepItem from "./ApprovalTemplateStepItem";
import { useWatch } from "antd/es/form/Form";
import { ReloadOutlined } from "@ant-design/icons";

interface EditApprovalTemplateModalProps {
  open: boolean;
  loading: boolean;
  editingRecord: ApprovalTemplate;
  // approvalSteps: any[];
  // setApprovalSteps: (steps: any[]) => void;
  setEditModalOpen: (open: boolean) => void;
  // setEditingName: (name: string) => void;
  // setEditingDescription: (desc: string) => void;
  // setEditingType: (type: string) => void;
  setRemoveApprovalList: (ids: number[]) => void;
  handleUpdateApprovalProcess: (steps: any[]) => void;
  onToggleStatus?: (checked: boolean) => void;
  onChangeEditMode?: (value: boolean) => void;
  isEditMode?: boolean;
  followers?: any[];
  responders?: any[];
  templateType?: ApprovalTemplateType;
  processMode?: "simple" | "complex";
}

export type TemplateCompact = "simple" | "complex";

const EditApprovalTemplateModal: React.FC<EditApprovalTemplateModalProps> = ({
  open,
  loading,
  editingRecord,
  // approvalSteps,
  // setApprovalSteps,
  setEditModalOpen,
  // setEditingName,
  // setEditingDescription,
  // setEditingType,
  setRemoveApprovalList,
  handleUpdateApprovalProcess,
  onToggleStatus,
  onChangeEditMode,
  templateType,
  followers = [],
  responders = [],
  isEditMode = true,
  processMode = "simple",
}) => {
  const [form] = Form.useForm();
  const [localIsActive, setLocalIsActive] = React.useState(
    !!editingRecord?.isActive
  );
  // const [localType, setLocalType] =
  //   React.useState<TemplateCompact>(processMode);
  // const [memberShips, setMemberShips] = useState<any[]>([]);
  const [selectedApprovalTemplate, setSelectedApprovalTemplate] =
    useState<any>(null);

  const steps = useWatch("steps", form);
  const mode = useWatch("mode", form);

  React.useEffect(() => {
    if (editingRecord) {
      handleSetFormData(editingRecord);
    }
  }, [editingRecord, form]);

  const handleSetFormData = (editingRecord: ApprovalTemplate) => {
    // debugger;
    form.setFieldsValue({
      name: editingRecord.name,
      mode: editingRecord.mode || ApprovalTemplateMode.Simple,
      description: editingRecord.description,
      steps: (editingRecord.approvalTemplateDetails || []).map(
        (step: ApprovalTemplateDetail) => ({
          ...step,
          approvers:
            step.memberShipApprovals.length > 0
              ? step.memberShipApprovals?.map((it) => ({
                  roleId: it.role?.id,
                  memberShipId: it.memberShip?.id,
                }))
              : step.name !== ApprovalTemplateName.Create
              ? [{}]
              : [],
          // memberShipId: step.memberShipId ?? step.memberShip?.id ?? undefined,
          // memberShip2Id:
          //   step.memberShip2Id ?? step.memberShip2?.id ?? undefined,
        })
      ),
      followers: (editingRecord.followMemberShips || [])
        .map((f: any) => f.memberShipId ?? f.id)
        .filter(Boolean),
      responders: (editingRecord.responderMemberShips || [])
        .map((r: any) => r.memberShipId ?? r.id)
        .filter(Boolean),
    });
    setLocalIsActive(!!editingRecord?.isActive);
    // if (editingRecord.approvalTemplateDetails) {
    //   const type =
    //     editingRecord.approvalTemplateDetails.filter(
    //       (it) => ![StepType.Create, StepType.Publish].includes(it.name as any)
    //     ).length >= 1
    //       ? "complex"
    //       : "simple";
    //   setLocalType(type);
    // } else {
    //   setLocalType("simple");
    // }
  };

  // React.useEffect(() => {
  //   if (processMode) {
  //     setLocalType(processMode);
  //   }
  // }, [processMode]);

  // React.useEffect(() => {
  //   if (open) {
  //     // Gọi API lấy danh sách memberShips
  //     const fetchMemberShips = async () => {
  //       try {
  //         const { data } = await memberShipApi.findAll({ isActive: true });
  //         setMemberShips(data?.memberShips || []);
  //       } catch (error) {
  //         setMemberShips([]);
  //       }
  //     };
  //     fetchMemberShips();
  //   }
  // }, [open]);

  // const getMemberShipInfo = (membershipId: number) => {
  //   return memberShips.find((membership) => membership.id === membershipId);
  // };

  // Thêm hàm khởi tạo bước mặc định cho từng loại
  const initSteps = () => [
    {
      note: "",
      name: StepType.Create,
      status: ApprovalListStatus.Pending,
    },
    {
      // memberShipId: undefined,
      // memberShip2Id: undefined,
      note: "",
      name: "",
      // status: ApprovalListStatus.Pending,
      approvers: [{}],
      isAllApproval: false, // Mặc định là một người duyệt
    },
    // {
    //   memberShipId: undefined,
    //   note: "",
    //   name: StepType.Publish,
    //   status: ApprovalListStatus.Pending,
    // },
  ];

  // // Reset lại steps khi đổi loại
  // React.useEffect(() => {
  //   form.setFieldsValue({
  //     steps: initSteps(localType),
  //   });
  // }, [localType, form]);

  const handleSelectApprovalTemplate = () => {
    if (selectedApprovalTemplate) {
      // Ví dụ: set steps theo mẫu đã chọn
      form.setFieldsValue({
        steps: selectedApprovalTemplate.approvalTemplateDetails || [],
      });
    }
  };

  const handleCheckApprover = (
    approvalTemplateDetails: {
      name: string;
      memberShipApprovals: { roleId: number; memberShipId: number }[];
    }[]
  ) => {
    let errorSteps = [];
    for (let i = 0; i < approvalTemplateDetails.length; i++) {
      let isValid = true;
      const step = approvalTemplateDetails[i];

      if (step.name == StepType.Create) continue;

      const approvers = step.memberShipApprovals || [];
      if (approvers.length === 0) {
        isValid = false;
      } else {
        let haveNullMemberShip = false;
        approvers.forEach((approver) => {
          if (!approver.memberShipId) {
            haveNullMemberShip = true;
          }
        });
        if (haveNullMemberShip) {
          isValid = false;
        }
      }
      if (!isValid) {
        errorSteps.push(i + 1);
      }
    }

    if (errorSteps.length > 0) {
      message.error(
        `Vui lòng chọn đầy đủ người duyệt cho bước ${errorSteps.join(", ")}`
      );
      return false;
    }
    return true;
  };

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      // debugger;
      // setEditingName(values.name);
      // setEditingDescription(values.description);

      const approvalTemplateDetails = (values.steps || []).map(
        (step: any, idx: number) => ({
          id: step.id ?? 0,
          name: step.name,
          actionText: step.actionText || "",
          statusText: step.statusText || "",
          statusColor: step.statusColor?.metaColor?.toHexString() || "",
          // type: "TASK",
          isAllApproval: step.isAllApproval || false,
          position: idx,
          note: step.note || "",
          memberShipApprovals: step.approvers || [],
          // memberShipId: step.memberShipId ?? 0,
          // memberShip2Id: step.memberShip2Id ?? 0,
        })
      );
      if (handleCheckApprover(approvalTemplateDetails)) {
        const followMemberShipIds = (values.followers || []).filter(Boolean);
        const responderMemberShipIds = (values.responders || []).filter(
          Boolean
        );

        const payload = {
          approvalTemplateDetails: approvalTemplateDetails,
          followMemberShipIds,
          responderMemberShipIds,
          approvalTemplate: {
            name: values.name || editingRecord?.name,
            description: values.description || editingRecord?.description,
            type: editingRecord?.type,
            isActive: localIsActive,
            isDefault: true,
            mode: values.mode,
          },
        };

        if (approvalTemplateDetails && handleUpdateApprovalProcess) {
          handleUpdateApprovalProcess(payload as any);
        }
      }
    } catch (err) {
      console.log({ err });
      // Xử lý lỗi validate nếu cần
    }
  };

  return (
    <Modal
      open={open}
      onCancel={() => setEditModalOpen(false)}
      width={"90%"}
      style={{ top: 10 }}
      title="Chỉnh sửa quy trình duyệt"
      destroyOnClose
      footer={
        <Space>
          {!isEditMode && localIsActive && (
            <CustomButton
              onClick={() => {
                onChangeEditMode?.(true);
              }}
              loading={loading}
            >
              Chỉnh sửa
            </CustomButton>
          )}
          <CustomButton
            onClick={handleSave}
            loading={loading}
            disabled={!isEditMode}
          >
            Lưu
          </CustomButton>
        </Space>
      }
      className="modal-approve-process"
    >
      <Form
        form={form}
        layout="vertical"
        disabled={!isEditMode}
        style={{ padding: "0 24px 0" }}
        // initialValues={{
        //   mode: ApprovalTemplateMode.Simple,
        // }}
      >
        <Form.Item style={{ marginBottom: 8 }}>
          <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
            {/* trạng thái */}
            <Form.Item name={"isActive"} label="Trạng thái">
              <Switch
                checked={localIsActive}
                checkedChildren="Bật"
                unCheckedChildren="Tắt"
                onChange={(checked) => {
                  setLocalIsActive(checked);
                  if (onToggleStatus) onToggleStatus(checked);
                }}
                disabled={loading}
              />
            </Form.Item>
            {/* <span style={{ minWidth: 90 }}>Trạng thái:</span> */}
          </div>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              gap: 8,
              marginTop: 4,
            }}
          >
            <span style={{ minWidth: 90, color: "#888" }}>
              Ghi chú trạng thái:
            </span>
            <span style={{ color: localIsActive ? "#52c41a" : "#aaa" }}>
              {localIsActive
                ? "Quy trình sẽ được sử dụng"
                : "Quy trình sẽ không được sử dụng"}
            </span>
          </div>
        </Form.Item>

        <div className="mb-2 mt-1">
          <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
            <Form.Item name="mode" label="Loại">
              <Switch
                checked={mode == ApprovalTemplateMode.Complex}
                checkedChildren={
                  ApprovalTemplateModeTrans[ApprovalTemplateMode.Complex].label
                }
                unCheckedChildren={
                  ApprovalTemplateModeTrans[ApprovalTemplateMode.Simple].label
                }
                onChange={(checked) => {
                  Modal.confirm({
                    title:
                      "Thay đổi loại quy trình sẽ xóa tất cả các bước hiện tại. Bạn có chắc chắn muốn tiếp tục?",
                    classNames: {
                      body: "custom-modal",
                    },
                    onOk: () => {
                      const type = checked
                        ? ApprovalTemplateMode.Complex
                        : ApprovalTemplateMode.Simple;
                      // setEditingType(type);
                      form.setFieldsValue({
                        steps: initSteps(),
                        mode: type,
                      });
                    },
                  });
                }}
                disabled={!isEditMode || !localIsActive}
              />
            </Form.Item>
          </div>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              gap: 8,
              marginTop: 4,
            }}
          >
            <span style={{ minWidth: 90, color: "#888" }}>Ghi chú loại:</span>
            <span style={{ color: "#1677ff" }}>
              {mode == ApprovalTemplateMode.Complex
                ? "Quy trình này có nhiều bước duyệt"
                : "Quy trình này chỉ có một bước duyệt"}
            </span>
          </div>
        </div>

        <div className="approve-process-main-box">
          {/* Quy trình duyệt */}
          <div className="content-card approve-template-card">
            <div className="content-card-header">
              <h2 className="header-text">Quy trình duyệt</h2>
              <div className="flex gap-2 items-center">
                <Tooltip title="Khôi phục dữ liệu đầu">
                  <Button
                    disabled={!isEditMode || !localIsActive}
                    icon={<ReloadOutlined className="translate-y-[-2px]" />}
                    onClick={() => {
                      handleSetFormData(editingRecord);
                    }}
                  ></Button>
                </Tooltip>

                {mode === ApprovalTemplateMode.Complex && (
                  <CustomButton
                    onClick={() => {
                      const updatedSteps = form.getFieldValue("steps") || [];
                      updatedSteps.push({
                        // memberShipId: undefined,
                        // memberShip2Id: undefined,
                        note: "",
                        name: "",
                        status: ApprovalListStatus.Pending,
                        approvers: [{}],
                        isAllApproval: false, // Mặc định là một người duyệt
                      });
                      form.setFieldsValue({ step: updatedSteps });
                      // const indexInsert = updatedSteps.length - 1;
                      // form.setFieldsValue({
                      //   steps: [
                      //     ...updatedSteps.slice(0, indexInsert),
                      //     {
                      //       memberShipId: undefined,
                      //       memberShip2Id: undefined,
                      //       note: "",
                      //       name: "",
                      //       status: ApprovalListStatus.Pending,
                      //     },
                      //     ...updatedSteps.slice(indexInsert),
                      //   ],
                      // });
                    }}
                    disabled={!isEditMode || !localIsActive}
                  >
                    Thêm bước
                  </CustomButton>
                )}
              </div>
            </div>
            <Form.List name="steps">
              {(fields, { remove }) => {
                // console.log({ fields });
                return (
                  <div className="card-body">
                    {fields.map((field, index) => {
                      // const stepName = form.getFieldValue([
                      //   "steps",
                      //   field.name,
                      //   "name",
                      // ]);

                      return (
                        <ApprovalTemplateStepItem
                          field={field}
                          fields={fields}
                          index={index}
                          isEditMode={isEditMode && localIsActive}
                          // localType={localType}
                          key={field.key}
                          onRemove={(name) => remove(name)}
                        />
                      );
                      // return (
                      //   <div
                      //     key={field.key}
                      //     className="approve-process-container"
                      //   >
                      //     <div className="process-icon">
                      //       <span>{index + 1}</span>
                      //     </div>
                      //     <div className="process-content">
                      //       <div
                      //         className="process-content-header"
                      //         style={{
                      //           display: "flex",
                      //           justifyContent: "space-between",
                      //           alignItems: "center",
                      //         }}
                      //       >
                      //         <div className="title flex gap-2 flex-wrap">
                      //           {StepTypeTrans[stepName as StepType] || (
                      //             <>
                      //               <Form.Item
                      //                 label="Bước"
                      //                 name={[field.name, "name"]}
                      //                 rules={[
                      //                   {
                      //                     required: true,
                      //                     message: "Nhập tên bước duyệt",
                      //                   },
                      //                 ]}
                      //                 style={{ marginBottom: 0 }}
                      //               >
                      //                 <Input
                      //                   placeholder="Nhập tên bước duyệt"
                      //                   disabled={!isEditMode}
                      //                 />
                      //               </Form.Item>
                      //               <Form.Item
                      //                 label="Thao tác"
                      //                 name={[field.name, "actionText"]}
                      //                 rules={[
                      //                   {
                      //                     required: true,
                      //                     message: "Nhập thao tác",
                      //                   },
                      //                 ]}
                      //                 style={{ marginBottom: 0 }}
                      //               >
                      //                 <Input
                      //                   placeholder="Nhập thao tác"
                      //                   disabled={!isEditMode}
                      //                 />
                      //               </Form.Item>
                      //               <Form.Item
                      //                 label="Trạng thái"
                      //                 name={[field.name, "statusText"]}
                      //                 rules={[
                      //                   {
                      //                     required: true,
                      //                     message: "Nhập trạng thái",
                      //                   },
                      //                 ]}
                      //                 style={{ marginBottom: 0 }}
                      //               >
                      //                 <Input
                      //                   placeholder="Nhập trạng thái"
                      //                   disabled={!isEditMode}
                      //                 />
                      //               </Form.Item>
                      //             </>
                      //           )}
                      //         </div>
                      //         <div style={{ display: "flex", gap: 8 }}>
                      //           {localType === "complex" &&
                      //             index > 1 &&
                      //             index < fields.length - 1 &&
                      //             isEditMode && (
                      //               <Button
                      //                 type="text"
                      //                 icon={<DeleteIcon />}
                      //                 onClick={() => remove(field.name)}
                      //               />
                      //             )}
                      //         </div>
                      //       </div>
                      //       <div
                      //         style={{
                      //           display: "flex",
                      //           flexDirection: "column",
                      //           alignItems: "flex-start",
                      //           marginBottom: "4px",
                      //           width: "100%",
                      //         }}
                      //       >
                      //         <div className="approve-process-form-row">
                      //           <div className="approve-process-staff-row">
                      //             <Form.Item
                      //               name={[field.name, "memberShipId"]}
                      //               // Chỉ required nếu KHÔNG phải bước "Tạo mới"
                      //               className="approve-process-staff1"
                      //             >
                      //               <MembershipSelector
                      //                 // disabled={!isEditMode}

                      //                 disabled={
                      //                   stepName === StepType.Create ||
                      //                   !isEditMode
                      //                 }

                      //                 // valueIsOption
                      //               />
                      //             </Form.Item>
                      //             {localType === "complex" && index == 1 ? (
                      //               <Button
                      //                 type="text"
                      //                 icon={
                      //                   <PlusIcon
                      //                     size={20}
                      //                     fill="#1677ff"
                      //                   />
                      //                 }
                      //                 onClick={() => {
                      //                   form.setFieldValue(
                      //                     [
                      //                       "steps",
                      //                       field.name,
                      //                       "memberShip2Id",
                      //                     ],
                      //                     null
                      //                   );
                      //                   form.setFieldsValue({
                      //                     steps: [
                      //                       ...form.getFieldValue("steps"),
                      //                     ],
                      //                   });
                      //                 }}
                      //               />
                      //             ) : (
                      //               <span className="approve-process-placeholder" />
                      //             )}
                      //           </div>
                      //           {form.getFieldValue([
                      //             "steps",
                      //             field.name,
                      //             "memberShip2Id",
                      //           ]) !== undefined && (
                      //             <div className="approve-process-staff-row">
                      //               <Form.Item
                      //                 name={[field.name, "memberShip2Id"]}
                      //                 className="approve-process-staff2"
                      //               >
                      //                 <MembershipSelector
                      //                   disabled={!isEditMode}
                      //                   //   valueIsOption
                      //                   placeholder="Chọn nhân viên 2"
                      //                 />
                      //               </Form.Item>
                      //               <Button
                      //                 type="text"
                      //                 danger
                      //                 icon={<DeleteIcon />}
                      //                 onClick={() => {
                      //                   form.setFieldValue(
                      //                     [
                      //                       "steps",
                      //                       field.name,
                      //                       "memberShip2Id",
                      //                     ],
                      //                     undefined
                      //                   );
                      //                   form.setFieldsValue({
                      //                     steps: [
                      //                       ...form.getFieldValue("steps"),
                      //                     ],
                      //                   });
                      //                 }}
                      //               />
                      //             </div>
                      //           )}
                      //         </div>
                      //       </div>
                      //     </div>
                      //     <div className="line" />
                      //   </div>
                      // );
                    })}
                  </div>
                );
              }}
            </Form.List>
          </div>
        </div>

        <>
          <Form.Item label="Người theo dõi" name="followers">
            <MembershipSelector
              multiple
              disabled={!isEditMode || !localIsActive}
              placeholder="Chọn người theo dõi"
            />
          </Form.Item>

          {templateType === ApprovalTemplateType.RFI && (
            <Form.Item
              style={{ marginTop: 8 }}
              label="Người phản hồi"
              name="responders"
            >
              <MembershipSelector
                multiple
                disabled={!isEditMode || !localIsActive}
                placeholder="Chọn người phản hồi"
              />
            </Form.Item>
          )}
        </>
      </Form>
    </Modal>
  );
};

export default EditApprovalTemplateModal;
