import { ApprovalHistory } from "./approvalHistory";
import { ApprovalList } from "./approvalList";
import { Comment } from "./comment";
import { Company } from "./company";
import { Dictionary } from "./dictionary";
import { FileAttach } from "./fileAttach";
import { MemberShip } from "./memberShip";
import { Project } from "./project";
import { Staff } from "./staff";

export enum RFIStatus {
  Draft = "DRAFT",
  Open = "OPEN",
  Responded = "RESPONDED",
  Closed = "CLOSED",
}

export const RFIStatusOptions = [
  { value: RFIStatus.Draft, label: "Nháp" },
  { value: RFIStatus.Open, label: "Mở" },
  { value: RFIStatus.Responded, label: "Đã phản hồi" },
  { value: RFIStatus.Closed, label: "Đã đóng" },
];

export enum ProcessRFIStatus {
  Draft = "DRAFT",
  Pending = "PENDING",
  Approved = "APPROVED",
  Rejected = "REJECTED",
  WaitingApprove = "WAITING_APPROVE",
}

export function getOverallApprovalStatus(
  approvalLists: any[]
): ProcessRFIStatus {
  if (!approvalLists || approvalLists.length === 0)
    return ProcessRFIStatus.Draft;
  if (approvalLists.some((item) => item.status === "REJECTED"))
    return ProcessRFIStatus.Rejected;

  // Nếu tất cả đều APPROVED
  if (approvalLists.every((item) => item.status === "APPROVED"))
    return ProcessRFIStatus.Approved;

  // Nếu tất cả đều PENDING
  if (approvalLists.every((item) => item.status === "PENDING"))
    return ProcessRFIStatus.Draft;

  // Nếu chỉ có CREATE là APPROVED, các bước sau đều PENDING => WAITING_APPROVE
  const firstStep = approvalLists[0];
  const restSteps = approvalLists.slice(1);
  if (
    firstStep &&
    firstStep.name === "CREATE" &&
    firstStep.status === "APPROVED" &&
    restSteps.every((item) => item.status === "PENDING")
  ) {
    return ProcessRFIStatus.WaitingApprove;
  }

  // Nếu có ít nhất 1 bước APPROVE đã duyệt => Đang thực hiện
  if (
    approvalLists.some(
      (item, idx) =>
        idx > 0 && item.name === "APPROVE" && item.status === "APPROVED"
    )
  ) {
    return ProcessRFIStatus.Pending;
  }

  // Nếu có ít nhất 1 bước PENDING => Đang thực hiện
  if (approvalLists.some((item) => item.status === "PENDING"))
    return ProcessRFIStatus.Pending;

  return ProcessRFIStatus.Draft;
}
// export enum RFIStatus {
//   Pending = "PENDING",
//   InProgress = "IN_PROGRESS",
//   Completed = "COMPLETED",
// }

export const ProgressRFIStatus = {
  [ProcessRFIStatus.Draft]: {
    progress: 10,
    label: "Chưa bắt đầu",
    color: "#3949AB",
    value: ProcessRFIStatus.Draft,
  },
  [ProcessRFIStatus.WaitingApprove]: {
    progress: 30,
    label: "Chờ duyệt",
    color: "#B9C3C5",
    value: ProcessRFIStatus.WaitingApprove,
  },
  [ProcessRFIStatus.Pending]: {
    progress: 50,
    label: "Đang thực hiện",
    color: "#FFB300",
    value: ProcessRFIStatus.Pending,
  },
  [ProcessRFIStatus.Approved]: {
    progress: 100,
    label: "Hoàn thành",
    color: "#43A047",
    value: ProcessRFIStatus.Approved,
  },
  [ProcessRFIStatus.Rejected]: {
    progress: 100,
    label: "Bị từ chối",
    color: "#E53935",
    value: ProcessRFIStatus.Rejected,
    showProgressBar: false,
  },
};

export interface RFI {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  code: string;
  title: string;
  status: RFIStatus;
  description: string;
  requestInfo?: string; // Thông tin yêu cầu
  responseInfo?: string; // trả lời yêu cầu
  codeDraw: string; // Số bản vẽ
  specifications?: string; // Thông số
  files: string; // Tệp đính kèm
  isActive: string;
  dueAt: number; // Ngày đến hạn
  isPrivate: boolean; // Riêng tư
  requestDate: string; // Ngày yêu cầu
  responseRequestedDate: string; // Ngày yêu cầu phản hồi
  responseDate: string; // Ngày phản hồi
  rfiCategory: Dictionary;
  responsibility: Staff; // Người phụ trách
  responsibilityMemberShip: MemberShip; // Người phụ trách
  // Thông tin bên gửi / nhận
  sender: Staff; // người gửi
  recipient: Staff; // người nhận
  senderMemberShip: MemberShip; // người gửi
  recipientMemberShip: MemberShip; // người nhận
  sendCompany: Company; // công ty gửi
  receiverCompany: Company;
  senderLevel: Dictionary; // chức vụ người gửi
  receiverLevel: Dictionary; // chức vụ người nhận
  // Thông tin yêu cầu
  staff: Staff; // người yêu cầu
  staffLevel: Dictionary; // chức vụ người yêu cầu
  senderAttachments: FileAttach[]; // file đinh kèm yêu cầu
  // Thông tin phản hồi
  responders: Staff[]; // người phản hồi
  responderMemberShips: MemberShip[]; // người phản hồi
  responderLevel: Dictionary;
  receiverAttachments: FileAttach[]; // file đinh kèm phản hồi
  // người duyệt danh sách và lịch sử
  approveStaffs: Staff[];
  approvalHistories: ApprovalHistory[];
  // người follow
  followStaffs: Staff[]; // người theo dõi
  followMemberShips: MemberShip[]; // người theo dõi
  // Danh sách nhận thông báo bổ sung (CC), gồm các bên liên quan khác.
  distributions: Staff[];
  project: Project;
  approvalLists: ApprovalList[];
  commentConfirm: Comment | null; // chốt phản hồi
  // info create update
  createdBy: Staff;
  updatedBy: Staff;
  //
  //
  //
  //
  //
}
