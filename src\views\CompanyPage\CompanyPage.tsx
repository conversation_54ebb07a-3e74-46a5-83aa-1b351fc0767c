import {
  But<PERSON>,
  Card,
  message,
  Modal,
  Select,
  Space,
  Tooltip,
  Tag,
} from "antd";
import { Pagination } from "components/Pagination";
import { useEffect, useRef, useState, useMemo } from "react";
import { getTitle } from "utils";
import { Link, useNavigate } from "react-router-dom";
import { checkRoles } from "utils/auth";
import { PermissionNames } from "types/PermissionNames";
import { permissionStore } from "store/permissionStore";
import PageTitle from "components/PageTitle/PageTitle";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import PencilIcon from "assets/svgs/PencilIcon";
import CustomButton from "components/Button/CustomButton";
import DeleteIcon from "assets/svgs/DeleteIcon";
import CustomInput from "components/Input/CustomInput";
import { TableProps } from "antd/lib";
import { ImportOutlined } from "@ant-design/icons";
import { useTransition } from "hooks/useTransition";
import { removeSubstringFromKeys } from "utils/common";
import { exportTemplateWithValidation } from "utils/TemplateExcel";
import { observer } from "mobx-react";
import { unixToDate, unixToFullDate } from "utils/dateFormat";
import { appStore } from "store/appStore";
import { useCompany } from "hooks/useCompany";
import { Company } from "types/company";
import { companyApi } from "api/company.api";
import { handleExport, MyExcelColumn } from "utils/MyExcel";
import QueryLabel from "components/QueryLabel/QueryLabel";
import ImportCompanies, {
  ImportCompanyModal,
} from "components/ImportDocument/ImportCompanies";
import EditButton from "components/Button/EditButton";
import DeleteButton from "components/Button/DeleteButton";
import { excelDateToDayjs } from "utils/date";

export const CompanyPage = observer(({ title = "" }) => {
  const { haveAddPermission, haveDeletePermission, haveEditPermission } =
    checkRoles(
      {
        add: PermissionNames.companyAdd,
        edit: PermissionNames.companyEdit,
        delete: PermissionNames.companyDelete,
      },
      permissionStore.permissions
    );

  const importModal = useRef<ImportCompanyModal>();
  const [loadingDownloadDemo, setLoadingDownloadDemo] = useState(false);
  const { isLoaded } = useTransition();

  const navigate = useNavigate();
  const {
    fetchData,
    loading,
    query,
    setQuery,
    total,
    isEmptyQuery,
    companies,
  } = useCompany({
    initQuery: { limit: 20, page: 1 },
  });

  useEffect(() => {
    document.title = getTitle(title);
  }, []);

  useEffect(() => {
    if (isLoaded) fetchData();
  }, [isLoaded]);

  const handleTableChange: TableProps<any>["onChange"] = (
    pagination,
    filters,
    sorter
  ) => {
    if (!Array.isArray(sorter)) {
      const fieldMap: Record<string, string> = {
        name: "company.name",
        code: "company.code",
        phone: "company.phone",
        email: "company.email",
        address: "company.address",
        taxCode: "company.taxCode",
        type: "company.type",
        createdAt: "company.createdAt",
        isActive: "isActive",
      };
      const columnKey = sorter.field || sorter.column?.key;

      if (!sorter.order) {
        query.queryObject = undefined;
      } else {
        const order = sorter.order === "ascend" ? "ASC" : "DESC";
        const field = fieldMap[columnKey as string];
        query.queryObject = JSON.stringify([
          { type: "sort", field, value: order },
        ]);
      }

      setQuery({ ...query });
      fetchData();
    }
  };

  const handleDeleteCompany = async (id: number) => {
    try {
      await companyApi.delete(id);
      message.success("Xóa công ty thành công");
      fetchData();
    } catch (e) {
      console.error(e);
      message.error("Có lỗi xảy ra khi xóa công ty");
    }
  };

  const handleDownloadDemoExcel = async () => {
    const link = document.createElement("a");
    link.href = "/exportFile/file_mau_nhap_company.xlsx";
    link.download = "file_mau_nhap_company.xlsx";
    link.click();
  };

  const exportColumns: MyExcelColumn<Company>[] = [
    {
      header: "Mã công ty",
      key: "code",
      columnKey: "code",
      headingStyle: { font: { bold: true } },
    },
    {
      header: "Tên công ty",
      key: "name",
      columnKey: "name",
      headingStyle: { font: { bold: true } },
    },
    {
      header: "Điện thoại",
      key: "phone",
      columnKey: "phone",
      headingStyle: { font: { bold: true } },
    },
    {
      header: "Email",
      key: "email",
      columnKey: "email",
      headingStyle: { font: { bold: true } },
    },
    {
      header: "Địa chỉ",
      key: "address",
      columnKey: "address",
      headingStyle: { font: { bold: true } },
    },
    {
      header: "Mã số thuế",
      key: "taxCode",
      columnKey: "taxCode",
      headingStyle: { font: { bold: true } },
    },
    {
      header: "Ngày bắt đầu",
      key: "startAt",
      columnKey: "startAt",
      headingStyle: { font: { bold: true } },
      render: (record: Company) => unixToDate(record.startAt),
    },
    {
      header: "Trạng thái",
      key: "isActive",
      columnKey: "isActive",
      headingStyle: { font: { bold: true } },
      render: (record: Company) => (record.isActive ? "Hoạt động" : "Bị khóa"),
    },
  ];

  const handleRowClick = (record: Company) => {
    navigate(
      `/system-config/${PermissionNames.companyEdit.replace(
        ":id",
        String(record.id)
      )}`
    );
  };

  const columns: CustomizableColumn<Company>[] = [
    {
      title: "Mã",
      dataIndex: "code",
      key: "code",
      sorter: true,
      width: 120,
      render: (_, record) => (
        <div
          className="text-[#1677ff] cursor-pointer font-medium"
          onClick={() => handleRowClick(record)}
        >
          {record.code}
        </div>
      ),
    },
    {
      title: "Tên công ty",
      dataIndex: "name",
      key: "name",
      sorter: true,
      width: 200,
      render: (_, record) => <span className="font-medium">{record.name}</span>,
    },
    {
      title: "Điện thoại",
      dataIndex: "phone",
      key: "phone",
      sorter: true,
      width: 130,
      render: (_, record) => <span>{record.phone}</span>,
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
      sorter: true,
      width: 200,
      render: (_, record) => (
        <span className="text-blue-600">{record.email}</span>
      ),
    },
    {
      title: "Địa chỉ",
      dataIndex: "address",
      key: "address",
      sorter: true,
      width: 250,
      render: (_, record) => <span>{record.address}</span>,
    },
    {
      title: "Mã số thuế",
      dataIndex: "taxCode",
      key: "taxCode",
      sorter: true,
      width: 120,
      render: (_, record) => <span>{record.taxCode}</span>,
    },
    {
      title: "Ngày bắt đầu",
      dataIndex: "startAt",
      key: "startAt",
      sorter: true,
      width: 150,
      render: (_, record) => <span>{unixToDate(record.startAt)}</span>,
    },
    {
      title: "Trạng thái",
      dataIndex: "isActive",
      key: "isActive",
      align: "center",
      width: 130,
      sorter: true,
      render: (isActive) => (
        <Tag className="status-tag" color={isActive ? "green" : "red"}>
          {isActive ? "Hoạt động" : "Bị khóa"}
        </Tag>
      ),
    },
    {
      title: "Xử lý",
      key: "action",
      fixed: "right",
      width: 100,
      align: "center",
      alwaysVisible: true,
      render: (_, record) => (
        <Space>
          {haveEditPermission && (
            <EditButton
              onClick={(e) => {
                e.stopPropagation();
                navigate(
                  `/system-config/${PermissionNames.companyEdit.replace(
                    ":id",
                    String(record.id)
                  )}?update=1`
                );
              }}
            />
          )}
          {haveDeletePermission && (
            <DeleteButton
              onClick={(e) => {
                e.stopPropagation();
                Modal.confirm({
                  title: `Xóa công ty "${record.name}"`,
                  getContainer: () =>
                    document.getElementById("App") as HTMLElement,
                  icon: null,
                  content: (
                    <>Bạn sẽ không thể khôi phục dữ liệu này sau khi xóa.</>
                  ),
                  footer: (_, { OkBtn, CancelBtn }) => (
                    <>
                      <CustomButton
                        variant="outline"
                        onClick={() => {
                          handleDeleteCompany(record.id);
                          Modal.destroyAll();
                        }}
                      >
                        Có
                      </CustomButton>
                      <CustomButton onClick={() => Modal.destroyAll()}>
                        Không
                      </CustomButton>
                    </>
                  ),
                });
              }}
            />
          )}
        </Space>
      ),
    },
  ];

  const handleOnUploadedFile = async (excelData: any, setData: any) => {
    const importData = excelData.results?.map((item: any) => {
      const refineRow = removeSubstringFromKeys(item, " *");
      const isActive =
        refineRow["Trạng thái"] === "Hoạt động"
          ? true
          : refineRow["Trạng thái"] === "Bị khóa"
          ? false
          : undefined;
      const startDate = refineRow["Ngày bắt đầu"]
        ? excelDateToDayjs(refineRow["Ngày bắt đầu"])
        : "";

      return {
        code: refineRow["Mã công ty"] || "",
        name: refineRow["Tên công ty"] || "",
        phone: refineRow["Điện thoại"] || "",
        phone2: refineRow["Điện thoại 2"] || "",
        otherContact: refineRow["Liên hệ khác"] || "",
        email: refineRow["Email"] || "",
        address: refineRow["Địa chỉ"] || "",
        taxCode: refineRow["Mã số thuế"] || "",
        note: refineRow["Ghi chú"] || "",
        startDate,
        isActive,
        rowNum: item.__rowNum__,
      };
    });
    setData(importData);
  };

  return (
    <div>
      <PageTitle
        title={title}
        breadcrumbs={["Cấu hình", title]}
        extra={
          haveAddPermission && (
            <Space>
              <CustomButton
                size="small"
                showPlusIcon
                onClick={() =>
                  navigate(`/system-config/${PermissionNames.companyAdd}`)
                }
              >
                Tạo công ty
              </CustomButton>
              <CustomButton
                size="small"
                icon={<ImportOutlined />}
                onClick={() => importModal.current?.open()}
              >
                Nhập excel
              </CustomButton>
            </Space>
          )
        }
      />

      <div className="app-container">
        <Card>
          <div className="flex gap-[16px] items-end pb-[12px] justify-between flex-wrap">
            <div className="flex gap-[16px] items-end">
              <div className="w-[300px]">
                <CustomInput
                  tooltipContent={"Tìm theo mã, tên công ty"}
                  label="Tìm kiếm"
                  placeholder="Tìm kiếm"
                  onPressEnter={() => {
                    query.page = 1;
                    setQuery({ ...query });
                    fetchData();
                  }}
                  value={query.search}
                  onChange={(value) => {
                    if (!value) {
                      delete query.search;
                      setQuery({ ...query });
                      fetchData();
                    } else {
                      query.search = value;
                      setQuery({ ...query });
                    }
                  }}
                  allowClear
                />
              </div>
              <div>
                <QueryLabel>Trạng thái</QueryLabel>
                <Select
                  value={query.isActive}
                  options={[
                    { label: "Hoạt động", value: true },
                    { label: "Bị khóa", value: false },
                  ]}
                  placeholder="Tất cả trạng thái"
                  allowClear
                  onChange={(value) => setQuery({ ...query, isActive: value })}
                />
              </div>

              <CustomButton
                onClick={() => {
                  query.page = 1;
                  setQuery({ ...query });
                  fetchData();
                }}
              >
                Áp dụng
              </CustomButton>
              {!isEmptyQuery && (
                <CustomButton
                  variant="outline"
                  onClick={() => {
                    delete query.isActive;
                    delete query.type;
                    delete query.search;
                    setQuery({ ...query });
                    fetchData();
                  }}
                >
                  Bỏ lọc
                </CustomButton>
              )}
            </div>
            <CustomButton
              onClick={() => {
                Modal.confirm({
                  title: `Bạn có muốn xuất file excel?`,
                  getContainer: () => {
                    return document.getElementById("App") as HTMLElement;
                  },
                  icon: null,

                  footer: (_, { OkBtn, CancelBtn }) => (
                    <>
                      <CustomButton
                        variant="outline"
                        className="cta-button"
                        onClick={() => {
                          handleExport({
                            onProgress(percent) {
                              console.log("What is percent", percent);
                            },
                            exportColumns,
                            fileType: "xlsx",
                            dataField: "companies",
                            query: query,
                            api: companyApi.findAll,
                            fileName: "Danh sách công ty",
                            sheetName: "Danh sách công ty",
                          });
                          Modal.destroyAll();
                        }}
                      >
                        Có
                      </CustomButton>
                      <CustomButton
                        onClick={() => {
                          Modal.destroyAll();
                        }}
                        className="cta-button"
                      >
                        Không
                      </CustomButton>
                    </>
                  ),
                });
              }}
            >
              Xuất excel
            </CustomButton>
          </div>

          <CustomizableTable
            columns={columns}
            dataSource={companies}
            rowKey="id"
            loading={loading}
            pagination={false}
            scroll={{ x: 1400 }}
            bordered
            displayOptions
            tableId="company-page"
            //@ts-ignore
            onChange={handleTableChange}
            onRowClick={handleRowClick}
          />

          <Pagination
            currentPage={query.page}
            defaultPageSize={query.limit}
            total={total}
            onChange={({ limit, page }) => {
              query.page = page;
              query.limit = limit;
              setQuery({ ...query });
              fetchData();
            }}
          />

          {useMemo(
            () => (
              <ImportCompanies
                guide={[
                  "Vui lòng tải và sử dụng file mẫu để import dữ liệu đúng cách",
                  "Không được thay đổi tiêu đề trong file csv/excel mẫu để tránh import thiếu dữ liệu",
                  "Chi tiết xem tại sheet Hướng dẫn của file import mẫu",
                  "Các trường bắt buộc: Mã công ty, Tên công ty, Điện thoại, Email",
                ]}
                onSuccess={() => {
                  query.page = 1;
                  fetchData();
                }}
                ref={importModal}
                createApi={companyApi.create}
                onUploaded={(excelData, setData) =>
                  handleOnUploadedFile(excelData, setData)
                }
                okText={`Nhập công ty ngay`}
                onDownloadDemoExcel={handleDownloadDemoExcel}
              />
            ),
            [loadingDownloadDemo]
          )}
        </Card>
      </div>
    </div>
  );
});
