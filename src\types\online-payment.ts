export enum EOnlinePayment {
  EWallet = "E-WALLET", //ví điện tử
  Bank = "BANK", // ngân hàng
}

export const EOnlinePaymentTrans = {
  [EOnlinePayment.Bank]: {
    label: "<PERSON>ân hàng",
    value: EOnlinePayment.Bank,
  },
  [EOnlinePayment.EWallet]: {
    label: "Ví điện tử",
    value: EOnlinePayment.EWallet,
  },
};

export interface OnlinePayment {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  deletedAt: number;
  name: string;
  icon: string;
  ownerName: string;
  bankNumber: string;
  qrCode: string; //code hoặc hình ảnh QR
  isEnabled: boolean;
  // orders: Order[];
  bank: Bank;
  type: EOnlinePayment;
}

export interface Bank {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  name: string;
  code: string;
  fullName: string;
  logo: string;
}
