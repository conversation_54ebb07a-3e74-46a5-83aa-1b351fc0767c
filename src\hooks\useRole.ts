import { roleApi } from "api/role.api";
import { useState } from "react";
import { Role } from "types/role";
import { QueryParam } from "types/query";

export interface RoleQuery extends QueryParam {}

interface UseRoleProps {
  initQuery: RoleQuery;
}

export const useRole = ({ initQuery }: UseRoleProps) => {
  const [data, setData] = useState<Role[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<RoleQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async (newQuery?: RoleQuery) => {
    setLoading(true);
    try {
      const { data } = await roleApi.findAll({ ...query, ...newQuery });

      setData(data.roles);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return {
    roles: data,
    totalRole: total,
    fetchRole: fetchData,
    loadingRole: loading,
    setQueryRole: setQuery,
    queryRole: query,
  };
};
