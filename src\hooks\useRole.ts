import { roleApi } from "api/role.api";
import { useState } from "react";
import { Role } from "types/role";
import { QueryParam } from "types/query";

export interface RoleQuery extends QueryParam {}

export const useRole = () => {
  const [data, setData] = useState<Role[]>([]);
  const [total, setTotal] = useState(0);

  const fetchData = async (query: RoleQuery) => {
    const { data } = await roleApi.findAll(query);

    // debugger
    setData(data.roles);
    setTotal(data.total);
  };

  return { roles: data, total, fetchRole: fetchData };
};
