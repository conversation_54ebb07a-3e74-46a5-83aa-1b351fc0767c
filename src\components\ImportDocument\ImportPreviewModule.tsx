import { <PERSON><PERSON>, But<PERSON>, message, Table } from "antd";
import { useMemo, useRef, useState, useEffect } from "react";
import { PreviewImportModal } from "./PreviewImportModal";

interface ImportPreviewModuleProps<T> {
  data?: T[];
  onValidateData?: (data: T[]) => T[];
  requiredFields?: (keyof T)[];
  duplicateCheckFields?: (keyof T)[];
  columns: any[];
  title?: string;
  previewButtonText?: string;
  onValidationStatusChange?: (hasErrors: boolean) => void;
  showAutoMessage?: boolean;
  dataReturn?: { data: any[]; successCount: number; errorCount: number };
}

const ImportPreviewModule = <T extends Record<string, any>>({
  data,
  onValidateData,
  requiredFields = [],
  duplicateCheckFields = [],
  columns,
  title = "Xem danh sách dữ liệu",
  previewButtonText = "Xem danh sách",
  onValidationStatusChange,
  showAutoMessage = false,
  dataReturn,
}: ImportPreviewModuleProps<T>) => {
  const previewModal = useRef<PreviewImportModal>(null);
  const [validatedData, setValidatedData] = useState<T[]>();
  const isAutoPreview = useRef(true);

  const getFieldTitle = (fieldKey: keyof T): string => {
    const column = columns.find((col) => col.dataIndex === fieldKey);
    return column?.title || String(fieldKey);
  };

  const validateRequiredFields = (items: T[]): T[] => {
    console.log("✅ validateRequiredFields called with:", items);
    return items.map((item, index) => {
      const errors: string[] = [];

      requiredFields.forEach((field) => {
        const value = item[field];
        console.log(
          `🔍 Row ${index + 1} - Checking required field ${String(field)}:`,
          value
        );

        if (
          value === null ||
          value === undefined ||
          value === "" ||
          (typeof value === "string" && value.trim() === "")
        ) {
          const fieldTitle = getFieldTitle(field);
          errors.push(`• Thiếu trường bắt buộc "${fieldTitle}"`);
        }
      });

      // Preserve existing errorMessage and append new errors
      const existingErrors = item.errorMessage || "";
      const newErrors = errors.length > 0 ? errors.join("\n") : "";
      const combinedErrors = [existingErrors, newErrors].filter(Boolean).join("\n");

      const result = {
        ...item,
        errorMessage: combinedErrors || undefined,
      };

      console.log("✅ Required validation result for row", index + 1, ":", {
        hasErrors: errors.length > 0,
        errors,
        errorMessage: result.errorMessage,
      });

      return result;
    });
  };

  const validateDuplicates = (items: T[]): T[] => {
    if (duplicateCheckFields.length === 0) {
      return items;
    }

    console.log(
      "🔍 validateDuplicates called with fields:",
      duplicateCheckFields
    );

    const seenValues = new Map<string, number[]>();

    items.forEach((item, index) => {
      duplicateCheckFields.forEach((field) => {
        const value = item[field];

        if (
          value !== null &&
          value !== undefined &&
          value !== "" &&
          (typeof value !== "string" || value.trim() !== "")
        ) {
          const normalizedValue = String(value).trim().toLowerCase();
          const key = `${String(field)}:${normalizedValue}`;

          if (!seenValues.has(key)) {
            seenValues.set(key, []);
          }
          seenValues.get(key)!.push(index);
        }
      });
    });

    return items.map((item, index) => {
      const duplicateErrors: string[] = [];

      duplicateCheckFields.forEach((field) => {
        const value = item[field];

        if (
          value !== null &&
          value !== undefined &&
          value !== "" &&
          (typeof value !== "string" || value.trim() !== "")
        ) {
          const normalizedValue = String(value).trim().toLowerCase();
          const key = `${String(field)}:${normalizedValue}`;
          const occurrences = seenValues.get(key) || [];

          if (occurrences.length > 1) {
            const duplicateRows = occurrences.map((i) => i + 1).join(", ");
            const fieldTitle = getFieldTitle(field);
            duplicateErrors.push(
              `Trường "${fieldTitle}" bị trùng lặp với dòng: ${duplicateRows}`
            );
          }
        }
      });

      console.log(`🔍 Duplicate check for row ${index + 1}:`, {
        item,
        duplicateErrors,
      });

      return {
        ...item,
        duplicateErrors:
          duplicateErrors.length > 0 ? duplicateErrors : undefined,
      };
    });
  };

  useEffect(() => {
    isAutoPreview.current = true; // check neu co data excel moi thi hien table
  }, [data]);

  useEffect(() => {
    if (!data || data.length === 0) {
      setValidatedData(undefined);
      onValidationStatusChange?.(false);
      return;
    }

    console.log("🚀 Auto-validating data on change");
    try {
      let processedData = validateRequiredFields(data);
      console.log("📝 After required validation:", processedData);

      processedData = validateDuplicates(processedData);
      console.log("📝 After duplicate validation:", processedData);

      if (onValidateData) {
        processedData = processedData.map((item) => {
          const customValidated = onValidateData([item])[0];

          const requiredErrors = item.errorMessage || "";
          const duplicateErrors = item.duplicateErrors
            ? item.duplicateErrors.map((err: string) => `• ${err}`).join("\n")
            : "";
          const customErrors = customValidated.errorMessage || "";

          let finalErrors = "";
          const errorParts = [
            requiredErrors,
            duplicateErrors,
            customErrors,
          ].filter(Boolean);
          finalErrors = errorParts.join("\n");

          return {
            ...customValidated,
            errorMessage: finalErrors || undefined,
            duplicateErrors: item.duplicateErrors,
          };
        });
        console.log("📝 After custom validation:", processedData);
      } else {
        processedData = processedData.map((item) => {
          const requiredErrors = item.errorMessage || "";
          const duplicateErrors = item.duplicateErrors
            ? item.duplicateErrors.map((err: string) => `• ${err}`).join("\n")
            : "";

          let finalErrors = "";
          const errorParts = [requiredErrors, duplicateErrors].filter(Boolean);
          finalErrors = errorParts.join("\n");

          return {
            ...item,
            errorMessage: finalErrors || undefined,
          };
        });
      }

      setValidatedData(processedData);

      const errorCount = processedData.filter(
        (item) => item.errorMessage
      ).length;
      const successCount = processedData.length - errorCount;

      if (isAutoPreview.current) {
        handleClickPreview(processedData, errorCount);
      }

      console.log("📊 Final validation summary:", {
        total: processedData.length,
        successCount,
        errorCount,
        validationDetails: processedData.map((item, idx) => ({
          row: idx + 1,
          hasError: !!item.errorMessage,
          errorMessage: item.errorMessage,
          duplicateErrors: item.duplicateErrors,
        })),
      });

      const hasErrors = errorCount > 0;
      onValidationStatusChange?.(hasErrors);
    } catch (error) {
      console.error("❌ Auto-validation error:", error);
      onValidationStatusChange?.(true);
    }
  }, [
    data,
    requiredFields,
    duplicateCheckFields,
    onValidateData,
    onValidationStatusChange,
  ]);

  const isError = useMemo(
    () => validatedData?.some((item) => !!item.errorMessage),
    [validatedData]
  );

  const successCount = useMemo(
    () => validatedData?.filter((item) => !item.errorMessage).length || 0,
    [validatedData]
  );

  const errorCount = useMemo(
    () => validatedData?.filter((item) => !!item.errorMessage).length || 0,
    [validatedData]
  );

  const handleClickPreview = (validatedData?: T[], errorCount?: number) => {
    if (validatedData) {
      isAutoPreview.current = false;
      previewModal.current?.open(validatedData, false, !!errorCount);
    }
  };

  console.log("🎨 ImportPreviewModule RENDER:", {
    hasData: !!data,
    dataLength: data?.length,
    hasValidatedData: !!validatedData,
    isError,
    successCount,
    errorCount,
  });

  if (dataReturn) {
    return (
      <Alert
        className="p-3 mt-2"
        type="warning"
        description={
          <div>
            <div className="text-blue-600 font-bold">
              Tổng dòng nhập: {dataReturn.data.length}
            </div>
            <div className="text-green-500">
              Tổng dòng thành công: {dataReturn.successCount}
            </div>
            <div className="text-red-500">
              Tổng dòng thất bại: {dataReturn.errorCount}
            </div>
            <div className="font-bold">Danh sách dòng thất bại</div>
            <div className="border-[1px] border-red-300 border-solid rounded-md overflow-hidden">
              <Table
                columns={[
                  { title: "Dòng", dataIndex: "rowNum" },
                  { title: "Lỗi", dataIndex: "msg" },
                ]}
                dataSource={dataReturn.data.filter(
                  (it) => it.status == "error"
                )}
                pagination={false}
              />
            </div>
          </div>
        }
      ></Alert>
    );
  }

  if (!data || data.length === 0) {
    return null;
  }

  return (
    <div className="relative mb-2 p-4 border border-gray-200 rounded-md bg-gray-50">
      <div className="mb-2 font-medium text-gray-700">
        Quản lý dữ liệu import ({data.length} dòng)
      </div>

      {isError && (
        <div className="mt-1 mb-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-600">
          ⚠️ Có {errorCount} dòng chứa lỗi. Vui lòng "{previewButtonText}" để
          xem chi tiết và sửa lỗi trước khi import.
        </div>
      )}

      {validatedData && !isError && (
        <div className="mt-1 mb-2 p-2 bg-green-50 border border-green-200 rounded text-sm text-green-600">
          ✅ Tất cả {successCount} dòng đều hợp lệ! Bạn có thể tiến hành import.
        </div>
      )}

      <div className="mt-2 flex gap-2">
        <Button
          disabled={!validatedData}
          onClick={() => {
            console.log("👁️ Preview button clicked");
            handleClickPreview(validatedData, errorCount);
          }}
          size="small"
          className="flex-1"
          type={isError ? "default" : "primary"}
        >
          {previewButtonText}{" "}
          {isError ? `(${errorCount} lỗi)` : `(${successCount} hợp lệ)`}
        </Button>
      </div>

      <PreviewImportModal
        ref={previewModal}
        onClose={() => {}}
        onSubmitOk={() => {}}
        columns={columns}
        title={title}
        totalData={data.length}
      />
    </div>
  );
};

export default ImportPreviewModule;
