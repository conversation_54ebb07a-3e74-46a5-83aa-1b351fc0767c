import { request } from "utils/request";
import { AxiosPromise } from "axios";


export const approvalListApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: '/v1/admin/approvalList',
      params
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/approvalList/${id}`,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: '/v1/admin/approvalList',
      data,
      method: 'post'
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/approvalList/${id}`,
      method: 'patch',
      data
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/approvalList/${id}`,
      method: 'delete'
    }),
  batch: (data: any): AxiosPromise<any> =>
    request({
      url: '/v1/admin/approvalList/batch',
      data,
      method: 'post'
    })
}
