import React from "react";
import { QRCodeSVG } from "qrcode.react";

const CLIENT_URL = import.meta.env.VITE_CLIENT_URL as string;

interface QRCodeSectionProps {
  blueprintCode?: string;
  blueprintId?: number;
  title?: string;
  section?: string;
  staff?: any;
  drawCategory?: any;
}

const QRCodeSection: React.FC<QRCodeSectionProps> = ({
  blueprintCode,
  blueprintId,
  title,
  section,
  staff,
  drawCategory,
}) => {
  const generateQRData = () => {
    // Nếu đang ở trang update thì trả về URL hiện tại
    if (window.location.pathname.includes("/update/")) {
      return window.location.href;
    }
    // Nếu tạo mới thì tạo URL với blueprint code
    const code = blueprintCode || "TEMP_CODE";
    return `${CLIENT_URL}/doc-management/blueprint/update/${code}`;
  };

  const hasRequiredData = !!(blueprintCode && title && drawCategory && staff);

  // console.log("QR Code data check:", {
  //   blueprintCode,
  //   title,
  //   drawCategory,
  //   staff,
  //   hasRequiredData,
  //   currentUrl: window.location.href,
  //   generatedQR: generateQRData(),
  // });

  return (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        gap: "16px",
        justifyContent: "flex-start",
      }}
    >
      <div
        style={{
          border: "1px solid #ccc",
          padding: "5px",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        {hasRequiredData ? (
          <QRCodeSVG
            value={generateQRData()}
            size={120}
            level="L"
            fgColor="#000000"
            bgColor="#ffffff"
            marginSize={0}
            includeMargin={false}
          />
        ) : (
          <div
            style={{
              width: "80px",
              height: "80px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              fontSize: "12px",
              color: "#ccc",
              backgroundColor: "#f9f9f9",
              border: "2px dashed #ddd",
            }}
          >
            QR CODE
          </div>
        )}
      </div>

      {/* Text Content */}
      <div style={{ textAlign: "left", flex: 1 }}>
        <p
          style={{
            fontWeight: 500,
            marginBottom: "4px",
            fontSize: "14px",
          }}
        >
          Mã QRCode truy cập nhanh
        </p>
        <p
          style={{
            color: "#666",
            fontSize: "12px",
            margin: 0,
            lineHeight: 1.4,
          }}
        >
          Tự động sinh ra khi điền đầy đủ thông tin bản vẽ
        </p>
      </div>
    </div>
  );
};

export default QRCodeSection;
