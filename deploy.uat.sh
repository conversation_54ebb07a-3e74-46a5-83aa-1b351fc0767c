#!/usr/bin/env sh

# abort on errors
set -e

echo '====================================================================================='
echo '=============================...DEPLOYING UAT...============================='
echo '====================================================================================='

npm run build:uat

echo '====================================================================================='
echo '=====================================...BUILD...====================================='
echo '====================================================================================='

cp .htaccess dist
cd dist

git init
git add -A
git commit -m 'deploy'
git branch -M master

echo '====================================================================================='
echo '==================================...PUSHING GIT...=================================='
echo '====================================================================================='
git push -f https://<EMAIL>/plesk-git/320admin.git master
cd -

rm -rf dist

green=`tput setaf 2`
reset=`tput sgr0`
now=$(date +"%T")

echo "${green}====================================================================================="
echo "${green}=======================...DEPLOY SUCCESS UAT AT $now...======================"
echo "${green}=====================================================================================${reset}"
