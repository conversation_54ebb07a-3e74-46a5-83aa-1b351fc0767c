import { roleApi } from "api/role.api";
import { cloneDeep } from "lodash";
import { makeAutoObservable } from "mobx";
import { makePersistable } from "mobx-persist-store";
import { Route } from "router/RouteType";
import { settings } from "settings";
import { Permission } from "types/permission";

class PermissionStore {
  permissions: Permission[] = [];
  accessRoutes: Route[] = [];

  constructor() {
    makeAutoObservable(this);
    makePersistable(this, {
      name: "PermissionStore",
      properties: ["permissions"],
      storage: localStorage,
    });
  }

  fetchPermissions = async (roleId: number) => {
    const res = await roleApi.findOne(roleId);
    this.permissions = res.data.permissions;
    console.log({ permissions: res.data.permissions.map((p: any) => p.name) });
  };

  setAccessRoutes = () => {
    if (settings.checkPermission && this.permissions.length) {
      for (const route of this.accessRoutes) {
        // if (route.name == "doc-management") debugger;
        // if (route.name == "master-data") debugger;
        // const routeJs = JSON.parse(JSON.stringify(route));
        // if (route?.path?.includes("/customer")) debugger;
        if (!route.children) {
          const find = this.permissions.find((e) => e.name == route.name);
          route.isAccess = !!find;
        } else {
          for (const childRoute of route.children) {
            const find = this.permissions.find(
              // (e) => e.path == route.path + "/" + childRoute.path
              (e) => e.name == childRoute.name
            );

            childRoute.isAccess = !!find;
          }
          route.isAccess = route.children.some((e) => e.isAccess);
        }
      }
      this.accessRoutes = cloneDeep(this.accessRoutes);
    }
  };
}

const permissionStore = new PermissionStore();

export { permissionStore };
