import React from "react";
import { Paper, Typography } from "@mui/material";
import { FavoriteDesign } from "../charts/ChartPage";
import { Table } from "antd";
import Column from "antd/es/table/Column";
import { ProductSelector } from "components/Selector/ProductSeletor";

type Props = {
  title: string;
  data: FavoriteDesign[];
  query: any;
  setQuery: (query: any) => void;
  loading: boolean;
};

export const PopularDesignTable = ({
  title,
  data,
  query,
  setQuery,
  loading = false,
}: Props) => {
  return (
    <div className="!min-h-[370px] !shadow-none bg-white p-4 rounded-md w-full lg:w-[49%]">
      <Typography variant="h6" gutterBottom className="!font-bold">
        {title}
      </Typography>
      <div className="min-w-1/3 max-w-[300px] my-2">
        <ProductSelector
          onChange={(value) => {
            if (value) {
              const newQuery = {
                ...query,
                productId: value || undefined,
              };
              setQuery(newQuery);
            } else {
              const newQuery = {
                ...query,
                productId: undefined,
              };
              setQuery(newQuery);
            }
          }}
        />
      </div>
      <div className="w-full my-4 min-h-[400px]">
        <Table
          loading={loading}
          className="h-full"
          pagination={false}
          rowKey="id"
          dataSource={data!}
          scroll={{ x: "max-content" }}
          components={{
            header: {
              cell: (props: any) => (
                <th
                  {...props}
                  style={{
                    ...props.style,
                    height: 50,
                    background: "black !important",
                    fontWeight: "bold",
                    borderBottom: "1px solid #d9d9d9",
                  }}
                />
              ),
            },
          }}
        >
          <Column
            //   width={300}
            title={"Thành phần"}
            dataIndex="fullname"
            align="left"
            key="fullname"
            className="h-[50px]"
            render={(totalRewardPoint, record: FavoriteDesign) => {
              return <>{record.componentName}</>;
            }}
          />
          <Column
            //   width={300}
            title={"Nguyên vật liệu"}
            dataIndex="fullname"
            align="left"
            key="fullname"
            className="h-[50px]"
            render={(totalRewardPoint, record: FavoriteDesign) => {
              return <>{record.materialName}</>;
            }}
          />
          <Column
            //   width={300}
            title={"Số lượng"}
            dataIndex="quantity"
            align="right"
            key="quantity"
            render={(totalRewardPoint, record: FavoriteDesign) => {
              return <>{record.total}</>;
            }}
          />
          <Column
            //   width={300}
            title={"Đơn vị"}
            dataIndex="unitName"
            align="right"
            key="unitName"
            render={(totalRewardPoint, record: FavoriteDesign) => {
              return <>{record.unitName}</>;
            }}
          />
        </Table>
      </div>
    </div>
  );
};
