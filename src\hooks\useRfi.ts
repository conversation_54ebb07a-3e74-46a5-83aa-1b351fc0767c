import { rfiApi } from "api/rfi.api";
import { useState } from "react";
import { RFI } from "types/rfi";
import { QueryParam } from "types/query";

export interface RfiQuery extends QueryParam {}

interface UseRfiProps {
  initQuery: RfiQuery;
}

export const useRfi = ({ initQuery }: UseRfiProps) => {
  const [data, setData] = useState<RFI[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<RfiQuery>(initQuery);
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState<string | null>(null);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await rfiApi.findAll(query);

      setData(data.rfis);
      setTotal(data.total);
      setStatus(data.status);
    } finally {
      setLoading(false);
    }
  };

  return {
    rfis: data,
    totalRfi: total,
    fetchRfi: fetchData,
    loadingRfi: loading,
    setQueryRfi: setQuery,
    queryRfi: query,
    statusRfi: status,
  };
};
