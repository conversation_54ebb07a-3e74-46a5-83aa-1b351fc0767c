import React, { useRef } from "react";
import { Pagination as AntPagination, PaginationProps, Select } from "antd";
import { formatVND } from "utils";
import CustomInput from "components/Input/CustomInput";
import { IoIosArrowBack, IoIosArrowForward } from "react-icons/io";

export interface IPagination
  extends Omit<
    PaginationProps,
    "onChange" | "total" | "current" | "defaultPageSize"
  > {
  total: number;
  totalText?: string;
  onChange: ({ page, limit }: { page: number; limit: number }) => void;
  currentPage: number;
  defaultPageSize?: number;
  showQuickJumper?: boolean;
  showSizeChange?: boolean;
}

export const Pagination = ({
  total,
  onChange,
  showQuickJumper,
  showSizeChange = false,
  currentPage,
  defaultPageSize = 50,
  totalText = "dòng",
  ...restProps
}: IPagination) => {
  // Tính số item thực tế hiển thị trên trang hiện tại
  const actualItemsShown = Math.min(
    defaultPageSize,
    total - (currentPage - 1) * defaultPageSize
  );
  const startItem = total === 0 ? 0 : (currentPage - 1) * defaultPageSize + 1;
  const endItem = Math.min(currentPage * defaultPageSize, total);

  return (
    <div className="custom-pagination flex justify-between gap-2 mt-[16px]">
      <div className="flex gap-[11px] items-center">
        <div className="flex-shrink-0">Hiển thị</div>
        <Select
          className="!w-[80px] !min-w-[50px]"
          placeholder=""
          value={defaultPageSize}
          options={[
            { label: "5", value: 5 },
            { label: "10", value: 10 },
            { label: "20", value: 20 },
            { label: "50", value: 50 },
            { label: "100", value: 100 },
          ]}
          onChange={(value) => {
            onChange({
              page: 1,
              limit: Number(value),
            });
          }}
        />
        <div className="flex-shrink-0">
          {Math.min(
            defaultPageSize,
            total - (currentPage - 1) * defaultPageSize
          )}{" "}
          trong {formatVND(total)} {totalText}
        </div>
      </div>
      <AntPagination
        current={currentPage}
        total={total}
        showSizeChanger={showSizeChange}
        pageSize={defaultPageSize}
        onChange={(page, limit) => {
          onChange({
            page,
            limit,
          });
          document.body.scrollTop = 0; // For Safari
          document.documentElement.scrollTop = 0;
        }}
        // nextIcon={}
        nextIcon={
          <IoIosArrowForward
            size={24}
            className="translate-y-[6px]"
            style={{
              color: "var(--color-neutral-n8)",
              opacity:
                currentPage == Math.ceil(total / defaultPageSize) ? "20%" : "",
            }}
          />
        }
        prevIcon={
          <IoIosArrowBack
            size={24}
            className="translate-y-[6px]"
            style={{
              color: "var(--color-neutral-n8)",
              opacity: currentPage == 1 ? "20%" : "",
            }}
          />
        }
        // onShowSizeChange={(limit) => {
        //   onChange({
        //     page: currentPage,
        //     limit,
        //   });
        //   document.body.scrollTop = 0; // For Safari
        //   document.documentElement.scrollTop = 0;
        // }}
        showQuickJumper={showQuickJumper}
        defaultPageSize={defaultPageSize}
        // showTotal={(total) => `Tổng ${formatVND(total)} ${totalText}`}
        {...restProps}
      />
    </div>
  );
};
