.custom-input-container {
  display: flex;
  flex-direction: column;
  // margin-bottom: 8px;
  width: 100%;
  border-radius: 0px !important;

  .custom-input-label {
    margin-bottom: 4px;
    color: var(--color-neutral-n8); // Thay đổi từ #000000
    font-weight: 700;
    font-size: 13px;
    line-height: 18px;
    letter-spacing: 0%;

    .custom-input-required {
      color: var(--color-accent); // Thay đổi từ #ff4d4f
      margin-left: 2px;
    }
  }

  .custom-input:not(textarea) {
    height: 35px;
  }

  .custom-input {
    width: 100%;
    background-color: var(--color-neutral-n0);
    color: var(--color-neutral-n8);
    border-radius: 5px !important;

    // Password toggle icon styling
    .ant-input-suffix {
      .anticon {
        transition: color 0.2s ease;

        &:hover {
          color: var(--color-primary) !important;
        }
      }
    }

    // Ensure consistent height for Select components
    &.ant-select {
      border: none !important; // Remove border from select wrapper

      .ant-select-selector {
        font-size: 13px;
        // height: 30px;
        padding: 6px 12px 6px 16px !important;

        .ant-select-selection-search-input {
          // height: 30px !important;
        }

        .ant-select-selection-placeholder,
        .ant-select-selection-item {
          line-height: 18px !important;
        }

        .ant-select-selection-placeholder {
          color: var(--color-neutral-n5) !important;
        }
      }
    }

    // Ensure consistent height for DatePicker components
    &.ant-picker {
      border: none !important; // Remove border from picker wrapper
      // min-height: 30px !important;
      padding: 4px 11px !important;

      .ant-picker-input > input {
        height: 30px !important;
        line-height: 30px !important;
      }

      .ant-picker-input > input::placeholder {
        color: var(--color-neutral-n5) !important;
      }
    }

    &.custom-input-normal {
      border: 1px solid var(--color-neutral-n3); // Thay đổi từ #d9d9d9

      // Border cho input/select chưa có giá trị
      &:placeholder-shown:not(:focus) {
        border: 1px solid #b9c3c5 !important;
      }

      // Cho input khi không có value
      &:not(:focus):invalid {
        border: 1px solid #b9c3c5 !important;
      }

      // Cho select khi không có value được chọn
      &.ant-select:not(.ant-select-focused) {
        .ant-select-selector {
          &:not(.ant-select-selector-selected) {
            border: 1px solid #b9c3c5 !important;
          }
        }
      }

      // Cho select khi placeholder đang hiển thị
      &.ant-select .ant-select-selector {
        &:has(.ant-select-selection-placeholder) {
          border: 1px solid #b9c3c5 !important;
        }
      }

      &:hover,
      &:focus {
        border-color: var(--color-primary); // Thay đổi từ #40a9ff
      }

      .ant-select-selector {
        border: 1px solid var(--color-neutral-n3) !important;
        background-color: var(--color-neutral-n0) !important;
        color: var(--color-neutral-n8) !important;
        border-radius: 5px !important;

        &:hover {
          border-color: var(--color-primary) !important;
        }
      }

      &.ant-select-focused .ant-select-selector {
        border-color: var(--color-primary) !important;
        box-shadow: 0 0 0 2px var(--color-primary-overlay-20) !important; // Sử dụng overlay
      }

      .ant-select-arrow {
        color: var(--color-neutral-n5) !important;
      }

      // DatePicker styles
      &.ant-picker {
        border: 1px solid var(--color-neutral-n3) !important;
        background-color: var(--color-neutral-n0) !important;
        color: var(--color-neutral-n8) !important;
        border-radius: 0px !important;

        &:hover {
          border-color: var(--color-primary) !important;
        }

        &.ant-picker-focused {
          border-color: var(--color-primary) !important;
          box-shadow: 0 0 0 2px var(--color-primary-overlay-20) !important;
        }

        .ant-picker-suffix {
          color: var(--color-neutral-n5) !important;
        }
      }
    }

    &.custom-input-disabled {
      background-color: var(--color-neutral-n2); // Thay đổi từ #f5f5f5
      border-color: var(--color-neutral-n4);
      color: var(--color-disabled-text);
      cursor: not-allowed;

      .ant-select-selector {
        background-color: var(--color-neutral-n2) !important;
        border-color: var(--color-neutral-n4) !important;
        color: var(--color-disabled-text) !important;
        cursor: not-allowed !important;
        border-radius: 5px !important;
      }

      .ant-select-arrow {
        color: var(--color-disabled-text) !important;
      }

      // DatePicker disabled styles
      &.ant-picker {
        background-color: var(--color-neutral-n2) !important;
        border-color: var(--color-neutral-n4) !important;
        color: var(--color-disabled-text) !important;
        cursor: not-allowed !important;
        border-radius: 0px !important;

        .ant-picker-suffix {
          color: var(--color-disabled-text) !important;
        }
      }
    }

    &.custom-input-error {
      border-color: var(--color-accent); // Sử dụng accent color cho error

      &:hover,
      &:focus {
        border-color: var(--color-accent);
        opacity: 0.8;
      }

      .ant-select-selector {
        border-color: var(--color-accent) !important;
        border-radius: 0px !important;

        &:hover {
          border-color: var(--color-accent) !important;
          opacity: 0.8;
        }
      }

      &.ant-select-focused .ant-select-selector {
        border-color: var(--color-accent) !important;
        box-shadow: 0 0 0 2px rgba(237, 28, 36, 0.2) !important; // Semi-transparent accent
      }

      // DatePicker error styles
      &.ant-picker {
        border-color: var(--color-accent) !important;
        border-radius: 0px !important;

        &:hover {
          border-color: var(--color-accent) !important;
          opacity: 0.8;
        }

        &.ant-picker-focused {
          border-color: var(--color-accent) !important;
          box-shadow: 0 0 0 2px rgba(237, 28, 36, 0.2) !important;
        }
      }
    }
  }

  .custom-input-error-message {
    padding-top: 2px;
    color: var(--color-accent);
    font-size: 14px;
    margin-top: 4px;
    font-weight: 300;
  }

  /* Ghi đè chiều cao của select multiple */
  .custom-input.ant-select-multiple .ant-select-selector {
    min-height: 40px !important; /* Đảm bảo không bị thu nhỏ */
    height: 40px !important; /* Cố định chiều cao */
    display: flex;
    align-items: center; /* Căn giữa nội dung */
    overflow: hidden; /* Nếu tag dài sẽ bị ẩn */
    padding: 0 11px !important; /* Tùy chỉnh padding */
  }

  /* Chỉnh khoảng cách giữa các tag bên trong */
  .custom-input.ant-select-multiple .ant-select-selection-item {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }

  /* Căn giữa placeholder */
  .custom-input.ant-select-multiple .ant-select-selection-placeholder {
    display: flex !important;
    align-items: center !important;
    justify-content: flex-start;
  }
}

// Override Ant Design Select styles với theme colors
.ant-select-dropdown {
  background-color: var(--color-neutral-n0);
  border: 1px solid var(--color-neutral-n2);

  .ant-select-item {
    color: var(--color-neutral-n8);
  }

  .ant-select-item-option-selected {
    background-color: var(
      --color-primary-overlay-10
    ); // Sử dụng primary overlay
    color: var(--color-primary);
    font-weight: 600;
  }

  .ant-select-item-option-active:not(.ant-select-item-option-selected) {
    background-color: var(--color-neutral-n1);
  }

  .ant-select-item-option:hover {
    // background-color: var(--color-primary-overlay-5);
  }
}

// Theme-specific adjustments
[data-theme="dark"] {
  .custom-input-container {
    .custom-input {
      &.custom-input-normal {
        .ant-select-arrow {
          color: var(--color-neutral-n5) !important;
        }
      }
    }
  }

  .ant-select-dropdown {
    .ant-select-item-option-active:not(.ant-select-item-option-selected) {
      background-color: var(--color-neutral-n2);
    }
  }
}

// Status-based input variants (tùy chọn thêm)
.custom-input-container {
  &.status-planning .custom-input.custom-input-normal {
    border-color: var(--color-status-planning);

    &:focus {
      border-color: var(--color-status-planning);
      box-shadow: 0 0 0 2px rgba(57, 73, 171, 0.2);
    }
  }

  &.status-in-progress .custom-input.custom-input-normal {
    border-color: var(--color-status-in-progress);

    &:focus {
      border-color: var(--color-status-in-progress);
      box-shadow: 0 0 0 2px rgba(255, 131, 0, 0.2);
    }
  }

  &.status-done .custom-input.custom-input-normal {
    border-color: var(--color-status-done);

    &:focus {
      border-color: var(--color-status-done);
      box-shadow: 0 0 0 2px rgba(67, 160, 71, 0.2);
    }
  }
}

// Global override for Ant Design Form.Item label spacing
.ant-form-item-label {
  margin-bottom: 4px !important;

  > label {
    margin-bottom: 0 !important;
    height: auto !important;
    font-weight: 700 !important; // Make labels bold
    font-size: 13px !important;
    color: var(--color-neutral-n8) !important;
  }
}

// Reduce overall spacing in form items
.ant-form-item {
  margin-bottom: 16px !important;
}

// Specific override for forms with custom components
.ant-form-vertical .ant-form-item-label {
  padding-bottom: 0 !important;
}

// Shared card header style for forms
.form-card {

  &.ant-card.ant-card-bordered {
    border: 1px solid var(--color-neutral-n2);
  }

  &.ant-card {

    .ant-card-body {
      padding: 8px 20px;
    }
  }

  .ant-card-head {
    background-color: var(--color-neutral-n1) !important;
    border-bottom: 1px solid var(--color-neutral-n2) !important;
    padding: 8px 20px;

    .ant-card-head-title {
      color: var(--color-primary) !important;
      font-weight: 700 !important;
      font-size: 16px !important;
      line-height: 21px;
    }
  }
}

.ant-tree-select-dropdown {
  .ant-select-tree {
    .ant-select-tree-treenode:not(.ant-select-tree-treenode-disabled) {
      .ant-select-tree-node-content-wrapper:hover {
        color: var(--color-neutral-n8);
      }

      &.filter-node .ant-select-tree-title {
        color: var(--color-neutral-n8);
        font-weight: 400;
      }
    }

    .ant-select-tree-node-content-wrapper {
      color: var(--color-neutral-n8);

      &:hover {
        color: var(--color-neutral-n8);
        background-color: var(--color-neutral-n1);
      }

      &.ant-select-tree-node-selected {
        background-color: #2e3a59;
        color: #fff !important;
        font-weight: 600;

        span {
          color: #fff !important;
        }
      }
    }
  }
}
