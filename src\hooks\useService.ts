import { serviceApi } from "api/service.api";
import { useMemo, useState } from "react";
import { Service } from "types/service";
import { QueryParam } from "types/query";

export interface ServiceQuery extends QueryParam {
  queryObject?: string;
}

interface UseServiceProps {
  initQuery: ServiceQuery;
}

export const useService = ({ initQuery }: UseServiceProps) => {
  const [data, setData] = useState<Service[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<ServiceQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const isEmptyQuery = useMemo(
    () =>
      Object.keys(query).filter(
        (k) =>
          query[k] != undefined && !["limit", "page", "queryObject"].includes(k)
      ).length == 0,
    [query]
  );

  const fetchData = async (newQuery?: ServiceQuery) => {
    setLoading(true);
    try {
      const { data } = await serviceApi.findAll({ ...query, ...newQuery });

      setData(data.services);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return {
    services: data,
    total,
    fetchData,
    loading,
    setQuery,
    query,
    isEmptyQuery,
  };
};
