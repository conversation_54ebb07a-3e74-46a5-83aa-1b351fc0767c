import React from "react";
import "./ProgressLegend.scss";

interface ProgressLegendProps {
  status?: {
    label: string;
    progress: number;
    color: string;
    value?: string;
  };
  statusText?: string;
  statusColor?: string;
  steps?: { status: string; position: number }[];
}

const ProgressLegend: React.FC<ProgressLegendProps> = ({
  status,
  steps,
  statusText,
  statusColor,
}) => {
  // if (!status) {
  //   return <span className="status-column__undefined">Không xác đ<PERSON>nh</span>;
  // }

  const sortedSteps =
    steps?.slice().sort((a, b) => a.position - b.position) ?? [];

  return (
    <div className="status-column">
      <div
        className="status-column__indicator"
        style={{ backgroundColor: statusColor || status?.color || "#cecece" }}
      />
      <div className="status-column__info">
        <span className="status-column__label">
          {statusText || status?.label || "Tạo mới"}
        </span>
        {/* <div className="status-column__progress">
          {sortedSteps.length > 0
            ? sortedSteps.map((step, idx) => {
                if (status?.value === "WAITING_APPROVE") {
                  // Chỉ tô xanh step đầu tiên (CREATE), còn lại xám
                  return (
                    <div
                      key={idx}
                      className={`status-column__progress-step ${
                        idx === 0 && step.status === "APPROVED"
                          ? "status-column__progress-step--active"
                          : "status-column__progress-step--inactive"
                      }`}
                    />
                  );
                }
                // Các trạng thái khác: tô xanh tất cả step đã APPROVED
                return (
                  <div
                    key={idx}
                    className={`status-column__progress-step ${
                      step.status === "APPROVED"
                        ? "status-column__progress-step--active"
                        : step.status === "REJECTED"
                        ? "status-column__progress-step--rejected"
                        : "status-column__progress-step--inactive"
                    }`}
                    style={
                      step.status === "REJECTED"
                        ? { backgroundColor: "#E53935" }
                        : undefined
                    }
                  />
                );
              })
            : null}
        </div> */}
      </div>
    </div>
  );
};

export default ProgressLegend;
