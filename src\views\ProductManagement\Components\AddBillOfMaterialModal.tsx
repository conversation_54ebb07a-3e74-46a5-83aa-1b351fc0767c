import { Col, Form, Modal, Row } from "antd";
import { materialApi } from "api/material.api";
import { InputNumber } from "components/Input/InputNumber";
import { MaterialSelector } from "components/Selector/MaterialSelector";
import { uniqueId } from "lodash";
import React, { useImperativeHandle, useState } from "react";
import { Material } from "types/material";
import { ModalStatus } from "types/modal";
import { BOM } from "types/product";
import { requiredRule } from "utils/validateRule";

export interface BillOfMaterialCreating {
  id: number;
  quantity: number;
  materialCode: string;
  materialName: string;
  materialId: number;
}
export interface BillOfMaterialUpdate extends Partial<BOM> {
  index: number;
  material: Material;
  quantity: number;
}
export interface BillOfMaterialRef {
  handleCreate: () => void;
  handleUpdate: (billOfMaterial: BillOfMaterialCreating) => void;
}
interface ComponentModalProps {
  onClose: () => void;
  onSubmitOk: (item: BillOfMaterialCreating) => void;
  onUpdate: (
    item: BillOfMaterialCreating,
    prevItem: BillOfMaterialCreating
  ) => void;
}

export const AddBillOfMaterialModal = React.forwardRef<
  BillOfMaterialRef,
  ComponentModalProps
>(({ onClose, onSubmitOk, onUpdate }, ref) => {
  const [form] = Form.useForm<BillOfMaterialCreating>();
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [status, setStatus] = useState<ModalStatus>("create");
  const [previousItem, setPreviousItem] = useState<BillOfMaterialCreating>();
  const [initItem, setInitItem] = useState<Material>();
  useImperativeHandle(
    ref,
    () => ({
      handleCreate() {
        form.resetFields();
        setStatus("create");
        setVisible(true);
        setInitItem(undefined);
      },
      handleUpdate(billOfMaterial) {
        console.log("BOM nhận đc là", billOfMaterial);
        form.setFieldsValue({
          id: billOfMaterial?.id,
          materialCode: billOfMaterial?.materialCode,
          quantity: billOfMaterial.quantity,
          materialId: billOfMaterial.materialId,
        });
        handleGetOneMaterial(billOfMaterial?.materialId).then((item) => {
          setInitItem(item);
        });
        setPreviousItem(billOfMaterial);
        setStatus("update");
        setVisible(true);
      },
    }),
    []
  );
  const handleGetOneMaterial = async (id: number) => {
    console.log("Id nhận đc là", id);
    try {
      if (id) {
        const { data } = await materialApi.findOne(id);
        return data;
      }
    } catch (error) {
      console.log(error);
    }
  };
  const handleOk = async () => {
    try {
      const values = form.getFieldsValue();
      setLoading(true);
      const materialResponse = await materialApi.findOne(values.materialId);
      console.log("Material respons là", materialResponse);
      const payload: BillOfMaterialCreating = {
        // id: materialResponse.data,
        id: materialResponse.data.id ?? uniqueId("material_"),
        materialId: materialResponse.data.id,
        materialCode: materialResponse.data.code,
        materialName: materialResponse.data.name,
        quantity: values.quantity,
      };
      console.log("payload gửi lên cho update", payload);
      if (status === "create") {
        onSubmitOk(payload);
      } else {
        onUpdate(payload, previousItem!);
      }

      // message.success(
      //   status === "create" ? "Thêm mới thành công" : "Cập nhật thành công"
      // );
      handleClose();
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setVisible(false);
    form.resetFields();
    onClose();
  };

  return (
    <Modal
      open={visible}
      onCancel={handleClose}
      onOk={() => form.submit()}
      confirmLoading={loading}
      title={status === "create" ? "Tạo định mức NVL" : "Cập nhật định mức NVL"}
      style={{ top: 20 }}
      destroyOnClose
    >
      <Form layout="vertical" form={form} onFinish={() => handleOk()}>
        <Form.Item hidden name={"id"}></Form.Item>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Nguyên vật liệu"
              name="materialId"
              rules={[requiredRule]}
            >
              <MaterialSelector initOptionItem={initItem} isGetMaterialCode />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Định mức sử dụng"
              name="quantity"
              rules={[requiredRule]}
            >
              <InputNumber />
            </Form.Item>
          </Col>
          {/* <Form.Item hidden name={"id"}></Form.Item> */}
        </Row>
      </Form>
    </Modal>
  );
});
