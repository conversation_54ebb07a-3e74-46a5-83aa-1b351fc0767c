import { useParams, useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";
import { <PERSON><PERSON>, Spin, Alert } from "antd";
import { Project } from "types/project";
import { useProject } from "hooks/useProject";
import dayjs from "dayjs";
import PageTitle from "components/PageTitle/PageTitle";
import MemberShipPage from "views/MemberShip/MemberShipPage";
import InDevelopment from "components/InDevevelopment/InDevelopment";
import CardViewDetail from "components/CardView/CardViewDetail";
import CalendarIcon from "assets/svgs/CalendarIcon";
import ManageByIcon from "assets/svgs/ManageBy";
import { formatDate } from "utils/date";

import "./styles/ProjectInfo.scss";
import { CreateOrUpdateProjectPage } from "./CreateOrUpdateProjectPage";

interface ProjectInfoProps {
  title: string;
  viewContent?: boolean;
}

function ProjectInfo({ title, viewContent }: ProjectInfoProps) {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { fetchDataDetail } = useProject({
    initQuery: { page: 1, limit: 10, search: "" },
  });

  useEffect(() => {
    if (id) loadProjectDetail(parseInt(id));
  }, [id]);

  const loadProjectDetail = async (projectId: number) => {
    setLoading(true);
    setError(null);
    try {
      const projectData = await fetchDataDetail(projectId);
      if (projectData) {
        setProject(projectData);
      } else {
        setError("Không tìm thấy dự án");
      }
    } catch (err: any) {
      setError(err.message || "Có lỗi xảy ra khi tải thông tin dự án");
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => navigate("/project");

  if (loading) {
    return (
      <div className="project-info">
        <div className="loading-container">
          <Spin size="large" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="project-info">
        <div className="error-container">
          <Alert message="Lỗi" description={error} type="error" showIcon />
          <Button onClick={handleBack} className="back-button">
            Quay lại danh sách
          </Button>
        </div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="project-info">
        <div className="not-found">
          <div>
            <h3>Không tìm thấy dự án</h3>
            <Button onClick={handleBack}>Quay lại danh sách</Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="project-info">
      <div className="header-info">
        <div className="info-item">
          <div className="info-label">Ngày bắt đầu</div>
          <div className="info-content">
            <div className="icon-wrapper">
              <CalendarIcon />
            </div>
            <div>{formatDate(project.startAt)}</div>
          </div>
        </div>
        <div className="info-item">
          <div className="info-label">Ngày kết thúc</div>
          <div className="info-content">
            <div className="icon-wrapper">
              <CalendarIcon />
            </div>
            <div>{formatDate(project.endAt)}</div>
          </div>
        </div>

        <div className="info-item">
          <div className="info-label">Chủ đầu tư</div>
          <div className="info-content">
            <div className="icon-wrapper">
              <ManageByIcon />
            </div>
            <div>{project.investor?.name || "Chưa xác định"}</div>
          </div>
        </div>
      </div>

      <div className="details my-4">
        <CardViewDetail key={project.id} project={project} />
      </div>
      <div>
        <CreateOrUpdateProjectPage
          title="Tạo dự án"
          status="update"
          viewContent={true}
          onSubmitSuccess={() => {
            loadProjectDetail(project.id);
          }}
        />
      </div>
    </div>
  );
}

export default ProjectInfo;
