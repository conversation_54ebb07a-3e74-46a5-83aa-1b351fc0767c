import React, { useMemo } from "react";
import { Checkbox, Table } from "antd";
import { uniq } from "lodash";
import { Permission } from "types/permission";

interface PermissionTableProps {
  permissionList: Permission[];
  checkedNames: string[];
  onCheckChange: (checkedNames: string[]) => void;
  loading?: boolean;
}

export const PermissionTable: React.FC<PermissionTableProps> = ({
  permissionList,
  checkedNames,
  onCheckChange,
  loading = false,
}) => {
  const columns = useMemo(() => [
    {
      title: "Tên quyền",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "<PERSON><PERSON> tả",
      dataIndex: "description", 
      key: "description",
    },
    {
      title: "Chọn",
      key: "select",
      align: "center" as const,
      render: (_: any, record: Permission) => (
        <Checkbox
          checked={checkedNames.includes(record.name)}
          onChange={(e) => {
            const checked = e.target.checked;
            if (checked) {
              onCheckChange(uniq([...checkedNames, record.name]));
            } else {
              onCheckChange(checkedNames.filter((name) => name !== record.name));
            }
          }}
        />
      ),
    },
  ], [checkedNames, onCheckChange]);

  return (
    <Table
      columns={columns}
      dataSource={permissionList}
      rowKey="id"
      loading={loading}
      pagination={{ pageSize: 20 }}
      size="small"
    />
  );
}; 