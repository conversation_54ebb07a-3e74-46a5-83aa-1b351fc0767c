import { Card, Space, Modal, Tabs } from "antd";
import CustomButton from "components/Button/CustomButton";
import CustomInput from "components/Input/CustomInput";
import PageTitle from "components/PageTitle/PageTitle";
import { useTheme } from "context/ThemeContext";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { PermissionNames } from "types/PermissionNames";
import {
  Instruction,
  InstructionType,
  InstructionTypeTrans,
} from "types/instruction";
import { handleExport, MyExcelColumn } from "utils/MyExcel";
import { instructionApi } from "api/instruction.api";
import dayjs from "dayjs";
import { checkRoles } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { observer } from "mobx-react";
import IndicativeTable from "./IndicativeTable";
import { getTitle } from "utils";

const exportColumns: MyExcelColumn<Instruction>[] = [
  {
    header: "Mã",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "code",
    columnKey: "code",
    render: (record) => record.code,
  },
  {
    header: "Tiêu đề",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "name",
    columnKey: "name",
    render: (record) => record.name,
  },
  {
    header: "Nội dung chỉ thị",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "content",
    columnKey: "content",
    render: (record) => record.content,
  },

  {
    header: "Dự án",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "project",
    columnKey: "project",
    render: (record) => record.project?.name,
  },
  {
    header: "Hạng mục",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "instructionCategory",
    columnKey: "instructionCategory",
    render: (record) => record.instructionCategory?.name,
  },
  {
    header: "Phân loại",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "type",
    columnKey: "type",
    render: (record: Instruction) => InstructionTypeTrans[record.type]?.label,
  },
  {
    header: "Mô tả chi phí",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "description",
    columnKey: "description",
    render: (record) => record.description,
  },
  {
    header: "Người phát hành",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "createdBy",
    columnKey: "createdBy",
    render: (record: Instruction) =>
      record.createdMemberShipBy?.staff
        ? record.createdMemberShipBy?.staff?.fullName
        : "",
  },
  {
    header: "Người nhận",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "receivedBy",
    columnKey: "receivedBy",
    render: (record: Instruction) =>
      record.receivedMemberShipBy?.staff
        ? record.receivedMemberShipBy?.staff?.fullName
        : "",
  },
  {
    header: "Ngày phát chỉ thị",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "issuedDate",
    columnKey: "issuedDate",
    render: (record) =>
      record.issuedDate ? dayjs(record.issuedDate).format("DD/MM/YYYY") : "",
  },
  {
    header: "Ngày tạo chỉ thị",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "createdDate",
    columnKey: "createdDate",
    render: (record) =>
      record.createdDate ? dayjs(record.createdDate).format("DD/MM/YYYY") : "",
  },
  {
    header: "Link tài liệu",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "link",
    columnKey: "link",
    render: (record) => record.link,
  },
  {
    header: "Địa chỉ",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "address",
    columnKey: "address",
    render: (record) => record.address,
  },
  {
    header: "Nhà thầu phụ",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "provider",
    columnKey: "provider",
    render: (record) => record.provider?.name,
  },
  {
    header: "Hợp đồng nhà thầu phụ",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "contractCode",
    columnKey: "contractCode",
    render: (record) => record.contractCode,
  },
  {
    header: "Trạng thái",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "isActive",
    columnKey: "isActive",
    render: (record: Instruction) =>
      record.isActive ? "Hoạt động" : "Bị khóa",
  },
];

function IndicativePage({ title }: { title: string }) {
  const {
    haveAddPermission,
    haveBlockPermission,
    haveEditPermission,
    haveViewAllPermission,
  } = checkRoles(
    {
      add: PermissionNames.indicativeAdd,
      edit: PermissionNames.indicativeEdit,
      block: PermissionNames.indicativeBlock,
      viewAll: PermissionNames.indicativeViewAll,
    },
    permissionStore.permissions
  );
  const [searchValue, setSearchValue] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [query, setQuery] = useState({
    limit: 10,
    page: 1,
    isAdmin: haveViewAllPermission ? true : undefined,
    search: "",
    status: "",
  });
  const [activeTab, setActiveTab] = useState(InstructionType.Directive);

  useEffect(() => {
    document.title = getTitle(title);
  }, []);

  const { darkMode } = useTheme();
  const navigate = useNavigate();

  // Update search handler
  const handleSearch = () => {
    setQuery((prev) => ({
      ...prev,
      page: 1, // Reset to first page on new search
      search: searchValue,
    }));
  };

  // Update status filter handler
  const handleStatusChange = (value: string) => {
    setStatusFilter(value);
    setQuery((prev) => ({
      ...prev,
      page: 1, // Reset to first page on filter change
      status: value,
    }));
  };

  // Reset filters
  const handleResetFilters = () => {
    setSearchValue("");
    setStatusFilter("");
    setQuery({
      page: 1,
      limit: 10,
      isAdmin: haveViewAllPermission ? true : undefined,
      search: "",
      status: "",
    });
  };

  const handleCreateIndicative = () => {
    navigate(`/report/${PermissionNames.indicativeAdd}`);
  };

  return (
    <div className="app-container">
      <PageTitle
        title="Chỉ thị và Biên bản hiện trường"
        breadcrumbs={["Báo cáo", "Chỉ thị công trường"]}
        extra={
          <Space>
            {haveAddPermission && (
              <CustomButton
                size="small"
                showPlusIcon
                onClick={handleCreateIndicative}
              >
                Tạo chỉ thị
              </CustomButton>
            )}
          </Space>
        }
      />

      <Card>
        <div className="pb-[16px]">
          <div className="flex gap-[16px] items-end pb-[12px] justify-between">
            <div className="flex gap-[16px] items-end">
              <div className="w-[300px]">
                <CustomInput
                  // tooltipContent={"Tìm theo mã, tên nhân viên"}
                  label="Tìm kiếm"
                  placeholder="Tìm kiếm theo mã"
                  value={searchValue}
                  onChange={(value) => setSearchValue(value)}
                  onPressEnter={handleSearch}
                  allowClear
                />
              </div>
              {/* <div>
                <QueryLabel>Trạng thái</QueryLabel>
                <Select
                  allowClear={true}
                  placeholder="Chọn trạng thái"
                  value={statusFilter ?? ""}
                  onChange={handleStatusChange}
                  style={{ minWidth: 150 }}
                  options={[
                    {
                      value: "",
                      label: "Tất cả trạng thái",
                    },
                    ...Object.entries(ProgressInstructionStatus).map(
                      ([key, value]) => ({
                        label: value.label,
                        value: key,
                      })
                    ),
                  ]}
                />
              </div> */}
              <CustomButton onClick={handleSearch}>Áp dụng</CustomButton>

              {(!!searchValue || !!statusFilter) && (
                <CustomButton variant="outline" onClick={handleResetFilters}>
                  Bỏ lọc
                </CustomButton>
              )}
            </div>

            <CustomButton
              onClick={() => {
                Modal.confirm({
                  title: `Bạn có muốn xuất file excel?`,
                  getContainer: () => {
                    return document.getElementById("App") as HTMLElement;
                  },
                  icon: null,

                  footer: (_, { OkBtn, CancelBtn }) => (
                    <>
                      <CustomButton
                        variant="outline"
                        className="cta-button"
                        onClick={() => {
                          const filename =
                            activeTab == InstructionType.Directive
                              ? "Danh sách chỉ thị công trường"
                              : "Danh sách biên bản hiện trường";

                          handleExport({
                            onProgress(percent) {
                              console.log("What is percent", percent);
                            },
                            exportColumns,
                            fileType: "xlsx",
                            dataField: "instructions",
                            query: {
                              ...query,
                              type: activeTab,
                            },
                            api: instructionApi.findAll,
                            fileName: filename,
                            sheetName: filename,
                          });
                          Modal.destroyAll();
                        }}
                      >
                        Có
                      </CustomButton>
                      <CustomButton
                        onClick={() => {
                          Modal.destroyAll();
                        }}
                        className="cta-button"
                      >
                        Không
                      </CustomButton>
                    </>
                  ),
                });
              }}
            >
              Xuất excel
            </CustomButton>
          </div>

          <Tabs
            type="line"
            activeKey={activeTab}
            onChange={(key) => {
              setActiveTab(key as InstructionType);
            }}
          >
            <Tabs.TabPane tab="Chỉ thị" key={InstructionType.Directive}>
              <IndicativeTable type={InstructionType.Directive} query={query} />
            </Tabs.TabPane>

            <Tabs.TabPane
              tab="Biên bản hiện trường"
              key={InstructionType.Report}
            >
              <IndicativeTable type={InstructionType.Report} query={query} />
            </Tabs.TabPane>
          </Tabs>
        </div>
      </Card>
    </div>
  );
}

export default observer(IndicativePage);
