import { Select } from "antd";
import { DefaultOptionType } from "antd/es/select";
import { SelectProps } from "antd/lib";
import { usePattern } from "hooks/usePattern";
import { debounce, uniqBy } from "lodash";
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
} from "react";
import { Pattern } from "types/pattern";
import { QueryParams2 } from "types/query";

type CustomFormItemProps = {
  value?: number;
  initQuery?: QueryParams2;
  disabled?: boolean;
  selectedPattern?: Pattern[];
  multiple?: boolean;
  onChange?: (value: any) => void;
  selectProps?: SelectProps;
  initOptionItem?: Pattern | Pattern[];
  valueIsOption?: boolean;
  allowClear?: boolean;
  placeholder?: string;
};

export interface PatternSelector {
  refresh(): void;
}

/**
 * A demo that changes it's number value on click.
 * @param value initialValue passed by parent Form.Item.
 * @param onChange a callback for Form.Item to read its child component's value.
 * @constructor
 */
export const PatternSelector = forwardRef(
  (
    {
      value,
      onChange,
      initQuery,
      disabled,
      multiple = false,
      selectedPattern,
      initOptionItem,
      valueIsOption,
      selectProps,
      allowClear = true,
      placeholder = "Chọn pattern",
    }: CustomFormItemProps,
    ref
  ) => {
    const { patterns, total, loading, fetchData, query } = usePattern({
      initQuery: {
        page: 1,
        limit: 50,
        ...initQuery,
      },
    });

    useImperativeHandle<any, PatternSelector>(
      ref,
      () => ({
        refresh() {
          fetchData();
        },
      }),
      []
    );

    useEffect(() => {
      fetchData();
    }, [selectedPattern]);

    const debounceSearch = useCallback(
      debounce((keyword) => {
        query.search = keyword;
        fetchData();
      }, 300),
      [query]
    );

    const options = useMemo(() => {
      let data = [...patterns];
      if (initOptionItem) {
        if ((initOptionItem as Pattern[])?.length) {
          data = data.concat(initOptionItem);
        } else {
          data.push(initOptionItem as Pattern);
        }
      }

      return uniqBy(data, (data) => data.id);
    }, [patterns, initOptionItem]);

    return (
      <Select<any, DefaultOptionType>
        mode={multiple ? "multiple" : undefined}
        disabled={disabled}
        loading={loading}
        style={{ width: "100%", minWidth: 200 }}
        allowClear={allowClear}
        onSearch={debounceSearch}
        showSearch
        placeholder={placeholder}
        filterOption={false}
        value={value}
        onChange={(v, opts) => {
          if (v === undefined || v.length == 0) {
            debounceSearch(v);
          }
          if (valueIsOption) {
            if (opts instanceof Array) {
              onChange?.(opts?.map((v) => v.item));
            } else {
              onChange?.(opts?.item);
            }
          } else {
            onChange?.(v);
          }
        }}
        {...selectProps}
      >
        {options?.map((item) => (
          <Select.Option item={item} value={item.id} key={item.id}>
            <div className="flex items-center gap-2">
              <span>{item.name}</span>
            </div>
          </Select.Option>
        ))}
      </Select>
    );
  }
);
