.custom-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  letter-spacing: 0%;
  // border-radius: 0px !important;
  transition: all 0.3s ease;
  gap: 4px;

  // Fix anticon alignment with button text
  .anticon {
    vertical-align: baseline !important;
    line-height: 1 !important;
  }

  .ant-btn {
    box-shadow: none !important;
  }

  .ant-btn-icon {
    // .path {
    //   fill: var(--color-neutral-n0);
    // }
    // height: 16px;
    // width: 16px;
  }

  &.block {
    width: 100%;
  }

  // Size variants
  &.custom-button-small {
    .anticon {
      font-size: 12px;
    }
  }

  &.custom-button-medium {
    // height: 40px;

    .anticon {
      font-size: 14px;
    }
  }

  &.custom-button-large {
    height: 48px;
    font-size: 16px;

    .anticon {
      font-size: 16px;
    }
  }

  // Primary button
  &.custom-button-primary {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
    color: var(--color-neutral-n0);

    &:hover,
    &:focus {
      background-color: var(--color-primary-hover);
      border-color: var(--color-primary-hover);
    }

    &:active {
      background-color: var(--color-primary-active);
      border-color: var(--color-primary-active);
    }

    &[disabled],
    &[disabled]:hover {
      border: none;

      background-color: var(--color-neutral-n2);
      color: var(--color-neutral-n5);
    }
  }

  // Secondary button
  &.custom-button-secondary {
    background-color: var(--color-neutral-n2);
    border-color: var(--color-neutral-n2);
    color: var(--color-primary);

    &:hover,
    &:focus {
      background-color: var(--color-secondary-hover);
      border-color: var(--color-secondary-hover);
    }

    &:active {
      background-color: var(--color-secondary-active);
      border-color: var(--color-secondary-active);
    }

    &[disabled],
    &[disabled]:hover {
      background-color: var(--color-secondary-disabled-bg);
      border-color: var(--color-secondary-disabled-bg);
      color: var(--color-disabled-border);
    }
  }

  // Outline button
  &.custom-button-outline {
    background-color: transparent;
    border-color: var(--color-primary);
    color: var(--color-primary);

    &:hover,
    &:focus {
      background-color: var(--color-primary-overlay-5);
      border-color: var(--color-primary-hover);
      color: var(--color-primary-hover);
    }

    &:active {
      background-color: var(--color-primary-overlay-10);
      border-color: var(--color-primary-active);
      color: var(--color-primary-active);
    }

    &[disabled],
    &[disabled]:hover {
      background-color: transparent;
      border-color: var(--color-disabled-border);
      color: var(--color-disabled-border);
    }
  }

  // Text button
  &.custom-button-text {
    background-color: transparent;
    border-color: transparent;
    color: var(--color-primary);
    box-shadow: none;

    &:hover,
    &:focus {
      background-color: var(--color-primary-overlay-5);
      color: var(--color-primary-hover);
    }

    &:active {
      background-color: var(--color-primary-overlay-10);
      color: var(--color-primary-active);
    }

    &[disabled],
    &[disabled]:hover {
      background-color: transparent;
      color: var(--color-disabled-border);
    }
  }

  // Dark mode styles
  &.dark {
    &.custom-button-primary {
      background-color: var(--color-primary);
      border-color: var(--color-primary);
      color: var(--color-neutral-n8);

      &:hover,
      &:focus {
        background-color: var(--color-primary-hover-dark);
        border-color: var(--color-primary-hover-dark);
      }

      &:active {
        background-color: var(--color-primary-active-dark);
        border-color: var(--color-primary-active-dark);
      }
    }

    &.custom-button-secondary {
      background-color: var(--color-neutral-n2);
      border-color: var(--color-neutral-n2);
      color: var(--color-neutral-n7);

      &:hover,
      &:focus {
        background-color: var(--color-secondary-hover-dark);
        border-color: var(--color-secondary-hover-dark);
      }

      &:active {
        background-color: var(--color-secondary-active-dark);
        border-color: var(--color-secondary-active-dark);
      }
    }

    &.custom-button-outline {
      background-color: transparent;
      border-color: var(--color-primary);
      color: var(--color-primary);

      &:hover,
      &:focus {
        background-color: var(--color-primary-overlay-10-dark);
        border-color: var(--color-primary-hover-outline-dark);
        color: var(--color-primary-hover-outline-dark);
      }

      &:active {
        background-color: var(--color-primary-overlay-20-dark);
        border-color: var(--color-primary-active-outline-dark);
        color: var(--color-primary-active-outline-dark);
      }
    }

    &.custom-button-text {
      background-color: transparent;
      border-color: transparent;
      color: var(--color-primary);

      &:hover,
      &:focus {
        background-color: var(--color-primary-overlay-10-dark);
        color: var(--color-primary-hover-outline-dark);
      }

      &:active {
        background-color: var(--color-primary-overlay-20-dark);
        color: var(--color-primary-active-outline-dark);
      }
    }
  }
}
