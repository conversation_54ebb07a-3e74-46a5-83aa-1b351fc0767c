import { Account } from "./account";
import { Company } from "./company";
import { Dictionary } from "./dictionary";
import { FileAttach } from "./fileAttach";
import { MemberShip } from "./memberShip";
import { Role } from "./role";
import { WorkStatus } from "./workStatus";


export enum StaffType {
  Company = 'COMPANY',// Nhân viên chính của công ty Minh Global
  Customer = 'CUSTOMER',// Khách hàng của công ty
  SubContractor = 'SUBCONTRACTOR', // thầu phụ
  Other = 'OTHER' // Đối tượng
}

export enum CustomerStatus {
  Active = "ACTIVE",
  Inactive = "INACTIVE",
}

export enum CustomerShift {
  Morning = "MORNING",
  Afternoon = "AFTERNOON",
  Night = "NIGHT",
}

export enum Language {
  Vi = "VI",
  En = "EN",
}

export enum Gender {
  Male = "MALE",
  Female = "FEMALE",
}

export enum MaritalStatus {
  Single = "SINGLE",
  Married = "MARRIED",
}

// export const LanguageTrans = {
//   [Language.Vi]: {
//     label: "Tiếng việt",
//     value: Language.Vi,
//   },
//   [Language.EquipmentRental]: {
//     label: "Cho thuê thiết bị",
//     value: Language.EquipmentRental,
//   },

// };

export const GenderTrans = {
  [Gender.Male]: {
    label: "Nam",
    value: Gender.Male,
  },
  [Gender.Female]: {
    label: "Nữ",
    value: Gender.Female,
  },
};

export const MaritalStatusTrans = {
  [MaritalStatus.Single]: {
    label: "Độc thân",
    value: MaritalStatus.Single,
  },
  [MaritalStatus.Married]: {
    label: "Đã kết hôn",
    value: MaritalStatus.Married,
  },
};

export const CustomerStatusTrans = {
  [CustomerStatus.Active]: {
    label: "Hoạt động",
    value: CustomerStatus.Active,
    color: "green",
  },
  [CustomerStatus.Inactive]: {
    label: "Bị khóa",
    value: CustomerStatus.Inactive,
    color: "red",
  },
};

export const CustomerShiftTrans = {
  [CustomerShift.Morning]: {
    label: "Ca sáng",
    value: CustomerShift.Morning,
  },
  [CustomerShift.Afternoon]: {
    label: "Ca chiều",
    value: CustomerShift.Afternoon,
  },
  [CustomerShift.Night]: {
    label: "Ca tối",
    value: CustomerShift.Night,
  },
};

export interface Staff {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  code: string; // max nhân viên
  status: WorkStatus; // trạng thái làm việc
  isBlocked: boolean;
  phone: string;
  avatar: string;
  dob: string; // YYYY-MM-DD
  loginFailCount: number;
  fullName: string;
  // companyType: CompanyType // Loại nhân viên;
  username: string;
  email: string;
  password: string;
  deviceId: string;
  is2FA: boolean;
  secret2FA: string;
  language: Language;
  startAt: number; // thời gian bắt đầu làm việc
  endAt: number; // thời gian kết thúc làm việc
  shift: CustomerShift; // ca làm việc
  payRate: number; // lương
  identificationCard: string; // CCCD
  identificationDate: string; // ngay cap
  identificationPlace: string; // noi cấp
  allowances: number; // trợ cấp
  personalTax: string; // thuế cá nhân
  bankAccount: string; // tài khoản ngân hàng
  bankName: string // tài khoản ngân hàng 
  educationLevel: string; // mức độ học vấn
  professionalQualification: string; // trình độ chuyên môn
  professionalSkills: string; // kĩ năng nghề nghiệp
  contactPerson: string; // hợp đồng lao động
  files: string;
  gender: Gender; // Gender
  maritalStatus: MaritalStatus; // Marital status
  contractType: string; // Hợp đồng lao động
  officialStartDate?: string; // Ngày chính thức (YYYY-MM-DD)
  workStatus: WorkStatus; // Tình trạng làm việc
  timekeepingCode: string; // Mã chấm công
  workAddress: string; // Địa chỉ làm việc
  baseSalary: number; // Mức lương cơ bản
  notes?: string; // Ghi chú (nếu có)
  permanentAddress: string; // Địa chỉ thường trú
  temporaryAddress: string; // Địa chỉ tạm trú
  role: Role;
  company: Company;
  department: Dictionary | null;
  level: Dictionary | null;
  fileAttaches: FileAttach[]; // file liên qua
  roleName: string;
  staffCode: string;
  // instructions: Instruction[]
  // provider: Provider // Là thầu phụ thì liên kết
  // projects: Project[]
  // approveRfis: RFI[]
  // followRfis: RFI[]
  // distributionRfis: RFI[]
  // followSubmittals: Submittal[]
  // approveSubmittals: Submittal[]
  // followPlans: Plan[]
  // approvePlans: Plan[]
  // tasks: Task[]
  jobTitle: Dictionary | null;
  // auditTemplates: AuditTemplate[]
  // audits: Audit[]
  // info create update
  createdBy: Staff;
  updatedBy: Staff;
  account: Account;
  memberShip?: MemberShip;

  //extra
  isDev: boolean;
  emergencyContactPhone: string // Số điện thoại 
  emergencyContactRelationship: string

}
