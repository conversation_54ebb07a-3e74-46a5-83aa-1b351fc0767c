import { Avatar, Dropdown, <PERSON>u, Space, Switch } from "antd";
import { Header } from "antd/lib/layout/layout";
import { observer } from "mobx-react";
import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { adminRoutes } from "router";
import { settings } from "settings";
import { userStore } from "store/userStore";
import { $url } from "utils/url";
import { useTheme } from "context/ThemeContext";
import clsx from "clsx";
import darkSwitch from "assets/svgs/darkSwitch.svg";
import lightSwitch from "assets/svgs/lightSwitch.svg";
import Language from "components/Language/Language";
import Notification from "components/Notification/Notification";
import { IoMdArrowDropdown } from "react-icons/io";
import { appStore } from "store/appStore";
import { HEADER_HEIGHT, SIDEBAR_WIDTH } from "utils/theme";
import { ReactComponent as ManageByIcon } from "assets/svgs/manageBy.svg";
import { ReactComponent as LogoutIcon } from "assets/svgs/logout.svg";
import SwitchProjectButton from "components/SwitchProjectButton/SwitchProjectButton";
import { Route } from "router/RouteType";
import { showUIBasedOnDeviceWidth } from "utils/devide";

export const Navbar = observer(
  ({ collapsed, toggle, oneSignalLoading, isSubscribed, handleChangePushNotification }:
    {
      collapsed: boolean;
      toggle: () => void, oneSignalLoading?: boolean,
      isSubscribed?: boolean,
      handleChangePushNotification: (checked: boolean) => void;
    }) => {
    const location = useLocation();
    const navigate = useNavigate();

    const [breadcrumbs, setBreadcrumbs] = useState<string[]>([]);
    const { darkMode, toggleDarkMode } = useTheme();

    const handleSetBreadcrumbs = (data: Route) => {
      if (data) {
        if (data.breadcrumb) {
          setBreadcrumbs(data.breadcrumb.split("/"));
        } else if (data.title) {
          setBreadcrumbs([data.title]);
        }
      }
    };

    useEffect(() => {
      adminRoutes.forEach((router) => {
        // console.log("router", router);
        // if (router.name == "/product") {
        //   debugger;
        // }
        if (router.path == location.pathname) {
          return handleSetBreadcrumbs(router);
        } else if (router.children?.length) {
          const findChild = router.children?.find((child) => {
            // console.log("childPath", (router.path || "") + child.path);

            return (router.path || "") + "/" + child.path == location.pathname;
          });
          if (findChild) {
            // console.log("findChild", findChild);

            return handleSetBreadcrumbs(findChild);
          }
        }
      });
    }, [location.pathname]);

    const menu = (
      <Menu style={{ borderRadius: "5px" }}>
        <Menu.Item key={"profile"}>
          <div
            className="flex items-center gap-2"
            onClick={() => {
              navigate("/profile");
            }}
          >
            <ManageByIcon />
            <span className="text-[var(--color-neutral-n7)]">Tài khoản</span>
          </div>
        </Menu.Item>

        <Menu.Item key={"login"}>
          <div
            className="flex items-center gap-2"
            onClick={() => {
              userStore.logout();
              appStore.clearCurrentProject();
              navigate("/login");
            }}
          >
            <LogoutIcon className="size-[16px]" />
            <span
              // to={"/login"}
              // onClick={() => {
              //   userStore.logout();
              //   appStore.clearCurrentProject();
              // }}
              className="text-[var(--color-neutral-n7)]"
            >
              Đăng xuất
            </span>
          </div>
        </Menu.Item>
      </Menu>
    );

    return (
      <Header
        className={`site-layout-background ${collapsed ? "collapsed" : ""}`}
        style={{ padding: 0 }}
      >
        <div
          className="relative p-2 flex items-center justify-center gap-2"
          style={{
            backgroundColor: "var(--color-primary)",
            width: SIDEBAR_WIDTH - 1,
            height: HEADER_HEIGHT,
          }}
        >
          <img
            src={darkMode ? settings.logoWhite : settings.logoWhite}
            alt=""
            className="inline-block h-[52px] w-full object-contain cursor-pointer"
            onClick={() => {
              appStore.clearCurrentProject();
              navigate("/");
            }}
          />
          <Switch
            size="small"
            checked={!collapsed}
            onClick={toggle}
            className="switch-left-menu absolute right-3 bottom-3"
          />
        </div>
        <div className="flex-1 flex justify-between px-[24px] shadow-md">
          {appStore.currentProject && (
            <div className="flex items-center gap-2">
              <div
                className="text-[16px] font-bold cursor-pointer"
                onClick={() => {
                  navigate(`/project-detail/${appStore.currentProject!.id}`);
                }}
              >
                {appStore.currentProject.name}
              </div>
              <SwitchProjectButton />
              {/* <ProjectStatusTag
                status={appStore.currentProject.status}
                variant="select"
              /> */}
            </div>
          )}
          <Space
            className="gap-2 md:gap-4"
            style={{ flex: 1, justifyContent: "flex-end" }}
          >
            <label
              style={{
                paddingLeft: "10px",
                color: "#4d74fc",
              }}
              htmlFor=""
            >
              v{settings.version}
            </label>

            {settings.mode === "development" && (
              <div className="relative hidden md:block">
                <img
                  src={darkSwitch}
                  className={clsx(
                    "h-[44px] object-contain cursor-pointer",
                    darkMode ? "" : "hidden"
                  )}
                  onClick={toggleDarkMode}
                />
                <img
                  src={lightSwitch}
                  className={clsx(
                    "h-[44px] object-contain cursor-pointer",
                    darkMode ? "hidden" : ""
                  )}
                  onClick={toggleDarkMode}
                />
              </div>
            )}

            {/* <Switch
            checked={darkMode}
            onChange={toggleDarkMode}
            checkedChildren={<MoonOutlined />}
            unCheckedChildren={<SunOutlined />}
            size="default"
            style={{
              backgroundColor: darkMode ? "#1890ff" : "#fa8c16",
            }}
          /> */}
            <Language />

            {showUIBasedOnDeviceWidth(500) && (
              <span>
                <Switch
                  loading={oneSignalLoading}
                  checked={isSubscribed}
                  onChange={handleChangePushNotification}
                ></Switch>
                <span
                  style={{
                    fontWeight: "600",
                    marginLeft: 10,
                  }}
                >
                  Bật thông báo đẩy
                </span>
              </span>
            )}

            <Notification />

            <Dropdown
              trigger={["click"]}
              overlay={menu}
              arrow
              placement="bottomRight"
            >
              <div className="nav-bar-avatar cursor-pointer flex items-center gap-2">
                <Avatar
                  src={$url(userStore.info.avatar)}
                  style={{
                    color: "#f56a00",
                    backgroundColor: "#fde3cf",
                    height: "44px",
                    width: "44px",
                  }}
                >
                  {userStore.info.fullName?.[0]}
                </Avatar>
                <div className="h-[44px] flex-col justify-center gap-2 hidden md:flex">
                  <div className="-full-name leading-3 font-bold">
                    {userStore.info.fullName}
                  </div>
                  <div className="-role leading-3">
                    {userStore.info.role?.name}
                  </div>
                </div>
                <IoMdArrowDropdown className="-icon" size={16} />
              </div>
            </Dropdown>
            {/* {userStore.info.avatar ? (
            <Image
              width={40}
              src={$url(userStore.info.avatar)}
              style={{ borderRadius: "50%" }}
              fallback={require("assets/images/user.png")}
            />
          ) : (
            <img
              width={40}
              alt=""
              style={{ borderRadius: "50%" }}
              src={require("assets/images/user.png")}
            />
          )} */}
          </Space>
        </div>
      </Header>
    );
  }
);
