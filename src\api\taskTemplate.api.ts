import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const taskTemplateApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/taskTemplate",
      params,
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/taskTemplate/${id}`,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/taskTemplate",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/taskTemplate/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/taskTemplate/${id}`,
      method: "delete",
    }),
};
