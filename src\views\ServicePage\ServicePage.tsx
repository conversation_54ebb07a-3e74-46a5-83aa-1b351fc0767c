import {
  Button,
  Card,
  Image,
  message,
  Modal,
  Select,
  Space,
  Table,
  TablePaginationConfig,
  Tooltip,
} from "antd";
import { Pagination } from "components/Pagination";
import { useEffect, useState, useRef, useMemo, useCallback } from "react";
import { DateTypeTrans, Service, ServiceTypeTrans } from "types/service";
import { formatVND, getTitle } from "utils";
import { $url } from "utils/url";
import { ServiceModal, ServiceModalRef } from "./components/Modal/ServiceModal";
import { useService } from "hooks/useService";
import { handleExport, MyExcelColumn } from "../../utils/MyExcel";
import { serviceApi } from "api/service.api";
import { Link, useNavigate } from "react-router-dom";
import {
  checkRole,
  checkRoles,
  filterActionColumnIfNoPermission,
} from "utils/auth";
import { PermissionNames } from "types/PermissionNames";
import { permissionStore } from "store/permissionStore";
import PageTitle from "components/PageTitle/PageTitle";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import PencilIcon from "assets/svgs/PencilIcon";
import CustomButton from "components/Button/CustomButton";
import DeleteIcon from "assets/svgs/DeleteIcon";
import CustomInput from "components/Input/CustomInput";
import { ReactComponent as FileIconSvg } from "assets/svgs/file.svg"; // Example SVG icon for default file type
import { FileAttach } from "types/fileAttach";
import { ProviderSelector } from "components/Selector/ProviderSelector";
import ActiveStatusTag from "components/ActiveStatus/ActiveStatusTag";
import { DictionarySelector } from "components/Selector/DictionarySelector";
import { DictionaryType } from "types/dictionary";
import { FilterValue, SorterResult } from "antd/es/table/interface";
import { TableProps } from "antd/lib";
import {
  LockOutlined,
  UnlockOutlined,
  ImportOutlined,
} from "@ant-design/icons";
import { useTransition } from "hooks/useTransition";
import ImportService, {
  ImportServiceModal,
} from "components/ImportDocument/ImportService";
import { removeSubstringFromKeys } from "utils/common";
import logoImage from "assets/images/logo.png";
import { providerApi } from "api/provider.api";
import { getListByKey } from "utils/data";
import { exportTemplateWithValidation } from "utils/TemplateExcel";
import {
  getListNameByApi,
  getListNameByTypeDictionary,
} from "hooks/useDictionary";
import { QueryParam } from "types/query";
import { unitApi } from "api/unit.api";
import QueryLabel from "components/QueryLabel/QueryLabel";
import { BMDImage } from "components/Image/BMDImage";
import { observer } from "mobx-react";
import LockButton from "components/Button/LockButton";
import "./style/ServicePage.scss";
import EditButton from "components/Button/EditButton";

const { ColumnGroup, Column } = Table;

export const ServicePage = observer(({ title = "" }) => {
  const {
    haveAddPermission,
    haveBlockPermission,
    haveEditPermission,
    haveViewAllPermission,
  } = checkRoles(
    {
      add: PermissionNames.serviceAdd,
      edit: PermissionNames.serviceEdit,
      block: PermissionNames.serviceBlock,
      viewAll: PermissionNames.serviceViewAll,
    },
    permissionStore.permissions
  );

  const modalRef = useRef<ServiceModalRef>(null);
  const importModal = useRef<ImportServiceModal>();
  const [loadingExport, setLoadingExport] = useState(false);
  const [loadingDelete, setLoadingDelete] = useState(false);
  const [loadingDownloadDemo, setLoadingDownloadDemo] = useState(false);
  const { isLoaded } = useTransition();

  const exportColumns: MyExcelColumn<Service>[] = [
    {
      header: "Mã DV",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "code",
      columnKey: "code",
    },
    {
      header: "Tên dịch vụ",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "name",
      columnKey: "name",
    },
    {
      header: "Loại dịch vụ",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "type",
      columnKey: "type",
      render: (record: Service) => record.serviceType?.name,
    },
    {
      header: "Chi phí ước tính",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "estPrice",
      columnKey: "estPrice",
      style: { numFmt: "###,##" },

      render: (record: Service) => record.estPrice,
    },
    {
      header: "Thời gian thực hiện",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "startAt",
      columnKey: "startAt",
      render: (record: Service) => record.workingDays,
    },
    {
      header: "NCC",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "provider",
      columnKey: "provider",
      render: (record: Service) => record.provider?.name,
    },
    {
      header: "Đơn vị tính",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "unit",
      columnKey: "unit",
      render: (record: Service) => record.unit?.name,
    },
    {
      header: "Mô tả",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "description",
      columnKey: "description",
      render: (record: Service) => record.description,
    },
    {
      header: "Trạng thái",
      headingStyle: {
        font: {
          bold: true,
        },
      },
      key: "isActive",
      columnKey: "isActive",
      render: (record: Service) => (record.isActive ? "Hoạt động" : "Bị khóa"),
    },
  ];
  const navigate = useNavigate();
  const { fetchData, loading, query, services, setQuery, total, isEmptyQuery } =
    useService({
      initQuery: {
        limit: 20,
        page: 1,
        isAdmin: haveViewAllPermission ? true : undefined,
      },
    });

  useEffect(() => {
    document.title = getTitle(title);
  }, []);

  useEffect(() => {
    if (isLoaded) {
      fetchData();
    }
  }, [isLoaded]);

  const handleDeleteService = async (id: number) => {
    try {
      setLoadingDelete(false);
      await serviceApi.delete(id);
      message.success("Xóa thành công");
      fetchData();
    } catch (error) {
    } finally {
      setLoadingDelete(true);
    }
  };

  const handleActiveService = async (id: number, value: boolean) => {
    try {
      setLoadingDelete(false);
      await serviceApi.update(id, { service: { isActive: !value } });
      message.success(value ? "Khóa thành công" : "Mở khóa thành công");
      fetchData();
    } catch (error) {
    } finally {
      setLoadingDelete(true);
    }
  };

  const handleTableChange: TableProps<any>["onChange"] = (
    pagination,
    filters,
    sorter
  ) => {
    if (!Array.isArray(sorter)) {
      const fieldMap: Record<string, string> = {
        name: "service.name",
        serviceType: "serviceType.name",
        estPrice: "service.estPrice",
        workingDays: "service.workingDays",
        provider: "provider.name",
        code: "service.code",
      };
      const columnKey = sorter.field || sorter.column?.key;

      if (!sorter.order) {
        // setSortField(null);
        // setSortOrder(null);
        query.queryObject = undefined;
        setQuery({ ...query });
      } else {
        const order = sorter.order === "ascend" ? "ASC" : "DESC";
        // setSortField("jobCategory.name");
        // setSortOrder(order);
        const field = fieldMap[columnKey as string];

        const newQueryObject = JSON.stringify([
          {
            type: "sort",
            field,
            value: order,
          },
        ]);
        query.queryObject = newQueryObject;
        setQuery({ ...query });
      }
      fetchData();
    } else {
      query.queryObject = undefined;
      setQuery({ ...query });
      fetchData();
    }
  };

  const handleRowClick = (record: Service) => {
    navigate(
      `/master-data/${PermissionNames.serviceEdit.replace(
        ":id",
        record!.id + ""
      )}`
    );
  };

  const columns: CustomizableColumn<Service>[] = [
    {
      title: "Mã",
      dataIndex: "code",
      key: "code",
      width: 100,
      sorter: true,

      align: "center",
      render: (_, record) => {
        return (
          <div
            className="text-[#1677ff] cursor-pointer"
            onClick={() => handleRowClick(record)}
          >
            {record.code}
          </div>
        );

        // return (
        //   <Link
        //     to={`/master-data/${PermissionNames.serviceEdit.replace(
        //       ":id",
        //       record.id + ""
        //     )}`}
        //   >
        //     {record.code}
        //   </Link>
        // );
      },
    },
    {
      title: "Dịch vụ",
      dataIndex: "name",
      key: "name",
      sorter: true,
      width: 371,
      render: (_, record) => (
        <div className="flex items-center gap-2">
          <BMDImage
            src={
              record.avatar && record.avatar.trim()
                ? $url(record.avatar)
                : logoImage
            }
            className="size-[34px] object-cover"
            width={34}
            height={34}
            fallback={logoImage}
          />
          <label htmlFor="" className="text-bold">
            {record.name}
          </label>
        </div>
      ),
    },
    {
      title: "Loại",
      dataIndex: "serviceType",
      key: "serviceType",
      sorter: true,

      width: 130,
      render: (_, record) => (
        <div className="flex items-center gap-[6px]">
          <span>{record.serviceType?.name}</span>
        </div>
      ),
    },
    {
      title: "Chi phí",
      dataIndex: "estPrice",
      key: "estPrice",
      sorter: true,
      align: "right",
      width: 120,
      render: (_, record) => {
        if (record.estPrice === null || record.estPrice === undefined) {
          return <div></div>;
        }
        return <div>{formatVND(record.estPrice)} VNĐ</div>;
      },
    },
    {
      title: "Thời gian thực hiện",
      dataIndex: "workingDays",
      key: "workingDays",
      sorter: false,
      align: "right",
      width: 160,
      render: (_, record) => <div>{record.workingDays}</div>,
    },
    {
      title: "NCC",
      dataIndex: "provider",
      key: "provider",
      sorter: true,
      align: "left",
      width: 160,
      render: (_, record) => <div>{record.provider?.name}</div>,
    },
    // {
    //   title: "Tệp đính kèm",
    //   dataIndex: "unit",
    //   key: "unit",
    //   align: "center",
    //   width: 150,
    //   render: (_, record) => {
    //     const files = JSON.parse(record.files) as FileAttach[];

    //     return (
    //       <div className="flex gap-1 items-center justify-center">
    //         <FileIconSvg className="size-[18px] service-file-icon" />
    //         <span>{files?.length || 0} tệp</span>
    //       </div>
    //     );
    //   },
    // },
    {
      title: "Trạng thái",
      dataIndex: "isActive",
      key: "isActive",
      align: "center",
      width: 150,
      render: (isActive, record) => <ActiveStatusTag isActive={isActive} />,
    },
    {
      title: "Xử lý",
      key: "actions",
      fixed: "right",
      width: 100,
      align: "center",
      alwaysVisible: true,
      render: (_, record) => (
        <Space>
          {haveEditPermission && (
            <EditButton
              onClick={(e) => {
                e.stopPropagation();
                navigate(
                  `/master-data/${PermissionNames.serviceEdit.replace(
                    ":id",
                    record!.id + ""
                  )}?update=1`
                );
              }}
            />
          )}
          {haveBlockPermission && (
            <LockButton
              isActive={record.isActive}
              onAccept={() => handleActiveService(record.id, record.isActive)}
              modalTitle={`${record.isActive ? "Khóa" : "Mở khóa"} dịch vụ: ${
                record.name
              }`}
              modalContent={
                <>
                  <div>
                    Khi {record.isActive ? "khóa" : "mở khóa"} dịch vụ này,
                    trạng thái của dịch vụ sẽ được thay đổi.
                    <br />
                    Bạn có chắc chắn muốn {record.isActive
                      ? "khóa"
                      : "mở khóa"}{" "}
                    dịch vụ này?
                  </div>
                </>
              }
            />
          )}
        </Space>
      ),
    },
  ];

  const handleOnUploadedFile = async (excelData: any, setData: any) => {
    const { results } = excelData;

    console.log("results", results);

    const importData = results?.map((item: any) => {
      const refineRow = removeSubstringFromKeys(item, " *");

      const code = refineRow["Mã DV"];
      const name = refineRow["Tên dịch vụ"];
      const serviceTypeName = refineRow["Loại dịch vụ"];
      const estPrice = refineRow["Chi phí ước tính"];
      const workingDays = refineRow["Thời gian thực hiện"];
      const providerName = refineRow["NCC"];
      const unitName = refineRow["Đơn vị tính"];
      const description = refineRow["Mô tả"];
      const statusName = refineRow["Trạng thái"];
      let isActive = undefined;
      if (statusName) {
        isActive = statusName == "Hoạt động" ? true : false;
      }

      return {
        name,
        code,
        serviceTypeName,
        estPrice,
        workingDays,
        providerName,
        unitName,
        description,
        isActive,
        rowNum: item.__rowNum__,
      };
    });
    console.log("importData", importData);

    setData(importData);
  };

  const handleDownloadDemoExcel = async () => {
    try {
      setLoadingDownloadDemo(true);
      const [unitNames, providerNames, serviceTypeNames] = await Promise.all([
        getListNameByApi({ api: unitApi.findAll, dataKey: "units" }), // lay ds don vi
        getListNameByApi({ api: providerApi.findAll, dataKey: "providers" }), // lay ds ncc
        getListNameByTypeDictionary(DictionaryType.ServiceType), // lay ds loai dv
      ]);

      const result = await exportTemplateWithValidation({
        templatePath: "/exportFile/file_mau_nhap_service.xlsx",
        outputFileName: "file_mau_nhap_service.xlsx",
        sheetsToAdd: [
          { name: "NCC", data: providerNames },
          { name: "ServiceType", data: serviceTypeNames },
          { name: "Unit", data: unitNames },
        ],
        validations: [
          {
            column: "F",
            type: "list",
            formulae: [`'NCC'!$A$1:$A$${providerNames.length}`],
          },
          {
            column: "C",
            type: "list",
            formulae: [`'ServiceType'!$A$1:$A$${serviceTypeNames.length}`],
          },
          {
            column: "G",
            type: "list",
            formulae: [`'Unit'!$A$1:$A$${unitNames.length}`],
          },
          {
            column: "I",
            type: "list",
            formulae: [`'Status'!$A$1:$A$2`],
          },
        ],
      });
    } catch (error) {
      message.error(
        `Có lỗi xảy ra: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    } finally {
      setLoadingDownloadDemo(false);
    }
  };

  return (
    <div>
      <PageTitle
        title={title}
        breadcrumbs={["Dữ liệu nguồn", title]}
        extra={
          <Space>
            {haveAddPermission && (
              <>
                <CustomButton
                  size="small"
                  showPlusIcon
                  onClick={() => {
                    navigate(`/master-data/${PermissionNames.serviceAdd}`);
                  }}
                >
                  Tạo dịch vụ
                </CustomButton>
                <CustomButton
                  size="small"
                  icon={<ImportOutlined />}
                  onClick={() => {
                    importModal.current?.open();
                  }}
                >
                  Nhập excel
                </CustomButton>
              </>
            )}
          </Space>
        }
      />

      <div className="app-container">
        <Card>
          <div className="flex gap-[16px] items-end pb-[12px] justify-between flex-wrap">
            <div className="flex gap-[16px] items-end flex-wrap">
              <div className="w-[300px]">
                <CustomInput
                  tooltipContent={"Tìm theo mã, tên dịch vụ"}
                  label="Tìm kiếm"
                  placeholder="Tìm kiếm"
                  onPressEnter={() => {
                    console.log("onPressEnter:");
                    query.page = 1;
                    setQuery({ ...query });
                    fetchData();
                  }}
                  value={query.search}
                  onChange={(value) => {
                    console.log("change search value:", value);
                    query.search = value;
                    setQuery({ ...query });

                    if (!value) {
                      fetchData();
                    }
                  }}
                  allowClear
                />
              </div>
              <div className="filter-col">
                <QueryLabel>Loại</QueryLabel>
                <DictionarySelector
                  tooltipContent={
                    query.serviceTypeId &&
                    query.serviceTypeIdName &&
                    query.serviceTypeId !== ""
                      ? query.serviceTypeIdName
                      : undefined
                  }
                  initQuery={{ type: DictionaryType.ServiceType }}
                  allowClear={true}
                  addonOptions={[
                    {
                      id: "",
                      name: "Tất cả các loại",
                    },
                  ]}
                  valueIsOption={true}
                  value={query.serviceTypeId ?? ""}
                  onChange={(value) => {
                    query.serviceTypeId = value?.id || "";
                    query.serviceTypeIdName = value?.name;
                    setQuery({ ...query });
                  }}
                />
              </div>
              <div className="filter-col">
                <QueryLabel>Nhà cung cấp</QueryLabel>
                <ProviderSelector
                  tooltipContent={
                    query.providerId &&
                    query.providerIdName &&
                    query.providerId !== ""
                      ? query.providerIdName
                      : undefined
                  }
                  allowClear={true}
                  addonOptions={[
                    {
                      id: "",
                      name: "Tất cả nhà cung cấp",
                    },
                  ]}
                  value={query.providerId ?? ""}
                  valueIsOption={true}
                  onChange={(value) => {
                    query.providerId = value?.id || "";
                    query.providerIdName = value?.name;
                    setQuery({ ...query });
                  }}
                  inputStyle={{
                    minWidth: 200,
                  }}
                />
              </div>
              <div>
                <QueryLabel>Trạng thái</QueryLabel>
                <Select
                  value={query.isActive}
                  options={[
                    { label: "Hoạt động", value: true },
                    {
                      label: "Bị khóa",
                      value: false,
                    },
                  ]}
                  placeholder="Trạng thái"
                  allowClear
                  onChange={(value) => {
                    query.isActive = value;
                    setQuery({ ...query });
                  }}
                />
              </div>
              <CustomButton
                onClick={() => {
                  query.page = 1;
                  // setQuery({ ...query });
                  fetchData({ ...query });
                }}
              >
                Áp dụng
              </CustomButton>

              {!isEmptyQuery && (
                <CustomButton
                  variant="outline"
                  onClick={() => {
                    delete query.providerId;
                    delete query.providerIdName;
                    delete query.serviceTypeId;
                    delete query.serviceTypeIdName;
                    delete query.search;
                    delete query.isActive;
                    query.page = 1;
                    setQuery({ ...query });
                    fetchData({ ...query });
                  }}
                >
                  Bỏ lọc
                </CustomButton>
              )}
            </div>

            <CustomButton
              onClick={() => {
                Modal.confirm({
                  title: `Bạn có muốn xuất file excel?`,
                  getContainer: () => {
                    return document.getElementById("App") as HTMLElement;
                  },
                  icon: null,

                  footer: (_, { OkBtn, CancelBtn }) => (
                    <>
                      <CustomButton
                        variant="outline"
                        className="cta-button"
                        onClick={() => {
                          handleExport({
                            onProgress(percent) {
                              console.log("What is percent", percent);
                            },
                            exportColumns,
                            fileType: "xlsx",
                            dataField: "services",
                            query: query,
                            api: serviceApi.findAll,
                            fileName: "Danh sách dịch vụ",
                            sheetName: "Danh sách dịch vụ",
                          });
                          Modal.destroyAll();
                        }}
                      >
                        Có
                      </CustomButton>
                      <CustomButton
                        onClick={() => {
                          Modal.destroyAll();
                        }}
                        className="cta-button"
                      >
                        Không
                      </CustomButton>
                    </>
                  ),
                });
              }}
            >
              Xuất excel
            </CustomButton>
          </div>
          <CustomizableTable
            columns={filterActionColumnIfNoPermission(columns, [
              haveEditPermission,
              haveBlockPermission,
            ])}
            dataSource={services}
            rowKey="id"
            loading={loading}
            pagination={false}
            scroll={{ x: 1200 }}
            bordered
            displayOptions
            tableId="service-page"
            //@ts-ignore
            onChange={handleTableChange}
            onRowClick={handleRowClick}
          />

          <Pagination
            currentPage={query.page}
            defaultPageSize={query.limit}
            total={total}
            onChange={({ limit, page }) => {
              query.page = page;
              query.limit = limit;
              setQuery({ ...query });
              fetchData();
            }}
          />
          <ServiceModal
            onSubmitOk={fetchData}
            onClose={() => {}}
            ref={modalRef}
          />
          {useMemo(
            () => (
              <ImportService
                guide={[
                  "Vui lòng tải và sử dụng file mẫu để import dữ liệu đúng cách",
                  "Không được thay đổi tiêu đề trong file csv/excel mẫu để tránh import thiếu dữ liệu",
                  "Chi tiết xem tại sheet Hướng dẫn của file import mẫu",
                ]}
                onSuccess={() => {
                  query.page = 1;
                  fetchData();
                }}
                ref={importModal}
                createApi={serviceApi.create}
                onUploaded={(excelData, setData) => {
                  console.log("up gì lên vậy", excelData);
                  handleOnUploadedFile(excelData, setData);
                }}
                okText={`Nhập dịch vụ ngay`}
                // demoExcel="/exportFile/file_mau_nhap_service.xlsx"
                onDownloadDemoExcel={handleDownloadDemoExcel}
                loadingDownloadDemo={loadingDownloadDemo}
              />
            ),
            [loadingDownloadDemo]
          )}
        </Card>
      </div>
    </div>
  );
});
