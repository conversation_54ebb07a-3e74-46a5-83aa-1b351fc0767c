import {
  ExportOutlined,
  ImportOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import {
  Button,
  Input,
  message,
  Popconfirm,
  Space,
  Spin,
  Table,
  Tag,
} from "antd";
import Column from "antd/es/table/Column";
import { Pagination } from "components/Pagination";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { Component } from "types/component";
import { $url } from "utils/url";
import { componentApi } from "api/component.api";
import { handleExport, MyExcelColumn } from "utils/MyExcel";
import ImportSettingComponent, {
  ImportSettingComponentModal,
} from "components/ImportDocument/ImportSettingComponent";
import { removeSubstringFromKeys } from "utils/common";
import { Variant } from "types/variant";
import { Material } from "types/material";
import { variantApi } from "api/variant.api";
import ImportVariantConfiguration from "components/ImportDocument/ImportVariantConfiguration";
import { VariantModal } from "views/VariantConfiguration/Components/CreateVariantModal";
import { CreateComponentModal } from "views/SettingComponent/Components/CreateComponentModal";
import { useUnit } from "hooks/useUnit";
import { Unit } from "types/unit";
import { Staff } from "types/staff";
import { CreateUnitModal } from "./Components/CreateStretchDirectionModal";
import { unitApi } from "api/unit.api";
import { useStretchDirection } from "hooks/useStretchDirection";
import { StretchDirection } from "types/stretchDirection";
import { stretchDirectionApi } from "api/stretchDirection.api";
import { IoImageOutline } from "react-icons/io5";
import defaultImg from "../../assets/images/No-Image.png";
import { debounce } from "lodash";
import { getTitle } from "utils";
const exportColumns: MyExcelColumn<Component>[] = [
  {
    header: "Icon",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "icon",
    columnKey: "icon",
    render: (record) => $url(record.icon),
  },
  {
    header: "Tên",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "name",
    columnKey: "name",
  },
  // {
  //   header: "Nhóm thành phần",
  //   headingStyle: {
  //     font: {
  //       bold: true,
  //     },
  //   },
  //   key: "component",
  //   columnKey: "component",
  //   render: (record) => record.code,
  // },
];
export const StretchDirectionPage = ({ title = "" }) => {
  const [loadingDelete, setLoadingDelete] = useState(false);
  useEffect(() => {
    document.title = getTitle(title);
  }, []);
  const { stretchDirections, fetchData, query, loading, setQuery, total } =
    useStretchDirection({
      initQuery: {
        page: 1,
        limit: 10,
        search: "",
      },
    });
  useEffect(() => {
    fetchData();
  }, [query]);
  const modalRef = React.useRef<VariantModal>(null);
  const tagColors = [
    "magenta",
    "volcano",
    "orange",
    "gold",
    "lime",
    "green",
    "cyan",
    "blue",
    "geekblue",
    "purple",
  ];

  const getColor = (index: number) => {
    return tagColors[index % tagColors.length];
  };
  const handleDelete = async (id: number) => {
    try {
      setLoadingDelete(true);
      await stretchDirectionApi.delete(id);
      message.success("Xóa thành công");
      fetchData();
    } catch (e) {
      console.log({ e });
    } finally {
      setLoadingDelete(false);
    }
  };
  const importModal = useRef<ImportSettingComponentModal>();
  const handleOnUploadedFile = async (excelData: any, setData: any) => {
    const { results } = excelData;

    console.log("results", results);

    const importData = results?.map((item: any) => {
      const refineRow = removeSubstringFromKeys(item, " (*)");
      const code = refineRow["MÃ BIẾN THỂ"] || "";
      const privateName = refineRow["TÊN BIẾN THỂ (NỘI BỘ)"] || "";
      const name = refineRow["TÊN BIẾN THỂ (KHÁCH HÀNG)"] || "";
      const frontImage = refineRow["ẢNH MẶT TRƯỚC"] || "";
      const backImage = refineRow["ẢNH MẶT SAU"] || "";
      const materialCode = refineRow["MÃ NGUYÊN VẬT LIỆU"] || "";
      const componentCode = refineRow["MÃ NHÓM"] || "";
      return {
        name,
        privateName,
        code,
        frontImage,
        backImage,
        materialCode,
        componentCode,
        rowNum: item.__rowNum__,
      };
    });
    console.log("importData", importData);

    setData(importData);
  };
  const debounceSearch = useCallback(
    debounce(
      (keyword) => setQuery({ ...query, search: keyword, page: 1 }),
      300
    ),
    []
  );
  return (
    <div>
      <div className="filter-container">
        <Space>
          <div className="filter-item">
            <label htmlFor="">Tìm kiếm</label>
            <Input
              onKeyDown={(ev) => {
                if (ev.code == "Enter") {
                  query.page = 1;
                  setQuery({ ...query });
                }
              }}
              size="middle"
              onChange={(ev) => {
                debounceSearch(ev.target.value);
              }}
              placeholder="Tìm kiếm"
            />
          </div>

          <div className="filter-item btn">
            <Button
              onClick={fetchData}
              type="primary"
              icon={<SearchOutlined />}
            >
              Tìm kiếm
            </Button>
          </div>

          <div className="filter-item btn">
            <Button
              onClick={() => {
                modalRef.current?.handleCreate();
              }}
              type="primary"
              icon={<PlusOutlined />}
            >
              Thêm mới
            </Button>
          </div>
          {/* <div className="filter-item btn">
            <Popconfirm
              title={`Bạn có muốn xuất file excel`}
              onConfirm={() =>
                handleExport({
                  onProgress(percent) {},
                  exportColumns,
                  fileType: "xlsx",
                  dataField: "stretchDirections",
                  query: query,
                  api: stretchDirectionApi.findAll,
                  fileName: "Danh sách cấu stretch direction",
                  sheetName: "Danh sách cấu stretch direction",
                })
              }
              okText={"Xuất excel"}
              cancelText={"Huỷ"}
            >
              <Button type="primary" loading={false} icon={<ExportOutlined />}>
                Xuất file excel
              </Button>
            </Popconfirm>
          </div> */}
          {/* <div className="filter-item btn">
            <Button
              onClick={() => {
                importModal.current?.open();
              }}
              type="primary"
              icon={<ImportOutlined />}
            >
              Nhập excel
            </Button>
          </div> */}
        </Space>
      </div>

      <Spin spinning={loading}>
        <Table
          pagination={false}
          rowKey="id"
          dataSource={stretchDirections}
          scroll={{ x: "max-content" }}
        >
          <Column
            title="Tên"
            align="left"
            dataIndex="name"
            key="name"
            render={(text, record: StretchDirection) => {
              return (
                <div className=" flex gap-2 items-center justify-start">
                  <div className="w-[50px] h-[50px] flex">
                    <img
                      src={record?.icon ? $url(record?.icon) : defaultImg}
                      className="object-cover size-full"
                    />
                  </div>
                  <div>{text}</div>
                </div>
              );
            }}
          />
          <Column
            fixed="right"
            width={120}
            title="Thao tác"
            key="action"
            render={(text, record: Variant) => (
              <div className="flex gap-2">
                <Popconfirm
                  onConfirm={() => {
                    handleDelete(record.id);
                  }}
                  title="Xác nhận xóa"
                >
                  <Button>Xóa</Button>
                </Popconfirm>
                <Button
                  type="primary"
                  onClick={() => {
                    modalRef.current?.handleUpdate(record);
                  }}
                >
                  Cập nhật
                </Button>
              </div>
            )}
          />
        </Table>

        <Pagination
          defaultPageSize={query.limit}
          currentPage={query.page}
          total={total}
          onChange={({ limit, page }) => {
            query.page = page;
            query.limit = limit;
            setQuery({ ...query });
          }}
        />
      </Spin>

      {useMemo(
        () => (
          <ImportVariantConfiguration
            guide={[
              "Vui lòng tải và sử dụng file mẫu để import dữ liệu đúng cách",
              "Không được thay đổi tiêu đề trong file csv/excel mẫu để tránh import thiếu dữ liệu",
              "Chi tiết xem tại sheet Hướng dẫn của file import mẫu",
            ]}
            onSuccess={() => {
              fetchData();
              // message.success("Nhập dữ liệu thành công.");
            }}
            ref={importModal}
            createApi={variantApi.import}
            onUploaded={(excelData, setData) => {
              console.log("up gì lên vậy", excelData);
              handleOnUploadedFile(excelData, setData);
            }}
            okText={`Nhập cấu hình thành phần ngay`}
            demoExcel="/exportFile/file_mau_nhap_direction_stretch.xlsx"
          />
        ),
        []
      )}
      <CreateUnitModal
        onSubmitOk={fetchData}
        onClose={() => {}}
        ref={modalRef}
      />
    </div>
  );
};

export default StretchDirectionPage;
