import { AxiosPromise } from "axios";
import { request } from "utils/request";

export const unitApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/unit",
      params,
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/unit/${id}`,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/unit",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/unit/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/unit/${id}`,
      method: "delete",
    }),
  import: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/unit/import",
      data,
      method: "post",
    }),
};
