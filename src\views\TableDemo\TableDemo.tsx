import React, { useState } from 'react';
import { Card, Tag, Button, Space, Tooltip } from 'antd';
import { EditOutlined, DeleteOutlined, FileTextOutlined } from '@ant-design/icons';
import CustomizableTable, { CustomizableColumn } from 'components/Table/CustomizableTable';
import { useThemeColors } from 'theme/useThemeColors';
import './TableDemo.scss';

interface ServiceItem {
  id: string;
  code: string;
  name: string;
  type: string;
  price: number;
  duration: string;
  status: string;
  ncc: string;
  attachments: number;
}

const TableDemo: React.FC = () => {
  const { color } = useThemeColors();
  const [loading, setLoading] = useState(false);
  
  // Sample data
  const dataSource: ServiceItem[] = [
    {
      id: 'DL123',
      code: 'DL123',
      name: 'Tên dịch vụ',
      type: 'Dịch vụ tư vấn',
      price: 1500000,
      duration: '1 tháng',
      status: 'active',
      ncc: 'NCC',
      attachments: 2,
    },
    {
      id: 'AD456',
      code: 'AD456',
      name: 'Tên dịch vụ',
      type: 'Bảo trì',
      price: 1500000,
      duration: '1 tháng',
      status: 'active',
      ncc: 'NCC',
      attachments: 2,
    },
    {
      id: 'GH789',
      code: 'GH789',
      name: 'Tên dịch vụ',
      type: 'Thuê thiết bị',
      price: 1500000,
      duration: '1 tháng',
      status: 'active',
      ncc: 'NCC',
      attachments: 2,
    },
  ];
  
  // Define columns
  const columns: CustomizableColumn<ServiceItem>[] = [
    {
      key: 'id',
      title: 'ID',
      dataIndex: 'id',
      width: 100,
      defaultVisible: true,
      alwaysVisible: true,
    },
    {
      key: 'type',
      title: 'Dịch vụ',
      width: 150,
      render: (_, record) => (
        <div className="service-cell">
          <div className="service-icon">
            <img src="https://via.placeholder.com/40" alt="Service" />
          </div>
          <div className="service-info">
            <div className="service-name">{record.name}</div>
            <Tag color={getServiceTypeColor(record.type)}>{record.type}</Tag>
          </div>
        </div>
      ),
      defaultVisible: true,
    },
    {
      key: 'price',
      title: 'Chi phí',
      dataIndex: 'price',
      width: 150,
      align: 'right',
      render: (price) => `${price.toLocaleString()} VNĐ`,
      defaultVisible: true,
    },
    {
      key: 'duration',
      title: 'Thời gian thực hiện',
      dataIndex: 'duration',
      width: 180,
      defaultVisible: true,
    },
    {
      key: 'ncc',
      title: 'NCC',
      dataIndex: 'ncc',
      width: 120,
      defaultVisible: true,
    },
    {
      key: 'attachments',
      title: 'Tệp đính kèm',
      width: 150,
      render: (_, record) => (
        <div className="attachments-cell">
          <FileTextOutlined /> {record.attachments} tệp
        </div>
      ),
      defaultVisible: true,
    },
    {
      key: 'status',
      title: 'Trạng thái',
      width: 150,
      render: (_, record) => (
        <Tag color="orange" className="status-tag">
          Đang thực hiện
        </Tag>
      ),
      defaultVisible: true,
    },
    {
      key: 'actions',
      title: 'Xử lý',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="Chỉnh sửa">
            <Button type="text" icon={<EditOutlined />} />
          </Tooltip>
          <Tooltip title="Xóa">
            <Button type="text" danger icon={<DeleteOutlined />} />
          </Tooltip>
        </Space>
      ),
      defaultVisible: true,
      alwaysVisible: true,
    },
  ];
  
  // Helper function to get color for service type
  const getServiceTypeColor = (type: string): string => {
    switch (type) {
      case 'Dịch vụ tư vấn':
        return 'green';
      case 'Bảo trì':
        return 'orange';
      case 'Thuê thiết bị':
        return 'blue';
      default:
        return 'default';
    }
  };
  
  // Pagination settings
  const pagination = {
    current: 1,
    pageSize: 10,
    total: dataSource.length,
    showSizeChanger: true,
  };
  
  return (
    <div className="table-demo-page">
      <Card title="Danh sách dịch vụ" className="table-card">
        <CustomizableTable
          columns={columns}
          dataSource={dataSource}
          rowKey="id"
          loading={loading}
          pagination={pagination}
          scroll={{ x: 1200 }}
          bordered
        />
      </Card>
    </div>
  );
};

export default TableDemo;