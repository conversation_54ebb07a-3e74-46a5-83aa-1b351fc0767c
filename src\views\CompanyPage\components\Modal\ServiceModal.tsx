import {
  Col,
  DatePicker,
  Form,
  Input,
  message,
  Modal,
  Row,
  Select,
  Switch,
} from "antd";
import TextArea from "antd/es/input/TextArea";
import { Rule } from "antd/lib/form";
import { serviceApi } from "api/service.api";
import { InputNumber } from "components/Input/InputNumber";
import { ProviderSelector } from "components/Selector/ProviderSelector";
import { BMDTextArea } from "components/TextArea/BMDTextArea";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import dayjs from "dayjs";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { settings } from "settings";
import { ModalStatus } from "types/modal";
import { Service, ServiceTypeTrans } from "types/service";

const rules: Rule[] = [{ required: true }];

export interface ServiceModalRef {
  handleCreate: () => void;
  handleUpdate: (service: Service) => void;
}
interface ServiceModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
}

export const ServiceModal = React.forwardRef(
  ({ onClose, onSubmitOk }: ServiceModalProps, ref) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");
    const [selectedService, setSelectedService] = useState<Service>();
    useImperativeHandle<any, ServiceModalRef>(
      ref,
      () => ({
        handleCreate() {
          form.resetFields();
          setVisible(true);
          setStatus("create");
        },
        handleUpdate(service: Service) {
          setSelectedService(service);
          form.setFieldsValue({
            ...service,
            serviceRange:
              service.startAt && service.endAt
                ? [
                    dayjs.unix(service.startAt || 0),
                    dayjs.unix(service.endAt || 0),
                  ]
                : undefined,
          });
          setVisible(true);
          setStatus("update");
        },
      }),
      []
    );

    const handleSubmitForm = async () => {
      await form.validateFields();

      setLoading(true);

      const {
        serviceRange,

        ...serviceData
      } = form.getFieldsValue();

      const dataPost = {
        service: {
          ...serviceData,
          startAt: serviceRange ? serviceRange?.[0]?.startOf("day")?.unix() : 0,
          endAt: serviceRange ? serviceRange?.[1]?.endOf("day")?.unix() : 0,
        },
      };

      let res;
      try {
        switch (status) {
          case "update":
            res = await serviceApi.update(selectedService?.id || 0, {
              ...dataPost,
            });
            message.success("Cập nhật dịch vụ thành công");

            break;

          default:
            res = await serviceApi.create({ ...dataPost });
            message.success("Thêm mới dịch vụ thành công");
            break;
        }

        form.resetFields();
        onSubmitOk();
        onClose();
        setVisible(false);
      } finally {
        setLoading(false);
      }
    };

    return (
      <Modal
        onCancel={() => {
          onClose?.();
          setVisible(false);
        }}
        visible={visible}
        title={status == "create" ? "Tạo dịch vụ" : "Cập nhật dịch vụ"}
        style={{ top: 20 }}
        width={700}
        confirmLoading={loading}
        onOk={() => {
          handleSubmitForm();
        }}
      >
        <Form layout="vertical" form={form}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="Tên dịch vụ" name="name" rules={rules}>
                <Input placeholder="" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item label="Loại dịch vụ" name="type" rules={rules}>
                <Select>
                  {Object.values(ServiceTypeTrans)?.map((item) => (
                    <Select.Option value={item.value} key={item.value}>
                      {item.label}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item label="Chi phí ước tính" name="estPrice" rules={rules}>
                <InputNumber placeholder="" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Thời gian dịch vụ"
                name="serviceRange"
                rules={rules}
              >
                <DatePicker.RangePicker
                  allowClear={false}
                  format={settings.dateFormat}
                  className="w-full"
                />
              </Form.Item>
            </Col>
            {/* <Col span={12}>
              <Form.Item label="Nhà cung cấp" name="providerId" rules={rules}>
                <ProviderSelector />
              </Form.Item>
            </Col> */}
            <Col span={12}>
              <Form.Item
                initialValue={false}
                label="Kích hoạt"
                name="isActive"
                rules={rules}
                valuePropName="checked"
              >
                <Switch checkedChildren={"Có"} unCheckedChildren={"Không"} />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="Mô tả" name="description">
                <BMDTextArea placeholder="" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
);
