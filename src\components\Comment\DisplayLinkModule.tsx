import { LinkOutlined } from "@ant-design/icons";
import React from "react";
import { IoTrashOutline } from "react-icons/io5";

interface LinkedModule {
  id: string;
  type: string;
  code: string;
  name: string;
}

interface DisplayLinkModuleProps {
  linkedModules: LinkedModule[];
  onDelete?: (moduleId: string) => void;
}

const DisplayLinkModule: React.FC<DisplayLinkModuleProps> = ({ linkedModules, onDelete }) => {
  return (
    <div className="grid grid-cols-4 gap-2">
      {linkedModules.map((module) => (
        <div
          key={module.id}
          className="bg-[var(--color-neutral-n2)] rounded p-2 flex items-center justify-between mt-2"
        >
          <div className="flex items-center gap-2 overflow-hidden">
            <div className="w-6 h-6 bg-primary rounded flex items-center justify-center text-white flex-shrink-0">
              <LinkOutlined />
            </div>
            <div className="overflow-hidden">
              <div className="font-medium text-xs truncate">{module.name}</div>
              <div className="text-[10px] text-neutral-n4">{module.code}</div>
            </div>
          </div>
          {onDelete && (
            <IoTrashOutline
              className="cursor-pointer text-red-500 text-sm flex-shrink-0"
              onClick={() => onDelete(module.id)}
            />
          )}
        </div>
      ))}
    </div>
  );
};

export default DisplayLinkModule;
