import {
  DeleteOutlined,
  ImportOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { Button, Input, message, Popconfirm, Space, Spin, Table } from "antd";
import { materialGroupApi } from "api/materialGroup.api";
import { Pagination } from "components/Pagination";
import React, {
  useEffect,
  useState,
  useRef,
  useMemo,
  useCallback,
} from "react";
import { ModalStatus } from "types/modal";
import { QueryParam } from "types/query";
import { MaterialGroup, MaterialGroupDetail } from "types/materialGroup";
import { getTitle } from "utils";
import { $url } from "utils/url";
import { MaterialGroupModal } from "./MaterialGroupModal";
import ImportMaterialGroup, {
  ImportMaterialGroupModal,
} from "components/ImportDocument/ImportMaterialGroup";
import { removeSubstringFromKeys } from "utils/common";
import { MaterialType } from "types/materialType";
import { useMaterialType } from "hooks/useMaterialType";
import { debounce } from "lodash";

const { ColumnGroup, Column } = Table;

export const MaterialGroupPage = ({ title = "" }) => {
  const [query, setQuery] = useState<QueryParam>({
    page: 1,
    limit: 10,
    search: "",
  });
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<MaterialGroup[]>([]);
  const [total, setTotal] = useState(0);
  const [selectedMaterialGroup, setSelectedMaterialGroup] = useState<
    Partial<MaterialGroup>
  >({});
  const modalRef = useRef<MaterialGroupModal>(null);
  const importModal = useRef<ImportMaterialGroupModal>();
  const [loadingDelete, setLoadingDelete] = useState(false);

  useEffect(() => {
    document.title = getTitle(title);
  }, []);

  useEffect(() => {
    fetchData();
  }, [query]);

  const fetchData = async () => {
    setLoading(true);
    const res = await materialGroupApi.findAll(query);
    setLoading(false);
    setData(res.data.materialGroups);
    setTotal(res.data.total);
  };

  const handleOnUploadedFile = async (excelData: any, setData: any) => {
    const { results } = excelData;

    console.log("results", results);

    const importData = results?.map((item: any) => {
      const refineRow = removeSubstringFromKeys(item, " (*)");

      const name = refineRow["Tên nhóm NVL"];
      const materialTypeName = refineRow["Tên loại NVL"];
      return {
        name,
        materialTypeName,
        rowNum: item.__rowNum__,
      };
    });
    console.log("importData", importData);

    setData(importData);
  };

  const handleDelete = async (id: number) => {
    try {
      setLoadingDelete(true);
      await materialGroupApi.delete(id);
      fetchData();
      message.success("Xóa thành công");
    } catch (e) {
      console.log({ e });
    } finally {
      setLoadingDelete(false);
    }
  };
  const handleFilterOfTable = (pagination: any, filters: any, sorter: any) => {
    console.log("sorter:", sorter);
    console.log("filters:", filters);

    let sortValue: "ASC" | "DESC" | undefined;
    if (!Array.isArray(sorter) && sorter?.order) {
      sortValue =
        sorter.order === "ascend"
          ? "ASC"
          : sorter.order === "descend"
          ? "DESC"
          : undefined;
    }
    const queryObject: any[] = [];
    if (filters.name) {
      queryObject.push({
        type: "sort",
        field: "materialGroup.name",
        value: filters.name?.[0],
      });
    }
    if (filters.materialType) {
      queryObject.push({
        type: "multi-filter",
        field: "materialGroup.materialTypeId",
        value: filters.materialType,
      });
    }
    query.page = 1;
    query.queryObject = JSON.stringify(queryObject);
    fetchData();
  };
  const {
    fetchData: fetchMaterialTypes,
    loading: loadingMaterialTypes,
    materialTypes,
    query: queryMaterialTypes,
    setQuery: setQueryMaterialTypes,
    total: totalMaterialTypes,
  } = useMaterialType({
    initQuery: {
      page: 1,
      limit: 50,
    },
  });
  useEffect(() => {
    fetchMaterialTypes();
  }, []);
  const debounceSearch = useCallback(
    debounce(
      (keyword) => setQuery({ ...query, search: keyword, page: 1 }),
      300
    ),
    [query]
  );
  return (
    <div>
      <div className="filter-container">
        <Space>
          <div className="filter-item">
            <label htmlFor="">Tìm kiếm</label>
            <Input
              onKeyDown={(ev) => {
                if (ev.code == "Enter") {
                  query.page = 1;
                  setQuery({ ...query });
                }
              }}
              size="middle"
              onChange={(ev) => {
                debounceSearch(ev.target.value);
              }}
              placeholder="Tìm kiếm"
            />
          </div>

          <div className="filter-item btn">
            <Button
              onClick={fetchData}
              type="primary"
              icon={<SearchOutlined />}
            >
              Tìm kiếm
            </Button>
          </div>

          <div className="filter-item btn">
            <Button
              onClick={() => {
                modalRef.current?.handleCreate();
              }}
              type="primary"
              icon={<PlusOutlined />}
            >
              Thêm mới
            </Button>
          </div>
          <div className="filter-item btn">
            <Button
              onClick={() => {
                importModal.current?.open();
              }}
              type="primary"
              icon={<ImportOutlined />}
            >
              Nhập excel
            </Button>
          </div>
        </Space>
      </div>

      <Spin spinning={loading}>
        <Table
          pagination={false}
          rowKey="id"
          dataSource={data}
          onChange={handleFilterOfTable}
        >
          <Column
            title="Tên nhóm NVL"
            dataIndex="name"
            key="name"
            filterMultiple={false}
            filteredValue={(() => {
              try {
                const obj = JSON.parse(query.queryObject || "[]");
                const item = obj.find(
                  (o: any) => o.field === "materialGroup.name"
                );
                return item ? [item.value] : null;
              } catch (e) {
                return null;
              }
            })()}
            filters={[
              { text: "A-Z", value: "ASC" },
              { text: "Z-A", value: "DESC" },
            ]}
          />
          <Column
            title="Loại NVL"
            dataIndex="materialType"
            key="materialType"
            render={(materialType: MaterialType) => (
              <div>{materialType?.name}</div>
            )}
            filterSearch
            filteredValue={(() => {
              try {
                const obj = JSON.parse(query.queryObject || "[]");
                const item = obj.find(
                  (o: any) => o.field === "materialGroup.materialTypeId"
                );
                return item?.value || null;
              } catch (e) {
                return null;
              }
            })()}
            filters={materialTypes?.map((item) => ({
              value: item.id,
              text: item.name,
            }))}
          />
          <Column
            align="right"
            title="Vị trí"
            key="position"
            render={(text, record: MaterialGroup) => (
              <div>{record.position}</div>
            )}
          />
          <Column
            width={200}
            align="center"
            title="Thao tác"
            key="action"
            render={(text, record: MaterialGroup) => (
              <Space>
                <Popconfirm
                  placement="topLeft"
                  title={`Xác nhận xóa nhóm NVL này?`}
                  onConfirm={() => handleDelete(record.id)}
                  okText="Xác nhận"
                  cancelText="Không"
                >
                  <Button
                    // icon={<DeleteOutlined />}
                    className="w-full  "
                    danger
                  >
                    Xóa
                  </Button>
                </Popconfirm>
                <Button
                  type="primary"
                  onClick={() => {
                    modalRef.current?.handleUpdate(record);
                  }}
                >
                  Cập nhật
                </Button>
              </Space>
            )}
          />
        </Table>
        <Pagination
          defaultPageSize={query.limit}
          currentPage={query.page}
          total={total}
          onChange={({ limit, page }) => {
            query.page = page;
            query.limit = limit;
            setQuery({ ...query });
          }}
        />
      </Spin>
      {useMemo(
        () => (
          <ImportMaterialGroup
            guide={[
              "Vui lòng tải và sử dụng file mẫu để import dữ liệu đúng cách",
              "Không được thay đổi tiêu đề trong file csv/excel mẫu để tránh import thiếu dữ liệu",
              "Chi tiết xem tại sheet Hướng dẫn của file import mẫu",
            ]}
            onSuccess={() => {
              query.page = 1;
              fetchData();
              // message.success("Nhập dữ liệu thành công.");
            }}
            ref={importModal}
            createApi={materialGroupApi.create}
            onUploaded={(excelData, setData) => {
              console.log("up gì lên vậy", excelData);
              handleOnUploadedFile(excelData, setData);
            }}
            okText={`Nhập nhóm NVL ngay`}
            demoExcel="/exportFile/Tao_moi_nhom_NVL.xlsx"
          />
        ),
        []
      )}
      <MaterialGroupModal
        onSubmitOk={fetchData}
        onClose={() => {}}
        ref={modalRef}
      />
    </div>
  );
};
