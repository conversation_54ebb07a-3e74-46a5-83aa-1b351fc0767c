import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const scheduleApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/schedule",
      params,
    }),
  findAllSelect: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/schedule/select",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/schedule",
      data,
      method: "post",
    }),
  createScheduleSelect: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/schedule/select",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/schedule/${id}`,
      method: "patch",
      data,
    }),
  updateScheduleSelect: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/schedule/select/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/schedule/${id}`,
      method: "delete",
    }),
};
