import React, { forwardRef, useImperativeHandle, useMemo } from "react";
import CustomSelect from "components/Input/CustomSelect";
import { WorkStatus, WorkStatusTrans } from "types/workStatus";
import { Select } from "antd";

type StaffStatusSelectorProps = {
  value?: string;
  disabled?: boolean;
  onChange?: (value: any) => void;
  allowClear?: boolean;
  placeholder?: string;
  label?: string;
  includeBlockedStatus?: boolean;
  includeAllOption?: boolean;
  showSearch?: boolean;
};

export interface StaffStatusSelector {
  // Add any methods if needed in the future
}

export const StaffStatusSelector = forwardRef(
  (
    {
      value,
      label,
      onChange,
      disabled,
      allowClear = true,
      placeholder = "Chọn trạng thái",
      includeBlockedStatus = true,
      includeAllOption = true,
      showSearch = true,
    }: StaffStatusSelectorProps,
    ref
  ) => {
    useImperativeHandle<any, StaffStatusSelector>(ref, () => ({}), []);

    const options = useMemo(() => {
      const statusOptions = [];

      // Add "Tất cả trạng thái" option if needed
      if (includeAllOption) {
        statusOptions.push({
          value: "",
          label: "Tất cả trạng thái",
        });
      }

      // Add "Bị khóa" status if needed
      if (includeBlockedStatus) {
        statusOptions.push({
          value: "blocked",
          label: "Bị khóa",
        });
      }

      // Add all work status options except DELETED
      statusOptions.push(
        ...Object.values(WorkStatusTrans)
          .filter((status) => status.value !== WorkStatus.Deleted)
          .map((status) => ({
            value: status.value,
            label: status.label,
          }))
      );

      return statusOptions;
    }, [includeBlockedStatus, includeAllOption]);

    const handleChange = (v: any) => {
      onChange?.(v);
    };

    // Custom filter function for client-side search
    const filterOption = (input: string, option?: any) => {
      if (!input) return true;
      return option?.label?.toLowerCase().includes(input.toLowerCase());
    };

    return (
      <Select
        value={value}
        onChange={handleChange}
        disabled={disabled}
        options={options}
        allowClear={allowClear}
        placeholder={placeholder}
        showSearch={showSearch}
        filterOption={filterOption}
      />
    );
  }
);
