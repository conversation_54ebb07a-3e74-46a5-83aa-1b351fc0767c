.grid-view {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 1rem;
  padding: 1rem 0;

  @media (min-width: 640px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (min-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (min-width: 1024px) {
    grid-template-columns: repeat(3, 1fr);
  }

  @media (min-width: 1280px) {
    grid-template-columns: repeat(4, 1fr);
  }

  @media (min-width: 1536px) {
    grid-template-columns: repeat(4, 1fr);
  }
}

.blueprint-card {
  transition: all 0.3s ease;
  width: 100%;
  max-width: 320px;
  margin: 0 auto;
  border-radius: 8px;
  overflow: hidden;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .card-cover,
  .pdf-preview {
    width: 100%;
    height: 180px;
    max-width: 100%;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background: #f5f5f5;
    padding: 0;
  }

  .card-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }

  .card-title {
    text-align: left;

    .title-text {
      color: #000000;
      font-weight: 500;
      font-size: 13px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .card-description {
    text-align: left;

    .date-text {
      font-size: 13px;
      color: #000000;
    }
  }

  .ant-card-cover {
    margin: 0;
    padding: 0;
    border-radius: 0;
  }

  .ant-card-actions {
    background: #fafafa;
    border-top: 1px solid #f0f0f0;
    
    li {
      margin: 0;
      
      button {
        border: none;
        background: transparent;
        
        &:hover {
          color: #1890ff;
        }
      }
    }
  }
}

// Placeholder styles
.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
  
  .placeholder-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }
  
  .placeholder-text {
    font-size: 0.875rem;
    text-align: center;
    max-width: 80%;
    word-break: break-word;
  }
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
}
