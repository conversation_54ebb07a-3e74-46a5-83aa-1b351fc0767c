.card-view {
  padding: 16px;
  background-color: var(--color-neutral-n0);
  border: 1px solid var(--color-neutral-n3);
  font-weight: 300;
  border-radius: 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .project-status {
    width: 16px;
    height: 16px;
  }

  .status-new {
    width: 100%;
    height: 100%;
    background-color: var(--color-project-planning);
  }

  .status-progress {
    width: 100%;
    height: 100%;
    background-color: var(--color-project-in-progress);
  }

  .status-cancel {
    width: 100%;
    height: 100%;
    background-color: var(--color-project-hold);
  }

  .status-complete {
    width: 100%;
    height: 100%;
    background-color: var(--color-project-done);
  }

  .task-status {
    position: relative;
    padding: 4px 16px;
    min-width: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: fit-content;

    .task-status-label {
      font-weight: 700;
      position: relative;
      text-align: center;
      display: inline-block;
    }
  }

  .card-view-collapse {
    .expand-icon * {
      stroke: var(--color-neutral-n8);
    }

    .ant-collapse-header,
    .ant-collapse-content-box {
      padding: 0;
    }
  }

  .header-info {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    margin-top: 0.25rem;
    align-items: start;

    .info-item {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: start;
      gap: 0.25rem;

      .info-content {
        display: flex;
        gap: 0.5rem;
        align-items: center;

        .icon-wrapper {
          width: 32px;
          height: 32px;
          background: #e4eced;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
}
