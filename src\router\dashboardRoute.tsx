import { lazy } from "react";
import { ReactComponent as DashboardIcon } from "assets/svgs/dashboard.svg";
import { PermissionType } from "types/permission";
import { PermissionNames } from "types/PermissionNames";
import { Route } from "./RouteType";

const InDevelopment = lazy(
  () => import("components/InDevevelopment/InDevelopment")
);

export const dashboardRoutes: Route[] = [
  {
    title: "Dashboard",
    breadcrumb: "Dashboard",
    path: "/dashboard",
    name: "dashboard",
    aliasPath: "/dashboard",
    icon: <DashboardIcon />,
    // element: <InDevelopment />,
    permissionTypes: [
      PermissionType.Add,

      PermissionType.List,
      PermissionType.Edit,
      PermissionType.Delete,
    ],
    isCompact: true,
    isPublic: true,
    children: [
      {
        title: "Dashboard",
        path: PermissionNames.dashboardList,
        name: PermissionNames.dashboardList,
        aliasPath: `/dashboard/${PermissionNames.dashboardList}`,
        element: <InDevelopment />,
        isPublic: true,
      },
    ],
  },
];
