import { <PERSON><PERSON>, Drawer, <PERSON>u } from "antd";
import Sider from "antd/lib/layout/Sider";
import SubMenu from "antd/lib/menu/SubMenu";
import { observer } from "mobx-react";
import { Fragment, useCallback, useEffect, useMemo, useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { settings } from "settings";
import { permissionStore } from "store/permissionStore";
import { $isShowGameRouter } from "constant";
import { userStore } from "store/userStore";
import { useTheme } from "context/ThemeContext";
import clsx from "clsx";
import { IoMdArrowDropdown } from "react-icons/io";
import { isMobile } from "react-device-detect";
import { HEADER_HEIGHT, SIDEBAR_WIDTH } from "utils/theme";
import { findMatchedRoute } from "router/routeUtil";
import { adminRoutes } from "router";
import { toJS } from "mobx";
import { PermissionType } from "types/permission";
import { appStore } from "store/appStore";
import { SYSTEM_CONFIG_GROUPS } from "types/SystemConfigRoute";
import { Route } from "router/RouteType";

const renderSystemConfigSubMenu = (route: any) => {
  return SYSTEM_CONFIG_GROUPS.map((group) => {
    const groupChildren = route.children?.filter(
      (child: any) =>
        group.permissions.includes(child.path) &&
        (child.isPublic || child.isAccess || !settings.checkPermission) &&
        !child.hidden
    );

    if (!groupChildren?.length) return null;

    return (
      <SubMenu
        key={`${route.path}/${group.key}`}
        title={group.title}
        popupClassName="custom-sub-menu-popup"
      >
        {groupChildren.map((item: any) => {
          const subKey = `${route.path}/${item.path}`;
          return (
            <Menu.Item key={subKey}>
              <div className="menu-dot"></div>
              <Link to={subKey}>{item.title}</Link>
            </Menu.Item>
          );
        })}
      </SubMenu>
    );
  });
};

const pinTopRouteNames = ["dashboard", "project"];

export const Sidebar = observer(
  ({ collapsed, toggle }: { collapsed: boolean; toggle: () => void }) => {
    const { darkMode } = useTheme();

    // const [defaultOpenKeys, setDefaultOpenKeys] = useState<string[]>([]);
    const location = useLocation();
    const [isLoaded, setIsLoaded] = useState(false);
    const [currentOpenKeys, setCurrentOpenKeys] = useState<string[]>([]);

    useEffect(() => {
      const routes = permissionStore.accessRoutes;
      let firstRoute = routes.find(
        (route) =>
          (route.isAccess || !settings.checkPermission) && route.children
      );
      if (
        firstRoute &&
        firstRoute.path &&
        location.pathname.includes(firstRoute.path)
      ) {
        setCurrentOpenKeys(() => [firstRoute?.path || ""]);
      }
      setIsLoaded(true);
    }, []);

    const visibleMenus = useMemo(() => {
      return permissionStore.accessRoutes
        .map((route) => {
          if (route.children) {
            return route.children.map((routeChildren) => {
              if (
                !routeChildren.hidden &&
                (routeChildren.isAccess ||
                  routeChildren.isPublic ||
                  !settings.checkPermission)
              )
                return route.path + "/" + routeChildren.path;
              return undefined;
            });
          }
          if (
            !route.hidden &&
            (route.isAccess || route.isPublic || !settings.checkPermission)
          )
            return route.path;
          return undefined;
        })
        .flat()
        .filter(Boolean) as string[];
    }, [permissionStore.accessRoutes]);

    const currenPath = useMemo(() => {
      //Cách này tổng quan hơn
      // debugger;
      const parentPath = location.pathname.split("/").slice(0, 3).join("/");
      let currentPath =
        visibleMenus.find((path) => {
          let checkPath = path;
          if (
            checkPath.includes("/project-detail/:id") &&
            appStore.currentProject
          ) {
            checkPath = checkPath.replace(
              ":id",
              appStore.currentProject.id.toString()
            );
          }
          return checkPath.startsWith(parentPath);
        }) || "";
      if (
        currentPath.includes("/project-detail/:id") &&
        appStore.currentProject
      ) {
        currentPath = currentPath.replace(
          ":id",
          appStore.currentProject.id.toString()
        );
      }
      // console.log({ parentPath, currentPath });
      return currentPath;

      //Cách này thủ công
      let pathname = location.pathname;
      if (pathname.includes("/update/")) {
        pathname = pathname.split("/").slice(0, -1).join("/");
      }
      return (
        pathname.split("/").slice(0, -1).join("/") + `/${PermissionType.List}`
      );
    }, [location.pathname, visibleMenus, appStore.currentProject]);
    // console.log("[sidebar] current key", currenPath);

    const checkFilterRoute = (route: Route) => {
      // if (route.name == "doc-management") debugger;
      if (
        !route.hidden &&
        (route.isPublic || route.isAccess || !settings.checkPermission)
      ) {
        if ($isShowGameRouter) {
          // console.log($isShowGameRouter);
          return true;
        } else {
          if (route?.isGameRouter) {
            return false;
          } else {
            return true;
          }
        }
      }
    };

    console.log(
      "appStore.unreadNotificationCount",
      appStore.unreadNotificationCount
    );

    const menuItemsRender = useCallback(
      (route: Route) => {
        // Xử lý đặc biệt cho menu Cấu hình
        if (route.path === "/system-config") {
          return (
            <Fragment key={route.path}>
              <SubMenu
                key={route.path}
                icon={route.icon}
                expandIcon={
                  <IoMdArrowDropdown className="ant-menu-submenu-arrow" />
                }
                title={
                  <span className="-title-content uppercase">
                    {route.title}
                  </span>
                }
                popupClassName="custom-sub-menu-popup"
              >
                {renderSystemConfigSubMenu(route)}
              </SubMenu>
              {/* <Divider
                      className={clsx(
                        "my-2 mx-auto min-w-0",
                        collapsed ? "w-1/2" : "w-[90%]"
                      )}
                    /> */}
            </Fragment>
          );
        }

        // Xử lý các menu khác như cũ
        if (route.children?.length) {
          const filterElementRoutes = route.children.filter(
            (item) =>
              item.element && (item.isAccess || item.isPublic) && !item.hidden
          );
          if (filterElementRoutes.length == 1 && route.isCompact) {
            // debugger;
            const element = filterElementRoutes[0];
            const path = `${route.path!}/${element.path}`.trim() || "";
            return (
              <Fragment key={path}>
                <Menu.Item
                  icon={route.icon}
                  key={path}
                  // onClick={() => console.log("path ne", route.path)}
                >
                  <Link to={path || ""} className="uppercase">
                    {route.title}
                  </Link>
                </Menu.Item>
                {/* <Divider
                        className={clsx(
                          "my-2 mx-auto min-w-0",
                          collapsed ? "w-1/2" : "w-[90%]"
                        )}
                      /> */}
              </Fragment>
            );
          }
          return (
            <Fragment key={route.path}>
              <SubMenu
                key={route.path}
                icon={route.icon}
                expandIcon={
                  <IoMdArrowDropdown className="ant-menu-submenu-arrow" />
                }
                title={
                  <span className="-title-content uppercase">
                    {route.title}
                  </span>
                }
                popupClassName="custom-sub-menu-popup"
              >
                {route.children
                  ?.filter(
                    (child) =>
                      child.isPublic ||
                      child.isAccess ||
                      !settings.checkPermission
                  )
                  .filter((item) => !item.hidden)
                  .map((item) => {
                    const subKey = route.path + "/" + item.path;

                    return (
                      <Menu.Item key={subKey}>
                        <div className="menu-dot"></div>
                        {route.path && item.path && (
                          <Link to={subKey}>{item.title} </Link>
                        )}
                      </Menu.Item>
                    );
                  })}
              </SubMenu>
              {/* <Divider
                      className={clsx(
                        "my-2 mx-auto min-w-0",
                        collapsed ? "w-1/2" : "w-[90%]"
                      )}
                    /> */}
            </Fragment>
          );
        }
        let link = route.path || "";
        let menuKey = link;
        if (link.includes("/project-detail/:id") && appStore.currentProject) {
          link = link.replace(":id", appStore.currentProject.id.toString());
          menuKey = menuKey.replace(
            ":id",
            appStore.currentProject.id.toString()
          );
        }
        // console.log("sidebarKey", route.path);
        return (
          <Fragment key={menuKey}>
            <Menu.Item icon={route.icon} key={menuKey}>
              <Link
                to={link || ""}
                className="uppercase flex items-center gap-2"
              >
                {route.title}
                {route.path === "/notification" &&
                  appStore.unreadNotificationCount > 0 && (
                    <Badge
                      count={appStore.unreadNotificationCount}
                      classNames={{
                        indicator: "!px-[1px] ml-2",
                      }}
                    />
                  )}
              </Link>
            </Menu.Item>
            {/* <Divider
                    className={clsx(
                      "my-2 mx-auto min-w-0",
                      collapsed ? "w-1/2" : "w-[90%]"
                    )}
                  /> */}
          </Fragment>
        );
      },
      [appStore.currentProject, permissionStore.accessRoutes]
    );

    // console.log({ accessRoute: toJS(permissionStore.accessRoutes) });

    const menuRender = useMemo(
      () => (
        <Menu
          className={clsx("sidebar custom-scrollbar", isMobile ? "" : "h-full")}
          theme="light"
          mode="inline"
          inlineCollapsed={collapsed}
          selectedKeys={[currenPath]}
          openKeys={currentOpenKeys}
          // defaultOpenKeys={defaultOpenKeys}
          onOpenChange={(openKeys) => {
            const currentOpenKey = openKeys.find(
              (key) => currentOpenKeys.indexOf(key) === -1
            );
            if (currentOpenKey !== undefined) {
              setCurrentOpenKeys(
                openKeys.filter((openKey) => currentOpenKey.includes(openKey))
              );
            } else {
              setCurrentOpenKeys(openKeys);
            }
          }}
          getPopupContainer={(trigger) =>
            document.getElementById("App") as HTMLElement
          }
          // forceSubMenuRender
        >
          {permissionStore.accessRoutes
            .filter((r) => {
              return pinTopRouteNames.includes(r.name || "");
            })
            .filter((route) => {
              return checkFilterRoute(route);
            })
            .map((route) => {
              return menuItemsRender(route);
            })}
          {appStore.currentProject &&
            permissionStore.accessRoutes
              .filter((r) => {
                return r.needProject;
              })
              .filter((route) => {
                return checkFilterRoute(route);
              })
              .map((route) => {
                return menuItemsRender(route);
              })}
          {permissionStore.accessRoutes
            .filter((r) => {
              return !r.needProject && !pinTopRouteNames.includes(r.name || "");
            })
            .filter((route) => {
              return checkFilterRoute(route);
            })
            .map((route) => {
              return menuItemsRender(route);
            })}
        </Menu>
      ),
      [
        location,
        // defaultOpenKeys,
        currentOpenKeys,
        isMobile,
        permissionStore.accessRoutes,
        appStore.currentProject,
        appStore.unreadNotificationCount,
        userStore.info.username,
        collapsed,
        currenPath,
      ]
    );

    return window.innerWidth <= 1024 ? (
      <Drawer
        open={!collapsed}
        onClose={() => {
          toggle();
        }}
        placement="left"
        className="custom-sider"
        classNames={{
          header: "!hidden",
          body: "!px-0 !py-4 bg-[var(--color-primary)]",
        }}
        width={SIDEBAR_WIDTH}
      >
        <div className="flex justify-center mb-2">
          <img
            src={settings.logoWhite}
            alt=""
            className="inline-block h-[52px] object-contain"
          />
        </div>
        {menuRender}
      </Drawer>
    ) : (
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        width={SIDEBAR_WIDTH}
        className="custom-sider"
        collapsedWidth={0}
      >
        <div
          className="h-[68px]"
          style={{
            height: HEADER_HEIGHT,
          }}
        ></div>

        {isLoaded && menuRender}
      </Sider>
    );
  }
);
