import {
  Card,
  Col,
  Row,
  Form,
  But<PERSON>,
  Spin,
  Tabs,
  Select,
  message,
  Input,
  DatePicker,
  Tooltip,
  Modal,
  Table,
  Collapse,
  UploadFile,
} from "antd";
import PageTitle from "components/PageTitle/PageTitle";
import { useEffect, useMemo, useRef, useState } from "react";
import { Rule } from "antd/lib/form";
import CustomButton from "components/Button/CustomButton";
import { ModalStatus } from "types/modal";
import { PermissionNames } from "types/PermissionNames";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { FileAttach } from "types/fileAttach";
import { isEmpty } from "lodash";
import { getTitle } from "utils";
import { FileUploadMultiple2 } from "components/Upload/FileUploadMultiple2";
import { useWatch } from "antd/es/form/Form";
import { fileAttachApi } from "api/fileAttach.api";
import { $url } from "utils/url";
import dayjs from "dayjs";
import { Staff } from "types/staff";
import TextArea from "antd/es/input/TextArea";
import { settings } from "settings";
import { dictionaryApi } from "api/dictionary.api";
import clsx from "clsx";
import { observer } from "mobx-react";
import { checkRoles } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { Comment } from "types/comment";
import { appStore } from "store/appStore";
import { MemberShip } from "types/memberShip";
import { useApprovalStep } from "hooks/useAppovalStep";
import { userStore } from "store/userStore";
import { toJS } from "mobx";
import { MembershipSelector } from "components/Selector/MembershipSelector";
import { DailyLog, DailyLogTypeOptions } from "types/dailyLog";
import { PlusOutlined } from "@ant-design/icons";
import DeleteIcon from "assets/svgs/DeleteIcon";
import { MultiImageUpload } from "components/Upload/MultiImageUpload";
import { CustomizableColumn } from "components/Table/CustomizableTable";
import { DailyLogDetailTable } from "./DailyLogDetailTable";
import { TaskTemplateSelector } from "components/Selector/TaskTemplateSelector";
import { dailyLogApi } from "api/dailyLog.api";
import { DictionarySelector } from "components/Selector/DictionarySelector";
import { DictionaryType } from "types/dictionary";
import { UnitSelector } from "components/Selector/UnitSelector";
import { MaterialSelector } from "components/Selector/MaterialSelector";
import { DeviceSelector } from "components/Selector/DeviceSelector";
import { BMDTextArea } from "components/TextArea/BMDTextArea";
import { UploadFileStatus } from "antd/es/upload/interface";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";

const rules: Rule[] = [{ required: true, message: "Trường này là bắt buộc" }];

interface EditDailyLogPageProps {
  title: string;
  status: ModalStatus;
}

interface DailyLogForm extends DailyLog {
  fileAttachIds?: number[];
  images?: string[]; // Mảng chứa đường dẫn ảnh
  reportMemberShipId?: number;
}

function CreateOrUpdateDailyLogPage({
  title = "",
  status,
}: EditDailyLogPageProps) {
  const { haveEditPermission } = checkRoles(
    {
      edit: PermissionNames.activityLogEdit,
    },
    permissionStore.permissions
  );

  const [form] = Form.useForm<DailyLogForm>();
  const [loading, setLoading] = useState(false);
  const [selectedDailyLog, setSelectedDailyLog] = useState<DailyLog>();
  const [fileList, setFileList] = useState<FileAttach[]>([]);
  const navigate = useNavigate();
  const [memberShips, setMemberShips] = useState<any[]>([]);

  const [readonly, setReadonly] = useState(true);
  const [loadingFetch, setLoadingFetch] = useState(false);
  const params = useParams();
  const [searchParams, setSearchParams] = useSearchParams();
  const printRef = useRef<HTMLDivElement>(null);

  const [materials, setMaterials] = useState<any[]>([]);
  const [staffs, setStaffs] = useState<any[]>([]);
  const [machines, setMachines] = useState<any[]>([]);

  const [loadingMaterials, setLoadingMaterials] = useState(false);
  const [loadingStaffs, setLoadingStaffs] = useState(false);
  const [loadingMachines, setLoadingMachines] = useState(false);
  const [imageFileList, setImageFileList] = useState<UploadFile<any>[]>([]);
  const [duplicateMaterialIds, setDuplicateMaterialIds] = useState<number[]>(
    []
  );
  const [duplicateStaffIds, setDuplicateStaffIds] = useState<number[]>([]);
  const [duplicateMachineIds, setDuplicateMachineIds] = useState<number[]>([]);
  const [image, setImage] = useState<string | undefined>();

  // Thêm state quản lý danh sách công việc mẫu
  const [taskRows, setTaskRows] = useState<
    | {
        id: number;
        taskName: string;
        quantity: string;
        executed: string;
        accumulated: string;
        remaining: string;
        unit: string;
      }[]
    | []
  >([]);

  useEffect(() => {
    document.title = getTitle(title);

    if (status === "create") {
      setReadonly(false);

      if (!appStore.currentProject) {
        return;
      }
    }

    if (status == "update") {
      const dailyLogId = params.id;
      if (dailyLogId) {
        getOneDailyLog(+dailyLogId);
        setReadonly(searchParams.get("update") != "1");
      }
    } else {
      setReadonly(false);
    }

    const projectId = appStore.currentProject?.id;
    console.log("projectId lấy từ localStorage:", projectId);
  }, [haveEditPermission]);

  useEffect(() => {
    if (memberShips.length > 0 && selectedDailyLog) {
      setDataToForm(selectedDailyLog);
    }
  }, [memberShips, selectedDailyLog]);

  const checkDuplicateField = (data: any[], field: string): number[] => {
    const valueCount: Record<string, number> = {};
    data.forEach((item) => {
      if (item[field]) {
        valueCount[item[field]] = (valueCount[item[field]] || 0) + 1;
      }
    });
    return data
      .filter((item) => item[field] && valueCount[item[field]] > 1)
      .map((item) => item.id);
  };

  const setDataToForm = async (data: DailyLog) => {
    form.setFieldsValue({
      type: data.type,
      //@ts-ignore
      startAt: data.startAt ? dayjs.unix(data.startAt) : undefined,
      //@ts-ignore
      endAt: data.endAt ? dayjs.unix(data.endAt) : undefined,
      morningTemp: data.morningTemp,
      morningRain: data.morningRain,
      noonTemp: data.noonTemp,
      noonRain: data.noonRain,
      eveningTemp: data.eveningTemp,
      eveningRain: data.eveningRain,
      note: data.note,
      incidentWarning: data.incidentWarning,
      reportMemberShipId: data.reportMemberShip?.id,
    });

    // Set lại danh sách file ảnh (imageFileList) cho MultiImageUpload
    if (Array.isArray(data.fileAttaches)) {
      setImageFileList(
        data.fileAttaches.map((f, idx) => ({
          uid: `${f.id || idx}`,
          name: f.name || `Ảnh ${idx + 1}`,
          status: "done",
          url: f.url?.startsWith("http")
            ? f.url
            : import.meta.env.VITE_IMG_URL + f.url,
          id: f.id,
          response: { id: f.id },
        }))
      );
    } else {
      setImageFileList([]);
    }

    setMaterials(
      Array.isArray(data.dailyLogMaterialDetails)
        ? data.dailyLogMaterialDetails.map((item) => ({
            ...item,
            materialId: item.material?.id || "",
            materialObj: item.material,
            materialGroupId: item.materialGroup?.id || "",
            materialGroupName: item.materialGroup?.name || "",
            unitId: item.unit?.id || "",
            unitName: item.unit?.name || "",
          }))
        : []
    );

    setStaffs(
      Array.isArray(data.dailyLogStaffDetails)
        ? data.dailyLogStaffDetails.map((item) => ({
            ...item,
            unitId: item.unit?.id ?? "",
            unitName: item.unit?.name ?? "",
            unit: item.unit ?? item.unit ?? 0,
          }))
        : []
    );

    setMachines(
      Array.isArray(data.dailyLogMachineDetails)
        ? data.dailyLogMachineDetails.map((item) => ({
            ...item,
            deviceCategoryName: item.deviceCategory?.name ?? "",
            deviceCategoryId: item.deviceCategory?.id ?? "",
            deviceCategory: item.deviceCategory ?? item.deviceCategory ?? null, // giữ lại object gốc
            unitName: item.unit?.name ?? "",
            unitId: item.unit?.id ?? "",
            unit: item.unit ?? item.unit ?? 0,
            deviceId: item.device?.id ?? "",
            deviceObj: item.device ?? undefined,
          }))
        : []
    );
  };

  const getOneDailyLog = async (id: number) => {
    try {
      setLoadingFetch(true);
      const { data } = await dailyLogApi.findOne(id);
      console.log("DailyLog detail:", data); // Kiểm tra dữ liệu trả về

      if (isEmpty(data)) {
        navigate("/404");
        return;
      }

      setSelectedDailyLog(data);
      setDataToForm(data);

      return data as DailyLog;
    } catch (e: any) {
    } finally {
      setLoadingFetch(false);
    }
  };

  const getDataSubmit = async () => {
    // Chuẩn hóa dữ liệu chi tiết từ các bảng
    const dailyLogMaterialDetails = materials.map(
      ({ id, deviceName, unitName, materialGroupName, ...item }) => ({
        ...item,
        type: "MATERIAL",
        materialId: Number(item.materialId) || 0,
        unitId: Number(item.unitId) || 0,
        quantity: Number(item.quantity) || 0,
        deviceId: Number(item.deviceId) || 0,
        materialGroupId: Number(item.materialGroupId) || 0,
        deviceCategoryId: Number(item.deviceCategoryId) || 0,
        code: item.code || "",
        labor: item.labor || "",
        workHour: Number(item.workHour) || 0,
        percent: Number(item.percent) || 0,
        volume: Number(item.volume) || 0,
        taskId: Number(item.taskId) || 0,
        staffId: Number(item.staffId) || 0,
      })
    );

    const dailyLogMachineDetails = machines.map(
      ({ id, deviceCategoryName, unitName, ...item }) => ({
        ...item,
        type: "MACHINE",
        machineId: Number(item.machineId) || 0,
        unitId: Number(item.unitId) || 0,
        quantity: Number(item.quantity) || 0,
        workHour: Number(item.workHour) || 0,
        deviceId: Number(item.deviceId) || 0,
        materialGroupId: Number(item.materialGroupId) || 0,
        deviceCategoryId: Number(item.deviceCategoryId) || 0,
        code: item.code || "",
        labor: item.labor || "",
        percent: Number(item.percent) || 0,
        volume: Number(item.volume) || 0,
        taskId: Number(item.taskId) || 0,
        staffId: Number(item.staffId) || 0,
        materialId: Number(item.materialId) || 0,
      })
    );

    const dailyLogStaffDetails = staffs.map(({ id, ...item }) => ({
      ...item,
      type: "STAFF",
      staffId: Number(item.staffId) || 0,
      unitId: Number(item.unitId) || 0,
      quantity: Number(item.quantity) || 0,
      workHour: Number(item.workHour) || 0,
      materialGroupId: Number(item.materialGroupId) || 0,
      deviceCategoryId: Number(item.deviceCategoryId) || 0,
      code: item.code || "",
      labor: item.labor || "",
      percent: Number(item.percent) || 0,
      volume: Number(item.volume) || 0,
      taskId: Number(item.taskId) || 0,
      deviceId: Number(item.deviceId) || 0,
      materialId: Number(item.materialId) || 0,
    }));

    const values = form.getFieldsValue();
    return {
      fileAttachIds: values.fileAttachIds || [],
      reportMemberShipId:
        values.reportMemberShipId &&
        typeof values.reportMemberShipId === "object" &&
        "id" in values.reportMemberShipId
          ? (values.reportMemberShipId as { id: number }).id
          : values.reportMemberShipId || 0,
      dailyLogTaskDetails: [],
      dailyLogMachineDetails,
      dailyLogMaterialDetails,
      dailyLogStaffDetails,
      dailyLog: {
        type: values.type,
        startAt: values.startAt ? dayjs(values.startAt).unix() : 0,
        endAt: values.endAt ? dayjs(values.endAt).unix() : 0,
        morningTemp: Number(values.morningTemp) || 0,
        morningRain: values.morningRain || "",
        noonTemp: Number(values.noonTemp) || 0,
        noonRain: values.noonRain || "",
        eveningTemp: Number(values.eveningTemp) || 0,
        eveningRain: values.eveningRain || "",
        note: values.note || "",
        incidentWarning: values.incidentWarning || "",
      },
    };
  };

  const createData = async () => {
    const valid = await form.validateFields();

    setLoading(true);
    try {
      const res = await dailyLogApi.create(await getDataSubmit());

      message.success("Tạo nhật ký hoạt động thành công!");
      navigate(`/progress-management/${PermissionNames.activityLogList}`);
      setFileList([]);
    } finally {
      setLoading(false);
    }
  };

  const updateData = async () => {
    const valid = await form.validateFields();

    setLoading(true);
    try {
      const res = await dailyLogApi.update(
        selectedDailyLog!?.id || 0,
        await getDataSubmit()
      );
      setSelectedDailyLog({ ...selectedDailyLog, ...res.data });
      message.success("Chỉnh sửa nhật ký hoạt động thành công!");

      // Fetch lại data mới nhất
      if (selectedDailyLog?.id) {
        await getOneDailyLog(selectedDailyLog.id);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = () => {
    if (status == "create") {
      createData();
    } else {
      updateData();
    }
  };

  const pageTitle = useMemo(
    () => (status == "create" ? "Thêm nhật ký" : "Chỉnh sửa nhật ký"),
    [status]
  );

  const handleCellChange = (
    value: any,
    record: any,
    dataIndex: string,
    data: any[],
    setData: (data: any[]) => void,
    uniqueField: string,
    setDuplicateIds: (ids: number[]) => void
  ) => {
    // Kiểm tra trùng ID
    if (dataIndex === uniqueField) {
      const duplicates = data
        .filter((item) => item[uniqueField] === value && item.id !== record.id)
        .map((item) => item.id);
      if (duplicates.length > 0) {
        setDuplicateIds([record.id, ...duplicates]);
        message.error("ID đã tồn tại ở dòng khác!");
      } else {
        setDuplicateIds([]);
      }
    }
    const newData = [...data];
    const index = newData.findIndex((item) => item.id === record.id);
    if (index > -1) {
      newData[index][dataIndex] = value;
      setData(newData);
    }
  };
  // Hàm tạo row mới cho từng bảng
  const getNewMaterialRow = () => ({
    id: Date.now(),
    code: "",
    materialId: "",
    materialGroupId: "",
    materialGroupName: "",
    quantity: "",
    unitId: "",
    unitName: "",
  });

  const getNewStaffRow = () => ({
    id: Date.now(),
    code: "",
    labor: "",
    workHour: "",
    quantity: "",
    materialGroupId: "",
    deviceCategoryId: "",
    unitId: "",
  });

  const getNewMachineRow = () => ({
    id: Date.now(),
    code: "",
    deviceId: "",
    deviceCategoryId: "",
    deviceCategoryName: "",
    quantity: "",
    unitId: "",
    unitName: "",
  });

  const materialColumns: CustomizableColumn<any>[] = [
    {
      key: "code",
      title: "ID",
      width: 100,
      dataIndex: "code",
      render: (text, record) => (
        <Form.Item
          validateStatus={
            duplicateMaterialIds.includes(record.id) ? "error" : undefined
          }
          help={
            duplicateMaterialIds.includes(record.id)
              ? "ID vật liệu đã tồn tại ở dòng khác!"
              : undefined
          }
          style={{ marginBottom: 0 }}
        >
          <Input
            value={record.code}
            onChange={(e) =>
              handleCellChange(
                e.target.value,
                record,
                "code",
                materials,
                setMaterials,
                "code",
                setDuplicateMaterialIds
              )
            }
            disabled={readonly}
            size="small"
            style={
              duplicateMaterialIds.includes(record.id)
                ? { borderColor: "#ff4d4f" }
                : {}
            }
          />
        </Form.Item>
      ),
    },
    {
      key: "materialId",
      title: "Nguyên vật liệu",
      dataIndex: "materialId",
      width: 200,
      defaultVisible: true,
      alwaysVisible: true,
      render: (text, record) => (
        <MaterialSelector
          value={record.materialId ?? ""} // Chỉ truyền id
          valueIsOption={true}
          placeholder="Chọn nguyên vật liệu"
          initQuery={{ isActive: true }}
          initOptionItem={record.materialObj} // Truyền object option để hiển thị label
          onChange={(selectedMaterial) => {
            // Lưu id vào record.materialId
            handleCellChange(
              selectedMaterial?.id ?? "",
              record,
              "materialId",
              materials,
              setMaterials,
              "materialId",
              setDuplicateMaterialIds
            );
            // Lưu object vào record.materialObj để hiển thị label
            handleCellChange(
              selectedMaterial ?? "",
              record,
              "materialObj",
              materials,
              setMaterials,
              "materialObj",
              setDuplicateMaterialIds
            );
            // Gán id nhóm hàng để submit
            handleCellChange(
              selectedMaterial?.materialGroup?.id ?? "",
              record,
              "materialGroupId",
              materials,
              setMaterials,
              "materialGroupId",
              setDuplicateMaterialIds
            );
            // Gán tên nhóm hàng để hiển thị
            handleCellChange(
              selectedMaterial?.materialGroup?.name ?? "",
              record,
              "materialGroupName",
              materials,
              setMaterials,
              "materialGroupName",
              setDuplicateMaterialIds
            );
            // Gán đơn vị tính (unit id và name)
            handleCellChange(
              selectedMaterial?.unit?.id ?? "",
              record,
              "unitId",
              materials,
              setMaterials,
              "unitId",
              setDuplicateMaterialIds
            );
            handleCellChange(
              selectedMaterial?.unit?.name ?? "",
              record,
              "unitName",
              materials,
              setMaterials,
              "unitName",
              setDuplicateMaterialIds
            );
          }}
          disabled={readonly}
        />
      ),
    },
    {
      key: "materialGroupName",
      title: "Nhóm hàng",
      dataIndex: "materialGroupName",
      width: 140,
      defaultVisible: true,
      alwaysVisible: true,
      render: (text, record) => (
        <Input
          value={record.materialGroupName || ""}
          disabled
          style={{ width: "100%" }}
          placeholder="Nhóm hàng"
        />
      ),
    },
    {
      key: "quantity",
      title: "Số lượng",
      dataIndex: "quantity",
      width: 140,
      defaultVisible: true,
      alwaysVisible: true,
      render: (text, record) => (
        <Input
          type="number"
          value={record.quantity}
          onChange={(e) =>
            handleCellChange(
              e.target.value,
              record,
              "quantity",
              materials,
              setMaterials,
              "quantity",
              setDuplicateMaterialIds
            )
          }
          disabled={readonly}
          min={0}
          style={{ width: "100%" }}
        />
      ),
    },
    {
      key: "unitName",
      title: "Đơn vị tính",
      dataIndex: "unitName",
      width: 140,
      defaultVisible: true,
      alwaysVisible: true,
      render: (text, record) => (
        <Input
          value={record.unitName || ""}
          disabled
          style={{ width: "100%" }}
          placeholder="Đơn vị tính"
        />
      ),
    },
  ];

  const staffColumns: CustomizableColumn<any>[] = [
    {
      key: "code",
      title: "ID",
      width: 100,
      dataIndex: "code",
      render: (text, record) => (
        <Form.Item
          validateStatus={
            duplicateStaffIds.includes(record.id) ? "error" : undefined
          }
          help={
            duplicateStaffIds.includes(record.id)
              ? "ID nhân công đã tồn tại ở dòng khác!"
              : undefined
          }
          style={{ marginBottom: 0 }}
        >
          <Input
            value={record.code}
            onChange={(e) =>
              handleCellChange(
                e.target.value,
                record,
                "code",
                staffs,
                setStaffs,
                "code",
                setDuplicateStaffIds
              )
            }
            disabled={readonly}
            size="small"
            style={
              duplicateStaffIds.includes(record.id)
                ? { borderColor: "#ff4d4f" }
                : {}
            }
          />
        </Form.Item>
      ),
    },
    {
      key: "labor",
      title: "Nhân công",
      dataIndex: "labor",
      width: 200,
      defaultVisible: true,
      alwaysVisible: true,
    },
    {
      key: "workHour",
      title: "Giờ làm",
      dataIndex: "workHour",
      width: 140,
      defaultVisible: true,
      alwaysVisible: true,
      render: (text, record) => (
        <Input
          type="number"
          value={record.workHour}
          onChange={(e) =>
            handleCellChange(
              e.target.value,
              record,
              "workHour",
              staffs,
              setStaffs,
              "workHour",
              setDuplicateStaffIds
            )
          }
          disabled={readonly}
          min={0}
          style={{ width: "100%" }}
        />
      ),
    },
    {
      key: "quantity",
      title: "Số lượng",
      dataIndex: "quantity",
      width: 140,
      defaultVisible: true,
      alwaysVisible: true,
      render: (text, record) => (
        <Input
          type="number"
          value={record.quantity}
          onChange={(e) =>
            handleCellChange(
              e.target.value,
              record,
              "quantity",
              staffs,
              setStaffs,
              "quantity",
              setDuplicateStaffIds
            )
          }
          disabled={readonly}
          min={0}
          style={{ width: "100%" }}
        />
      ),
    },
    {
      key: "unitId",
      title: "Đơn vị tính",
      dataIndex: "unitId",
      width: 140,
      defaultVisible: true,
      alwaysVisible: true,
      render: (text, record) => (
        <UnitSelector
          value={record.unitId}
          placeholder="Đơn vị"
          style={{ width: 100 }}
          dropdownStyle={{ minWidth: 120 }}
          onChange={(value) => {
            handleCellChange(
              value?.id || value,
              record,
              "unitId",
              staffs,
              setStaffs,
              "unitId",
              setDuplicateStaffIds
            );
          }}
          disabled={readonly}
        />
      ),
    },
  ];

  const machineColumns: CustomizableColumn<any>[] = [
    {
      key: "code",
      title: "ID",
      width: 100,
      dataIndex: "code",
      render: (text, record) => (
        <Form.Item
          validateStatus={
            duplicateMachineIds.includes(record.id) ? "error" : undefined
          }
          help={
            duplicateMachineIds.includes(record.id)
              ? "ID máy đã tồn tại ở dòng khác!"
              : undefined
          }
          style={{ marginBottom: 0 }}
        >
          <Input
            value={record.code}
            onChange={(e) =>
              handleCellChange(
                e.target.value,
                record,
                "code",
                machines,
                setMachines,
                "code",
                setDuplicateMachineIds
              )
            }
            disabled={readonly}
            size="small"
            style={
              duplicateMachineIds.includes(record.id)
                ? { borderColor: "#ff4d4f" }
                : {}
            }
          />
        </Form.Item>
      ),
    },
    {
      key: "deviceId",
      title: "Máy thi công",
      dataIndex: "deviceId",
      width: 200,
      defaultVisible: true,
      alwaysVisible: true,
      render: (text, record) => (
        <DeviceSelector
          value={record.deviceId ?? ""} // Chỉ truyền id
          valueIsOption={true}
          placeholder="Chọn máy thi công"
          initQuery={{ isActive: true }}
          initOptionItem={record.deviceObj} // Truyền object option để hiển thị label
          onChange={(device) => {
            // Lưu id vào record.deviceId
            handleCellChange(
              device?.id ?? "",
              record,
              "deviceId",
              machines,
              setMachines,
              "deviceId",
              setDuplicateMachineIds
            );
            // Lưu object vào record.deviceObj để hiển thị label
            handleCellChange(
              device ?? "",
              record,
              "deviceObj",
              machines,
              setMachines,
              "deviceObj",
              setDuplicateMachineIds
            );
            // Lưu id nhóm máy thi công để submit
            handleCellChange(
              device?.deviceCategory?.id ?? "",
              record,
              "deviceCategoryId",
              machines,
              setMachines,
              "deviceCategoryId",
              setDuplicateMachineIds
            );
            // Lưu tên nhóm máy để hiển thị
            handleCellChange(
              device?.deviceCategory?.name ?? "",
              record,
              "deviceCategoryName",
              machines,
              setMachines,
              "deviceCategoryName",
              setDuplicateMachineIds
            );
            // Lưu đơn vị tính (id và name)
            handleCellChange(
              device?.unit?.id ?? "",
              record,
              "unitId",
              machines,
              setMachines,
              "unitId",
              setDuplicateMachineIds
            );
            handleCellChange(
              device?.unit?.name ?? "",
              record,
              "unitName",
              machines,
              setMachines,
              "unitName",
              setDuplicateMachineIds
            );
          }}
          disabled={readonly}
        />
      ),
    },
    {
      key: "deviceCategoryName",
      title: "Nhóm máy thi công",
      dataIndex: "deviceCategoryName",
      width: 140,
      defaultVisible: true,
      alwaysVisible: true,
      render: (text, record) => (
        <Input
          value={record.deviceCategoryName || ""}
          disabled
          style={{ width: "100%" }}
          placeholder="Nhóm máy thi công"
        />
      ),
    },
    {
      key: "quantity",
      title: "Số lượng",
      dataIndex: "quantity",
      width: 140,
      defaultVisible: true,
      alwaysVisible: true,
      render: (text, record) => (
        <Input
          type="number"
          value={record.quantity}
          onChange={(e) =>
            handleCellChange(
              e.target.value,
              record,
              "quantity",
              machines,
              setMachines,
              "quantity",
              setDuplicateMachineIds
            )
          }
          disabled={readonly}
          min={0}
          style={{ width: "100%" }}
        />
      ),
    },
    {
      key: "unitName",
      title: "Đơn vị tính",
      dataIndex: "unitName",
      width: 140,
      defaultVisible: true,
      alwaysVisible: true,
      render: (text, record) => (
        <Input
          value={record.unitName || ""}
          disabled
          style={{ width: "100%" }}
          placeholder="Đơn vị tính"
        />
      ),
    },
  ];

  // Hàm xử lý khi bấm nút "Chọn"
  const handleSelectTaskTemplate = () => {
    // Giả lập dữ liệu, sau này lấy từ API hoặc form
    setTaskRows([
      {
        id: Date.now(),
        taskName: "Công việc",
        quantity: "",
        executed: "",
        accumulated: "",
        remaining: "",
        unit: "",
      },
      {
        id: Date.now() + 1,
        taskName: "Công việc",
        quantity: "",
        executed: "",
        accumulated: "",
        remaining: "",
        unit: "",
      },
      {
        id: Date.now() + 2,
        taskName: "Công việc",
        quantity: "",
        executed: "",
        accumulated: "",
        remaining: "",
        unit: "",
      },
    ]);
  };

  // Hàm xóa dòng
  const handleDeleteTaskRow = (id: number) => {
    Modal.confirm({
      title: "Xác nhận xóa công việc",
      icon: null,
      content: (
        <div>
          Bạn sẽ không thể khôi phục dữ liệu này sau khi xóa.
          <br />
          Bạn có chắc chắn muốn xóa dòng này?
        </div>
      ),
      footer: (_, { OkBtn, CancelBtn }) => (
        <>
          <CustomButton
            variant="outline"
            className="cta-button"
            onClick={() => {
              setTaskRows((prev) => prev.filter((row) => row.id !== id));
              Modal.destroyAll();
            }}
          >
            Có
          </CustomButton>
          <CustomButton
            onClick={() => {
              Modal.destroyAll();
            }}
            className="cta-button"
          >
            Không
          </CustomButton>
        </>
      ),
    });
  };

  return (
    <div className="app-container">
      <PageTitle
        back
        breadcrumbs={[
          { label: "Tiến độ" },
          {
            label: "Nhật ký hoạt động",
            href: `/progress-management/${PermissionNames.activityLogList}`,
          },
          { label: pageTitle },
        ]}
        title={pageTitle}
      />

      <Spin spinning={loadingFetch}>
        <Form
          form={form}
          layout="vertical"
          disabled={readonly}
          className={clsx(readonly ? "readonly" : "")}
          onFinish={handleSubmit}
          initialValues={{
            createdDate: dayjs(),
          }}
        >
          <Row gutter={24}>
            <Col span={24}>
              <Card className="content-card ">
                <Card title="Nhật ký hoạt động" className="mb-0 form-card">
                  <Row gutter={16}>
                    <Col span={6}>
                      <Form.Item name="type" label="Loại" rules={rules}>
                        <Select
                          placeholder="Chọn loại"
                          disabled={status === "update"}
                          allowClear
                        >
                          {DailyLogTypeOptions.map((opt) => (
                            <Select.Option key={opt.value} value={opt.value}>
                              {opt.label}
                            </Select.Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>

                    <Col span={6}>
                      <Form.Item
                        name="startAt"
                        label="Ngày bắt đầu"
                        rules={[
                          ...rules,
                          ({ getFieldValue }) => ({
                            validator(_, value) {
                              const endAt = getFieldValue("endAt");
                              if (!value || !endAt) return Promise.resolve();
                              if (dayjs(value).isAfter(dayjs(endAt), "day")) {
                                return Promise.reject(
                                  new Error(
                                    "Ngày bắt đầu phải trước hoặc bằng ngày kết thúc!"
                                  )
                                );
                              }
                              return Promise.resolve();
                            },
                          }),
                        ]}
                      >
                        <DatePicker
                          allowClear={false}
                          placeholder="Ngày bắt đầu"
                          format={settings.dateFormat}
                          className="w-full"
                          onChange={() => {
                            form.validateFields(["endAt", "startAt"]);
                          }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item
                        name="endAt"
                        label="Ngày kết thúc"
                        rules={[
                          ...rules,
                          ({ getFieldValue }) => ({
                            validator(_, value) {
                              const startAt = getFieldValue("startAt");
                              if (!value || !startAt) return Promise.resolve();
                              if (
                                dayjs(value).isBefore(dayjs(startAt), "day")
                              ) {
                                return Promise.reject(
                                  new Error(
                                    "Ngày kết thúc phải sau hoặc bằng ngày bắt đầu!"
                                  )
                                );
                              }
                              return Promise.resolve();
                            },
                          }),
                        ]}
                      >
                        <DatePicker
                          allowClear={false}
                          format={settings.dateFormat}
                          className="w-full"
                          onChange={() => {
                            form.validateFields(["endAt", "startAt"]);
                          }}
                        />
                      </Form.Item>
                    </Col>

                    <Col span={6}>
                      <Form.Item
                        name="reportMemberShipId"
                        label="Người báo cáo"
                        rules={rules}
                      >
                        <MembershipSelector placeholder="Người báo cáo" />
                      </Form.Item>
                    </Col>
                  </Row>
                </Card>

                <Card
                  title="Chọn công việc"
                  className="mb-0 form-card mt-[16px]"
                >
                  <Row gutter={8}>
                    <Col flex="auto">
                      <Form.Item
                        name="taskTemplateId"
                        style={{ marginBottom: 0 }}
                      >
                        <TaskTemplateSelector placeholder="Chọn công việc" />
                      </Form.Item>
                    </Col>
                    <Col>
                      <Button
                        type="primary"
                        onClick={handleSelectTaskTemplate}
                        style={{ height: 35 }}
                      >
                        Chọn
                      </Button>
                    </Col>
                  </Row>

                  {/* UI danh sách công việc mẫu */}
                  {taskRows.length > 0 && (
                    <div style={{ marginTop: 16 }}>
                      {/* Hàng dữ liệu */}
                      {taskRows.map((row) => (
                        <Row
                          gutter={8}
                          align="middle"
                          style={{ marginBottom: 8, width: "100%" }}
                          key={row.id}
                        >
                          <Col span={1}>
                            <Button
                              type="text"
                              icon={<DeleteIcon />}
                              onClick={() => handleDeleteTaskRow(row.id)}
                              style={{
                                padding: 0,
                                boxShadow: "none",
                                border: "none",
                                background: "none",
                                color: "#ff4d4f",
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                              }}
                            />
                          </Col>
                          <Col span={4}>
                            <Form.Item
                              label="Công việc"
                              style={{ marginBottom: 0 }}
                            >
                              <Input value={row.taskName} disabled />
                            </Form.Item>
                          </Col>
                          <Col span={4}>
                            <Form.Item
                              label="Khối lượng"
                              style={{ marginBottom: 0 }}
                            >
                              <Input value={row.quantity} disabled />
                            </Form.Item>
                          </Col>
                          <Col span={4}>
                            <Form.Item
                              label="Khối lượng thi công"
                              style={{ marginBottom: 0 }}
                            >
                              <Input
                                value={row.executed}
                                onChange={(e) => {
                                  const newRows = taskRows.map((r) =>
                                    r.id === row.id
                                      ? { ...r, executed: e.target.value }
                                      : r
                                  );
                                  setTaskRows(newRows);
                                }}
                              />
                            </Form.Item>
                          </Col>
                          <Col span={3}>
                            <Form.Item
                              label="Tích lũy"
                              style={{ marginBottom: 0 }}
                            >
                              <Input value={row.accumulated} disabled />
                            </Form.Item>
                          </Col>
                          <Col span={3}>
                            <Form.Item
                              label="Còn lại"
                              style={{ marginBottom: 0 }}
                            >
                              <Input value={row.remaining} disabled />
                            </Form.Item>
                          </Col>
                          <Col span={5}>
                            <Form.Item
                              label="Đơn vị tính"
                              style={{ marginBottom: 0 }}
                            >
                              <Input value={row.unit} disabled />
                            </Form.Item>
                          </Col>
                        </Row>
                      ))}
                    </div>
                  )}
                </Card>

                <Collapse
                  className="mb-0 form-card mt-[16px]"
                  defaultActiveKey={["1"]}
                >
                  <Collapse.Panel
                    header="Nguyên vật liệu"
                    key="1"
                    extra={
                      <Tooltip
                        title={
                          readonly ? "Bạn không thể thêm mới" : "Thêm dòng"
                        }
                      >
                        <PlusOutlined
                          style={{
                            fontSize: 18,
                            color: readonly ? "#ccc" : "#1677ff",
                            cursor: readonly ? "not-allowed" : "pointer",
                          }}
                          onClick={(e) => {
                            e.stopPropagation();
                            if (!readonly)
                              setMaterials((prev) => [
                                ...prev,
                                getNewMaterialRow(),
                              ]);
                          }}
                        />
                      </Tooltip>
                    }
                  >
                    <DailyLogDetailTable
                      title="Nguyên vật liệu"
                      columns={materialColumns}
                      data={materials}
                      setData={setMaterials}
                      readonly={readonly}
                      loading={loadingMaterials}
                      getNewRow={getNewMaterialRow}
                      onAfterDeleteRow={(newData) => {
                        setDuplicateMaterialIds(
                          checkDuplicateField(newData, "code")
                        );
                      }}
                    />
                  </Collapse.Panel>
                </Collapse>

                <Collapse
                  className="mb-0 form-card mt-[16px]"
                  defaultActiveKey={["1"]}
                >
                  <Collapse.Panel
                    header="Nhân công"
                    key="1"
                    extra={
                      <Tooltip
                        title={
                          readonly ? "Bạn không thể thêm mới" : "Thêm dòng"
                        }
                      >
                        <PlusOutlined
                          style={{
                            fontSize: 18,
                            color: readonly ? "#ccc" : "#1677ff",
                            cursor: readonly ? "not-allowed" : "pointer",
                          }}
                          onClick={(e) => {
                            e.stopPropagation();
                            if (!readonly)
                              setStaffs((prev) => [...prev, getNewStaffRow()]);
                          }}
                        />
                      </Tooltip>
                    }
                  >
                    <DailyLogDetailTable
                      title="Nhân công"
                      columns={staffColumns}
                      data={staffs}
                      setData={setStaffs}
                      readonly={readonly}
                      loading={loadingStaffs}
                      getNewRow={getNewStaffRow}
                      onAfterDeleteRow={(newData) => {
                        setDuplicateStaffIds(
                          checkDuplicateField(newData, "code")
                        );
                      }}
                    />
                  </Collapse.Panel>
                </Collapse>

                <Collapse
                  className="mb-0 form-card mt-[16px]"
                  defaultActiveKey={["1"]}
                >
                  <Collapse.Panel
                    header="Máy thi công"
                    key="1"
                    extra={
                      <Tooltip
                        title={
                          readonly ? "Bạn không thể thêm mới" : "Thêm dòng"
                        }
                      >
                        <PlusOutlined
                          style={{
                            fontSize: 18,
                            color: readonly ? "#ccc" : "#1677ff",
                            cursor: readonly ? "not-allowed" : "pointer",
                          }}
                          onClick={(e) => {
                            e.stopPropagation();
                            if (!readonly)
                              setMachines((prev) => [
                                ...prev,
                                getNewMachineRow(),
                              ]);
                          }}
                        />
                      </Tooltip>
                    }
                  >
                    <DailyLogDetailTable
                      title="Máy thi công"
                      columns={machineColumns}
                      data={machines}
                      setData={setMachines}
                      readonly={readonly}
                      loading={loadingMachines}
                      getNewRow={getNewMachineRow}
                      onAfterDeleteRow={(newData) => {
                        setDuplicateMachineIds(
                          checkDuplicateField(newData, "code")
                        );
                      }}
                    />
                  </Collapse.Panel>
                </Collapse>

                <Card title="Thời tiết" className="mb-0 form-card mt-[16px]">
                  <Row>
                    <Col span={24}>
                      <Row align="middle" gutter={24}>
                        <Col
                          span={4}
                          style={{
                            color: "#19345b",
                            fontWeight: 600,
                            fontSize: 16,
                          }}
                        >
                          Sáng
                        </Col>
                        <Col span={10}>
                          <Form.Item
                            name="morningTemp"
                            label="Nhiệt độ"
                            rules={[
                              {
                                required: true,
                                message: "Nhập nhiệt độ buổi sáng",
                              },
                            ]}
                            labelCol={{ span: 24 }}
                            wrapperCol={{ span: 24 }}
                          >
                            <Input
                              type="number"
                              placeholder="VD: 30"
                              suffix={
                                <span
                                  style={{ color: "#888", fontWeight: 500 }}
                                >
                                  °C
                                </span>
                              }
                            />
                          </Form.Item>
                        </Col>
                        <Col span={10}>
                          <Form.Item
                            name="morningRain"
                            label="Lượng mưa"
                            rules={[
                              {
                                required: true,
                                message: "Nhập lượng mưa buổi sáng",
                              },
                            ]}
                            labelCol={{ span: 24 }}
                            wrapperCol={{ span: 24 }}
                          >
                            <Input placeholder="VD: 50.0mm/12 giờ" />
                          </Form.Item>
                        </Col>
                      </Row>
                      <Row align="middle" gutter={24}>
                        <Col
                          span={4}
                          style={{
                            color: "#19345b",
                            fontWeight: 600,
                            fontSize: 16,
                          }}
                        >
                          Trưa
                        </Col>
                        <Col span={10}>
                          <Form.Item
                            name="noonTemp"
                            label="Nhiệt độ"
                            rules={[
                              {
                                required: true,
                                message: "Nhập nhiệt độ buổi trưa",
                              },
                            ]}
                            labelCol={{ span: 24 }}
                            wrapperCol={{ span: 24 }}
                          >
                            <Input
                              type="number"
                              placeholder="VD: 30"
                              suffix={
                                <span
                                  style={{ color: "#888", fontWeight: 500 }}
                                >
                                  °C
                                </span>
                              }
                            />
                          </Form.Item>
                        </Col>
                        <Col span={10}>
                          <Form.Item
                            name="noonRain"
                            label="Lượng mưa"
                            rules={[
                              {
                                required: true,
                                message: "Nhập lượng mưa buổi trưa",
                              },
                            ]}
                            labelCol={{ span: 24 }}
                            wrapperCol={{ span: 24 }}
                          >
                            <Input placeholder="VD: 50.0mm/12 giờ" />
                          </Form.Item>
                        </Col>
                      </Row>
                      <Row align="middle" gutter={24}>
                        <Col
                          span={4}
                          style={{
                            color: "#19345b",
                            fontWeight: 600,
                            fontSize: 16,
                          }}
                        >
                          Tối
                        </Col>
                        <Col span={10}>
                          <Form.Item
                            name="eveningTemp"
                            label="Nhiệt độ"
                            rules={[
                              {
                                required: true,
                                message: "Nhập nhiệt độ buổi tối",
                              },
                            ]}
                            labelCol={{ span: 24 }}
                            wrapperCol={{ span: 24 }}
                          >
                            <Input
                              type="number"
                              placeholder="VD: 30"
                              suffix={
                                <span
                                  style={{ color: "#888", fontWeight: 500 }}
                                >
                                  °C
                                </span>
                              }
                            />
                          </Form.Item>
                        </Col>
                        <Col span={10}>
                          <Form.Item
                            name="eveningRain"
                            label="Lượng mưa"
                            rules={[
                              {
                                required: true,
                                message: "Nhập lượng mưa buổi tối",
                              },
                            ]}
                            labelCol={{ span: 24 }}
                            wrapperCol={{ span: 24 }}
                          >
                            <Input placeholder="VD: 50.0mm/12 giờ" />
                          </Form.Item>
                        </Col>
                      </Row>
                    </Col>
                  </Row>
                </Card>

                <Row gutter={16} className="mt-[16px]">
                  {/* First Row */}
                  <Col span={24}>
                    <Form.Item name="note" label="Ghi chú">
                      <BMDTextArea placeholder="Nhập ghi chú" />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16} className="mt-[16px]">
                  {/* First Row */}
                  <Col span={24}>
                    <Form.Item name="incidentWarning" label="Cảnh báo sự cố">
                      <BMDTextArea placeholder="Nhập cảnh báo sự cố" />
                    </Form.Item>
                  </Col>
                </Row>

                <Card title="Hình ảnh" className="mb-0 form-card mt-[16px]">
                  <MultiImageUpload
                    fileListProp={imageFileList}
                    onUploadOk={(urls) => {
                      const validUrls = urls.filter(Boolean);
                      setImageFileList(
                        validUrls.map((url, idx) => ({
                          uid: `${Date.now()}-${idx}`,
                          name: `Ảnh ${idx + 1}`,
                          status: "done",
                          url,
                          id: undefined,
                          response: {},
                        }))
                      );
                      form.setFieldsValue({ images: validUrls });
                      console.log("Uploaded images:", validUrls);
                    }}
                    recommendSize={{ width: 1920, height: 1080 }}
                  />
                </Card>

                {/* Action Buttons */}
                <div
                  className="mt-[16px]"
                  style={{
                    display: "flex",
                    justifyContent: "flex-end",
                    gap: "12px",
                  }}
                >
                  {!readonly && (
                    <CustomButton
                      variant="outline"
                      className="cta-button"
                      onClick={() => {
                        if (status == "create") {
                          navigate(
                            `/progress-management/${PermissionNames.activityLogList}`
                          );
                        } else {
                          setDataToForm(selectedDailyLog!);
                          setReadonly(true);
                        }
                      }}
                    >
                      Hủy
                    </CustomButton>
                  )}
                  <CustomButton
                    className="cta-button"
                    loading={loading}
                    onClick={() => {
                      if (!readonly) {
                        handleSubmit();
                      } else {
                        setReadonly(false);
                      }
                    }}
                    disabled={status == "update" && !haveEditPermission}
                  >
                    {status == "create"
                      ? "Lưu"
                      : readonly
                      ? "Chỉnh sửa"
                      : "Lưu chỉnh sửa"}
                  </CustomButton>
                </div>
              </Card>
            </Col>
          </Row>
        </Form>
      </Spin>
    </div>
  );
}

export default observer(CreateOrUpdateDailyLogPage);
