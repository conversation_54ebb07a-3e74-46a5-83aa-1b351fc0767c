import { Select } from "antd";
import { DefaultOptionType } from "antd/es/select";
import { SelectProps } from "antd/lib";
import { useColor } from "hooks/useColor";
import { useMaterialGroup } from "hooks/useMaterialGroup";
import { debounce, uniqBy } from "lodash";
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
} from "react";
import { MaterialGroup } from "types/materialGroup";
import { QueryParams2 } from "types/query";

type CustomFormItemProps = {
  value?: number;
  initQuery?: QueryParams2;
  disabled?: boolean;
  selectedColor?: MaterialGroup[];
  multiple?: boolean;
  onChange?: (value: any, materialGroups: MaterialGroup[]) => void;
  selectProps?: SelectProps;
  initOptionItem?: MaterialGroup | MaterialGroup[];
  valueIsOption?: boolean;
  allowClear?: boolean;
  placeholder?: string;
  materialTypeId?: number;
  allowSelectAll?: boolean | { enable: boolean; [key: string]: any };
};

export interface MaterialGroupSelector {
  refresh(): void;
}

/**
 * A demo that changes it's number value on click.
 * @param value initialValue passed by parent Form.Item.
 * @param onChange a callback for Form.Item to read its child component's value.
 * @constructor
 */
export const MaterialGroupSelector = forwardRef(
  (
    {
      value,
      onChange,
      initQuery,
      disabled,
      multiple = false,
      selectedColor,
      initOptionItem,
      valueIsOption,
      selectProps,
      allowClear = true,
      placeholder = "Chọn nhóm material",
      materialTypeId,
      allowSelectAll,
    }: CustomFormItemProps,
    ref
  ) => {
    const { materialGroups, total, loading, fetchData, query } =
      useMaterialGroup({
        initQuery: {
          page: 1,
          limit: 50,
          ...initQuery,
        },
      });

    useImperativeHandle<any, MaterialGroupSelector>(
      ref,
      () => ({
        refresh() {
          fetchData();
        },
      }),
      []
    );

    useEffect(() => {
      query.materialTypeId = materialTypeId;
      fetchData();
    }, [materialTypeId]);

    const debounceSearch = useCallback(
      debounce((keyword) => {
        query.search = keyword;
        fetchData();
      }, 300),
      [query]
    );

    const options = useMemo(() => {
      let data = [...materialGroups];
      if (initOptionItem) {
        if ((initOptionItem as MaterialGroup[])?.length) {
          data = data.concat(initOptionItem);
        } else {
          data.push(initOptionItem as MaterialGroup);
        }
      }
      if (
        (typeof allowSelectAll == "object" && allowSelectAll.enable) ||
        allowSelectAll
      ) {
        if (data.length > 0 && materialTypeId) {
          data = [
            //@ts-ignore
            { id: -1, name: "All " + allowSelectAll.materialTypeName },
            ...data,
          ];
        } else {
          data = [];
        }
      }

      return uniqBy(data, (data) => data.id);
    }, [materialGroups, initOptionItem, allowSelectAll]);

    return (
      <Select<any, DefaultOptionType>
        mode={multiple ? "multiple" : undefined}
        disabled={disabled}
        loading={loading}
        style={{ width: "100%", minWidth: 200 }}
        allowClear={allowClear}
        onSearch={debounceSearch}
        showSearch
        placeholder={placeholder}
        filterOption={false}
        value={value}
        onChange={(v, opts) => {
          if (v === undefined || v.length == 0) {
            debounceSearch(v);
          }
          if (valueIsOption) {
            if (opts instanceof Array) {
              onChange?.(
                opts?.map((v) => v.item),
                materialGroups
              );
            } else {
              onChange?.(opts?.item, materialGroups);
            }
          } else {
            onChange?.(v, materialGroups);
          }
        }}
        {...selectProps}
      >
        {options?.map((item) => (
          <Select.Option item={item} value={item.id} key={item.id}>
            <div className="flex items-center gap-2">
              <span>{item.name}</span>
            </div>
          </Select.Option>
        ))}
      </Select>
    );
  }
);
