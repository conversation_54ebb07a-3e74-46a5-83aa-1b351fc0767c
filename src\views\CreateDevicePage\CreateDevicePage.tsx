import {
  <PERSON><PERSON>,
  <PERSON>,
  Col,
  DatePicker,
  Divider,
  Form,
  Input,
  message,
  Modal,
  Row,
  Select,
} from "antd";
import TextArea from "antd/es/input/TextArea";
import { Rule } from "antd/lib/form";
import { deviceApi } from "api/device.api";
import { providerApi } from "api/provider.api";
import { DeviceCategorySelector } from "components/Selector/DeviceCategorySelector";
import { ProjectSelector } from "components/Selector/ProjectSelector";
import { ProviderSelector } from "components/Selector/ProviderSelector";
import { UnitSelector } from "components/Selector/UnitSelector";
import { FileUploadMultiple } from "components/Upload/FileUploadMultiple";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { useNavigate } from "react-router-dom";
import { DeviceType, ProductOriginTrans } from "types/device";
import { FileAttach, FileAttachType } from "types/fileAttach";
import { ModalStatus } from "types/modal";
import { Provider, ProviderTypeTrans } from "types/provider";
import { getTitle } from "utils";
import UploadImg from "assets/images/upload-img.png";
import { FileAttachPayload } from "components/Upload/FileUploadItem";
import { $url } from "utils/url";
import { UploadFile } from "antd/lib";
import { fileAttachApi } from "api/fileAttach.api";
import { useWatch } from "antd/es/form/Form";
import { settings } from "settings";
import dayjs from "dayjs";
import { DepartmentSelector } from "components/Selector/DepartmentSelector";
import { StaffSelector } from "components/Selector/StaffSelector";
import { departmentApi } from "api/department.api";
import { BrandSelector } from "components/Selector/BrandSelector";
import CustomInput from "components/Input/CustomInput";
import CustomDatePicker from "components/Input/CustomDatePicker";
import CustomButton from "components/Button/CustomButton";
import CustomSelect from "components/Input/CustomSelect";
import { DictionarySelector } from "components/Selector/DictionarySelector";
import { DictionaryType } from "types/dictionary";

const rules: Rule[] = [{ required: true }];

export const CreateDevicePage = ({
  title = "",
  type = DeviceType.Equipment,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  useEffect(() => {
    document.title = getTitle(title);
  }, []);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const avatar = useWatch("avatar", form);
  const createData = async () => {
    const valid = await form.validateFields();

    const {
      purchaseAt,
      useAt,
      files,
      unitId,
      deviceCategoryId,
      projectId,
      staffId,
      departmentId,
      brandId,
      ...data
    } = form.getFieldsValue();

    const payload = {
      device: {
        ...data,
        type,
        files: typeof files === "string" ? files : JSON.stringify(files),
        purchaseAt: purchaseAt ? purchaseAt?.startOf("day")?.unix() : undefined,
        useAt: useAt ? useAt?.startOf("day")?.unix() : undefined,
      },
      unitId,
      deviceCategoryId,
      staffId,
      projectId,
      departmentId,
      brandId,
    };
    // let fileAttachIds: number[] = [];
    // if (fileAttachList && fileAttachList.length > 0) {
    //   const results = await Promise.allSettled(
    //     fileAttachList.map((file: FileAttachPayload) =>
    //       fileAttachApi.create({
    //         fileAttach: {
    //           name: file.name,
    //           type: file.type,
    //           url: file.url,
    //           path: file.path,
    //           size: file.size,
    //         },
    //       })
    //     )
    //   );
    //   //@ts-ignore
    //   fileAttachIds = results.map((result) => result.value?.data?.id);
    //   if (fileAttachIds.length > 0) {
    //     Object.assign(payload, { fileAttachIds });
    //   }

    setLoading(true);
    try {
      const res = await deviceApi.create(payload);
      message.success(
        `Tạo ${type == DeviceType.Equipment ? "thiết bị" : "máy"} thành công!`
      );
      navigate(
        type == DeviceType.Equipment
          ? "/master-data/device-list"
          : "/master-data/machine-list"
      );
      setFileList([]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <div className="font-bold text-2xl mb-[20px]">
        Tạo {type == DeviceType.Equipment ? "thiết bị" : " máy thi công"}
      </div>
      <Card>
        <Form layout="vertical" form={form}>
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item
                style={{ marginBottom: 0, height: "100%" }}
                label={
                  <div>
                    Hình ảnh{" "}
                    {type == DeviceType.Equipment ? "thiết bị" : " máy"}
                  </div>
                }
                name="avatar"
                className="[&_.ant-form-item-row]:h-full [&_.ant-form-item-control-input]:h-full [&_.ant-form-item-control-input-content]:h-full"
              >
                <SingleImageUpload
                  onUploadOk={(file: FileAttach) => {
                    console.log(file);
                    form.setFieldsValue({
                      avatar: file.path,
                    });
                  }}
                  imageUrl={avatar}
                  height={"100%"}
                  width={"100%"}
                  className="h-full"
                />
              </Form.Item>
            </Col>

            <Col span={9}>
              <Form.Item
                label={type == DeviceType.Equipment ? "Mã thiết bị" : "Mã máy"}
                name="code"
              >
                <CustomInput placeholder="Nếu không điền hệ thống sẽ tự sinh mã" />
              </Form.Item>
              <Form.Item
                label={
                  type == DeviceType.Equipment ? "Tên thiết bị" : "Tên máy"
                }
                name="name"
                rules={rules}
              >
                <CustomInput placeholder="" />
              </Form.Item>
              <Form.Item
                label={
                  type == DeviceType.Equipment ? "Nhóm thiết bị" : " Nhóm máy"
                }
                name="deviceCategoryId"
                rules={rules}
              >
                <DeviceCategorySelector />
              </Form.Item>
              <Form.Item label="Mô tả" name="description">
                <CustomInput type="textarea" placeholder="Mô tả" rows={4} />
              </Form.Item>
            </Col>
            <Col span={9}>
              <Form.Item
                shouldUpdate={true}
                style={{ marginBottom: 0, height: "100%" }}
                className="[&_.ant-form-item-row]:!h-full [&_.ant-form-item-control-input]:!h-full [&_.ant-form-item-control-input-content]:!h-full [&_.ant-form-item-has-success]:!h-full"
              >
                {() => {
                  return (
                    <Form.Item
                      label={"Tệp đính kèm"}
                      style={{ marginBottom: 0, height: "100%" }}
                      name="files"
                      className="[&_.ant-form-item-row]:!h-full [&_.ant-form-item-control-input]:!h-full [&_.ant-form-item-control-input-content]:!h-full [&_.ant-form-item-has-success]:!h-full"
                    >
                      <FileUploadMultiple
                        className="h-full"
                        draggerContent={
                          <p className="ant-upload-text ">
                            <img
                              src={UploadImg}
                              height={30}
                              width={30}
                              className="mr-2"
                            />
                            <div className="font-bold">
                              {"Tải lên tệp đính kèm"}
                            </div>
                            <div className="text-gray-500">
                              Tệp hợp đồng, hóa đơn, tài liệu
                            </div>
                          </p>
                        }
                        fileList={fileList}
                        onUploadOk={(fileList) => {
                          console.log({ fileList });
                          const filePayloads: FileAttachPayload[] =
                            fileList.map((item) => {
                              return {
                                url:
                                  item.url || $url(item.response?.data?.path),
                                name: item.name,
                                size: item?.size,
                                uid: item?.uid,
                                type: item.type,
                                path: item.response?.data?.path,
                                destination: item.response?.data?.destination,
                              };
                            });

                          console.log({ filePayload: filePayloads });
                          setFileList(fileList);
                          form.setFieldsValue({
                            files: JSON.stringify(filePayloads),
                          });
                        }}
                        onDelete={setFileList}
                      />
                    </Form.Item>
                  );
                }}
              </Form.Item>
            </Col>
          </Row>
          <Card title="Thông tin chung" className="mb-0 form-card mt-[16px]">
            <Row gutter={16}>
              <Col span={6}>
                <Form.Item label="Số hiệu" name="serialNumber">
                  <CustomInput placeholder="Số hiệu" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="Mã hệ thống cũ" name="oldCode">
                  <CustomInput placeholder="Mã hệ thống cũ" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="Đơn vị tính" name="unitId" rules={rules}>
                  <UnitSelector />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="Nguồn gốc sản phẩm" name="productOrigin">
                  <CustomSelect
                    placeholder="Nguồn gốc sản phẩm"
                    options={Object.values(ProductOriginTrans).map((item) => ({
                      label: item.label,
                      value: item.value,
                    }))}
                  />{" "}
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="Nhà cung cấp" name="providerId">
                  <ProviderSelector />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="Mã SP NCC" name="supplierProductCode">
                  <CustomInput placeholder="Nhập mã SP NCC" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="Xuất xứ" name="placeOfManufacture">
                  <CustomInput placeholder="Nhập xuất xứ" />
                </Form.Item>
              </Col>
              {/* <Col span={8}>
                <Form.Item label="Mã nhóm" name="groupCode" rules={rules}>
                  <CustomInput placeholder="Nhập mã nhóm" />
                </Form.Item>
              </Col> */}
              <Col span={24}>
                <Form.Item label="Ghi chú" name="notes">
                  <CustomInput placeholder="Ghi chú" />
                </Form.Item>
              </Col>

              <Col span={8}>
                <Form.Item label="Thương hiệu" name="brandId">
                  <DictionarySelector
                    placeholder="Chọn thương hiệu"
                    initQuery={{ type: DictionaryType.Brand }}
                  />
                </Form.Item>
              </Col>
              {/* <Col span={6}>
                <Form.Item label="Mã NCC" name="providerId">
                  <ProviderSelector />
                </Form.Item>
              </Col> */}
              <Col span={8}>
                <Form.Item
                  label="Số tháng bảo hành"
                  name="warrantyPeriodMonths"
                >
                  <CustomInput
                    typeText="number"
                    placeholder="Số tháng bảo hành"
                  />
                </Form.Item>
              </Col>

              <Col span={8}>
                <Form.Item label="Ngày mua" name="purchaseAt">
                  <CustomDatePicker
                    allowClear={false}
                    format={settings.dateFormat}
                    className="w-full"
                  />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="Điều kiện bảo hành" name="warrantyConditions">
                  <CustomInput placeholder="Điều kiện bảo hành" />
                </Form.Item>
              </Col>
            </Row>
          </Card>
          {/* <Card
            title="Thông tin định danh"
            className="mb-0 form-card mt-[16px]"
          >
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item label="Mã định danh" name="identificationCode">
                  <Input />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="Mã thuộc tính" name="attributeCode">
                  <Input />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="Mô tả" name="code">
                  <Input />
                </Form.Item>
              </Col>

              <Col span={12}>
                <Form.Item label="Số hiệu" name="code">
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Thuộc tính định danh"
                  name="identificationAttribute"
                >
                  <Input />
                </Form.Item>
              </Col>
            </Row>
          </Card> */}

          {/* <Divider orientation="left" orientationMargin="0">
            Thông tin khấu hao
          </Divider> */}
          {/* <Row gutter={16}>
            <Col span={8}>
              <Form.Item label="Trạng thái sử dụng" name="status">
                <Input />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="Ngày sử dụng" name="useAt">
                <DatePicker
                  allowClear={false}
                  format={settings.dateFormat}
                  className="w-full"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="Dự án" name="projectId">
                <ProjectSelector />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="Phòng ban" name="departmentId">
                <DepartmentSelector />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="Bộ phận" name="code">
                <Input />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="Nhân viên sử dụng" name="staffId">
                <StaffSelector />
              </Form.Item>
            </Col>
          </Row> */}

          <Card title="Thông tin bổ sung" className="mb-0 form-card mt-[16px]">
            <Row gutter={16}>
              {/* <Col span={8}>
              <Form.Item label="Nhà sản xuất" name="code">
                <Input />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="Số seri" name="serialNumber">
                <Input />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="Thời gian bảo hành" name="code">
                <Input />
              </Form.Item>
            </Col> */}
              <Col span={12}>
                <Form.Item label="Chi phí thuê" name="rentalCost">
                  <CustomInput typeText="number" placeholder="Chi phí thuê" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Chi phí mua" name="purchaseCost">
                  <CustomInput typeText="number" placeholder="Chi phí mua" />
                </Form.Item>
              </Col>
            </Row>
          </Card>
        </Form>
        <div className="flex gap-[16px] justify-end mt-2">
          <CustomButton
            variant="outline"
            className="cta-button"
            onClick={() => {
              navigate("/master-data/device-list");
            }}
          >
            Hủy
          </CustomButton>
          <CustomButton
            className="cta-button"
            loading={loading}
            onClick={() => {
              createData();
            }}
          >
            Tạo {type == DeviceType.Equipment ? "thiết bị" : " máy thi công"}
          </CustomButton>
        </div>
      </Card>
    </div>
  );
};
