export enum ContentDefineType {
  HowToUseApp = "HOW_TO_USE_APP", //hd sử dụng
  SecurityPolicy = "SECURITY_POLICY", //cs bảo mật
  TermOfUse = "TERM_OF_USE", //dkhoan sử sụng
}

export const ContentDefineTypeTrans = {
  [ContentDefineType.HowToUseApp]: "Hướng dẫn sử dụng",
  [ContentDefineType.SecurityPolicy]: "Chính sách bảo mật",
  [ContentDefineType.TermOfUse]: "Điều khoản sử dụng",
};

export interface ContentDefine {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  body: string;
  type: ContentDefineType;
}
