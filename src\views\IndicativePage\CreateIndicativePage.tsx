import { Card, Col, Row, Form, Button, Avatar } from "antd";
import PageTitle from "components/PageTitle/PageTitle";
import CustomInput from "components/Input/CustomInput";
import CustomSelect from "components/Input/CustomSelect";
import CustomDatePicker from "components/Input/CustomDatePicker";
import React, { useState } from "react";
import { Rule } from "antd/lib/form";
import CustomButton from "components/Button/CustomButton";

const rules: Rule[] = [{ required: true, message: "Trường này là bắt buộc" }];

function CreateIndicativePage() {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);

  // Sample options for select fields
  const categoryOptions = [
    { label: "Chỉ thị kỹ thuật", value: "technical" },
    { label: "Chỉ thị an toàn", value: "safety" },
    { label: "Chỉ thị chất lượng", value: "quality" },
  ];

  const typeOptions = [
    { label: "Khẩn cấp", value: "urgent" },
    { label: "Thông thường", value: "normal" },
    { label: "Quan trọng", value: "important" },
  ];

  const projectOptions = [
    { label: "Dự án ABC", value: "project-abc" },
    { label: "Dự án XYZ", value: "project-xyz" },
    { label: "Dự án 123", value: "project-123" },
  ];

  const subcontractorOptions = [
    { label: "Công ty TNHH ABC", value: "contractor-abc" },
    { label: "Công ty TNHH XYZ", value: "contractor-xyz" },
    { label: "Công ty TNHH 123", value: "contractor-123" },
  ];

  const approvalOptions = [
    { label: "Cấp 1", value: "level-1" },
    { label: "Cấp 2", value: "level-2" },
    { label: "Cấp 3", value: "level-3" },
  ];

  const statusOptions = [
    { label: "Nháp", value: "draft" },
    { label: "Chờ duyệt", value: "pending" },
    { label: "Đã duyệt", value: "approved" },
    { label: "Đã hủy", value: "cancelled" },
  ];

  // Steps configuration
  const steps = [
    {
      title: "Tạo mới",
      time: "08:30 12/06/2025",
      user: "Ngô An - Nhân viên",
      note: "Note message",
      icon: "✓",
      status: "finish",
    },
    {
      title: "Duyệt cấp 1",
      time: "08:30 12/06/2025",
      user: "Trần Ánh - Giám sát",
      note: "Note message",
      icon: "✓",
      status: "finish",
    },
    {
      title: "Duyệt cấp 2",
      time: "08:30 12/06/2025",
      user: "Phạm Văn Trí - Quản lý",
      note: "Note message",
      icon: "✓",
      status: "finish",
    },
    {
      title: "Phát hành",
      time: "08:30 12/06/2025",
      user: "Phạm Văn Trí - Quản lý",
      note: "Note message",
      icon: "✓",
      status: "finish",
    },
  ];

  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      console.log("Form values:", values);
      // TODO: Implement API call to create indicative
    } catch (error) {
      console.error("Error creating indicative:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadTemplate = () => {
    // TODO: Implement template download functionality
    console.log("Download template");
  };

  return (
    <div>
      <PageTitle
        title="Tạo chỉ thị"
        breadcrumbs={[
          "Báo cáo",
          "Chỉ thị công trường",
          "Tạo chỉ thị công trường",
        ]}
      />

      <Form form={form} layout="vertical" onFinish={handleSubmit}>
        <Row gutter={24}>
          <Col span={18}>
            <Card className="content-card mb-6">
              <Card title="Thông tin cơ bản" className="mb-0 form-card">
                <Row gutter={16}>
                  {/* First Row */}
                  <Col span={6}>
                    <Form.Item
                      name="indicativeNumber"
                      label="Số chỉ thị"
                      rules={rules}
                    >
                      <CustomSelect
                        placeholder="Số chỉ thị"
                        options={[
                          { label: "CT-001", value: "CT-001" },
                          { label: "CT-002", value: "CT-002" },
                          { label: "CT-003", value: "CT-003" },
                        ]}
                      />
                    </Form.Item>
                  </Col>

                  <Col span={12}>
                    <Form.Item name="title" label="Tiêu đề" rules={rules}>
                      <CustomInput placeholder="Tiêu đề" />
                    </Form.Item>
                  </Col>

                  {/* Second Row */}
                  <Col span={6}>
                    <Form.Item name="category" label="Phân loại" rules={rules}>
                      <CustomSelect
                        placeholder="Phân loại"
                        options={categoryOptions}
                      />
                    </Form.Item>
                  </Col>

                  <Col span={6}>
                    <Form.Item name="projectCode" label="Mã dự án">
                      <CustomSelect
                        placeholder="Mã dự án"
                        options={projectOptions}
                        allowClear
                      />
                    </Form.Item>
                  </Col>

                  <Col span={6}>
                    <Form.Item name="location" label="Địa chỉ">
                      <CustomInput
                        placeholder="Lấy từ thông tin dự án"
                        disabled
                      />
                    </Form.Item>
                  </Col>

                  {/* Third Row */}
                  <Col span={6}>
                    <Form.Item name="subcontractor" label="Nhà thầu phụ">
                      <CustomSelect
                        placeholder="Nhà thầu phụ"
                        options={subcontractorOptions}
                        allowClear
                      />
                    </Form.Item>
                  </Col>

                  <Col span={6}>
                    <Form.Item
                      name="subcontractorAgreement"
                      label="Hợp đồng nhà thầu phụ"
                    >
                      <CustomSelect
                        placeholder="Hợp đồng nhà thầu phụ"
                        options={[
                          { label: "HD-001", value: "HD-001" },
                          { label: "HD-002", value: "HD-002" },
                          { label: "HD-003", value: "HD-003" },
                        ]}
                        allowClear
                      />
                    </Form.Item>
                  </Col>

                  <Col span={6}>
                    <Form.Item name="documentLink" label="Link tài liệu">
                      <CustomInput placeholder="Link tài liệu" />
                    </Form.Item>
                  </Col>

                  {/* Fourth Row */}
                  <Col span={6}>
                    <Form.Item name="issueDate" label="Ngày phát chỉ thị">
                      <CustomDatePicker
                        placeholder="Ngày phát chỉ thị"
                        format="DD/MM/YYYY"
                      />
                    </Form.Item>
                  </Col>

                  <Col span={6}>
                    <Form.Item name="createdDate" label="Ngày tạo chỉ thị">
                      <CustomDatePicker
                        placeholder="Ngày tạo chỉ thị"
                        format="DD/MM/YYYY"
                      />
                    </Form.Item>
                  </Col>

                  <Col span={6}>
                    <Form.Item name="status" label="Trạng thái duyệt">
                      <CustomSelect
                        placeholder="Trạng thái duyệt"
                        options={statusOptions}
                      />
                    </Form.Item>
                  </Col>

                  {/* File Upload Section */}
                  <Col span={24}>
                    <Form.Item label="Tải lên file đính kèm">
                      <div
                        style={{
                          border: "2px dashed var(--color-neutral-n3)",
                          borderRadius: "8px",
                          padding: "40px",
                          textAlign: "center",
                          backgroundColor: "var(--color-neutral-n0)",
                          cursor: "pointer",
                        }}
                        onClick={handleDownloadTemplate}
                      >
                        <div style={{ marginBottom: "16px" }}>
                          <svg
                            width="48"
                            height="48"
                            viewBox="0 0 24 24"
                            fill="none"
                            style={{ margin: "0 auto", display: "block" }}
                          >
                            <path
                              d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"
                              stroke="var(--color-neutral-n5)"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                            <polyline
                              points="14,2 14,8 20,8"
                              stroke="var(--color-neutral-n5)"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                            <line
                              x1="16"
                              y1="13"
                              x2="8"
                              y2="13"
                              stroke="var(--color-neutral-n5)"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                            <line
                              x1="16"
                              y1="17"
                              x2="8"
                              y2="17"
                              stroke="var(--color-neutral-n5)"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                            <polyline
                              points="10,9 9,9 8,9"
                              stroke="var(--color-neutral-n5)"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        </div>
                        <div style={{ fontWeight: "600", marginBottom: "8px" }}>
                          Tải lên tập đính kèm
                        </div>
                        <div
                          style={{
                            color: "var(--color-neutral-n5)",
                            marginBottom: "16px",
                          }}
                        >
                          Tệp hợp đồng lao động, CCCD, hộ chiếu
                        </div>
                        <Button
                          type="default"
                          style={{
                            borderColor: "var(--color-primary)",
                            color: "var(--color-primary)",
                          }}
                        >
                          Download tài liệu mẫu
                        </Button>
                      </div>
                    </Form.Item>
                  </Col>
                </Row>
              </Card>

              <Card title="Nội dung chỉ thị" className="mb-0 mt-6 form-card">
                <Form.Item name="content" label="Nội dung chỉ thị">
                  <CustomInput
                    type="textarea"
                    placeholder="Nhập nội dung chỉ thị"
                    rows={10}
                    required
                  />
                </Form.Item>

                <Form.Item
                  name="tradeAgreement"
                  label="Thỏa thuận thương mại/hợp đồng"
                >
                  <CustomInput
                    placeholder="Thỏa thuận thương mại/hợp đồng"
                    rows={10}
                    required
                  />
                </Form.Item>

                <Form.Item name="description" label="Mô tả chi phí">
                  <CustomInput placeholder="Mô tả chi phí" rows={10} required />
                </Form.Item>
              </Card>

              <Card
                title="Ký duyệt và xác nhận"
                className="mb-0 mt-6 form-card"
              >
                <Row gutter={16}>
                  {/* First Row */}
                  <Col span={12}>
                    <Form.Item
                      name="approverLevel1"
                      label="Thông tin người lập"
                    >
                      <CustomSelect
                        placeholder="Tên - mã - chức vụ"
                        options={[]}
                        allowClear
                      />
                    </Form.Item>
                  </Col>

                  <Col span={12}>
                    <Form.Item
                      name="approverLevel2"
                      label="Thông tin người phát hành"
                    >
                      <CustomSelect
                        placeholder="Tên - mã - chức vụ"
                        options={[]}
                        allowClear
                      />
                    </Form.Item>
                  </Col>

                  {/* Second Row */}
                  <Col span={12}>
                    <Form.Item name="confirmer" label="Thông tin người nhận">
                      <CustomSelect
                        placeholder="Tên - mã - chức vụ"
                        options={[]}
                        allowClear
                      />
                    </Form.Item>
                  </Col>

                  <Col span={12}>
                    <Form.Item
                      name="approvalDate"
                      label="Thông tin người ký duyệt"
                    >
                      <CustomInput
                        type="select"
                        placeholder="Tên - mã - chức vụ"
                        options={[]}
                      />
                    </Form.Item>
                  </Col>

                  {/* Third Row */}
                  <Col span={18}>
                    <Form.Item name="approvalDate" label="Ngày ký">
                      <CustomDatePicker
                        placeholder="Ngày ký"
                        format="DD/MM/YYYY"
                        required
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </Card>
            </Card>

            {/* Action Buttons */}
            <div
              style={{
                display: "flex",
                justifyContent: "flex-end",
                gap: "12px",
              }}
            >
              <CustomButton
                variant="outline"
                onClick={() => form.resetFields()}
                className="cta-button"
              >
                Hủy
              </CustomButton>
              <CustomButton
                htmlType="submit"
                loading={loading}
                className="cta-button"
              >
                Tạo chỉ thị
              </CustomButton>
            </div>
          </Col>

          <Col span={6}>
            <Card
              className="content-card"
              style={{ position: "sticky", top: "20px" }}
              bodyStyle={{ padding: 0 }}
            >
              <div style={{ padding: "20px" }}>
                <h3
                  style={{
                    margin: "20px",
                    fontWeight: "700",
                    fontSize: "20px",
                    color: "#19345b",
                  }}
                >
                  Quy trình duyệt
                </h3>
                <div style={{ minHeight: "400px" }}>
                  {steps.map((step, index) => (
                    <div
                      key={index}
                      style={{
                        display: "flex",
                        marginBottom: "20px",
                        position: "relative",
                        alignItems: "flex-start",
                      }}
                    >
                      {/* Icon */}
                      <div
                        style={{
                          width: "32px",
                          height: "32px",
                          borderRadius: "50%",
                          backgroundColor: "#52c41a",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          color: "white",
                          fontSize: "16px",
                          fontWeight: "bold",
                          marginRight: "16px",
                          flexShrink: 0,
                        }}
                      >
                        ✓
                      </div>

                      {/* Content */}
                      <div style={{ flex: 1, minWidth: 0 }}>
                        {/* Title and Time Row */}
                        <div
                          style={{
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "center",
                            marginBottom: "4px",
                            gap: "8px",
                          }}
                        >
                          <div
                            style={{
                              fontWeight: "600",
                              fontSize: "16px",
                              color: "#19345b",
                              whiteSpace: "nowrap",
                              flexShrink: 0,
                            }}
                          >
                            {step.title}
                          </div>
                          <div
                            style={{
                              fontSize: "12px",
                              color: "#8c8c8c",
                              flexShrink: 0,
                              whiteSpace: "nowrap",
                            }}
                          >
                            {step.time}
                          </div>
                        </div>

                        {/* User with Avatar */}
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            marginBottom: "4px",
                          }}
                        >
                          <Avatar
                            size={20}
                            style={{
                              backgroundColor: "#1890ff",
                              marginRight: "8px",
                              flexShrink: 0,
                            }}
                          >
                            {step.user.charAt(0)}
                          </Avatar>
                          <span
                            style={{
                              fontSize: "14px",
                              color: "#595959",
                              fontWeight: "500",
                            }}
                          >
                            {step.user}
                          </span>
                        </div>

                        {/* Note */}
                        <div
                          style={{
                            fontSize: "14px",
                            color: "#8c8c8c",
                            marginLeft: "28px",
                          }}
                        >
                          {step.note}
                        </div>
                      </div>

                      {/* Connecting Line */}
                      {index < steps.length - 1 && (
                        <div
                          style={{
                            position: "absolute",
                            left: "15px",
                            top: "36px",
                            width: "2px",
                            height: "calc(100% - 12px)",
                            backgroundColor: "#d9d9d9",
                          }}
                        />
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </Card>

            {/* Người theo dõi Card */}
            <Card
              className="content-card"
              style={{ position: "sticky", top: "20px", marginTop: "16px" }}
              bodyStyle={{ padding: 0 }}
            >
              <div style={{ padding: "20px" }}>
                <h3
                  style={{
                    margin: "20px",
                    fontWeight: "700",
                    fontSize: "20px",
                    color: "#19345b",
                  }}
                >
                  Người theo dõi (6)
                </h3>

                {/* Thêm người Button */}
                <div style={{ padding: "0 20px 20px 20px" }}>
                  <CustomButton
                    variant="outline"
                    size="medium"
                    block
                    style={{
                      height: "40px",
                      fontSize: "14px",
                    }}
                  >
                    Thêm người
                  </CustomButton>
                </div>

                {/* User List */}
                <div style={{ padding: "0 20px" }}>
                  {[
                    { name: "Trần Ánh", code: "NV240", role: "Giám sát" },
                    { name: "Trần Ánh", code: "NV240", role: "Giám sát" },
                    { name: "Trần Ánh", code: "NV240", role: "Giám sát" },
                    { name: "Trần Ánh", code: "NV240", role: "Giám sát" },
                    { name: "Trần Ánh", code: "NV240", role: "Giám sát" },
                    { name: "Trần Ánh", code: "NV240", role: "Giám sát" },
                  ].map((user, index) => (
                    <div
                      key={index}
                      style={{
                        display: "flex",
                        alignItems: "center",
                        padding: "12px 0",
                        borderBottom: index < 5 ? "1px solid #f0f0f0" : "none",
                      }}
                    >
                      <Avatar
                        size={40}
                        style={{
                          backgroundColor: "#1890ff",
                          marginRight: "12px",
                          flexShrink: 0,
                        }}
                      >
                        {user.name.charAt(0)}
                      </Avatar>
                      <div style={{ flex: 1 }}>
                        <div
                          style={{
                            fontSize: "14px",
                            fontWeight: "500",
                            color: "#262626",
                            marginBottom: "2px",
                          }}
                        >
                          {user.name}
                        </div>
                        <div
                          style={{
                            fontSize: "12px",
                            color: "#8c8c8c",
                          }}
                        >
                          {user.code} | {user.role}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </Card>
          </Col>
        </Row>
      </Form>
    </div>
  );
}

export default CreateIndicativePage;
