import {
  DeleteOutlined,
  ExportOutlined,
  ImportOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import {
  Button,
  Input,
  message,
  Popconfirm,
  Space,
  Spin,
  Table,
  Tag,
} from "antd";
import Column from "antd/es/table/Column";
import { Pagination } from "components/Pagination";
import { useComponent } from "hooks/useComponent";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  Component,
  ComponentShowType,
  ComponentShowTypeTrans,
  RelationComponent,
} from "types/component";
import { formatVND, getTitle } from "utils";
import { $url } from "utils/url";
import {
  CreateComponentModal,
  VariantModal,
} from "./Components/CreateVariantModal";
import { componentApi } from "api/component.api";
import { handleExport, MyExcelColumn } from "utils/MyExcel";
import ImportSettingComponent, {
  ImportSettingComponentModal,
} from "components/ImportDocument/ImportSettingComponent";
import { removeSubstringFromKeys } from "utils/common";
import { useVariant } from "hooks/useVariant";
import { Variant } from "types/variant";
import { Material } from "types/material";
import { variantApi } from "api/variant.api";
import ImportVariantConfiguration from "components/ImportDocument/ImportVariantConfiguration";
import { TableProps } from "antd/lib";
import { debounce } from "lodash";
import { ProductSelector } from "components/Selector/ProductSeletor";
import { ComponentsSelector } from "components/Selector/ComponentsSelector";
const exportColumns: MyExcelColumn<Component>[] = [
  {
    header: "Ảnh mặt trước",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "frontImage",
    columnKey: "frontImage",
  },
  {
    header: "Ảnh mặt sau",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "backImage",
    columnKey: "backImage",
  },
  {
    header: "Tên thành phần (Nội bộ)",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "privateName",
    columnKey: "privateName",
  },
  {
    header: "Tên thành phần (Khách hàng)",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "name",
    columnKey: "name",
  },
  {
    header: "Mã biến thể",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "code",
    columnKey: "code",
  },
  {
    header: "Loại nguyên vật liệu",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "materialType",
    columnKey: "materialType",
    render: (record) => record.material?.type,
  },
  {
    header: "Truy xuất thông tin mô tả",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "featureImageShowType",
    columnKey: "featureImageShowType",
    render: (record: Component) =>
      ComponentShowTypeTrans[record?.featureImageShowType]?.label || "-",
  },
  {
    header: "Mã nguyên vật liệu sử dụng",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "material",
    columnKey: "material",
    render: (record) => record.code,
  },
  // {
  //   header: "Nhóm thành phần",
  //   headingStyle: {
  //     font: {
  //       bold: true,
  //     },
  //   },
  //   key: "component",
  //   columnKey: "component",
  //   render: (record) => record.code,
  // },
];
export const VariantConfiguration = ({ title = "" }) => {
  const [loadingDelete, setLoadingDelete] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>();
  const { variants, fetchData, query, loading, setQuery, total } = useVariant({
    initQuery: {
      page: 1,
      limit: 10,
      search: "",
    },
  });
  const [selectedProduct, setSelectedProduct] = useState<number>();
  const [selectedComponent, setSelectedComponent] = useState<number>();
  useEffect(() => {
    document.title = getTitle(title);
  }, []);
  const lastFilterChanged = useRef<"name" | "code" | undefined>();
  const [deleteManyLoading, setDeleteManyLoading] = useState<boolean>(false);
  const [selectionType, setSelectionType] = useState<"checkbox" | "radio">(
    "checkbox"
  );
  const [selectedProductId, setSelectedProductId] = useState<number>();
  const [deleteAllLoading, setDeleteAllLoading] = useState<boolean>(false);
  const [selectedComponentId, setSelectedComponentId] = useState<number>();

  console.log("[VariantConfiguration].selectedProductId:", selectedProductId);

  useEffect(() => {
    fetchData();
  }, [query]);
  const modalRef = React.useRef<VariantModal>(null);
  const tagColors = [
    "magenta",
    "volcano",
    "orange",
    "gold",
    "lime",
    "green",
    "cyan",
    "blue",
    "geekblue",
    "purple",
  ];

  const getColor = (index: number) => {
    return tagColors[index % tagColors.length];
  };
  const handleDelete = async (id: number) => {
    try {
      setLoadingDelete(true);
      await variantApi.delete(id);
      setSelectedRowKeys([]);
      message.success("Xóa thành công");
      fetchData();
    } catch (e) {
      console.log({ e });
    } finally {
      setLoadingDelete(false);
    }
  };
  const importModal = useRef<ImportSettingComponentModal>();
  const handleOnUploadedFile = async (excelData: any, setData: any) => {
    const { results } = excelData;

    console.log("results", results);

    const importData = results?.map((item: any) => {
      const refineRow = removeSubstringFromKeys(item, " (*)");
      const code = refineRow["MÃ BIẾN THỂ"] || "";
      const privateName = refineRow["TÊN BIẾN THỂ (NỘI BỘ)"] || "";
      // const name = refineRow["TÊN BIẾN THỂ (KHÁCH HÀNG)"] || "";
      const frontImage = refineRow["ẢNH MẶT TRƯỚC"] || "";
      const backImage = refineRow["ẢNH MẶT SAU"] || "";
      const materialCode = refineRow["MÃ NGUYÊN VẬT LIỆU"] || "";
      const componentCode = refineRow["MÃ NHÓM"] || "";
      const showTypeTemp = refineRow["Truy xuất thông tin mô tả"];
      const featureImageShowType =
        showTypeTemp == "Thành phần"
          ? ComponentShowType.Component
          : ComponentShowType.Material || "";

      return {
        // name,
        privateName,
        code,
        frontImage,
        backImage,
        materialCode,
        componentCode,
        featureImageShowType,
        rowNum: item.__rowNum__,
      };
    });
    console.log("importData", importData);

    setData(importData);
  };
  const handleDeleteManyVariant = async () => {
    try {
      setDeleteManyLoading(true);
      const { data } = await variantApi.deleteMany({
        variantIds: selectedRowKeys,
      });
      setSelectedRowKeys([]);
      message.success("Xóa thành công");
      fetchData();
    } catch (error) {
      console.log(error);
    } finally {
      setDeleteManyLoading(false);
    }
  };
  console.log("What is selected row keys", selectedRowKeys);
  const rowSelection: TableProps<Variant>["rowSelection"] = {
    onChange: (selectedRowKeys: React.Key[], selectedRows: Variant[]) => {
      setSelectedRowKeys(selectedRowKeys);
    },
    getCheckboxProps: (record: Variant) => ({
      disabled: record.name === "Disabled User", // Column configuration not to be checked
      name: record.name,
    }),
  };
  const handleFilterOfTable = (pagination: any, filters: any, sorter: any) => {
    const filterCode = filters.code?.[0];
    const filterName = filters.privateName?.[0];

    const queryObject: any[] = [];

    if (filterCode && filterName) {
      // Xử lý clear 1 trong 2 theo người dùng chọn cuối
      if (lastFilterChanged.current === "name") {
        filters.code = undefined;
      } else if (lastFilterChanged.current === "code") {
        filters.privateName = undefined;
      }
    }

    if (filters.code?.[0]) {
      queryObject.push({
        type: "sort",
        field: "variant.code",
        value: filters.code?.[0],
      });
    }

    if (filters.privateName?.[0]) {
      queryObject.push({
        type: "sort",
        field: "variant.privateName",
        value: filters.privateName?.[0],
      });
    }

    query.queryObject = JSON.stringify(queryObject);
    setQuery({ ...query });
    fetchData();
  };
  useEffect(() => {
    query.componentId = selectedComponent;
    fetchData();
  }, [selectedComponent]);
  const debounceSearch = useCallback(
    debounce(
      (keyword) => setQuery({ ...query, search: keyword, page: 1 }),
      300
    ),
    [query]
  );

  // Xử lý chọn option sản phẩm
  const handleChangeProduct = (id: number) => {
    setSelectedProductId(id);
  };

  const handleChangeComponent = (id: number) => {
    setSelectedComponentId(id);
  };

  useEffect(() => {
    if (selectedProductId) {
      console.log("Product value:", selectedProductId);
      query.productId = selectedProductId;

      fetchData();
    }
  }, [selectedProductId, query]);

  console.log("[VariantConfiguration].variants:", variants);

  // Xử lý trường hợp xóa tất cả
  const handleDeleteAllVariants = async () => {
    if (!selectedProductId) {
      message.error("Vui lòng chọn sản phẩm");
      return;
    }

    try {
      setDeleteAllLoading(true);
      let deleteAllPayload: any = { productId: selectedProductId };

      if (selectedComponentId)
        deleteAllPayload = {
          ...deleteAllPayload,
          componentId: selectedComponentId,
        };

      console.log("Delete all payload:", deleteAllPayload);

      const { data } = await variantApi.deleteAll(deleteAllPayload);

      console.log("Xóa tất cả variants res:", data);

      message.success("Xóa tất cả biến thể thành công");
      fetchData();
    } catch (error) {
      console.log(error);
    } finally {
      setDeleteAllLoading(false);
    }
  };

  const handleDistableDeleteAllBtn = () => {
    if (!selectedProductId) return true;

    if (selectedProductId && variants?.length === 0) return true;

    return false;
  };

  return (
    <div>
      <div className="filter-container">
        <Space className="flex flex-wrap">
          <div className="filter-item btn !min-w-[250px]">
            <label htmlFor="">Sản phẩm</label>
            <ProductSelector
              onChange={(v) => {
                setSelectedProduct(v);
                setSelectedComponent(undefined);
                handleChangeProduct(v);
              }}
            />
          </div>
          <div className="filter-item btn ">
            <label htmlFor="">Thành phần</label>
            <ComponentsSelector
              productId={selectedProduct}
              value={selectedComponent}
              onChange={(v) => {
                setSelectedComponent(v);
                handleChangeComponent(v);
              }}
            />
          </div>

          <div className="filter-item">
            <label htmlFor="">Tìm kiếm</label>
            <Input
              onKeyDown={(ev) => {
                if (ev.code == "Enter") {
                  query.page = 1;
                  setQuery({ ...query });
                }
              }}
              size="middle"
              onChange={(ev) => {
                debounceSearch(ev.target.value);
              }}
              placeholder="Tìm kiếm theo mã hoặc tên"
            />
          </div>

          <div className="filter-item btn">
            <Button
              onClick={fetchData}
              type="primary"
              icon={<SearchOutlined />}
            >
              Tìm kiếm
            </Button>
          </div>

          <div className="filter-item btn">
            <Button
              disabled={!selectedProductId}
              onClick={() => {
                modalRef.current?.handleCreate();
              }}
              type="primary"
              icon={<PlusOutlined />}
            >
              Thêm mới
            </Button>
          </div>

          <div className="filter-item btn">
            <Popconfirm
              disabled={handleDistableDeleteAllBtn()}
              onConfirm={() => {
                handleDeleteAllVariants();
              }}
              title="Xác nhận xóa tất cả những biến thể"
            >
              <Button
                loading={deleteAllLoading}
                disabled={handleDistableDeleteAllBtn()}
                danger
                icon={<DeleteOutlined />}
              >
                Xóa tất cả
              </Button>
            </Popconfirm>
          </div>

          <div className="filter-item btn">
            <Popconfirm
              disabled={!selectedRowKeys}
              onConfirm={() => {
                handleDeleteManyVariant();
              }}
              title="Xác nhận xóa những biến thể này"
            >
              <Button
                loading={deleteManyLoading}
                disabled={
                  selectedRowKeys && selectedRowKeys?.length > 0 ? false : true
                }
                danger
                icon={<DeleteOutlined />}
              >
                Xóa nhiều
              </Button>
            </Popconfirm>
          </div>
          <div className="filter-item btn">
            <Popconfirm
              title={`Bạn có muốn xuất file excel`}
              onConfirm={() =>
                handleExport({
                  onProgress(percent) {
                    console.log("What is percent", percent);
                  },
                  exportColumns,
                  fileType: "xlsx",
                  dataField: "variants",
                  query: query,
                  api: variantApi.findAll,
                  fileName: "Danh sách cấu hình biến thể",
                  sheetName: "Danh sách cấu hình biến thể",
                })
              }
              okText={"Xuất excel"}
              cancelText={"Huỷ"}
            >
              <Button type="primary" loading={false} icon={<ExportOutlined />}>
                Xuất file excel
              </Button>
            </Popconfirm>
          </div>
          <div className="filter-item btn">
            <Button
              onClick={() => {
                importModal.current?.open();
              }}
              type="primary"
              icon={<ImportOutlined />}
            >
              Nhập excel
            </Button>
          </div>
        </Space>
      </div>

      {!selectedProductId ? (
        <div className="mt-5 font-semibold text-red-500">
          Vui lòng chọn sản phẩm
        </div>
      ) : (
        <Spin spinning={loading}>
          <Table<Variant>
            rowSelection={{ type: selectionType, ...rowSelection }}
            pagination={false}
            rowKey="id"
            dataSource={variants}
            scroll={{ x: "max-content" }}
            onChange={handleFilterOfTable}
          >
            <Column
              title="Mã biến thể"
              align="left"
              dataIndex="code"
              key="code"
              render={(text, record: Component) => {
                return <div>{text}</div>;
              }}
              filteredValue={(() => {
                try {
                  const obj = JSON.parse(query.queryObject || "[]");
                  const item = obj.find((o: any) => o.field === "variant.code");
                  return item ? [item.value] : null;
                } catch (e) {
                  return null;
                }
              })()}
              filterDropdownProps={{
                onOpenChange: (open) => {
                  if (open) lastFilterChanged.current = "code";
                },
              }}
              filterMultiple={false}
              filters={[
                { text: "A-Z", value: "ASC" },
                { text: "Z-A", value: "DESC" },
              ]}
            />
            <Column
              title="Ảnh mặt trước"
              align="center"
              dataIndex="fileAttachFrontImage"
              key="fileAttachFrontImage"
              render={(text, record: Variant) => {
                return (
                  <div>
                    {record.fileAttachFrontImage && (
                      <img
                        width={40}
                        height={40}
                        style={{ objectFit: "cover" }}
                        src={$url(record.fileAttachFrontImage?.url)}
                        alt=""
                      />
                    )}
                  </div>
                );
              }}
            />
            <Column
              title="Ảnh mặt sau"
              align="center"
              dataIndex="fileAttachBackImage"
              key="fileAttachBackImage"
              render={(text, record: Variant) => {
                return (
                  <div>
                    {record.fileAttachBackImage && (
                      <img
                        width={40}
                        height={40}
                        style={{ objectFit: "cover" }}
                        src={$url(record.fileAttachBackImage?.url)}
                        alt=""
                      />
                    )}
                  </div>
                );
              }}
            />
            <Column
              title="Tên biến thể (Nội bộ)"
              align="left"
              dataIndex="privateName"
              key="privateName"
              render={(text, record: Variant) => {
                return <div>{text}</div>;
              }}
              filterMultiple={false}
              filteredValue={(() => {
                try {
                  const obj = JSON.parse(query.queryObject || "[]");
                  const item = obj.find(
                    (o: any) => o.field === "variant.privateName"
                  );
                  return item ? [item.value] : null;
                } catch (e) {
                  return null;
                }
              })()}
              filterDropdownProps={{
                onOpenChange: (open) => {
                  if (open) lastFilterChanged.current = "name";
                },
              }}
              filters={[
                { text: "A-Z", value: "ASC" },
                { text: "Z-A", value: "DESC" },
              ]}
            />
            {/* <Column
            title="Tên biến thể (Khách hàng xem)"
            align="left"
            dataIndex="name"
            key="name"
            render={(text, record: Variant) => {
              return <div>{text}</div>;
            }}
          /> */}

            {/* <Column
            title="Loại nguyên vật liệu (Dành cho cấu hình nguyên vật liệu)"
            align="left"
            dataIndex="material"
            key="material"
            render={(material: Material, record: Variant) => {
              return <div>{record.material?.materialType?.name}</div>;
            }}
          /> */}
            <Column
              title="Mã nguyên vật liệu sử dụng"
              align="left"
              dataIndex="material"
              key="material"
              render={(material: Material, record: Variant) => {
                return <div>{material?.code}</div>;
              }}
            />

            <Column
              title="Nhóm thành phần"
              dataIndex="component"
              align="center"
              key="component"
              render={(component: Component, record: Variant) => (
                <div className="flex flex-wrap gap-1">
                  {component && (
                    <Tag color={getColor(component?.id)}>{component?.code}</Tag>
                  )}
                </div>
              )}
            />
            <Column
              title="Truy xuất thông tin mô tả"
              dataIndex="featureImageShowType"
              align="center"
              key="featureImageShowType"
              render={(
                featureImageShowType: ComponentShowType,
                record: Variant
              ) => (
                <div className="flex flex-wrap gap-1">
                  {ComponentShowTypeTrans[featureImageShowType]?.label}
                </div>
              )}
            />
            <Column
              fixed="right"
              title="Thao tác"
              key="action"
              render={(text, record: Variant) => (
                <div className="flex gap-2">
                  <Popconfirm
                    onConfirm={() => {
                      handleDelete(record.id);
                    }}
                    title="Xác nhận xóa"
                  >
                    <Button>Xóa</Button>
                  </Popconfirm>
                  <Button
                    type="primary"
                    onClick={() => {
                      modalRef.current?.handleUpdate(record);
                    }}
                  >
                    Cập nhật
                  </Button>
                </div>
              )}
            />
          </Table>

          <Pagination
            defaultPageSize={query.limit}
            currentPage={query.page}
            total={total}
            onChange={({ limit, page }) => {
              query.page = page;
              query.limit = limit;
              setQuery({ ...query });
            }}
          />
        </Spin>
      )}

      {useMemo(
        () => (
          <ImportVariantConfiguration
            guide={[
              "Vui lòng tải và sử dụng file mẫu để import dữ liệu đúng cách",
              "Không được thay đổi tiêu đề trong file csv/excel mẫu để tránh import thiếu dữ liệu",
              "Chi tiết xem tại sheet Hướng dẫn của file import mẫu",
            ]}
            onSuccess={() => {
              fetchData();
              query.page = 1;

              // message.success("Nhập dữ liệu thành công.");
            }}
            ref={importModal}
            createApi={variantApi.import}
            onUploaded={(excelData, setData) => {
              console.log("up gì lên vậy", excelData);
              handleOnUploadedFile(excelData, setData);
            }}
            okText={`Nhập cấu hình thành phần ngay`}
            demoExcel="/exportFile/file_nhap_mau_bien_the.xlsx"
          />
        ),
        []
      )}
      <CreateComponentModal
        onSubmitOk={fetchData}
        onClose={() => {}}
        ref={modalRef}
        productId={selectedProductId}
      />
    </div>
  );
};

export default VariantConfiguration;
