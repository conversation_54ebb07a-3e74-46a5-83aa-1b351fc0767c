import clsx from "clsx";
import "./Pagetitle.scss";
import { useNavigate } from "react-router-dom";
import { ArrowLeftOutlined } from "@ant-design/icons";
import { Tooltip } from "antd";

interface Props {
  breadcrumbs: (string | { label: string; href?: string })[];
  title?: string;
  extra?: React.ReactNode;
  className?: string;
  back?: boolean;
  onBack?: () => void;
}

const PageTitle = ({
  breadcrumbs,
  extra,
  title,
  className,
  back,
  onBack,
}: Props) => {
  const navigate = useNavigate();

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      window.history.back();
    }
  };

  return (
    <div
      className={clsx(
        "page-title flex justify-between md:items-center mb-[20px]",
        className
      )}
    >
      <div className="breadcrumbs">
        {breadcrumbs.length > 0 ? (
          <div>
            {breadcrumbs.map((bc, i) => {
              if (typeof bc === "string") {
                if (i == breadcrumbs.length - 1) {
                  return (
                    <span key={i} className="breadcrumb-item-last">
                      {bc}
                    </span>
                  );
                }

                return (
                  <>
                    <span className="breadcrumb-item">{bc}</span>
                    <span className="breadcrumb-item-separator">/</span>
                  </>
                );
              } else {
                if (i == breadcrumbs.length - 1) {
                  return (
                    <span
                      className={clsx(
                        "breadcrumb-item-last",
                        bc.href ? "cursor-pointer" : ""
                      )}
                      onClick={() => {
                        if (bc.href) {
                          navigate(bc.href);
                        }
                      }}
                    >
                      {bc.label}
                    </span>
                  );
                }

                return (
                  <>
                    <span
                      className={clsx(
                        "breadcrumb-item",
                        bc.href ? "cursor-pointer" : ""
                      )}
                      onClick={() => {
                        if (bc.href) {
                          navigate(bc.href);
                        }
                      }}
                    >
                      {bc.label}
                    </span>
                    <span className="breadcrumb-item-separator">/</span>
                  </>
                );
              }
            })}
          </div>
        ) : null}
        <div
          className="font-bold text-header"
          style={{ color: "var(--color-neutral-n8)" }}
        >
          {back && (
            <Tooltip title="Trở về" mouseEnterDelay={0.3}>
              <ArrowLeftOutlined
                onClick={handleBack}
                className="-translate-y-[2px]"
                style={{ cursor: "pointer", verticalAlign: "middle" }}
              />
            </Tooltip>
          )}{" "}
          {title}
        </div>
      </div>
      <div>{extra}</div>
    </div>
  );
};

export default PageTitle;
