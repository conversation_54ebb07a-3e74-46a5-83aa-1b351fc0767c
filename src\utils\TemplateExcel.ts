import ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';
import { removeSubstringFromKeys } from './common';
import { addNewSheet } from './excel';

export interface ExcelValidationConfig {
  headerName?: string
  cell?: string;
  column?: string
  type: 'list' | 'whole' | 'decimal' | 'date' | 'textLength' | 'custom';
  allowBlank?: boolean;
  formulae: string[];
  error?: string;
  errorStyle?: 'stop' | 'warning' | 'information';
}

export interface SheetData {
  name: string;
  data: string[];
}

export interface ExportTemplateOptions {
  templatePath: string; // duong dan toi file template
  outputFileName: string; // ten file output
  sheetsToAdd: SheetData[]; // danh sach sheet can them
  validations: ExcelValidationConfig[]; // danh sach validation
  successMessage?: string; // thong bao thanh cong
  errorMessage?: string; // thong bao that bai
}

/**
 * Export demo excel with validation
 * @param options Options
 * @returns 
 */
export const exportTemplateWithValidation = async ({
  templatePath,
  outputFileName,
  sheetsToAdd = [],
  validations = [],
  successMessage = 'Tải file import mẫu thành công!',
  errorMessage = 'Có lỗi xảy ra',
}: ExportTemplateOptions) => {
  try {
    // Load template file
    const response = await fetch(templatePath);
    const arrayBuffer = await response.arrayBuffer();

    // Create workbook from template
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(arrayBuffer);
    const mainWorksheet = workbook.getWorksheet(1);

    // Add additional sheets
    for (const sheet of sheetsToAdd) {
      const { name, data } = sheet;
      addNewSheet(workbook, name, data, "veryHidden");
    }

    // Apply validations to main worksheet
    validations.forEach((validation) => {
      if (mainWorksheet) {
        applyValidation(mainWorksheet, validation)
      }
    });

    // Export file
    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });

    saveAs(blob, outputFileName);
    return { success: true, message: successMessage };
  } catch (error) {
    const errorMsg = error instanceof Error ? error.message : String(error);
    return {
      success: false,
      message: `${errorMessage}: ${errorMsg}`
    };
  }
};

const applyValidation = (worksheet: ExcelJS.Worksheet, validation: ExcelValidationConfig) => {
  const error = validation.error ?? 'Vui lòng chọn một giá trị từ danh sách.'
  const validationConfig = {
    type: validation.type,
    allowBlank: validation.allowBlank ?? true,
    formulae: validation.formulae,
    error,
    errorStyle: validation.errorStyle,
    showErrorMessage: !!error,
  };

  // Apply to entire column starting from startRow (default: 2) to max row
  const startRow = 2;
  const maxRow = 10000 // max 10000 dong

  let targetColumn: string | undefined = validation.column;
  if (validation.headerName) {
    // Tìm cột có tên header tương ứng
    const headerRow = worksheet.getRow(1);

    headerRow.eachCell((cell, colNumber) => {
      const plainText = getPlainTextFromCell(cell);

      if (
        plainText.replace("*", "").trim().toLowerCase() === validation.headerName!.trim().toLowerCase()
      ) {
        targetColumn = getExcelColumnLetter(colNumber); // Chuyển số sang 'A', 'B', ...
      }
    });
  }

  if (targetColumn) {
    for (let row = startRow; row <= maxRow; row++) {
      const cell = worksheet.getCell(`${targetColumn}${row}`);
      cell.dataValidation = validationConfig;
    }
  } else if (validation.cell) {
    // Backward compatibility for single cell validation
    const cell = worksheet.getCell(validation.cell);
    cell.dataValidation = validationConfig;
  }
};

const getExcelColumnLetter = (colNum: number): string => {
  let letter = '';
  while (colNum > 0) {
    const modulo = (colNum - 1) % 26;
    letter = String.fromCharCode(65 + modulo) + letter;
    colNum = Math.floor((colNum - 1) / 26);
  }
  return letter;
};

const getPlainTextFromCell = (cell: ExcelJS.Cell): string => {
  const value = cell.value;

  if (typeof value === 'string') return value.trim();

  if (typeof value === 'object' && value !== null && 'richText' in value) {
    return value.richText.map((part: any) => part.text).join('').trim();
  }

  return '';
};