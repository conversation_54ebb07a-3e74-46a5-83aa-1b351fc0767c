import React from "react";
import { Button as AntButton } from "antd";
import { useTheme } from "context/ThemeContext";
import { useThemeColors } from "theme/useThemeColors";
import "./CustomButton.scss";
import { PlusIcon } from "assets/svgs/PlusIcon";
import { PlusOutlined } from "@ant-design/icons";
import { FiPlus } from "react-icons/fi";

export type ButtonVariant = "primary" | "secondary" | "outline" | "text";
export type ButtonSize = "small" | "medium" | "large";

export interface CustomButtonProps {
  children?: React.ReactNode;
  variant?: ButtonVariant;
  size?: ButtonSize;
  icon?: React.ReactNode;
  showPlusIcon?: boolean;
  disabled?: boolean;
  loading?: boolean;
  block?: boolean;
  className?: string;
  onClick?: (ev: React.MouseEvent<HTMLElement, MouseEvent>) => void;
  htmlType?: "button" | "submit" | "reset";
  style?: React.CSSProperties;
}

const CustomButton: React.FC<CustomButtonProps> = ({
  children,
  variant = "primary",
  size = "medium",
  icon,
  showPlusIcon = false,
  disabled = false,
  loading = false,
  block = false,
  className = "",
  onClick,
  htmlType = "button",
  style,
}) => {
  const { darkMode } = useTheme();
  const { color } = useThemeColors();

  const buttonIcon = icon || (showPlusIcon ? <FiPlus size={16} /> : null);

  const buttonClassName = `
    custom-button
    custom-button-${variant}
    custom-button-${size}
    ${darkMode ? "dark" : ""}
    ${block ? "block" : ""}
    ${className}
    shadow-none
  `;

  return (
    <AntButton
      className={buttonClassName}
      type={variant === "primary" ? "primary" : "default"}
      icon={buttonIcon}
      disabled={disabled}
      loading={loading}
      block={block}
      onClick={(ev) => {
        onClick?.(ev);
      }}
      htmlType={htmlType}
      style={style}
    >
      {children}
    </AntButton>
  );
};

export default CustomButton;
