import { Device } from "./device";
import { Dictionary } from "./dictionary";
import { FileAttach } from "./fileAttach";
import { RequiredResource } from "./requiredResource";
import { Step } from "./step";
import { Task } from "./task";
import { Todo } from "./todo";
import { Unit } from "./unit";


export enum DependencyType {
  FS = "FS",// Finish to Start
  SS = "SS",// Start to Start
  FF = "FF",// Finish to Finish
  SF = "SF" // Start to Finish
}

export enum TaskType {
  Construction = 'CONSTRUCTION',
  Office = 'OFFICE'
}

export const TaskTypeTrans = {
  [TaskType.Construction]: {
    value: TaskType.Construction,
    label: "Thi công",
  },
  [TaskType.Office]: {
    value: TaskType.Office,
    label: "Văn phòng",
  },
};

export interface TaskTemplate {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  name: string; // Tên công việc mẫu (Template Name) (*)
  type: TaskType; // Loại công việc (Task Type)
  description?: string; // Mô tả (Description)
  estimateAt?: number; // Thời gian dự kiến hoàn thành (Estimated Duration, tính theo giờ hoặc ngày)
  estimatedCost?: number; // Chi phí dự kiến (Estimated Cost)
  isActive: boolean;
  steps: Step[];
  // requiredResources: RequiredResource[];
  requiredResources: string;
  note: string;
  fileAttaches: FileAttach[];
  avatar: string;
  files: string;
  requiredDevices: RequiredResource[]
  requiredMachines: RequiredResource[]
  workTypes: Dictionary[]
  children: any[]
  code: string
  parents: any[]
  taskTemplateDetails: TaskTemplateDetail[]
}

export interface TaskTemplateDetail {
  task: TaskTemplate
  dependent: TaskTemplate
  totalChildren: number
}

interface RequiredResourceForm {
  id: number;
  quantity: number;
  type: string;
  unitId: number;
  materialId: number;
  deviceId: number;
  device?: Device
  unit?: Unit
}

interface TaskTemplateDetailForm {
  id?: number;
  name?: string;
  dependencyType: string;
  delay: string;
  taskId: number;
  dependentId: number;
  task?: TaskTemplate
  dependent?: TaskTemplate
}

export interface TaskTemplateForm extends Omit<TaskTemplate, "requiredDevices" | "requiredMachines" | "taskTemplateDetails"> {
  fileAttachIds: number[]
  requiredDevices: RequiredResourceForm[]
  requiredMachines: RequiredResourceForm[]
  workTypeIds: number[]
  todos: Todo[]
  taskTemplateDetails: TaskTemplateDetailForm[]
  parentIds: number[]
}