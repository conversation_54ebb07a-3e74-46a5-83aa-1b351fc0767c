import { Select } from "antd";
import { DefaultOptionType } from "antd/es/select";
import { SelectProps } from "antd/lib";
import { useColor } from "hooks/useColor";
import { useDevice } from "hooks/useDevice";
import { debounce, uniqBy } from "lodash";
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
} from "react";
import { Device } from "types/device";
import { QueryParams2 } from "types/query";

type CustomFormItemProps = {
  value?: number;
  initQuery?: QueryParams2;
  disabled?: boolean;
  selectedColor?: Device[];
  multiple?: boolean;
  onChange?: (value: any) => void;
  selectProps?: SelectProps;
  initOptionItem?: Device | Device[];
  valueIsOption?: boolean;
  allowClear?: boolean;
  placeholder?: string;
  forFilterDevice?: string;
  isGetDeviceCode?: boolean;
};

export interface DeviceSelector {
  refresh(): void;
}

/**
 * A demo that changes it's number value on click.
 * @param value initialValue passed by parent Form.Item.
 * @param onChange a callback for Form.Item to read its child component's value.
 * @constructor
 */
export const DeviceSelector = forwardRef(
  (
    {
      value,
      onChange,
      initQuery,
      disabled,
      multiple = false,
      selectedColor,
      initOptionItem,
      valueIsOption,
      selectProps,
      allowClear = true,
      placeholder = "Chọn thiết bị",
      forFilterDevice,
      isGetDeviceCode = false,
    }: CustomFormItemProps,
    ref
  ) => {

    const { devices, total, loading, fetchData, query } = useDevice({
      initQuery: {
        page: 1,
        limit: 90,
        type: forFilterDevice,
        ...initQuery,
      },
    });

    useImperativeHandle<any, DeviceSelector>(
      ref,
      () => ({
        refresh() {
          fetchData();
        },
      }),
      []
    );

    useEffect(() => {
      if (forFilterDevice !== undefined) {
        query.type = forFilterDevice;
      }
      fetchData();
    }, [forFilterDevice]);

    const debounceSearch = useCallback(
      debounce((keyword) => {
        query.search = keyword;
        fetchData();
      }, 300),
      [query]
    );

    const options = useMemo(() => {
      let data = [...devices];
      if (initOptionItem) {
        if ((initOptionItem as Device[])?.length) {
          data = data.concat(initOptionItem);
        } else {
          data.push(initOptionItem as Device);
        }
      }

      return uniqBy(data, (data) => data.id);
    }, [devices, initOptionItem]);

    return (
      <Select<any, DefaultOptionType>
        mode={multiple ? "multiple" : undefined}
        disabled={disabled}
        loading={loading}
        style={{ width: "100%", minWidth: 200 }}
        allowClear={allowClear}
        onSearch={debounceSearch}
        showSearch
        placeholder={placeholder}
        filterOption={false}
        value={value}
        onChange={(v, opts) => {
          if (v === undefined || v.length == 0) {
            debounceSearch(v);
          }
          if (valueIsOption) {
            if (opts instanceof Array) {
              onChange?.(opts?.map((v) => v.item));
            } else {
              onChange?.(opts?.item);
            }
          } else {
            onChange?.(v);
          }
        }}
        {...selectProps}
      >
        {options?.map((item) => (
          <Select.Option item={item} value={item.id} key={item.id}>
            <div className="flex items-center gap-2">
              <span>{isGetDeviceCode ? item.code : item.name}</span>
            </div>
          </Select.Option>
        ))}
      </Select>
    );
  }
);
