import { $url } from "utils/url";
import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const authApi = {
  login: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/auth/login",
      data,
      method: "post",
    }),

  passwordUpdate: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/auth/password",
      data,
      method: "patch",
    }),

  updateProfile: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/auth/profile`,
      data,
      method: "patch",
    }),

  profile: (): AxiosPromise<any> =>
    request({
      url: "/v1/admin/auth/profile",
    }),
  checkIsAuthGG: (): AxiosPromise<any> =>
    request({
      url: "/v1/admin/auth/google/isAuth",
    }),
  authGG: (): AxiosPromise<any> =>
    request({
      url: "/v1/admin/auth/google/auth",
    }),
  authGG2: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/auth/google/oauth2",
      data,
      method: "post",
    }),
  sendOtp: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/auth/forgot",
      data,
      method: "post",
    }),
  resetPassword: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/auth/forgot/confirm",
      data,
      method: "post",
    }),
  verifyOtp: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/auth/otp/verify",
      data,
      method: "post",
    }),
};
