@import "../../styles/Theme.scss";

.column-visibility-modal {
  // Remove the problematic rule that makes outline button text white
  // .ant-btn-variant-outlined {
  //   color: var(--color-neutral-n0);
  // }

  .ant-modal-title {
    font-weight: 700;
    font-size: 20px;
    line-height: 140%;
    letter-spacing: 0%;
    color: $color-neutral-n8;
  }

  .modal-description {
    margin-bottom: 20px;
    color: $color-neutral-n5;
    font-weight: 300;
    font-size: 14px;
    line-height: 140%;
  }

  .column-list-container {
    // border: 1px solid $color-neutral-n3;
    overflow: hidden;

    .column-list-header {
      background-color: $color-primary;
      color: $color-neutral-n0;
      padding: 8px;
      border-bottom: 1px solid $color-neutral-n3;

      .select-all-checkbox {
        .ant-checkbox {
          .ant-checkbox-inner {
            background-color: white;
            border-color: white;
            border-radius: 5px !important;
            width: 20px;
            height: 20px;
          }

          &.ant-checkbox-checked .ant-checkbox-inner {
            background-color: white;
            border-color: white;
            border-radius: 5px !important;

            &::after {
              border-color: $color-primary;
              inset-inline-start: 30%;
            }
          }

          &.ant-checkbox-indeterminate .ant-checkbox-inner {
            background-color: white;
            border-color: white;
            border-radius: 5px !important;

            &::after {
              background-color: $color-primary;
            }
          }
        }

        .header-title {
          color: $color-neutral-n0 !important;
          font-weight: 600;
          font-size: 16px;
          margin-left: 8px;
        }
      }
    }

    .column-list {
      max-height: 400px;
      overflow-y: auto;

      .ant-list-item {
        border-bottom: none;
        margin: 0;
      }

      .column-list-item {
        padding: 8px;
        height: 40px;
        margin: 0;
        border-bottom: 1px solid $color-neutral-n2;
        background-color: $color-neutral-n1;

        &:last-child {
          border-bottom: none;
        }

        .column-checkbox {
          width: 100%;
          margin: 0;

          .ant-checkbox {
            .ant-checkbox-inner {
              border-color: $color-neutral-n4;
              border-radius: 5px !important;
              width: 20px;
              height: 20px;
            }

            &.ant-checkbox-checked .ant-checkbox-inner {
              background-color: $color-primary;
              border-color: $color-primary;

              &::after {
                inset-inline-start: 30%;
              }
            }
          }

          .column-title {
            margin-left: 12px;
            font-weight: 500;
            font-size: 14px;
            color: $color-neutral-n8;
          }

          // &.disabled.column-title {
          //   color: $color-neutral-n3 !important;
          // }
        }
        &.disabled {
          opacity: 0.6;
          background-color: $color-neutral-n3;
        }
      }
    }
  }

  .ant-modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 20px;

    .ant-btn {
      height: 40px;
      padding: 0 24px;
      font-weight: 500;
      border-radius: 6px;
    }

    // Ensure cancel button has proper text color for outline variant
    .custom-button-outline {
      color: var(--color-primary) !important;
      border-color: var(--color-primary) !important;
      background-color: transparent !important;

      &:hover {
        color: var(--color-primary-hover) !important;
        border-color: var(--color-primary-hover) !important;
        background-color: var(--color-primary-overlay-5) !important;
      }
    }

    .custom-button-primary {
      background-color: $color-primary !important;
      border-color: $color-primary !important;
      color: white !important;

      &:hover {
        background-color: darken($color-primary, 10%) !important;
        border-color: darken($color-primary, 10%) !important;
      }
    }
  }
  // Dark mode styles
  &.dark {
    .column-visibility-modal {
      .modal-description {
        background-color: $color-neutral-n5 !important;
      }

      .column-list-container {
        border-color: $color-neutral-n6;

        .column-list-header {
          background-color: $color-primary-dark;
          border-bottom-color: $color-neutral-n6;
        }

        .column-list {
          .column-list-item {
            border-bottom-color: $color-neutral-n6;

            &.even {
              background-color: $color-neutral-n1;
            }

            &.odd {
              background-color: $color-neutral-n2;
            }

            &.disabled {
              opacity: 0.6;
              background-color: $color-neutral-n7 !important;
            }

            .column-checkbox {
              .column-title {
                color: $color-neutral-n2;
              }
            }
          }
        }
      }
    }
  }
}

.dark {
  .column-visibility-modal {
    .ant-modal-footer {
      // White outline button for dark mode
      .custom-button-outline {
        color: var(--color-neutral-n8) !important;
        border-color: var(--color-neutral-n8) !important;
        background-color: transparent !important;

        &:hover {
          color: var(--color-neutral-n8) !important;
          border-color: var(--color-neutral-n8) !important;
          background-color: rgba(255, 255, 255, 0.1) !important;
        }

        &:active {
          color: var(--color-neutral-n8) !important;
          border-color: var(--color-neutral-n8) !important;
          background-color: rgba(255, 255, 255, 0.2) !important;
        }
      }

      .custom-button-primary {
        background-color: $color-primary-dark !important;
        border-color: $color-primary-dark !important;
        color: white !important;

        &:hover {
          background-color: lighten($color-primary-dark, 10%) !important;
          border-color: lighten($color-primary-dark, 10%) !important;
        }
      }
    }
  }
}
