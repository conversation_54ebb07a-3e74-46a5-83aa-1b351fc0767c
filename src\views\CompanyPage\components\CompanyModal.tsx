import { Col, DatePicker, Form, Input, message, Modal, Row } from "antd";
import { Rule } from "antd/lib/form";
import { companyApi } from "api/company.api";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { ModalStatus } from "types/modal";
import { Company } from "types/company";
import dayjs from "dayjs";
import { phoneValidate } from "utils/validateRule";
import { phoneNumberRule } from "utils/phoneInput";
import { settings } from "settings";
import { Project } from "types/project";
import { Params } from "react-router-dom";
import { get } from "lodash";
import { BMDTextArea } from "components/TextArea/BMDTextArea";

const rules: Rule[] = [{ required: true }];

export interface CompanyModal {
  handleCreate: (
    getOneProject: (id: number) => Promise<Project | undefined>,
    params?: string
  ) => void;
  handleUpdate: (company: Company) => void;
}
interface CompanyModalProps {
  onClose: () => void;
  onSubmitOk: (data: Company) => void;
}

interface CompanyForm {
  id: number;
  name: string;
  code: string;
  phone: string;
  phone2: string;
  otherContact: string;
  email: string;
  address: string;
  taxCode: string;
  startAt: number;
  note: string;
  isActive: boolean;
}

export const CompanyModal = React.forwardRef(
  ({ onClose, onSubmitOk }: CompanyModalProps, ref) => {
    const [form] = Form.useForm<CompanyForm>();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");
    const [selectedCompany, setSelectedCompany] = useState<Company>();

    useImperativeHandle<any, CompanyModal>(
      ref,
      () => ({
        handleCreate() {
          form.resetFields();
          setVisible(true);
          setStatus("create");
          setSelectedCompany(undefined);
        },
        handleUpdate(company: Company) {
          form.setFieldsValue({ ...company });
          setVisible(true);
          setStatus("update");
          setSelectedCompany(company);
        },
      }),
      []
    );

    const getPayload = () => {
      const { startAt, ...data } = form.getFieldsValue();

      return {
        company: {
          ...data,
          startAt: startAt ? dayjs(startAt).unix() : 0,
          isActive: true,
        },
      };
    };

    const getDataSubmit = async () => {
      const { startAt, ...data } = form.getFieldsValue();

      const payload = {
        company: {
          ...data,
          startAt: startAt ? dayjs(startAt).unix() : 0,
          isActive: selectedCompany?.isActive ?? true,
        },
      };

      return payload;
    };

    const createData = async () => {
      await form.validateFields();

      setLoading(true);
      try {
        const { data } = await companyApi.create(await getDataSubmit());
        message.success("Tạo công ty thành công!");
        onSubmitOk(data);
        handleClose();
      } finally {
        setLoading(false);
      }
    };

    const handleSubmit = () => {
      if (status === "create") {
        createData();
      }
      onClose();
    };

    // const submitForm = async () => {
    //   try {
    //     setLoading(true);
    //     await form.validateFields();
    //     const data = getPayload();

    //     switch (status) {
    //       case "create":
    //         await companyApi.create(data);
    //         message.success("Tạo chủ đầu tư thành công!");
    //         break;
    //       case "update":
    //         await companyApi.update(selectedCompany?.id || 0, data);
    //         message.success("Cập nhật chủ đầu tư thành công!");
    //         break;
    //     }
    //     onSubmitOk();
    //     handleClose();
    //   } finally {
    //     setLoading(false);
    //   }
    // };

    const handleClose = () => {
      onClose?.();
      setVisible(false);
      setSelectedCompany(undefined);
    };

    return (
      <Modal
        onCancel={handleClose}
        open={visible}
        title={
          status === "create"
            ? "Tạo chủ đầu tư mới"
            : "Cập nhật thông tin chủ đầu tư"
        }
        style={{ top: 20 }}
        width="70%"
        confirmLoading={loading}
        onOk={handleSubmit}
        okText={status === "create" ? "Tạo" : "Cập nhật"}
        cancelButtonProps={{ className: "hidden" }}
      >
        <Form layout="vertical" form={form}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="Mã công ty" name="code">
                <Input
                  disabled={status === "update"}
                  placeholder="Nếu không điền hệ thống sẽ tự sinh mã"
                />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item label="Tên công ty" name="name" rules={rules}>
                <Input placeholder="Tên công ty" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                label="Số điện thoại"
                name="phone"
                rules={[{ required: true }, ...phoneValidate]}
              >
                <Input placeholder="Số điện thoại" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                label="Số điện thoại 2"
                name="phone2"
                rules={phoneValidate}
              >
                <Input placeholder="Số điện thoại 2" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                label="Liên hệ khác"
                name="otherContact"
                rules={[phoneNumberRule]}
              >
                <Input placeholder="Liên hệ khác" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                label="Email"
                name="email"
                rules={[
                  { required: true, message: "Vui lòng nhập email" },
                  { type: "email", message: "Email không hợp lệ" },
                ]}
              >
                <Input placeholder="Email" />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item label="Địa chỉ" name="address" rules={rules}>
                <Input placeholder="Địa chỉ" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item label="Mã số thuế" name="taxCode" rules={rules}>
                <Input placeholder="Mã số thuế" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item label="Ngày bắt đầu" name="startAt">
                <DatePicker
                  allowClear={false}
                  format={settings.dateFormat}
                  className="w-full"
                />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item label="Ghi chú" name="note">
                <BMDTextArea placeholder="Ghi chú" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
);
