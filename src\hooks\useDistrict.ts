import { districtApi } from "api/district.api";
import { useState } from "react";
import { District } from "types/address";
import { QueryParam } from "types/query";

export interface DistrictQuery extends QueryParam {
  parentCode?: string;
  storeId?: number;
}

export const useDistrict = () => {
  const [data, setData] = useState<District[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const fetchData = async (query?: DistrictQuery) => {
    setLoading(true);
    const { data } = await districtApi.findAll(query);
    setData(data.districts);
    setTotal(data.total);
    setLoading(false);
  };

  return {
    districts: data,
    total,
    fetchDistricts: fetchData,
    districtLoading: loading,
  };
};
