import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
} from "react";
import { debounce, uniqBy } from "lodash";
import { useDeviceCategory } from "hooks/useDeviceCategory";
import { DeviceCategory } from "types/deviceCategory";
import { QueryParams2 } from "types/query";
import CustomSelect from "components/Input/CustomSelect";

type CustomFormItemProps = {
  value?: number;
  initQuery?: QueryParams2;
  disabled?: boolean;
  selectedDeviceCategory?: DeviceCategory[];
  multiple?: boolean;
  onChange?: (value: any) => void;
  initOptionItem?: DeviceCategory | DeviceCategory[];
  valueIsOption?: boolean;
  allowClear?: boolean;
  placeholder?: string;
};

export interface DeviceCategorySelector {
  refresh(): void;
}

export const DeviceCategorySelector = forwardRef(
  (
    {
      value,
      onChange,
      initQuery,
      disabled,
      multiple = false,
      selectedDeviceCategory,
      initOptionItem,
      valueIsOption,
      allowClear = true,
      placeholder = "Chọn loại thiết bị",
    }: CustomFormItemProps,
    ref
  ) => {
    const { deviceCategories, loading, fetchData, query } = useDeviceCategory({
      initQuery: { page: 1, limit: 50, ...initQuery },
    });

    useImperativeHandle<any, DeviceCategorySelector>(
      ref,
      () => ({
        refresh() {
          fetchData();
        },
      }),
      []
    );

    useEffect(() => {
      fetchData();
    }, [selectedDeviceCategory]);

    const debounceSearch = useCallback(
      debounce((keyword) => {
        query.search = keyword;
        fetchData();
      }, 300),
      [query]
    );

    const options = useMemo(() => {
      let data = [...deviceCategories];
      if (initOptionItem) {
        if ((initOptionItem as DeviceCategory[])?.length) {
          data = data.concat(initOptionItem);
        } else {
          data.push(initOptionItem as DeviceCategory);
        }
      }
      return uniqBy(data, (item) => item.id).map((item) => ({
        label: item.name,
        value: item.id,
        item, // lưu nguyên object nếu cần dùng sau
      }));
    }, [deviceCategories, initOptionItem]);

    const handleChange = (v: any, option: any) => {
      if (valueIsOption) {
        if (option instanceof Array) {
          onChange?.(option.map((opt) => opt.item));
        } else {
          onChange?.(option.item);
        }
      } else {
        onChange?.(v);
      }
    };

    return (
      <CustomSelect
        value={value}
        onChange={handleChange}
        disabled={disabled}
        options={options}
        mode={multiple ? "multiple" : undefined}
        allowClear={allowClear}
        placeholder={placeholder}
        onSearch={debounceSearch}
        loading={loading}
      />
    );
  }
);
