import { Role } from "./role";

export interface Permission {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  name: string;
  path: string;
  title: string;
  roles: Role[];
}

export enum PermissionType {
  List = "list",
  Add = "add",
  Edit = "update",
  Delete = "delete",
  Block = "block",
  Reset = "reset",
  ViewAll = "viewAll",
  // ExportExcel = "exportExcel",
  // SendEmail = "sendEmail",
  // ExportPdf = "exportPdf",
  // Approve = "approve",
  // Reject = "reject",
  // Block = "block",
  // Reset = "reset",
  // Activate = "activate",
}

export const PermissionTypeTrans = {
  [PermissionType.List]: {
    value: PermissionType.List,
    label: "Xem",
  },
  [PermissionType.Add]: {
    value: PermissionType.Add,
    label: "Thêm",
  },
  [PermissionType.Edit]: {
    value: PermissionType.Edit,
    label: "Sửa",
  },
  [PermissionType.Delete]: {
    value: PermissionType.Delete,
    label: "Xoá",
  },
  [PermissionType.Block]: {
    value: PermissionType.Block,
    label: "Khóa",
  },
  [PermissionType.Reset]: {
    value: PermissionType.Reset,
    label: "Reset",
  },
  [PermissionType.ViewAll]: {
    value: PermissionType.ViewAll,
    label: "Xem tất cả",
  },
};
