import { taskTemplateApi } from "api/taskTemplate.api";
import { useMemo, useState } from "react";
import { TaskTemplate } from "types/taskTemplate";
import { QueryParam } from "types/query";

export interface TaskTemplateQuery extends QueryParam { }

interface UseTaskTemplateProps {
  initQuery: TaskTemplateQuery;
}

export const useTaskTemplate = ({ initQuery }: UseTaskTemplateProps) => {
  const [data, setData] = useState<TaskTemplate[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<TaskTemplateQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const isEmptyQuery = useMemo(() => Object.keys(query).filter(
    (k) => (query[k] != undefined || query[k] != null) && !["limit", "page", "queryObject", "excludeIds"].includes(k)
  ).length == 0, [query])

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await taskTemplateApi.findAll(query);

      setData(data.taskTemplates);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return { taskTemplates: data, total, fetchData, loading, setQuery, query, isEmptyQuery };
};
