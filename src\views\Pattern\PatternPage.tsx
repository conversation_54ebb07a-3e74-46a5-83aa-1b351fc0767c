import { PlusOutlined, SearchOutlined } from "@ant-design/icons";
import {
  Avatar,
  Button,
  Image,
  Input,
  message,
  Popconfirm,
  Space,
  Spin,
  Table,
} from "antd";
import { patternApi } from "api/pattern.api";
import { Pagination } from "components/Pagination";
import React, { useEffect, useState, useRef } from "react";
import { ModalStatus } from "types/modal";
import { QueryParam } from "types/query";
import { Pattern } from "types/pattern";
import { getTitle } from "utils";
import { $url } from "utils/url";
import { PatternModal } from "./PatternModal";
import { usePattern } from "hooks/usePattern";
import { settings } from "settings";

const { ColumnGroup, Column } = Table;

export const PatternPage = ({ title = "" }) => {
  const { patterns, fetchData, loading, query, setQuery, total } = usePattern({
    initQuery: { limit: 10, page: 1 },
  });
  const [loadingDelete, setLoadingDelete] = useState(false);
  const modalRef = useRef<PatternModal>(null);

  useEffect(() => {
    document.title = getTitle(title);
  }, []);

  useEffect(() => {
    fetchData();
  }, [query]);

  const handleDelete = async (id: number) => {
    try {
      setLoadingDelete(true);
      await patternApi.delete(id);
      message.success("Xóa thành công");

      fetchData();
    } catch (e) {
      console.log({ e });
    } finally {
      setLoadingDelete(false);
    }
  };

  return (
    <div>
      <div className="filter-container">
        <Space>
          <div className="filter-item">
            <label htmlFor="">Tìm kiếm</label>
            <Input
              onKeyDown={(ev) => {
                if (ev.code == "Enter") {
                  query.page = 1;
                  setQuery({ ...query });
                }
              }}
              size="middle"
              onChange={(ev) => {
                query.search = ev.currentTarget.value;
                setQuery({ ...query });
              }}
              placeholder="Tìm kiếm"
            />
          </div>

          <div className="filter-item btn">
            <Button
              onClick={fetchData}
              type="primary"
              icon={<SearchOutlined />}
            >
              Tìm kiếm
            </Button>
          </div>

          <div className="filter-item btn">
            <Button
              onClick={() => {
                modalRef.current?.handleCreate();
              }}
              type="primary"
              icon={<PlusOutlined />}
            >
              Thêm mới
            </Button>
          </div>
        </Space>
      </div>

      <Spin spinning={loading}>
        <Table pagination={false} rowKey="id" dataSource={patterns}>
          <Column
            title="Icon"
            dataIndex="icon"
            key="pattern.icon"
            render={(text, record: Pattern) => (
              <>
                <div className="flex items-center gap-2">
                  <Avatar
                    src={$url(record.icon)}
                    icon={
                      <Image
                        preview={false}
                        src={"/default-thumbnail.jpg"}
                        className="!w-8 !h-8"
                      />
                    }
                  />
                </div>
              </>
            )}
          />
          <Column title="Tên pattern" dataIndex="name" key="name" />

          <Column
            width={180}
            align="center"
            title="Thao tác"
            key="action"
            render={(text, record: Pattern) => (
              <Space>
                <Popconfirm
                  onConfirm={() => {
                    handleDelete(record.id);
                  }}
                  title="Xác nhận xóa"
                >
                  <Button loading={loadingDelete} danger>
                    Xóa
                  </Button>
                </Popconfirm>
                <Button
                  type="primary"
                  onClick={() => {
                    modalRef.current?.handleUpdate(record);
                  }}
                >
                  Cập nhật
                </Button>
              </Space>
            )}
          />
        </Table>

        <Pagination
          currentPage={query.page}
          total={total}
          onChange={({ limit, page }) => {
            query.page = page;
            query.limit = limit;
            setQuery({ ...query });
          }}
        />
      </Spin>

      <PatternModal onSubmitOk={fetchData} onClose={() => {}} ref={modalRef} />
    </div>
  );
};
