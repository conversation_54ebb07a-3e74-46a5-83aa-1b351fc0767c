import { materialApi } from "api/material.api";
import { useMemo, useState } from "react";
import { Material } from "types/material";
import { QueryParam } from "types/query";

export interface MaterialQuery extends QueryParam {}

interface UseMaterialProps {
  initQuery: MaterialQuery;
}

export const useMaterial = ({ initQuery }: UseMaterialProps) => {
  const [data, setData] = useState<Material[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<MaterialQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const isEmptyQuery = useMemo(
    () =>
      Object.keys(query).filter(
        (k) => query[k] && !["limit", "page", "queryObject", "type"].includes(k)
      ).length == 0,
    [query]
  );

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await materialApi.findAll(query);

      setData(data.materials);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return {
    materials: data,
    total,
    fetchData,
    loading,
    setQuery,
    query,
    setData,
    isEmptyQuery,
  };
};
