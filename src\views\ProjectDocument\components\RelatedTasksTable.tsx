import React, { useState, useEffect } from "react";
import { Table, Space, Button, Tooltip, Input, Card, message } from "antd";
import { CustomizableColumn } from "components/Table/CustomizableTable";
import EditButton from "components/Button/EditButton";
import DeleteButton from "components/Button/DeleteButton";
import ActiveStatusTag from "components/ActiveStatus/ActiveStatusTag";
import ProgressLegend from "components/ProgressLegend/ProgressLegend";
import { Task, TaskModule } from "types/task";
import { useNavigate } from "react-router-dom";
import { PermissionNames } from "types/PermissionNames";
import { checkRoles } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { observer } from "mobx-react";
import { ReactComponent as Copy } from "assets/svgs/copy.svg";
import dayjs from "dayjs";
import { useTask } from "hooks/useTask";
import CustomInput from "components/Input/CustomInput";
import CustomButton from "components/Button/CustomButton";
import { PlusIcon } from "assets/svgs/PlusIcon";
import { Modal } from "antd";
import CreateOrUpdateTaskPage from "views/Task/CreateOrUpdateTaskPage";

interface RelatedTasksTableProps {
  documentId: number;
  readonly?: boolean;
}

const RelatedTasksTable: React.FC<RelatedTasksTableProps> = ({
  documentId,
  readonly = false,
}) => {
  const navigate = useNavigate();
  const [search, setSearch] = useState("");
  const [showCreateModal, setShowCreateModal] = useState(false);

  const { haveAddPermission, haveEditPermission, haveBlockPermission } =
    checkRoles(
      {
        add: PermissionNames.taskAdd,
        edit: PermissionNames.taskEdit,
        block: PermissionNames.taskBlock,
      },
      permissionStore.permissions
    );

  const { tasks, loading, fetchData } = useTask({
    initQuery: {
      page: 1,
      limit: 5,
      moduleId: documentId,
      search,
    },
  });

  useEffect(() => {
    if (documentId) {
      fetchData();
    }
  }, [documentId, search]);

  const handleViewTask = (taskId: number) => {
    navigate(
      `/progress-management/${PermissionNames.taskEdit.replace(
        ":id",
        taskId.toString()
      )}`
    );
  };

  const handleEditTask = (taskId: number) => {
    navigate(
      `/progress-management/${PermissionNames.taskEdit.replace(
        ":id",
        taskId.toString()
      )}?update=1`
    );
  };

  const handleCopyTask = (task: Task) => {
    // TODO: Implement copy task functionality
    message.info("Chức năng sao chép công việc đang được phát triển");
  };

  const handleDeleteTask = async (task: Task) => {
    // TODO: Implement delete task functionality
    message.info("Chức năng xóa công việc đang được phát triển");
  };

  const canEditRecord = (task: Task) => {
    return haveEditPermission && !readonly;
  };

  const canDeleteRecord = (task: Task) => {
    return haveBlockPermission && !readonly;
  };

  const renderStaffRow = (staff: any) => {
    if (!staff) return "N/A";
    return (
      <div className="flex items-center gap-2">
        <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-white text-xs">
          {staff.fullName?.charAt(0) || "?"}
        </div>
        <span className="text-sm">{staff.fullName}</span>
      </div>
    );
  };

  const taskColumns: CustomizableColumn<Task>[] = [
    {
      key: "code",
      title: "ID",
      dataIndex: "code",
      width: 100,
      render: (_, record) => (
        <div
          className="text-[#1677ff] cursor-pointer"
          onClick={() => handleViewTask(record.id)}
        >
          {record.code}
        </div>
      ),
      defaultVisible: true,
      alwaysVisible: true,
    },
    {
      key: "title",
      title: "Công việc",
      dataIndex: "title",
      width: 200,
      defaultVisible: true,
      alwaysVisible: true,
    },
    {
      key: "assignee",
      title: "Người phụ trách",
      dataIndex: "assigneeMemberShip",
      width: 150,
      render: (_, record) => renderStaffRow(record.assigneeMemberShip?.staff),
      defaultVisible: true,
      alwaysVisible: true,
    },
    {
      key: "endDate",
      title: "Ngày đến hạn",
      dataIndex: "endDate",
      width: 120,
      render: (_, record) =>
        record.endDate ? dayjs(record.endDate).format("DD/MM/YYYY") : "N/A",
      defaultVisible: true,
      alwaysVisible: true,
    },
    {
      key: "status",
      title: "Trạng thái",
      dataIndex: "percentComplete",
      width: 120,
      render: (_, record) => (
        <ProgressLegend
          // percent={record.percentComplete || 0}
          // showText={true}
        />
      ),
      defaultVisible: true,
      alwaysVisible: true,
    },
    {
      key: "actions",
      title: "Hành động",
      align: "center",
      width: 120,
      render: (_, record) => (
        <Space size="small">
          {haveAddPermission && !readonly && (
            <Tooltip title="Tạo nhanh từ bản này">
              <Button
                type="text"
                icon={<Copy />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleCopyTask(record);
                }}
              />
            </Tooltip>
          )}
          {canEditRecord(record) && (
            <EditButton
              onClick={(e) => {
                e.stopPropagation();
                handleEditTask(record.id);
              }}
            />
          )}
          {canDeleteRecord(record) && (
            <DeleteButton
              onClick={(e) => {
                e.stopPropagation();
                handleDeleteTask(record);
              }}
            />
          )}
        </Space>
      ),
      defaultVisible: true,
      alwaysVisible: true,
    },
  ];

  return (
    <Card title="Công việc liên quan đến tài liệu" className="mt-4">
      <div className="flex flex-wrap gap-[16px] items-end max-w-full mb-4">
        <div className="w-[300px]">
          <CustomInput
            tooltipContent="Tìm theo mã, tên công việc"
            label="Tìm kiếm"
            placeholder="Tìm kiếm"
            value={search}
            onChange={(e) => setSearch(e)}
            onPressEnter={() => fetchData()}
            allowClear
          />
        </div>
        {haveAddPermission && !readonly && (
          <CustomButton
            size="small"
            icon={<PlusIcon />}
            onClick={() => setShowCreateModal(true)}
          >
            Thêm công việc
          </CustomButton>
        )}
      </div>

      <Table
        columns={taskColumns}
        dataSource={tasks}
        rowKey="id"
        loading={loading}
        pagination={false}
        bordered={false}
        size="small"
        className="related-tasks-table"
        onRow={(record) => ({
          onDoubleClick: () => {
            handleViewTask(record.id);
          },
        })}
      />

      <Modal
        open={showCreateModal}
        onCancel={() => setShowCreateModal(false)}
        footer={null}
        width={1800}
        destroyOnClose
        title="Tạo công việc liên quan đến tài liệu"
      >
        <CreateOrUpdateTaskPage
          title="Tạo công việc liên quan đến tài liệu"
          status="create"
          // onSuccess={() => {
          //   setShowCreateModal(false);
          //   fetchData();
          // }}
        />
      </Modal>
    </Card>
  );
};

export default observer(RelatedTasksTable);
