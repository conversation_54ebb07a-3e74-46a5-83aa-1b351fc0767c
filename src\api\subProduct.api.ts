import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const subProductApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/subProduct",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/subProduct",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/subProduct/${id}`,
      method: "patch",
      data,
    }),
  updatePos: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/subProduct/position`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/subProduct/${id}`,
      method: "delete",
    }),
};
