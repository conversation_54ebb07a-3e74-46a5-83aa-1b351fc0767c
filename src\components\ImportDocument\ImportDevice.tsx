import { DownloadOutlined, InboxOutlined } from "@ant-design/icons";
import { Alert, Modal, Space, Spin, Table, Upload, message } from "antd";
import { Rule } from "antd/es/form";
import { deviceApi } from "api/device.api";
import CustomButton from "components/Button/CustomButton";
import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
  useMemo,
} from "react";
import { Link } from "react-router-dom";
import { Device, DeviceType } from "types/device";
import { readerData } from "utils/excel2";
import ImportPreviewModule from "./ImportPreviewModule";
import { handleConfirmImportExcel } from "utils/function";

const rules: Rule[] = [{ required: true }];
const { Dragger } = Upload;

export interface ImportDeviceModal {
  open: () => void;
  close: () => void;
}

export interface IValidate {
  index: number;
  message: string;
}

interface DataImportReturn {
  msg: string;
  rowNum: number;
  status: string;
}

export interface DeviceImport extends Device {
  rowNum: number;
  deviceCategoryName?: string; // From Excel
  unitName?: string; // From Excel
  providerName?: string; // From Excel
  brandName?: string; // From Excel
}

interface IProps {
  onSuccess?: () => void;
  createApi?: (data: any) => any;
  onUploaded?: (excelData: any, setData: (data: any) => any) => void;
  demoExcel?: string;
  guide?: React.ReactNode[];
  uploadText?: string;
  okText?: string;
  onClose?: () => void;
  titleText?: string;
  validateMessage?: IValidate[];
  onDownloadDemoExcel?: () => Promise<void>;
  loadingDownloadDemo?: boolean;
  deviceType?: DeviceType; // Thêm prop để xác định loại device
}

const ImportDevice = forwardRef(
  (
    {
      onSuccess,
      createApi,
      onUploaded,
      onClose,
      validateMessage,
      guide,
      demoExcel,
      uploadText = "Kéo thả hoặc click vào đây để upload file",
      okText = "Nhập dữ liệu ngay",
      titleText = "Nhập excel dữ liệu",
      onDownloadDemoExcel,
      loadingDownloadDemo,
      deviceType,
    }: IProps,
    ref
  ) => {
    const [errorsLog, setErrorsLog] = useState<any[]>([]);
    const [dataPosts, setDataPosts] = useState<DeviceImport[]>([]);
    const [visible, setVisible] = useState<boolean>(false);
    const [loading, setLoading] = useState(false);
    const [hasValidationErrors, setHasValidationErrors] = useState(false);
    const [dataReturn, setDataReturn] = useState<{
      data: DataImportReturn[];
      successCount: number;
      errorCount: number;
    }>();

    useEffect(() => {
      if (validateMessage?.length) {
        setErrorsLog([]);
      }
    }, [validateMessage]);

    // Callback to receive validation status from ImportPreviewModule
    const handleValidationStatusChange = (hasErrors: boolean) => {
      console.log("🚨 Device validation status changed:", hasErrors);
      setHasValidationErrors(hasErrors);
    };

    // Tự động nhận diện loại device dựa trên dữ liệu hoặc prop
    const detectedDeviceType = useMemo(() => {
      if (deviceType) return deviceType;

      // Có thể phân tích dữ liệu để tự động phát hiện dựa trên các trường đặc trưng
      // Ví dụ: nếu có thông tin về máy móc (serial number, warranty) thì là Machine
      if (dataPosts.length > 0) {
        const hasMachineFields = dataPosts.some(
          (item) =>
            item.serialNumber &&
            (item.warrantyPeriodMonths !== undefined || item.warrantyConditions)
        );
        return hasMachineFields ? DeviceType.Machine : DeviceType.Equipment;
      }

      return DeviceType.Equipment; // Default
    }, [deviceType, dataPosts]);

    // Tự động tạo title và button text dựa trên loại device
    const dynamicTexts = useMemo(() => {
      const isMachine = detectedDeviceType === DeviceType.Machine;
      return {
        modalTitle:
          titleText || `Nhập excel ${isMachine ? "máy thi công" : "thiết bị"}`,
        previewTitle: `Xem danh sách ${
          isMachine ? "máy thi công" : "thiết bị"
        }`,
        previewButtonText: `Xem danh sách ${
          isMachine ? "máy thi công" : "thiết bị"
        }`,
      };
    }, [detectedDeviceType, titleText]);

    const handleOnImport = async () => {
      if (!dataPosts.length) return;
      let errors: any = [];

      try {
        setLoading(true);
        await handleConfirmImportExcel();
        const { data } = await deviceApi.import({
          devices: dataPosts.map((dataPost) => ({
            ...dataPost,
          })),
        });
        if (data.length) {
          const successCount = data.reduce(
            (acc: number, item: DataImportReturn) => {
              if (item.status == "ok") return acc + 1;
              return acc;
            },
            0
          );
          const errorCount = data.reduce(
            (acc: number, item: DataImportReturn) => {
              if (item.status == "error") return acc + 1;
              return acc;
            },
            0
          );
          if (errorCount == 0) {
            handleOnCancel();
          }
          setDataReturn({ data, successCount, errorCount });
          onSuccess?.();
          setDataPosts([]);
        }
      } catch (err) {
        console.log({ err });
      } finally {
        setLoading(false);
      }
    };

    const handleOnCancel = () => {
      setVisible(false);
      onClose?.();
    };

    useImperativeHandle(
      ref,
      () => ({
        open: () => setVisible(true),
        close: () => setVisible(false),
      }),
      []
    );

    // Define preview columns for the table - make dynamic based on deviceType
    const previewColumns = useMemo(() => {
      const isMachine = detectedDeviceType === DeviceType.Machine;

      return [
        {
          key: "rowNum",
          title: "Dòng excel",
          dataIndex: "rowNum",
          width: 100,
          render: (text: number) => <span>{text}</span>,
        },
        {
          key: "code",
          title: isMachine ? "Mã máy" : "Mã thiết bị",
          dataIndex: "code",
          render: (text: string) => (
            <span className={!text ? "text-gray-400" : ""}>
              {text || "Tự sinh"}
            </span>
          ),
        },
        {
          key: "name",
          title: isMachine ? "Tên máy thi công *" : "Tên thiết bị *",
          dataIndex: "name",
          render: (text: string, record: any) => (
            <span className={!text ? "text-red-500" : ""}>
              {text || "Thiếu"}
            </span>
          ),
        },
        {
          key: "deviceCategoryName",
          title: isMachine ? "Loại máy *" : "Nhóm thiết bị *",
          dataIndex: "deviceCategoryName",
          render: (text: string, record: any) => (
            <span className={!text ? "text-red-500" : ""}>
              {text || "Thiếu"}
            </span>
          ),
        },
        {
          key: "unitName",
          title: "Đơn vị tính *",
          dataIndex: "unitName",
          render: (text: string, record: any) => (
            <span className={!text ? "text-red-500" : ""}>
              {text || "Thiếu"}
            </span>
          ),
        },
        {
          key: "serialNumber",
          title: "Số hiệu",
          dataIndex: "serialNumber",
          render: (text: string) => (
            <span className={!text ? "text-gray-400" : ""}>
              {text || "Không có"}
            </span>
          ),
        },
        {
          key: "providerName",
          title: "Nhà cung cấp",
          dataIndex: "providerName",
          render: (text: string) => (
            <span className={!text ? "text-gray-400" : ""}>
              {text || "Không có"}
            </span>
          ),
        },
        {
          key: "brandName",
          title: "Thương hiệu",
          dataIndex: "brandName",
          render: (text: string) => (
            <span className={!text ? "text-gray-400" : ""}>
              {text || "Không có"}
            </span>
          ),
        },
        {
          key: "purchaseCost",
          title: "Chi phí mua",
          dataIndex: "purchaseCost",
          render: (text: number) => (
            <span>{text ? `${text.toLocaleString()} VNĐ` : "Không có"}</span>
          ),
        },
        {
          key: "description",
          title: "Mô tả",
          dataIndex: "description",
          render: (text: string) => (
            <span className={!text ? "text-gray-400" : ""}>
              {text || "Không có"}
            </span>
          ),
        },
        {
          key: "errorMessage",
          title: "Lỗi",
          dataIndex: "errorMessage",
          width: 300,
          render: (text: string) => (
            <span className="text-red-500 whitespace-pre-line text-xs">
              {text}
            </span>
          ),
        },
      ];
    }, [detectedDeviceType]);

    // Define required fields for validation - based on CreateDevicePage
    const requiredFields: (keyof DeviceImport)[] = [
      "name", // Tên thiết bị
      "deviceCategoryName", // Nhóm thiết bị (from Excel)
      "unitName", // Đơn vị tính (from Excel)
    ];

    const handleValidateData = (data: DeviceImport[]): DeviceImport[] => {
      console.log("🔍 handleValidateData called with:", data);
      return data.map((item) => {
        const additionalErrors: string[] = [];

        // Purchase cost validation (only if purchaseCost exists)
        if (item.purchaseCost !== undefined && item.purchaseCost !== null) {
          if (
            isNaN(Number(item.purchaseCost)) ||
            Number(item.purchaseCost) < 0
          ) {
            additionalErrors.push("Chi phí mua phải là số không âm");
          }
        }

        // Rental cost validation (only if rentalCost exists)
        if (item.rentalCost !== undefined && item.rentalCost !== null) {
          if (isNaN(Number(item.rentalCost)) || Number(item.rentalCost) < 0) {
            additionalErrors.push("Chi phí thuê phải là số không âm");
          }
        }

        // Warranty period validation (only if warrantyPeriodMonths exists)
        if (
          item.warrantyPeriodMonths !== undefined &&
          item.warrantyPeriodMonths !== null
        ) {
          if (
            isNaN(Number(item.warrantyPeriodMonths)) ||
            Number(item.warrantyPeriodMonths) < 0
          ) {
            additionalErrors.push("Số tháng bảo hành phải là số không âm");
          }
        }

        console.log(
          "🔍 Additional validation for item:",
          item,
          "additional errors:",
          additionalErrors
        );

        // Return item with additional errors (don't overwrite existing errorMessage)
        return {
          ...item,
          errorMessage:
            additionalErrors.length > 0
              ? additionalErrors.join("; ")
              : undefined,
        };
      });
    };

    return (
      <Modal
        maskClosable={false}
        width={1200} // Increase width to accommodate more columns
        style={{ top: 50 }}
        visible={visible}
        onCancel={handleOnCancel}
        destroyOnClose={true}
        afterClose={() => {
          setDataPosts([]);
          setErrorsLog([]);
          setDataReturn(undefined);
        }}
        title={dynamicTexts.modalTitle}
        footer={[
          <CustomButton
            key="import"
            loading={loading}
            variant="primary"
            disabled={!dataPosts.length || hasValidationErrors}
            onClick={() => {
              handleOnImport();
            }}
          >
            {okText}
          </CustomButton>,
          <CustomButton
            key="close"
            variant="outline"
            className="cta-button"
            onClick={() => {
              handleOnCancel();
            }}
          >
            Đóng
          </CustomButton>,
        ]}
      >
        <Spin spinning={false}>
          {guide && (
            <Alert
              style={{ padding: "10px", marginBottom: "10px" }}
              message={<b>Lưu ý</b>}
              type="warning"
              description={
                <ul>
                  {guide.map((text, index) => (
                    <li key={index}>
                      <p>{text}</p>
                    </li>
                  ))}
                </ul>
              }
            />
          )}
          {demoExcel && (
            <Link to={demoExcel} target="_blank" download>
              <Space className={`flex gap-2 cursor-pointer`}>
                <DownloadOutlined />
                Tải file import mẫu{" "}
              </Space>
            </Link>
          )}
          {onDownloadDemoExcel && (
            <a>
              <Space
                className={`flex gap-2 cursor-pointer`}
                onClick={async () => {
                  try {
                    await onDownloadDemoExcel();
                  } catch (error) {
                    console.log({ error });
                  }
                }}
                style={{ pointerEvents: loadingDownloadDemo ? "none" : "auto" }}
              >
                {loadingDownloadDemo ? (
                  <Spin spinning={loadingDownloadDemo} />
                ) : (
                  <DownloadOutlined />
                )}
                Tải file import mẫu
              </Space>
            </a>
          )}

          <Dragger
            style={{ marginTop: "0.5em" }}
            maxCount={1}
            multiple={false}
            beforeUpload={async (file) => {
              //Check file type
              const isCSVFile = file.name.includes("xlsx");
              if (isCSVFile === false) {
                message.error("Bạn chỉ có thể upload file excel!");
                return Upload.LIST_IGNORE;
              }
              const excelData = await readerData(file, 0);
              setDataReturn(undefined);
              console.log("Data khi import vào là", excelData);
              onUploaded?.(excelData, setDataPosts);
              return false;
            }}
            onChange={(info) => {
              //reset data
              if (info.fileList.length == 0) {
                setErrorsLog([]);
                setDataPosts([]);
              }
            }}
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">{uploadText}</p>
          </Dragger>

          {/* Import Preview Module */}
          <ImportPreviewModule
            data={dataPosts}
            dataReturn={dataReturn}
            onValidateData={handleValidateData}
            requiredFields={requiredFields}
            duplicateCheckFields={["code", "serialNumber", "name"]} // Kiểm tra trùng lặp mã thiết bị, số serial và tên thiết bị
            columns={previewColumns}
            title={dynamicTexts.previewTitle}
            previewButtonText={dynamicTexts.previewButtonText}
            onValidationStatusChange={handleValidationStatusChange}
          />
        </Spin>
        <Space
          style={{ width: "100%", justifyContent: "end", marginTop: "1em" }}
        ></Space>
      </Modal>
    );
  }
);

export default ImportDevice;
