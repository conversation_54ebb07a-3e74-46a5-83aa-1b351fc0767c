import { Select } from "antd";
import { DefaultOptionType } from "antd/es/select";
import { SelectProps } from "antd/lib";
import { useStaff } from "hooks/useStaff";
import { useStaff2 } from "hooks/useStaff2";
import { debounce, uniqBy } from "lodash";
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
} from "react";
import { QueryParams2 } from "types/query";
import { Staff } from "types/staff";

type CustomFormItemProps = {
  value?: number;
  initQuery?: QueryParams2;
  disabled?: boolean;
  multiple?: boolean;
  onChange?: (value: any) => void;
  selectProps?: SelectProps;
  initOptionItem?: Staff | Staff[];
  valueIsOption?: boolean;
  allowClear?: boolean;
  placeholder?: string;
  companyId?: number;
};

export interface MaterialGroupSelector {
  refresh(): void;
}

/**
 * A demo that changes it's number value on click.
 * @param value initialValue passed by parent Form.Item.
 * @param onChange a callback for Form.Item to read its child component's value.
 * @constructor
 */
export const StaffSelector = forwardRef(
  (
    {
      value,
      onChange,
      initQuery,
      disabled,
      multiple = false,
      initOptionItem,
      valueIsOption,
      selectProps,
      allowClear = true,
      placeholder = "Chọn nhân viên",
      companyId,
    }: CustomFormItemProps,
    ref
  ) => {
    const { staffs, total, loading, fetchData, query, setQuery } = useStaff2({
      initQuery: {
        page: 1,
        limit: 50,
        ...initQuery,
      },
    });

    useImperativeHandle<any, MaterialGroupSelector>(
      ref,
      () => ({
        refresh() {
          fetchData();
        },
      }),
      []
    );

    useEffect(() => {
      setQuery({ ...query, companyId });
      fetchData({ companyId });
    }, [companyId]);

    const debounceSearch = useCallback(
      debounce((keyword) => {
        query.search = keyword;
        fetchData();
      }, 300),
      [query]
    );

    const options = useMemo(() => {
      let data = [...staffs];
      if (initOptionItem) {
        if ((initOptionItem as Staff[])?.length) {
          data = data.concat(initOptionItem);
        } else {
          data.push(initOptionItem as Staff);
        }
      }

      return uniqBy(data, (data) => data.id);
    }, [staffs, initOptionItem]);

    return (
      <Select<any, DefaultOptionType>
        mode={multiple ? "multiple" : undefined}
        disabled={disabled}
        loading={loading}
        style={{ width: "100%" }}
        allowClear={allowClear}
        onSearch={debounceSearch}
        showSearch
        placeholder={placeholder}
        filterOption={false}
        value={value}
        optionLabelProp="children"
        onChange={(v, opts) => {
          if (v === undefined || v.length == 0) {
            debounceSearch(v);
          }
          if (valueIsOption) {
            if (v == undefined) {
              onChange?.(null);
            } else if (opts instanceof Array) {
              onChange?.(opts?.map((v) => v.item));
            } else {
              onChange?.(opts?.item);
            }
          } else {
            onChange?.(v);
          }
        }}
        {...selectProps}
      >
        {options?.map((item) => (
          <Select.Option item={item} value={item.id} key={item.id}>
            {item.fullName}
          </Select.Option>
        ))}
      </Select>
    );
  }
);
