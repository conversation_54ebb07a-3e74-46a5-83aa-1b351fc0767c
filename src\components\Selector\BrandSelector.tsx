import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
} from "react";
import { debounce, uniqBy } from "lodash";
import { useBrand } from "hooks/useBrand";
import { Brand } from "types/brand";
import { QueryParams2 } from "types/query";
import CustomSelect from "components/Input/CustomSelect";

type CustomFormItemProps = {
  value?: number;
  initQuery?: QueryParams2;
  disabled?: boolean;
  selectedBrand?: Brand[];
  multiple?: boolean;
  onChange?: (value: any) => void;
  initOptionItem?: Brand | Brand[];
  valueIsOption?: boolean;
  allowClear?: boolean;
  placeholder?: string;
};

export interface BrandSelector {
  refresh(): void;
}

export const BrandSelector = forwardRef(
  (
    {
      value,
      onChange,
      initQuery,
      disabled,
      multiple = false,
      selectedBrand,
      initOptionItem,
      valueIsOption,
      allowClear = true,
      placeholder = "Chọn thương hiệu",
    }: CustomFormItemProps,
    ref
  ) => {
    const { brands, loading, fetchData, query } = useBrand({
      initQuery: { page: 1, limit: 50, ...initQuery },
    });

    useImperativeHandle<any, BrandSelector>(
      ref,
      () => ({
        refresh() {
          fetchData();
        },
      }),
      []
    );

    useEffect(() => {
      fetchData();
    }, [selectedBrand]);

    const debounceSearch = useCallback(
      debounce((keyword) => {
        query.search = keyword;
        fetchData();
      }, 300),
      [query]
    );

    const options = useMemo(() => {
      let data = [...brands];
      if (initOptionItem) {
        if ((initOptionItem as Brand[])?.length) {
          data = data.concat(initOptionItem);
        } else {
          data.push(initOptionItem as Brand);
        }
      }
      return uniqBy(data, (item) => item.id).map((item) => ({
        label: item.name,
        value: item.id,
        item, // lưu nguyên object nếu cần dùng sau
      }));
    }, [brands, initOptionItem]);

    const handleChange = (v: any, option: any) => {
      if (valueIsOption) {
        if (option instanceof Array) {
          onChange?.(option.map((opt) => opt.item));
        } else {
          onChange?.(option.item);
        }
      } else {
        onChange?.(v);
      }
    };

    return (
      <CustomSelect
        value={value}
        onChange={handleChange}
        disabled={disabled}
        options={options}
        mode={multiple ? "multiple" : undefined}
        allowClear={allowClear}
        placeholder={placeholder}
        onSearch={debounceSearch}
        loading={loading}
      />
    );
  }
);
