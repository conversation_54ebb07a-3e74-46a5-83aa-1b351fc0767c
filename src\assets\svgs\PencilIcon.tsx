import { useTheme } from "context/ThemeContext";
import * as React from "react";

const PencilIcon = ({
  fill = "#050505",
  size = 20,
  ...props
}: React.SVGProps<SVGSVGElement> & { size?: number }) => {
  const { darkMode } = useTheme();
  if (darkMode) {
    fill = "#ffffff";
  }
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      fill={fill}
      viewBox="0 0 20 20"
      {...props}
    >
      <path
        fill={fill}
        d="m9.4 16.161 7.396-7.396a10.29 10.29 0 0 1-3.326-2.234 10.29 10.29 0 0 1-2.235-3.327L3.839 10.6c-.577.577-.866.866-1.114 1.184a6.556 6.556 0 0 0-.749 1.211c-.173.364-.302.752-.56 1.526L.054 18.604a1.06 1.06 0 0 0 1.342 1.342l4.083-1.362c.775-.258 1.162-.387 1.526-.56.43-.205.836-.456 1.211-.749.318-.248.607-.537 1.184-1.114ZM18.848 6.713a3.932 3.932 0 0 0-5.561-5.561l-.887.887.038.111a8.754 8.754 0 0 0 2.092 3.32 8.754 8.754 0 0 0 3.431 2.13l.887-.887Z"
      />
    </svg>
  );
};
export default PencilIcon;
