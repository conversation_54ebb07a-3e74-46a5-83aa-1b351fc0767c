import {
  Card,
  Col,
  Input,
  message,
  Row,
  Spin,
  DatePicker,
  Select,
  Form,
} from "antd";
import { documentApi } from "api/document.api";
import PageTitle from "components/PageTitle/PageTitle";
import React, { useEffect, useMemo, useState } from "react";
import { PermissionNames } from "types/PermissionNames";
import { getTitle } from "utils";
import { useParams, useSearchParams, useNavigate } from "react-router-dom";
import { Rule } from "antd/es/form";
import clsx from "clsx";
import { FileAttach } from "types/fileAttach";
import { DictionarySelector } from "components/Selector/DictionarySelector";
import { DictionaryType } from "types/dictionary";
import { FileUploadMultiple2 } from "components/Upload/FileUploadMultiple2";
import CustomButton from "components/Button/CustomButton";
import { isEmpty } from "lodash";
import dayjs from "dayjs";
import { settings } from "settings";
import ActiveStatusTagSelect from "components/ActiveStatus/ActiveStatusTagSelect";
import { Space } from "antd";
import { fileAttachApi } from "api/fileAttach.api";
import { $url } from "utils/url";
import { observer } from "mobx-react";
import { userStore } from "store/userStore";
import { TextInput } from "components/Input/TextInput";
import { checkRoles } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { DocumentType, DocumentModule } from "types/document";
import { BMDTextArea } from "components/TextArea/BMDTextArea";
import { BMDCKEditor } from "components/Editor/BMDCKEditor";

const { TextArea } = Input;
const rules: Rule[] = [{ required: true }];
const descriptionRules: Rule[] = [{ required: false }];

interface DocumentForm {
  code: string;
  name: string;
  categoryId?: number;
  departmentId?: number;
  createdById: number;
  createdByName: string;
  createdDate: any;
  releaseDate: any;
  isActive: boolean;
  description: string;
  files: FileAttach[];
  path: string;
}

interface CreateOrUpdateDocumentPayload {
  categoryId: number | null; // Cho phép null
  departmentId: number | null; // Cho phép null
  fileAttachIds: number[];
  createdById: number;
  projectId: number;
  document: {
    name: string;
    code: string;
    type: DocumentType;
    createdDate: string;
    releaseDate: string;
    isActive: boolean;
    description: string;
    path: string;
    module: DocumentModule;
  };
}

function CreateOrUpdateDocumentPage({
  title,
  status,
}: {
  title: string;
  status: string;
}) {
  const { haveEditPermission } = checkRoles(
    {
      edit: PermissionNames.documentEdit,
    },
    permissionStore.permissions
  );

  const [form] = Form.useForm<DocumentForm>();
  const [loading, setLoading] = useState(false);
  const [loadingFetch, setLoadingFetch] = useState(false);
  const [documentData, setDocumentData] = useState<any>(null);
  const [searchParams, setSearchParams] = useSearchParams();
  const [readonly, setReadonly] = useState(true);
  const [fileList, setFileList] = useState<FileAttach[]>([]);
  const [currentPath, setCurrentPath] = useState<string>("/");
  const [descriptionContent, setDescriptionContent] = useState<string>("");

  const params = useParams();
  const navigate = useNavigate();

  // Get current user info
  const currentUser = userStore.info;

  const setDataToForm = (data: any) => {
    form.setFieldsValue({
      ...data,
      categoryId: data.category?.id || undefined,
      departmentId: data.department?.id || undefined,
      createdDate: data.createdDate
        ? dayjs(data.createdDate, "YYYY-MM-DD")
        : undefined,
      releaseDate: data.releaseDate
        ? dayjs(data.releaseDate, "YYYY-MM-DD")
        : undefined,
      createdById: data.createdById || currentUser.id,
      createdByName:
        data.createdBy?.fullName ||
        data.createdBy?.name ||
        currentUser.fullName ||
        "",
      path: data.path || currentPath,
      description: data.description || "",
    });

    // Set description content for CKEditor
    setDescriptionContent(data.description || "");

    // Set files
    setFileList(data.fileAttaches ? [...data.fileAttaches] : []);
  };

  const getOneDocument = async (id: number) => {
    try {
      setLoadingFetch(true);
      const { data } = await documentApi.findOne(id);

      if (isEmpty(data)) {
        navigate("/404");
        return;
      }

      setDocumentData(data);
      setDataToForm(data);
    } catch (error) {
      message.error("Có lỗi xảy ra khi tải thông tin tài liệu!");
    } finally {
      setLoadingFetch(false);
    }
  };

  const getDataSubmit = async () => {
    const { categoryId, departmentId, createdDate, releaseDate, ...data } =
      form.getFieldsValue();

    const fileAttachIds: number[] = [];

    for (const file of fileList) {
      if (file.id) {
        fileAttachIds.push(file.id);
      } else if (file.originFile) {
        const { data: uploadData } = await fileAttachApi.upload(
          file.originFile
        );

        const resFileAttach = await fileAttachApi.create({
          fileAttach: {
            ...file,
            url: $url(uploadData.path),
          },
        });

        fileAttachIds.push(resFileAttach.data.id);
      }
    }

    const payload: CreateOrUpdateDocumentPayload = {
      categoryId: categoryId ?? 0,
      departmentId: departmentId ?? 0,
      fileAttachIds,
      createdById: currentUser.id || 0,
      projectId: 0,
      document: {
        ...data,
        createdDate: createdDate ? createdDate.format("YYYY-MM-DD") : "",
        releaseDate: releaseDate ? releaseDate.format("YYYY-MM-DD") : "",
        isActive: documentData?.isActive ?? true,
        type: DocumentType.Document,
        path: currentPath,
        module: DocumentModule.File,
      },
    };

    return payload;
  };

  const createData = async () => {
    const valid = await form.validateFields();

    setLoading(true);
    try {
      const payload = await getDataSubmit();
      await documentApi.create(payload);
      message.success("Tạo tài liệu thành công!");

      console.log("🔍 Current path when navigating back:", currentPath);
      console.log(
        "🔍 Navigate URL will be:",
        `/master-data/${PermissionNames.documentList}${
          currentPath !== "/" ? `?path=${encodeURIComponent(currentPath)}` : ""
        }`
      );

      navigate(
        `/master-data/${PermissionNames.documentList}${
          currentPath !== "/" ? `?path=${encodeURIComponent(currentPath)}` : ""
        }`
      );
      setFileList([]);
    } finally {
      setLoading(false);
    }
  };

  const updateData = async () => {
    const valid = await form.validateFields();

    setLoading(true);
    try {
      const payload = await getDataSubmit();
      await documentApi.update(documentData?.id || 0, payload);
      message.success("Chỉnh sửa tài liệu thành công!");
      getOneDocument(documentData?.id || 0);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = () => {
    if (status === "create") {
      createData();
    } else {
      updateData();
    }
  };

  const pageTitle = useMemo(() => {
    return status === "create" ? "Tạo tài liệu" : "Chỉnh sửa tài liệu";
  }, [status]);

  useEffect(() => {
    document.title = getTitle(title);

    // Get path from URL params with proper decoding
    const pathParam = searchParams.get("path");
    console.log("🔍 Raw pathParam from URL:", pathParam);

    let decodedPath = "/";
    if (pathParam) {
      try {
        // First decode URI component, then replace + with spaces for proper handling
        decodedPath = decodeURIComponent(pathParam.replace(/\+/g, " "));
        console.log("🔍 Decoded path:", decodedPath);
        setCurrentPath(decodedPath);
      } catch (error) {
        console.error("Error decoding path:", error);
        decodedPath = "/";
        setCurrentPath("/");
      }
    } else {
      setCurrentPath("/");
    }

    if (status === "create") {
      setReadonly(false);
      // Set default values for create mode
      form.setFieldsValue({
        createdById: currentUser.id,
        createdByName: currentUser.fullName || "",
        createdDate: dayjs(),
        path: decodedPath,
        description: "",
      });
      setDescriptionContent("");
    } else {
      const documentId = params.id;
      if (documentId) {
        getOneDocument(+documentId);
        setReadonly(searchParams.get("update") != "1");
      } else {
        navigate("/404");
      }
    }
  }, [status, params.id, title, searchParams]);

  return (
    <div className="app-container">
      <PageTitle
        back
        breadcrumbs={[
          { label: "Dữ liệu nguồn" },
          {
            label: "Danh sách tài liệu",
            href: `/master-data/${PermissionNames.documentList}${
              currentPath !== "/"
                ? `?path=${encodeURIComponent(currentPath)}`
                : ""
            }`,
          },
          { label: pageTitle },
        ]}
        title={pageTitle}
        extra={
          documentData &&
          status === "update" && (
            <Space>
              <ActiveStatusTagSelect
                disabled={readonly}
                isActive={documentData?.isActive}
                onChange={(value) => {
                  setDocumentData({
                    ...documentData,
                    isActive: value,
                  });
                  form.setFieldsValue({
                    isActive: value,
                  });
                }}
              />
            </Space>
          )
        }
      />

      <Card>
        <Spin spinning={loadingFetch}>
          <Form
            layout="vertical"
            form={form}
            className={clsx(readonly ? "readonly" : "")}
            disabled={readonly}
          >
            <Card title="Thông tin tài liệu" className="mb-4 form-card">
              {/* Hidden path field */}
              <Form.Item name="path" hidden>
                <Input />
              </Form.Item>

              {/* Row 1: Mã tài liệu, Tên tài liệu, Danh mục, Phòng ban */}
              <Row gutter={16}>
                <Col span={6}>
                  <Form.Item label="Mã tài liệu" name="code">
                    <TextInput
                      disabled={status == "update"}
                      placeholder={
                        status == "create" ? "Để trống sẽ tự sinh" : ""
                      }
                    />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="Tên tài liệu" name="name" rules={rules}>
                    <Input
                      placeholder="Nhập tên tài liệu"
                      disabled={readonly}
                    />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="Danh mục" name="categoryId">
                    <DictionarySelector
                      placeholder="Chọn danh mục"
                      initQuery={{
                        type: DictionaryType.Document,
                        isActive: true,
                      }}
                      showSearch
                      disabled={readonly}
                    />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="Phòng ban" name="departmentId">
                    <DictionarySelector
                      placeholder="Chọn phòng ban"
                      initQuery={{
                        type: DictionaryType.Department,
                        isActive: true,
                      }}
                      showSearch
                      disabled={readonly}
                    />
                  </Form.Item>
                </Col>
              </Row>

              {/* Row 2: Người tạo, Ngày tạo, Ngày ban hành, Trạng thái */}
              <Row gutter={16}>
                <Col span={6}>
                  <Form.Item label="Người tạo" name="createdByName">
                    <Input placeholder="Nhập tên người tạo" disabled />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="Ngày tạo" name="createdDate">
                    <DatePicker
                      placeholder="Chọn ngày tạo"
                      format={settings.dateFormat}
                      style={{ width: "100%" }}
                      disabled
                    />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="Ngày ban hành" name="releaseDate">
                    <DatePicker
                      placeholder="Chọn ngày ban hành"
                      format={settings.dateFormat}
                      style={{ width: "100%" }}
                      disabled={readonly}
                    />
                  </Form.Item>
                </Col>
              </Row>

              {/* Row 3: Mô tả */}
              <Row gutter={16}>
                <Col span={24}>
                  <Form.Item
                    label="Mô tả"
                    name="description"
                    rules={descriptionRules}
                  >
                    <BMDCKEditor
                      placeholder="Nhập mô tả tài liệu"
                      disabled={readonly}
                      inputHeight={300}
                      value={descriptionContent}
                      onChange={(content) => {
                        setDescriptionContent(content);
                        form.setFieldsValue({ description: content });
                      }}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Card>

            {/* Tệp đính kèm */}
            <Card title="Tệp đính kèm" className="mb-4 form-card">
              <Form.Item
                shouldUpdate={true}
                style={{ marginBottom: 0, height: "100%" }}
                className="form-height-full"
              >
                {() => {
                  return (
                    <Form.Item
                      label=""
                      noStyle
                      style={{ marginBottom: 0 }}
                      name="files"
                      className="h-full"
                    >
                      <FileUploadMultiple2
                        className="h-full"
                        fileList={fileList}
                        onUploadOk={(file) => {
                          fileList.push(file);
                          setFileList([...fileList]);
                        }}
                        onDelete={(file) => {
                          const findIndex = fileList.findIndex(
                            (e) => e.uid === file.uid
                          );

                          if (findIndex > -1) {
                            fileList.splice(findIndex, 1);
                            setFileList([...fileList]);
                          }
                        }}
                        hideUploadButton={readonly}
                      />
                    </Form.Item>
                  );
                }}
              </Form.Item>
            </Card>
          </Form>

          {/* Action Buttons */}
          <div className="flex gap-[16px] justify-end mt-2">
            {!readonly && (
              <CustomButton
                variant="outline"
                className="cta-button"
                onClick={() => {
                  if (status === "create") {
                    navigate(
                      `/master-data/${PermissionNames.documentList}${
                        currentPath !== "/"
                          ? `?path=${encodeURIComponent(currentPath)}`
                          : ""
                      }`
                    );
                  } else {
                    setDataToForm(documentData!);
                    setReadonly(true);
                    setDescriptionContent(documentData?.description || "");
                  }
                }}
              >
                Hủy
              </CustomButton>
            )}

            <CustomButton
              className="cta-button"
              loading={loading}
              onClick={() => {
                if (!readonly) {
                  handleSubmit();
                } else {
                  setReadonly(false);
                }
              }}
              disabled={status == "update" && !haveEditPermission}
            >
              {status === "create"
                ? "Tạo tài liệu"
                : readonly
                ? "Chỉnh sửa"
                : "Lưu chỉnh sửa"}
            </CustomButton>
          </div>
        </Spin>
      </Card>
    </div>
  );
}

export default observer(CreateOrUpdateDocumentPage);
