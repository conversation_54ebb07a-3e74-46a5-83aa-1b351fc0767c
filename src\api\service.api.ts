import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const serviceApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/service",
      params,
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/service/${id}`,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/service",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/service/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/service/${id}`,
      method: "delete",
    }),
  import: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/service/import",
      data,
      method: "post",
    }),
};
