.site-layout-background {
  .anticon.trigger {
    color: #4d74fc;
  }

  .nav-bar-item-wrapper {
    background-color: var(--color-neutral-n1);
    border: 1px solid var(--color-neutral-n2);
    border-radius: 22px;

    .ant-scroll-number-custom-component {
      background-color: var(--color-accent);
      border: 2px solid var(--color-neutral-n0);
      padding: 3px 5px;
      border-radius: 10px;
      font-size: 10px;
      color: #ffffff;
      font-weight: 700;
    }

    .language-text,
    .language-icon {
      color: var(--color-neutral-n8);
    }

    .bell-icon path {
      stroke: var(--color-neutral-n8);
    }
  }

  .nav-bar-avatar {
    .-full-name,
    .-role,
    .-icon {
      color: var(--color-neutral-n8);
    }
  }
}

.ant-layout {
  .trigger {
    padding: 0 24px;
    font-size: 18px;
    line-height: 64px;
    cursor: pointer;
    transition: color 0.3s;

    &:hover {
      color: #1890ff;
    }
  }

  .logo {
    width: 100%;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;

    // background: rgba(255, 255, 255, 0.3);

    img {
      width: 120px;
      // border-radius: 5px;
    }
  }
  .logo-small {
    width: 100%;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;

    // background: rgba(255, 255, 255, 0.3);

    img {
      width: 60px;
      // border-radius: 5px;
    }
  }
}

// header
.ant-layout-header {
  position: fixed;
  width: calc(100%);
  top: 0;
  right: 0;
  z-index: 100;
  display: flex;
  height: 68px;
  justify-content: space-between;
  transition: width 0.2s;
  &.collapsed {
    width: calc(100%);
  }
}

// content
.ant-layout-content {
  margin-top: 64px !important;
  min-height: calc(100vh - 64px);

  overflow: auto;
}

// .ant-layout-sider {
//   flex: 0 0 250px !important;
//   max-width: 250px !important;
//   min-width: 250px !important;
//   width: 250px !important;
// }

// .ant-layout-sider-collapsed {
//   flex: 0 0 80px !important;
//   max-width: 80px !important;
//   min-width: 80px !important;
//   width: 80px !important;
// }

.site-layout {
  transition: margin-left 0.2s;
  // margin-left: 250px !important;
}

// sider
.ant-layout-sider {
  position: fixed;
  height: 100vh;
}

.ant-menu-root {
  li:first-child {
    margin-top: 0;
  }
}

@media screen and (max-width: 500px) {
  .mobile-breadcrumb-none {
    display: none !important;
  }
}

.page-container {
  padding: 24px;
  background-color: var(--color-neutral-n0);
  border: 1px solid var(--color-neutral-n2);
}

.custom-pagination {
  .custom-input-container {
    .ant-select {
      min-width: unset;
    }
  }

  .ant-pagination {
    .ant-pagination-prev {
      margin-right: 4px;
    }
    .ant-pagination-next {
      margin-left: 4px;
    }

    .ant-pagination-item {
      border: none;
      height: 36px;
      width: 36px;
      border-radius: 5px;

      &.ant-pagination-item-active {
        background-color: var(--color-primary);
        a {
          color: #ffffff;
        }
      }

      a {
        color: var(--color-neutral-n5);
        font-weight: 700;
        line-height: 34px;
      }
    }
  }
}

.service-file-icon {
  path {
    fill: var(--color-neutral-n8);
  }
}
