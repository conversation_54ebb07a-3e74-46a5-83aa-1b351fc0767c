import { seasonApi } from "api/season.api";
import { useState } from "react";
import { Season, SeasonType } from "types/season";
import { QueryParam } from "types/query";

export interface SeasonQuery extends QueryParam {}

interface UseSeasonProps {
  initQuery: SeasonQuery;
}

export const useSeason = ({ initQuery }: UseSeasonProps) => {
  const [data, setData] = useState<SeasonType[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<SeasonQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await seasonApi.findAll(query);

      setData(data.seasons);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return { seasons: data, total, fetchData, loading, setQuery, query };
};
