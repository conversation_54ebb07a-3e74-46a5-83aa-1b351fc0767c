import {
  Card,
  Spin,
  Button,
  Space,
  Tooltip,
  Modal,
  Tag,
  Avatar,
  message,
  Select,
} from "antd";
import PencilIcon from "assets/svgs/PencilIcon";
import CustomButton from "components/Button/CustomButton";
import CustomInput from "components/Input/CustomInput";
import PageTitle from "components/PageTitle/PageTitle";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import { Pagination } from "components/Pagination";
import { useTheme } from "context/ThemeContext";
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { PermissionNames } from "types/PermissionNames";
import dayjs from "dayjs";
import { checkRoles, filterActionColumnIfNoPermission } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { observer } from "mobx-react";
import { $url } from "utils/url";
import logoImage from "assets/images/logo.png";
import { Staff } from "types/staff";
import { unixToDate } from "utils/dateFormat";
import DeleteIcon from "assets/svgs/DeleteIcon";
import QueryLabel from "components/QueryLabel/QueryLabel";
import { DictionarySelector } from "components/Selector/DictionarySelector";
import { DictionaryType } from "types/dictionary";
import { use } from "echarts";
import { projectItemApi } from "api/projectItem.api";
import { useProjectItem } from "hooks/useProjectItem";
import { TableProps } from "antd/lib";
import { ReactComponent as Copy } from "assets/svgs/copy.svg";
import LockButton from "components/Button/LockButton";
import { getTitle } from "utils";
import { appStore } from "store/appStore";

interface ProjectItemPageProps {
  title: string;
  projectId?: number;
  hidePageTitle?: boolean;
}

function ProjectItemPage({
  title,
  projectId,
  hidePageTitle = false,
}: ProjectItemPageProps) {
  const {
    haveAddPermission,
    haveDeletePermission,
    haveEditPermission,
    haveBlockPermission,
    haveViewAllPermission,
  } = checkRoles(
    {
      add: PermissionNames.projectItemAdd,
      edit: PermissionNames.projectItemEdit,
      delete: PermissionNames.projectItemDelete,
      block: PermissionNames.projectItemBlock,
      viewAll: PermissionNames.projectItemViewAll,
    },
    permissionStore.permissions
  );

  const [searchValue, setSearchValue] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [loadingDelete, setLoadingDelete] = useState(false);
  const [page, setPage] = React.useState(1);
  const [limit, setLimit] = React.useState(20);

  useEffect(() => {
    document.title = getTitle(title);
  }, []);

  const {
    fetchData,
    projectItems,
    loading,
    query,
    setQuery,
    total,
    isEmptyQuery,
  } = useProjectItem({
    initQuery: {
      limit: 10,
      page: 1,
      isAdmin: haveViewAllPermission ? true : undefined,
      projectId: appStore.currentProject?.id,
    },
  });

  useEffect(() => {
    fetchData();
  }, []);

  const handleTableChange: TableProps<any>["onChange"] = (
    pagination,
    filters,
    sorter
  ) => {
    if (!Array.isArray(sorter)) {
      const fieldMap: Record<string, string> = {
        code: "projectItem.code",
        floors: "projectItem.floors",
        name: "projectItem.name",
        area: "projectItem.area",
        note: "projectItem.note",
        isActive: "projectItem.isActive",
      };
      const columnKey = sorter.field || sorter.column?.key;

      if (!sorter.order) {
        // setSortField(null);
        // setSortOrder(null);
        query.queryObject = undefined;
        setQuery({ ...query });
      } else {
        const order = sorter.order === "ascend" ? "ASC" : "DESC";
        // setSortField("jobCategory.name");
        // setSortOrder(order);
        const field = fieldMap[columnKey as string] || columnKey;
        const newQueryObject = JSON.stringify([
          {
            type: "sort",
            field,
            value: order,
          },
        ]);
        query.queryObject = newQueryObject;
        setQuery({ ...query });
      }
      fetchData();
    } else {
      query.queryObject = undefined;
      setQuery({ ...query });
      fetchData();
    }
  };

  const { darkMode } = useTheme();
  const navigate = useNavigate();

  const handleSearch = (value: string) => {
    setSearchValue(value);
  };

  const handleStatusChange = (value: string) => {
    setStatusFilter(value);
    const newQuery = {
      ...query,
      page: 1,
      status: value || undefined,
    };
    setQuery(newQuery);

    setTimeout(() => {
      fetchData();
    }, 100);
  };

  const handleCreateBlueprint = () => {
    navigate(`/report/${PermissionNames.projectItemAdd}`);
  };

  const handleDeleteProjectItem = async (id: number) => {
    try {
      await projectItemApi.delete(id);
      message.success("Xóa hạng mục thành công");
      fetchData();
    } catch (e) {
      console.log({ e });
    } finally {
    }
  };

  const handleActiveReport = async (id: number, isActive: boolean) => {
    // Tìm record hiện tại
    const record = projectItems?.find((item) => item.id === id);
    if (!record) return;

    try {
      await projectItemApi.update(id, {
        projectId: appStore.currentProject?.id,
        projectItem: {
          code: record.code,
          name: record.name,
          area: record.area,
          isActive: !isActive,
          note: record.note,
          floors: record.floors,
        },
        projectItemDetails: record.projectItemDetails || [],
      });
      message.success(!isActive ? "Mở khóa thành công" : "Khóa thành công");
      fetchData();
    } catch (error) {
      message.error("Có lỗi xảy ra khi thay đổi trạng thái");
    }
  };

  const handleRowClick = (record: any) => {
    navigate(
      `/report/${PermissionNames.projectItemEdit.replace(
        ":id",
        record!.id + ""
      )}`
    );
  };

  const columns: CustomizableColumn<any>[] = [
    {
      key: "STT",
      title: "STT",
      dataIndex: "STT",
      width: 50,
      defaultVisible: true,
      alwaysVisible: false,
      align: "left",
      render: (_, __, index) => {
        return (
          <div className="text-left">
            {query.page && query.limit
              ? (query.page - 1) * query.limit + index + 1
              : index + 1}
          </div>
        );
      },
    },
    {
      key: "code",
      title: "Mã",
      dataIndex: "code",
      width: 100,
      defaultVisible: true,
      alwaysVisible: false,
      align: "left",
      sorter: true,
      render: (_, record) => {
        return (
          <div
            className="text-[#1677ff] cursor-pointer"
            onClick={() => handleRowClick(record)}
          >
            {record.code}
          </div>
        );
      },
    },
    {
      key: "name",
      title: "Tên hạng mục",
      dataIndex: "name",
      width: 200,
      defaultVisible: true,
      alwaysVisible: false,
      sorter: true,
    },
    {
      key: "area",
      title: "Diện tích",
      dataIndex: "area",
      width: 100,
      defaultVisible: true,
      alwaysVisible: false,
      sorter: true,
      align: "right",
    },
    {
      key: "floors",
      title: "Số tầng",
      dataIndex: "floors",
      width: 100,
      defaultVisible: true,
      alwaysVisible: false,
      align: "right",
    },
    // {
    //   key: "note",
    //   title: "Ghi chú",
    //   dataIndex: "note",
    //   width: 100,
    //   defaultVisible: true,
    //   alwaysVisible: false,
    //   sorter: true,
    // },
    {
      key: "status",
      title: "Trạng thái",
      width: 100,
      align: "left",
      dataIndex: "status",

      render: (status, record) => (
        <div className="justify-left flex">
          {record.isActive ? (
            <Tag color="green" className="status-tag !mr-0">
              Hoạt động
            </Tag>
          ) : (
            <Tag color="red" className="status-tag !mr-0">
              Bị khóa
            </Tag>
          )}
        </div>
      ),
      defaultVisible: true,
    },
    {
      key: "actions",
      title: "Xử lý",
      width: 100,
      align: "center",
      fixed: "right",
      defaultVisible: true,
      alwaysVisible: true,
      render: (_, record) => (
        <Space size="small">
          {/* Nút Copy để đầu tiên và dùng icon của Ant Design */}
          <Tooltip title="Tạo nhanh từ bản này">
            <Button
              type="text"
              icon={<Copy />}
              onClick={async (e) => {
                e.stopPropagation();
                const { data } = await projectItemApi.findOne(record.id);
                navigate(`/report/${PermissionNames.projectItemAdd}`, {
                  state: { copyData: data },
                });
              }}
            />
          </Tooltip>

          {haveEditPermission && (
            <Tooltip title="Chỉnh sửa">
              <Button
                type="text"
                icon={<PencilIcon fill={darkMode ? "#ffffff" : "#050505"} />}
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(
                    `/report/${PermissionNames.projectItemEdit.replace(
                      ":id",
                      record!.id + ""
                    )}?update=1`
                  );
                }}
              />
            </Tooltip>
          )}

          {haveDeletePermission && (
            <Tooltip title="Xóa">
              <Button
                type="text"
                danger
                icon={<DeleteIcon />}
                onClick={(e) => {
                  e.stopPropagation();
                  Modal.confirm({
                    title: `Xóa bản vẽ "${record.name}"`,
                    getContainer: () => {
                      return document.getElementById("App") as HTMLElement;
                    },
                    icon: null,
                    content: (
                      <>
                        <div>
                          Bạn sẽ không thể khôi phục dữ liệu này sau khi xóa.
                          <br />
                          Bạn có chắc chắn muốn xóa dữ liệu này?
                        </div>
                      </>
                    ),
                    footer: (_, { OkBtn, CancelBtn }) => (
                      <>
                        <CustomButton
                          variant="outline"
                          className="cta-button"
                          onClick={() => {
                            handleDeleteProjectItem(record.id);
                            Modal.destroyAll();
                          }}
                        >
                          Có
                        </CustomButton>
                        <CustomButton
                          onClick={() => {
                            Modal.destroyAll();
                          }}
                          className="cta-button"
                        >
                          Không
                        </CustomButton>
                      </>
                    ),
                  });
                }}
              />
            </Tooltip>
          )}

          {haveBlockPermission && (
            <LockButton
              isActive={record.isActive}
              onAccept={() => handleActiveReport(record.id, record.isActive)}
              modalTitle={`${record.isActive ? "Khóa" : "Mở khóa"} hạng mục: ${
                record.note
              }`}
              modalContent={
                <>
                  <div>
                    Khi {record.isActive ? "khóa" : "mở khóa"} hạng mục này,
                    trạng thái của hạng mục sẽ được thay đổi.
                    <br />
                    Bạn có chắc chắn muốn {record.isActive
                      ? "khóa"
                      : "mở khóa"}{" "}
                    hạng mục này?
                  </div>
                </>
              }
            />
          )}
        </Space>
      ),
    },
  ];

  return (
    <div>
      {/* Nút trên cùng */}

      {!hidePageTitle && (
        <PageTitle
          title={title}
          breadcrumbs={["Báo cáo", title]}
          extra={
            haveAddPermission && (
              <CustomButton
                size="small"
                showPlusIcon
                onClick={handleCreateBlueprint}
              >
                Tạo hạng mục
              </CustomButton>
            )
          }
        />
      )}

      <div className={hidePageTitle ? "" : "app-container"}>
        <Card>
          <div className="pb-[16px]">
            <div className="flex justify-between items-end mb-4">
              {/* Filter section */}
              <div className="flex gap-4">
                <div className="flex gap-[16px] items-center w-[300px]">
                  <CustomInput
                    label="Tìm kiếm"
                    tooltipContent={"Tìm theo mã và tên hạng mục"}
                    placeholder="Tìm kiếm"
                    onPressEnter={() => {
                      query.page = 1;
                      setQuery({ ...query });
                      fetchData();
                    }}
                    value={query.search}
                    onChange={(value) => {
                      query.search = value;
                      setQuery({ ...query });

                      if (!value) {
                        fetchData();
                      }
                    }}
                    allowClear
                  />
                </div>

                <div className="flex flex-col">
                  <QueryLabel>Trạng thái</QueryLabel>
                  <Select
                    placeholder="Chọn trạng thái"
                    allowClear={true}
                    value={query.isActive}
                    onChange={(value) => {
                      query.isActive = value;
                      setQuery({ ...query });
                    }}
                    style={{
                      minWidth: 200,
                    }}
                    showSearch
                    filterOption={(input, option) =>
                      (typeof option?.label === "string"
                        ? option.label
                        : String(option?.label ?? "")
                      )
                        .toLowerCase()
                        .includes(input.toLowerCase())
                    }
                    options={[
                      { value: true, label: "Hoạt động" },
                      { value: false, label: "Bị khóa" },
                    ]}
                  />
                </div>

                {/* Thêm div wrapper cho buttons với style align */}
                <div className="flex flex-col justify-end">
                  <div className="flex gap-2">
                    <CustomButton
                      onClick={() => {
                        if (!query.drawCategoryId) delete query.drawCategoryId;
                        if (query.isActive === undefined) delete query.isActive;
                        query.page = 1;
                        query.projectId = appStore.currentProject?.id || 0;
                        setQuery({ ...query });
                        fetchData();
                      }}
                    >
                      Áp dụng
                    </CustomButton>

                    {!isEmptyQuery && (
                      <CustomButton
                        variant="outline"
                        onClick={() => {
                          delete query.drawCategoryId;
                          delete query.isActive;
                          delete query.search;
                          delete query.queryObject;
                          query.page = 1;
                          query.projectId = appStore.currentProject?.id || 0;
                          setQuery({ ...query });
                          fetchData();
                        }}
                      >
                        Bỏ lọc
                      </CustomButton>
                    )}
                  </div>
                </div>
              </div>

              {/* Nút tạo hạng mục chỉ hiện khi !hidePageTitle */}
              {hidePageTitle && haveAddPermission && (
                <CustomButton
                  size="small"
                  onClick={handleCreateBlueprint}
                  className="whitespace-nowrap"
                >
                  + Tạo hạng mục
                </CustomButton>
              )}
            </div>
            <Spin spinning={loading}>
              <CustomizableTable
                columns={filterActionColumnIfNoPermission(columns, [
                  haveEditPermission,
                  haveDeletePermission,
                ])}
                dataSource={projectItems}
                rowKey="id"
                // loading={loadingprojectItem}
                pagination={false}
                scroll={{ x: 1200 }}
                onChange={handleTableChange}
                bordered
                displayOptions
                onRowClick={handleRowClick}
              />
            </Spin>
          </div>

          <Pagination
            currentPage={query.page}
            total={total}
            defaultPageSize={query.limit}
            onChange={({ limit, page }) => {
              query.page = page;
              query.limit = limit;
              query.projectId = appStore.currentProject?.id || 0;
              setQuery({ ...query });
              setPage(page);
              setLimit(limit);
              fetchData();
            }}
          />
        </Card>
      </div>
    </div>
  );
}

export default observer(ProjectItemPage);
