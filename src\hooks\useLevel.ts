import { levelApi } from "api/level.api";
import { useState } from "react";
import { Level } from "types/level";
import { QueryParam } from "types/query";

export interface LevelQuery extends QueryParam {}

interface UseLevelProps {
  initQuery: LevelQuery;
}

export const useLevel = ({ initQuery }: UseLevelProps) => {
  const [data, setData] = useState<Level[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<LevelQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await levelApi.findAll(query);

      setData(data.levels);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return { levels: data, total, fetchData, loading, setQuery, query };
};
