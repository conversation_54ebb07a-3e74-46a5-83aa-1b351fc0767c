import { action, makeAutoObservable } from "mobx";
import { makePersistable } from "mobx-persist-store";
import { Project } from "types/project";
import { permissionStore } from "./permissionStore";
import { userStore } from "./userStore";
import { settings } from "settings";

class AppStore {
  constructor() {
    makeAutoObservable(this);
    makePersistable(this, {
      name: "AppStore",
      properties: ["currentProject", "unreadNotificationCount"],
      storage: window.localStorage,
    });
  }

  currentProject?: Project = undefined;
  lastUrl = "";
  unreadNotificationCount: number = 0;

  @action
  async setCurrentProject(project: Project) {
    if (settings.checkPermission && userStore.info.role) {
      // debugger;
      await permissionStore.fetchPermissions(userStore.info.role?.id);
      permissionStore.setAccessRoutes();
    }
    this.currentProject = project;
  }
  @action
  async clearCurrentProject() {
    if (settings.checkPermission && userStore.info.role) {
      await permissionStore.fetchPermissions(userStore.info.role?.id);
      permissionStore.setAccessRoutes();
    }
    this.currentProject = undefined;
  }

  @action
  setUnreadNotificationCount(count: number) {
    this.unreadNotificationCount = count;
  }
}

const appStore = new AppStore();

export { appStore };
