.bmd-ckeditor {
  .ck-editor__editable {
    min-height: 300px;
    max-height: 500px;
    overflow-y: auto;
    
    &:focus {
      box-shadow: none;
      border-color: #1890ff;
    }
  }

  .ck-toolbar {
    border-radius: 6px 6px 0 0;
    border-color: #d9d9d9;
  }

  .ck-content {
    border-radius: 0 0 6px 6px;
    border-color: #d9d9d9;
    border-top: none;
  }

  &.disabled {
    .ck-editor__editable {
      background-color: #f5f5f5;
      cursor: not-allowed;
    }
  }

  // Custom styling for Vietnamese language
  .ck-editor__editable {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif;
    font-size: 14px;
    line-height: 1.5715;
  }

  // Table styling
  .ck-content table {
    border-collapse: collapse;
    margin: 0;
    overflow: hidden;
    table-layout: fixed;
    width: 100%;
  }

  .ck-content table td,
  .ck-content table th {
    border: 2px solid #bfbfbf;
    padding: 0.75rem;
    position: relative;
    vertical-align: top;
  }

  .ck-content table th {
    background-color: #f8f9fa;
    font-weight: bold;
  }

  // Image styling
  .ck-content figure {
    margin: 1em 0;
    text-align: center;
  }

  .ck-content figure img {
    max-width: 100%;
    height: auto;
  }

  .ck-content figure figcaption {
    color: #666;
    font-size: 0.875em;
    margin-top: 0.5em;
  }
} 