import { useTheme } from "context/ThemeContext";
import * as React from "react";

const CameraTimeLapseIcon = ({ fill = "#19345B" }) => {
  const { darkMode } = useTheme();
  if (darkMode) {
    fill = "#ffffff";
  }
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width={20} height={20} fill={fill}>
      <path
        fill={fill}
        fillRule="evenodd"
        d="M11.646 16.667H8.354c-2.312 0-3.468 0-4.298-.545a3.264 3.264 0 0 1-.908-.892c-.555-.815-.555-1.95-.555-4.22 0-2.27 0-3.405.555-4.22.24-.353.548-.656.908-.892.533-.35 1.201-.475 2.224-.52.488 0 .908-.363 1.004-.833A1.529 1.529 0 0 1 8.79 3.333h2.42c.732 0 1.363.508 1.506 ************.516.833 1.004.833 1.023.045 1.69.17 ***********.236.669.54.909.892.555.815.555 1.95.555 4.22 0 2.27 0 3.405-.555 4.22-.24.353-.55.656-.909.892-.83.545-1.986.545-4.298.545Zm.776-8.445a.556.556 0 0 0-1.11 0v.106a2.903 2.903 0 0 0-3.364 4.642 2.902 2.902 0 0 0 4.935-1.722.556.556 0 0 0-1.104-.126A1.79 1.79 0 1 1 10.506 9.2a.556.556 0 0 0 .35 1.047l1.125-.237a.556.556 0 0 0 .441-.544V8.222Z"
        clipRule="evenodd"
      />
    </svg>
  );
};
export default CameraTimeLapseIcon;
