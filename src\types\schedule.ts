import { MemberShip } from "./memberShip";
import { Staff } from "./staff";

export interface Schedule {
  id: number;
  TaskID: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  code: string;
  TaskName: string;
  StartDate: string;
  EndDate: string;
  Duration: number; // thời hạn task
  work: string; // số giờ làm
  Progress: number; // phân trăm hoàng thành
  Predecessor: string; // phụ thuộc
  Money: number; // số tiền
  info: string; // số tiền
  parent: Schedule | null;
  subtasks: Schedule[];
  createdBy: Staff;
  updatedBy: Staff;
  resources: MemberShip[];
  position: number;
}
