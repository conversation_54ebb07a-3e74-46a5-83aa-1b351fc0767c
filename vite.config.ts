/// <reference types="vite/client" />
import { defineConfig } from "vite";
import tsconfigPaths from "vite-tsconfig-paths";
import react from "@vitejs/plugin-react";
import viteTsconfigPaths from "vite-tsconfig-paths";
import svgrPlugin from "vite-plugin-svgr";
import { esbuildCommonjs } from "@originjs/vite-plugin-commonjs";
import nodeResolve from "@rollup/plugin-node-resolve";
import rollupNodePolyFill from "rollup-plugin-node-polyfills";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), viteTsconfigPaths(), svgrPlugin(), tsconfigPaths()],
  resolve: {
    alias: {
      "./runtimeConfig": "./runtimeConfig.browser",
    },
  },
  server: {
    // open: true,
    port: 5175,
    host: true,
  },
  build: {
    rollupOptions: {
      plugins: [
        nodeResolve({
          browser: true,
          preferBuiltins: false,
        }),
      ],
    },
  },
  // mode: "development",
  // build: {
  //   minify: false,
  // },
  css: {
    preprocessorOptions: {
      less: {
        modifyVars: {
          "primary-color": "#4d74fc",
          "secondary-color": "#16aced",
        },
        javascriptEnabled: true,
        additionalData: "@root-entry-name: default;",
      },
    },
  },

  // optimizeDeps: {
  //   esbuildOptions: {
  //     plugins: [esbuildCommonjs(["react-calendar", "react-date-picker"])],
  //   },
  // },
});
