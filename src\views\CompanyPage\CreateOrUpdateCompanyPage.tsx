import {
  Card,
  Col,
  DatePicker,
  Form,
  Input,
  message,
  Row,
  Space,
  Spin,
} from "antd";
import { Rule } from "antd/lib/form";
import { useEffect, useMemo, useState } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { getTitle } from "utils";
import { isEmpty } from "lodash";
import clsx from "clsx";
import { observer } from "mobx-react";
import CustomButton from "components/Button/CustomButton";
import PageTitle from "components/PageTitle/PageTitle";
import { settings } from "settings";
import CustomDatePicker from "components/Input/CustomDatePicker";
import { checkRoles } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { PermissionNames } from "types/PermissionNames";
import { appStore } from "store/appStore";
import { companyApi } from "api/company.api";
import dayjs from "dayjs";
import { phoneNumberRule } from "utils/phoneInput";
import ActiveStatusTagSelect from "components/ActiveStatus/ActiveStatusTagSelect";
import { Company } from "types/company";
import { phoneValidate } from "utils/validateRule";
import { BMDTextArea } from "components/TextArea/BMDTextArea";
import { BMDCKEditor } from "components/Editor";

const rules: Rule[] = [{ required: true }];

interface EditServicePageProps {
  title: string;
  status: "create" | "update";
}

interface CompanyForm {
  id: number;
  name: string;
  code: string;
  phone: string;
  phone2: string;
  otherContact: string;
  email: string;
  address: string;
  taxCode: string;
  startAt: number;
  note: string;
  isActive: boolean;
}

const descriptionRules: Rule[] = [{ required: false }];

export const CreateOrUpdateCompanyPage = observer(
  ({ title = "", status }: EditServicePageProps) => {
    const { haveEditPermission } = checkRoles(
      { edit: PermissionNames.serviceEdit },
      permissionStore.permissions
    );

    const [form] = Form.useForm<CompanyForm>();
    const [loading, setLoading] = useState(false);
    const [loadingFetch, setLoadingFetch] = useState(false);
    const [readonly, setReadonly] = useState(true);
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const params = useParams();
    const [selectedCompany, setSelectedCompany] = useState<CompanyForm>();

    useEffect(() => {
      document.title = getTitle(title);

      if (status === "update") {
        const companyId = params.id;

        if (companyId) {
          getOneCompany(+companyId);
          setReadonly(searchParams.get("update") !== "1");
        }
      } else {
        setReadonly(false);
      }
    }, []);

    const setDataToForm = (data: CompanyForm) => {
      form.setFieldsValue({
        ...data,
        startAt: data.startAt ? (dayjs.unix(data.startAt) as any) : null,
      });
    };

    const getOneCompany = async (id: number) => {
      try {
        setLoadingFetch(true);
        const { data } = await companyApi.findOne(id);
        if (isEmpty(data)) {
          navigate("/404");
          return;
        }

        setSelectedCompany(data);
        setDataToForm(data);
        return data as CompanyForm;
      } catch {
        message.error("Lỗi khi tải dữ liệu công ty");
      } finally {
        setLoadingFetch(false);
      }
    };

    const getDataSubmit = async () => {
      const { startAt, ...data } = form.getFieldsValue();

      const payload = {
        company: {
          ...data,
          startAt: startAt ? dayjs(startAt).unix() : 0,
          isActive: selectedCompany?.isActive ?? true,
        },
      };

      return payload;
    };

    const createData = async () => {
      await form.validateFields();

      setLoading(true);
      try {
        await companyApi.create(await getDataSubmit());
        message.success("Tạo công ty thành công!");
        navigate(`/system-config/${PermissionNames.companyList}`);
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      await form.validateFields();

      setLoading(true);
      try {
        const res = await companyApi.update(
          selectedCompany?.id || 0,
          await getDataSubmit()
        );
        setSelectedCompany({ ...selectedCompany, ...res.data });
        message.success("Chỉnh sửa công ty thành công!");
      } finally {
        setLoading(false);
      }
    };

    const handleSubmit = () => {
      if (status === "create") {
        createData();
      } else {
        updateData();
      }
    };

    const pageTitle = useMemo(
      () => (status === "create" ? "Tạo công ty" : "Chỉnh sửa công ty"),
      [status]
    );

    return (
      <div>
        <PageTitle
          back
          breadcrumbs={[
            { label: "Cấu hình" },
            {
              label: "Công ty",
              href: `/system-config/${PermissionNames.companyList}`,
            },
            { label: pageTitle },
          ]}
          title={pageTitle}
          extra={
            selectedCompany &&
            status == "update" && (
              <Space>
                <ActiveStatusTagSelect
                  disabled={readonly}
                  isActive={selectedCompany?.isActive}
                  onChange={(value) => {
                    setSelectedCompany({
                      ...selectedCompany,
                      isActive: value,
                    } as Company);
                  }}
                />
              </Space>
            )
          }
        />

        <Spin spinning={loadingFetch}>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Card>
                <Form
                  layout="vertical"
                  form={form}
                  className={clsx(readonly && "readonly")}
                  disabled={readonly}
                >
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item label="Mã công ty" name="code">
                        <Input
                          disabled={status === "update"}
                          placeholder="Nếu không điền hệ thống sẽ tự sinh mã"
                        />
                      </Form.Item>
                    </Col>

                    <Col span={12}>
                      <Form.Item label="Tên công ty" name="name" rules={rules}>
                        <Input placeholder="Tên công ty" />
                      </Form.Item>
                    </Col>

                    <Col span={12}>
                      <Form.Item
                        label="Số điện thoại"
                        name="phone"
                        rules={[{ required: true }, ...phoneValidate]}
                      >
                        <Input placeholder="Số điện thoại" />
                      </Form.Item>
                    </Col>

                    <Col span={12}>
                      <Form.Item
                        label="Số điện thoại 2"
                        name="phone2"
                        rules={phoneValidate}
                      >
                        <Input placeholder="Số điện thoại 2" />
                      </Form.Item>
                    </Col>

                    <Col span={12}>
                      <Form.Item
                        label="Liên hệ khác"
                        name="otherContact"
                        rules={[phoneNumberRule]}
                      >
                        <Input placeholder="Liên hệ khác" />
                      </Form.Item>
                    </Col>

                    <Col span={12}>
                      <Form.Item
                        label="Email"
                        name="email"
                        rules={[
                          { required: true, message: "Vui lòng nhập email" },
                          { type: "email", message: "Email không hợp lệ" },
                        ]}
                      >
                        <Input placeholder="Email" />
                      </Form.Item>
                    </Col>

                    <Col span={24}>
                      <Form.Item label="Địa chỉ" name="address" rules={rules}>
                        <Input placeholder="Địa chỉ" />
                      </Form.Item>
                    </Col>

                    <Col span={12}>
                      <Form.Item
                        label="Mã số thuế"
                        name="taxCode"
                        rules={rules}
                      >
                        <Input placeholder="Mã số thuế" />
                      </Form.Item>
                    </Col>

                    <Col span={12}>
                      <Form.Item label="Ngày bắt đầu" name="startAt">
                        <DatePicker
                          allowClear={false}
                          format={settings.dateFormat}
                          className="w-full"
                        />
                      </Form.Item>
                    </Col>

                    <Col span={24}>
                      <Form.Item
                        label="Ghi chú"
                        name="note"
                        rules={descriptionRules}
                      >
                        <BMDCKEditor
                          placeholder="Ghi chú"
                          value={selectedCompany?.note}
                          disabled={readonly}
                          inputHeight={300}
                          onChange={(content) => {
                            form.setFieldsValue({ note: content });
                          }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </Form>

                <div className="flex gap-[16px] justify-end mt-2">
                  {!readonly && (
                    <CustomButton
                      variant="outline"
                      className="cta-button"
                      onClick={() => {
                        if (status === "create") {
                          navigate(
                            `/system-config/${PermissionNames.companyList}`
                          );
                        } else {
                          setReadonly(true);
                          setDataToForm(selectedCompany!);
                        }
                      }}
                    >
                      Hủy
                    </CustomButton>
                  )}

                  <CustomButton
                    className="cta-button"
                    loading={loading}
                    onClick={() => {
                      if (!readonly) {
                        handleSubmit();
                      } else {
                        setReadonly(false);
                      }
                    }}
                    disabled={status === "update" && !haveEditPermission}
                  >
                    {status === "create"
                      ? "Tạo công ty"
                      : readonly
                      ? "Chỉnh sửa"
                      : "Lưu chỉnh sửa"}
                  </CustomButton>
                </div>
              </Card>
            </Col>
          </Row>
        </Spin>
      </div>
    );
  }
);
