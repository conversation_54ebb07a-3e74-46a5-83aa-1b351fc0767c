import { ApprovalList, ApprovalListStatus } from "./approvalList";
import { MemberShip } from "./memberShip";
import { Role } from "./role";

export interface ApprovalListDetail {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  position: number;
  note: string;
  status: ApprovalListStatus;
  approveAt: number;
  rejectAt: number;
  approvalList: ApprovalList;
  role: Role;
  memberShip: MemberShip;
}
