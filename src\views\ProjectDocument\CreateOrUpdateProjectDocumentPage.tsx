
import {
  Card,
  Col,
  Row,
  Form,
  Input,
  Select,
  Spin,
  message,
  DatePicker,
  Space,
} from "antd";
import PageTitle from "components/PageTitle/PageTitle";
import CustomInput from "components/Input/CustomInput";
import CustomButton from "components/Button/CustomButton";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import { DictionarySelector } from "components/Selector/DictionarySelector";
import { observer } from "mobx-react";
import { useEffect, useMemo, useState } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { Rule } from "antd/lib/form";
import { FileAttach } from "types/fileAttach";
import { getTitle } from "utils";
import { useWatch } from "antd/es/form/Form";
import { DictionaryType } from "types/dictionary";
import { FileUploadMultiple2 } from "components/Upload/FileUploadMultiple2";
import { fileAttachApi } from "api/fileAttach.api";
import { $url } from "utils/url";
import dayjs from "dayjs";
import { PermissionNames } from "types/PermissionNames";
import { checkRoles } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { Staff } from "types/staff";
import {
  ApprovalStepsCard,
  ApproveData,
  StepItem,
} from "components/ApproveProcess/ApprovalStepsCard";
import { FollowerSelector } from "components/Follower/FollowerSelector";
import styles from "./styles/CreateOrUpdateProjectDocument.module.scss";
import { ModalStatus } from "types/modal";
import { isEmpty } from "lodash";
import ActiveStatusTagSelect from "components/ActiveStatus/ActiveStatusTagSelect";
import clsx from "clsx";
import QRCodeSection from "../BlueprintPage/components/QRCodeSection";
import { BMDTextArea } from "components/TextArea/BMDTextArea";
import { documentApi } from "api/document.api";
import { DocumentModule, DocumentType } from "types/document";
import { userStore } from "store/userStore";
import { useDocument } from "hooks/useDocument";
import { appStore } from "store/appStore";
import { ApprovalTemplateType } from "types/approvalTemplate";
import { useApprovalStep } from "hooks/useAppovalStep";
import { ProjectItemSelector } from "components/Selector/ProjectItemSelector";
import { ProjectItem } from "types/projectItem";
import { BMDCKEditor } from "components/Editor";
import { toJS } from "mobx";
import { ApprovalListType } from "types/approvalList";
import { transformApproveData } from "components/ApproveProcess/approveUtil";
import RelatedTasksTable from "./components/RelatedTasksTable";

const rules: Rule[] = [{ required: true, message: "Trường này là bắt buộc" }];
const descriptionRules: Rule[] = [{ required: false }];

export interface DocumentItem {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  code: string;
  name: string;
  isActive: boolean;
  type: string;
  isDefault: boolean;
}

// Mock interface - replace with actual ProjectDocument type
interface ProjectDocument {
  id: number;
  code: string;
  name: string;
  documentType: string;
  category?: DocumentItem;
  folder: string;
  description: string;
  avatar?: string;
  isActive: boolean;
  createdDate: string;
  fileAttaches?: FileAttach[];
  path?: string;
  projectItem?: ProjectItem;
}

interface EditProjectDocumentPageProps {
  title: string;
  status: ModalStatus;
}

function CreateOrUpdateProjectDocumentPage({
  title = "",
  status,
}: EditProjectDocumentPageProps) {
  const { haveEditPermission } = checkRoles(
    {
      edit: PermissionNames.projectDocEdit,
    },
    permissionStore.permissions
  );

  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [loadingFetch, setLoadingFetch] = useState(false);
  const [fileList, setFileList] = useState<FileAttach[]>([]);
  const [readonly, setReadonly] = useState(true);
  const [selectedDocument, setSelectedDocument] = useState<ProjectDocument>();
  const [folderName, setFolderName] = useState("");
  const [fileAttachIds, setFileAttachIds] = useState<number[]>([]);
  const [loadingApprove, setLoadingApprove] = useState(false);
  const [removeApprovalList, setRemoveApprovalList] = useState<number[]>([]);

  const [searchParams, setSearchParams] = useSearchParams();
  const params = useParams();

  const navigate = useNavigate();

  // Watch form values for QR code display condition
  const code = useWatch("code", form);
  const name = useWatch("name", form);
  const documentType = useWatch("documentType", form);
  const category = useWatch("category", form);
  const avatar = useWatch("avatar", form);

  // Check if all required fields are filled for QR code display
  const shouldShowQRCode = useMemo(() => {
    return code && name && documentType && category;
  }, [code, name, documentType, category]);

  // Sample options for folder select
  const folderOptions = [
    { label: "Thư mục dự án A", value: "folder-a" },
    { label: "Thư mục dự án B", value: "folder-b" },
    { label: "Thư mục kỹ thuật", value: "folder-technical" },
    { label: "Thư mục báo cáo", value: "folder-report" },
  ];

  const {
    followers,
    setFollowers,
    approvalSteps,
    setApprovalSteps,
    fetchApprovalTemplate,
  } = useApprovalStep();

  const setDataToForm = (data: ProjectDocument) => {
    form.setFieldsValue({
      ...data,
      folder: data?.path,
      avatar: data?.fileAttaches?.[0]?.url,
      createdDate: data.createdDate ? dayjs(data.createdDate) : dayjs(),
      projectItemId: data.projectItem?.id,
      categoryId: data.category?.id,
    });

    setFileList(data.fileAttaches ? [...data.fileAttaches] : []);

    // Handle approval steps and followers if available
    if ((data as any).approvalLists) {
      const transformedApproveData = transformApproveData(
        (data as any).approvalLists,
        (data as any).createdBy
      );
      setApprovalSteps(transformedApproveData);
    }

    if ((data as any).followMemberShips) {
      setFollowers((data as any).followMemberShips || []);
    }
  };

  const getOneDocument = async (id: number) => {
    try {
      setLoadingFetch(true);
      const { data } = await documentApi.findOne(id);

      if (isEmpty(data)) {
        navigate("/404");
        return;
      }

      setSelectedDocument(data);
      setDataToForm(data);

      return data as ProjectDocument;
    } catch (e: any) {
      console.error("Error fetching document:", e);
    } finally {
      setLoadingFetch(false);
    }
  };

  useEffect(() => {
    document.title = getTitle(title);
    const rawPath = searchParams.get("path-folder") || "";
    const cleanPath = rawPath.startsWith("/") ? rawPath.slice(1) : rawPath;

    if (status === "update") {
      const documentId = params.id;

      if (documentId) {
        getOneDocument(+documentId);
        setReadonly(searchParams.get("update") !== "1");
        setFolderName(cleanPath);
      }
    } else {
      searchParams.delete("path-folder");
      setSearchParams(searchParams);

      setFolderName(cleanPath);
      setReadonly(false);
      // Set default values for create mode
      form.setFieldsValue({
        createdDate: dayjs(),
      });

      // Fetch approval template for create mode
      if (appStore.currentProject && userStore.info) {
        fetchApprovalTemplate({
          projectId: appStore.currentProject.id,
          createdStaff: toJS(userStore.info) as Staff,
          type: ApprovalTemplateType.Document,
        });
      }
    }
  }, []);



  const handleApproveProcess = async (data: ApproveData) => {
    console.log("Approve process");
    try {
      setLoadingApprove(true);
      // TODO: Implement documentApi.approve when available
      // await documentApi.approve(selectedDocument!.id || 0, data);
      message.success("Duyệt tài liệu dự án thành công!");
      // await getOneDocument(selectedDocument!.id || 0);
    } finally {
      setLoadingApprove(false);
    }
  };

  const handleRejectProcess = async (data: ApproveData) => {
    console.log("Reject process");
    try {
      setLoadingApprove(true);
      // TODO: Implement documentApi.reject when available
      // await documentApi.reject(selectedDocument!.id || 0, data);
      message.success("Từ chối tài liệu dự án thành công!");
      // await getOneDocument(selectedDocument!.id || 0);
    } finally {
      setLoadingApprove(false);
    }
  };

  const { haveViewAllPermission } = checkRoles(
    {
      add: PermissionNames.documentAdd,
      edit: PermissionNames.documentEdit,
      block: PermissionNames.documentBlock,
      viewAll: PermissionNames.documentViewAll,
    },
    permissionStore.permissions
  );

  const {
    documents,
    fetchData,
    loading: loadingDocs,
  } = useDocument({
    initQuery: {
      page: 1,
      limit: 100,
      isAdmin: haveViewAllPermission ? true : undefined,
      type: DocumentType.Document,
      path: "/",
      projectId: appStore.currentProject?.id,
    },
  });

  useEffect(() => {
    fetchData();
  }, []);

  const getDataSubmit = async () => {
    const {
      categoryId,
      projectItemId,
      createdDate,
      releaseDate,
      folder,
      ...data
    } = form.getFieldsValue();

    // Prepare approval lists for submission
    const approvalLists = approvalSteps.map((e) => ({
      id: e.id,
      name: e.name,
      type: ApprovalListType.Document,
      position: e.position,
      note: e.note,
      memberShipId: e.memberShipId,
      memberShip2Id: e.memberShip2Id,
      staffId: e.staffId,
      documentId: selectedDocument?.id || 0,
    }));

    const payload = {
      categoryId: categoryId ?? 0,
      projectItemId: projectItemId ?? 0,
      fileAttachIds,
      createdById: userStore.info?.id || 0,
      followMemberShipIds: followers?.map((it) => it.id),
      approvalLists,
      // projectId: 0,
      document: {
        ...data,
        createdDate: createdDate ? createdDate.format("YYYY-MM-DD") : "",
        releaseDate: releaseDate ? releaseDate.format("YYYY-MM-DD") : "",
        type: DocumentType.Document,
        path: folder,
        module: DocumentModule.File,
      },
    };

    return payload;
  };

  const createData = async () => {
    try {
      await form.validateFields();
      setLoading(true);

      const payload = await getDataSubmit();
      await documentApi.create(payload);

      message.success("Tạo tài liệu thành công!");
      navigate(`/doc-management/${PermissionNames.projectDocList}`);
      setFileList([]);
    } catch (error) {
      console.error("Error creating document:", error);
    } finally {
      setLoading(false);
    }
  };

  const updateData = async () => {
    try {
      await form.validateFields();
      setLoading(true);

      const payload = await getDataSubmit();
      await documentApi.update(selectedDocument!.id, payload);

      message.success("Chỉnh sửa tài liệu thành công!");
    } catch (error) {
      console.error("Error updating document:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = () => {
    if (status === "create") {
      createData();
    } else {
      updateData();
    }
  };

  const pageTitle = useMemo(
    () =>
      status === "create" ? "Tạo tài liệu dự án" : "Chỉnh sửa tài liệu dự án",
    [status]
  );

  return (
    <div className="app-container">
      <PageTitle
        back
        title={pageTitle}
        breadcrumbs={[
          { label: "Quản lý tài liệu" },
          {
            label: "Tài liệu dự án",
            href: `/doc-management/${PermissionNames.projectDocList}`,
          },
          { label: pageTitle },
        ]}
        // extra={
        //   selectedDocument &&
        //   status === "update" && (
        //     <Space>
        //       <ActiveStatusTagSelect
        //         disabled={readonly}
        //         isActive={selectedDocument?.isActive}
        //         onChange={(value) => {
        //           setSelectedDocument({
        //             ...selectedDocument,
        //             isActive: value,
        //           } as ProjectDocument);
        //         }}
        //       />
        //     </Space>
        //   )
        // }
      />

      <Spin spinning={loadingFetch}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          className={clsx(readonly ? "readonly" : "")}
          disabled={readonly}
        >
          <Row gutter={24}>
            <Col span={18}>
              <Card className="content-card">
                <Card title="Thông tin tài liệu" className="mb-0 form-card">
                  <Row gutter={24}>
                    {/* Left Column - Custom Upload Box */}
                    <Col span={8}>
                      <Form.Item
                        style={{
                          marginBottom: 0,
                          marginRight: 20,
                        }}
                        label={""}
                        name="avatar"
                        className="form-height-full"
                      >
                        <SingleImageUpload
                          onUploadOk={async (file: FileAttach) => {
                            if (file) {
                              const resFileAttach = await fileAttachApi.create({
                                fileAttach: {
                                  ...file,
                                  url: $url(file.path),
                                  name: file.filename,
                                },
                              });

                              setFileAttachIds([resFileAttach.data.id]);
                            } else {
                              setFileAttachIds([]);
                            }

                            form.setFieldsValue({
                              avatar: file.path,
                            });
                          }}
                          imageUrl={avatar}
                          height={"100%"}
                          width={"100%"}
                          className={`h-full upload-avatar ${styles.uploadAvatarOverride}`}
                          hideUploadButton={readonly}
                          disabled={readonly}
                        />
                      </Form.Item>
                    </Col>

                    {/* Right Column - Form Fields */}
                    <Col span={16}>
                      <Row gutter={[16, 16]}>
                        {/* Row 1: Mã tài liệu, Loại tài liệu */}
                        <Col span={12}>
                          <Form.Item
                            label="Mã tài liệu"
                            name="code"
                            rules={status === "create" ? [] : rules}
                          >
                            <CustomInput
                              placeholder={
                                status === "create"
                                  ? "Nếu không điền hệ thống sẽ tự sinh mã"
                                  : ""
                              }
                              disabled={status === "update"}
                            />
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item
                            label="Loại tài liệu"
                            name="categoryId"
                            rules={rules}
                          >
                            <DictionarySelector
                              placeholder="Chọn loại tài liệu"
                              initQuery={{
                                type: DictionaryType.Document,
                                isActive: true,
                              }}
                              showSearch
                            />
                          </Form.Item>
                        </Col>

                        {/* Row 2: Tên tài liệu */}
                        <Col span={24}>
                          <Form.Item
                            label="Tên tài liệu"
                            name="name"
                            rules={rules}
                          >
                            <CustomInput placeholder="Nhập tên tài liệu" />
                          </Form.Item>
                        </Col>

                        {/* Row 3: Hạng mục, Thư mục */}
                        <Col span={12}>
                          <Form.Item
                            label="Hạng mục"
                            name="projectItemId"
                            rules={rules}
                          >
                            <ProjectItemSelector placeholder="Hạng mục" />
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item
                            label="Thư mục"
                            name="folder"
                            rules={rules}
                          >
                            <Select
                              placeholder="Chọn thư mục"
                              showSearch
                              options={documents.map((document) => ({
                                value: `/${document.name}`,
                                label: document.name,
                              }))}
                              defaultValue={folderName}
                            />
                          </Form.Item>
                        </Col>

                        {/* Row 4: QR Code (conditionally shown) */}
                        {/* {shouldShowQRCode && ( */}
                        <Col span={24}>
                          <QRCodeSection
                            blueprintCode={code}
                            title={name}
                            section={category}
                            staff={documentType}
                          />
                        </Col>
                        {/* )} */}
                      </Row>
                    </Col>

                    {/* Description Section - Full Width */}
                    <Col span={24}>
                      <Form.Item
                        name="description"
                        label="Mô tả"
                        rules={descriptionRules}
                      >
                        <BMDCKEditor
                          placeholder="Nhập mô tả tài liệu"
                          value={selectedDocument?.description}
                          disabled={readonly}
                          inputHeight={300}
                          onChange={(content) => {
                            form.setFieldsValue({ description: content });
                          }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </Card>

                {/* Action Buttons */}
                <div className="flex gap-[16px] justify-end mt-4">
                  {!readonly && (
                    <CustomButton
                      variant="outline"
                      className="cta-button"
                      onClick={() => {
                        if (status === "create") {
                          navigate(
                            `/doc-management/${PermissionNames.projectDocList}`
                          );
                        } else {
                          setReadonly(true);
                          setDataToForm(selectedDocument!);
                        }
                      }}
                    >
                      Hủy
                    </CustomButton>
                  )}

                  <CustomButton
                    className="cta-button"
                    loading={loading}
                    onClick={() => {
                      if (!readonly) {
                        handleSubmit();
                      } else {
                        setReadonly(false);
                      }
                    }}
                    disabled={status === "update" && !haveEditPermission}
                  >
                    {status === "create"
                      ? "Thêm tài liệu"
                      : readonly
                      ? "Chỉnh sửa"
                      : "Lưu chỉnh sửa"}
                  </CustomButton>
                </div>
              </Card>
            </Col>

            <Col span={6}>
              <ApprovalStepsCard
                steps={approvalSteps}
                loading={loadingApprove}
                onSelectStep={setApprovalSteps}
                onRemove={setRemoveApprovalList}
                onApprove={handleApproveProcess}
                onReject={handleRejectProcess}
                templateType={ApprovalTemplateType.Document}
                editable={true}
                isShowActionButton={status === "update"}
              />

              <FollowerSelector
                followers={followers}
                setFollowers={setFollowers}
                readonly={readonly}
                headerTitle={`Người theo dõi (${followers?.length})`}
              />
            </Col>
          </Row>
        </Form>

        {/* Related Tasks Section - Only show in update mode */}
        {status === "update" && selectedDocument?.id && (
          <RelatedTasksTable
            documentId={selectedDocument.id}
            readonly={readonly}
          />
        )}
      </Spin>
    </div>
  );
}

export default observer(CreateOrUpdateProjectDocumentPage);
