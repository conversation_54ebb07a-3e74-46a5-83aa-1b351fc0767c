import React from "react";
import { RouteObject } from "react-router-dom";
import { PermissionType } from "types/permission";

export interface Route extends RouteObject {
  title?: any;
  children?: Route[];
  icon?: React.ReactNode;
  breadcrumb?: string;
  isAccess?: boolean;
  hidden?: boolean;
  name?: string;
  isFeature?: boolean;
  noRole?: boolean;
  aliasPath: string;
  checkIsDev?: boolean;
  isGameRouter?: boolean;
  isPublic?: boolean; //Ẩn ở menu chọn quyền và luôn hiển thị với tất cả user
  permissionTypes?: PermissionType[];
  isCompact?: boolean; //Nếu chỉ có 1 menu con thì đưa ra menu chính
  needProject?: boolean;
}
