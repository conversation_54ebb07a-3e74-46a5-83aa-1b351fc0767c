import { FileAttach } from "./fileAttach";
import { Material } from "./material";

export enum Season {
  FallWinter = "FALL_WINTER",
  SpringSummer = "SPRING_SUMMER",
  All = "ALL",
}

export const SeasonTrans = {
  [Season.FallWinter]: {
    value: Season.FallWinter,
    label: "Fall/Winter",
  },
  [Season.SpringSummer]: {
    value: Season.SpringSummer,
    label: "Spring/Summer",
  },
  [Season.All]: {
    value: Season.All,
    label: "Tất cả mùa",
  },
};
export interface SeasonType {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  name: string;
  material: Material;
  icon: string;
  fileAttachAvatar: FileAttach;
}
