import { Card, Col, Form, message, Modal, Popconfirm, Row, Space } from "antd";
import { Rule } from "antd/lib/form";
import { FileUploadMultiple } from "components/Upload/FileUploadMultiple";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { FileAttach } from "types/fileAttach";
import { getTitle } from "utils";
import UploadImg from "assets/images/upload-img.png";
import { FileAttachPayload } from "components/Upload/FileUploadItem";
import { $url } from "utils/url";
import { UploadFile } from "antd/lib";
import { useWatch } from "antd/es/form/Form";
import { CustomerShiftTrans } from "types/staff";
import { settings } from "settings";
import { taskTemplateApi } from "api/taskTemplate.api";
import { Step } from "types/step";
import { DeleteOutlined } from "@ant-design/icons";
import CustomInput from "components/Input/CustomInput";
import CustomSelect from "components/Input/CustomSelect";
import CustomDatePicker from "components/Input/CustomDatePicker";
import CustomButton from "components/Button/CustomButton";

const rules: Rule[] = [{ required: true }];

export const CreateTaskTemplatePage = ({ title = "" }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  useEffect(() => {
    document.title = getTitle(title);
  }, []);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [steps, setSteps] = useState<Partial<Step>[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [newJobName, setNewJobName] = useState("");
  const avatar = useWatch("avatar", form);
  const createData = async () => {
    const valid = await form.validateFields();

    const { estimateAt, files, ...data } = form.getFieldsValue();

    const payload = {
      taskTemplate: {
        ...data,
        files: typeof files === "string" ? files : JSON.stringify(files),
        estimateAt: estimateAt ? estimateAt?.startOf("day")?.unix() : undefined,
      },
      steps,
    };
    // let fileAttachIds: number[] = [];
    // if (fileAttachList && fileAttachList.length > 0) {
    //   const results = await Promise.allSettled(
    //     fileAttachList.map((file: FileAttachPayload) =>
    //       fileAttachApi.create({
    //         fileAttach: {
    //           name: file.name,
    //           type: file.type,
    //           url: file.url,
    //           path: file.path,
    //           size: file.size,
    //         },
    //       })
    //     )
    //   );
    //   //@ts-ignore
    //   fileAttachIds = results.map((result) => result.value?.data?.id);
    //   if (fileAttachIds.length > 0) {
    //     Object.assign(payload, { fileAttachIds });
    //   }

    setLoading(true);
    try {
      const res = await taskTemplateApi.create(payload);
      message.success("Tạo công việc mẫu thành công!");
      navigate("/master-data/task-template-list");
      setFileList([]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <div className="font-bold text-2xl mb-[20px]">Tạo công việc mẫu</div>
      <Card>
        <Form layout="vertical" form={form}>
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item
                style={{ marginBottom: 0, height: "100%" }}
                label={<div>Hình ảnh công việc mẫu</div>}
                name="avatar"
                className="[&_.ant-form-item-row]:h-full [&_.ant-form-item-control-input]:h-full [&_.ant-form-item-control-input-content]:h-full"
              >
                <SingleImageUpload
                  onUploadOk={(file: FileAttach) => {
                    console.log(file);
                    form.setFieldsValue({
                      avatar: file.path,
                    });
                  }}
                  imageUrl={avatar}
                  height={"100%"}
                  width={"100%"}
                  className="h-full"
                />
              </Form.Item>
            </Col>

            <Col span={9}>
              <Form.Item label="Mã công việc mẫu" name="code">
                <CustomInput placeholder="Nếu không điền hệ thống sẽ tự sinh mã" />
              </Form.Item>
              <Form.Item label="Tên công việc mẫu" name="name" rules={rules}>
                <CustomInput placeholder="Tên công việc mẫu" />
              </Form.Item>

              <Form.Item label="Loại công việc mẫu" name="type" rules={rules}>
                <CustomSelect
                  placeholder="Loại công việc mẫu"
                  options={Object.values(CustomerShiftTrans).map((item) => ({
                    label: item.label,
                    value: item.value,
                  }))}
                />
              </Form.Item>
              <Form.Item label="Mô tả" name="description">
                <CustomInput type="textarea" placeholder="Mô tả" rows={4} />
              </Form.Item>
            </Col>
            <Col span={9}>
              <Form.Item
                shouldUpdate={true}
                style={{ marginBottom: 0, height: "100%" }}
                className="[&_.ant-form-item-row]:!h-full [&_.ant-form-item-control-input]:!h-full [&_.ant-form-item-control-input-content]:!h-full [&_.ant-form-item-has-success]:!h-full"
              >
                {() => {
                  return (
                    <Form.Item
                      label={"Tệp đính kèm"}
                      style={{ marginBottom: 0, height: "100%" }}
                      name="files"
                      className="[&_.ant-form-item-row]:!h-full [&_.ant-form-item-control-input]:!h-full [&_.ant-form-item-control-input-content]:!h-full [&_.ant-form-item-has-success]:!h-full"
                    >
                      <FileUploadMultiple
                        className="h-full"
                        draggerContent={
                          <p className="ant-upload-text ">
                            <img
                              src={UploadImg}
                              height={30}
                              width={30}
                              className="mr-2"
                            />
                            <div className="font-bold">
                              {"Tải lên tệp đính kèm"}
                            </div>
                            <div className="text-gray-500">
                              Tệp hợp đồng, hóa đơn, tài liệu
                            </div>
                          </p>
                        }
                        fileList={fileList}
                        onUploadOk={(fileList) => {
                          console.log({ fileList });
                          const filePayloads: FileAttachPayload[] =
                            fileList.map((item) => {
                              return {
                                url:
                                  item.url || $url(item.response?.data?.path),
                                name: item.name,
                                size: item?.size,
                                uid: item?.uid,
                                type: item.type,
                                path: item.response?.data?.path,
                                destination: item.response?.data?.destination,
                              };
                            });

                          console.log({ filePayload: filePayloads });
                          setFileList(fileList);
                          form.setFieldsValue({
                            files: JSON.stringify(filePayloads),
                          });
                        }}
                        onDelete={setFileList}
                      />
                    </Form.Item>
                  );
                }}
              </Form.Item>
            </Col>
          </Row>
          <Card title="Thông tin chung" className="mb-0 form-card mt-[16px]">
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  label="Thời gian dự kiến hoàn thành"
                  name="estimateAt"
                >
                  <CustomDatePicker
                    allowClear={false}
                    format={settings.dateFormat}
                    className="w-full"
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="Chi phí dự kiến" name="estimatedCost">
                  <CustomInput
                    typeText="number"
                    placeholder="Chi phí dự kiến"
                    suffix="VNĐ"
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="Ghi chú" name="note">
                  <CustomInput placeholder="Ghi chú" />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="Tài nguyên cần thiết" name="description">
                  <CustomInput
                    type="textarea"
                    rows={6}
                    placeholder="Nhân sự, vật liệu, thiết bị"
                  ></CustomInput>
                </Form.Item>
              </Col>
              <Col>
                <div className="font-medium">Danh sách các bước thực hiện</div>
              </Col>
              <Col span={24} className="mt-4">
                <CustomButton
                  variant="outline"
                  showPlusIcon
                  className="border border-solid"
                  onClick={() => setIsModalVisible(true)}
                >
                  Thêm công việc
                </CustomButton>

                <div
                  style={{ marginTop: 16 }}
                  className="border border-solid p-[16px] border-[#B9C3C5]"
                >
                  {steps.length === 0 ? (
                    <div
                      style={{
                        textAlign: "center",
                        padding: "20px",
                        color: "#aaa",
                      }}
                    >
                      Chưa có danh sách công việc
                    </div>
                  ) : (
                    <div className="flex flex-col justify-center gap-4">
                      {steps.map((job, index) => (
                        <div
                          key={index}
                          style={{
                            display: "flex",
                            alignItems: "center",
                          }}
                          className="border border-solid p-[16px] border-[#F7F7F7]"
                        >
                          <Popconfirm
                            title="Bạn có chắc chắn muốn xóa công việc này?"
                            okText="Xóa"
                            cancelText="Hủy"
                            onConfirm={() => {
                              const updated = [...steps];
                              updated.splice(index, 1);
                              setSteps(updated);
                            }}
                          >
                            <span
                              style={{
                                marginRight: 8,
                                cursor: "pointer",
                                color: "red",
                              }}
                            >
                              <DeleteOutlined />
                            </span>
                          </Popconfirm>
                          <span style={{ flex: 1 }}>{job.name}</span>
                          {/* <span>{job.childCount} CV con</span> */}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </Col>
            </Row>
          </Card>
        </Form>
        <div className="flex gap-[16px] justify-end mt-2">
          <CustomButton
            variant="outline"
            className="cta-button"
            onClick={() => {
              navigate("/master-data/task-template-list");
            }}
          >
            Hủy
          </CustomButton>
          <CustomButton
            className="cta-button"
            loading={loading}
            onClick={() => {
              createData();
            }}
          >
            Tạo công việc mẫu
          </CustomButton>
        </div>
      </Card>
      <Modal
        title="Thêm công việc"
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        onOk={() => {
          if (newJobName.trim()) {
            setSteps([...steps, { name: newJobName.trim() }]);
            setNewJobName("");
            setIsModalVisible(false);
          } else {
            message.error("Vui lòng nhập tên công việc");
          }
        }}
        okText="Thêm"
        cancelText="Hủy"
        footer={
          <Space>
            <CustomButton
              variant="outline"
              className="cta-button"
              onClick={() => {
                setIsModalVisible(false);
              }}
            >
              Hủy
            </CustomButton>
            <CustomButton
              className="cta-button"
              loading={loading}
              onClick={() => {
                if (newJobName.trim()) {
                  setSteps([...steps, { name: newJobName.trim() }]);
                  setNewJobName("");
                  setIsModalVisible(false);
                } else {
                  message.error("Vui lòng nhập tên công việc");
                }
              }}
            >
              Thêm
            </CustomButton>
          </Space>
        }
      >
        <CustomInput
          placeholder="Nhập tên công việc"
          value={newJobName}
          onChange={(e) => setNewJobName(e)}
        />
      </Modal>
    </div>
  );
};
