import { providerApi } from "api/provider.api";
import { useMemo, useState } from "react";
import { Provider } from "types/provider";
import { QueryParam } from "types/query";

export interface ProviderQuery extends QueryParam { }

interface UseProviderProps {
  initQuery: ProviderQuery;
}

export const useProvider = ({ initQuery }: UseProviderProps) => {
  const [data, setData] = useState<Provider[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<ProviderQuery>(initQuery);
  const [loading, setLoading] = useState(false);
  const [isFetched, setIsFetched] = useState(false)

  const isEmptyQuery = useMemo(() => {
    const defaultKeys = ["limit", "page", "queryObject", "module"];
    return (
      Object.keys(query).filter(
        (k) => query[k] != null && query[k] != "" && !defaultKeys.includes(k)
      ).length === 0
    );
  }, [query]);

  const fetchData = async (newQuery?: QueryParam) => {
    setLoading(true);
    try {
      const { data } = await providerApi.findAll({ ...query, ...newQuery });

      setData(data.providers);
      setTotal(data.total);
    } finally {
      setLoading(false);
      setIsFetched(true);
    }
  };

  return {
    providers: data,
    total,
    fetchData,
    loading,
    setQuery,
    query,
    isEmptyQuery,
    setData,
    isFetched
  };
};
