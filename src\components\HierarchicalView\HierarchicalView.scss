.hierarchical-view {
  width: 100%;
  
  &__header {
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f0;
  }

  &__column-headers {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background-color: #fafafa;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    margin-bottom: 12px;
    font-weight: 600;
    font-size: 13px;
    color: #595959;

    .column-header {
      &.group-code-header {
        width: 80px;
        min-width: 80px;
        flex-shrink: 0;
      }

      &.name-header {
        flex: 1;
        margin-left: 12px;
      }

      &.actions-header {
        width: 120px;
        min-width: 120px;
        text-align: center;
      }
    }
  }

  &__controls {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  &__content {
    background: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    overflow: hidden;
  }

  &__empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 48px 24px;
    text-align: center;
    color: #8c8c8c;

    &-icon {
      font-size: 48px;
      color: #d9d9d9;
      margin-bottom: 16px;
    }

    span {
      margin-bottom: 16px;
      font-size: 14px;
    }
  }

  &.empty {
    .hierarchical-view__content {
      border: 1px dashed #d9d9d9;
      background: #fafafa;
    }
  }
}

.hierarchical-item {
  &__content {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.2s ease;
    position: relative;

    &:hover {
      background-color: #f5f5f5;
      
      .hierarchical-item__actions {
        opacity: 1;
      }
    }

    &.inactive {
      opacity: 0.6;
      background-color: #f9f9f9;
    }

    &.level-0 {
      background-color: #fafbfc;
      font-weight: 500;
    }

    &.level-1 {
      background-color: #fdfdfd;
    }
  }

  &__group-code {
    width: 80px;
    min-width: 80px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;

    .group-code-badge {
      display: inline-block;
      padding: 2px 8px;
      // background: linear-gradient(135deg, #1890ff 0%, #36cfc9 100%);
      // color: white;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 600;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      box-shadow: 0 1px 3px rgba(24, 144, 255, 0.2);
      transition: all 0.2s ease;
      cursor: pointer;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 6px rgba(24, 144, 255, 0.3);
      }
    }
  }

  &__left {
    display: flex;
    align-items: center;
    flex: 1;
    cursor: pointer;
    min-width: 0;
    padding-right: 12px;

    &:hover {
      .hierarchical-item__expand-icon {
        color: #1890ff;
      }
    }
  }

  &__expand-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    color: #8c8c8c;
    transition: all 0.2s ease;
    flex-shrink: 0;
    
    &:hover {
      color: #1890ff;
      transform: scale(1.1);
    }
  }

  &__expand-placeholder {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    flex-shrink: 0;
  }

  &__icon {
    margin-right: 8px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    font-size: 14px;
  }

  &__info {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
  }

  &__name {
    font-size: 14px;
    color: #262626;
    font-weight: 500;
    line-height: 1.4;
    word-break: break-word;
    
    .level-0 & {
      font-weight: 600;
      color: #1890ff;
    }
  }

  &__description {
    font-size: 12px;
    color: #8c8c8c;
    margin-top: 2px;
    line-height: 1.3;
    word-break: break-word;
  }

  &__actions {
    opacity: 0;
    transition: opacity 0.2s ease;
    display: flex;
    align-items: center;
    width: 120px;
    justify-content: flex-end;
    flex-shrink: 0;

    .hierarchical-item__content:hover & {
      opacity: 1;
    }
  }

  &__action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px !important;
    height: 28px !important;
    border-radius: 6px;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }

    &.add-btn {
      color: #52c41a;
      
      &:hover {
        background-color: #f6ffed;
        border-color: #b7eb8f;
      }
    }
  }

  &__children {
    .hierarchical-item__content {
      border-left: 2px solid #e6f7ff;
      margin-left: 12px;
      
      &.level-1 {
        border-left-color: #bae7ff;
      }
      
      &.level-2 {
        border-left-color: #91d5ff;
      }
      
      &.level-3 {
        border-left-color: #69c0ff;
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .hierarchical-view {
    &__column-headers {
      .column-header {
        &.group-code-header {
          width: 60px;
          min-width: 60px;
        }

        &.actions-header {
          width: 100px;
          min-width: 100px;
        }
      }
    }
  }

  .hierarchical-item {
    &__group-code {
      width: 60px;
      min-width: 60px;

      .group-code-badge {
        font-size: 10px;
        padding: 1px 6px;
      }
    }

    &__actions {
      width: 100px;
    }

    &__content {
      padding: 6px 8px;
    }
  }
}
