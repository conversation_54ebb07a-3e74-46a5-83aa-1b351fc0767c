import { Select } from "antd";
import { DefaultOptionType } from "antd/es/select";
import { SelectProps } from "antd/lib";
import { useColor } from "hooks/useColor";
import { useComponent } from "hooks/useComponent";
import { useMaterialGroup } from "hooks/useMaterialGroup";
import { useVariant } from "hooks/useVariant";
import { debounce, uniqBy } from "lodash";
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
} from "react";
import { Component } from "types/component";
import { MaterialGroup } from "types/materialGroup";
import { QueryParams2 } from "types/query";
import { Variant } from "types/variant";

type CustomFormItemProps = {
  value?: number;
  initQuery?: QueryParams2;
  disabled?: boolean;
  selectedColor?: MaterialGroup[];
  multiple?: boolean;
  onChange?: (value: any) => void;
  selectProps?: SelectProps;
  initOptionItem?: Variant | Variant[];
  valueIsOption?: boolean;
  allowClear?: boolean;
  placeholder?: string;
  isMainComponent?: boolean;
  productId?: number;
  componentId?: number;
  isDisplayNameAndCode?: boolean;
};

export interface MaterialGroupSelector {
  refresh(): void;
}

/**
 * A demo that changes it's number value on click.
 * @param value initialValue passed by parent Form.Item.
 * @param onChange a callback for Form.Item to read its child component's value.
 * @constructor
 */
export const VariantSelector = forwardRef(
  (
    {
      value,
      onChange,
      initQuery,
      disabled,
      multiple = false,
      selectedColor,
      initOptionItem,
      valueIsOption,
      selectProps,
      allowClear = true,
      placeholder = "Chọn biến thể",
      isMainComponent = false,
      productId,
      componentId,
      isDisplayNameAndCode = false,
    }: CustomFormItemProps,
    ref
  ) => {
    const { variants, total, loading, fetchData, query } = useVariant({
      initQuery: {
        page: 1,
        limit: 50,
        componentId,
        ...initQuery,
      },
    });

    useImperativeHandle<any, MaterialGroupSelector>(
      ref,
      () => ({
        refresh() {
          fetchData();
        },
      }),
      []
    );

    useEffect(() => {
      if (componentId !== undefined) {
        query.componentId = componentId;
      } else {
        delete query.componentId;
      }
      fetchData();
    }, [componentId]);

    const debounceSearch = useCallback(
      debounce((keyword) => {
        query.search = keyword;
        fetchData();
      }, 300),
      [query]
    );

    const options = useMemo(() => {
      let data = [...variants];
      if (initOptionItem) {
        if ((initOptionItem as Variant[])?.length) {
          data = data.concat(initOptionItem);
        } else {
          data.push(initOptionItem as Variant);
        }
      }

      return uniqBy(data, (data) => data.id);
    }, [variants, initOptionItem]);

    return (
      <Select<any, DefaultOptionType>
        mode={multiple ? "multiple" : undefined}
        disabled={disabled}
        loading={loading}
        style={{ width: "100%", minWidth: 200 }}
        allowClear={allowClear}
        onSearch={debounceSearch}
        showSearch
        placeholder={placeholder}
        filterOption={false}
        value={value}
        onChange={(v, opts) => {
          if (v === undefined || v.length == 0) {
            debounceSearch(v);
          }
          if (valueIsOption) {
            if (opts instanceof Array) {
              onChange?.(opts?.map((v) => v.item));
            } else {
              onChange?.(opts?.item);
            }
          } else {
            onChange?.(v);
          }
        }}
        {...selectProps}
      >
        {options?.map((item) => (
          <Select.Option item={item} value={item.id} key={item.id}>
            <div className="flex items-center gap-2">
              {isDisplayNameAndCode ? (
                <span>
                  <>
                    {item.code}-{item.privateName}
                  </>
                </span>
              ) : (
                <span>
                  <>{item.privateName}</>
                </span>
              )}
            </div>
          </Select.Option>
        ))}
      </Select>
    );
  }
);
