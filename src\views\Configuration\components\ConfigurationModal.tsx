import { <PERSON><PERSON>, Card, Col, Form, Input, message, Modal, Row } from "antd";
import { Rule } from "antd/lib/form";
import { configurationApi } from "api/configuration.api";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import { useEffect, useMemo, useRef, useState } from "react";
import { SketchPicker, TwitterPicker } from "react-color";
import {
  ConfigSlugsNote,
  Configuration,
  ConfigurationParam,
  ConfigurationParamTrans,
} from "types/configuration";
import { FileAttach } from "types/fileAttach";
import { ModalStatus } from "types/modal";

const rules: Rule[] = [{ required: true }];

const ZALO_PREFIX = "https://zalo.me/";
const MESSAGER_PREFIX = "https://www.messenger.com/t/";

const slugs = {};

const emailRule = {
  pattern:
    /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
  // pattern: /\d+/g,
  message: "Email không đúng định dạng.",
  required: true,
};

export const ConfigurationModal = ({
  visible,
  status,
  configuration,
  onClose,
  onSubmitOk,
}: {
  visible: boolean;
  status: ModalStatus;
  configuration: Partial<Configuration>;
  onClose: () => void;
  onSubmitOk: () => void;
}) => {
  const [form] = Form.useForm<
    Configuration & { configuration: Configuration }
  >();
  const [loading, setLoading] = useState(false);
  const [content, setContent] = useState("");
  const editorRef = useRef<{ setContent: (content: string) => void }>(null);
  const [color, setColor] = useState<any>("#000");
  const [bgColor, setBgColor] = useState<any>("#ffeaa7");
  useEffect(() => {
    if (status == "create" && visible) {
      form.resetFields();
    }
    setContent(configuration.value || "");
    setBgColor(configuration?.buttonColor || "#ffeaa7");
    setColor(configuration?.textColor || "#000");
  }, [visible, status]);

  useEffect(() => {
    form.setFieldsValue({ ...configuration });
    return () => {};
  }, [configuration]);

  const createData = async () => {
    const valid = await form.validateFields();
    const data = { staff: form.getFieldsValue() };

    setLoading(true);
    try {
      const res = await configurationApi.create(data);
      message.success("Create config successfully!");
      onClose();
      onSubmitOk();
    } finally {
      setLoading(false);
    }
  };

  const updateData = async () => {
    const valid = await form.validateFields();
    const { value, link, description, title, buttonText } =
      form.getFieldsValue();
    setLoading(true);
    try {
      const res = await configurationApi.update(configuration?.id || 0, {
        configuration: {
          ...configuration,
          link,
          value,
          description,
          title,
          buttonText,
          buttonColor: bgColor,
          textColor: color,
        },
      });
      message.success("Cập nhật cấu hình thành công!");
      onClose();
      onSubmitOk();
    } finally {
      setLoading(false);
    }
  };

  const rules = useMemo(() => {
    switch (configuration.param) {
      default:
        return [];
    }
  }, [configuration]);

  console.log(configuration);

  return (
    <Modal
      onCancel={onClose}
      visible={visible}
      title={status == "create" ? "Thêm cấu hình" : "Cập nhật cấu hình"}
      style={{ top: 20 }}
      width={700}
      confirmLoading={loading}
      onOk={() => {
        status == "create" ? createData() : updateData();
      }}
      okText="Xác nhận"
    >
      <Form layout="vertical" form={form}>
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              hidden
              name="configuration"
              initialValue={configuration}
              label={configuration.title}
            >
              <Input placeholder="" value={configuration.value} />
            </Form.Item>
            {configuration.param == ConfigurationParam.HomeBanner1 ||
            configuration.param == ConfigurationParam.HomeBanner2 ? (
              <>
                <Form.Item shouldUpdate={true}>
                  {() => {
                    return (
                      <Form.Item
                        label={
                          ConfigurationParamTrans[configuration?.param!]?.label
                        }
                        name="value"
                        rules={[...rules]}
                      >
                        <SingleImageUpload
                          recommendFileSize={5 * 1024 * 1024}
                          recommendSize={{ height: 640, width: 1920 }}
                          onUploadOk={(file: FileAttach) => {
                            form.setFieldsValue({
                              value: file.url,
                            });
                          }}
                          imageUrl={form.getFieldValue("value")}
                        />
                      </Form.Item>
                    );
                  }}
                </Form.Item>
                {configuration.param == ConfigurationParam.HomeBanner2 && (
                  <>
                    {/* <Form.Item name="title" rules={rules} label={"Tiêu đề"}>
                      <Input placeholder="Nhập vào tiêu đề banner" />
                    </Form.Item>
                    <Form.Item name="description" rules={rules} label={"Mô tả"}>
                      <Input placeholder="Nhập vào mô tả banner" />
                    </Form.Item> */}
                    <div className="w-full flex justify-around">
                      <Form.Item label="Chọn màu chữ">
                        <div className="flex gap-2 items-center">
                          <Card
                            className="  !mb-2 !rounded w-fit h-fit"
                            bodyStyle={{
                              padding: "10px 10px",
                            }}
                          >
                            <div
                              className={` w-[70px] h-6`}
                              style={{ background: color }}
                            ></div>
                          </Card>
                          <h3
                            style={{
                              color,
                            }}
                          >
                            Test chữ
                          </h3>
                        </div>
                        <SketchPicker
                          width="200px"
                          color={color}
                          onChangeComplete={(color) => setColor(color.hex)}
                        />
                      </Form.Item>
                      <Form.Item label="Chọn màu nút">
                        <Card
                          className="  !mb-2 !rounded w-fit h-fit"
                          bodyStyle={{
                            padding: "10px 10px",
                          }}
                        >
                          <div
                            className={` w-[70px] h-6`}
                            style={{ background: bgColor }}
                          ></div>
                        </Card>
                        <SketchPicker
                          width="200px"
                          color={bgColor}
                          onChangeComplete={(color) => setBgColor(color.hex)}
                        />
                      </Form.Item>
                    </div>
                    <Card
                      className="  !mb-2 !mt-3 !rounded w-full h-fit"
                      bodyStyle={{
                        padding: "10px 10px",
                      }}
                    >
                      <div
                        className={` w-full h-8 text-center`}
                        style={{ background: bgColor }}
                      >
                        <h3
                          style={{
                            color,
                          }}
                        >
                          Test chữ
                        </h3>
                      </div>
                    </Card>
                  </>
                )}

                <Form.Item name="link" rules={rules} label={"Link"}>
                  <Input placeholder="" />
                </Form.Item>
                <Form.Item name="buttonText" rules={rules} label={"Tên nút"}>
                  <Input placeholder="Vui lòng nhập tên nút" />
                </Form.Item>
              </>
            ) : (
              <Form.Item
                name="value"
                rules={rules}
                label={ConfigurationParamTrans[configuration?.param!]?.label}
              >
                <Input
                  type={
                    configuration.param ==
                    ConfigurationParam.RejectRequestVipAfterDay
                      ? "number"
                      : ""
                  }
                  placeholder=""
                  value={configuration.value}
                />
              </Form.Item>
            )}

            {/* {configuration.param == ConfigurationParam.WarningEmail && (
              <Form.Item label="Email nhận thông báo" name="value" rules={rules}>
                <InputNumber placeholder="" />
              </Form.Item>
            )} */}
            {/* {configuration.param == ConfigurationParam.MaxSizeUpload && (
              <Form.Item label="Giá trị (Đơn vị MB)" name="value" rules={rules}>
                <InputNumber placeholder="" />
              </Form.Item>
            )}

            {configuration.param == ConfigurationParam.Zalo && (
              <Form.Item
                label="Link đến tài khoản Zalo"
                name="value"
                rules={rules}
              >
                <Input placeholder="VD: https://zalo.me/0937277200" />
              </Form.Item>
            )}

            {configuration.param == ConfigurationParam.Messenger && (
              <Form.Item label="Link đến Messager" name="value" rules={rules}>
                <Input placeholder="VD: https://www.messenger.com/t/100005129014554/" />
              </Form.Item>
            )}

            {configuration.param == ConfigurationParam.Line && (
              <Form.Item label="Link đến Line" name="value" rules={rules}>
                <Input placeholder="" />
              </Form.Item>
            )}
            {configuration.param == ConfigurationParam.Facebook && (
              <Form.Item
                label="Link đến MXH Facebook"
                name="value"
                rules={rules}
              >
                <Input placeholder="VD: https://www.facebook.com/berivina" />
              </Form.Item>
            )}
            {configuration.param == ConfigurationParam.Youtube && (
              <Form.Item
                label="Link đến MXH Youtube"
                name="value"
                rules={rules}
              >
                <Input placeholder="" />
              </Form.Item>
            )}
            {configuration.param == ConfigurationParam.Instagram && (
              <Form.Item
                label="Link đến MXH Instagram"
                name="value"
                rules={rules}
              >
                <Input placeholder="" />
              </Form.Item>
            )}
            {configuration.param == ConfigurationParam.Tiktok && (
              <Form.Item label="Link đến MXH Tiktok" name="value" rules={rules}>
                <Input placeholder="" />
              </Form.Item>
            )}
            {configuration.param == ConfigurationParam.EmailReceiveLead && (
              <Form.Item
                label="Email nhận nội dung liên hệ"
                name="value"
                rules={[
                  { required: true },
                  { type: "email", message: "Sai định dạng" },
                ]}
              >
                <Input type="item" placeholder="" />
              </Form.Item>
            )} */}
          </Col>
        </Row>
      </Form>
      {/* @ts-ignore */}
      {ConfigSlugsNote[configuration?.param]?.length && (
        <Alert
          type="warning"
          description={
            <ul style={{ marginBottom: 0 }}>
              {/* @ts-ignore */}
              {ConfigSlugsNote[configuration?.param]?.map((item) => (
                <li>
                  <b>{item.slug}</b> : {item.content}
                </li>
              ))}
            </ul>
          }
        />
      )}
    </Modal>
  );
};
