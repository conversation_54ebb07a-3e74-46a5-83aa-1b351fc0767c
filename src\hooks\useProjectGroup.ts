import { projectGroupApi } from "api/projectGroup.api";
import { useMemo, useState } from "react";
import { ProjectGroup } from "types/projectGroup";
import { QueryParam } from "types/query";

export interface ProjectGroupQuery extends QueryParam {}

interface UseProjectGroupProps {
  initQuery: ProjectGroupQuery;
}

export const useProjectGroup = ({ initQuery }: UseProjectGroupProps) => {
  const [data, setData] = useState<ProjectGroup[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<ProjectGroupQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const isEmptyQuery = useMemo(
    () =>
      Object.keys(query).filter(
        (k) => query[k] && !["limit", "page", "queryObject"].includes(k)
      ).length == 0,
    [query]
  );

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await projectGroupApi.findAll(query);

      setData(data.projectGroups);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return {
    projectGroups: data,
    total,
    fetchData,
    loading,
    setQuery,
    query,
    isEmptyQuery,
  };
};
