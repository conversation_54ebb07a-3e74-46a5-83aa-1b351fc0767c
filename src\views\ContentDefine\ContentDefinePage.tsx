import { Button, message, Select, Space, Spin, Table } from "antd";
import { contentDefineApi } from "api/content-define.api";

import { Pagination } from "components/Pagination";
import { observer } from "mobx-react";
import { useEffect, useMemo, useState } from "react";
import {
  ContentDefine,
  ContentDefineType,
  ContentDefineTypeTrans,
} from "types/content-define";
import { ModalStatus } from "types/modal";
import { QueryParam } from "types/query";
import { getTitle } from "utils";
import { ContentDefineModal } from "./components/ContentDefineModal";
import { useHandlerContentDefine } from "./handler/useHandlerContentDefine";
// import { StaffModal } from "./components/StaffModal";

const { Column } = Table;
const { Option } = Select;

interface BannerQuery extends QueryParam {
  areaId: null | null;
}

export const ContentDefinePage = observer(({ title = "" }) => {
  const { loading, data, total, query, fetchData } = useHandlerContentDefine({
    initQuery: {
      page: 1,
      limit: 50,
      search: "",
      areaId: null,
    },
  });
  const [visibleModal, setVisibleModal] = useState(false);
  const [selectedContentDefine, setSelectedContentDefine] = useState<
    Partial<ContentDefine>
  >({});
  const [modalStatus, setModalStatus] = useState<ModalStatus>("create");

  useEffect(() => {
    document.title = getTitle(title);
    fetchData();
  }, []);

  // const contentDefineArr = useMemo(() => {
  //   return Object.values(ContentDefineType);
  // }, []);

  // const contentDefineExist = useMemo(() => {
  //   return data.filter((content) => contentDefineArr.includes(content.type));
  // }, [data]);

  // console.log({ contentDefineArr });

  return (
    <div>
      <Spin spinning={loading}>
        <Table
          bordered
          size="small"
          pagination={false}
          rowKey="id"
          dataSource={data}
          onRow={(r: ContentDefine) => {
            return {
              onClick: () => {
                setSelectedContentDefine(r);
                setVisibleModal(true);
                setModalStatus("update");
              },
            };
          }}
        >
          <Column
            title="Loại"
            dataIndex="type"
            key="type"
            render={(text, record: ContentDefine) => (
              <span>{ContentDefineTypeTrans[record.type]}</span>
            )}
          />

          <Column
            width={100}
            align="center"
            title="Thao tác"
            key="action"
            render={(text, record: ContentDefine) => (
              <span>
                <Space>
                  <Button
                    type="primary"
                    onClick={() => {
                      setSelectedContentDefine(record);
                      setVisibleModal(true);
                      setModalStatus("update");
                    }}
                  >
                    Cập nhật
                  </Button>
                </Space>
              </span>
            )}
          />
        </Table>

        <Pagination
          total={total}
          currentPage={query.page}
          onChange={fetchData}
        />
      </Spin>

      <ContentDefineModal
        onSubmitOk={fetchData}
        onClose={() => setVisibleModal(false)}
        visible={visibleModal}
        contentDefine={selectedContentDefine}
        status={modalStatus}
      />
    </div>
  );
});
