import { Device } from "./device";
import { Material } from "./material";
import { Staff } from "./staff";
import { TaskTemplate } from "./taskTemplate";
import { Unit } from "./unit";

export enum RequiredResourceType {
  Material = 'MATERIAL',
  Product = 'PRODUCT'
}

export interface RequiredResource {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  name: string;
  quantity: number;
  taskTemplateDevice: TaskTemplate
  taskTemplateMachine: TaskTemplate
  staff: Staff;
  device: Device;
  material: Material;
  type: RequiredResourceType
  unit: Unit

}
