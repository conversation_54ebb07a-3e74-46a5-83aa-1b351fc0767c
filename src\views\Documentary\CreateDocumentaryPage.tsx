import { Card, Col, Row, Form, But<PERSON>, Avatar } from "antd";
import PageTitle from "components/PageTitle/PageTitle";
import CustomInput from "components/Input/CustomInput";
import CustomSelect from "components/Input/CustomSelect";
import React, { useState } from "react";
import { Rule } from "antd/lib/form";
import CustomButton from "components/Button/CustomButton";

const rules: Rule[] = [{ required: true, message: "Trường này là bắt buộc" }];

function CreateDocumentaryPage({ title = "" }) {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // Sample options for select fields
  const folderOptions = [
    { label: "Thư mục dự án A", value: "folder-a" },
    { label: "Thư mục dự án B", value: "folder-b" },
    { label: "Th<PERSON> mục kỹ thuật", value: "folder-technical" },
    { label: "<PERSON>h<PERSON> mục báo cáo", value: "folder-report" },
  ];

  // Steps configuration for approval process
  const steps = [
    {
      title: "Tạo mới",
      time: "08:30 12/06/2025",
      user: "Ngô An - Nhân viên",
      note: "Note message",
      icon: "✓",
      status: "finish",
    },
    {
      title: "Duyệt cấp 1",
      time: "08:30 12/06/2025",
      user: "Trần Ánh - Giám sát",
      note: "Note message",
      icon: "✓",
      status: "finish",
    },
    {
      title: "Duyệt cấp 2",
      time: "08:30 12/06/2025",
      user: "Phạm Văn Trí - Quản lý",
      note: "Note message",
      icon: "✓",
      status: "finish",
    },
    {
      title: "Phát hành",
      time: "08:30 12/06/2025",
      user: "Phạm Văn Trí - Quản lý",
      note: "Note message",
      icon: "✓",
      status: "finish",
    },
  ];

  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      console.log("Form values:", values);
      // TODO: Implement API call to create documentary
    } catch (error) {
      console.error("Error creating documentary:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = () => {
    // TODO: Implement file upload functionality
    console.log("Upload file");
  };

  return (
    <div>
      <PageTitle
        title="Tạo tài liệu"
        breadcrumbs={["Báo cáo", "Tài liệu", "Tạo tài liệu"]}
      />

      <Form form={form} layout="vertical" onFinish={handleSubmit}>
        <Row gutter={24}>
          <Col span={18}>
            <Card className="content-card mb-6">
              <Card title="Thông tin tài liệu" className="mb-0 form-card">
                <Row gutter={16}>
                  {/* Left Column - File Upload (spans multiple rows) */}
                  <Col span={12}>
                    <Form.Item label="Bản vẽ" name="drawings">
                      <div
                        style={{
                          border: "2px dashed var(--color-neutral-n3)",
                          borderRadius: "8px",
                          padding: "60px 40px",
                          textAlign: "center",
                          backgroundColor: "var(--color-neutral-n0)",
                          cursor: "pointer",
                          minHeight: "320px",
                          display: "flex",
                          flexDirection: "column",
                          justifyContent: "center",
                        }}
                        onClick={handleFileUpload}
                      >
                        <div style={{ marginBottom: "16px" }}>
                          <svg
                            width="48"
                            height="48"
                            viewBox="0 0 24 24"
                            fill="none"
                            style={{ margin: "0 auto", display: "block" }}
                          >
                            <path
                              d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"
                              stroke="var(--color-neutral-n5)"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                            <polyline
                              points="14,2 14,8 20,8"
                              stroke="var(--color-neutral-n5)"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        </div>
                        <div style={{ fontWeight: "600", marginBottom: "8px" }}>
                          Tải lên tập đính kèm
                        </div>
                        <div
                          style={{
                            color: "var(--color-neutral-n5)",
                            marginBottom: "16px",
                          }}
                        >
                          PDF, Ảnh, Word, Excel
                        </div>
                        <div
                          style={{
                            display: "flex",
                            justifyContent: "center",
                            width: "100%",
                          }}
                        >
                          <CustomButton
                            className="cta-button"
                            variant="outline"
                            size="small"
                          >
                            Tải ảnh
                          </CustomButton>
                        </div>
                      </div>
                    </Form.Item>
                  </Col>

                  {/* Right Column - Form Fields */}
                  <Col span={12}>
                    <Row gutter={[0, 16]}>
                      {/* Document Name - Row 1 */}
                      <Col span={24}>
                        <Form.Item
                          name="documentName"
                          label="Tên tài liệu"
                          rules={rules}
                        >
                          <CustomInput placeholder="Nhập tên tài liệu" />
                        </Form.Item>
                      </Col>

                      {/* Folder - Row 2 */}
                      <Col span={24}>
                        <Form.Item name="folder" label="Thư mục" rules={rules}>
                          <CustomSelect
                            placeholder="Chọn thư mục"
                            options={folderOptions}
                          />
                        </Form.Item>
                      </Col>

                      {/* QR Code - Row 3 */}
                      <Col span={24}>
                        <Form.Item>
                          <div
                            style={{
                              display: "flex",
                              alignItems: "center",
                              gap: "16px",
                            }}
                          >
                            {/* QR Code placeholder */}
                            <div
                              style={{
                                width: "120px",
                                height: "120px",
                                border: "2px solid var(--color-neutral-n3)",
                                borderRadius: "8px",
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                backgroundColor: "var(--color-neutral-n1)",
                              }}
                            >
                              {/* QR Code pattern */}
                              <svg
                                width="100"
                                height="100"
                                viewBox="0 0 100 100"
                              >
                                {/* Simple QR code pattern */}
                                <rect
                                  x="0"
                                  y="0"
                                  width="20"
                                  height="20"
                                  fill="#000"
                                />
                                <rect
                                  x="0"
                                  y="80"
                                  width="20"
                                  height="20"
                                  fill="#000"
                                />
                                <rect
                                  x="80"
                                  y="0"
                                  width="20"
                                  height="20"
                                  fill="#000"
                                />
                                <rect
                                  x="40"
                                  y="40"
                                  width="20"
                                  height="20"
                                  fill="#000"
                                />
                                <rect
                                  x="20"
                                  y="20"
                                  width="10"
                                  height="10"
                                  fill="#000"
                                />
                                <rect
                                  x="60"
                                  y="20"
                                  width="10"
                                  height="10"
                                  fill="#000"
                                />
                                <rect
                                  x="20"
                                  y="60"
                                  width="10"
                                  height="10"
                                  fill="#000"
                                />
                                <rect
                                  x="60"
                                  y="60"
                                  width="10"
                                  height="10"
                                  fill="#000"
                                />
                                <rect
                                  x="10"
                                  y="40"
                                  width="10"
                                  height="10"
                                  fill="#000"
                                />
                                <rect
                                  x="70"
                                  y="40"
                                  width="10"
                                  height="10"
                                  fill="#000"
                                />
                                <rect
                                  x="40"
                                  y="10"
                                  width="10"
                                  height="10"
                                  fill="#000"
                                />
                                <rect
                                  x="40"
                                  y="70"
                                  width="10"
                                  height="10"
                                  fill="#000"
                                />
                              </svg>
                            </div>
                            <div>
                              <div
                                style={{
                                  fontWeight: "600",
                                  marginBottom: "4px",
                                }}
                              >
                                Mã QRCode truy cập nhanh
                              </div>
                              <div
                                style={{
                                  color: "var(--color-neutral-n5)",
                                  fontSize: "14px",
                                }}
                              >
                                Tự động sinh ra khi điền đầy đủ thông tin tài
                                liệu
                              </div>
                            </div>
                          </div>
                        </Form.Item>
                      </Col>
                    </Row>
                  </Col>

                  {/* Notes Section - Full Width */}
                  <Col span={24}>
                    <Form.Item name="notes" label="Ghi chú">
                      <CustomInput
                        type="textarea"
                        placeholder="Ghi chú"
                        rows={4}
                      />
                    </Form.Item>
                  </Col>
                </Row>

                {/* Action Buttons */}
                <div
                  style={{
                    display: "flex",
                    justifyContent: "flex-end",
                    gap: "12px",
                    marginTop: "20px",
                  }}
                >
                  <CustomButton
                    variant="outline"
                    onClick={() => form.resetFields()}
                    className="cta-button"
                  >
                    Hủy
                  </CustomButton>
                  <CustomButton
                    htmlType="submit"
                    loading={loading}
                    className="cta-button"
                  >
                    Thêm tài liệu
                  </CustomButton>
                </div>
              </Card>
            </Card>
          </Col>

          <Col span={6}>
            <Card
              className="content-card"
              style={{ position: "sticky", top: "20px" }}
              bodyStyle={{ padding: 0 }}
            >
              <div style={{ padding: "20px" }}>
                <h3
                  style={{
                    margin: "20px",
                    fontWeight: "700",
                    fontSize: "20px",
                    color: "#19345b",
                  }}
                >
                  Quy trình duyệt
                </h3>
                <div style={{ minHeight: "400px" }}>
                  {steps.map((step, index) => (
                    <div
                      key={index}
                      style={{
                        display: "flex",
                        marginBottom: "20px",
                        position: "relative",
                        alignItems: "flex-start",
                      }}
                    >
                      {/* Icon */}
                      <div
                        style={{
                          width: "32px",
                          height: "32px",
                          borderRadius: "50%",
                          backgroundColor: "#52c41a",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          color: "white",
                          fontSize: "16px",
                          fontWeight: "bold",
                          marginRight: "16px",
                          flexShrink: 0,
                        }}
                      >
                        ✓
                      </div>

                      {/* Content */}
                      <div style={{ flex: 1, minWidth: 0 }}>
                        {/* Title and Time Row */}
                        <div
                          style={{
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "center",
                            marginBottom: "4px",
                            gap: "8px",
                          }}
                        >
                          <div
                            style={{
                              fontWeight: "600",
                              fontSize: "16px",
                              color: "#19345b",
                              whiteSpace: "nowrap",
                              flexShrink: 0,
                            }}
                          >
                            {step.title}
                          </div>
                          <div
                            style={{
                              fontSize: "12px",
                              color: "#8c8c8c",
                              flexShrink: 0,
                              whiteSpace: "nowrap",
                            }}
                          >
                            {step.time}
                          </div>
                        </div>

                        {/* User with Avatar */}
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            marginBottom: "4px",
                          }}
                        >
                          <Avatar
                            size={20}
                            style={{
                              backgroundColor: "#1890ff",
                              marginRight: "8px",
                              flexShrink: 0,
                            }}
                          >
                            {step.user.charAt(0)}
                          </Avatar>
                          <span
                            style={{
                              fontSize: "14px",
                              color: "#595959",
                              fontWeight: "500",
                            }}
                          >
                            {step.user}
                          </span>
                        </div>

                        {/* Note */}
                        <div
                          style={{
                            fontSize: "14px",
                            color: "#8c8c8c",
                            marginLeft: "28px",
                          }}
                        >
                          {step.note}
                        </div>
                      </div>

                      {/* Connecting Line */}
                      {index < steps.length - 1 && (
                        <div
                          style={{
                            position: "absolute",
                            left: "15px",
                            top: "36px",
                            width: "2px",
                            height: "calc(100% - 12px)",
                            backgroundColor: "#d9d9d9",
                          }}
                        />
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </Card>

            {/* Người theo dõi Card */}
            <Card
              className="content-card"
              style={{ position: "sticky", top: "20px", marginTop: "16px" }}
              bodyStyle={{ padding: 0 }}
            >
              <div style={{ padding: "20px" }}>
                <h3
                  style={{
                    margin: "20px",
                    fontWeight: "700",
                    fontSize: "20px",
                    color: "#19345b",
                  }}
                >
                  Người theo dõi (6)
                </h3>

                {/* Thêm người Button */}
                <div style={{ padding: "0 20px 20px 20px" }}>
                  <CustomButton
                    variant="outline"
                    size="medium"
                    block
                    style={{
                      height: "40px",
                      fontSize: "14px",
                    }}
                  >
                    Thêm người
                  </CustomButton>
                </div>

                {/* User List */}
                <div style={{ padding: "0 20px" }}>
                  {[
                    { name: "Trần Ánh", code: "NV240", role: "Giám sát" },
                    { name: "Trần Ánh", code: "NV240", role: "Giám sát" },
                    { name: "Trần Ánh", code: "NV240", role: "Giám sát" },
                    { name: "Trần Ánh", code: "NV240", role: "Giám sát" },
                    { name: "Trần Ánh", code: "NV240", role: "Giám sát" },
                    { name: "Trần Ánh", code: "NV240", role: "Giám sát" },
                  ].map((user, index) => (
                    <div
                      key={index}
                      style={{
                        display: "flex",
                        alignItems: "center",
                        padding: "12px 0",
                        borderBottom: index < 5 ? "1px solid #f0f0f0" : "none",
                      }}
                    >
                      <Avatar
                        size={40}
                        style={{
                          backgroundColor: "#1890ff",
                          marginRight: "12px",
                          flexShrink: 0,
                        }}
                      >
                        {user.name.charAt(0)}
                      </Avatar>
                      <div style={{ flex: 1 }}>
                        <div
                          style={{
                            fontSize: "14px",
                            fontWeight: "500",
                            color: "#262626",
                            marginBottom: "2px",
                          }}
                        >
                          {user.name}
                        </div>
                        <div
                          style={{
                            fontSize: "12px",
                            color: "#8c8c8c",
                          }}
                        >
                          {user.code} | {user.role}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </Card>
          </Col>
        </Row>
      </Form>
    </div>
  );
}

export default CreateDocumentaryPage;
