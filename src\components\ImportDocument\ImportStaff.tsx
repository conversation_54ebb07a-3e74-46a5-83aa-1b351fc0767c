import { DownloadOutlined, InboxOutlined } from "@ant-design/icons";
import {
  <PERSON><PERSON>,
  Button,
  Modal,
  Space,
  Spin,
  Table,
  Upload,
  message,
} from "antd";
import { Rule } from "antd/es/form";
import { staffApi } from "api/staff.api";
import dayjs from "dayjs";
import { forwardRef, useEffect, useImperativeHandle, useState } from "react";
import { Link } from "react-router-dom";
import {
  Staff,
  CustomerStatus,
  CustomerStatusTrans,
  Gender,
  GenderTrans,
  MaritalStatus,
  MaritalStatusTrans,
} from "types/staff";
import { WorkStatus, WorkStatusTrans } from "types/workStatus";
import { readerData } from "utils/excel2";
import CustomButton from "components/Button/CustomButton";
import ImportPreviewModule from "./ImportPreviewModule";
import { handleConfirmImportExcel } from "utils/function";

const rules: Rule[] = [{ required: true }];
const { Dragger } = Upload;

export interface ImportStaffModal {
  open: () => void;
  close: () => void;
}

export interface IValidate {
  index: number;
  message: string;
}

interface DataImportReturn {
  msg: string;
  rowNum: number;
  status: string;
}

export interface StaffImport extends Staff {
  rowNum: number;
}

interface IProps {
  onSuccess?: () => void;
  createApi?: (data: any) => any;
  onUploaded?: (excelData: any, setData: (data: any) => any) => void;
  demoExcel?: string;
  guide?: React.ReactNode[];
  uploadText?: string;
  okText?: string;
  onClose?: () => void;
  titleText?: string;
  validateMessage?: IValidate[];
  onDownloadDemoExcel?: () => void;
  loadingDownloadDemo?: boolean;
}

const ImportStaff = forwardRef(
  (
    {
      onSuccess,
      createApi,
      onUploaded,
      onClose,
      validateMessage,
      guide,
      demoExcel,
      uploadText = "Kéo thả hoặc click vào đây để upload file",
      okText = "Nhập dữ liệu ngay",
      titleText = "Nhập excel dữ liệu",
      onDownloadDemoExcel,
      loadingDownloadDemo,
    }: IProps,
    ref
  ) => {
    const [errorsLog, setErrorsLog] = useState<any[]>([]);
    const [dataPosts, setDataPosts] = useState<StaffImport[]>([]);
    const [visible, setVisible] = useState<boolean>(false);
    const [loading, setLoading] = useState(false);
    const [hasValidationErrors, setHasValidationErrors] = useState(false);
    const [dataReturn, setDataReturn] = useState<{
      data: DataImportReturn[];
      successCount: number;
      errorCount: number;
    }>();

    // Add debug useEffect
    useEffect(() => {
      console.log("📊 dataPosts changed:", dataPosts);
      console.log("📊 dataPosts length:", dataPosts.length);
    }, [dataPosts]);

    useEffect(() => {
      if (validateMessage?.length) {
        setErrorsLog([]);
      }
    }, [validateMessage]);

    // Callback to receive validation status from ImportPreviewModule
    const handleValidationStatusChange = (hasErrors: boolean) => {
      console.log("🚨 Validation status changed:", hasErrors);
      setHasValidationErrors(hasErrors);
    };

    const getWorkStatus = (label: string): WorkStatus | undefined => {
      const entry = Object.values(WorkStatusTrans).find(
        (item) => item.label === label
      );
      return entry ? entry.value : undefined;
    };

    const getCustomerStatus = (label: string): CustomerStatus | undefined => {
      const entry = Object.values(CustomerStatusTrans).find(
        (item) => item.label === label
      );
      return entry ? entry.value : undefined;
    };

    const getGender = (label: string): Gender | undefined => {
      const entry = Object.values(GenderTrans).find(
        (item) => item.label === label
      );
      return entry ? entry.value : undefined;
    };

    const getMaritalStatus = (label: string): MaritalStatus | undefined => {
      const entry = Object.values(MaritalStatusTrans).find(
        (item) => item.label === label
      );
      return entry ? entry.value : undefined;
    };

    const handleOnImport = async () => {
      if (!dataPosts.length) return;
      let errors: any = [];

      try {
        setLoading(true);
        await handleConfirmImportExcel();
        const { data } = await staffApi.import({
          staffs: dataPosts.map((dataPost) => ({
            ...dataPost,
            workStatus: getWorkStatus(dataPost.workStatus),
            status: getCustomerStatus(dataPost.status),
            gender: getGender(dataPost.gender),
            maritalStatus: getMaritalStatus(dataPost.maritalStatus),
          })),
        });
        if (data.length) {
          const successCount = data.reduce(
            (acc: number, item: DataImportReturn) => {
              if (item.status == "ok") return acc + 1;
              return acc;
            },
            0
          );
          const errorCount = data.reduce(
            (acc: number, item: DataImportReturn) => {
              if (item.status == "error") return acc + 1;
              return acc;
            },
            0
          );
          if (errorCount == 0) {
            handleOnCancel();
          }
          setDataReturn({ data, successCount, errorCount });
          onSuccess?.();
          setDataPosts([]);
        }
      } catch (err) {
        console.log({ err });
      } finally {
        setLoading(false);
      }
    };

    const handleOnCancel = () => {
      setVisible(false);
      onClose?.();
    };

    useImperativeHandle(
      ref,
      () => ({
        open: () => {
          setVisible(true);
          setLoading(false);
        },
        close: () => setVisible(false),
      }),
      []
    );

    // Define columns for preview table
    const previewColumns = [
      {
        key: "rowNum",
        title: "Dòng excel",
        dataIndex: "rowNum",
        width: 100,
      },
      {
        key: "code",
        title: "Mã NV",
        dataIndex: "code",
      },
      {
        key: "fullName",
        title: "Họ tên *",
        dataIndex: "fullName",
        render: (text: string, record: any) => (
          <span className={!text ? "text-red-500" : ""}>{text || "Thiếu"}</span>
        ),
      },
      {
        key: "email",
        title: "Email *",
        dataIndex: "email",
        render: (text: string, record: any) => (
          <span className={!text ? "text-red-500" : ""}>{text || "Thiếu"}</span>
        ),
      },
      {
        key: "phone",
        title: "SĐT *",
        dataIndex: "phone",
        render: (text: string, record: any) => (
          <span className={!text ? "text-red-500" : ""}>{text || "Thiếu"}</span>
        ),
      },
      {
        key: "gender",
        title: "Giới tính *",
        dataIndex: "gender",
        render: (text: string, record: any) => (
          <span className={!text ? "text-red-500" : ""}>{text || "Thiếu"}</span>
        ),
      },
      {
        key: "errorMessage",
        title: "Lỗi",
        dataIndex: "errorMessage",
        width: 300,
        render: (text: string) => (
          <span className="text-red-500 whitespace-pre-line text-xs">
            {text}
          </span>
        ),
      },
    ];

    // Define required fields for validation - UPDATED to match CreateOrUpdateStaffPage
    const requiredFields: (keyof StaffImport)[] = [
      "fullName", // Họ tên
      "email", // Email
      "phone", // Số điện thoại
      "gender", // Giới tính
    ];

    const handleValidateData = (data: StaffImport[]): StaffImport[] => {
      console.log("🔍 handleValidateData called with:", data);
      return data.map((item) => {
        const additionalErrors: string[] = [];

        // Email validation (only if email exists)
        if (item.email && item.email.trim()) {
          if (!/\S+@\S+\.\S+/.test(item.email)) {
            additionalErrors.push("Email không đúng định dạng");
          }
        }

        // Phone validation (only if phone exists)
        if (item.phone && item.phone.trim()) {
          const phoneRegex = /^(\+84|84|0)(3|5|7|8|9)([0-9]{8})$/;
          const cleanPhone = item.phone.replace(/[\s\-\(\)]/g, "");
          if (!phoneRegex.test(cleanPhone)) {
            additionalErrors.push(
              "Số điện thoại không đúng định dạng Việt Nam"
            );
          }
        }

        // Gender validation (only if gender exists)
        if (item.gender && item.gender.trim()) {
          const validGenders = Object.values(GenderTrans).map((g) => g.label);
          if (!validGenders.includes(item.gender)) {
            additionalErrors.push(
              `Giới tính phải là một trong: ${validGenders.join(", ")}`
            );
          }
        }

        // Work Status validation (only if workStatus exists)
        if (item.workStatus && item.workStatus.trim()) {
          const validWorkStatuses = Object.values(WorkStatusTrans).map(
            (w) => w.label
          );
          if (!validWorkStatuses.includes(item.workStatus)) {
            additionalErrors.push(
              `Tình trạng làm việc phải là một trong: ${validWorkStatuses.join(
                ", "
              )}`
            );
          }
        }

        // Marital Status validation (only if maritalStatus exists)
        if (item.maritalStatus && item.maritalStatus.trim()) {
          const validMaritalStatuses = Object.values(MaritalStatusTrans).map(
            (m) => m.label
          );
          if (!validMaritalStatuses.includes(item.maritalStatus)) {
            additionalErrors.push(
              `Tình trạng hôn nhân phải là một trong: ${validMaritalStatuses.join(
                ", "
              )}`
            );
          }
        }

        console.log(
          "🔍 Additional validation for item:",
          item,
          "additional errors:",
          additionalErrors
        );

        // Return item with additional errors (don't overwrite existing errorMessage)
        return {
          ...item,
          errorMessage:
            additionalErrors.length > 0
              ? additionalErrors.join("; ")
              : undefined,
        };
      });
    };

    return (
      <Modal
        maskClosable={false}
        width={1200} // Increase width to accommodate more columns
        style={{ top: 50 }}
        visible={visible}
        onCancel={handleOnCancel}
        destroyOnClose={true}
        afterClose={() => {
          setDataPosts([]);
          setErrorsLog([]);
          setDataReturn(undefined);
        }}
        title={titleText}
        footer={[
          <CustomButton
            key="import"
            loading={loading}
            variant="primary"
            disabled={!dataPosts.length || hasValidationErrors}
            onClick={() => {
              handleOnImport();
            }}
          >
            {okText}
          </CustomButton>,
          <CustomButton
            key="close"
            variant="outline"
            className="cta-button"
            onClick={() => {
              handleOnCancel();
            }}
          >
            Đóng
          </CustomButton>,
        ]}
      >
        <Spin spinning={false}>
          {guide && (
            <Alert
              style={{ padding: "10px", marginBottom: "10px" }}
              message={<b>Lưu ý</b>}
              type="warning"
              description={
                <ul>
                  {guide.map((text, index) => (
                    <li key={index}>
                      <p>{text}</p>
                    </li>
                  ))}
                </ul>
              }
            />
          )}
          {demoExcel && (
            <Link to={demoExcel} target="_blank" download>
              <Space className={`flex gap-2 cursor-pointer`}>
                <DownloadOutlined />
                Tải file import mẫu{" "}
              </Space>
            </Link>
          )}
          {onDownloadDemoExcel && (
            <a>
              <Space
                className={`flex gap-2 cursor-pointer`}
                onClick={() => {
                  onDownloadDemoExcel();
                }}
                style={{ pointerEvents: loadingDownloadDemo ? "none" : "auto" }}
              >
                {loadingDownloadDemo ? (
                  <Spin spinning={loadingDownloadDemo} />
                ) : (
                  <DownloadOutlined />
                )}
                Tải file import mẫu
              </Space>
            </a>
          )}

          <Dragger
            style={{ marginTop: "0.5em" }}
            maxCount={1}
            multiple={false}
            beforeUpload={async (file) => {
              //Check file type
              const isCSVFile = file.name.includes("xlsx");
              if (isCSVFile === false) {
                message.error("Bạn chỉ có thể upload file excel!");
                return Upload.LIST_IGNORE;
              }
              const excelData = await readerData(file, 0);
              setDataReturn(undefined);
              console.log("Data khi import vào là", excelData);
              onUploaded?.(excelData, setDataPosts);
              return false;
            }}
            onChange={(info) => {
              //reset data
              if (info.fileList.length == 0) {
                setErrorsLog([]);
                setDataPosts([]);
              }
            }}
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">{uploadText}</p>
          </Dragger>

          {/* Add the new ImportPreviewModule */}
          <div className="mt-4">
            <ImportPreviewModule
              data={dataPosts}
              dataReturn={dataReturn}
              onValidateData={handleValidateData}
              requiredFields={requiredFields}
              duplicateCheckFields={["code", "phone", "email"]} // Kiểm tra trùng lặp mã nhân viên, số điện thoại và email
              columns={previewColumns}
              title="Kiểm tra nhập dữ liệu nhân viên"
              previewButtonText="Kiểm tra nhập dữ liệu nhân viên"
              onValidationStatusChange={handleValidationStatusChange}
              showAutoMessage={false} // Hoặc không cần prop này nếu dùng Option 2
            />
          </div>

          {/* ... existing dataReturn section ... */}
        </Spin>
      </Modal>
    );
  }
);

export default ImportStaff;
