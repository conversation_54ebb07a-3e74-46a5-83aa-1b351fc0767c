import * as ExcelJS from "exceljs";
import saveAs from "file-saver";
import { MaterialEnum, MaterialEnumTrans } from "types/material";
import { Season, SeasonTrans } from "types/season";
// import { <PERSON><PERSON>, <PERSON> } from "types/customer";
// import { Language } from "types/language";

const borderStyles = {
  top: { style: "thin" },
  left: { style: "thin" },
  bottom: { style: "thin" },
  right: { style: "thin" },
};

const fontRed = {
  color: { argb: "ff0000" },
  bold: true,
};

const bgYellow = {
  type: "pattern",
  pattern: "solid",
  fgColor: { argb: "ffff00" },
};

const bgRed = {
  type: "pattern",
  pattern: "solid",
  fgColor: { argb: "ff7373" },
};

const fontOdd = {
  color: { argb: "0070c0" },
  bold: true,
};
const fontEven = {
  color: { argb: "c00066" },
  bold: true,
};

export interface ExcelDataType {
  title?: string;
  required?: boolean;
  positive?: boolean;
  unique?: boolean;
  description?: string[];
  formuleData?: string[];
}

export const handleAddFormuleData = (
  worksheet: any,
  formule: string[],
  columnIndex: number
) => {
  //Cách này chỉ áp dụng cho các bảng có cột từ A->Z
  const alphabet = [
    "A",
    "B",
    "C",
    "D",
    "E",
    "F",
    "G",
    "H",
    "I",
    "J",
    "K",
    "L",
    "M",
    "N",
    "O",
    "P",
    "Q",
    "R",
    "S",
    "T",
    "U",
    "V",
    "W",
    "X",
    "Y",
    "Z",
  ];
  const columnName = alphabet[columnIndex];
  //Thêm danh hộp chọn danh mục
  const formuleData = '"' + formule.join(",") + '"';
  const address = `${columnName}2:${columnName}9999`;
  //@ts-ignore
  worksheet.dataValidations.add(address, {
    allowBlank: true,
    error: "Vui lòng chọn giá trị hợp lệ trong hộp chọn",
    errorTitle: "Giá trị không tồn tại",
    formulae: [formuleData],
    showErrorMessage: true,
    type: "list",
  });
};

export const autoWidth = (worksheet: ExcelJS.Worksheet) => {
  worksheet.columns?.forEach?.(function (column, i) {
    var maxLength = 0;
    //@ts-ignore
    column?.["eachCell"]({ includeEmpty: true }, function (cell) {
      var columnLength = cell.value ? cell.value.toString().length : 20;
      if (columnLength > maxLength) {
        maxLength = columnLength;
      }
    });
    column.width = maxLength < 20 ? 20 : maxLength;
  });
};

const addDemoFileSheet = (
  workbook: ExcelJS.Workbook,
  seasons: { value: Season; name: string }[],
  types: { value: MaterialEnum; name: string }[],
  // branches: Branch[],
  dataSheet: ExcelDataType[]
) => {
  const worksheetDemo = workbook.addWorksheet("Mẫu nhập", {
    pageSetup: { fitToPage: true, fitToHeight: 5, fitToWidth: 7 },
  });

  //Thêm danh hộp chọn danh mục
  const address = `season!$A$3:$A$${2 + seasons.length}`;
  // const branchAddress = `branch!$A$3:$A$${2 + branches.length}`;

  const type = `type!$A$3:$A$${2 + types.length}`;

  console.log("address", address);

  //@ts-ignore
  worksheetDemo.dataValidations.add("M2:M9999", {
    type: "list",
    allowBlank: true,
    formulae: [address],
  });

  //@ts-ignore
  // worksheetDemo.dataValidations.add("G2:G9999", {
  //   type: "list",
  //   allowBlank: true,
  //   formulae: [branchAddress],
  // });

  worksheetDemo.dataValidations.add("A2:A9999", {
    type: "list",
    allowBlank: true,
    formulae: [type],
  });

  // worksheetDemo.getCell("C2").dataValidation = {
  //   type: "list",
  //   allowBlank: true,
  //   formulae: [address],
  //   showInputMessage: true,
  // };

  const titleRow = worksheetDemo.getRow(1);
  titleRow.height = 50;
  dataSheet.forEach((cell, index) => {
    if (cell.formuleData) {
      handleAddFormuleData(worksheetDemo, cell.formuleData, index);
    }
    const currCell = titleRow.getCell(index + 1);
    currCell.value = cell.title;
    currCell.font = {
      bold: true,
      color: {
        argb: "ffffff",
      },
    };
    currCell.border = {
      top: {
        style: "thin",
        color: {
          argb: "000000",
        },
      },
      left: {
        style: "thin",
        color: {
          argb: "000000",
        },
      },
      right: {
        style: "thin",
        color: {
          argb: "000000",
        },
      },
      bottom: {
        style: "thin",
        color: {
          argb: "000000",
        },
      },
    };
    currCell.alignment = {
      vertical: "middle",
      horizontal: "center",
    };
    currCell.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "fe7b52" },
    };
  });

  autoWidth(worksheetDemo);
};

const handleDemoSheet = (
  workbook: ExcelJS.Workbook,
  dataSheet: ExcelDataType[]
) =>
  new Promise((resolve, reject) => {
    const worksheetDemo = workbook.addWorksheet("Hướng dẫn", {
      pageSetup: { fitToPage: true, fitToHeight: 5, fitToWidth: 7 },
    });
    const title = "Hướng dẫn nhập mẫu NVL";

    const headingRow = worksheetDemo.getRow(1);

    headingRow.getCell(1).value = title;
    headingRow.getCell(1).alignment = { horizontal: "center" };
    headingRow.getCell(1).font = { size: 15, bold: true };
    headingRow.getCell(1).border = {
      bottom: {
        style: "thin",
        color: {
          argb: "000000",
        },
      },
    };

    worksheetDemo.mergeCells(1, 1, 1, dataSheet.length);

    const titleRow = worksheetDemo.getRow(2);
    titleRow.height = 50;
    const conditionRow = worksheetDemo.getRow(3);
    conditionRow.height = 100;
    const descRow = worksheetDemo.getRow(4);
    descRow.height = 50;
    for (let i = 0; i < dataSheet.length; i++) {
      const title = dataSheet[i].title;
      const isRequired = dataSheet[i].required;
      titleRow.getCell(i + 1).value = title;

      conditionRow.getCell(i + 1).value = isRequired ? "Bắt buộc" : "Tùy chọn";

      titleRow.getCell(i + 1).font = { bold: true, color: { argb: "ffffff" } };
      conditionRow.getCell(i + 1).font = {
        bold: isRequired,
        color: isRequired ? { argb: "ff0505" } : { argb: "000000" },
      };
      titleRow.getCell(i + 1).alignment = { horizontal: "center" };
      conditionRow.getCell(i + 1).alignment = {
        ...conditionRow.getCell(i + 1).alignment,
        horizontal: "center",
      };
      descRow.getCell(i + 1).alignment = { horizontal: "center" };

      titleRow.getCell(i + 1).border = {
        bottom: {
          style: "thin",
          color: {
            argb: "000000",
          },
        },
      };
      conditionRow.getCell(i + 1).border = {
        bottom: {
          style: "thin",
          color: {
            argb: "000000",
          },
        },
      };

      titleRow.getCell(i + 1).fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "fe7b52" },
      };
      conditionRow.getCell(i + 1).fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "dddddd" },
      };
      titleRow.getCell(i + 1).alignment = {
        vertical: "middle",
        horizontal: "center",
      };
      conditionRow.getCell(i + 1).alignment = {
        vertical: "middle",
        horizontal: "center",
      };
      descRow.getCell(i + 1).alignment = {
        vertical: "middle",
        horizontal: "center",
      };
      const desc = dataSheet[i].description;

      desc?.forEach((value: string, index) => {
        const newDesc = worksheetDemo.getRow(4 + index);
        const currCell = newDesc.getCell(i + 1);
        currCell.value = value;
        currCell.alignment = {
          horizontal: "center",
          vertical: "middle",
        };
      });
    }
    autoWidth(worksheetDemo);
    return resolve(true);
  });

export const handleAddSheet = <T>(
  workbook: ExcelJS.Workbook,
  title: string,
  data: T[],
  heading: Partial<ExcelJS.Column>[]
) => {
  //Khai báo sheet
  const sheet = workbook.addWorksheet(title, {
    pageSetup: { fitToPage: true, fitToHeight: 5, fitToWidth: 7 },
  });
  //Khai báo giá trị của từng column
  sheet.columns = heading;
  sheet.state = "veryHidden";
  // Khai báo heading
  const headingRow = sheet.getRow(1);
  headingRow.getCell(1).value = title;
  headingRow.getCell(1).alignment = { horizontal: "center" };
  headingRow.getCell(1).font = { size: 15, bold: true };
  headingRow.getCell(1).border = {
    bottom: {
      style: "thin",
      color: {
        argb: "000000",
      },
    },
  };
  //Merge Cell heading
  sheet.mergeCells(1, 1, 1, heading.length);

  //Khai báo title
  const titleRow = sheet.getRow(2);
  heading.forEach((title, index) => {
    titleRow.getCell(index + 1).value = title.header as string;
    titleRow.getCell(index + 1).font = {
      bold: true,
    };
    titleRow.getCell(index + 1).alignment = {
      horizontal: "center",
    };
  });

  //Add từng row
  sheet.insertRows(3, data);

  //Fit width
  autoWidth(sheet);
};

const unitSheetHeading: Partial<ExcelJS.Column>[] = [
  // {
  //   header: "Đơn vị tính",
  //   key: "id",
  //   alignment: {
  //     horizontal: "center",
  //   },
  // },
  { header: "Loại chu kỳ", key: "name" },
];

const statusSheetHeading: Partial<ExcelJS.Column>[] = [
  // {
  //   header: "Đơn vị tính",
  //   key: "id",
  //   alignment: {
  //     horizontal: "center",
  //   },
  // },
  { header: "", key: "name" },
];

const materialExportDemo = async (companyId?: number) => {
  //Khai bao file
  const workbook = new ExcelJS.Workbook();

  // const { data: dataRes } = await branchApi.findAll({
  //   page: 1,
  //   limit: 100,
  //   companyId: companyId || undefined,
  // });
  // console.log("Xưởng lấy đc khi có companyId", {
  //   companyId,
  //   dataRes,
  // });
  // const branches = dataRes.branches;

  const data: ExcelDataType[] = [
    {
      title: "Loại NVL",
    },
    {
      title: "Mã nguyên liệu",
    },
    {
      title: "Tên nội bộ",
    },
    {
      title: "Tên NVL",
    },
    {
      title: "Ảnh material",
    },
    {
      title: "Ảnh material detail",
    },
    {
      title: "Video material",
    },
    {
      title: "Mô tả",
    },
    {
      title: "Tồn kho",
    },
    {
      title: "Đơn giá cộng thêm",
    },
    {
      title: "Màu",
    },

    {
      title: "Pattern",
    },
    {
      title: "Season",
    },
    {
      title: "Material",
    },
    {
      title: "Weight",
    },
    {
      title: "Stretchability",
    },
    {
      title: "Stretch direction",
    },
    {
      title: "Nhà sản xuất",
    },
    {
      title: "Collection",
    },
  ];

  addDemoFileSheet(
    workbook,
    Object.values(SeasonTrans).map(({ value, label }) => ({
      value,
      name: label,
    })),
    Object.values(MaterialEnumTrans).map(({ value, label }) => ({
      value,
      name: label,
    })),
    // branches,
    data
  );
  handleAddSheet(
    workbook,
    "season",
    Object.values(SeasonTrans).map(({ value, label }) => ({
      value,
      name: label,
    })),

    unitSheetHeading
  );
  handleAddSheet(
    workbook,
    "type",
    Object.values(MaterialEnumTrans).map(({ value, label }) => ({
      value,
      name: label,
    })),

    statusSheetHeading
  );
  // handleAddSheet(workbook, "branch", branches, statusSheetHeading);
  handleDemoSheet(workbook, data);

  workbook.xlsx.writeBuffer().then(function (buffer: any) {
    saveAs(
      new Blob([buffer], { type: "application/octet-stream" }),
      "Mẫu hướng dẫn nhập NVL.xlsx"
    );
  });
};

export default materialExportDemo;
