import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
} from "react";
import { debounce, uniqBy } from "lodash";
import { useAccount } from "hooks/useAccount";
import { Account } from "types/account";
import { QueryParams2 } from "types/query";
import CustomSelect from "components/Input/CustomSelect";
import { Select } from "antd";

type CustomFormItemProps = {
  value?: number;
  initQuery?: QueryParams2;
  disabled?: boolean;
  selectedAccount?: Account[];
  multiple?: boolean;
  onChange?: (value: any) => void;
  initOptionItem?: Account | Account[];
  valueIsOption?: boolean;
  allowClear?: boolean;
  placeholder?: string;
};

export interface AccountSelector {
  refresh(): void;
}

export const AccountSelector = forwardRef(
  (
    {
      value,
      onChange,
      initQuery,
      disabled,
      multiple = false,
      selectedAccount,
      initOptionItem,
      valueIsOption,
      allowClear = true,
      placeholder = "Chọn tài khoản",
    }: CustomFormItemProps,
    ref
  ) => {
    const { Accounts, loadingAccount, fetchAccount, queryAccount } = useAccount(
      {
        initQuery: { page: 1, limit: 10, ...initQuery },
      }
    );

    useImperativeHandle<any, AccountSelector>(
      ref,
      () => ({
        refresh() {
          fetchAccount();
        },
      }),
      []
    );

    useEffect(() => {
      fetchAccount();
    }, [selectedAccount]);

    const debounceSearch = useCallback(
      debounce((keyword) => {
        queryAccount.search = keyword;
        fetchAccount();
      }, 300),
      [queryAccount]
    );

    const options = useMemo(() => {
      let data = [...Accounts];
      if (initOptionItem) {
        if ((initOptionItem as Account[])?.length) {
          data = data.concat(initOptionItem);
        } else {
          data.push(initOptionItem as Account);
        }
      }
      return uniqBy(data, (item) => item.id).map((item) => ({
        label: item.username,
        value: item.id,
        item, // lưu nguyên object nếu cần dùng sau
      }));
    }, [Accounts, initOptionItem]);

    const handleChange = (v: any, option: any) => {
      if (valueIsOption) {
        if (option instanceof Array) {
          onChange?.(option.map((opt) => opt.item));
        } else {
          onChange?.(option.item);
        }
      } else {
        onChange?.(v);
      }
    };

    return (
      <Select
        value={value}
        onChange={handleChange}
        disabled={disabled}
        options={options}
        mode={multiple ? "multiple" : undefined}
        allowClear={allowClear}
        placeholder={placeholder}
        onSearch={debounceSearch}
        loading={loadingAccount}
        showSearch
        filterOption={false}
      />
    );
  }
);
