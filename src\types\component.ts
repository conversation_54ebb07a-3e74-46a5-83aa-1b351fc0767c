import { FileAttach } from "./fileAttach";
import { Material } from "./material";
import { MaterialType } from "./materialType";
import { Product } from "./product";
import { Variant } from "./variant";

export enum RelationComponentType {
  Dep = "DEP", // phụ thuộc
  Exclude = "EXCLUDE", // loại trừ
}
export interface RelationComponent {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  type: RelationComponentType;
  component1: Component; // from
  component2: Component; // to
}
export interface Component {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  code: string;
  name: string;
  privateName: string;
  avatar: string; // ảnh tỉ lệ 1:1
  desc: string;
  layer: number;
  level: number;
  featureImageShowType: ComponentShowType;
  extraPrice: number; // giá công thêm
  isEmbroidery: boolean; // true: cho phép thêu tên
  isBlocked: boolean;
  isDefault: boolean;
  note: string;
  position: number;
  standard: number;
  featureImage: string;
  isDefaultConfig: boolean;
  avatarBack: string; // ảnh măt sao
  maxLength: number;
  // custom thêm để xử lý
  selectedComponent?: Component;
  selectedVariant?: Variant;
  groupChildren: Component[];
  visible?: boolean;
  disabled?: boolean;
  monoText?: string;
  monoFont?: string;
  displayImage: DisplayImageType; // ảnh hiển thị
  zoomPosition: ZoomPosition;
  selectedMaterial?: Material;
  // cha - con
  parent: Component;
  children: Component[];
  //
  depComponents: RelationComponent[]; // phụ thuộc
  excludeComponents: RelationComponent[]; // loại trừ
  //
  product: Product;
  componentGroup: Component;
  variants: Variant[];
  fileAttachAvatar: FileAttach;
  fileAttachAvatarBack: FileAttach;
  fileAttachFeatureImage: FileAttach;
  material: Material;
  productMainComponent: Product;
  materialType?: MaterialType;
}
export interface ComponentCreating
  extends Omit<Component, "depComponents" | "excludeComponents"> {
  parentId: number;
  depComponents: number[];
  excludeComponents: number[];
  productId: number;
  componentGroupId: number;
  materialTypeId: number;
}

export enum DisplayImageType {
  Front = "FRONT",
  Back = "BACK",
}

export enum ComponentShowType {
  Component = "COMPONENT",
  Material = "MATERIAL",
}
export const ComponentShowTypeTrans = {
  [ComponentShowType.Component]: {
    value: ComponentShowType.Component,
    label: "Thành phần",
  },
  [ComponentShowType.Material]: {
    value: ComponentShowType.Material,
    label: "Nguyên vật liệu",
  },
};
export const DisplayImageTypeTrans = {
  [DisplayImageType.Front]: {
    value: DisplayImageType.Front,
    label: "Mặt trước",
  },
  [DisplayImageType.Back]: {
    value: DisplayImageType.Back,
    label: "Mặt sau",
  },
};
export enum ZoomPosition {
  topRight = "top_right",
  topCenter = "top_center",
  topLeft = "top_left",
  centerRight = "center_right",
  centerCenter = "center_center",
  centerLeft = "center_left",
  bottomRight = "bottom_right",
  bottomCenter = "bottom_center",
  bottomLeft = "bottom_left",
  default = "default",
}

export const ZoomPositionTrans = {
  [ZoomPosition.topRight]: {
    value: ZoomPosition.topRight,
    label: "Trên phải",
  },
  [ZoomPosition.topCenter]: {
    value: ZoomPosition.topCenter,
    label: "Trên giữa",
  },
  [ZoomPosition.topLeft]: {
    value: ZoomPosition.topLeft,
    label: "Trên trái",
  },
  [ZoomPosition.centerRight]: {
    value: ZoomPosition.centerRight,
    label: "Giữa phải",
  },
  [ZoomPosition.centerCenter]: {
    value: ZoomPosition.centerCenter,
    label: "Chính giữa",
  },
  [ZoomPosition.centerLeft]: {
    value: ZoomPosition.centerLeft,
    label: "Giữa trái",
  },
  [ZoomPosition.bottomRight]: {
    value: ZoomPosition.bottomRight,
    label: "Dưới phải",
  },
  [ZoomPosition.bottomCenter]: {
    value: ZoomPosition.bottomCenter,
    label: "Dưới giữa",
  },
  [ZoomPosition.bottomLeft]: {
    value: ZoomPosition.bottomLeft,
    label: "Dưới trái",
  },
  [ZoomPosition.default]: {
    value: ZoomPosition.default,
    label: "Mặc định",
  },
};
