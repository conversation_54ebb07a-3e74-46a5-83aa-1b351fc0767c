import { Avatar, Select } from "antd";
import { DefaultOptionType } from "antd/es/select";
import { SelectProps } from "antd/lib";
import { memberShipApi } from "api/memberShip.api";
import { MemberShipQuery, useMemberShip } from "hooks/useMemberShip";
import { debounce, isEmpty, uniqBy } from "lodash";
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
} from "react";
import { appStore } from "store/appStore";
import { MemberShip } from "types/memberShip";
import { QueryParams2 } from "types/query";

type CustomFormItemProps = {
  value?: number;
  initQuery?: MemberShipQuery;
  query?: MemberShipQuery;
  disabled?: boolean;
  multiple?: boolean;
  onChange?: (value: any, options: MemberShip[]) => void;
  selectProps?: SelectProps;
  initOptionItem?: MemberShip | MemberShip[];
  valueIsOption?: boolean;
  allowClear?: boolean;
  placeholder?: string;
};

export interface MaterialGroupSelector {
  refresh(): void;
}

/**
 * MembershipSelector lấy danh sách nhân viên theo projectId trong localStorage.
 */
export const MembershipSelector = forwardRef(
  (
    {
      value,
      onChange,
      initQuery,
      query: externalQuery,
      disabled,
      multiple = false,
      initOptionItem,
      valueIsOption,
      selectProps,
      allowClear = true,
      placeholder = "Chọn nhân viên",
    }: CustomFormItemProps,
    ref
  ) => {
    const projectId = appStore.currentProject?.id;

    const {
      memberShips,
      total,
      loading,
      fetchData,
      query,
      setQuery,
      isFetched,
      setData: setMemberShips,
    } = useMemberShip({
      initQuery: {
        page: 1,
        limit: 50,
        isActive: true,
        ...initQuery,
        projectId,
      },
    });

    useImperativeHandle<any, MaterialGroupSelector>(
      ref,
      () => ({
        refresh() {
          fetchData();
        },
      }),
      []
    );

    useEffect(() => {
      setQuery({ ...query, projectId });
      fetchData();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [projectId]);

    //xử lý nếu trong projects thiếu value hiện tại thì add vào
    useEffect(() => {
      if (value && isFetched) {
        const find = memberShips.find((e) => e.id == value);
        if (!find) {
          memberShipApi.findOne(value).then((res) => {
            if (!isEmpty(res.data)) {
              setMemberShips((prev) => [res.data, ...prev]);
            }
          });
        }
      }
    }, [value, memberShips, isFetched]);

    useEffect(() => {
      Object.assign(query, externalQuery);
      setQuery({ ...query });
      fetchData();
    }, [externalQuery]);

    const debounceSearch = useCallback(
      debounce((keyword) => {
        query.search = keyword;
        fetchData();
      }, 300),
      [query, fetchData]
    );

    const options = useMemo(() => {
      let data = [...memberShips];
      if (initOptionItem) {
        if (Array.isArray(initOptionItem)) {
          data = data.concat(initOptionItem);
        } else {
          data.push(initOptionItem);
        }
      }
      return uniqBy(data, (data) => data.id);
    }, [memberShips, initOptionItem]);

    return (
      <Select<MemberShip, DefaultOptionType>
        mode={multiple ? "multiple" : undefined}
        disabled={disabled}
        loading={loading}
        style={{ width: "100%" }}
        allowClear={allowClear}
        onSearch={debounceSearch}
        showSearch
        placeholder={placeholder}
        filterOption={false}
        value={
          valueIsOption
            ? value
              ? typeof value === "object"
                ? (value as MemberShip).id
                : value
              : undefined
            : value
        }
        onChange={(v, opts) => {
          if (v === undefined || (Array.isArray(v) && v.length == 0)) {
            debounceSearch(v);
          }
          if (valueIsOption) {
            if (v == undefined) {
              onChange?.(null, memberShips);
            } else if (opts instanceof Array) {
              onChange?.(
                opts?.map((v) => v.item),
                memberShips
              );
            } else {
              onChange?.(opts?.item, memberShips);
            }
          } else {
            onChange?.(v, memberShips);
          }
        }}
        {...selectProps}
      >
        {options?.map((item) => (
          <Select.Option
            item={item}
            value={item.id}
            key={item.id}
            label={item.name}
          >
            <div className="flex items-center gap-2">
              <span>{item.name}</span>
            </div>
          </Select.Option>
        ))}
      </Select>
    );
  }
);
