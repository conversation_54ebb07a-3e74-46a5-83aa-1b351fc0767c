import { Select } from "antd";
import { DefaultOptionType } from "antd/es/select";
import { SelectProps } from "antd/lib";
import { useColor } from "hooks/useColor";
import { useComponent } from "hooks/useComponent";
import { useMaterialGroup } from "hooks/useMaterialGroup";
import { debounce, uniqBy } from "lodash";
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
} from "react";
import { Component } from "types/component";
import { MaterialGroup } from "types/materialGroup";
import { QueryParams2 } from "types/query";

type CustomFormItemProps = {
  value?: number;
  initQuery?: QueryParams2;
  disabled?: boolean;
  selectedColor?: MaterialGroup[];
  multiple?: boolean;
  onChange?: (value: any) => void;
  selectProps?: SelectProps;
  initOptionItem?: Component | Component[];
  valueIsOption?: boolean;
  allowClear?: boolean;
  placeholder?: string;
  isMainComponent?: boolean;
  productId?: number;
  exceptMainComponentId?: number;
  isParent?: boolean;
};

export interface MaterialGroupSelector {
  refresh(): void;
}

/**
 * A demo that changes it's number value on click.
 * @param value initialValue passed by parent Form.Item.
 * @param onChange a callback for Form.Item to read its child component's value.
 * @constructor
 */
export const MainComponentSelector = forwardRef(
  (
    {
      value,
      onChange,
      initQuery,
      disabled,
      multiple = false,
      selectedColor,
      initOptionItem,
      valueIsOption,
      selectProps,
      allowClear = true,
      placeholder = "Chọn nhóm",
      isMainComponent = false,
      productId,
      exceptMainComponentId,
      isParent,
    }: CustomFormItemProps,
    ref
  ) => {
    const { components, total, loading, fetchData, query } = useComponent({
      initQuery: {
        page: 1,
        limit: 50,
        isMainComponent,
        productMainComponentId: productId,
        isParent,
        ...initQuery,
      },
    });
    useImperativeHandle<any, MaterialGroupSelector>(
      ref,
      () => ({
        refresh() {
          fetchData();
        },
      }),
      []
    );
    useEffect(() => {
      if (productId !== undefined) {
        query.productMainComponentId = productId;
        fetchData();
      } else {
        delete query.productMainComponentId;
      }
      // query.productMainComponentId = productId;
      // fetchData();
    }, [productId]);

    const debounceSearch = useCallback(
      debounce((keyword) => {
        query.search = keyword;
        fetchData();
      }, 300),
      [query]
    );

    const options = useMemo(() => {
      let data = [...components];

      if (initOptionItem) {
        if ((initOptionItem as Component[])?.length) {
          data = data.concat(initOptionItem as Component[]);
        } else {
          data.push(initOptionItem as Component);
        }
      }

      // Bỏ qua phần tử có id trùng với exceptMainComponentId nếu được truyền vào
      if (exceptMainComponentId !== undefined) {
        // debugger;
        data = data.filter((item) => item.id !== exceptMainComponentId);
      }

      return uniqBy(data, (item) => item.id);
    }, [components, initOptionItem, exceptMainComponentId]);

    return (
      <Select<any, DefaultOptionType>
        mode={multiple ? "multiple" : undefined}
        disabled={disabled}
        loading={loading}
        style={{ width: "100%", minWidth: 200 }}
        allowClear={allowClear}
        onSearch={debounceSearch}
        showSearch
        placeholder={placeholder}
        filterOption={false}
        value={value}
        onChange={(v, opts) => {
          if (v === undefined || v.length == 0) {
            debounceSearch(v);
          }
          if (valueIsOption) {
            if (opts instanceof Array) {
              onChange?.(opts?.map((v) => v.item));
            } else {
              onChange?.(opts?.item);
            }
          } else {
            onChange?.(v);
          }
        }}
        {...selectProps}
      >
        {options?.map((item) => (
          <Select.Option item={item} value={item.id} key={item.id}>
            <div className="flex items-center gap-2">
              <span>
                {isMainComponent ? (
                  item.name
                ) : (
                  <>
                    <span className="text-blue-400">{item.code}</span> -{" "}
                    {item.name}
                  </>
                )}
              </span>
            </div>
          </Select.Option>
        ))}
      </Select>
    );
  }
);
