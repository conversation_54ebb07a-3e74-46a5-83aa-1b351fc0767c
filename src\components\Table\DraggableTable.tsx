import {
  Dnd<PERSON>ontext,
  Drag<PERSON>nd<PERSON><PERSON>,
  Mouse<PERSON><PERSON>or,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Table } from "antd";
import { ColumnsType, TableProps } from "antd/es/table";
import React, { memo } from "react";
import { LuArrowDownUp } from "react-icons/lu";

// Row sortable wrapper
const SortableRow = ({ children, ...props }: any) => {
  const id = props["data-row-key"];
  const { setNodeRef, transform, transition, attributes, listeners } =
    useSortable({ id });

  const style: React.CSSProperties = {
    transform: CSS.Transform.toString(transform),
    transition,
    cursor: "grab",
  };

  return (
    <tr ref={setNodeRef} style={style} {...attributes} {...listeners}>
      {children}
    </tr>
  );
};

// Drag icon
const DragHandle = () => {
  return (
    <div className="cursor-grab flex justify-center items-center h-full">
      <LuArrowDownUp />
    </div>
  );
};

interface DraggableTableProps<T> extends TableProps<T> {
  dataSource: T[];
  onChangeOrder: (newList: T[]) => void;
}

function DraggableTable<T extends object & { id?: number }>({
  dataSource,
  onChangeOrder,
  columns,
  ...props
}: DraggableTableProps<T>) {
  const sensors = useSensors(useSensor(MouseSensor));

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active?.id !== over?.id) {
      const oldIndex = dataSource.findIndex((item) => item.id === active.id);
      const newIndex = dataSource.findIndex((item) => item.id === over?.id);

      const newList = arrayMove([...dataSource], oldIndex, newIndex);
      onChangeOrder(newList);
    }
  };

  const columnsWithDrag: ColumnsType<T> = [
    ...(columns || []),
    {
      title: "Sắp xếp",
      width: 70,
      align: "center",
      key: "sort",
      render: () => <DragHandle />,
    },
  ];

  return (
    <DndContext sensors={sensors} onDragEnd={handleDragEnd}>
      <SortableContext
        items={dataSource.map((item) => item.id!)}
        strategy={verticalListSortingStrategy}
      >
        <Table
          {...props}
          rowKey={(record) => record.id!}
          columns={columnsWithDrag}
          dataSource={dataSource}
          pagination={false}
          components={{
            body: {
              row: (rowProps: any) => <SortableRow {...rowProps} />,
            },
          }}
        />
      </SortableContext>
    </DndContext>
  );
}

export default DraggableTable;
