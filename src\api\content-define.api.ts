import { request } from "utils/request";
import { AxiosPromise } from "axios";


export const contentDefineApi = {
    findAll: (params?: any): AxiosPromise<any> => request({
        url: '/v1/admin/contentDefine',
        params
    }),
    create: (data: any): AxiosPromise<any> => request({
        url: '/v1/admin/contentDefine',
        data,
        method: 'post'
    }),
    update: (id: number, data: any): AxiosPromise<any> => request({
        url: `/v1/admin/contentDefine/${id}`,
        method: 'patch',
        data
    }),
    delete: (id: number): AxiosPromise<any> => request({
        url: `/v1/admin/contentDefine/${id}`,
        method: 'delete'
    }),
}
