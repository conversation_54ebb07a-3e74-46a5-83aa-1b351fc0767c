import React, { useState } from 'react';
import { Card, Table, Tag, Space } from 'antd';
import FilterBar, { FilterOption } from 'components/Filter/FilterBar';
import { useThemeColors } from 'theme/useThemeColors';
import './FilterDemo.scss';

interface Product {
  id: string;
  name: string;
  category: string;
  status: string;
  price: number;
}

const FilterDemo: React.FC = () => {
  const { color } = useThemeColors();
  const [loading, setLoading] = useState(false);
  const [filteredData, setFilteredData] = useState<Product[]>([]);
  const [activeFilters, setActiveFilters] = useState<Record<string, any>>({});

  // Sample data
  const products: Product[] = [
    { id: 'P001', name: 'Laptop Dell XPS 13', category: 'Laptop', status: 'Còn hàng', price: 30000000 },
    { id: 'P002', name: 'iPhone 13 Pro', category: 'Đi<PERSON>n thoại', status: 'Còn hàng', price: 25000000 },
    { id: 'P003', name: 'Samsung Galaxy S21', category: 'Điện thoại', status: 'Hết hàng', price: 18000000 },
    { id: 'P004', name: 'Macbook Pro M1', category: 'Laptop', status: 'Còn hàng', price: 35000000 },
    { id: 'P005', name: 'iPad Pro 2021', category: 'Máy tính bảng', status: 'Còn hàng', price: 20000000 },
    { id: 'P006', name: 'Acer Predator', category: 'Laptop', status: 'Hết hàng', price: 28000000 },
    { id: 'P007', name: 'Xiaomi Mi 11', category: 'Điện thoại', status: 'Còn hàng', price: 12000000 },
    { id: 'P008', name: 'Samsung Tab S7', category: 'Máy tính bảng', status: 'Hết hàng', price: 15000000 },
  ];

  // Filter options
  const categoryOptions: FilterOption[] = [
    { value: 'all', label: 'Tất cả các nhóm' },
    { value: 'Laptop', label: 'Laptop' },
    { value: 'Điện thoại', label: 'Điện thoại' },
    { value: 'Máy tính bảng', label: 'Máy tính bảng' },
  ];

  const statusOptions: FilterOption[] = [
    { value: 'all', label: 'Tất cả trạng thái' },
    { value: 'Còn hàng', label: 'Còn hàng' },
    { value: 'Hết hàng', label: 'Hết hàng' },
  ];

  // Handle filter application
  const handleFilter = (filters: Record<string, any>) => {
    setLoading(true);
    setActiveFilters(filters);
    
    // Simulate API call
    setTimeout(() => {
      let result = [...products];
      
      // Apply search filter
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        result = result.filter(item => 
          item.name.toLowerCase().includes(searchTerm) || 
          item.id.toLowerCase().includes(searchTerm)
        );
      }
      
      // Apply category filter
      if (filters.category && filters.category !== 'all') {
        result = result.filter(item => item.category === filters.category);
      }
      
      // Apply status filter
      if (filters.status && filters.status !== 'all') {
        result = result.filter(item => item.status === filters.status);
      }
      
      setFilteredData(result);
      setLoading(false);
    }, 500);
  };

  // Handle filter reset
  const handleReset = () => {
    setActiveFilters({});
    setFilteredData(products);
  };

  // Initialize data on component mount
  React.useEffect(() => {
    setFilteredData(products);
  }, []);

  // Table columns
  const columns = [
    {
      title: 'Mã sản phẩm',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: 'Tên sản phẩm',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Nhóm hàng',
      dataIndex: 'category',
      key: 'category',
      render: (category: string) => (
        <Tag color="blue">{category}</Tag>
      ),
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'Còn hàng' ? 'green' : 'red'}>{status}</Tag>
      ),
    },
    {
      title: 'Giá bán',
      dataIndex: 'price',
      key: 'price',
      render: (price: number) => (
        <span>{price.toLocaleString()} VNĐ</span>
      ),
    },
  ];

  return (
    <div className="filter-demo-page">
      <Card title="Danh sách sản phẩm" className="filter-demo-card">
        <FilterBar
          searchPlaceholder="Tìm kiếm theo mã, tên sản phẩm"
          filterGroups={[
            {
              key: 'category',
              label: 'Nhóm hàng',
              options: categoryOptions,
              value: activeFilters.category || 'all',
            },
            {
              key: 'status',
              label: 'Trạng thái',
              options: statusOptions,
              value: activeFilters.status || 'all',
            },
          ]}
          onFilter={handleFilter}
          onReset={handleReset}
          loading={loading}
        />
        
        <Table
          columns={columns}
          dataSource={filteredData}
          rowKey="id"
          loading={loading}
          pagination={{ pageSize: 5 }}
        />
      </Card>
    </div>
  );
};

export default FilterDemo;