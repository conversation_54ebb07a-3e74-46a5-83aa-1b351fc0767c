import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
} from "react";
import { debounce, uniqBy } from "lodash";
import { QueryParams2 } from "types/query";
import CustomSelect from "components/Input/CustomSelect";
import { Select } from "antd";
import { useApprovalList } from "hooks/useApprovalList";
import { ApprovalList } from "types/approvalList";
import { useApprovalTemplate } from "hooks/useApprovalTemplate";
import { ApprovalTemplate } from "types/approvalTemplate";

type CustomFormItemProps = {
  value?: number;
  initQuery?: QueryParams2;
  disabled?: boolean;
  selectedApprovalList?: ApprovalTemplate[];
  multiple?: boolean;
  onChange?: (value: any) => void;
  initOptionItem?: ApprovalTemplate | ApprovalTemplate[];
  valueIsOption?: boolean;
  allowClear?: boolean;
  placeholder?: string;
  type?: ApprovalList["type"];
};

export interface ApprovalListSelector {
  refresh(): void;
}

export const ApprovalTemplateSelector = forwardRef(
  (
    {
      value,
      onChange,
      initQuery,
      disabled,
      multiple = false,
      selectedApprovalList,
      initOptionItem,
      valueIsOption,
      allowClear = true,
      placeholder = "Chọn",
      type,
    }: CustomFormItemProps,
    ref
  ) => {
    const { approvalTemplates, loading, fetchData, query } =
      useApprovalTemplate({
        initQuery: {
          page: 1,
          limit: 10,
          type,
          ...initQuery,
        }, // truyền type vào query
      });

    useImperativeHandle<any, ApprovalListSelector>(
      ref,
      () => ({
        refresh() {
          fetchData();
        },
      }),
      []
    );

    useEffect(() => {
      fetchData();
    }, [selectedApprovalList]);

    const debounceSearch = useCallback(
      debounce((keyword) => {
        query.search = keyword;
        fetchData();
      }, 300),
      [query]
    );

    const options = useMemo(() => {
      let data = [...approvalTemplates].filter(
        (item) => item.isActive && item.isDefault
      );
      if (initOptionItem) {
        if ((initOptionItem as ApprovalTemplate[])?.length) {
          data = data.concat(initOptionItem);
        } else {
          data.push(initOptionItem as ApprovalTemplate);
        }
      }
      return uniqBy(data, (item) => item.id).map((item) => ({
        label: item.name,
        value: item.id,
        item, // lưu nguyên object nếu cần dùng sau
      }));
    }, [approvalTemplates, initOptionItem]);

    const handleChange = (v: any, option: any) => {
      if (valueIsOption) {
        if (v == undefined) {
          onChange?.(null);
        } else if (option instanceof Array) {
          onChange?.(option.map((opt) => opt.item));
        } else {
          onChange?.(option.item);
        }
      } else {
        onChange?.(v);
      }
    };

    return (
      <Select
        value={value}
        onChange={handleChange}
        disabled={disabled}
        options={options}
        mode={multiple ? "multiple" : undefined}
        allowClear={allowClear}
        placeholder={placeholder}
        onSearch={debounceSearch}
        loading={loading}
        showSearch
        filterOption={false}
      />
    );
  }
);
