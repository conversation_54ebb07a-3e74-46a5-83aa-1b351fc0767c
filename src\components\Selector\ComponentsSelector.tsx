import { Select } from "antd";
import { DefaultOptionType } from "antd/es/select";
import { SelectProps } from "antd/lib";
import { useColor } from "hooks/useColor";
import { useComponent } from "hooks/useComponent";
import { useMaterialGroup } from "hooks/useMaterialGroup";
import { debounce, uniqBy } from "lodash";
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
} from "react";
import { Component } from "types/component";
import { MaterialGroup } from "types/materialGroup";
import { QueryParams2 } from "types/query";
import { uniqueArrayByKey } from "utils/common";

type CustomFormItemProps = {
  value?: number;
  initQuery?: QueryParams2;
  disabled?: boolean;
  selectedColor?: MaterialGroup[];
  multiple?: boolean;
  onChange?: (value: any) => void;
  selectProps?: SelectProps;
  initOptionItem?: Component | Component[];
  valueIsOption?: boolean;
  allowClear?: boolean;
  placeholder?: string;
  isMainComponent?: boolean;
  productId?: number;
  componentGroupId?: number;
  loading?: boolean;
  isDisplayCodeAndName?: boolean;
};

export interface ComponentsSelectorRef {
  refresh(): void;
  addData: (item: Component) => void;
}

/**
 * A demo that changes it's number value on click.
 * @param value initialValue passed by parent Form.Item.
 * @param onChange a callback for Form.Item to read its child component's value.
 * @constructor
 */
export const ComponentsSelector = forwardRef(
  (
    {
      value,
      onChange,
      initQuery,
      disabled,
      multiple = false,
      selectedColor,
      initOptionItem,
      valueIsOption,
      selectProps,
      allowClear = true,
      placeholder = "Chọn nhóm",
      isMainComponent = false,
      componentGroupId,
      productId,
      loading,
      isDisplayCodeAndName = false,
    }: CustomFormItemProps,
    ref
  ) => {
    // console.log("Component groupd id là bao nhiêu");
    console.log("[ComponentsSelector].productId:", productId);

    const {
      components,
      total,
      loading: componentLoading,
      fetchData,
      query,
      setData: setComponents,
    } = useComponent({
      initQuery: {
        page: 1,
        limit: 50,
        isMainComponent,
        componentGroupId,
        productId: productId,
        ...initQuery,
      },
    });

    console.log("[ComponentsSelector].components:", components);

    useImperativeHandle<any, ComponentsSelectorRef>(
      ref,
      () => ({
        refresh() {
          fetchData();
        },
        addData(item) {
          components.push(item);
          setComponents(uniqueArrayByKey(components, "id"));
        },
      }),
      [components]
    );

    useEffect(() => {
      if (componentGroupId !== undefined) {
        query.componentGroupId = componentGroupId;
      } else {
        delete query.componentGroupId;
      }
      if (productId !== undefined) {
        query.productId = productId;
      } else {
        delete query.productId;
      }
      fetchData();
    }, [componentGroupId, productId]);

    const debounceSearch = useCallback(
      debounce((keyword) => {
        query.search = keyword;
        fetchData();
      }, 300),
      [query]
    );

    const options = useMemo(() => {
      let data = [...components];

      if (initOptionItem) {
        if ((initOptionItem as Component[])?.length) {
          data = data.concat(initOptionItem);
        } else {
          data.push(initOptionItem as Component);
        }
      }

      return uniqBy(data, (data) => data.id);
    }, [components, initOptionItem]);

    console.log("Init options là", initOptionItem);

    return (
      <Select<any, DefaultOptionType>
        mode={multiple ? "multiple" : undefined}
        disabled={disabled}
        // loading={loading || componentLoading}
        loading={componentLoading}
        style={{ width: "100%", minWidth: 200 }}
        allowClear={allowClear}
        onSearch={debounceSearch}
        showSearch
        placeholder={placeholder}
        filterOption={false}
        value={value}
        onChange={(v, opts) => {
          if (v === undefined || v.length == 0) {
            debounceSearch(v);
          }
          if (valueIsOption) {
            if (opts instanceof Array) {
              onChange?.(opts?.map((v) => v.item));
            } else {
              onChange?.(opts?.item);
            }
          } else {
            onChange?.(v);
          }
        }}
        {...selectProps}
      >
        {options?.map((item) => (
          <Select.Option item={item} value={item.id} key={item.id}>
            <div className="flex items-center gap-2">
              {isDisplayCodeAndName ? (
                <span>
                  <>
                    <span>
                      {item.code}-{item.name}
                    </span>
                  </>
                </span>
              ) : (
                <span>
                  {isMainComponent ? (
                    item.name
                  ) : (
                    <>
                      <span>{item.code}</span>
                    </>
                  )}
                </span>
              )}
            </div>
          </Select.Option>
        ))}
      </Select>
    );
  }
);
