import {
  <PERSON><PERSON>,
  Card,
  Col,
  DatePicker,
  Form,
  message,
  Row,
  Input,
  Spin,
  Space,
  Upload,
  Image,
} from "antd";
import { Rule } from "antd/lib/form";
import { UploadOutlined } from "@ant-design/icons";
import type { UploadProps, UploadFile } from "antd/lib/upload";
import CustomButton from "components/Button/CustomButton";
import CustomInput from "components/Input/CustomInput";
import CustomSelect from "components/Input/CustomSelect";
import PageTitle from "components/PageTitle/PageTitle";
import React, { useEffect, useState, useMemo } from "react";
import {
  useNavigate,
  useParams,
  useSearchParams,
  useLocation,
} from "react-router-dom";
import { getTitle } from "utils";
import { settings } from "settings";
import { PermissionNames } from "types/PermissionNames";
import { ModalStatus } from "types/modal";
import dayjs from "dayjs";
import { isEmpty } from "lodash";
import clsx from "clsx";
import { observer } from "mobx-react";
import { checkRoles } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import UploadImageIcon from "assets/svgs/UploadImageIcon";
import { useFileAttach } from "hooks/useFileAttach";
import {
  FileAttach,
  FileAttachType,
  FileAttachTypeTrans,
} from "types/fileAttach";
import { appStore } from "store/appStore";
import { useDictionary } from "hooks/useDictionary";
import { DictionaryType } from "types/dictionary";
import { fileAttachApi } from "api/fileAttach.api";
import { formatDate } from "utils/date";
import { $url } from "utils/url";
import "./ImagePage.scss";
import { getMimeTypeCategory } from "utils/file";

const { Dragger } = Upload;
const { TextArea } = Input;

const rules: Rule[] = [{ required: true, message: "Trường này là bắt buộc" }];

interface Props {
  title: string;
  status: ModalStatus;
  projectId?: number;
}

interface ImageData {
  id?: string;
  imageSrc?: string;
  projectId?: number;
  projectName?: string;
  captureDate?: string;
  location?: string;
  type?: string;
  tags?: string;
  isActive?: boolean;
  // Add new fields
  name?: string;
  size?: number;
  mimetype?: string;
  uid?: string;
}

function CreateOrUpdateImagePage({ title = "", projectId = 0, status }: Props) {
  const { haveEditPermission } = checkRoles(
    { edit: PermissionNames.imagesEdit, add: PermissionNames.imagesAdd },
    permissionStore.permissions
  );

  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [loadingFetch, setLoadingFetch] = useState(false);
  const [selectedImage, setSelectedImage] = useState<ImageData | null>(null);
  const [readonly, setReadonly] = useState(true);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const navigate = useNavigate();
  const params = useParams();
  const [searchParams] = useSearchParams();
  const location = useLocation();
  const searchParamsFromLocation = new URLSearchParams(location.search);
  const projectIdFromQuery = searchParamsFromLocation.get("projectId");
  const currentFolder = searchParamsFromLocation.get("folder");
  const effectiveProjectId =
    projectId || (projectIdFromQuery ? Number(projectIdFromQuery) : undefined);

  // Initialize useFileAttach hook
  const {
    uploadFile,
    createFileAttach,
    updateFileAttach,
    uploading,
    fileAttaches,
    fetchData,
  } = useFileAttach({
    initQuery: {
      page: 1,
      limit: 10,
      path: currentFolder ? `/${currentFolder}` : "/",
    },
  });

  // Initialize dictionary hooks for dropdowns
  const { dictionaries: categories, fetchData: fetchCategories } =
    useDictionary({
      initQuery: {
        page: 1,
        limit: 100,
        type: DictionaryType.FileAttachCategory,
      },
    });

  const { dictionaries: workTypes, fetchData: fetchWorkTypes } = useDictionary({
    initQuery: {
      page: 1,
      limit: 100,
      type: DictionaryType.WorkType,
    },
  });

  // Fetch dictionary data on component mount
  useEffect(() => {
    fetchCategories();
    fetchWorkTypes();
  }, []);

  // Transform dictionary data to options
  const typeOptions = useMemo(() => Object.values(FileAttachTypeTrans), []);

  const workTypeOptions = useMemo(
    () =>
      workTypes.map((type) => ({
        label: type.name,
        value: type.name,
      })),
    [workTypes]
  );

  // Initialize form with today's date
  const initialDate = useMemo(() => dayjs(), []);

  const setDataToForm = (data: ImageData) => {
    console.log("Setting data to form:", data); // Debug log

    form.setFieldsValue({
      projectId: data.projectId?.toString(),
      captureDate: data.captureDate ? dayjs(data.captureDate) : null,
      location: data.location,
      type: data.type,
      tags: data.tags,
    });

    // Set file list if image exists
    if (data.imageSrc) {
      setFileList([
        {
          uid: "-1",
          name: "image.jpg",
          status: "done",
          url: data.imageSrc,
        },
      ]);
    }
  };

  useEffect(() => {
    document.title = getTitle(title);

    if (status === "update") {
      const imageId = params.id;
      if (imageId) {
        fetchImageData(imageId);
        // Check if we're in view mode (no update=1 in URL)
        const isViewMode = searchParams.get("update") !== "1";
        setReadonly(isViewMode);
      }
    } else {
      setReadonly(false);
      // Set default project if provided
      if (effectiveProjectId) {
        form.setFieldValue("projectId", effectiveProjectId.toString());
      }
    }
  }, [status, params.id, searchParams, effectiveProjectId]);

  const fetchImageData = async (id: string) => {
    setLoadingFetch(true);
    try {
      const response = await fileAttachApi.findById(Number(id));
      const imageData = response.data;

      if (!imageData) {
        message.error("Không tìm thấy hình ảnh");
        navigate("/404");
        return;
      }

      const transformedData: ImageData = {
        id: imageData.id.toString(),
        imageSrc: imageData.url,
        projectId: appStore.currentProject?.id,
        projectName: appStore.currentProject?.name,
        captureDate: imageData.createdAt ? formatDate(imageData.createdAt) : "",
        location: imageData.address,
        type: imageData.type,
        tags: imageData.tag,
        isActive: imageData.isActive,
        name: imageData.name,
        size: imageData.size,
        mimetype: imageData.mimetype,
        uid: imageData.uid,
      };

      setSelectedImage(transformedData);
      setDataToForm(transformedData);

      return transformedData;
    } catch (error) {
      console.error("Error fetching image data:", error);
      message.error("Lỗi khi tải dữ liệu hình ảnh");
      navigate("/404");
    } finally {
      setLoadingFetch(false);
    }
  };

  // Get file from upload list
  const getFileFromList = (): File | null => {
    if (fileList.length === 0) return null;

    const fileItem = fileList[0];
    if (fileItem.originFileObj) {
      return fileItem.originFileObj;
    }
    return null;
  };

  // Upload props
  const uploadProps: UploadProps = {
    name: "file",
    multiple: false,
    fileList: fileList,
    onChange: async ({ fileList: newFileList }) => {
      // Only handle when there's a new file
      if (newFileList.length > 0 && newFileList[0].originFileObj) {
        const file = newFileList[0].originFileObj;

        // Upload file first
        const uploadResponse = await uploadFile(file);
        if (uploadResponse && uploadResponse.data) {
          // Get FileAttachType based on mimetype
          const fileType = getMimeTypeCategory(uploadResponse.data.mimetype);

          // Update file list with server URL for preview
          setFileList([
            {
              uid: uploadResponse.data.filename,
              name: uploadResponse.data.originalname,
              status: "done",
              url: $url(uploadResponse.data.path),
            },
          ]);

          // Store upload info for later use in create/update
          form.setFieldsValue({
            image: {
              filename: uploadResponse.data.filename,
              originalname: uploadResponse.data.originalname,
              path: uploadResponse.data.path,
              size: uploadResponse.data.size,
              mimetype: uploadResponse.data.mimetype,
              url: $url(uploadResponse.data.path),
            },
            type: fileType, // Automatically set the type based on mimetype
          });
        }
      } else {
        setFileList(newFileList);
      }
    },
    beforeUpload: (file) => {
      const isImage = file.type.startsWith("image/");
      if (!isImage) {
        message.error("Chỉ có thể tải lên file hình ảnh!");
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        message.error("Hình ảnh phải nhỏ hơn 10MB!");
        return false;
      }
      return false; // Prevent automatic upload
    },
    onDrop(e) {
      console.log("Dropped files", e.dataTransfer.files);
    },
  };

  // Create data
  const createData = async () => {
    try {
      await form.validateFields();
      const formData = form.getFieldsValue();
      const imageData = formData.image;

      if (!imageData) {
        message.error("Vui lòng tải lên hình ảnh");
        return;
      }

      const projectId =
        Number(formData.projectId) || appStore.currentProject?.id;
      if (!projectId) {
        message.error("Không tìm thấy thông tin dự án");
        return;
      }

      setLoading(true);

      // Create file attachment with uploaded file info
      const result = await createFileAttach({
        projectId,
        fileAttachCategoryId: 0,
        fileAttach: {
          name: imageData.originalname,
          type: FileAttachType.Image,
          url: imageData.url,
          path: currentFolder ? `/${currentFolder}` : "/",
          size: imageData.size,
          desc: `Image uploaded for project ${projectId}`,
          isActive: true,
          mimetype: imageData.mimetype,
          uid: imageData.filename,
          date: formData.captureDate?.format("YYYY-MM-DD"),
          address: formData.location,
          tag: formData.tags,
        },
      });

      if (result) {
        message.success("Thêm hình ảnh thành công!");
        navigate(
          `/doc-management/${PermissionNames.imagesList}${
            currentFolder ? `?folder=${currentFolder}` : ""
          }`
        );
      }
    } catch (error) {
      console.error("Error creating image:", error);
      message.error("Lỗi khi thêm hình ảnh");
    } finally {
      setLoading(false);
    }
  };

  // Update data
  const updateData = async () => {
    try {
      await form.validateFields();
      const formData = form.getFieldsValue();
      const imageData = formData.image;

      if (!selectedImage?.id) {
        message.error("Không tìm thấy ID hình ảnh");
        return;
      }

      const projectId =
        Number(formData.projectId) || appStore.currentProject?.id;
      if (!projectId) {
        message.error("Không tìm thấy thông tin dự án");
        return;
      }

      setLoading(true);

      // Prepare update data
      const updatePayload = {
        projectId,
        fileAttachCategoryId: 0,
        fileAttach: {
          name: imageData ? imageData.originalname : selectedImage.name,
          type: formData.type || FileAttachType.Image,
          url: imageData ? imageData.url : selectedImage.imageSrc,
          path: currentFolder ? `/${currentFolder}` : "/",
          size: imageData ? imageData.size : selectedImage.size,
          desc: `Updated image for project ${projectId}`,
          isActive: true,
          mimetype: imageData ? imageData.mimetype : selectedImage.mimetype,
          uid: imageData ? imageData.filename : selectedImage.uid,
          date: formData.captureDate?.format("YYYY-MM-DD"),
          address: formData.location,
          tag: formData.tags,
        },
      };

      // Update file attachment
      const result = await updateFileAttach(
        Number(selectedImage.id),
        updatePayload
      );

      if (result) {
        message.success("Cập nhật hình ảnh thành công!");
        navigate(
          `/doc-management/${PermissionNames.imagesList}${
            currentFolder ? `?folder=${currentFolder}` : ""
          }`
        );
      }
    } catch (error) {
      console.error("Error updating image:", error);
      message.error("Lỗi khi cập nhật hình ảnh");
    } finally {
      setLoading(false);
    }
  };

  // Handle submit
  const handleSubmit = () => {
    if (status === "create") {
      createData();
    } else {
      updateData();
    }
  };

  // Page title logic
  const pageTitle = useMemo(() => {
    if (status === "create") {
      return "Thêm hình ảnh";
    } else {
      // Check if we're in view mode or edit mode
      const isViewMode = searchParams.get("update") !== "1";
      return isViewMode ? "Chi tiết hình ảnh" : "Chỉnh sửa hình ảnh";
    }
  }, [status, searchParams]);

  // Get breadcrumb path
  const getBreadcrumbs = () => {
    const baseBreadcrumbs = [
      appStore.currentProject?.name || "",
      "Quản lý tài liệu & Bản vẽ",
      "Hình ảnh",
    ];

    if (currentFolder) {
      baseBreadcrumbs.push(currentFolder);
    }

    if (status === "create") {
      return [...baseBreadcrumbs, "Thêm hình ảnh"];
    } else {
      // Check if we're in view mode or edit mode
      const isViewMode = searchParams.get("update") !== "1";
      return [
        ...baseBreadcrumbs,
        isViewMode ? "Chi tiết hình ảnh" : "Chỉnh sửa hình ảnh",
      ];
    }
  };

  // Add this function to handle back navigation
  const handleBack = () => {
    if (status === "update" && !readonly) {
      // If in edit mode, return to view mode
      setReadonly(true);
      const queryParams = new URLSearchParams(searchParams);
      queryParams.delete("update");
      navigate(`${location.pathname}?${queryParams.toString()}`);
    } else {
      // Navigate back to list
      const queryParams = new URLSearchParams();
      if (currentFolder) {
        queryParams.append("folder", currentFolder);
      }
      navigate(
        `/doc-management/${PermissionNames.imagesList}${
          queryParams.toString() ? `?${queryParams.toString()}` : ""
        }`
      );
    }
  };

  // Handle switching to edit mode
  const handleEdit = () => {
    setReadonly(false);
    // Update URL to include update=1 parameter
    const queryParams = new URLSearchParams(searchParams);
    queryParams.set("update", "1");
    navigate(`${location.pathname}?${queryParams.toString()}`);
  };

  if (loadingFetch) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <>
      <PageTitle
        back
        breadcrumbs={getBreadcrumbs()}
        title={pageTitle}
        onBack={handleBack}
      />
      <Card>
        <Spin spinning={loadingFetch}>
          <div className="pb-[16px]">
            <Form
              layout="vertical"
              form={form}
              className={clsx(
                "create-update-image-form",
                readonly ? "readonly" : ""
              )}
              disabled={readonly}
            >
              {/* Image Upload Section */}
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="Hình ảnh"
                    name="image"
                    rules={
                      status === "create"
                        ? [
                            {
                              required: true,
                              message: "Vui lòng tải lên hình ảnh",
                            },
                          ]
                        : []
                    }
                  >
                    <div className="image-upload-section">
                      {(status === "update" && selectedImage?.imageSrc) ||
                      (fileList.length > 0 && fileList[0].url) ? (
                        <div className="image-preview-container">
                          <Image
                            src={
                              status === "update"
                                ? selectedImage?.imageSrc
                                : fileList[0].url
                            }
                            alt="Current image"
                            className="w-full h-64 object-cover"
                            preview={{
                              mask: false,
                            }}
                          />

                          {/* Upload button overlay */}
                          <div className="absolute top-2 right-2">
                            <Upload
                              {...uploadProps}
                              showUploadList={false}
                              className="upload-overlay-btn rounded-full !p-0 w-12 h-12 flex items-center justify-center bg-white/80 shadow-md border-none"
                            >
                              <CustomButton
                                variant="outline"
                                className="rounded-full !p-0 w-14 h-14 flex items-center justify-center bg-white/80 shadow-md border-none"
                                size="small"
                              >
                                {status === "update"
                                  ? <UploadImageIcon width={28} height={28} />
                                  : <UploadOutlined />}
                              </CustomButton>
                            </Upload>
                          </div>
                        </div>
                      ) : (
                        <Dragger {...uploadProps} className="w-full">
                          <p className="ant-upload-drag-icon">
                            <UploadImageIcon width={28} height={28} />
                          </p>
                          <p className="ant-upload-text">Tải hình ảnh</p>
                          <p className="ant-upload-hint">
                            Hỗ trợ định dạng .jpg, .jpeg, .png, .gif, .bmp,
                            .tif, .tiff
                          </p>

                          <CustomButton
                            variant="outline"
                            className="cta-button"
                            onClick={() => {}}
                            icon={<UploadOutlined />}
                          >
                            Tải ảnh
                          </CustomButton>
                        </Dragger>
                      )}
                    </div>
                  </Form.Item>
                </Col>

                <Col span={12}>
                  {/* Project Selection */}
                  <Row gutter={16}>
                    <Col span={24}>
                      {/* Capture Date */}
                      <Row gutter={16}>
                        <Col span={12}>
                        <Form.Item label="Dự án" name="projectId" rules={rules}>
                    <CustomSelect
                      placeholder="Chọn dự án"
                      value={appStore.currentProject?.name}
                      disabled={true}
                    />
                  </Form.Item>
                        </Col>
                        <Col span={12}>
                          {" "}
                          <Form.Item
                            label="Ngày chụp"
                            name="captureDate"
                            rules={[
                              {
                                required: true,
                                message: "Vui lòng chọn ngày chụp",
                              },
                              {
                                validator: (_, value) => {
                                  if (value && value.isAfter(dayjs(), "day")) {
                                    return Promise.reject(
                                      "Không thể chọn ngày trong tương lai"
                                    );
                                  }
                                  return Promise.resolve();
                                },
                              },
                            ]}
                            initialValue={initialDate}
                          >
                            <DatePicker
                              placeholder="dd/mm/yyyy"
                              format={settings.dateFormat}
                              style={{ width: "100%" }}
                              disabled={readonly}
                              disabledDate={(current) => {
                                return (
                                  current && current > dayjs().endOf("day")
                                );
                              }}
                            />
                          </Form.Item>{" "}
                        </Col>
                      </Row>
                    </Col>
                  </Row>

                  {/* Location */}
                  <Form.Item label="Địa điểm" name="location" rules={rules}>
                    <CustomInput
                      placeholder="Ghi chú địa điểm chụp"
                      disabled={readonly}
                      value={appStore.currentProject?.location}
                    />
                  </Form.Item>

                  {/* Type - using FILE_ATTACH_CATEGORY */}
                  <Form.Item label="Loại" name="type">
                    <CustomSelect
                      placeholder="Loại file"
                      options={typeOptions}
                      disabled={true} // Always disabled as type is determined by mimetype
                    />
                  </Form.Item>

                  {/* Tags - using WORK_TYPE */}
                  <Form.Item label="Loại công việc" name="tags">
                    <CustomSelect
                      placeholder="Chọn loại công việc"
                      options={workTypeOptions}
                      disabled={readonly}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Form>

            {/* Action buttons */}
            <div className="flex gap-[16px] justify-end mt-6">
              {!readonly && (
                <CustomButton
                  variant="outline"
                  className="cta-button"
                  onClick={handleBack}
                >
                  Hủy
                </CustomButton>
              )}

              {readonly && status === "update" && haveEditPermission && (
                <CustomButton className="cta-button" onClick={handleEdit}>
                  Chỉnh sửa
                </CustomButton>
              )}

              {!readonly && (
                <CustomButton
                  loading={loading || uploading}
                  className="cta-button"
                  disabled={status === "update" && !haveEditPermission}
                  onClick={handleSubmit}
                >
                  {status === "create" ? "Thêm hình ảnh" : "Cập nhật"}
                </CustomButton>
              )}
            </div>
          </div>
        </Spin>
      </Card>
    </>
  );
}

export default observer(CreateOrUpdateImagePage);
