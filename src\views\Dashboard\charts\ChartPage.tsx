import React, { useEffect, useState } from "react";
import { Box, Typography, Grid } from "@mui/material";
import { DatePicker, Spin } from "antd";
import dayjs from "dayjs";
import { BarChartBlock } from "../components/BarChartBlock";
import { PieChartBlock } from "../components/PieChartBlock";
import { PopularDesignTable } from "../components/PopularDesignTable";
import { dashboardApi } from "api/dashboard.api";

const weeklyData = [
  { label: "Tuần 1", value: 12 },
  { label: "Tuần 2", value: 18 },
  { label: "Tuần 3", value: 22 },
  { label: "Tuần 4", value: 15 },
];

const monthlyData = [
  { label: "Tháng 1", value: 60 },
  { label: "Tháng 2", value: 48 },
  { label: "Tháng 3", value: 72 },
];

const optionUsage = [
  { label: "Vẽ áo", value: 40 },
  { label: "<PERSON><PERSON><PERSON>", value: 25 },
  { label: "<PERSON><PERSON><PERSON>", value: 20 },
  { label: "<PERSON><PERSON><PERSON>", value: 15 },
];

const popularDesigns = [
  { name: "<PERSON>hiế<PERSON> kế A", favorites: 120, usage: 45 },
  { name: "<PERSON><PERSON>ế<PERSON> kế <PERSON>", favorites: 98, usage: 38 },
  { name: "Thiết kế C", favorites: 88, usage: 32 },
];
interface DataItem {
  total: number;
  date: string; // định dạng YYYY-MM-DD
}
export interface FavoriteDesign {
  componentName: string;
  materialName: string;
  total: number;
  unitName: string;
}
export default function ChartPage() {
  const [selectedMonth, setSelectedMonth] = React.useState<dayjs.Dayjs | null>(
    null
  );
  const [query, setQuery] = useState<Record<string, any>>({
    fromAt: dayjs().startOf("month").unix(),
    toAt: dayjs().endOf("month").unix(),
  });

  const [numberOfDesign, setNumberOfDesign] = useState<DataItem[]>();
  const [favoriteDesigns, setFavoriteDesigns] = useState<FavoriteDesign[]>();
  const [optionalDesigns, setOptionalDesigns] = useState<any[]>();
  const [optionalDesignQueries, setOptionalDesignQueries] = useState();
  const [favoriteDesignQueries, setFavoriteDesignQueries] = useState();
  const [loadingForOptionalDesigns, setLoadingForOptionalDesigns] =
    useState<boolean>(false);
  const [loadingForFavoriteDesign, setLoadingForFavoriteDesign] =
    useState<boolean>(false);
  const handleFetchDashboardData = async () => {
    try {
      const { data } = await dashboardApi.getDataComponent(query);
      console.log("Data number of design là", data);
      setNumberOfDesign(data);
    } catch (error) {
      console.error("Lỗi khi fetch dashboard:", error);
    }
  };
  const handleFetchFavoriteDesign = async () => {
    try {
      setLoadingForFavoriteDesign(true);
      const { data } = await dashboardApi.getFavoriteDesigns({
        //@ts-ignore
        productId: favoriteDesignQueries?.productId,
      });
      console.log(data);
      setFavoriteDesigns(data?.result);
    } catch (error) {
      console.error("Lỗi khi fetch dashboard:", error);
    } finally {
      setLoadingForFavoriteDesign(false);
    }
  };
  const handleFetchOptionalDesign = async () => {
    console.log("Vào đây không", optionalDesignQueries);
    try {
      setLoadingForOptionalDesigns(true);
      const { data } = await dashboardApi.getOptionalDesigns({
        //@ts-ignore
        productId: optionalDesignQueries?.productId,
      });
      console.log(data);
      const chartArray = data?.result.map((item: any) => ({
        label: (
          <div className="text-[15px] leading-5">
            {item.componentName} {item.materialName && "-"} {item.materialName}
          </div>
        ),
        value: item.total,
      }));
      setOptionalDesigns(chartArray);
    } catch (error) {
      console.error("Lỗi khi fetch dashboard:", error);
    } finally {
      setLoadingForOptionalDesigns(false);
    }
  };
  useEffect(() => {
    handleFetchDashboardData();
    handleFetchFavoriteDesign();
    handleFetchOptionalDesign();
  }, []);
  useEffect(() => {
    if (query) {
      handleFetchDashboardData();
    }
  }, [query]);
  useEffect(() => {
    handleFetchOptionalDesign();
  }, [optionalDesignQueries]);
  useEffect(() => {
    handleFetchFavoriteDesign();
  }, [favoriteDesignQueries]);
  return (
    <Box>
      <div className="flex flex-wrap gap-2">
        <div className="flex flex-row gap-2 flex-wrap w-full justify-around">
          <div className="w-full flex">
            <BarChartBlock
              className="w-full"
              query={query}
              label="Mẫu thiết kế"
              setQuery={setQuery}
              refetchData={handleFetchDashboardData}
              title="Số lượng mẫu thiết kế được tạo mới"
              labels={numberOfDesign?.map((d) => d.date) || []}
              data={numberOfDesign?.map((d) => d.total) || []}
            />
          </div>
        </div>
        <div className="flex flex-col gap-2 w-full justify-center lg:justify-between lg:flex-row ">
          <PieChartBlock
            title="Tỷ lệ sử dụng tùy chọn thiết kế"
            data={optionalDesigns || []}
            query={optionalDesignQueries}
            setQuery={setOptionalDesignQueries}
            loading={loadingForOptionalDesigns}
          />
          <PopularDesignTable
            title="Thiết kế được yêu thích & sử dụng nhiều"
            data={favoriteDesigns || []}
            query={favoriteDesignQueries}
            setQuery={setFavoriteDesignQueries}
            loading={loadingForFavoriteDesign}
          />
        </div>
      </div>
    </Box>
  );
}
