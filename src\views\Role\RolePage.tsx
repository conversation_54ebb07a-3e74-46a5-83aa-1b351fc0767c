import {
  CloseOutlined,
  DownOutlined,
  EditFilled,
  ImportOutlined,
  PlusOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import {
  Button,
  Card,
  Divider,
  Input,
  message,
  Modal,
  Pagination,
  Popconfirm,
  Space,
  Spin,
  Table,
  Tooltip,
} from "antd";
import { roleApi } from "api/role.api";
import DropdownCell from "components/Table/DropdownCell";
import { $isDev } from "constant";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { adminRoutes } from "router";
import { PermissionNames } from "types/PermissionNames";
import { settings } from "settings";
import { ModalStatus } from "types/modal";
import { QueryParam } from "types/query";
import { Role } from "types/role";
import { getTitle } from "utils";
import {
  checkRole,
  checkRoles,
  filterActionColumnIfNoPermission,
} from "utils/auth";
import { RoleModal } from "./components/RoleModal";
import { Permission } from "types/permission";
import { useNavigate } from "react-router-dom";
import { permissionStore } from "store/permissionStore";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import PencilIcon from "assets/svgs/PencilIcon";
import PageTitle from "components/PageTitle/PageTitle";
import CustomButton from "components/Button/CustomButton";
import CustomInput from "components/Input/CustomInput";
import { useRole } from "hooks/useRole";
import { ReactComponent as SearchIcon } from "assets/svgs/search.svg";
import { ReactComponent as EyeIcon } from "assets/svgs/eye.svg";
import { debounce } from "lodash";
import DeleteIcon from "assets/svgs/DeleteIcon";
import { TableProps } from "antd/lib";
import clsx from "clsx";
import { observer } from "mobx-react";
import EditButton from "components/Button/EditButton";
import DeleteButton from "components/Button/DeleteButton";

const { ColumnGroup, Column } = Table;

export const RolePage = ({ title = "" }) => {
  const {
    haveAddPermission,
    haveDeletePermission,
    haveEditPermission,
    haveViewAllPermission,
  } = checkRoles(
    {
      add: PermissionNames.roleAdd,
      edit: PermissionNames.roleEdit,
      delete: PermissionNames.roleDelete,
      viewAll: PermissionNames.roleViewAll,
    },
    permissionStore.permissions
  );

  const { fetchRole, loadingRole, queryRole, roles, setQueryRole, totalRole } =
    useRole({
      initQuery: {
        page: 1,
        limit: 0,
        isAdmin: haveViewAllPermission ? true : undefined,
      },
    });

  const [loadingImport, setLoadingImport] = useState(false);

  const [adminPermissions, setAdminPermissions] = useState<Permission[]>([]);

  useEffect(() => {
    document.title = getTitle(title);
    fetchPermissions();
  }, []);

  useEffect(() => {
    fetchRole();
  }, []);

  const navigate = useNavigate();

  const fetchPermissions = async () => {
    const { data } = await roleApi.getPermission();
    setAdminPermissions(data);
  };

  const handleDeleteRole = async (roleId: number) => {
    try {
      const res = await roleApi.delete(roleId);
      fetchRole();
      message.success("Xóa phân quyền thành công!");
    } catch (error) {
      console.log({ error });
    }
  };

  const handleTableChange: TableProps<any>["onChange"] = (
    pagination,
    filters,
    sorter
  ) => {
    if (!Array.isArray(sorter)) {
      const fieldMap: Record<string, string> = {
        name: "role.name",
        description: "role.description",
        totalStaff: "role.totalStaff",
      };
      const columnKey = sorter.field || sorter.column?.key;

      if (!sorter.order) {
        // setSortField(null);
        // setSortOrder(null);
        queryRole.queryObject = undefined;
        setQueryRole({ ...queryRole });
      } else {
        const order = sorter.order === "ascend" ? "ASC" : "DESC";
        // setSortField("jobCategory.name");
        // setSortOrder(order);
        const field = fieldMap[columnKey as string];

        const newQueryObject = JSON.stringify([
          {
            type: "sort",
            field,
            value: order,
          },
        ]);
        queryRole.queryObject = newQueryObject;
        setQueryRole({ ...queryRole });
      }
      fetchRole();
    } else {
      queryRole.queryObject = undefined;
      setQueryRole({ ...queryRole });
      fetchRole();
    }
  };

  const columns: CustomizableColumn<Role>[] = [
    {
      title: "STT",
      dataIndex: "name",
      align: "center",
      key: "STT",
      width: 50,
      render: (_, record, i) => i + 1,
    },
    {
      key: "name",
      title: "Vai trò",
      dataIndex: "name",
      width: 200,
      defaultVisible: true,
      alwaysVisible: true,
      // sorter: true,
    },
    // {
    //   key: "description",
    //   title: "Mô tả",
    //   dataIndex: "description",
    //   width: 400,
    //   // sorter: true,

    //   // render: (_, record) => (
    //   // 	<div className="service-cell">

    //   // 		<div className="service-info">
    //   // 			<div className="service-name">{record.name}</div>
    //   // 		</div>
    //   // 	</div>
    //   // ),
    //   defaultVisible: false,
    // },
    {
      key: "permissionCount",
      title: "Số lượng phân quyền",
      dataIndex: "permissions",
      align: "center",
      width: 200,
      render: (_, record) => record.permissions?.length,
      defaultVisible: true,
      // sorter: true,
    },
    {
      key: "totalStaff",
      title: "Số lượng tài khoản",
      dataIndex: "totalStaff",
      align: "center",
      width: 200,
      defaultVisible: true,
      // sorter: true,
    },
    {
      key: "actions",
      title: "Xử lý",
      align: "center",
      width: 140,
      fixed: "right",
      render: (_, record) => (
        <Space className="gap-[0px]" wrap>
          <Tooltip title="Chi tiết">
            <Button
              type="text"
              icon={<EyeIcon />}
              onClick={() => {
                navigate(
                  `/master-data/${PermissionNames.roleDetail.replace(
                    ":id",
                    record.id + ""
                  )}`
                );
              }}
            />
          </Tooltip>
          {(haveEditPermission || record.isAdmin) && (
            <EditButton
              onClick={() => {
                // modalRef?.current?.handleUpdate(record);
                navigate(
                  `/master-data/${PermissionNames.roleEdit.replace(
                    ":id",
                    record!.id + ""
                  )}`
                );
              }}
            />
          )}
          {haveDeletePermission && (
            <DeleteButton
              iconColor={record.isAdmin ? "#cecece" : undefined}
              className={clsx(record.isAdmin && "cursor-default")}
              onClick={(e) => {
                if (!record.isAdmin) {
                  e.stopPropagation();
                  Modal.confirm({
                    title: `Xóa vai trò ${record.name}`,
                    getContainer: () => {
                      return document.getElementById("App") as HTMLElement;
                    },
                    icon: null,
                    content: (
                      <>
                        <ul>
                          <li>
                            Các tài khoản có vai trò này sẽ không thể truy cập
                          </li>
                          <li>Không thể khôi phục vai trò này sau khi xóa</li>
                        </ul>
                        <div>Bạn có chắc chắn muốn xóa vai trò này?</div>
                      </>
                    ),
                    footer: (_, { OkBtn, CancelBtn }) => (
                      <>
                        <CustomButton
                          variant="outline"
                          className="cta-button"
                          onClick={() => {
                            handleDeleteRole(record.id);
                            Modal.destroyAll();
                          }}
                        >
                          Có
                        </CustomButton>
                        <CustomButton
                          onClick={() => {
                            Modal.destroyAll();
                          }}
                          className="cta-button"
                        >
                          Không
                        </CustomButton>
                      </>
                    ),
                  });
                }
              }}
            />
          )}
        </Space>
      ),
      defaultVisible: true,
      alwaysVisible: true,
    },
  ];

  const handleImportAdminRoutes = async () => {
    const permissions: Permission[] = [];

    for (const route of adminRoutes) {
      if (route.children) {
        for (const childRoute of route.children) {
          //   debugger;

          const find = adminPermissions.find((e) => e.name == childRoute.name);
          const path = route.path + "/" + childRoute.path;
          const id = find?.id;
          permissions.push({
            path,
            //@ts-ignore
            id,
            name: find?.name || (childRoute?.name as string),
          });
        }
      } else {
        const find = adminPermissions.find((e) => e.name == route.name);
        const path = route.path;
        const id = find?.id;

        permissions.push({
          path: path as string,
          //@ts-ignore
          id,
          name: find?.name || (route.name as string),
        });
      }
    }

    setLoadingImport(true);

    await roleApi.importPermission({
      permissions,
    });
    message.success("successfully");
    fetchRole();
    // fetchPermissions();
    setLoadingImport(false);
  };

  const debounceSearch = useCallback(
    debounce((query) => fetchRole({ ...query }), 300),
    []
  );

  return (
    <div className="app-container">
      <PageTitle
        title={title}
        breadcrumbs={["Dữ liệu nguồn", title]}
        extra={
          <Space>
            {haveAddPermission ? (
              <CustomButton
                size="small"
                showPlusIcon
                onClick={() => {
                  navigate(`/master-data/${PermissionNames.roleAdd}`);
                }}
              >
                Tạo vai trò
              </CustomButton>
            ) : null}
            {$isDev && (
              <CustomButton
                onClick={handleImportAdminRoutes}
                icon={<ImportOutlined />}
                loading={loadingImport}
                size="small"
              >
                Import routes
              </CustomButton>
            )}
          </Space>
        }
      />
      <Card>
        <div className="flex gap-[16px] pb-[16px] justify-between flex-wrap">
          <div className="font-bold text-[20px] uppercase">
            {totalRole} Vai trò
          </div>
          <div className="w-[500px]">
            <CustomInput
              placeholder="Tìm kiếm"
              value={queryRole.search}
              onChange={(ev) => {
                queryRole.search = ev;
                setQueryRole({ ...queryRole });
                debounceSearch(queryRole);
              }}
              suffix={<SearchIcon />}
              onPressEnter={() => {
                fetchRole();
              }}
              // error="Có lỗi xãy ra"
            />
          </div>
        </div>
        <CustomizableTable
          columns={filterActionColumnIfNoPermission(columns, [
            haveEditPermission,
            haveDeletePermission,
          ])}
          dataSource={roles}
          rowKey="id"
          loading={loadingRole}
          pagination={false}
          scroll={{ x: 1200 }}
          bordered
          //@ts-ignore
          onChange={handleTableChange}
        />
      </Card>
    </div>
  );
};

export default observer(RolePage);
