import { Button, Image, Space, Spin, Table, Typography, message } from "antd";
import { configurationApi } from "api/configuration.api";
import { useEffect, useState } from "react";
import { Configuration, ConfigurationParam } from "types/configuration";
import { ModalStatus } from "types/modal";
import { QueryParam } from "types/query";
import { getTitle } from "utils";
import { ConfigurationModal } from "./components/ConfigurationModal";

const { ColumnGroup, Column } = Table;

export const ConfigurationPage = ({ title = "" }) => {
  const [query, setQuery] = useState<QueryParam>({
    page: 1,
    limit: 0,
    search: "",
  });
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [visibleModal, setVisibleModal] = useState(false);
  const [configurations, setConfigurations] = useState<Configuration[]>([]);
  const [sections, setSections] = useState<
    { title: string; params: ConfigurationParam[]; list: Configuration[] }[]
  >([
    {
      title: "Facebook Link",
      list: [],
      params: [ConfigurationParam.FacebookLink],
    },
    {
      title: "Youtube Link",
      list: [],
      params: [ConfigurationParam.YoutubeLink],
    },
    {
      title: "Google link",
      list: [],
      params: [ConfigurationParam.GoogleLink],
    },
    {
      title: "Tiktok link",
      list: [],
      params: [ConfigurationParam.TiktokLink],
    },
    {
      title: "Hotline",
      list: [],
      params: [ConfigurationParam.Hotline],
    },
    {
      title: "Tên công ty",
      list: [],
      params: [ConfigurationParam.CompanyName],
    },
    {
      title: "Mã số thuế",
      list: [],
      params: [ConfigurationParam.CompanyTaxCode],
    },
    {
      title: "Địa chỉ công ty",
      list: [],
      params: [ConfigurationParam.CompanyAddress],
    },
    {
      title: "Banner trang chủ (trên)",
      list: [],
      params: [ConfigurationParam.HomeBanner1],
    },
    {
      title: "Banner trang chủ (dưới)",
      list: [],
      params: [ConfigurationParam.HomeBanner2],
    },
  ]);
  const [selectedConfiguration, setSelectedConfiguration] = useState<
    Partial<Configuration>
  >({});
  const [modalStatus, setModalStatus] = useState<ModalStatus>("create");

  console.log(sections);
  useEffect(() => {
    document.title = getTitle(title);
  }, []);

  useEffect(() => {
    fetchData();
  }, [query]);

  const fetchData = async () => {
    setLoading(true);

    const res = await configurationApi.findAll();

    const result = res.data.configurations as Configuration[];
    // console.log("result", result)
    // setConfigurations(result);
    for (const section of sections) {
      section.list = result.filter((r) => {
        return section.params.includes(r.param);
      });
    }

    setSections([...sections]);
    setLoading(false);
  };

  const handleUpdateVisible = async (
    record: Configuration,
    isEnable: boolean
  ) => {
    const data = { configuration: { isEnable } };
    setLoading(true);
    try {
      const res = await configurationApi.update(record?.id || 0, data);
      message.success("Update staff successfully!");
      fetchData();
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <Spin spinning={loading}>
        {sections.map((section) => {
          return (
            <Space
              direction="vertical"
              style={{ width: "100%", paddingBottom: 10 }}
            >
              <Typography.Title level={5}>{section.title}</Typography.Title>
              <Table
                showHeader={false}
                pagination={false}
                rowKey="id"
                dataSource={section.list}
                size="middle"
                // title={title}
              >
                <Column
                  title="Giá trị"
                  dataIndex="value"
                  key="value"
                  render={(text, record: Configuration) => (
                    <>
                      {record.param === ConfigurationParam.HomeBanner1 ||
                      record.param === ConfigurationParam.HomeBanner2 ? (
                        <div className="flex items-center gap-3">
                          <Image
                            className="!w-[90px] !h-[40px] object-cover"
                            src={record.value}
                          ></Image>
                          <a href={record.link} target="_blank">
                            {record.link}
                          </a>
                        </div>
                      ) : (
                        <span className="text-primary font-medium">
                          {record?.value}
                        </span>
                      )}
                    </>
                  )}
                />

                <Column
                  align="center"
                  width={150}
                  title=""
                  key="action"
                  render={(text, record: Configuration) => (
                    <span>
                      <Button
                        type="primary"
                        onClick={() => {
                          setSelectedConfiguration(record);
                          setVisibleModal(true);
                          setModalStatus("update");
                        }}
                      >
                        Cập nhật
                      </Button>
                    </span>
                  )}
                />
              </Table>
            </Space>
          );
        })}
      </Spin>

      <ConfigurationModal
        onSubmitOk={fetchData}
        onClose={() => setVisibleModal(false)}
        visible={visibleModal}
        configuration={selectedConfiguration}
        status={modalStatus}
      />
    </div>
  );
};
