import { Tabs } from "antd";
import { useEffect } from "react";
import { getTitle } from "utils";
import ProjectProgressGantt from "./components/ProjectProgressGantt";
import ProjectProgressCalendar from "./components/ProjectProgressCalendar";

const ProjectProgressPage = ({ title = "" }) => {
  useEffect(() => {
    document.title = getTitle(title);
  }, []);

  return (
    <Tabs
      items={[
        {
          key: "gantt",
          label: "Gantt Chart",
          children: <ProjectProgressGantt />,
        },
        {
          key: "calendar",
          label: "Calendar",
          children: <ProjectProgressCalendar />,
        },
      ]}
    />
  );
};

export default ProjectProgressPage;
