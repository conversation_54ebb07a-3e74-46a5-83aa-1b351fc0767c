import { InboxOutlined } from "@ant-design/icons";
import { message, Upload } from "antd";
import { ExcelUtil } from "utils/ExcelUtil";

const { Dragger } = Upload;


interface UploadExcelProps {
  onOk?: ({ headers, data }: { headers: string[]; data: any[] }) => void;
  /** @example
   * const mappingExcelToCustomJson: { [key: string]: keyof IEditPhonePayload } = {
    "Mã DMS": "syncCode",
    "SĐT remove": "oldPhone",
    "SĐT thay thế": "newPhone",
};
   */
  mappingExcelToJsonObject: { [key: string]: keyof any; }
  handleUploadToServer: (data: any) => void
}



export const UploadExcel = ({ onOk, mappingExcelToJsonObject, handleUploadToServer }: UploadExcelProps) => {

  const handleMappingExcelToJson = (excelData: any) => {

    const transformedArray: any[] = excelData.map(
      (item: any) => {
        const transformedItem: any = {};
        for (const key in item) {
          if (mappingExcelToJsonObject[key]) {
            transformedItem[mappingExcelToJsonObject[key]] = item[key];
          }
        }
        return transformedItem as any;
      }
    );
    console.log(transformedArray)
    return transformedArray
  };

  return (
    <Dragger
      style={{ marginTop: "0.5em" }}
      maxCount={1}
      multiple={false}
      beforeUpload={(file) => {
        //Check file type
        const isExcelFile = file.name.includes("xls" || "xlsx");
        if (!isExcelFile) {
          message.error("You can only upload XLSX/XLS file!");
          return Upload.LIST_IGNORE;
        }
        ExcelUtil.readExcel(file).then((data: any) => {
          //* Data trước khi lấy từ excel
          console.log(data)
          const jsonData = handleMappingExcelToJson(data)
          // * Data sau khi map
          console.log(jsonData)
          handleUploadToServer(jsonData);
        });
        return false;
      }}
      onChange={(info) => {
        //reset data
      }}
    >
      <p className="ant-upload-drag-icon">
        <InboxOutlined />
      </p>
      <p className="ant-upload-text">Click hoặc kéo file excel vào đây</p>
    </Dragger>
  );
};
