import { colorApi } from "api/color.api";
import { useState } from "react";
import { Color } from "types/color";
import { QueryParam } from "types/query";

export interface ColorQuery extends QueryParam {}

interface UseColorProps {
  initQuery: ColorQuery;
}

export const useColor = ({ initQuery }: UseColorProps) => {
  const [data, setData] = useState<Color[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<ColorQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await colorApi.findAll(query);

      setData(data.colors);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return { colors: data, total, fetchData, loading, setQuery, query };
};
