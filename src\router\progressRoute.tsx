import { Route } from "./RouteType";
import { ReactComponent as PlanIcon } from "assets/svgs/plan.svg";
import { PermissionType } from "types/permission";
import { PermissionNames } from "types/PermissionNames";
import { lazy } from "react";

const ProjectProgressPage = lazy(
  () => import("views/ProjectProgressPage/ProjectProgressPage")
);
const TaskPage = lazy(() => import("views/Task/TaskPage"));
const CreateOrUpdateTaskPage = lazy(
  () => import("views/Task/CreateOrUpdateTaskPage")
);
const DailyLogPage = lazy(() => import("views/DailyLogPage/DailyLogPage"));
const CreateOrUpdateDailyLogPage = lazy(
  () => import("views/DailyLogPage/CreateOrUpdateDailyLogPage")
);

export const progressRoutes: Route[] = [
  {
    title: "Tiến độ",
    breadcrumb: "progress-management",
    path: "/progress-management",
    name: "progress-management",
    aliasPath: "/progress-management",
    icon: <PlanIcon />,
    permissionTypes: [
      PermissionType.Add,
      PermissionType.Delete,
      PermissionType.Edit,
      PermissionType.List,
    ],
    needProject: true,
    children: [
      {
        title: "Tiến độ kế hoạch",
        breadcrumb: "Tiến độ kế hoạch",
        path: PermissionNames.projectProgressList,
        name: PermissionNames.projectProgressList,
        aliasPath: `/progress-management/${PermissionNames.projectProgressList}`,
        element: <ProjectProgressPage title="Tiến độ kế hoạch" />,
        permissionTypes: [PermissionType.List],
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.projectProgressViewAll,
        name: PermissionNames.projectProgressViewAll,
        aliasPath: `/progress-management/${PermissionNames.projectProgressViewAll}`,
        hidden: true,
      },
      // {
      //   title: "Công việc",
      //   breadcrumb: "Công việc",
      //   path: PermissionNames.taskList,
      //   name: PermissionNames.taskList,
      //   aliasPath: `/progress-management/${PermissionNames.taskList}`,
      //   element: <TaskPage title="Công việc" />,
      //   permissionTypes: [PermissionType.List],

      //   // icon: <TbPackages />,
      // },
      // {
      //   title: "Xem tất cả",
      //   path: PermissionNames.taskViewAll,
      //   name: PermissionNames.taskViewAll,
      //   aliasPath: `/progress-management/${PermissionNames.taskViewAll}`,
      //   hidden: true,
      // },
      // {
      //   title: "Tạo công việc",
      //   breadcrumb: "Công việc",
      //   path: PermissionNames.taskAdd,
      //   name: PermissionNames.taskAdd,
      //   aliasPath: `/progress-management/${PermissionNames.taskAdd}`,
      //   element: (
      //     <CreateOrUpdateTaskPage title="Tạo công việc" status="create" />
      //   ),
      //   permissionTypes: [PermissionType.Add],
      //   isPublic: true,
      //   hidden: true,

      //   // icon: <TbPackages />,
      // },
      // {
      //   title: "Chỉnh sửa công việc",
      //   breadcrumb: "Công việc",
      //   path: PermissionNames.taskEdit,
      //   name: PermissionNames.taskEdit,
      //   aliasPath: `/progress-management/${PermissionNames.taskEdit.replace(
      //     "/:id",
      //     ""
      //   )}`,
      //   element: (
      //     <CreateOrUpdateTaskPage title="Chỉnh sửa công việc" status="update" />
      //   ),
      //   permissionTypes: [PermissionType.Edit],
      //   isPublic: true,
      //   hidden: true,

      //   // icon: <TbPackages />,
      // },
      // {
      //   title: "Xóa công việc",
      //   breadcrumb: "Xóa công việc",
      //   path: PermissionNames.taskDelete,
      //   name: PermissionNames.taskDelete,
      //   aliasPath: `/tasks/${PermissionNames.taskDelete}`,
      //   hidden: true,
      // },
      {
        title: "Nhật ký hoạt động",
        breadcrumb: "Nhật ký hoạt động",
        path: PermissionNames.activityLogList,
        name: PermissionNames.activityLogList,
        aliasPath: `/progress-management/${PermissionNames.activityLogList}`,
        element: <DailyLogPage title="Nhật ký hoạt động" />,
        permissionTypes: [PermissionType.List],

        // icon: <TbPackages />,
      },
      {
        title: "Tạo nhật ký hoạt động",
        breadcrumb: "Nhật ký hoạt động",
        path: PermissionNames.activityLogAdd,
        name: PermissionNames.activityLogAdd,
        aliasPath: `/progress-management/${PermissionNames.activityLogAdd}`,
        element: (
          <CreateOrUpdateDailyLogPage
            title="Tạo nhật ký hoạt động"
            status="create"
          />
        ),
        permissionTypes: [PermissionType.Add],
        isPublic: true,
        hidden: true,

        // icon: <TbPackages />,
      },
      {
        title: "Chỉnh sửa nhật ký hoạt động",
        breadcrumb: "Nhật ký hoạt động",
        path: PermissionNames.activityLogEdit,
        name: PermissionNames.activityLogEdit,
        aliasPath: `/progress-management/${PermissionNames.activityLogEdit.replace(
          "/:id",
          ""
        )}`,
        element: (
          <CreateOrUpdateDailyLogPage
            title="Chỉnh sửa nhật ký hoạt động"
            status="update"
          />
        ),
        permissionTypes: [PermissionType.Edit],
        isPublic: true,
        hidden: true,

        // icon: <TbPackages />,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.activityLogViewAll,
        name: PermissionNames.activityLogViewAll,
        aliasPath: `/progress-management/${PermissionNames.activityLogViewAll}`,
        hidden: true,
      },
    ],
  },
];
