import { DownOutlined } from "@ant-design/icons";
import { Button, Dropdown, Spin } from "antd";
import CustomButton from "components/Button/CustomButton";
import CardView from "components/CardView/CardView";
import CustomInput from "components/Input/CustomInput";
import { useProject } from "hooks/useProject";
import { debounce } from "lodash";
import { fetchData } from "pdfjs-dist";
import { useCallback, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { appStore } from "store/appStore";

const SwitchProjectButton = () => {
  const navigate = useNavigate();
  const scrollRef = useRef<HTMLDivElement | null>(null);
  const attachedScrollListener = useRef(false); // tránh gắn lại nhiều lần

  const {
    projects,
    fetchData,
    loading,
    query,
    setQuery,
    total,
    loadMore,
    isFetched,
  } = useProject({
    initQuery: { limit: 10, page: 1 },
  });

  useEffect(() => {
    fetchData();
  }, []);

  function handleScroll(e: Event) {
    const { scrollTop, scrollHeight, clientHeight } =
      e.target as HTMLDivElement;
    if (scrollTop + clientHeight >= scrollHeight - 10) {
      // Người dùng đã gần chạm đáy
      loadMore();
    }
  }

  const debounceSearch = useCallback(
    debounce((keyword) => {
      query.search = keyword;
      query.page = 1; // Reset page khi tìm kiếm mới
      setQuery({ ...query });
      fetchData({ ...query });
    }, 300),
    [query]
  );

  const renderDropdown = (menu: React.ReactNode) => {
    setTimeout(() => {
      const el = scrollRef.current;
      if (el && !attachedScrollListener.current) {
        el.addEventListener("scroll", handleScroll);
        attachedScrollListener.current = true;
      }
    }, 0);

    return (
      <div
        ref={(el) => {
          scrollRef.current = el;
        }}
        className="max-h-[500px] w-[300px] overflow-y-auto project-list-menu"
      >
        <div className="px-6 sticky top-0 z-10">
          <CustomInput
            placeholder="Tìm kiếm theo tên, mã dự án"
            onChange={(value) => {
              debounceSearch(value);
            }}
          />
        </div>
        {menu}
        {loading && (
          <div className="text-center py-2">
            <Spin size="small" />
          </div>
        )}
      </div>
    );
  };

  return (
    <Dropdown
      trigger={["click"]}
      overlayClassName="shadow-md"
      menu={{
        className: "!py-2",
        items: projects.map((p) => ({
          label: (
            <CardView
              project={p}
              compactMode
              onDetail={(project) => {
                appStore.setCurrentProject(project);
                navigate(`/project-detail/${project.id}`);
              }}
            />
          ),
          key: p.id,
        })),
      }}
      dropdownRender={renderDropdown}
      placement="bottomCenter"
    >
      {/* <CustomButton variant="outline">
        <div className="flex items-center gap-2 bg-transparent border-none">
          <DownOutlined className="text-xs" />
        </div>
      </CustomButton> */}
      <Button
        className="flex items-center gap-2 border-none hover:bg-transparent !p-1 h-5"
        size="small"
      >
        <DownOutlined className="text-xs" />
      </Button>
    </Dropdown>
  );
};

export default SwitchProjectButton;
