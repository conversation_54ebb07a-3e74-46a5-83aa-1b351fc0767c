export enum PermissionNames {
  //* Dashboard
  dashboardList = "dashboard/list",

  //* Project
  projectAdd = "project/add",
  projectList = "project/list",
  projectEditStatus = "project/update/:id",
  projectBlock = "project/block",
  projectViewAll = "project/viewAll",

  //* Account
  accountList = "account/list",
  accountAdd = "account/add",
  accountEdit = "account/update/:id",
  accountBlock = "account/block",
  accountResetPassword = "account/reset",
  accountViewAll = "account/viewAll",

  // * Project phonebook
  projectPhoneBookList = "project-phonebook/list",
  projectPhoneBookAdd = "project-phonebook/add",
  projectPhoneBookBlock = "project-phonebook/block",
  projectPhoneBookEdit = "project-phonebook/update/:id",
  projectPhoneBookViewAll = "project-phonebook/viewAll",


  //* Project Doc
  projectDocList = "project-doc/list",
  projectDocAdd = "project-doc/add",
  projectDocEdit = "project-doc/update/:id",
  projectDocBlock = "project-doc/block",
  projectDocViewAll = "project-doc/viewAll",

  // * Project item
  projectItemList = "project-item/list",
  projectItemAdd = "project-item/add",
  projectItemEdit = "project-item/update/:id",
  projectItemBlock = "project-item/block",
  projectItemDelete = "project-item/delete",
  projectItemViewAll = "project-item/viewAll",

  // * Approve process
  approveProcessList = "approve-process/list",
  approveProcessAdd = "approve-process/add",
  approveProcessEdit = "approve-process/update/:id",
  approveProcessBlock = "approve-process/block",
  approveProcessDelete = "approve-process/delete",
  approveProcessViewAll = "approve-process/viewAll",

  //* Rfis
  rfisList = "rfis/list",
  rfisAdd = "rfis/add",
  rfisEdit = "rfis/update/:id",
  rfisBlock = "rfis/block",
  rfisDelete = "rfis/delete",
  rfisViewAll = "rfis/viewAll",

  //* Project report
  projectReportList = "project-report/list",
  projectReportAdd = "project-report/add",
  projectReportEdit = "project-report/update/:id",
  projectReportViewAll = "project-report/viewAll",

  //* Event log
  eventLogList = "event-log/list",
  eventLogAdd = "event-log/add",
  eventLogEdit = "event-log/update/:id",
  eventLogDelete = "event-log/delete",
  eventLogViewAll = "event-log/viewAll",

  //* Blueprint
  blueprintList = "blueprint/list",
  blueprintAdd = "blueprint/add",
  blueprintEdit = "blueprint/update/:id",
  blueprintDelete = "blueprint/delete",
  blueprintViewAll = "blueprint/viewAll",

  //* Images
  imagesList = "images/list",
  imagesAdd = "images/add",
  imagesEdit = "images/update/:id",
  imagesViewAll = "images/viewAll",

  // *BOQ
  boqList = "boq/list",
  boqAdd = "boq/add",
  boqEdit = "boq/update/:id",
  boqDelete = "boq/delete",
  boqViewAll = "boq/viewAll",

  //* Project progress
  projectProgressList = "project-progress/list",
  projectProgressViewAll = "project-progress/viewAll",

  //* Task
  taskList = "task/list",
  taskAdd = "task/add",
  taskEdit = "task/update/:id",
  taskBlock = "task/block",
  taskDelete = "task/delete",
  taskViewAll = "task/viewAll",

  //* Activity log
  activityLogList = "activity-log/list",
  activityLogViewAll = "activity-log/viewAll",
  activityLogAdd = "activity-log/add",
  activityLogEdit = "activity-log/update/:id",
  activityLogDelete = "activity-log/delete",

  //* Service
  serviceList = "service/list",
  serviceAdd = "service/add",
  serviceEdit = "service/update/:id",
  serviceBlock = "service/block",
  serviceViewAll = "service/viewAll",

  //* Indicative
  indicativeList = "indicative/list",
  indicativeAdd = "indicative/add",
  indicativeEdit = "indicative/update/:id",
  indicativeBlock = "indicative/block",
  indicativeViewAll = "indicative/viewAll",

  //* Staff
  staffList = "staff/list",
  staffAdd = "staff/add",
  staffEdit = "staff/update/:id",
  staffBlock = "staff/block",
  staffViewAll = "staff/viewAll",

  //* Staff Other
  staffOtherList = "staff-other/list",
  staffOtherAdd = "staff-other/add",
  staffOtherEdit = "staff-other/update/:id",
  staffOtherBlock = "staff-other/block",
  staffOtherViewAll = "staff-other/viewAll",

  //* Device
  deviceList = "device/list",
  deviceAdd = "device/add",
  deviceEdit = "device/update/:id",
  deviceBlock = "device/block",
  deviceViewAll = "device/viewAll",

  //* Machine
  machineList = "machine/list",
  machineAdd = "machine/add",
  machineEdit = "machine/update/:id",
  machineBlock = "machine/block",
  machineViewAll = "machine/viewAll",

  //* Provider
  providerList = "provider/list",
  providerAdd = "provider/add",
  providerEdit = "provider/update/:id",
  providerBlock = "provider/block",
  providerViewAll = "provider/viewAll",

  //* Unit
  unitAdd = "unit/add",
  unitEdit = "unit/update/:id",
  unitList = "unit/list",
  unitBlock = "unit/block",
  unitViewAll = "unit/viewAll",

  //* Material
  materialList = "material/list",
  materialAdd = "material/add",
  materialEdit = "material/update/:id",
  materialBlock = "material/block",
  materialViewAll = "material/viewAll",

  //* Goods
  goodsList = "goods/list",
  goodsAdd = "goods/add",
  goodsEdit = "goods/update/:id",
  goodsBlock = "goods/block",
  goodsViewAll = "goods/viewAll",

  //* Project group
  projectGroupAdd = "project-group/add",
  projectGroupEdit = "project-group/update/:id",
  projectGroupList = "project-group/list",
  projectGroupBlock = "project-group/block",
  projectGroupViewAll = "project-group/viewAll",

  //* Task template
  taskTemplateAdd = "task-template/add",
  taskTemplateEdit = "task-template/update/:id",
  taskTemplateList = "task-template/list",
  taskTemplateBlock = "task-template/block",
  taskTemplateViewAll = "task-template/viewAll",

  // * Document
  documentAdd = "document/add",
  documentEdit = "document/update/:id",
  documentList = "document/list",
  documentBlock = "document/block",
  documentViewAll = "document/viewAll",

  //* Role
  roleAdd = "role/add",
  roleEdit = "role/update/:id",
  roleDetail = "role/list/:id",
  roleList = "role/list",
  roleDelete = "role/delete",
  roleViewAll = "role/viewAll",

  //* Subcontractor
  subcontractorList = "subcontractor/list",
  subcontractorAdd = "subcontractor/add",
  subcontractorEdit = "subcontractor/update/:id",
  subcontractorBlock = "subcontractor/block",
  subcontractorViewAll = "subcontractor/viewAll",

  //* Service Type
  serviceTypeList = "service-type/list",
  serviceTypeAdd = "service-type/add",
  serviceTypeEdit = "service-type/update/:id",
  serviceTypeDelete = "service-type/delete",
  serviceTypeViewAll = "service-type/viewAll",

  //* Job Title
  jobTitleList = "job-title/list",
  jobTitleAdd = "job-title/add",
  jobTitleEdit = "job-title/update/:id",
  jobTitleDelete = "job-title/delete",
  jobTitleViewAll = "job-title/viewAll",

  //* Staff level
  levelList = "level/list",
  levelAdd = "level/add",
  levelEdit = "level/update/:id",
  levelDelete = "level/delete",
  levelViewAll = "level/viewAll",

  //* Department
  departmentList = "department/list",
  departmentAdd = "department/add",
  departmentEdit = "department/update/:id",
  departmentDelete = "department/delete",
  departmentViewAll = "department/viewAll",

  //* Brand
  brandList = "brand/list",
  brandAdd = "brand/add",
  brandEdit = "brand/update/:id",
  brandDelete = "brand/delete",
  brandViewAll = "brand/viewAll",

  //* Account group
  accountGroupList = "account-group/list",
  accountGroupAdd = "account-group/add",
  accountGroupEdit = "account-group/update/:id",
  accountGroupDelete = "account-group/delete",
  accountGroupViewAll = "account-group/viewAll",

  //* Material group
  materialGroupList = "material-group/list",
  materialGroupAdd = "material-group/add",
  materialGroupEdit = "material-group/update/:id",
  materialGroupDelete = "material-group/delete",
  materialGroupViewAll = "material-group/viewAll",

  //* Product group
  productGroupList = "product-group/list",
  productGroupAdd = "product-group/add",
  productGroupEdit = "product-group/update/:id",
  productGroupDelete = "product-group/delete",
  productGroupViewAll = "product-group/viewAll",

  //* Device group
  deviceGroupList = "device-group/list",
  deviceGroupAdd = "device-group/add",
  deviceGroupEdit = "device-group/update/:id",
  deviceGroupDelete = "device-group/delete",
  deviceGroupViewAll = "device-group/viewAll",

  //* Machine group
  machineGroupList = "machine-group/list",
  machineGroupAdd = "machine-group/add",
  machineGroupEdit = "machine-group/update/:id",
  machineGroupDelete = "machine-group/delete",
  machineGroupViewAll = "machine-group/viewAll",

  //* Supplier
  providerCategoryList = "provider-category/list",
  providerCategoryAdd = "provider-category/add",
  providerCategoryEdit = "provider-category/update/:id",
  providerCategoryDelete = "provider-category/delete",
  providerCategoryViewAll = "provider-category/viewAll",

  //* Nation type
  countryList = "country/list",
  countryAdd = "country/add",
  countryEdit = "country/update/:id",
  countryDelete = "country/delete",
  countryViewAll = "country/viewAll",

  //* Subcontractor Type
  subcontractorCategoryList = "subcontractor-category/list",
  subcontractorCategoryAdd = "subcontractor-category/add",
  subcontractorCategoryEdit = "subcontractor-category/update/:id",
  subcontractorCategoryDelete = "subcontractor-category/delete",
  subcontractorCategoryViewAll = "subcontractor-category/viewAll",

  //* Work Type
  workTypeList = "work-type/list",
  workTypeAdd = "work-type/add",
  workTypeEdit = "work-type/update/:id",
  workTypeDelete = "work-type/delete",
  workTypeViewAll = "work-type/viewAll",

  //* Instruction Category
  instructionCategoryList = "instruction-category/list",
  instructionCategoryAdd = "instruction-category/add",
  instructionCategoryEdit = "instruction-category/update/:id",
  instructionCategoryDelete = "instruction-category/delete",
  instructionCategoryViewAll = "instruction-category/viewAll",

  //* Rfi Category
  rfiCategoryList = "rfi-category/list",
  rfiCategoryAdd = "rfi-category/add",
  rfiCategoryEdit = "rfi-category/update/:id",
  rfiCategoryDelete = "rfi-category/delete",
  rfiCategoryViewAll = "rfi-category/viewAll",

  //* File Attach Category
  fileAttachCategoryList = "file-attach-category/list",
  fileAttachCategoryAdd = "file-attach-category/add",
  fileAttachCategoryEdit = "file-attach-category/update/:id",
  fileAttachCategoryDelete = "file-attach-category/delete",
  fileAttachCategoryViewAll = "file-attach-category/viewAll",

  //* Classify
  classifyList = "classify/list",
  classifyAdd = "classify/add",
  classifyEdit = "classify/update/:id",
  classifyDelete = "classify/delete",
  classifyViewAll = "classify/viewAll",

  //* Contacts
  contactsList = "contacts/list",
  contactsAdd = "contacts/add",
  contactsEdit = "contacts/update/:id",
  contactsDelete = "contacts/delete",
  contactsViewAll = "contacts/viewAll",

  //* Company
  companyList = "company/list",
  companyAdd = "company/add",
  companyEdit = "company/update/:id",
  companyDelete = "company/delete",
  companyViewAll = "company/viewAll",

  //* BOQ Group
  boqGroupList = "boq-group/list",
  boqGroupAdd = "boq-group/add",
  boqGroupEdit = "boq-group/update/:id",
  boqGroupDelete = "boq-group/delete",
  boqGroupViewAll = "boq-group/viewAll",
}
