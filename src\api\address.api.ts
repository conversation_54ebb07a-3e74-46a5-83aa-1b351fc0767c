import { AxiosPromise } from "axios";
import { request } from "utils/request";

export const addressApi = {
  getDistricts: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/public/district",
      params,
    }),
  getCities: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/public/city",
      params,
    }),
  getWard: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/public/ward",
      params,
    }),

  updateShipFee: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/public/city/${id}`,
      method: "patch",
      data,
    }),
};
