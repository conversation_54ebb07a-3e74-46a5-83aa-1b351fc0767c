import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const materialGroupApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/materialGroup",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/materialGroup",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/materialGroup/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/materialGroup/${id}`,
      method: "delete",
    }),
  import: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/materialGroup/import`,
      method: "post",
      data,
    }),
};
