import { useTheme } from "context/ThemeContext";
import * as React from "react";

const LocationIcon = ({ fill = "#050505", width = 16, height = 16 }) => {
  const { darkMode } = useTheme();
  if (darkMode) {
    fill = "#ffffff";
  }
  return (
    <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    fill={fill}
  >
    <path
      fill={fill}
      fillRule="evenodd"
      d="M2.167 6.762C2.167 3.496 4.77.833 8 .833s5.833 2.663 5.833 5.93c0 1.576-.45 3.269-1.244 4.732-.793 1.46-1.952 2.73-3.402 3.408a2.8 2.8 0 0 1-2.374 0c-1.45-.678-2.61-1.948-3.403-3.409-.794-1.462-1.244-3.155-1.244-4.732ZM8 1.833c-2.661 0-4.833 2.199-4.833 4.93 0 1.397.401 2.927 1.122 4.254.722 1.33 1.742 2.416 2.947 2.98a1.8 1.8 0 0 0 1.527 0c1.206-.564 2.226-1.65 2.948-2.98.72-1.327 1.122-2.857 1.122-4.255 0-2.73-2.172-4.929-4.833-4.929Zm0 3.334a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3Zm-2.5 1.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0Z"
      clipRule="evenodd"
    />
  </svg>
  );
};
export default LocationIcon;
