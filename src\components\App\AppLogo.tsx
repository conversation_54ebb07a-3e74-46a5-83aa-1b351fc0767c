import { ImgHTMLAttributes } from "react";
import LogoImg from "assets/images/logo.png";
import { useTheme } from "context/ThemeContext";
import { settings } from "settings";

export interface AppLogoProps {
  width?: number;
  height?: number;
}

export const AppLogo = ({ width = 100, height }: AppLogoProps) => {
  const { darkMode } = useTheme();

  return (
    <div
      style={{
        display: "block",
        width,
        height,
      }}
    >
      <img
        className="full"
        src={darkMode ? settings.logoWhite : settings.logo}
      />
    </div>
  );
};
