import { useTheme } from "context/ThemeContext";
import * as React from "react";

const Camera360Icon = ({ fill = "#19345B" }) => {
  const { darkMode } = useTheme();
  if (darkMode) {
    fill = "#ffffff";
  }
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width={20} height={20} fill={fill}>
      <path
        fill={fill}
        d="M3.885 10.139c-.16.427-.263.873-.306 1.327-.5.176-.94.372-1.308.586-.708.412-1.099.856-1.1 1.25 0 .42.458.906 1.255 1.336.786.424 1.847.772 3.101 1.021l-.725-1.179a.586.586 0 0 1 .998-.614l1.449 2.354a.587.587 0 0 1-.231.829L4.56 18.308a.585.585 0 1 1-.535-1.043l1-.513c-1.253-.269-2.33-.636-3.157-1.082C.324 14.837 0 13.922 0 13.302c0-.587.292-1.455 1.683-2.263.594-.346 1.34-.65 2.202-.9Zm12.23 0c.866.252 1.614.557 2.21.905C19.71 11.85 20 12.717 20 13.302c0 .773-.489 1.887-2.815 2.802-1.637.642-3.858 1.043-6.256 1.126h-.02a.586.586 0 0 1-.021-1.171c2.268-.079 4.352-.45 5.868-1.045 1.297-.51 2.072-1.15 2.072-1.712 0-.393-.388-.836-1.093-1.246a7.718 7.718 0 0 0-1.314-.59 5.194 5.194 0 0 0-.306-1.327ZM10 2.5c3.23 0 5.86 2.629 5.86 5.86 0 3.23-2.63 5.859-5.86 5.859a5.866 5.866 0 0 1-5.86-5.86C4.14 5.13 6.77 2.5 10 2.5Zm0 3.516a2.346 2.346 0 0 0-2.344 2.343A2.346 2.346 0 0 0 10 10.703a2.347 2.347 0 0 0 2.344-2.344A2.346 2.346 0 0 0 10 6.016Zm0 1.171c.646 0 1.172.526 1.172 1.172 0 .646-.526 1.172-1.172 1.172A1.173 1.173 0 0 1 8.828 8.36c0-.646.526-1.171 1.172-1.171Z"
      />
    </svg>
  );
};
export default Camera360Icon;
