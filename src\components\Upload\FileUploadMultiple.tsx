import { ReactNode, useEffect, useState } from "react";
import { PaperClipOutlined } from "@ant-design/icons";
import type { UploadFile, UploadProps } from "antd";
import { Button, message, Row, Upload } from "antd";
import { $url } from "utils/url";
import { getToken } from "utils/auth";
import { useTheme } from "@mui/material";
import clsx from "clsx";
import FileUploadItem, { FileCustomProps } from "./FileUploadItem";
import UploadImg from "assets/images/upload-img.png";
import CustomButton from "components/Button/CustomButton";
import { ReactComponent as UploadSvg } from "assets/svgs/icon-upload.svg";
import { uniqueId } from "lodash";
const { Dragger } = Upload;

interface Props {
  fileList: UploadFile[];
  fileTypeText?: string;
  fileTypes?: string[];
  placeholder?: string;
  uploadUrl?: string;
  acceptType?: string;
  readOnly?: boolean;
  uploadFileComment?: boolean;
  disabled?: boolean;
  id?: string;
  className?: string;
  draggerContent?: ReactNode;
  onUploadOk: (file: Array<any>) => void;
  onDelete: (file: Array<any>) => void;
  onBefore?: () => Promise<boolean>;
  variant?: "compact" | "detailed";
  maxFile?: number;
  hideUploadButton?: boolean;
  uploadDescription?: string;
}

export const FileUploadMultiple = ({
  onUploadOk,
  onDelete,
  onBefore,
  fileList,
  fileTypes,
  fileTypeText = "PDF",
  placeholder,
  acceptType,
  readOnly = false,
  uploadFileComment,
  uploadUrl = import.meta.env.VITE_API_URL + "/v1/admin/fileAttach/upload",
  id,
  disabled,
  className,
  draggerContent,
  variant = "compact",
  maxFile = 5,
  hideUploadButton = false,
  uploadDescription = "Hỗ trợ lưu trữ tất cả các định dạng tập tin.",
}: Props) => {
  const theme = useTheme();
  const [duplicateImage, setDuplicateImage] = useState(false);

  const [uidDragger, setUidDragger] = useState(uniqueId("FileUploadMultiple"));
  // const [fileListPaste, setFileListPaste] = useState<UploadFile[]>();
  // useEffect(() => {
  //   setFileListPaste(() => fileList.map((e) => ({ ...e })));
  // }, [fileList]);

  const props: UploadProps = {
    accept: acceptType,
    // id: id,
    fileList,
    name: "file",
    multiple: true,
    action: uploadUrl,
    className: clsx("inline-block w-full", className),
    disabled,
    beforeUpload(info) {
      setDuplicateImage(false);
      if (fileList?.length > 0 && info) {
        const value = fileList.find(
          (item) => item.name === info.name && item.size === info.size
        );

        if (value) {
          setDuplicateImage(true);
          message.error("Tệp đã tồn tại");
          return;
        }
      }
    },
    onChange(info) {
      if (duplicateImage) return;
      const currentFiles = info.fileList;

      if (currentFiles.length > maxFile) {
        message.warning(`Số file tối đa là: ${maxFile}`);
        return;
      }
      // console.log("fileListOrigin", fileListOrigin);
      if (info.file.status === "uploading") {
        info.file.status = "done";
        // setLoading(true); // Đang trong quá trình tải file lên
      }

      if (info.file.status === "done") {
        // Khi file tải lên thành công
        const updatedFileList = info.fileList.map((file) => {
          if (file.uid === info.file.uid && info.file.response) {
            // Gán URL từ response vào file item
            return {
              ...file,
            };
          }
          return file;
        });
        // setFileListPaste(updatedFileList);
        onUploadOk(updatedFileList);
        // setFileListOrigin(updatedFileList); // Cập nhật danh sách file với URL mới
        // onUploadOk(updatedFileList); // Gọi hàm onUploadOk sau khi tải xong với URL đã được gán

        // setLoading(false); // Dừng loading
      }
    },
    onDrop(e) {
      console.log("Dropped files", e.dataTransfer.files);
    },
  };
  const handleDelete = (id: string) => {
    // console.log("fileListOrigin", fileListOrigin);
    const filterFile = fileList?.filter((item) => {
      return item.uid !== id;
    });
    // setFileListOrigin(filterFile);
    // @ts-ignore
    onUploadOk(filterFile);
  };
  const handleDeleteAll = () => {
    onUploadOk([]);
  };
  const uploadFileToServer = async (file: File) => {
    const formData = new FormData();
    formData.append("file", file); // Thêm file vào FormData
    const response = await fetch(uploadUrl, {
      method: "POST",
      body: formData,
      headers: {
        token: getToken() || "", // Nếu cần thêm header
      },
    });

    if (!response.ok) {
      throw new Error("Upload failed");
    }

    return response.json(); // Giả sử server trả về JSON
  };
  const handleUpload = (file: File, responseData: { path: string }) => {
    const newFile: UploadFile<any> = {
      uid: Math.floor(Math.random() * 1000).toString(),
      name: file.name,
      size: file.size,
      url: `${import.meta.env.VITE_IMG_URL}${responseData.path}`,
      type: file.type,
    };
    // setFileListPaste((prevFileList) => {
    //   const updatedFileList = [...(prevFileList || []), newFile];
    //   onUploadOk(updatedFileList);
    //   return updatedFileList;
    // });
  };

  const handlePasteImage = async (e: ClipboardEvent) => {
    const target = e.target as HTMLElement;

    // Kiểm tra nếu sự kiện xảy ra bên trong textarea hoặc input
    if (target.tagName === "TEXTAREA" || target.tagName === "INPUT") {
      return; // Bỏ qua xử lý sự kiện paste
    }
    const items = e.clipboardData?.items;
    console.log(items);
    if (items) {
      const fileArray: File[] = Array.from(items)
        .map((item) => item.getAsFile())
        .filter((file) => file) as File[];

      console.log(fileArray);
      // Xử lý tất cả các file ảnh dán
      for (const file of fileArray) {
        const response = await uploadFileToServer(file); // Upload ảnh lên server
        handleUpload(file, response.data); // Thêm ảnh vào danh sách
      }
    }
  };
  useEffect(() => {
    const pasteHandler = handlePasteImage as unknown as EventListener;
    window.addEventListener("paste", pasteHandler);

    return () => {
      window.removeEventListener("paste", pasteHandler);
    };
  }, [handlePasteImage]);

  return (
    <div className="p-[16px] bg-[#F7F7F7] h-full">
      {uploadFileComment ? (
        <>
          <Upload
            fileList={fileList}
            headers={{ token: getToken() || "" }}
            showUploadList={false}
            {...props}
          >
            <Button icon={<PaperClipOutlined />}></Button>
          </Upload>
          <Row gutter={[6, 6]}>
            {fileList &&
              fileList.map((item, index) => {
                const file: FileCustomProps = {
                  id: item.uid,
                  fileName: item.name || item.response?.data?.originalname,
                  fileSize: item.size || item.response?.data?.size,
                  fileUrl: item.url || $url(item.response?.data?.path),
                };
                return (
                  <FileUploadItem
                    key={index}
                    file={file}
                    onDelete={handleDelete}
                  />
                );
              })}
          </Row>
        </>
      ) : (
        <>
          {fileList && (
            <div className="relative  ">
              {/* {fileList.length > 0 && (
                <Button
                  className="w-fit text-center font-bold"
                  danger
                  onClick={handleDeleteAll}
                >
                  {t("deleteAll")}
                </Button>
              )} */}
              <div className="w-full space-y-3">
                {fileList.map((item, index) => {
                  const file: FileCustomProps = {
                    id: item.uid,
                    fileType: item.type,
                    fileName: item.name || item.response?.data?.originalname,
                    fileSize: item.size || item.response?.data?.size,
                    fileUrl: item.url || $url(item.response?.data?.path),
                  };
                  return (
                    <FileUploadItem
                      key={index}
                      file={file}
                      onDelete={handleDelete}
                      variant={variant}
                    />
                  );
                })}
              </div>
            </div>
          )}
          {fileList?.length > 0 ? (
            <div className="pt-2 flex flex-col gap-4">
              <CustomButton
                variant="outline"
                className="w-[max-content]"
                icon={<img src={UploadImg} height={22} width={22} />}
                onClick={() => {
                  document.getElementById(uidDragger)?.click();
                }}
              >
                Tải thêm tệp
              </CustomButton>
              {!hideUploadButton && (
                <CustomButton
                  variant="outline"
                  icon={<UploadSvg />}
                  className="w-[max-content]"
                  onClick={(ev) => {
                    ev.preventDefault();
                    ev.stopPropagation();
                    message.warning("Tính năng đang phát triển");
                  }}
                >
                  Download tài liệu mẫu
                </CustomButton>
              )}
            </div>
          ) : (
            <></>
          )}
          <div
            className={clsx(
              "hover:shadow-md rounded-md transition-all h-full",
              fileList?.length > 0 ? "invisible h-0" : ""
            )}
          >
            <Dragger
              headers={{ token: getToken() || "" }}
              showUploadList={false}
              {...props}
              id={uidDragger}
              className="!h-full"
            >
              {draggerContent ? (
                draggerContent
              ) : (
                <p className="ant-upload-text flex flex-col gap-2 items-center">
                  <img
                    src={UploadImg}
                    height={30}
                    width={30}
                    className="mr-2"
                  />
                  <div
                    className="font-bold"
                    style={{ fontSize: 13, color: "var(--color-neutral-n8)" }}
                  >
                    {"Tải lên tệp đính kèm"}
                  </div>
                  <div
                    style={{ fontSize: 13, color: "var(--color-neutral-n6)" }}
                  >
                    {uploadDescription}
                  </div>
                  {!hideUploadButton && (
                    <CustomButton
                      variant="outline"
                      icon={<UploadSvg />}
                      onClick={() => {
                        message.warning("Tính năng đang phát triển");
                      }}
                    >
                      Download tài liệu mẫu
                    </CustomButton>
                  )}
                </p>
              )}
            </Dragger>
          </div>
        </>
      )}
    </div>
  );
};
