import { Button, Col, Form, Input, message, Modal, Row, Space } from "antd";
import { Rule } from "antd/lib/form";
import { dictionaryApi } from "api/dictionary.api";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { ModalStatus } from "types/modal";
import {
  Dictionary,
  DictionaryType,
  DictionaryTypeTrans,
} from "types/dictionary";
import CustomInput from "components/Input/CustomInput";
import { DictionarySelector } from "components/Selector/DictionarySelector";
import { DictionaryTreeTypes } from "utils/common";
import CustomButton from "components/Button/CustomButton";
import { useWatch } from "antd/es/form/Form";

const rules: Rule[] = [{ required: true }];

export interface DictionaryModal {
  handleCreate: (parentId?: number) => void;
  handleUpdate: (dictionary: Dictionary, parentId?: number) => void;
}
interface DictionaryModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
  type?: DictionaryType;
}

export const DictionaryModal = React.forwardRef(
  ({ onClose, onSubmitOk, type }: DictionaryModalProps, ref) => {
    const [form] = Form.useForm<Dictionary & { parentId?: number }>();
    const formParentId = useWatch("parentId", form);
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");
    const [selectedDictionary, setSelectedDictionary] = useState<Dictionary>();

    useImperativeHandle<any, DictionaryModal>(
      ref,
      () => ({
        handleCreate(parentId?: number) {
          form.resetFields();
          form.setFieldValue("parentId", parentId);
          setVisible(true);
          setStatus("create");
        },
        handleUpdate(dictionary: Dictionary, parentId?: number) {
          form.setFieldsValue({ ...dictionary, parentId });
          setVisible(true);
          setStatus("update");
          setSelectedDictionary(dictionary);
        },
      }),
      []
    );

    const getPayload = () => {
      const { parentId, ...rest } = form.getFieldsValue();
      return {
        parentId,
        dictionary: { ...rest, type },
      };
    };

    const createData = async () => {
      const valid = await form.validateFields();
      const data = getPayload();

      setLoading(true);
      try {
        const res = await dictionaryApi.create(data);
        message.success("Tạo thành công");
        handleClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const valid = await form.validateFields();
      const data = getPayload();
      setLoading(true);
      try {
        const res = await dictionaryApi.update(
          selectedDictionary?.id || 0,
          data
        );
        message.success("Cập nhật thành công");
        handleClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    const handleClose = () => {
      onClose?.();
      setVisible(false);
      form.resetFields();
      setStatus("create");
      setSelectedDictionary(undefined);
    };

    return (
      <Modal
        className="footer-full"
        onCancel={() => {
          handleClose();
        }}
        centered
        open={visible}
        title={
          <div className="text-[20px] font-bold">
            {status == "create"
              ? `Tạo ${DictionaryTypeTrans[type as DictionaryType].label}`
              : `Sửa ${DictionaryTypeTrans[type as DictionaryType].label}`}
          </div>
        }
        style={{ top: 20 }}
        width={500}
        confirmLoading={loading}
        onOk={() => {
          status == "create" ? createData() : updateData();
        }}
        cancelButtonProps={{ style: { display: "none" } }}
        footer={(originNode) => {
          return (
            <Space>
              <CustomButton
                variant="outline"
                onClick={() => {
                  handleClose();
                }}
              >
                Hủy
              </CustomButton>
              <CustomButton
                onClick={() => {
                  status == "create" ? createData() : updateData();
                }}
              >
                Lưu
              </CustomButton>
            </Space>
          );
        }}
      >
        <Form layout="vertical" form={form}>
          <Form.Item name="parentId" hidden></Form.Item>
          <Row gutter={16}>
            {DictionaryTreeTypes.includes(type as DictionaryType) &&
              formParentId && (
                <Col span={24}>
                  <Form.Item
                    label="Lựa chọn cấp cha"
                    name="parentId"
                    rules={rules}
                  >
                    <DictionarySelector
                      initQuery={{ type }}
                      placeholder="Lựa chọn cấp cha"
                    />
                  </Form.Item>
                </Col>
              )}

            {type == DictionaryType.WorkType && (
              <Col span={24}>
                <Form.Item label="Mã" name="code" rules={rules}>
                  <CustomInput placeholder="" />
                </Form.Item>
              </Col>
            )}

            <Col span={24}>
              <Form.Item label="Tên" name="name" rules={rules}>
                <CustomInput placeholder="" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
);
