import { Brand } from "./brand";
import { DeviceCategory } from "./deviceCategory";
import { Department } from "./department";
import { Project } from "./project";
import { Provider } from "./provider";
import { Staff } from "./staff";
import { Unit } from "./unit";
import { FileAttach } from "./fileAttach";

export enum DeviceType {
  Machine = "MACHINE", // máy móc
  Equipment = "EQUIPMENT", // thiết bị
}

export enum DeviceStatus {
  Active = "ACTIVE",
  Inactive = "INACTIVE",
}

export const DeviceStatusTrans = {
  [DeviceStatus.Active]: {
    label: "Hoạt động",
    value: DeviceStatus.Active,
    color: "green",
  },
  [DeviceStatus.Inactive]: {
    label: "Bị khóa",
    value: DeviceStatus.Inactive,
    color: "red",
  },
};

export const DeviceTypeTrans = {
  [DeviceType.Machine]: {
    label: "<PERSON><PERSON>y móc",
    value: DeviceType.Machine,
  },
  [DeviceType.Equipment]: {
    label: "Thiết bị",
    value: DeviceType.Equipment,
  },
};

export enum ProductOrigin {
  // Production = "PRODUCTION", // Sản xuất
  // Processing = "PROCESSING", // Gia công
  DomesticPurchase = "DOMESTIC_PURCHASE", // Mua hàng nội địa
  Import = "IMPORT", // Nhập khẩu
  // Service = "SERVICE" // Dịch vụ
}

export const ProductOriginTrans = {
  [ProductOrigin.DomesticPurchase]: {
    label: "Nội địa",
    value: ProductOrigin.DomesticPurchase,
  },
  [ProductOrigin.Import]: {
    label: "Nhập khẩu",
    value: ProductOrigin.Import,
  },
};

export interface Device {
  id: number;
  files: string;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  // Mã (*)
  code: string;
  type: DeviceType;
  // Mã Nhóm (*)
  groupCode: string;
  // Tên (*)
  name: string;
  supplierProductCode?: string; // Mã SP NCC
  productOrigin?: string; // Nguồn gốc sản phẩm
  placeOfManufacture?: string; // Xuất xứ
  warrantyPeriodMonths?: number; // Số tháng bảo hành
  warrantyConditions?: string; // Điều kiện bảo hành
  // Ngày mua
  purchaseAt?: number;
  // Ghi chú
  notes?: string;
  // Mã định danh
  identificationCode?: string;
  // Mô tả
  description?: string;
  // Mã thuộc tính
  attributeCode?: string;
  // Số hiệu
  serialNumber?: string;
  // Thuộc tính định danh
  identificationAttribute?: string;
  UseAt?: number; // Ngày sử dụng
  status: DeviceStatus; // Tình trạng
  isActive: boolean;
  unit: Unit;
  deviceCategory: DeviceCategory;
  provider: Provider;
  brand: Brand;
  project: Project;
  department: Department;
  staff: Staff; // nhan vien su dung
  oldCode: string;
  rentalCost: number;
  purchaseCost: number;
  avatar: string;
  useAt: number;
  fileAttaches: FileAttach[];
}
