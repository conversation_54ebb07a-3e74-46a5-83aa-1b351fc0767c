import { Select } from "antd";
import { DefaultOptionType } from "antd/es/select";
import { SelectProps } from "antd/lib";
import { debounce, isEmpty, uniqBy } from "lodash";
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
} from "react";
import { QueryParams2 } from "types/query";
import { useTask } from "hooks/useTask";
import { taskApi } from "api/task.api";
import { Task } from "types/task";

type CustomFormItemProps = {
  value?: number;
  initQuery?: QueryParams2;
  disabled?: boolean;
  selectedColor?: any[];
  multiple?: boolean;
  onChange?: (value: any) => void;
  selectProps?: SelectProps;
  initOptionItem?: Task | Task[];
  valueIsOption?: boolean;
  allowClear?: boolean;
  placeholder?: string;
  addonOptions?: any[];
};

export interface TaskSelector {
  refresh(): void;
}

export const TaskSelector = forwardRef(
  (
    {
      value,
      onChange,
      initQuery,
      disabled,
      multiple = false,
      selectedColor,
      initOptionItem,
      valueIsOption,
      selectProps,
      allowClear = true,
      placeholder = "Chọn task",
      addonOptions = [],
    }: CustomFormItemProps,
    ref
  ) => {
    const {
      tasks,
      loading,
      fetchData,
      query,
      setData: setProjectItems,
      isFetched,
    } = useTask({
      initQuery: {
        page: 1,
        limit: 50,
        ...initQuery,
      },
    });

    useImperativeHandle<any, TaskSelector>(
      ref,
      () => ({
        refresh() {
          fetchData();
        },
      }),
      []
    );

    useEffect(() => {
      fetchData();
    }, [selectedColor]);

    const debounceSearch = useCallback(
      debounce((keyword) => {
        query.search = keyword;
        fetchData();
      }, 300),
      [query]
    );

    //xử lý nếu trong projects thiếu value hiện tại thì add vào
    useEffect(() => {
      if (value && isFetched) {
        const find = tasks.find((e) => e.id == value);
        if (!find) {
          taskApi.findOne(value).then((res) => {
            if (!isEmpty(res.data)) {
              setProjectItems((prev) => [res.data, ...prev]);
            }
          });
        }
      }
    }, [value, tasks, isFetched]);

    const options = useMemo(() => {
      let data = [...tasks];
      if (initOptionItem) {
        if ((initOptionItem as Task[])?.length) {
          data = data.concat(initOptionItem);
        } else {
          data.push(initOptionItem as Task);
        }
      }

      return uniqBy([...addonOptions, ...data], (data) => data.id);
    }, [tasks, initOptionItem]);

    return (
      <Select<any, DefaultOptionType>
        mode={multiple ? "multiple" : undefined}
        disabled={disabled}
        loading={loading}
        style={{ width: "100%" }}
        allowClear={allowClear}
        onSearch={debounceSearch}
        showSearch
        placeholder={placeholder}
        filterOption={false}
        value={value}
        onChange={(v, opts) => {
          if (v === undefined || v.length == 0) {
            debounceSearch(v);
          }
          if (valueIsOption) {
            if (opts instanceof Array) {
              onChange?.(opts?.map((v) => v.item));
            } else {
              onChange?.(opts?.item);
            }
          } else {
            onChange?.(v);
          }
        }}
        {...selectProps}
      >
        {options?.map((item) => (
          <Select.Option item={item} value={item.id} key={item.id}>
            <div className="flex items-center gap-2">
              <span>{item.title}</span>
            </div>
          </Select.Option>
        ))}
      </Select>
    );
  }
);
