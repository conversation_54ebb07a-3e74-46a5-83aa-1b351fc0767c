import { <PERSON>, <PERSON>, <PERSON><PERSON>, Row, Col, DatePicker, message, Modal } from "antd";
import { LeftOutlined } from "@ant-design/icons";
import CustomButton from "components/Button/CustomButton";
import CustomInput from "components/Input/CustomInput";
import CustomSelect from "components/Input/CustomSelect";
import PageTitle from "components/PageTitle/PageTitle";
import QueryLabel from "components/QueryLabel/QueryLabel";
import { useTheme } from "context/ThemeContext";
import React, { useEffect, useRef, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { checkRoles } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { observer } from "mobx-react";
import { PermissionNames } from "types/PermissionNames";
import { getTitle } from "utils";
import CardImage from "components/CardImage/CardImage";
import { useFileAttach } from "hooks/useFileAttach";
import { FileAttachType, FileAttachTypeTrans } from "types/fileAttach";
import EmptyIcon from "assets/svgs/EmptyIcon";
import { settings } from "settings";
import { appStore } from "store/appStore";
import { FolderCreateModal } from "components/FolderCreateModal/FolderCreateModal";
import dayjs from "dayjs";

function ImagePage({ title }: { title: string }) {
  const { haveAddPermission, haveViewAllPermission } = checkRoles(
    {
      add: PermissionNames.projectDocAdd,
      viewAll: PermissionNames.projectDocViewAll,
    },
    permissionStore.permissions
  );

  const [searchParams] = useSearchParams();
  const currentFolderFromParams = searchParams.get("folder");

  const [searchValue, setSearchValue] = useState("");
  const [projectFilter, setProjectFilter] = useState("");
  const [dateFilter, setDateFilter] = useState("");
  const [typeFilter, setTypeFilter] = useState("");
  const [currentFolder, setCurrentFolder] = useState<string | null>(
    currentFolderFromParams
  );

  const folderCreateModalRef = useRef<FolderCreateModal>();

  console.log("appStore:", appStore.currentProject);

  const {
    fileAttaches,
    total,
    fetchData,
    loading,
    setQuery,
    query,
    getTransformedData,
    createFileAttach,
    deleteFileAttach,
  } = useFileAttach({
    initQuery: {
      page: 1,
      limit: 8,
      isAdmin: haveViewAllPermission ? true : undefined,
      search: "",
      project: "",
      date: "",
      type: "",
      path: currentFolder ? `/${currentFolder}` : "/",
    },
  });

  useEffect(() => {
    document.title = getTitle(title);
  }, [title]);

  useEffect(() => {
    const currentPath = currentFolder ? `/${currentFolder}` : "/";
    fetchData(currentPath);
  }, [query, currentFolder, typeFilter]);

  const { darkMode } = useTheme();
  const navigate = useNavigate();

  const handleSearch = (value: string) => {
    setSearchValue(value);
    // Only trigger search when value is empty (cleared)
    if (value === "") {
      setQuery({ ...query, search: "", page: 1 });
    }
  };

  const handleSearchOnEnter = (value: string) => {
    setQuery({
      ...query,
      search: value,
      project: projectFilter,
      date: dateFilter,
      type: typeFilter,
      page: 1,
    });
  };

  const handleProjectChange = (value: string) => {
    setProjectFilter(value);
    setQuery({ ...query, project: value, page: 1 });
  };

  // Modify handleDateChange to work with DatePicker
  const handleDateChange = (date: any) => {
    const formattedDate = date ? dayjs(date).format("YYYY-MM-DD") : "";
    setDateFilter(formattedDate);
    setQuery({ ...query, date: formattedDate, page: 1 });
  };

  const handleTypeChange = (value: string) => {
    console.log("value", value);
    setQuery({ ...query, type: value, page: 1 });
    setTypeFilter(value);
  };

  const handleCreateFolder = async (name: string) => {
    try {
      const result = await createFileAttach({
        projectId: appStore.currentProject?.id || 0,
        fileAttachCategoryId: 0,
        fileAttach: {
          name: name,
          type: FileAttachType.Folder,
          url: "",
          path: currentFolder ? `/${currentFolder}` : "/",
          size: 0,
          desc: ``,
          isActive: true,
          mimetype: "folder",
          uid: Date.now().toString(),
          date: dayjs().format("YYYY-MM-DD"),
        },
      });

      if (result) {
        message.success("Tạo thư mục thành công!");
        fetchData(currentFolder ? `/${currentFolder}` : "/");
      }
    } catch (error) {
      console.error("Error creating folder:", error);
      message.error("Lỗi khi tạo thư mục");
    }
  };

  const handleUploadImage = () => {
    const queryParams = new URLSearchParams();
    if (currentFolder) {
      queryParams.append("folder", currentFolder);
    }
    navigate(
      `/doc-management/${PermissionNames.imagesAdd}?${queryParams.toString()}`
    );
  };

  const handleCardMenuClick = async (
    id: string,
    action: string,
    type: FileAttachType
  ) => {
    if (action === "edit") {
      const queryParams = new URLSearchParams();
      if (currentFolder) {
        queryParams.append("folder", currentFolder);
      }
      navigate(
        `/doc-management/${PermissionNames.imagesEdit.replace(":id", id)}${
          queryParams.toString() ? `?${queryParams.toString()}` : ""
        }`
      );
    } else if (action === "delete") {
      Modal.confirm({
        title: `Xóa ${type === FileAttachType.Folder ? "thư mục" : "hình ảnh"}`,
        getContainer: () => {
          return document.getElementById("App") as HTMLElement;
        },
        icon: null,
        content: (
          <>
            <div>
              Bạn sẽ không thể khôi phục dữ liệu này sau khi xóa.
              <br />
              Bạn có chắc chắn muốn xóa dữ liệu này?
            </div>
          </>
        ),
        footer: (_, { OkBtn, CancelBtn }) => (
          <>
            <CustomButton
              variant="outline"
              className="cta-button"
              onClick={() => {
                deleteFileAttach(Number(id)).then((success) => {
                  if (success) {
                    const currentPath = currentFolder
                      ? `/${currentFolder}`
                      : "/";
                    fetchData(currentPath);
                  }
                });
                Modal.destroyAll();
              }}
            >
              Có
            </CustomButton>
            <CustomButton
              onClick={() => {
                Modal.destroyAll();
              }}
              className="cta-button"
            >
              Không
            </CustomButton>
          </>
        ),
      });
    }
  };

  const handleCardClick = (id: string, type: string, name: string) => {
    if (type === FileAttachType.Folder) {
      setCurrentFolder(name);
      setQuery({ ...query, path: `/${name}` });
    } else {
      // Open image detail page (view mode)
      const queryParams = new URLSearchParams();
      if (currentFolder) {
        queryParams.append("folder", currentFolder);
      }
      navigate(
        `/doc-management/${PermissionNames.imagesEdit.replace(":id", id)}${
          queryParams.toString() ? `?${queryParams.toString()}` : ""
        }`
      );
    }
  };

  const handleBackToFolders = () => {
    setQuery({
      ...query,
      path: "/",
      page: 1,
      project: "",
      date: "",
      type: "",
      search: "",
    });
    handleResetFilter();
    setCurrentFolder(null);
  };

  const handleResetFilter = () => {
    setSearchValue("");
    setProjectFilter("");
    setDateFilter("");
    setTypeFilter("");
    setQuery({
      ...query,
      search: "",
      project: "",
      date: "",
      type: "",
      page: 1,
    });
  };

  // Get transformed data from hook
  const currentData = getTransformedData(currentFolder);

  // Filter data based on search and filters
  const filteredData = currentData.filter((item: any) => {
    const matchesSearch =
      !searchValue ||
      item.folderName.toLowerCase().includes(searchValue.toLowerCase()) ||
      item.projectName.toLowerCase().includes(searchValue.toLowerCase());

    const matchesProject = !projectFilter || item.projectName === projectFilter;

    // Add date filter
    const matchesDate =
      !dateFilter || (item.date && item.date.includes(dateFilter));

    // Fix type filter to use actual type
    const matchesType = !typeFilter || item.type === typeFilter;

    return matchesSearch && matchesProject && matchesDate && matchesType;
  });

  const isEmpty = currentData.length === 0;

  // Get current folder name for title
  const getCurrentFolderName = () => {
    if (currentFolder) {
      return `THƯ MỤC ${currentFolder.toUpperCase()}`;
    }
    return null;
  };

  return (
    <div className="app-container">
      <PageTitle
        title="Hình ảnh"
        breadcrumbs={["Quản lý tài liệu & Bản vẽ", "Hình ảnh"]}
        extra={
          currentFolder ? (
            <CustomButton size="small" showPlusIcon onClick={handleUploadImage}>
              Thêm hình ảnh
            </CustomButton>
          ) : (
            <CustomButton
              size="small"
              showPlusIcon
              onClick={() => {
                folderCreateModalRef.current?.handleCreate();
              }}
            >
              Tạo thư mục
            </CustomButton>
          )
        }
      />

      <Card>
        {/* Folder Header with Filters - only show when inside a folder */}
        {currentFolder && (
          <div className="mb-6">
            {/* Filter Bar in Folder View */}
            <div className="flex gap-[16px] items-end pb-[12px] justify-between flex-wrap">
              <div className="flex gap-[16px] items-end">
                <div className="w-[300px]">
                  <QueryLabel>Tìm kiếm</QueryLabel>
                  <CustomInput
                    inputSearch
                    placeholder="Tìm kiếm"
                    value={searchValue}
                    onChange={handleSearch}
                    onPressEnter={handleSearchOnEnter}
                    allowClear
                  />
                </div>
                <div>
                  <QueryLabel>Ngày chụp</QueryLabel>
                  <DatePicker
                    placeholder="dd/mm/yyyy"
                    format={settings.dateFormat}
                    style={{ width: "100%" }}
                    onChange={handleDateChange}
                    value={dateFilter ? dayjs(dateFilter) : null}
                  />
                </div>
                {/* <div>
                  <QueryLabel>Loại</QueryLabel>
                  <CustomSelect
                    placeholder="Tất cả"
                    value={typeFilter}
                    options={[
                      { label: "Tất cả", value: "" },
                      { label: "Hình ảnh", value: FileAttachType.Image },
                      { label: "PDF", value: FileAttachType.Pdf },
                      { label: "Video", value: FileAttachType.Video },
                      { label: "Khác", value: FileAttachType.Other },
                    ]}
                    onChange={handleTypeChange}
                    inputStyle={{ minWidth: 180 }}
                  />
                </div> */}
                <CustomButton
                  onClick={() => {
                    setQuery({
                      ...query,
                      search: searchValue,
                      project: projectFilter,
                      date: dateFilter,
                      type: typeFilter,
                      page: 1,
                    });
                  }}
                >
                  Áp dụng
                </CustomButton>

                {(searchValue || projectFilter || dateFilter || typeFilter) && (
                  <CustomButton variant="outline" onClick={handleResetFilter}>
                    Bỏ lọc
                  </CustomButton>
                )}
              </div>
            </div>
            {/* Folder Title with Back Button */}
            <div className="flex items-center gap-3 mb-4 pb-4 border-b border-gray-200">
              <Button
                type="text"
                icon={<LeftOutlined />}
                onClick={handleBackToFolders}
                className="flex items-center gap-2 text-gray-600 hover:text-gray-800"
              />
              <div className="text-lg font-bold">{getCurrentFolderName()}</div>
            </div>
          </div>
        )}

        {/* Filter Bar - only show in main folder view */}
        {!currentFolder && (
          <div className="flex gap-[16px] items-end pb-[12px] justify-between flex-wrap">
            <div className="flex gap-[16px] items-end">
              <div className="w-[300px]">
                <QueryLabel>Tìm kiếm</QueryLabel>
                <CustomInput
                  inputSearch
                  placeholder="Tìm kiếm"
                  value={searchValue}
                  onChange={handleSearch}
                  onPressEnter={handleSearchOnEnter}
                  allowClear
                />
              </div>
              {/* <div>
                <QueryLabel>Ngày chụp</QueryLabel>
                <DatePicker
                  placeholder="dd/mm/yyyy"
                  format={settings.dateFormat}
                  style={{ width: "100%" }}
                  disabled={false}
                />
              </div>
              <div>
                <QueryLabel>Loại</QueryLabel>
                <CustomSelect
                  placeholder="Tất cả"
                  value={typeFilter}
                  options={[
                    { label: "Tất cả", value: "" },
                    { label: "Báo cáo tiến độ", value: "Báo cáo tiến độ" },
                    { label: "An toàn lao động", value: "An toàn lao động" },
                    { label: "Chất lượng", value: "Chất lượng" },
                  ]}
                  onChange={handleTypeChange}
                  inputStyle={{ minWidth: 180 }}
                />
              </div> */}
              <CustomButton
                onClick={() => {
                  setQuery({
                    ...query,
                    search: searchValue,
                    project: projectFilter,
                    date: dateFilter,
                    type: typeFilter,
                    page: 1,
                  });
                }}
              >
                Áp dụng
              </CustomButton>

              {(searchValue || projectFilter || dateFilter || typeFilter) && (
                <CustomButton variant="outline" onClick={handleResetFilter}>
                  Bỏ lọc
                </CustomButton>
              )}
            </div>
          </div>
        )}

        <div className="pb-[16px]">
          <Spin spinning={loading}>
            {isEmpty ? (
              // Empty State
              <div className="flex flex-col items-center justify-center py-16">
                <div className="w-20 h-16 mb-4 opacity-50">
                  <EmptyIcon width={80} height={80} />
                </div>
                <p className="text-gray-500 text-base">
                  {currentFolder
                    ? "Chưa có hình ảnh nào trong thư mục này"
                    : "Chưa có hình ảnh nào được tạo"}
                </p>
              </div>
            ) : (
              // Image Grid
              <Row gutter={[16, 16]}>
                {currentData.map((item) => (
                  <Col key={item.id} xs={24} sm={12} md={8} lg={6}>
                    <CardImage
                      folderName={item.folderName}
                      imageSrc={item.imageSrc}
                      location={item.location}
                      date={item.date}
                      categoryLabel={item.categoryLabel}
                      type={item.type}
                      totalImage={item.totalImage}
                      onClick={() =>
                        handleCardClick(item.id, item.type, item.folderName)
                      }
                      onMenuClick={(action) =>
                        handleCardMenuClick(item.id, action, item.type)
                      }
                    />
                  </Col>
                ))}
              </Row>
            )}
          </Spin>
        </div>
      </Card>
      <FolderCreateModal
        onClose={() => {}}
        onSubmitOk={(name) => {
          handleCreateFolder(name);
        }}
        ref={folderCreateModalRef}
      />
    </div>
  );
}

export default observer(ImagePage);
