import { ReactComponent as DatasourceIcon } from "assets/svgs/datasource.svg";
import { PermissionType } from "types/permission";
import { lazy } from "react";
import { PermissionNames } from "types/PermissionNames";
import { Route } from "./RouteType";
import { StaffType } from "types/staff";
import { DeviceType } from "types/device";
import { ProviderModule } from "types/provider";
import { EMaterialType } from "types/material";

const CreateOrUpdateGoodsPage = lazy(
  () => import("views/GoodsPage/CreateOrUpdateGoodsPage")
);
const CreateOrUpdateDocumentPage = lazy(
  () => import("views/ImageManagement/CreateOrUpdateDocumentPage")
);
const DocumentPage = lazy(() => import("views/ImageManagement/DocumentPage"));
const RolePage = lazy(() => import("views/Role/RolePage"));

// named-ed pages → wrap with .then(...)
const CreateOrUpdateServicePage = lazy(() =>
  import("views/ServicePage/CreateOrUpdateServicePage").then((m) => ({
    default: m.CreateOrUpdateServicePage,
  }))
);
const ServicePage = lazy(() =>
  import("views/ServicePage/ServicePage").then((m) => ({
    default: m.ServicePage,
  }))
);
const CreateOrUpdateAccountPage = lazy(() =>
  import("views/AccountPage/CreateOrUpdateAccountPage").then((m) => ({
    default: m.CreateOrUpdateAccountPage,
  }))
);
const AccountPage = lazy(() =>
  import("views/AccountPage/AccountPage").then((m) => ({
    default: m.AccountPage,
  }))
);
const CreateOrUpdateStaffPage = lazy(() =>
  import("views/StaffPage/CreateOrUpdateStaffPage").then((m) => ({
    default: m.CreateOrUpdateStaffPage,
  }))
);
const StaffPage = lazy(() =>
  import("views/StaffPage/StaffPage").then((m) => ({ default: m.StaffPage }))
);
const CreateOrUpdateDevicePage = lazy(() =>
  import("views/DevicePage/CreateOrUpdateDevicePage").then((m) => ({
    default: m.CreateOrUpdateDevicePage,
  }))
);
const DevicePage = lazy(() =>
  import("views/DevicePage/DevicePage").then((m) => ({ default: m.DevicePage }))
);
const CreateOrUpdateProviderPage = lazy(() =>
  import("views/ProviderPage/CreateOrUpdateProviderPage").then((m) => ({
    default: m.CreateOrUpdateProviderPage,
  }))
);
const ProviderPage = lazy(() =>
  import("views/ProviderPage/ProviderPage").then((m) => ({
    default: m.ProviderPage,
  }))
);
const CreateOrUpdateUnitPage = lazy(() =>
  import("views/Unit/CreateOrUpdateUnitPage").then((m) => ({
    default: m.CreateOrUpdateUnitPage,
  }))
);
const UnitPage = lazy(() =>
  import("views/Unit/UnitPage").then((m) => ({ default: m.UnitPage }))
);
const GoodsPage = lazy(() =>
  import("views/GoodsPage/GoodsPage").then((m) => ({ default: m.GoodsPage }))
);
const ProjectGroupPage = lazy(() =>
  import("views/ProjectGroup/ProjectGroupPage").then((m) => ({
    default: m.ProjectGroupPage,
  }))
);
const CreateOrUpdateTaskTemplatePage = lazy(() =>
  import("views/TaskTemplate/CreateOrUpdateTaskTemplatePage").then((m) => ({
    default: m.CreateOrUpdateTaskTemplatePage,
  }))
);
const TaskTemplatePage = lazy(() =>
  import("views/TaskTemplate/TaskTemplatePage").then((m) => ({
    default: m.TaskTemplatePage,
  }))
);
const CreateOrUpdateRolePage = lazy(() =>
  import("views/Role/CreateOrUpdateRolePage").then((m) => ({
    default: m.CreateOrUpdateRolePage,
  }))
);
const RoleDetail = lazy(() =>
  import("views/Role/RoleDetail").then((m) => ({ default: m.RoleDetail }))
);

export const masterDataRoutes: Route[] = [
  {
    title: "Dữ liệu nguồn",
    breadcrumb: "master-data",
    path: "/master-data",
    name: "master-data",
    aliasPath: "/master-data",
    icon: <DatasourceIcon />,
    permissionTypes: [
      PermissionType.Add,
      PermissionType.Delete,
      PermissionType.Edit,
      PermissionType.List,
    ],
    children: [
      {
        title: "Tạo dịch vụ",
        breadcrumb: "Tạo dịch vụ",
        path: PermissionNames.serviceAdd,
        name: PermissionNames.serviceAdd,
        aliasPath: `/master-data/${PermissionNames.serviceAdd}`,
        element: (
          <CreateOrUpdateServicePage status="create" title="Tạo dịch vụ" />
        ),
        permissionTypes: [PermissionType.Add],
        hidden: true,
        isPublic: true,
        // icon: <TbPackages />,
      },
      {
        title: "Cập nhật dịch vụ",
        breadcrumb: "Cập nhật dịch vụ",
        path: PermissionNames.serviceEdit,
        name: PermissionNames.serviceEdit,
        aliasPath: `/master-data/${PermissionNames.serviceEdit.replace(
          "/:id",
          ""
        )}`,
        element: (
          <CreateOrUpdateServicePage status="update" title="Cập nhật dịch vụ" />
        ),
        // icon: <TbPackages />,
        isPublic: true,
        hidden: true,
        permissionTypes: [PermissionType.Edit],
      },
      {
        title: "Khóa dịch vụ",
        breadcrumb: "Khóa dịch vụ",
        path: PermissionNames.serviceBlock,
        name: PermissionNames.serviceBlock,
        aliasPath: `/master-data/${PermissionNames.serviceBlock}`,
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.serviceViewAll,
        name: PermissionNames.serviceViewAll,
        aliasPath: `/master-data/${PermissionNames.serviceViewAll}`,
        hidden: true,
      },
      {
        title: "Danh mục dịch vụ",
        breadcrumb: "Danh mục dịch vụ",
        path: PermissionNames.serviceList,
        name: PermissionNames.serviceList,
        aliasPath: `/master-data/${PermissionNames.serviceList}`,
        element: <ServicePage title="Danh mục dịch vụ" />,
        permissionTypes: [PermissionType.List],

        // icon: <TbPackages />,
      },
      {
        title: "Danh mục tài khoản",
        breadcrumb: "Danh mục tài khoản",
        path: PermissionNames.accountList,
        name: PermissionNames.accountList,
        aliasPath: `/master-data/${PermissionNames.accountList}`,
        element: <AccountPage title="Danh mục tài khoản" />,
        permissionTypes: [PermissionType.List],

        // icon: <TbPackages />,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.accountViewAll,
        name: PermissionNames.accountViewAll,
        aliasPath: `/master-data/${PermissionNames.accountViewAll}`,
        hidden: true,
      },
      {
        title: "Tạo tài khoản",
        breadcrumb: "Tạo tài khoản",
        path: PermissionNames.accountAdd,
        name: PermissionNames.accountAdd,
        aliasPath: `/master-data/${PermissionNames.accountAdd}`,
        element: (
          <CreateOrUpdateAccountPage status="create" title="Tạo tài khoản" />
        ),
        permissionTypes: [PermissionType.Add],
        hidden: true,
        isPublic: true,
      },
      {
        title: "Cập nhật tài khoản",
        breadcrumb: "Cập nhật tài khoản",
        path: PermissionNames.accountEdit,
        name: PermissionNames.accountEdit,
        aliasPath: `/master-data/${PermissionNames.accountEdit.replace(
          ":id",
          ""
        )}`,
        element: (
          <CreateOrUpdateAccountPage
            status="update"
            title="Chỉnh sửa tài khoản"
          />
        ),
        isPublic: true,
        hidden: true,
        permissionTypes: [PermissionType.Edit],
      },
      {
        title: "Khóa tài khoản",
        breadcrumb: "Khóa tài khoản",
        path: PermissionNames.accountBlock,
        name: PermissionNames.accountBlock,
        aliasPath: `/master-data/${PermissionNames.accountBlock}`,
        hidden: true,
      },
      {
        title: "Reset mật khẩu",
        breadcrumb: "Reset mật khẩu",
        path: PermissionNames.accountResetPassword,
        name: PermissionNames.accountResetPassword,
        aliasPath: `/master-data/${PermissionNames.accountResetPassword}`,
        hidden: true,
      },
      {
        title: "Tạo nhân viên",
        breadcrumb: "Tạo nhân viên",
        path: PermissionNames.staffAdd,
        name: PermissionNames.staffAdd,
        aliasPath: `/master-data/${PermissionNames.staffAdd}`,
        element: (
          <CreateOrUpdateStaffPage status="create" title="Tạo nhân viên" />
        ),
        // icon: <TbPackages />,
        permissionTypes: [PermissionType.Add],
        hidden: true,
        isPublic: true,
      },
      {
        title: "Cập nhật nhân viên",
        breadcrumb: "Cập nhật nhân viên",
        path: PermissionNames.staffEdit,
        name: PermissionNames.staffEdit,
        aliasPath: `/master-data/${PermissionNames.staffEdit.replace(
          ":id",
          ""
        )}`,
        element: (
          <CreateOrUpdateStaffPage
            status="update"
            title="Chỉnh sửa nhân viên"
          />
        ),
        // icon: <TbPackages />,
        isPublic: true,
        hidden: true,
        permissionTypes: [PermissionType.Edit],
      },
      {
        title: "Khóa nhân viên",
        breadcrumb: "Khóa nhân viên",
        path: PermissionNames.staffBlock,
        name: PermissionNames.staffBlock,
        aliasPath: `/master-data/${PermissionNames.staffBlock}`,
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.staffViewAll,
        name: PermissionNames.staffViewAll,
        aliasPath: `/master-data/${PermissionNames.staffViewAll}`,
        hidden: true,
      },
      {
        title: "Danh mục nhân viên",
        breadcrumb: "Danh mục nhân viên",
        path: PermissionNames.staffList,
        name: PermissionNames.staffList,
        aliasPath: `/master-data/${PermissionNames.staffList}`,
        element: <StaffPage title="Danh mục nhân viên" />,
        // icon: <TbPackages />,
        permissionTypes: [PermissionType.List],
      },
      {
        title: "Tạo đối tượng",
        breadcrumb: "Tạo đối tượng",
        path: PermissionNames.staffOtherAdd,
        name: PermissionNames.staffOtherAdd,
        aliasPath: `/master-data/${PermissionNames.staffOtherAdd}`,
        element: (
          <CreateOrUpdateStaffPage
            status="create"
            title="Tạo đối tượng"
            companyType={StaffType.Other}
          />
        ),
        // icon: <TbPackages />,
        permissionTypes: [PermissionType.Add],
        hidden: true,
        isPublic: true,
      },
      {
        title: "Cập nhật đối tượng",
        breadcrumb: "Cập nhật đối tượng",
        path: PermissionNames.staffOtherEdit,
        name: PermissionNames.staffOtherEdit,
        aliasPath: `/master-data/${PermissionNames.staffOtherEdit.replace(
          ":id",
          ""
        )}`,
        element: (
          <CreateOrUpdateStaffPage
            status="update"
            title="Chỉnh sửa đối tượng"
            companyType={StaffType.Other}
          />
        ),
        // icon: <TbPackages />,
        isPublic: true,
        hidden: true,
        permissionTypes: [PermissionType.Edit],
      },
      {
        title: "Khóa đối tượng",
        breadcrumb: "Khóa đối tượng",
        path: PermissionNames.staffOtherBlock,
        name: PermissionNames.staffOtherBlock,
        aliasPath: `/master-data/${PermissionNames.staffOtherBlock}`,
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.staffOtherViewAll,
        name: PermissionNames.staffOtherViewAll,
        aliasPath: `/master-data/${PermissionNames.staffOtherViewAll}`,
        hidden: true,
      },
      {
        title: "Danh mục đối tượng",
        breadcrumb: "Danh mục đối tượng",
        path: PermissionNames.staffOtherList,
        name: PermissionNames.staffOtherList,
        aliasPath: `/master-data/${PermissionNames.staffOtherList}`,
        element: (
          <StaffPage
            key={StaffType.Other}
            title="Danh mục đối tượng"
            companyType={StaffType.Other}
          />
        ),
        // icon: <TbPackages />,
        permissionTypes: [PermissionType.List],
      },
      {
        title: "Tạo thiết bị",
        breadcrumb: "Tạo thiết bị",
        path: PermissionNames.deviceAdd,
        name: PermissionNames.deviceAdd,
        aliasPath: `/master-data/${PermissionNames.deviceAdd}`,
        element: (
          <CreateOrUpdateDevicePage
            type={DeviceType.Equipment}
            key={DeviceType.Equipment}
            status="create"
            title="Tạo thiết bị"
          />
        ),
        // icon: <TbPackages />,
        isPublic: true,
        hidden: true,
        permissionTypes: [PermissionType.Add],
      },
      {
        title: "Cập nhật thiết bị",
        breadcrumb: "Cập nhật thiết bị",
        path: PermissionNames.deviceEdit,
        name: PermissionNames.deviceEdit,
        aliasPath: `/master-data/${PermissionNames.deviceEdit.replace(
          "/:id",
          ""
        )}`,
        element: (
          <CreateOrUpdateDevicePage
            type={DeviceType.Equipment}
            key={DeviceType.Equipment}
            status="update"
            title="Cập nhật thiết bị"
          />
        ),
        // icon: <TbPackages />,
        isPublic: true,
        hidden: true,
        permissionTypes: [PermissionType.Edit],
      },
      {
        title: "Khóa thiết bị",
        breadcrumb: "Khóa thiết bị",
        path: PermissionNames.deviceBlock,
        name: PermissionNames.deviceBlock,
        aliasPath: `/master-data/${PermissionNames.deviceBlock}`,
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.deviceViewAll,
        name: PermissionNames.deviceViewAll,
        aliasPath: `/master-data/${PermissionNames.deviceViewAll}`,
        hidden: true,
      },
      {
        title: "Danh mục thiết bị",
        breadcrumb: "Danh mục thiết bị",
        path: PermissionNames.deviceList,
        name: PermissionNames.deviceList,
        aliasPath: `/master-data/${PermissionNames.deviceList}`,
        element: <DevicePage title="Danh mục thiết bị" />,
        // icon: <TbPackages />,
        permissionTypes: [PermissionType.List],
      },
      {
        title: "Tạo máy thi công",
        breadcrumb: "Tạo máy thi công",
        path: PermissionNames.machineAdd,
        name: PermissionNames.machineAdd,
        aliasPath: `/master-data/${PermissionNames.machineAdd}`,
        element: (
          <CreateOrUpdateDevicePage
            type={DeviceType.Machine}
            key={DeviceType.Machine}
            status="create"
            title="Tạo máy thi công"
          />
        ),
        permissionTypes: [PermissionType.Add],
        isPublic: true,
        hidden: true,
        // icon: <TbPackages />,
      },
      {
        title: "Cập nhật máy thi công",
        breadcrumb: "Cập nhật máy thi công",
        path: PermissionNames.machineEdit,
        name: PermissionNames.machineEdit,
        aliasPath: `/master-data/${PermissionNames.machineEdit.replace(
          "/:id",
          ""
        )}`,
        element: (
          <CreateOrUpdateDevicePage
            type={DeviceType.Machine}
            key={DeviceType.Machine}
            status="update"
            title="Cập nhật máy thi công"
          />
        ),
        // icon: <TbPackages />,
        isPublic: true,
        hidden: true,
        permissionTypes: [PermissionType.Edit],
      },
      {
        title: "Khóa máy thi công",
        breadcrumb: "Khóa máy thi công",
        path: PermissionNames.machineBlock,
        name: PermissionNames.machineBlock,
        aliasPath: `/master-data/${PermissionNames.machineBlock}`,
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.machineViewAll,
        name: PermissionNames.machineViewAll,
        aliasPath: `/master-data/${PermissionNames.machineViewAll}`,
        hidden: true,
      },
      {
        title: "Danh mục máy thi công",
        breadcrumb: "Danh mục máy thi công",
        path: PermissionNames.machineList,
        name: PermissionNames.machineList,
        aliasPath: `/master-data/${PermissionNames.machineList}`,
        element: (
          <DevicePage
            title="Danh mục máy thi công"
            key={DeviceType.Machine}
            type={DeviceType.Machine}
          />
        ),
        permissionTypes: [PermissionType.List],

        // icon: <TbPackages />,
      },
      {
        title: "Tạo NCC",
        breadcrumb: "Tạo NCC",
        path: PermissionNames.providerAdd,
        name: PermissionNames.providerAdd,
        aliasPath: `/master-data/${PermissionNames.providerAdd}`,
        element: (
          <CreateOrUpdateProviderPage
            status="create"
            title="Tạo NCC"
            module={ProviderModule.Supplier}
          />
        ),
        // icon: <TbPackages />,
        isPublic: true,
        hidden: true,
        permissionTypes: [PermissionType.Add],
      },
      {
        title: "Cập nhật NCC",
        breadcrumb: "Cập nhật NCC",
        path: PermissionNames.providerEdit,
        name: PermissionNames.providerEdit,
        aliasPath: `/master-data/${PermissionNames.providerEdit.replace(
          "/:id",
          ""
        )}`,
        element: (
          <CreateOrUpdateProviderPage
            status="update"
            title="Cập nhật NCC"
            module={ProviderModule.Supplier}
          />
        ),
        // icon: <TbPackages />,
        isPublic: true,
        hidden: true,
        permissionTypes: [PermissionType.Edit],
      },
      {
        title: "Khóa NCC",
        breadcrumb: "Khóa NCC",
        path: PermissionNames.providerBlock,
        name: PermissionNames.providerBlock,
        aliasPath: `/master-data/${PermissionNames.providerBlock}`,
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.providerViewAll,
        name: PermissionNames.providerViewAll,
        aliasPath: `/master-data/${PermissionNames.providerViewAll}`,
        hidden: true,
      },
      {
        title: "Danh mục NCC",
        breadcrumb: "Danh mục NCC",
        path: PermissionNames.providerList,
        name: PermissionNames.providerList,
        aliasPath: `/master-data/${PermissionNames.providerList}`,
        element: (
          <ProviderPage
            key={ProviderModule.Supplier}
            title="Danh mục NCC"
            module={ProviderModule.Supplier}
          />
        ),
        permissionTypes: [PermissionType.List],
        // icon: <TbPackages />,
      },
      {
        title: "Tạo đơn vị tính",
        breadcrumb: "Tạo đơn vị tính",
        path: PermissionNames.unitAdd,
        name: PermissionNames.unitAdd,
        aliasPath: `/master-data/${PermissionNames.unitAdd}`,
        element: (
          <CreateOrUpdateUnitPage status="create" title="Tạo đơn vị tính" />
        ),
        isPublic: true,
        hidden: true,
        // icon: <TbPackages />,
        permissionTypes: [PermissionType.Add],
      },
      {
        title: "Cập nhật đơn vị tính",
        breadcrumb: "Cập nhật đơn vị tính",
        path: PermissionNames.unitEdit,
        name: PermissionNames.unitEdit,
        aliasPath: `/master-data/${PermissionNames.unitEdit.replace(
          "/:id",
          ""
        )}`,
        element: (
          <CreateOrUpdateUnitPage
            status="update"
            title="Cập nhật đơn vị tính"
          />
        ),
        // icon: <TbPackages />,
        isPublic: true,
        hidden: true,
        permissionTypes: [PermissionType.Edit],
      },
      {
        title: "Khóa đơn vị tính",
        breadcrumb: "Khóa đơn vị tính",
        path: PermissionNames.unitBlock,
        name: PermissionNames.unitBlock,
        aliasPath: `/master-data/${PermissionNames.unitBlock}`,
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.unitViewAll,
        name: PermissionNames.unitViewAll,
        aliasPath: `/master-data/${PermissionNames.unitViewAll}`,
        hidden: true,
      },
      {
        title: "Danh mục đơn vị tính",
        breadcrumb: "Danh mục đơn vị tính",
        path: PermissionNames.unitList,
        name: PermissionNames.unitList,
        aliasPath: `/master-data/${PermissionNames.unitList}`,
        element: <UnitPage title="Danh mục đơn vị tính" />,
        permissionTypes: [PermissionType.List],

        // icon: <TbPackages />,
      },
      {
        title: "Tạo nguyên vật liệu",
        breadcrumb: "Tạo nguyên vật liệu",
        path: PermissionNames.materialAdd,
        name: PermissionNames.materialAdd,
        aliasPath: `/master-data/${PermissionNames.materialAdd}`,
        element: (
          <CreateOrUpdateGoodsPage
            title="Tạo nguyên vật liệu"
            status="create"
            type={EMaterialType.Material}
          />
        ),
        // icon: <TbPackages />,
        isPublic: true,
        hidden: true,
        permissionTypes: [PermissionType.Add],
      },
      {
        title: "Cập nhật nguyên vật liệu",
        breadcrumb: "Cập nhật nguyên vật liệu",
        path: PermissionNames.materialEdit,
        name: PermissionNames.materialEdit,
        aliasPath: `/master-data/${PermissionNames.materialEdit.replace(
          "/:id",
          ""
        )}`,
        element: (
          <CreateOrUpdateGoodsPage
            title="Cập nhật nguyên vật liệu"
            status="update"
            type={EMaterialType.Material}
          />
        ),
        isPublic: true,
        hidden: true,
        permissionTypes: [PermissionType.Edit],
      },
      {
        title: "Khóa nguyên vật liệu",
        breadcrumb: "Khóa nguyên vật liệu",
        path: PermissionNames.materialBlock,
        name: PermissionNames.materialBlock,
        aliasPath: `/master-data/${PermissionNames.materialBlock}`,
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.materialViewAll,
        name: PermissionNames.materialViewAll,
        aliasPath: `/master-data/${PermissionNames.materialViewAll}`,
        hidden: true,
      },
      {
        title: "Danh mục nguyên vật liệu",
        breadcrumb: "Danh mục nguyên vật liệu",
        path: PermissionNames.materialList,
        name: PermissionNames.materialList,
        aliasPath: `/master-data/${PermissionNames.materialList}`,
        element: (
          <GoodsPage
            key={EMaterialType.Material}
            title="Danh mục nguyên vật liệu"
            type={EMaterialType.Material}
          />
        ),
        permissionTypes: [PermissionType.List],

        // icon: <TbPackages />,
      },
      {
        title: "Tạo hàng hóa",
        breadcrumb: "Tạo hàng hóa",
        path: PermissionNames.goodsAdd,
        name: PermissionNames.goodsAdd,
        aliasPath: `/master-data/${PermissionNames.goodsAdd}`,
        element: (
          <CreateOrUpdateGoodsPage
            title="Tạo hàng hóa"
            status="create"
            type={EMaterialType.Product}
          />
        ),
        // icon: <TbPackages />,
        isPublic: true,
        hidden: true,
        permissionTypes: [PermissionType.Add],
      },
      {
        title: "Cập nhật hàng hóa",
        breadcrumb: "Cập nhật hàng hóa",
        path: PermissionNames.goodsEdit,
        name: PermissionNames.goodsEdit,
        aliasPath: `/master-data/${PermissionNames.goodsEdit.replace(
          "/:id",
          ""
        )}`,
        element: (
          <CreateOrUpdateGoodsPage
            title="Cập nhật hàng hóa"
            status="update"
            type={EMaterialType.Product}
          />
        ),
        isPublic: true,
        hidden: true,
        permissionTypes: [PermissionType.Edit],
      },
      {
        title: "Khóa hàng hóa",
        breadcrumb: "Khóa hàng hóa",
        path: PermissionNames.goodsBlock,
        name: PermissionNames.goodsBlock,
        aliasPath: `/master-data/${PermissionNames.goodsBlock}`,
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.goodsViewAll,
        name: PermissionNames.goodsViewAll,
        aliasPath: `/master-data/${PermissionNames.goodsViewAll}`,
        hidden: true,
      },
      {
        title: "Danh mục hàng hóa",
        breadcrumb: "Danh mục hàng hóa",
        path: PermissionNames.goodsList,
        name: PermissionNames.goodsList,
        aliasPath: `/master-data/${PermissionNames.goodsList}`,
        element: (
          <GoodsPage
            key={EMaterialType.Product}
            title="Danh mục hàng hóa"
            type={EMaterialType.Product}
          />
        ),
        // icon: <TbPackages />,
        permissionTypes: [PermissionType.List],
      },
      {
        title: "Tạo nhóm dự án",
        breadcrumb: "Tạo nhóm dự án",
        path: PermissionNames.projectGroupAdd,
        name: PermissionNames.projectGroupAdd,
        aliasPath: `/master-data/${PermissionNames.projectGroupAdd}`,
        hidden: true,
      },
      {
        title: "Cập nhật nhóm dự án",
        breadcrumb: "Cập nhật nhóm dự án",
        path: PermissionNames.projectGroupEdit,
        name: PermissionNames.projectGroupEdit,
        aliasPath: `/master-data/${PermissionNames.projectGroupEdit}`,
        hidden: true,
      },
      {
        title: "Khóa nhóm dự án",
        breadcrumb: "Khóa nhóm dự án",
        path: PermissionNames.projectGroupBlock,
        name: PermissionNames.projectGroupBlock,
        aliasPath: `/master-data/${PermissionNames.projectGroupBlock}`,
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.projectGroupViewAll,
        name: PermissionNames.projectGroupViewAll,
        aliasPath: `/master-data/${PermissionNames.projectGroupViewAll}`,
        hidden: true,
      },
      {
        title: "Danh mục nhóm dự án",
        breadcrumb: "Danh mục nhóm dự án",
        path: PermissionNames.projectGroupList,
        name: PermissionNames.projectGroupList,
        aliasPath: `/master-data/${PermissionNames.projectGroupList}`,
        element: <ProjectGroupPage title="Danh mục nhóm dự án" />,
        // icon: <TbPackages />,
        permissionTypes: [PermissionType.List],
      },
      {
        title: "Tạo công việc mẫu",
        breadcrumb: "Tạo công việc mẫu",
        path: PermissionNames.taskTemplateAdd,
        name: PermissionNames.taskTemplateAdd,
        aliasPath: `/master-data/${PermissionNames.taskTemplateAdd}`,
        element: (
          <CreateOrUpdateTaskTemplatePage
            status="create"
            title="Tạo công việc mẫu"
          />
        ),
        // icon: <TbPackages />,
        isPublic: true,
        hidden: true,
        permissionTypes: [PermissionType.Add],
      },
      {
        title: "Cập nhật công việc mẫu",
        breadcrumb: "Cập nhật công việc mẫu",
        path: PermissionNames.taskTemplateEdit,
        name: PermissionNames.taskTemplateEdit,
        aliasPath: `/master-data/${PermissionNames.taskTemplateEdit.replace(
          "/:id",
          ""
        )}`,
        element: (
          <CreateOrUpdateTaskTemplatePage
            status="update"
            title="Cập nhật công việc mẫu"
          />
        ),
        // icon: <TbPackages />,
        isPublic: true,
        hidden: true,
        permissionTypes: [PermissionType.Edit],
      },
      {
        title: "Khóa công việc mẫu",
        breadcrumb: "Khóa công việc mẫu",
        path: PermissionNames.taskTemplateBlock,
        name: PermissionNames.taskTemplateBlock,
        aliasPath: `/master-data/${PermissionNames.taskTemplateBlock}`,
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.taskTemplateViewAll,
        name: PermissionNames.taskTemplateViewAll,
        aliasPath: `/master-data/${PermissionNames.taskTemplateViewAll}`,
        hidden: true,
      },
      {
        title: "Danh mục công việc mẫu",
        breadcrumb: "Danh mục công việc mẫu",
        path: PermissionNames.taskTemplateList,
        name: PermissionNames.taskTemplateList,
        aliasPath: `/master-data/${PermissionNames.taskTemplateList}`,
        element: <TaskTemplatePage title="Danh mục công việc mẫu" />,
        // icon: <TbPackages />,
        permissionTypes: [PermissionType.List],
      },
      {
        title: "Tạo tài liệu",
        breadcrumb: "Tạo tài liệu",
        path: PermissionNames.documentAdd,
        name: PermissionNames.documentAdd,
        aliasPath: `/master-data/${PermissionNames.documentAdd}`,
        element: (
          <CreateOrUpdateDocumentPage status="create" title="Tạo tài liệu" />
        ),
        isPublic: true,
        hidden: true,
        permissionTypes: [PermissionType.Add],
      },
      {
        title: "Cập nhật tài liệu",
        breadcrumb: "Cập nhật tài liệu",
        path: PermissionNames.documentEdit,
        name: PermissionNames.documentEdit,
        aliasPath: `/master-data/${PermissionNames.documentEdit.replace(
          "/:id",
          ""
        )}`,
        element: (
          <CreateOrUpdateDocumentPage
            status="update"
            title="Cập nhật tài liệu"
          />
        ),
        isPublic: true,
        hidden: true,
        permissionTypes: [PermissionType.Edit],
      },
      {
        title: "Khóa tài liệu",
        breadcrumb: "Khóa tài liệu",
        path: PermissionNames.documentBlock,
        name: PermissionNames.documentBlock,
        aliasPath: `/master-data/${PermissionNames.documentBlock}`,
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.documentViewAll,
        name: PermissionNames.documentViewAll,
        aliasPath: `/master-data/${PermissionNames.documentViewAll}`,
        hidden: true,
      },
      {
        title: "Danh mục tài liệu",
        breadcrumb: "Danh mục tài liệu",
        path: PermissionNames.documentList,
        name: PermissionNames.documentList,
        aliasPath: `/master-data/${PermissionNames.documentList}`,
        element: <DocumentPage title="Danh mục tài liệu" />,
        // icon: <TbPackages />,
        permissionTypes: [PermissionType.List],
      },

      {
        title: "Tạo vai trò",
        breadcrumb: "Tạo vai trò",
        path: PermissionNames.roleAdd,
        name: PermissionNames.roleAdd,
        aliasPath: `/master-data/${PermissionNames.roleAdd}`,
        element: <CreateOrUpdateRolePage status="create" title="Tạo vai trò" />,
        isPublic: true,
        hidden: true,
        // icon: <TbPackages />,
        permissionTypes: [PermissionType.Add],
      },
      {
        title: "Cập nhật vai trò",
        breadcrumb: "Cập nhật vai trò",
        path: PermissionNames.roleEdit,
        name: PermissionNames.roleEdit,
        aliasPath: `/master-data/${PermissionNames.roleEdit.replace(
          "/:id",
          ""
        )}`,
        element: (
          <CreateOrUpdateRolePage status="update" title="Cập nhật vai trò" />
        ),
        // icon: <TbPackages />,
        isPublic: true,
        hidden: true,
        permissionTypes: [PermissionType.Edit],
      },
      {
        title: "Xóa vai trò",
        breadcrumb: "Xóa vai trò",
        path: PermissionNames.roleDelete,
        name: PermissionNames.roleDelete,
        aliasPath: `/master-data/${PermissionNames.roleDelete}`,
        hidden: true,
      },
      {
        title: "Chi tiết vai trò",
        breadcrumb: "Chi tiết vai trò",
        path: PermissionNames.roleDetail,
        name: PermissionNames.roleDetail,
        aliasPath: `/master-data/${PermissionNames.roleDetail}`,
        element: <RoleDetail title="Chi tiết vai trò" />,
        // icon: <TbPackages />,
        permissionTypes: [PermissionType.List],
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.roleViewAll,
        name: PermissionNames.roleViewAll,
        aliasPath: `/master-data/${PermissionNames.roleViewAll}`,
        hidden: true,
      },
      {
        title: "Vai trò",
        breadcrumb: "Vai trò",
        path: PermissionNames.roleList,
        name: PermissionNames.roleList,
        aliasPath: `/master-data/${PermissionNames.roleList}`,
        element: <RolePage title="Vai trò" />,
        // icon: <TbPackages />,
        permissionTypes: [PermissionType.List],
      },
      {
        title: "Tạo thầu phụ",
        breadcrumb: "Tạo thầu phụ",
        path: PermissionNames.subcontractorAdd,
        name: PermissionNames.subcontractorAdd,
        aliasPath: `/master-data/${PermissionNames.subcontractorAdd}`,
        element: (
          <CreateOrUpdateProviderPage
            status="create"
            title="Tạo thầu phụ"
            module={ProviderModule.SubContractor}
          />
        ),
        // icon: <TbPackages />,
        isPublic: true,
        hidden: true,
        permissionTypes: [PermissionType.Add],
      },
      {
        title: "Cập nhật thầu phụ",
        breadcrumb: "Cập nhật thầu phụ",
        path: PermissionNames.subcontractorEdit,
        name: PermissionNames.subcontractorEdit,
        aliasPath: `/master-data/${PermissionNames.subcontractorEdit.replace(
          "/:id",
          ""
        )}`,
        element: (
          <CreateOrUpdateProviderPage
            status="update"
            title="Cập nhật thầu phụ"
            module={ProviderModule.SubContractor}
          />
        ),
        // icon: <TbPackages />,
        isPublic: true,
        hidden: true,
        permissionTypes: [PermissionType.Edit],
      },
      {
        title: "Khóa thầu phụ",
        breadcrumb: "Khóa thầu phụ",
        path: PermissionNames.subcontractorBlock,
        name: PermissionNames.subcontractorBlock,
        aliasPath: `/master-data/${PermissionNames.subcontractorBlock}`,
        hidden: true,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.subcontractorViewAll,
        name: PermissionNames.subcontractorViewAll,
        aliasPath: `/master-data/${PermissionNames.subcontractorViewAll}`,
        hidden: true,
      },
      {
        title: "Danh mục thầu phụ",
        breadcrumb: "Danh mục thầu phụ",
        path: PermissionNames.subcontractorList,
        name: PermissionNames.subcontractorList,
        aliasPath: `/master-data/${PermissionNames.subcontractorList}`,
        element: (
          <ProviderPage
            key={ProviderModule.SubContractor}
            title="Danh mục thầu phụ"
            module={ProviderModule.SubContractor}
          />
        ),
        // icon: <TbPackages />,
        permissionTypes: [PermissionType.List],
      },
    ],
  },
];
