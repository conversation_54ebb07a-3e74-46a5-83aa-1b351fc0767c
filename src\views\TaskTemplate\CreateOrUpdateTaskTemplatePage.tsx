import {
  <PERSON><PERSON>,
  Card,
  Col,
  DatePicker,
  Form,
  Input,
  message,
  Modal,
  Row,
  Select,
  Space,
  Tabs,
} from "antd";
import { Rule } from "antd/lib/form";
import { useEffect, useMemo, useState } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { FileAttach } from "types/fileAttach";
import { getTitle } from "utils";
import { useWatch } from "antd/es/form/Form";
import {
  TaskTemplate,
  TaskTemplateForm,
  TaskType,
  TaskTypeTrans,
} from "types/taskTemplate";
import CustomSelect from "components/Input/CustomSelect";
import CustomInput from "components/Input/CustomInput";
import CustomButton from "components/Button/CustomButton";
import PageTitle from "components/PageTitle/PageTitle";
import ActiveStatusTagSelect from "components/ActiveStatus/ActiveStatusTagSelect";
import { ModalStatus } from "types/modal";
import { isEmpty } from "lodash";
import { PermissionNames } from "types/PermissionNames";
import { DictionarySelector } from "components/Selector/DictionarySelector";
import { DictionaryType } from "types/dictionary";
import { taskTemplateApi } from "api/taskTemplate.api";
import { Step } from "types/step";
import dayjs from "dayjs";
import CustomDatePicker from "components/Input/CustomDatePicker";
import { settings } from "settings";
import { FileUploadMultiple2 } from "components/Upload/FileUploadMultiple2";
import RequiredResourceTableView from "./components/RequiredResourceTableView";
import ChildTaskTemplateTableView from "./components/ChildTaskTemplateTableView";
import { PlusIcon } from "assets/svgs/PlusIcon";
import TaskTableView from "./components/TaskTableView";
import { InputNumber } from "components/Input/InputNumber";
import { fileAttachApi } from "api/fileAttach.api";
import { $url } from "utils/url";
import { uploadFileAttach } from "hooks/useFileAttach";
import clsx from "clsx";
import { TextInput } from "components/Input/TextInput";
import { observer } from "mobx-react";
import { checkRoles } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { BMDTextArea } from "components/TextArea/BMDTextArea";
import { BMDCKEditor } from "components/Editor";

const rules: Rule[] = [{ required: true }];
const descriptionRules: Rule[] = [{ required: false }];

interface EditTaskTemplatePageProps {
  title: string;
  status: ModalStatus;
}

export const CreateOrUpdateTaskTemplatePage = observer(
  ({ title = "", status }: EditTaskTemplatePageProps) => {
    const { haveEditPermission } = checkRoles(
      {
        edit: PermissionNames.taskTemplateEdit,
      },
      permissionStore.permissions
    );

    const [form] = Form.useForm<TaskTemplateForm>();
    const [loading, setLoading] = useState(false);
    const navigate = useNavigate();
    useEffect(() => {
      document.title = getTitle(title);
    }, []);
    const [fileList, setFileList] = useState<FileAttach[]>([]);
    const type = useWatch("type", form);

    const [searchParams, setSearchParams] = useSearchParams();
    const [selectedTaskTemplate, setSelectedTaskTemplate] =
      useState<TaskTemplate>();
    const [steps, setSteps] = useState<Partial<Step>[]>([]);
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [newJobName, setNewJobName] = useState("");
    const [readonly, setReadonly] = useState(true);
    const params = useParams();

    const getOneTaskTemplate = async (id: number) => {
      try {
        setLoading(true);
        const { data } = await taskTemplateApi.findOne(id);

        if (isEmpty(data)) {
          navigate("/404");

          return;
        }

        setDataToForm(data);
        setSelectedTaskTemplate(data);

        return data as TaskTemplate;
      } catch (e: any) {
      } finally {
        setLoading(false);
      }
    };

    const setDataToForm = (data: TaskTemplate) => {
      form.setFieldsValue({
        ...data,
        // @ts-ignore
        estimateAt: data.estimateAt ? dayjs.unix(data.estimateAt) : undefined,
        workTypeIds: data.workTypes?.map((item) => item.id),
        requiredDevices: data.requiredDevices?.map((item) => ({
          ...item,
          deviceId: item.device?.id,
          unitId: item.unit?.id,
        })),
        requiredMachines: data.requiredMachines?.map((item) => ({
          ...item,
          deviceId: item.device?.id,
          unitId: item.unit?.id,
        })),
        parentIds: data.parents?.map((item) => item.name),
        taskTemplateDetails: data.taskTemplateDetails?.map((item) => ({
          ...item,
          taskId: item.task?.id,
          dependentId: item.dependent?.id,
        })),
      });

      setFileList(data.fileAttaches ? [...data.fileAttaches] : []);
    };

    useEffect(() => {
      document.title = getTitle(title);

      if (status == "update") {
        const taskTemplateId = params.id;
        if (taskTemplateId) {
          getOneTaskTemplate(+taskTemplateId);
          setReadonly(searchParams.get("update") != "1");
        }
      } else {
        setReadonly(false);
      }
    }, []);

    const getDataSubmit = async () => {
      const {
        estimateAt,
        workTypeIds,
        todos,
        requiredDevices,
        requiredMachines,
        taskTemplateDetails,
        ...data
      } = form.getFieldsValue();

      const fileAttachIds = await uploadFileAttach(fileList);

      const payload = {
        taskTemplate: {
          ...data,
          estimateAt: estimateAt
            ? //@ts-ignore
              estimateAt?.startOf("day")?.unix()
            : undefined,
          isActive: selectedTaskTemplate?.isActive,
        },
        workTypeIds,
        todos: todos?.map((item) => ({
          ...item,
          id: item.id > 0 ? item.id : undefined,
          isActive: item.isActive || false,
        })),
        requiredDevices: requiredDevices?.map((item) => ({
          ...item,
          id: item.id > 0 ? item.id : undefined,
        })),
        requiredMachines: requiredMachines?.map((item) => ({
          ...item,
          id: item.id > 0 ? item.id : undefined,
        })),
        taskTemplateDetails: taskTemplateDetails,
        fileAttachIds,
      };

      return payload;
    };

    const createData = async () => {
      const valid = await form.validateFields();

      setLoading(true);
      try {
        const payload = await getDataSubmit();
        const res = await taskTemplateApi.create(payload);
        message.success("Tạo công việc mẫu thành công!");
        navigate(`/master-data/${PermissionNames.taskTemplateList}`);
        setFileList([]);
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const valid = await form.validateFields();

      setLoading(true);
      try {
        const payload = await getDataSubmit();
        const res = await taskTemplateApi.update(
          selectedTaskTemplate!?.id || 0,
          payload
        );
        message.success("Chỉnh sửa công việc mẫu thành công!");
      } finally {
        setLoading(false);
      }
    };

    const handleSubmit = () => {
      if (status == "create") {
        createData();
      } else {
        updateData();
      }
    };

    const pageTitle = useMemo(
      () =>
        status == "create" ? "Tạo công việc mẫu" : "Chỉnh sửa công việc mẫu",
      [status]
    );

    return (
      <div className="app-container">
        <PageTitle
          back
          breadcrumbs={[
            { label: "Dữ liệu nguồn" },
            {
              label: "Danh mục công việc mẫu",
              href: `/master-data/${PermissionNames.taskTemplateList}`,
            },
            { label: pageTitle },
          ]}
          title={pageTitle}
          extra={
            selectedTaskTemplate &&
            status == "update" && (
              <Space>
                <ActiveStatusTagSelect
                  disabled={readonly}
                  isActive={selectedTaskTemplate?.isActive}
                  onChange={(value) => {
                    setSelectedTaskTemplate({
                      ...selectedTaskTemplate,
                      isActive: value,
                    } as TaskTemplate);
                  }}
                />
              </Space>
            )
          }
        />
        <Card>
          <Form
            layout="vertical"
            form={form}
            className={clsx(readonly ? "readonly" : "")}
            disabled={readonly}
          >
            <Card title="Thông tin chung" className="mb-0 form-card">
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item label="Mã công việc mẫu" name="code">
                    <TextInput
                      disabled={status == "update"}
                      placeholder="Nếu không điền hệ thống sẽ tự sinh mã"
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="Tên công việc mẫu"
                    name="name"
                    rules={rules}
                  >
                    <Input placeholder="Tên công việc mẫu" />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="Loại công việc mẫu"
                    name="type"
                    rules={rules}
                  >
                    <Select
                      placeholder="Loại công việc mẫu"
                      options={Object.values(TaskTypeTrans).map((item) => ({
                        label: item.label,
                        value: item.value,
                      }))}
                      onChange={() => {
                        form.setFieldValue("taskTemplateDetails", []);
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="Thời gian dự kiến hoàn thành"
                    name="estimateAt"
                  >
                    <DatePicker
                      allowClear={false}
                      format={settings.dateFormat}
                      className="w-full"
                    />
                  </Form.Item>
                </Col>
                {type == TaskType.Construction ? (
                  <Col span={8}>
                    <Form.Item label="Chi phí dự kiến" name="estimatedCost">
                      <InputNumber placeholder="Chi phí dự kiến" suffix="VNĐ" />
                    </Form.Item>
                  </Col>
                ) : (
                  <Col span={8}>
                    <Form.Item label="Công việc cha" name="parentIds">
                      <Select
                        mode="multiple"
                        disabled
                        placeholder="Công việc cha"
                      />
                    </Form.Item>
                  </Col>
                )}
                <Col span={8}>
                  <Form.Item
                    label="Loại công tác"
                    name="workTypeIds"
                    rules={rules}
                  >
                    <DictionarySelector
                      multiple
                      placeholder="Loại công tác"
                      initQuery={{
                        type: DictionaryType.WorkType,
                        isActive: true,
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col span={24}>
                  <Form.Item
                    label="Ghi chú"
                    name="note"
                    rules={descriptionRules}
                  >
                    <BMDCKEditor
                      placeholder="Ghi chú"
                      disabled={readonly}
                      inputHeight={300}
                      onChange={(content) => {
                        form.setFieldsValue({ note: content });
                      }}
                      value={selectedTaskTemplate?.note}
                    />
                  </Form.Item>
                </Col>
                <Col span={24}>
                  <Tabs defaultActiveKey="1" type="line" className="mt-[16px]">
                    {type == TaskType.Construction && (
                      <>
                        <Tabs.TabPane tab="Thiết bị cần thiết" key="1">
                          <Form.Item name="requiredDevices">
                            <RequiredResourceTableView
                              form={form}
                              type="device"
                            />
                          </Form.Item>
                        </Tabs.TabPane>
                        <Tabs.TabPane tab="Máy thi công" key="2">
                          <Form.Item name="requiredMachines">
                            <RequiredResourceTableView
                              form={form}
                              type="machine"
                            />
                          </Form.Item>
                        </Tabs.TabPane>
                      </>
                    )}

                    <Tabs.TabPane tab="Công việc con" key="3">
                      <Form.Item name="taskTemplateDetails">
                        <ChildTaskTemplateTableView form={form} />
                      </Form.Item>
                    </Tabs.TabPane>
                  </Tabs>
                </Col>

                {type == TaskType.Office && (
                  <Col span={24}>
                    <Form.Item label="Danh sách việc cần làm" name="todos">
                      <TaskTableView form={form as any} readonly={readonly} />
                    </Form.Item>
                  </Col>
                )}
              </Row>
            </Card>

            {/* Tệp đính kèm - using Tabs like in StaffPage */}
            <Tabs defaultActiveKey="1" type="line" className="mt-[16px]">
              <Tabs.TabPane tab="Tệp đính kèm" key="1">
                <Form.Item
                  shouldUpdate={true}
                  style={{ marginBottom: 0, height: "100%" }}
                  className="form-height-full"
                >
                  {() => {
                    return (
                      <Form.Item
                        label={""}
                        noStyle
                        style={{ marginBottom: 0 }}
                        name="files"
                        className="h-full"
                      >
                        <FileUploadMultiple2
                          className="h-full"
                          fileList={fileList}
                          onUploadOk={(file) => {
                            fileList.push(file);
                            setFileList([...fileList]);
                          }}
                          onDelete={(file) => {
                            const findIndex = fileList.findIndex(
                              (e) => e.uid == file.uid
                            );

                            if (findIndex > -1) {
                              fileList.splice(findIndex, 1);
                              setFileList([...fileList]);
                            }
                          }}
                          hideUploadButton={readonly}
                        />
                      </Form.Item>
                    );
                  }}
                </Form.Item>
              </Tabs.TabPane>
            </Tabs>
          </Form>
          <div className="flex gap-[16px] justify-end mt-2">
            {!readonly && (
              <CustomButton
                variant="outline"
                className="cta-button"
                onClick={() => {
                  if (status == "create") {
                    navigate(
                      `/master-data/${PermissionNames.taskTemplateList}`
                    );
                  } else {
                    setReadonly(true);
                    setDataToForm(selectedTaskTemplate!);
                  }
                }}
              >
                Hủy
              </CustomButton>
            )}
            <CustomButton
              // variant="outline"
              className="cta-button"
              loading={loading}
              onClick={() => {
                if (!readonly) {
                  handleSubmit();
                } else {
                  setReadonly(false);
                }
              }}
              disabled={status == "update" && !haveEditPermission}
            >
              {status == "create"
                ? "Tạo công việc mẫu"
                : readonly
                ? "Chỉnh sửa"
                : "Lưu chỉnh sửa"}
            </CustomButton>
          </div>
        </Card>
      </div>
    );
  }
);
