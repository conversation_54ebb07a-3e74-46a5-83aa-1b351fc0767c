import React, { useMemo, useRef } from "react";
import { Tooltip, Button, Form, Input, Select } from "antd";
import { useTheme } from "context/ThemeContext";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import { Space } from "antd/lib";
import DeleteIcon from "assets/svgs/DeleteIcon";
import { PlusIcon } from "assets/svgs/PlusIcon";
import {
  DependencyType,
  TaskTemplate,
  TaskTemplateForm,
  TaskType,
} from "types/taskTemplate";
import { FormInstance, useWatch } from "antd/es/form/Form";
import { ChildTaskTemplateModal } from "./ChildTaskTemplateModal";

export type ChildTaskTemplateTableViewProps = {
  form: FormInstance<TaskTemplateForm>;
};

const ChildTaskTemplateTableView: React.FC<ChildTaskTemplateTableViewProps> = (
  props
) => {
  const { form } = props;
  const formListKey = "taskTemplateDetails";
  const taskTemplateDetails = useWatch(formListKey, form);

  const type = useWatch("type", form);
  const childTaskTemplateModalRef = useRef<ChildTaskTemplateModal>(null);
  const formListRemove = useRef<(index: number | number[]) => void>();
  const { darkMode } = useTheme();

  const excludeIds = useMemo(() => {
    const id = form.getFieldValue("id");
    const { parents } = form.getFieldsValue(true);

    if (id) {
      const ids = (parents || []).map((item: any) => item.id);
      return [...ids, id];
    }
    return [];
  }, [form.getFieldValue("id")]);

  const columns: CustomizableColumn<any>[] = [
    {
      key: "name",
      title: "Công việc",
      dataIndex: "name",
      defaultVisible: true,
      width: "auto",
      render: (_, record) => (
        <Form.Item noStyle shouldUpdate={true}>
          {({ getFieldValue }) => (
            <div>{getFieldValue([formListKey, record.name, "task"])?.name}</div>
          )}
        </Form.Item>
      ),
    },
    {
      key: "totalChildren",
      title: "Công việc con",
      dataIndex: "totalChildren",
      render: (_, record) => (
        <Form.Item noStyle shouldUpdate={true}>
          {({ getFieldValue }) => (
            <div>
              {getFieldValue([formListKey, record.name, "task"])?.totalChildren}{" "}
              công việc con
            </div>
          )}
        </Form.Item>
      ),
      defaultVisible: true,
    },
    {
      key: "dependentId",
      title: "Công việc phụ thuộc",
      dataIndex: "dependentId",
      render: (_, record, index) => {
        const taskId = (form as any).getFieldValue([
          formListKey,
          record.name,
          "taskId",
        ]);
        return index != 0 ? (
          <div style={{ display: "flex", flex: 1 }}>
            <Form.Item name={[record.name, "dependentId"]} style={{ flex: 1 }}>
              <Select
                options={(taskTemplateDetails || [])
                  .filter((item) => item.taskId != taskId)
                  .map((item) => ({
                    label: item.task?.name,
                    value: item.task?.id,
                  }))}
                placeholder={"Chọn công việc phụ thuộc"}
              />
            </Form.Item>
          </div>
        ) : (
          "-"
        );
      },
      defaultVisible: true,
    },
    {
      key: "dependencyType",
      title: "Loại phụ thuộc",
      dataIndex: "dependencyType",
      render: (_, record, index) =>
        index != 0 ? (
          <div style={{ display: "flex", flex: 1 }}>
            <Form.Item
              name={[record.name, "dependencyType"]}
              style={{ flex: 1 }}
            >
              <Select
                options={Object.values(DependencyType).map((item) => ({
                  label: item,
                  value: item,
                }))}
                placeholder={"Chọn loại phụ thuộc"}
              />
            </Form.Item>
          </div>
        ) : (
          "-"
        ),
      defaultVisible: true,
    },
    {
      key: "delay",
      title: "Delay",
      dataIndex: "delay",
      render: (_, record, index) =>
        index != 0 ? (
          <div style={{ display: "flex", flex: 1 }}>
            <Form.Item name={[record.name, "delay"]} style={{ flex: 1 }}>
              <Input placeholder="Nhập thời gian delay (vd: 1 ngày)" />
            </Form.Item>
          </div>
        ) : (
          "-"
        ),
      defaultVisible: true,
    },
    {
      key: "actions",
      title: (
        <Button
          type="text"
          icon={<PlusIcon size={24} />}
          onClick={() => {
            const { taskTemplateDetails = [] } = form.getFieldsValue();
            childTaskTemplateModalRef.current?.handleView(
              taskTemplateDetails.map((e) => ({ ...e.task! }))
            );
          }}
        />
      ),
      align: "center",
      width: 50,
      fixed: "right",
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="Xóa">
            <Button
              type="text"
              icon={<DeleteIcon />}
              onClick={() => formListRemove.current?.(record.name)}
            />
          </Tooltip>
        </Space>
      ),
      defaultVisible: true,
      alwaysVisible: true,
    },
  ];

  const handleAddTable = (selectedRows: TaskTemplate[]) => {
    const { taskTemplateDetails = [] } = form.getFieldsValue();
    const newTaskTemplateDetails = selectedRows.map((e) => {
      const findTemplateDetail = taskTemplateDetails.find(
        (resource) => resource.task?.id == e.id
      );
      return {
        delay: findTemplateDetail?.delay || "",
        dependencyType: findTemplateDetail?.dependencyType || "",
        task: e,
        taskId: e.id,
        dependentId: findTemplateDetail?.dependent?.id,
        dependent: findTemplateDetail?.dependent,
        id: findTemplateDetail?.id,
      };
    });
    form.setFieldsValue({
      taskTemplateDetails: newTaskTemplateDetails,
    });
  };

  return (
    <>
      <Form.List name={formListKey}>
        {(fields, { remove }) => {
          formListRemove.current = remove;
          return (
            <>
              <CustomizableTable
                columns={columns}
                dataSource={fields}
                rowKey="id"
                pagination={false}
                // scroll={{ x: 1200 }}
                bordered
                className="required-resource-table"
              />
            </>
          );
        }}
      </Form.List>

      <ChildTaskTemplateModal
        ref={childTaskTemplateModalRef}
        onSubmitOk={handleAddTable}
        excludeIds={excludeIds}
        type={type || TaskType.Construction}
      />
    </>
  );
};

export default ChildTaskTemplateTableView;
