import React, { ReactNode, useRef, useState } from "react";
import { Form, Tooltip, Button, Input } from "antd";
import { useTheme } from "context/ThemeContext";
import { ReactComponent as SearchIcon } from "assets/svgs/search.svg";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import { Space } from "antd/lib";
import DeleteIcon from "assets/svgs/DeleteIcon";
import { PlusIcon } from "assets/svgs/PlusIcon";
import { TaskTemplateForm } from "types/taskTemplate";
import { FormInstance, useWatch } from "antd/es/form/Form";
import {
  RequiredResourceModal,
  TabRequiredResource,
} from "./RequiredResourceModal";
import { Device } from "types/device";
import { InputNumber } from "components/Input/InputNumber";
import { RequiredResource } from "types/requiredResource";
import { Rule } from "antd/es/form";
import "../styles/TaskTemplateStyle.scss";
import { StoreValue } from "antd/es/form/interface";

export type RequiredResourceTableViewProps = {
  form: FormInstance<TaskTemplateForm>;
  type: TabRequiredResource;
};

const RequiredResourceTableView: React.FC<RequiredResourceTableViewProps> = (
  props
) => {
  const { form, type } = props;
  const formListKey = type == "device" ? "requiredDevices" : "requiredMachines";
  const requiredResourceModalRef = useRef<RequiredResourceModal>(null);
  const formListRemove = useRef<(index: number | number[]) => void>();
  const { darkMode } = useTheme();

  const quantityRule: Rule[] = [
    {
      // message: "Vui lòng nhập số lượng tối thiểu là 1!",
      validator(rule, value, callback) {
        const numberValue = Number(value);

        // Check if it's a decimal number
        if (numberValue % 1 !== 0) {
          return callback("Vui lòng nhập số nguyên dương!");
        }

        const isValid = numberValue > 0;

        // Check if it's a positive number
        if (isValid) {
          return Promise.resolve();
        }
        return callback("Vui lòng nhập số lượng tối thiểu là 1!");
      },
    },
  ];

  const columns: CustomizableColumn<any>[] = [
    {
      key: "code",
      title: "Mã",
      dataIndex: "code",
      width: 140,
      defaultVisible: true,
      render: (_, record) => (
        <Form.Item noStyle shouldUpdate={true}>
          {({ getFieldValue }) => (
            <div>
              {getFieldValue([formListKey, record.name, "device"])?.code}
            </div>
          )}
        </Form.Item>
      ),
    },
    {
      key: "name",
      title: "Tên",
      dataIndex: "name",
      defaultVisible: true,
      render: (_, record) => (
        <Form.Item noStyle shouldUpdate={true}>
          {({ getFieldValue }) => (
            <div>
              {getFieldValue([formListKey, record.name, "device"])?.name}
            </div>
          )}
        </Form.Item>
      ),
    },
    {
      key: "unit",
      title: "Đơn vị tính",
      dataIndex: "unit",
      render: (_, record) => (
        <Form.Item noStyle shouldUpdate={true}>
          {({ getFieldValue }) => (
            <div>{getFieldValue([formListKey, record.name, "unit"])?.name}</div>
          )}
        </Form.Item>
      ),
      defaultVisible: true,
    },
    {
      key: "quantity",
      title: "Số lượng",
      dataIndex: "quantity",
      render: (_, record) => (
        <div style={{ display: "flex", flex: 1 }}>
          <Form.Item name={[record.name, "quantity"]} rules={quantityRule}>
            <InputNumber />
          </Form.Item>
        </div>
      ),
      defaultVisible: true,
    },
    {
      key: "actions",
      title: (
        <Button
          type="text"
          icon={<PlusIcon size={24} />}
          onClick={() => {
            const { requiredDevices = [], requiredMachines = [] } =
              form.getFieldsValue();
            requiredResourceModalRef.current?.handleView(
              props.type,
              requiredDevices.map((e) => ({ ...e.device!, unit: e.unit! })),
              requiredMachines.map((e) => ({ ...e.device!, unit: e.unit! }))
            );
          }}
        />
      ),
      align: "center",
      width: 50,
      fixed: "right",
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="Xóa">
            <Button
              type="text"
              icon={<DeleteIcon />}
              onClick={() => formListRemove.current?.(record.name)}
            />
          </Tooltip>
        </Space>
      ),
      defaultVisible: true,
      alwaysVisible: true,
    },
  ];

  const transformData = (data: Device[], resources: RequiredResource[]) => {
    return data.map((e) => {
      const findResource = resources.find(
        (resource) => resource.device?.id == e.id
      );
      return {
        quantity: findResource?.quantity || 1,
        deviceId: e.id,
        device: e,
        unitId: e.unit?.id,
        unit: e.unit,
        id: findResource?.id,
      };
    });
  };

  const handleAddTable = (
    selectedRowDevice: Device[],
    selectedRowMachine: Device[]
  ) => {
    const { requiredDevices = [], requiredMachines = [] } =
      form.getFieldsValue();

    const newRequiredDevices = transformData(
      selectedRowDevice,
      requiredDevices as unknown as RequiredResource[]
    );
    const newRequiredMachines = transformData(
      selectedRowMachine,
      requiredMachines as unknown as RequiredResource[]
    );
    form.setFieldsValue({
      requiredDevices: newRequiredDevices,
      requiredMachines: newRequiredMachines,
    });
  };

  return (
    <>
      <Form.List name={formListKey}>
        {(fields, { remove }) => {
          formListRemove.current = remove;
          return (
            <>
              <CustomizableTable
                columns={columns}
                dataSource={fields}
                rowKey="deviceId"
                pagination={false}
                scroll={{ x: 1200 }}
                bordered
                className="required-resource-table"
              />
            </>
          );
        }}
      </Form.List>

      <RequiredResourceModal
        ref={requiredResourceModalRef}
        onSubmitOk={handleAddTable}
      />
    </>
  );
};

export default RequiredResourceTableView;
