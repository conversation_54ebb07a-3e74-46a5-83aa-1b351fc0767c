import { AxiosPromise } from "axios";
import { request } from "utils/request";

export const bomApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/bom",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/bom",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/bom/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/bom/${id}`,
      method: "delete",
    }),
  import: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/bom/import`,
      method: "post",
      data,
    }),
};
