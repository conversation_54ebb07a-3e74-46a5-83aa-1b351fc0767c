import { Card, Spin, Button, Space, Tooltip, Modal, Tag, Tabs } from "antd";
import CustomButton from "components/Button/CustomButton";
import PageTitle from "components/PageTitle/PageTitle";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import { Pagination } from "components/Pagination";
import { useTheme } from "context/ThemeContext";
import React, { useEffect, useState } from "react";
import { QueryParam } from "types/query";
import { useNavigate } from "react-router-dom";
import { PermissionNames } from "types/PermissionNames";
import { useInstruction } from "hooks/useInstruction";
import {
  Instruction,
  ProgressInstructionStatus,
  InstructionType,
  InstructionTypeTrans,
} from "types/instruction";
import { LockOutlined, UnlockOutlined } from "@ant-design/icons";
import { checkRole, checkRoles } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { observer } from "mobx-react";
import { BMDImage } from "components/Image/BMDImage";
import { $url } from "utils/url";
import logoImage from "assets/images/logo.png";
import { Staff } from "types/staff";
import TaskTable from "./TaskTable";
import "./TaskPage.scss";
import { userStore } from "store/userStore";
import { TaskSummary } from "./components/TaskSummary";
import { getTitle } from "utils";
import { InstructionStatus } from "types/instruction";
import TaskFilter from "./components/TaskFilter";

function TaskPage({ title }: { title: string }) {
  const { haveAddPermission, haveBlockPermission, haveEditPermission } =
    checkRoles(
      {
        add: PermissionNames.taskAdd,
        edit: PermissionNames.taskEdit,
        block: PermissionNames.taskBlock,
      },
      permissionStore.permissions
    );

  // State để quản lý tab hiện tại
  const [activeTab, setActiveTab] = useState<"assign" | "create">("assign");

  // Query states for each tab
  const [assignTaskQuery, setAssignTaskQuery] = useState({
    limit: 0,
    page: 1,
    search: "",
    status: "",
    dueAt: undefined as string | undefined,
    priority: undefined as string | undefined,
    projectId: undefined as number | undefined,
  });

  const [createTaskQuery, setCreateTaskQuery] = useState({
    limit: 0,
    page: 1,
    search: "",
    status: "",
    dueAt: undefined as string | undefined,
    priority: undefined as string | undefined,
    projectId: undefined as number | undefined,
  });

  useEffect(() => {
    document.title = getTitle(title);
  }, []);

  const { darkMode } = useTheme();
  const navigate = useNavigate();

  const handleCreateTask = () => {
    navigate(`/task/${PermissionNames.taskAdd}`);
  };

  // Handler khi chuyển tab
  const handleTabChange = (tabKey: string) => {
    const newTabType =
      tabKey === InstructionType.Directive ? "assign" : "create";
    setActiveTab(newTabType);
  };

  return (
    <div className="app-container">
      <PageTitle
        title="Quản lý công việc"
        breadcrumbs={[
          {
            label: "Quản lý công việc",
          },
          {
            label: "Danh sách công việc",
          },
        ]}
        extra={
          <Space>
            {haveAddPermission && (
              <CustomButton
                size="small"
                showPlusIcon
                onClick={handleCreateTask}
              >
                Tạo công việc
              </CustomButton>
            )}
          </Space>
        }
      />

      <Card>
        <div className="pb-[16px]">
          <Tabs
            type="line"
            className="role-tabs"
            defaultActiveKey={InstructionType.Directive}
            onChange={handleTabChange}
          >
            <Tabs.TabPane
              tab="Công việc của tôi"
              key={InstructionType.Directive}
            >
              {/* Thống kê tổng quan */}
              <TaskSummary type="assign" />

              {/* Filter Component */}
              <TaskFilter
                type="assign"
                onQueryChange={setAssignTaskQuery}
                initialQuery={assignTaskQuery}
              />

              {/* bảng */}
              <TaskTable
                key={`assign-${activeTab}`}
                type="assign"
                userId={userStore.info.memberShip?.id!}
                query={assignTaskQuery}
                onQueryChange={setAssignTaskQuery}
              />
            </Tabs.TabPane>

            <Tabs.TabPane tab="Công việc tôi giao" key={InstructionType.Report}>
              {/* Thống kê tổng quan */}
              <TaskSummary type="department" />

              {/* Filter Component */}
              <TaskFilter
                type="create"
                onQueryChange={setCreateTaskQuery}
                initialQuery={createTaskQuery}
              />

              {/* bảng */}
              <TaskTable
                key={`create-${activeTab}`}
                type="create"
                userId={userStore.info.id!}
                query={createTaskQuery}
                onQueryChange={setCreateTaskQuery}
              />
            </Tabs.TabPane>
          </Tabs>
        </div>
      </Card>
    </div>
  );
}

export default observer(TaskPage);
