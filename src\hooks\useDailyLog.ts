import { dailyLogApi } from "api/dailyLog.api";
import { useState } from "react";
import { DailyLog } from "types/dailyLog";
import { QueryParam } from "types/query";

export interface DailyLogQuery extends QueryParam {}

interface UseDailyLogProps {
  initQuery: DailyLogQuery;
}

export const useDailyLog = ({ initQuery }: UseDailyLogProps) => {
  const [data, setData] = useState<DailyLog[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<DailyLogQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async (query: any) => {
    setLoading(true);
    try {
      const { data } = await dailyLogApi.findAll(query);

      setData(data.dailyLogs);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return { dailyLogs: data, total, fetchData, loading, setQuery, query };
};
