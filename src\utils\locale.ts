import { Locale } from "antd/es/locale";
import viVN from "antd/es/locale/vi_VN";
import dayjs from "dayjs";
import "dayjs/locale/vi";

dayjs.locale("vi");

export const viVNLocale: Locale = {
  ...viVN,
  DatePicker: {
    ...viVN.DatePicker,
    // @ts-ignore
    lang: {
      ...(viVN.DatePicker?.lang ?? {}),
      placeholder: "Chọn thời điểm",
      yearPlaceholder: "<PERSON>ọn năm",
      quarterPlaceholder: "<PERSON>ọn quý",
      monthPlaceholder: "<PERSON>ọn tháng",
      weekPlaceholder: "Chọn tuần",
      rangePlaceholder: ["<PERSON><PERSON><PERSON> bắt đầu", "<PERSON><PERSON><PERSON> kết thúc"],
      rangeYearPlaceholder: ["<PERSON>ăm bắt đầu", "<PERSON>ă<PERSON> kết thúc"],
      rangeQuarterPlaceholder: ["<PERSON><PERSON><PERSON> bắt đầu", "<PERSON><PERSON><PERSON> kết thúc"],
      rangeMonthPlaceholder: ["Tháng bắt đầu", "Tháng kết thúc"],
      rangeWeekPlaceholder: ["Tuần bắt đầu", "Tuần kết thúc"],
      shortWeekDays: ["CN", "Hai", "Ba", "Tư", "Năm", "Sáu", "Bảy"],
      shortMonths: [
        "Th1",
        "Th2",
        "Th3",
        "Th4",
        "Th5",
        "Th6",
        "Th7",
        "Th8",
        "Th9",
        "Th10",
        "Th11",
        "Th12",
      ],
    },
  },
};
