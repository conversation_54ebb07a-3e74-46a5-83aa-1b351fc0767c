import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const brandApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/brand",
      params,
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/brand/${id}`,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/brand",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/brand/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/brand/${id}`,
      method: "delete",
    }),
};
