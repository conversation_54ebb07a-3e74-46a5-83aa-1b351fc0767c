import { accountApi } from "api/account.api";
import { useState } from "react";
import { Account } from "types/account";
import { QueryParam } from "types/query";

export interface AccountQuery extends QueryParam { }

interface UseAccountProps {
  initQuery: AccountQuery;
}

export const useAccount = ({ initQuery }: UseAccountProps) => {
  const [data, setData] = useState<Account[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<AccountQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await accountApi.findAll(query);

      setData(data.accounts);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return {
    Accounts: data,
    totalAccount: total,
    fetchAccount: fetchData,
    loadingAccount: loading,
    setQueryAccount: setQuery,
    queryAccount: query,
  };
};
