import { scheduleApi } from "api/schedule.api";
import { useState } from "react";
import { Schedule } from "types/schedule";
import { QueryParam } from "types/query";

export interface ScheduleQuery extends QueryParam {}

interface UseScheduleProps {
  initQuery: ScheduleQuery;
}

export const useSchedule = ({ initQuery }: UseScheduleProps) => {
  const [data, setData] = useState<Schedule[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<ScheduleQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async (newQuery?: ScheduleQuery) => {
    setLoading(true);
    try {
      const queryToUse = newQuery || query;
      const { data } = await scheduleApi.findAll(queryToUse);

      setData(data.schedules);
      setTotal(data.total);

      // Cập nhật query state nếu có query mới
      if (newQuery) {
        setQuery(newQuery);
      }
      return data.schedules;
    } finally {
      setLoading(false);
    }
  };

  const fetchDataFlat = async (newQuery?: ScheduleQuery) => {
    setLoading(true);
    try {
      const queryToUse = newQuery || query;
      const { data } = await scheduleApi.findAllSelect(queryToUse);

      setData(data.schedules);
      setTotal(data.total);

      // Cập nhật query state nếu có query mới
      if (newQuery) {
        setQuery(newQuery);
      }
    } finally {
      setLoading(false);
    }
  };

  return {
    schedules: data,
    setSchedule: setData,
    totalSchedule: total,
    fetchSchedule: fetchData,
    fetchScheduleFlat: fetchDataFlat,
    loadingSchedule: loading,
    setQuerySchedule: setQuery,
    querySchedule: query,
  };
};
