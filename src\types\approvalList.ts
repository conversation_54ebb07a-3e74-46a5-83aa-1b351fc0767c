import { ApprovalListDetail } from "./approvalListDetail";
import { BOQ } from "./boq";
import { ChangeEvent } from "./changeEvent";
import { Draw } from "./draw";
import { Instruction } from "./instruction";
import { MemberShip } from "./memberShip";
import { Report } from "./report";
import { RFI } from "./rfi";
import { Staff } from "./staff";
import { Task } from "./task";

export enum ApprovalListType {
  Task = "TASK", // Công việc
  Instruction = "INSTRUCTION", // Yêu cầu
  RFI = "RFI", // Yêu cầu
  Draw = "DRAW", // Vẽ
  ChangeEvent = "CHANGE_EVENT", //
  BOQ = "BOQ", // BOQ
  Report = "REPORT", // Báo cáo
}

export enum ApprovalListStatus {
  Pending = "PENDING",
  Approved = "APPROVED",
  Rejected = "REJECTED",
}

export interface ApprovalList {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  name: string;
  type: ApprovalListType;
  position: number;
  note: string;
  action: string; // thao tác
  actionText: string; // thao tác
  statusText: string;
  statusColor: string;
  status: ApprovalListStatus;
  version: number;
  approveAt: number;
  rejectAt: number;
  isAllApproval: boolean; // tất cả điều phải duyệt
  staff: Staff;
  staff2: Staff; // người duyệt 2
  memberShip: MemberShip;
  memberShip2: MemberShip; // người duyệt 2
  memberShips: MemberShip[];
  approver: Staff; // người xác nhận duyệt
  task: Task | null;
  instruction: Instruction | null;
  rfi: RFI;
  draw: Draw;
  changeEvent: ChangeEvent;
  boq: BOQ;
  approvalListDetails: ApprovalListDetail[];
}
