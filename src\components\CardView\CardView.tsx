import { Collapse, Divider } from "antd";
import { ReactComponent as ArrowIcon } from "assets/svgs/arrowUp.svg";
import calendarIcon from "assets/svgs/calendar.svg";
import camera360Icon from "assets/svgs/camera360.svg";
import cameraTimeLapseIcon from "assets/svgs/cameraTimeLapse.svg";
import manageByIcon from "assets/svgs/manageBy.svg";
import clsx from "clsx";
import ReactECharts from "echarts-for-react";
import { settings } from "settings";
import "./CardViewStyle.scss";
import { formatNumber, formatVND } from "utils";
import { EChartsOption } from "echarts";
import { useTheme } from "context/ThemeContext";
import { ColorThemes } from "utils/theme";
import { Project, ProjectStatusTrans } from "types/project";
import { useNavigate } from "react-router-dom";
import { useProject } from "hooks/useProject";
import Camera360Icon from "assets/svgs/Camera360";
import CameraTimeLapseIcon from "assets/svgs/CameratTimeLapse";
import CalendarIcon from "assets/svgs/CalendarIcon";
import ManageByIcon from "assets/svgs/ManageBy";
import { ProjectStatusTag } from "components/ProjectStatusTag/ProjectStatusTag";
import { formatDate, formatDateTime } from "utils/date";
import { $url } from "utils/url";
import { useMemo } from "react";

interface CardViewProps {
  project: Project;
  onDetail?: (project: Project) => void;
  compactMode?: boolean;
}

const CardView = ({ project, onDetail, compactMode }: CardViewProps) => {
  const statusInfo =
    ProjectStatusTrans[project.status as keyof typeof ProjectStatusTrans];

  const rings = [
    {
      value: 205,
      total: 300,
      name: "Công việc",
      color: ["#4F46E5", "#7C3AED"],
    },
    {
      value: 150,
      total: 200,
      name: "Hoàn thành",
      color: ["#10B981", "#059669"],
    },
    { value: 75, total: 100, name: "Đang chờ", color: ["#F59E0B", "#D97706"] },
  ];

  const { darkMode } = useTheme();

  const { avatar, isHaveAvatar } = useMemo(() => {
    if (!!project.avatar) {
      return { avatar: $url(project.avatar), isHaveAvatar: true };
    }
    return {
      avatar: darkMode ? settings.logoWhite : settings.logo,
      isHaveAvatar: false,
    };
  }, [project.avatar, darkMode]);

  const platformOptions: EChartsOption = {
    series: [
      {
        type: "pie",
        radius: ["85%", "100%"],
        center: ["50%", "50%"],
        startAngle: 90,
        data: [
          {
            value: 82,
            itemStyle: {
              color: darkMode
                ? ColorThemes.dark.task.normal
                : ColorThemes.light.task.normal,
            },
          },
          {
            value: 18,
            itemStyle: {
              color: darkMode
                ? ColorThemes.dark.neutral.n1
                : ColorThemes.light.neutral.n1,
            },
          },
        ],
        label: {
          show: false,
        },
        labelLine: {
          show: false,
        },
      },
      {
        type: "pie",
        radius: ["67%", "80%"],
        center: ["50%", "50%"],
        startAngle: 90,
        data: [
          {
            value: 12,
            itemStyle: {
              color: darkMode
                ? ColorThemes.dark.task.speedup
                : ColorThemes.light.task.speedup,
            },
          },
          {
            value: 88,
            itemStyle: {
              color: darkMode
                ? ColorThemes.dark.neutral.n1
                : ColorThemes.light.neutral.n1,
            },
          },
        ],
        label: {
          show: false,
        },
        labelLine: {
          show: false,
        },
      },
    ],
    graphic: [
      {
        type: "text",
        style: {
          text: "205",
          fontSize: 20,
          fontWeight: "bold",
          fill: darkMode
            ? ColorThemes.dark.neutral.n8
            : ColorThemes.light.neutral.n8,
          y: 32,
          x: 32,
        },
      },
      {
        type: "text",
        style: {
          text: "Công việc",
          fontSize: 12,
          fontWeight: "lighter",
          fill: darkMode
            ? ColorThemes.dark.neutral.n8
            : ColorThemes.light.neutral.n8,
          y: 57,
          x: 22,
        },
      },
    ],
  };

  if (compactMode) {
    return (
      <div
        className="card-view !p-1"
        onClick={() => {
          onDetail?.(project);
        }}
      >
        <div className="flex gap-3 items-center">
          <div className="relative w-[60px] h-[60px] flex-shrink-0 cursor-pointer hover:opacity-80 transition-opacity">
            <img
              src={avatar}
              className="w-full h-full object-cover rounded-md"
              alt={project.name}
            />
          </div>
          <div>
            <div className="font-bold text-base">{project.name}</div>
            <div className="text-[10px]">{project.code}</div>
            <div className="flex items-center gap-1 text-xs">
              <CalendarIcon />
              <div>Ngày tạo: {formatDate(project.createdAt)}</div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="card-view">
      <div className="flex justify-between gap-2">
        <div className="flex flex-col md:flex-row gap-4 w-full">
          <div
            className="relative w-full md:w-[120px] h-[120px] flex-shrink-0 cursor-pointer hover:opacity-80 transition-opacity"
            onClick={() => {
              onDetail?.(project);
            }}
          >
            <img
              src={avatar}
              className={clsx(
                "w-full h-full rounded-md",
                isHaveAvatar ? "object-cover" : "object-contain"
              )}
              alt={project.name}
            />
          </div>
          <div className="w-full">
            <div className="flex justify-between gap-2">
              <div className="flex items-center gap-2">
                <div
                  className="cursor-pointer hover:text-blue-600 transition-colors"
                  onClick={() => {
                    onDetail?.(project);
                  }}
                >
                  {project.code}
                </div>
                {!!project.vr360 && (
                  <div
                    className="cursor-pointer"
                    onClick={() => {
                      window.open(project.vr360, "_blank");
                    }}
                  >
                    <Camera360Icon />
                  </div>
                )}
                {!!project.siteCamera && (
                  <div
                    className="cursor-pointer"
                    onClick={() => {
                      window.open(project.siteCamera, "_blank");
                    }}
                  >
                    <CameraTimeLapseIcon />
                  </div>
                )}
              </div>

              <div className="flex gap-2 justify-end">
                <div className="task-status">
                  <div
                    className="absolute left-0 top-0 w-full h-full opacity-15 rounded-[5px]"
                    style={{ backgroundColor: `var(${statusInfo.color})` }}
                  ></div>
                  <div
                    className="task-status-label"
                    style={{ color: `var(${statusInfo.color})` }}
                  >
                    {statusInfo.label}
                  </div>
                </div>
              </div>
            </div>
            <div
              className="font-bold text-[20px] md:mt-2 cursor-pointer hover:text-blue-600 transition-colors"
              onClick={() => {
                onDetail?.(project);
              }}
            >
              {project.name}
            </div>
            {/* <div className="flex gap-x-4 gap-y-2 flex-wrap mt-1">
              <div className="flex items-center gap-1">
                <CalendarIcon />
                <div>Ngày bắt đầu: {formatDate(project.startAt)}</div>
              </div>
              <div className="flex items-center gap-1">
                <CalendarIcon />
                <div>Ngày kết thúc: {formatDate(project.endAt)}</div>
              </div>
              <div className="flex items-center gap-1">
                <ManageByIcon />
                <div>
                  Chủ đầu tư: {project.investor?.name || "Chưa xác định"}
                </div>
              </div>
            </div> */}
            <div className="header-info">
              <div className="info-item">
                <div className="info-label">Ngày bắt đầu</div>
                <div className="info-content">
                  <div className="icon-wrapper">
                    <CalendarIcon />
                  </div>
                  <div>{formatDate(project.startAt)}</div>
                </div>
              </div>
              <div className="info-item">
                <div className="info-label">Ngày kết thúc</div>
                <div className="info-content">
                  <div className="icon-wrapper">
                    <CalendarIcon />
                  </div>
                  <div>{formatDate(project.endAt)}</div>
                </div>
              </div>

              <div className="info-item">
                <div className="info-label">Chủ đầu tư</div>
                <div className="info-content">
                  <div className="icon-wrapper">
                    <ManageByIcon />
                  </div>
                  <div>{project.investor?.name || "Chưa xác định"}</div>
                </div>
              </div>
            </div>
            <div className="md:hidden flex gap-2 flex-wrap mt-2">
              <div className="font-bold">Tích hợp</div>
              <div className="flex items-center gap-1">
                <img src={camera360Icon} className="w-[20px] h-[20px]" />
                <div>Camera 360</div>
              </div>
              <div className="flex items-center gap-1">
                <img src={cameraTimeLapseIcon} className="w-[20px] h-[20px]" />
                <div>Camera timelapse</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="mt-4">
        <Collapse
          className="card-view-collapse"
          ghost
          expandIconPosition="end"
          defaultActiveKey={["1"]}
          expandIcon={({ isActive }) => {
            return (
              <ArrowIcon
                className={clsx("expand-icon", isActive ? "" : "rotate-180")}
              />
            );
          }}
          items={[
            {
              key: "1",
              label: (
                <div className="flex gap-2 items-center w-full">
                  <span className="flex-shrink-0">
                    Thống kê lúc: {formatDateTime(project.updatedAt)}
                  </span>
                  <Divider
                    className="!my-0 !min-w-0 w-max flex-1"
                    style={{ borderBlockStartColor: "var(--color-neutral-n3)" }}
                  />
                </div>
              ),
              children: (
                <div className="flex flex-col md:flex-row justify-between md:items-center gap-4 md:gap-2">
                  <div className="flex items-center gap-[20px]">
                    <ReactECharts
                      option={platformOptions}
                      style={{ width: "100px", height: "100px" }}
                      opts={{ renderer: "canvas" }}
                    />
                    <div className="flex flex-col md:flex-row gap-2 md:gap-[20px]">
                      <div className="relative w-[160px]">
                        <div className="font-bold">Tiến độ kế hoạch</div>
                        <div className="flex gap-2 items-center w-[calc(100%-30px)]">
                          <div
                            className="h-2 transition-all"
                            style={{
                              width: `${82}%`,
                              backgroundColor: "var(--color-task-normal)",
                            }}
                          ></div>
                          <div
                            className="font-bold w-[0px]"
                            style={{
                              color: "var(--color-neutral-n4)",
                            }}
                          >
                            82%
                          </div>
                        </div>
                      </div>
                      <div className="relative w-[160px]">
                        <div className="font-bold">Tiến độ thực tế</div>
                        <div className="flex gap-2 items-center w-[calc(100%-30px)]">
                          <div
                            className="h-2 transition-all"
                            style={{
                              width: `${12}%`,
                              backgroundColor: "var(--color-task-speedup)",
                            }}
                          ></div>
                          <div
                            className="font-bold w-[0px]"
                            style={{
                              color: "var(--color-neutral-n4)",
                            }}
                          >
                            12%
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="w-full md:w-[200px] flex flex-col gap-2">
                    <div className="flex gap-2 justify-between">
                      <span className="whitespace-nowrap">Ngân sách</span>
                      <span className="text-right font-bold">
                        {formatVND(project?.budget ?? 0)} đ
                      </span>
                    </div>
                    <div className="flex gap-2 justify-between">
                      <span>Thu</span>
                      <span
                        className="text-right font-bold"
                        style={{ color: "var(--color-project-done)" }}
                      >
                        --
                      </span>
                    </div>
                    <div className="flex gap-2 justify-between">
                      <span>Chi</span>
                      <span
                        className="text-right font-bold"
                        style={{ color: "var(--color-task-slow)" }}
                      >
                        --
                      </span>
                    </div>
                  </div>
                </div>
              ),
            },
          ]}
        />
      </div>
    </div>
  );
};

export default CardView;
