import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const approvalTemplateApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/approvalTemplate",
      params,
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/approvalTemplate/${id}`,
      method: "get",
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/approvalTemplate",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/approvalTemplate/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/approvalTemplate/${id}`,
      method: "delete",
    }),
};
