.task-summary-row {
  display: flex;
  gap: 16px;
}
.task-summary-box {
  background: #e5e7eb;
  // border-radius: 6px;
  padding: 8px 16px;
  min-width: 140px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}
.task-summary-label {
  font-weight: 600;
}
.task-summary-badge {
  background: #ef4444;
  color: #fff;
  border-radius: 50%;
  padding: 2px 10px;
  margin-left: 8px;
  font-weight: 700;
  font-size: 15px;
  display: inline-block;
}

.task-status-row {
  display: flex;
  gap: 16px;
}
.task-status-box {
  border-radius: 8px;
  padding: 12px;
  min-width: 120px;
  text-align: center;
  background: #f3f4f6;
}
.status-todo {
  background: #e0e7ff;
}
.status-doing {
  background: #fef3c7;
}
.status-done {
  background: #d1fae5;
}
.status-reopen {
  background: #f3f4f6;
}
.task-status-label {
  font-weight: 600;
}
.task-status-value {
  font-size: 20px;
  font-weight: 700;
}

// Department Summary Styles
.department-summary-row {
  h4 {
    color: #374151;
    margin-bottom: 8px;
    font-weight: 600;
    font-size: 16px;
  }
}

.department-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 12px;
  
  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }
}

.department-summary-box {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px;
  text-align: center;
  transition: all 0.2s ease;
  
  &:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.department-name {
  color: #4b5563;
  font-size: 13px;
  font-weight: 500;
  margin-bottom: 4px;
  line-height: 1.3;
}

.department-total {
  color: #2563eb;
  font-size: 18px;
  font-weight: 700;
}
