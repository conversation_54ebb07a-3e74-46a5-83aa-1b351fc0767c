import { departmentApi } from "api/department.api";
import { useState } from "react";
import { Department } from "types/department";
import { QueryParam } from "types/query";

export interface DepartmentQuery extends QueryParam {}

interface UseDepartmentProps {
  initQuery: DepartmentQuery;
}

export const useDepartment = ({ initQuery }: UseDepartmentProps) => {
  const [data, setData] = useState<Department[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<DepartmentQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await departmentApi.findAll(query);

      setData(data.departments);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return { departments: data, total, fetchData, loading, setQuery, query };
};
