import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const mediaApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/media",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/media",
      data,
      method: "post",
    }),

  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/media/${id}`,
      method: "delete",
    }),
};
