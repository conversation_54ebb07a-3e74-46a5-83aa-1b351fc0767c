import { PermissionType } from "types/permission";
import { PermissionNames } from "types/PermissionNames";

export const permissionForStandardRole = [
  PermissionType.Add,
  PermissionType.Edit,
  PermissionType.List,
  PermissionType.Block,
  PermissionType.Delete,
];

export const permissionDataForMenuRole = [
  {
    name: "dashboard",
    permissions: [PermissionNames.dashboardList],
  },
  {
    name: "project",
    permissions: [
      PermissionNames.projectAdd,
      PermissionNames.projectEditStatus,
      PermissionNames.projectList,
      PermissionNames.projectViewAll,
    ],
  },
  {
    name: "project-phonebook",
    permissions: [
      PermissionNames.projectPhoneBookAdd,
      PermissionNames.projectPhoneBookEdit,
      PermissionNames.projectPhoneBookList,
      PermissionNames.projectPhoneBookBlock,
      PermissionNames.projectPhoneBookViewAll,
    ],
  },
  {
    name: "indicative",
    permissions: [
      PermissionNames.indicativeAdd,
      PermissionNames.indicativeEdit,
      PermissionNames.indicativeList,
      PermissionNames.indicativeBlock,
      PermissionNames.indicativeViewAll,
    ],
  },
  {
    name: "rfis",
    permissions: [
      PermissionNames.rfisAdd,
      PermissionNames.rfisEdit,
      PermissionNames.rfisList,
      PermissionNames.rfisDelete,
      PermissionNames.rfisViewAll,
    ],
  },
  {
    name: "project-item",
    permissions: [
      PermissionNames.projectItemAdd,
      PermissionNames.projectItemEdit,
      PermissionNames.projectItemList,
      PermissionNames.projectItemDelete,
      PermissionNames.projectItemBlock,
      PermissionNames.projectItemViewAll,
    ],
  },
  {
    name: "approve-process",
    permissions: [
      PermissionNames.approveProcessAdd,
      PermissionNames.approveProcessEdit,
      PermissionNames.approveProcessList,
      PermissionNames.approveProcessDelete,
      PermissionNames.approveProcessBlock,
      PermissionNames.approveProcessViewAll,
    ],
  },
  {
    name: "project-report",
    permissions: [
      PermissionNames.projectReportAdd,
      PermissionNames.projectReportEdit,
      PermissionNames.projectReportList,
      PermissionNames.projectReportViewAll,
    ],
  },
  {
    name: "event-log",
    permissions: [
      PermissionNames.eventLogAdd,
      PermissionNames.eventLogEdit,
      PermissionNames.eventLogList,
      PermissionNames.eventLogViewAll,
    ],
  },
  {
    name: "blueprint",
    permissions: [
      PermissionNames.blueprintAdd,
      PermissionNames.blueprintEdit,
      PermissionNames.blueprintList,
      PermissionNames.blueprintDelete,
      PermissionNames.blueprintViewAll,
    ],
  },
  {
    name: "project-doc",
    permissions: [
      PermissionNames.projectDocAdd,
      PermissionNames.projectDocEdit,
      PermissionNames.projectDocList,
      PermissionNames.projectDocBlock,
      PermissionNames.projectDocViewAll,
    ],
  },
  {
    name: "images",
    permissions: [PermissionNames.imagesList, PermissionNames.imagesViewAll],
  },
  {
    name: "project-progress",
    permissions: [
      PermissionNames.projectProgressList,
      PermissionNames.projectProgressViewAll,
    ],
  },
  {
    name: "task",
    permissions: [
      PermissionNames.taskAdd,
      PermissionNames.taskEdit,
      PermissionNames.taskList,
      PermissionNames.taskDelete,
      PermissionNames.taskViewAll,
    ],
  },
  {
    name: "activity-log",
    permissions: [
      PermissionNames.activityLogList,
      PermissionNames.activityLogViewAll,
      PermissionNames.activityLogAdd,
      PermissionNames.activityLogEdit,
    ],
  },
  {
    name: "service",
    permissions: [
      PermissionNames.serviceAdd,
      PermissionNames.serviceEdit,
      PermissionNames.serviceList,
      PermissionNames.serviceBlock,
      PermissionNames.serviceViewAll,
    ],
  },
  {
    name: "account",
    permissions: [
      PermissionNames.accountAdd,
      PermissionNames.accountEdit,
      PermissionNames.accountList,
      PermissionNames.accountBlock,
      PermissionNames.accountResetPassword,
      PermissionNames.accountViewAll,
    ],
  },
  {
    name: "boq",
    permissions: [
      PermissionNames.boqAdd,
      PermissionNames.boqEdit,
      PermissionNames.boqList,
      PermissionNames.boqViewAll,
    ],
  },
  {
    name: "staff",
    permissions: [
      PermissionNames.staffAdd,
      PermissionNames.staffEdit,
      PermissionNames.staffList,
      PermissionNames.staffBlock,
      PermissionNames.staffViewAll,
    ],
  },
  {
    name: "staff-other",
    permissions: [
      PermissionNames.staffOtherAdd,
      PermissionNames.staffOtherEdit,
      PermissionNames.staffOtherList,
      PermissionNames.staffOtherBlock,
      PermissionNames.staffOtherViewAll,
    ],
  },
  {
    name: "device",
    permissions: [
      PermissionNames.deviceAdd,
      PermissionNames.deviceEdit,
      PermissionNames.deviceList,
      PermissionNames.deviceBlock,
      PermissionNames.deviceViewAll,
    ],
  },
  {
    name: "machine",
    permissions: [
      PermissionNames.machineAdd,
      PermissionNames.machineEdit,
      PermissionNames.machineList,
      PermissionNames.machineBlock,
      PermissionNames.machineViewAll,
    ],
  },
  {
    name: "provider",
    permissions: [
      PermissionNames.providerAdd,
      PermissionNames.providerEdit,
      PermissionNames.providerList,
      PermissionNames.providerBlock,
      PermissionNames.providerViewAll,
    ],
  },
  {
    name: "unit",
    permissions: [
      PermissionNames.unitAdd,
      PermissionNames.unitEdit,
      PermissionNames.unitList,
      PermissionNames.unitBlock,
      PermissionNames.unitViewAll,
    ],
  },
  {
    name: "material",
    permissions: [
      PermissionNames.materialAdd,
      PermissionNames.materialEdit,
      PermissionNames.materialList,
      PermissionNames.materialBlock,
      PermissionNames.materialViewAll,
    ],
  },
  {
    name: "goods",
    permissions: [
      PermissionNames.goodsAdd,
      PermissionNames.goodsEdit,
      PermissionNames.goodsList,
      PermissionNames.goodsBlock,
      PermissionNames.goodsViewAll,
    ],
  },
  {
    name: "project-group",
    permissions: [
      PermissionNames.projectGroupAdd,
      PermissionNames.projectGroupEdit,
      PermissionNames.projectGroupList,
      PermissionNames.projectGroupBlock,
      PermissionNames.projectGroupViewAll,
    ],
  },
  {
    name: "task-template",
    permissions: [
      PermissionNames.taskTemplateAdd,
      PermissionNames.taskTemplateEdit,
      PermissionNames.taskTemplateList,
      PermissionNames.taskTemplateBlock,
      PermissionNames.taskTemplateViewAll,
    ],
  },
  {
    name: "document",
    permissions: [
      PermissionNames.documentAdd,
      PermissionNames.documentEdit,
      PermissionNames.documentList,
      PermissionNames.documentBlock,
      PermissionNames.documentViewAll,
    ],
  },
  {
    name: "role",
    permissions: [
      PermissionNames.roleAdd,
      PermissionNames.roleEdit,
      PermissionNames.roleList,
      PermissionNames.roleDelete,
      PermissionNames.roleViewAll,
    ],
  },
  {
    name: "subcontractor",
    permissions: [
      PermissionNames.subcontractorAdd,
      PermissionNames.subcontractorEdit,
      PermissionNames.subcontractorList,
      PermissionNames.subcontractorBlock,
      PermissionNames.subcontractorViewAll,
    ],
  },
  {
    name: "service-type",
    permissions: [
      PermissionNames.serviceTypeAdd,
      PermissionNames.serviceTypeEdit,
      PermissionNames.serviceTypeList,
      PermissionNames.serviceTypeDelete,
      PermissionNames.serviceTypeViewAll,
    ],
  },
  {
    name: "job-title",
    permissions: [
      PermissionNames.jobTitleAdd,
      PermissionNames.jobTitleEdit,
      PermissionNames.jobTitleList,
      PermissionNames.jobTitleDelete,
      PermissionNames.jobTitleViewAll,
    ],
  },
  {
    name: "level",
    permissions: [
      PermissionNames.levelAdd,
      PermissionNames.levelEdit,
      PermissionNames.levelList,
      PermissionNames.levelDelete,
      PermissionNames.levelViewAll,
    ],
  },
  {
    name: "department",
    permissions: [
      PermissionNames.departmentAdd,
      PermissionNames.departmentEdit,
      PermissionNames.departmentList,
      PermissionNames.departmentDelete,
      PermissionNames.departmentViewAll,
    ],
  },
  {
    name: "brand",
    permissions: [
      PermissionNames.brandAdd,
      PermissionNames.brandEdit,
      PermissionNames.brandList,
      PermissionNames.brandDelete,
      PermissionNames.brandViewAll,
    ],
  },
  {
    name: "account-group",
    permissions: [
      PermissionNames.accountGroupAdd,
      PermissionNames.accountGroupEdit,
      PermissionNames.accountGroupList,
      PermissionNames.accountGroupDelete,
      PermissionNames.accountGroupViewAll,
    ],
  },
  {
    name: "material-group",
    permissions: [
      PermissionNames.materialGroupAdd,
      PermissionNames.materialGroupEdit,
      PermissionNames.materialGroupList,
      PermissionNames.materialGroupDelete,
      PermissionNames.materialGroupViewAll,
    ],
  },
  {
    name: "product-group",
    permissions: [
      PermissionNames.productGroupAdd,
      PermissionNames.productGroupEdit,
      PermissionNames.productGroupList,
      PermissionNames.productGroupDelete,
      PermissionNames.productGroupViewAll,
    ],
  },
  {
    name: "device-group",
    permissions: [
      PermissionNames.deviceGroupAdd,
      PermissionNames.deviceGroupEdit,
      PermissionNames.deviceGroupList,
      PermissionNames.deviceGroupDelete,
      PermissionNames.deviceGroupViewAll,
    ],
  },
  {
    name: "machine-group",
    permissions: [
      PermissionNames.machineGroupAdd,
      PermissionNames.machineGroupEdit,
      PermissionNames.machineGroupList,
      PermissionNames.machineGroupDelete,
      PermissionNames.machineGroupViewAll,
    ],
  },
  {
    name: "provider-category",
    permissions: [
      PermissionNames.providerCategoryAdd,
      PermissionNames.providerCategoryEdit,
      PermissionNames.providerCategoryList,
      PermissionNames.providerCategoryDelete,
      PermissionNames.providerCategoryViewAll,
    ],
  },
  {
    name: "country",
    permissions: [
      PermissionNames.countryAdd,
      PermissionNames.countryEdit,
      PermissionNames.countryList,
      PermissionNames.countryDelete,
      PermissionNames.countryViewAll,
    ],
  },
  {
    name: "subcontractor-category",
    permissions: [
      PermissionNames.subcontractorCategoryAdd,
      PermissionNames.subcontractorCategoryEdit,
      PermissionNames.subcontractorCategoryList,
      PermissionNames.subcontractorCategoryDelete,
      PermissionNames.subcontractorCategoryViewAll,
    ],
  },
  {
    name: "work-type",
    permissions: [
      PermissionNames.workTypeAdd,
      PermissionNames.workTypeEdit,
      PermissionNames.workTypeList,
      PermissionNames.workTypeDelete,
      PermissionNames.workTypeViewAll,
    ],
  },
  {
    name: "instruction-category",
    permissions: [
      PermissionNames.instructionCategoryAdd,
      PermissionNames.instructionCategoryEdit,
      PermissionNames.instructionCategoryList,
      PermissionNames.instructionCategoryDelete,
      PermissionNames.instructionCategoryViewAll,
    ],
  },
  {
    name: "rfi-category",
    permissions: [
      PermissionNames.rfiCategoryAdd,
      PermissionNames.rfiCategoryEdit,
      PermissionNames.rfiCategoryList,
      PermissionNames.rfiCategoryDelete,
      PermissionNames.rfiCategoryViewAll,
    ],
  },
  {
    name: "classify",
    permissions: [
      PermissionNames.classifyAdd,
      PermissionNames.classifyEdit,
      PermissionNames.classifyList,
      PermissionNames.classifyDelete,
      PermissionNames.classifyViewAll,
    ],
  },
  {
    name: "contacts",
    permissions: [
      PermissionNames.contactsAdd,
      PermissionNames.contactsEdit,
      PermissionNames.contactsList,
      PermissionNames.contactsDelete,
      PermissionNames.contactsViewAll,
    ],
  },
  {
    name: "company",
    permissions: [
      PermissionNames.companyAdd,
      PermissionNames.companyEdit,
      PermissionNames.companyList,
      PermissionNames.companyDelete,
      PermissionNames.companyViewAll,
    ],
  },
  {
    name: "boq-group",
    permissions: [
      PermissionNames.boqGroupAdd,
      PermissionNames.boqGroupEdit,
      PermissionNames.boqGroupList,
      PermissionNames.boqGroupDelete,
      PermissionNames.boqGroupViewAll,
    ],
  },
];
