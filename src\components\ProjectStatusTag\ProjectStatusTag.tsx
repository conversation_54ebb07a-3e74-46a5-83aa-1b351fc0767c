import "./ProjectStatusTag.scss";

import { Select, Tag } from "antd";
import clsx from "clsx";
import React from "react";
import { IoMdArrowDropdown } from "react-icons/io";
import { ProjectStatus, ProjectStatusTrans } from "types/project";

interface Props {
  status: ProjectStatus;
  variant?: "icon" | "select";
  className?: string;
  onChangeStatus?: (status: ProjectStatus) => void;
}

export const ProjectStatusTag = ({
  status,
  variant = "icon",
  className,
  onChangeStatus,
}: Props) => {
  return (
    <div className={clsx("project-status-tag", className)}>
      {variant == "select" ? (
        <Select
          options={Object.values(ProjectStatusTrans)}
          onChange={onChangeStatus}
          value={status}
          suffixIcon={<IoMdArrowDropdown className="text-white size-[14px]" />}
          className={clsx(`color-${status}`)}
        />
      ) : (
        <div className="flex items-center gap-1">
          <div
            className="size-[16px]"
            style={{
              backgroundColor: `var(${ProjectStatusTrans[status].color})`,
            }}
          ></div>
          <div>{ProjectStatusTrans[status].label}</div>
        </div>
      )}
    </div>
  );
};
