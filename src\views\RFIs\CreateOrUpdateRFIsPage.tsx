// This file combines Create and Edit Indicative Page into a single component
import {
  Card,
  Col,
  Row,
  Form,
  Button,
  Spin,
  Tabs,
  Select,
  message,
  Input,
  DatePicker,
} from "antd";
import PageTitle from "components/PageTitle/PageTitle";
import { useEffect, useMemo, useRef, useState } from "react";
import { Rule } from "antd/lib/form";
import CustomButton from "components/Button/CustomButton";
import { ModalStatus } from "types/modal";
import { PermissionNames } from "types/PermissionNames";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { FileAttach } from "types/fileAttach";
import { isEmpty } from "lodash";
import { getTitle } from "utils";
import { FileUploadMultiple2 } from "components/Upload/FileUploadMultiple2";
import { useWatch } from "antd/es/form/Form";
import { DictionarySelector } from "components/Selector/DictionarySelector";
import { DictionaryType } from "types/dictionary";
import { fileAttachApi } from "api/fileAttach.api";
import { $url } from "utils/url";
import dayjs from "dayjs";
import { Staff } from "types/staff";
import {
  ApprovalStepsCard,
  ApproveData,
} from "components/ApproveProcess/ApprovalStepsCard";
import { FollowerSelector } from "components/Follower/FollowerSelector";
import { rfiApi } from "api/rfi.api";
import { RFI, RFIStatus } from "types/rfi";
import TextArea from "antd/es/input/TextArea";
import { CommentView } from "../../components/Comment/CommentView";
import { settings } from "settings";
import { dictionaryApi } from "api/dictionary.api";
import clsx from "clsx";
import { observer } from "mobx-react";
import { checkEditPermissionByCreator, checkRoles } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { memberShipApi } from "api/memberShip.api";
import { ApprovalListType } from "types/approvalList";
import { approvalListApi } from "api/approvalList.api";
import { transformApproveData } from "components/ApproveProcess/approveUtil";
import { Comment } from "types/comment";
import { Popconfirm } from "antd/lib";
import { ExportPdfRFIs } from "./ExportPdfRFIs";
import { useReactToPrint } from "react-to-print";
import { ResponderSelector } from "components/Responder/ResponderSelector";
import { appStore } from "store/appStore";
import {
  ApprovalTemplateName,
  ApprovalTemplateType,
} from "types/approvalTemplate";
import { MemberShip } from "types/memberShip";
import { useApprovalStep } from "hooks/useAppovalStep";
import { userStore } from "store/userStore";
import { toJS } from "mobx";
import { MembershipSelector } from "components/Selector/MembershipSelector";
import { BMDTextArea } from "components/TextArea/BMDTextArea";
import { BMDCKEditor } from "components/Editor";

const rules: Rule[] = [{ required: true, message: "Trường này là bắt buộc" }];

const descriptionRules: Rule[] = [{ required: false }];

interface EditRFIsPageProps {
  title: string;
  status: ModalStatus;
}

interface RFIForm extends RFI {
  projectId: number;
  code: string;
  id: number;
  recipientMemberShipId?: number;
  senderMemberShipId?: number;
  senderLevelId?: number;
  sendCompanyId?: number;
  receiverLevelId?: number;
  receiverCompanyId?: number;
  responsibilityMemberShipId?: number;
  rfiCategoryId?: number;
  senderAttachments: FileAttach[];
  sendCompanyDisplay?: string;
  senderLevelDisplay?: string;
  receiverCompanyDisplay?: string;
  receiverLevelDisplay?: string;
}

function CreateOrUpdateRFIsPage({ title = "", status }: EditRFIsPageProps) {
  const { haveEditPermission, haveViewAllPermission } = checkRoles(
    {
      edit: PermissionNames.rfisEdit,
      viewAll: PermissionNames.rfisViewAll,
    },
    permissionStore.permissions
  );

  const [form] = Form.useForm<RFIForm>();
  const [loading, setLoading] = useState(false);
  const [selectedRFI, setSelectedRFI] = useState<RFI>();
  const [fileList, setFileList] = useState<FileAttach[]>([]);
  const navigate = useNavigate();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [responders, setResponders] = useState<MemberShip[]>([]);
  const [rfiCategories, setRfiCategories] = useState<any[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(false);
  const [memberShips, setMemberShips] = useState<any[]>([]);
  const [loadingMemberShips, setLoadingMemberShips] = useState(false);
  const [removeApprovalList, setRemoveApprovalList] = useState<number[]>([]);
  const [loadingApprove, setLoadingApprove] = useState(false);
  const [commentRefreshTrigger, setCommentRefreshTrigger] = useState(0);

  const {
    followers,
    setFollowers,
    approvalSteps,
    setApprovalSteps,
    fetchApprovalTemplate,
  } = useApprovalStep();

  const handleAddFollowers = () => {
    setIsModalVisible(true);
  };

  const [readonly, setReadonly] = useState(true);
  const [loadingFetch, setLoadingFetch] = useState(false);
  const params = useParams();
  const [searchParams, setSearchParams] = useSearchParams();
  const type = useWatch("type", form);
  const [senderInfo, setSenderInfo] = useState<any>(null);
  const [receiverInfo, setReceiverInfo] = useState<any>(null);
  const [commentConfirm, setCommentConfirm] = useState<null | Comment>(null);
  const [showDownload, setShowDownload] = useState(false);
  const printRef = useRef<HTMLDivElement>(null);
  const [requestInfo, setRequestInfo] = useState<string>("");

  // Dùng cách mới với contentRef
  const handlePrint = useReactToPrint({
    contentRef: printRef,
    documentTitle: "RFIs.pdf",
  });

  useEffect(() => {
    document.title = getTitle(title);

    if (status === "create") {
      setReadonly(false);
      form.setFieldsValue({
        status: RFIStatus.Draft,
        senderMemberShipId: userStore?.info?.memberShip?.id,
        sendCompanyDisplay: userStore?.info?.company?.name,
        senderLevelDisplay: userStore?.info?.jobTitle?.name,
      });

      setSenderInfo(userStore.info.company);

      if (!appStore.currentProject) {
        return;
      }

      fetchApprovalTemplate({
        projectId: appStore.currentProject.id,
        createdStaff: toJS(userStore.info) as Staff,
        type: ApprovalTemplateType.RFI,
      });
    }

    if (status == "update") {
      const rfiId = params.id;
      if (rfiId) {
        getOneRFI(+rfiId);
        setReadonly(searchParams.get("update") != "1");
      }
    } else {
      setReadonly(false);
    }

    fetchRfiCategories();

    const projectId = appStore.currentProject?.id;
    console.log("projectId lấy từ localStorage:", projectId);

    fetchMemberShips(projectId);
  }, [haveEditPermission]);

  useEffect(() => {
    if (memberShips.length > 0 && selectedRFI) {
      setDataToForm(selectedRFI);
    }
  }, [memberShips, selectedRFI]);

  const fetchMemberShips = async (projectId?: number) => {
    try {
      setLoadingMemberShips(true);
      const { data } = await memberShipApi.findAll({
        isActive: true,
        ...(projectId ? { projectId } : {}),
      });
      setMemberShips(data?.memberShips || []);
    } catch (error) {
      console.error("Error fetching memberShips:", error);
      message.error("Không thể tải danh sách thành viên");
    } finally {
      setLoadingMemberShips(false);
    }
  };

  const getMemberShipInfo = (membershipId: number) => {
    return memberShips.find((membership) => membership.id === membershipId);
  };

  const setDataToForm = async (data: RFI) => {
    const senderMembership = data.senderMemberShip
      ? data.senderMemberShip
      : memberShips.find((m) => m.staff?.id === data.sender?.id);

    const recipientMembership = data.recipientMemberShip
      ? data.recipientMemberShip
      : memberShips.find((m) => m.staff?.id === data.recipient?.id);

    const responsibilityMembership = data.responsibilityMemberShip
      ? data.responsibilityMemberShip
      : memberShips.find((m) => m.staff?.id === data.responsibility?.id);

    form.setFieldsValue({
      ...data,
      projectId: data.project?.id,
      senderMemberShipId: data.senderMemberShip?.id, // membership.id
      recipientMemberShipId: data.recipientMemberShip?.id, // membership.id
      responsibilityMemberShipId: data.responsibilityMemberShip?.id, // membership.id
      sendCompanyId: data.sendCompany?.id || 0,
      receiverCompanyId: data.receiverCompany?.id || 0,
      senderLevelId: data.senderLevel?.id || 0,
      receiverLevelId: data.receiverLevel?.id || 0,
      sendCompanyDisplay: data.sendCompany?.name || "",
      receiverCompanyDisplay: data.receiverCompany?.name || "",
      senderLevelDisplay: data.senderLevel?.name || "",
      receiverLevelDisplay: data.receiverLevel?.name || "",
      rfiCategoryId: data.rfiCategory?.id,
      //@ts-ignore
      dueAt: data.dueAt ? dayjs.unix(data.dueAt) : undefined,
      //@ts-ignore
      requestDate: data?.requestDate
        ? dayjs(data?.requestDate, "YYYY-MM-DD")
        : undefined,
    });

    // Load thông tin sender và receiver
    if (senderMembership) {
      setSenderInfo(senderMembership);
    }

    if (recipientMembership) {
      setReceiverInfo(recipientMembership);
    }

    if (responsibilityMembership) {
      form.setFieldsValue({
        responsibilityMemberShipId: responsibilityMembership.id,
      });
    }

    setFileList(data.senderAttachments ? [...data.senderAttachments] : []);
    const transformedApproveData = transformApproveData(
      data.approvalLists,
      data.createdBy
    );
    setApprovalSteps(transformedApproveData);
    setFollowers(data.followMemberShips || []);
    setResponders(data.responderMemberShips || []);
  };

  const getOneRFI = async (id: number) => {
    // debugger;

    try {
      setLoadingFetch(true);
      const { data } = await rfiApi.findOne(id);

      if (data.commentConfirm) {
        setCommentConfirm(data.commentConfirm);
      }
      if (isEmpty(data)) {
        navigate("/404");
        return;
      }

      setSelectedRFI(data);
      if (memberShips.length > 0) {
        setDataToForm(data);
      }

      return data as RFI;
    } catch (e: any) {
    } finally {
      setLoadingFetch(false);
    }
  };

  const fetchRfiCategories = async () => {
    try {
      setLoadingCategories(true);
      const { data } = await dictionaryApi.findAll({
        isActive: true,
        projectId: appStore.currentProject?.id,
      });
      setRfiCategories(data?.dictionaries || []);
    } catch (error) {
      console.error("Error fetching RFI categories:", error);
      message.error("Không thể tải danh sách phân loại RFI");
    } finally {
      setLoadingCategories(false);
    }
  };

  const getDataSubmit = async () => {
    const {
      projectId,
      senderMemberShipId,
      recipientMemberShipId,
      senderLevelId,
      receiverLevelId,
      sendCompanyId,
      receiverCompanyId,
      requestDate,
      // responsibilityMemberShipId,
      rfiCategoryId,
      dueAt,
      ...data
    } = form.getFieldsValue();

    const senderMemberShip = senderMemberShipId
      ? getMemberShipInfo(senderMemberShipId)
      : null;
    const recipientMemberShip = recipientMemberShipId
      ? getMemberShipInfo(recipientMemberShipId)
      : null;
    // const responsibilityMemberShip = responsibilityMemberShipId
    //   ? getMemberShipInfo(responsibilityMemberShipId)
    //   : 0;

    const fileAttachIds: number[] = fileList?.length
      ? fileList.map((f) => f.id || 0).filter(Boolean)
      : [];

    for (const file of fileList) {
      if (file.id) {
        fileAttachIds.push(file.id);
      } else if (file.url && !file.originFile) {
        try {
          const resFileAttach = await fileAttachApi.create({
            fileAttach: {
              name: file.name,
              url: file.url,
              mimetype: file.mimetype || "application/octet-stream",
              size: file.size || 0,
            },
          });
          fileAttachIds.push(resFileAttach.data.id);
          console.log("Created file record:", resFileAttach.data);
        } catch (error) {
          console.error("Error creating file record:", error);
        }
      } else if (file.originFile) {
        try {
          const { data: uploadData } = await fileAttachApi.upload(
            file.originFile
          );

          const resFileAttach = await fileAttachApi.create({
            fileAttach: {
              name: file.name,
              url: $url(uploadData.path),
              mimetype: file.mimetype || file.originFile.type,
              size: file.size || file.originFile.size,
            },
          });

          fileAttachIds.push(resFileAttach.data.id);
        } catch (error) {
          console.error("Error uploading file:", error);
        }
      }
    }

    const approvalLists = approvalSteps.map((step, i) => {
      const _approvalListDetails = step.approvers.map((approver, index) => {
        if (
          !approver.memberShipId &&
          step.name !== ApprovalTemplateName.Create
        ) {
          message.error("Vui lòng chọn người duyệt cho bước " + (i + 1));
          throw new Error("Vui lòng chọn người duyệt cho bước " + (i + 1));
        }
        return {
          position: index,
          memberShipId: approver.memberShipId,
          roleId: approver.roleId,
        };
      });

      return {
        ...(status === "update" && { id: step.id }),
        name: step.name,
        type: ApprovalListType.RFI,
        position: step.position,
        note: step.note,
        isAllApproval: step.isAllApproval,
        actionText: step.actionText,
        statusText: step.statusText,
        statusColor: step.statusColor,
        approvalListDetails: _approvalListDetails,
        // memberShipId: e.memberShipId,
        // memberShip2Id: e.memberShip2Id || 0,
        rfiId: selectedRFI?.id || 0,
        staffId: step.staffId,
      };
    });

    if (removeApprovalList.length) {
      for (let i = 0; i < removeApprovalList.length; i++) {
        const element = removeApprovalList[i];
        await approvalListApi.delete(element);
      }
    }

    if (approvalLists.length && status == "update") {
      const dataSubmit = {
        approvalLists: approvalLists,
      };
      await approvalListApi.batch(dataSubmit);
    }

    const payload = {
      rfi: {
        ...data,
        requestDate: requestDate ? dayjs(requestDate).format("YYYY-MM-DD") : "",
        //@ts-ignore
        dueAt: dueAt ? dueAt.startOf("day").unix() : 0,
        isActive: selectedRFI?.isActive,
      },
      fileAttachIds: fileAttachIds.length ? fileAttachIds : [],
      senderAttachmentIds: fileAttachIds.length ? fileAttachIds : [],
      projectId: projectId || 0,
      senderMemberShipId: senderMemberShip.id || 0,
      recipientMemberShipId: recipientMemberShip.id || 0,
      // responsibilityMemberShipId: responsibilityMemberShip.id || 0,
      senderLevelId: senderLevelId || 0,
      receiverLevelId: receiverLevelId || 0,
      sendCompanyId: sendCompanyId || 0,
      receiverCompanyId: receiverCompanyId || 0,
      rfiCategoryId: rfiCategoryId || 0,
      followMemberShipIds: Array.isArray(followers)
        ? followers.map((it) => it.id)
        : [],
      responderMemberShipIds: Array.isArray(responders)
        ? responders.map((it) => it.id)
        : [],
      approvalLists: approvalLists,
    };

    console.log("Final payload:", payload);
    return payload;
  };

  const createData = async () => {
    const valid = await form.validateFields();

    setLoading(true);
    try {
      const res = await rfiApi.create(await getDataSubmit());

      message.success("Tạo RFIs thành công!");
      navigate(`/report/${PermissionNames.rfisList}`);
      setFileList([]);
    } finally {
      setLoading(false);
    }
  };

  const updateData = async () => {
    const valid = await form.validateFields();

    setLoading(true);
    try {
      const res = await rfiApi.update(
        selectedRFI!?.id || 0,
        await getDataSubmit()
      );
      setSelectedRFI({ ...selectedRFI, ...res.data });
      setRemoveApprovalList([]);
      message.success("Chỉnh sửa RFIs thành công!");

      // Fetch lại data mới nhất
      if (selectedRFI?.id) {
        await getOneRFI(selectedRFI.id);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = () => {
    if (!readonly) {
      if (!responders || responders.length === 0) {
        message.error("Vui lòng chọn ít nhất một người phản hồi!");
        return;
      }
    }

    if (status == "create") {
      createData();
    } else {
      updateData();
    }
  };

  const handleApproveProcess = async (data: ApproveData) => {
    console.log("Approve process");
    try {
      setLoadingApprove(true);
      await rfiApi.approve(selectedRFI!.id || 0, data);
      message.success("Duyệt RFIs thành công!");
      await getOneRFI(selectedRFI!.id || 0);
      setCommentRefreshTrigger((prev) => prev + 1);
    } finally {
      setLoadingApprove(false);
    }
  };

  const handleRejectProcess = async (data: ApproveData) => {
    console.log("Reject process");
    try {
      setLoadingApprove(true);
      await rfiApi.reject(selectedRFI!.id || 0, data);
      message.success("Từ chối RFIs thành công!");
      await getOneRFI(selectedRFI!.id || 0);
      setCommentRefreshTrigger((prev) => prev + 1);
    } finally {
      setLoadingApprove(false);
    }
  };

  const handleDownloadTemplate = () => {
    console.log("Download template");
  };

  const handleCommentConfirm = async (rfiId: number, commentId: number) => {
    const rfi = await rfiApi.findOne(rfiId);
    const payload = {
      rfi: {
        ...rfi.data,
      },
      commentConfirmId: commentId,
    };
    await rfiApi.update(rfiId, payload);
    message.success("Xác nhận chốt phản hồi thành công!");
    await getOneRFI(rfiId);
  };

  const pageTitle = useMemo(
    () => (status == "create" ? "Tạo yêu cầu" : "Chỉnh sửa yêu cầu"),
    [status]
  );

  const rfiData = {
    projectName: appStore.currentProject?.name || "",

    item: form.getFieldValue("title") || "",
    date: form.getFieldValue("requestDate")
      ? dayjs(form.getFieldValue("requestDate")).format("DD/MM/YYYY")
      : "",
    no: form.getFieldValue("code") || "",
    fromCompany: senderInfo?.company?.name || "",
    fromName: senderInfo?.staff?.fullName || "",
    fromPosition: senderInfo?.jobTitle?.name || "",
    toCompany: receiverInfo?.company?.name || "",
    toName: receiverInfo?.staff?.fullName || "",
    toPosition: receiverInfo?.jobTitle?.name || "",
    ccName: "", // Nếu có trường đồng kính gửi thì lấy, không thì để rỗng
    ccPosition: "",
    expectedReply: form.getFieldValue("dueAt")
      ? dayjs(form.getFieldValue("dueAt")).format("DD/MM/YYYY")
      : "",
    requestInfo: form.getFieldValue("requestInfo") || "",
    attachment:
      fileList && fileList.length ? fileList.map((f) => f.name).join(", ") : "",
    requestedBy: senderInfo?.staff?.fullName || "",
    consultantReply: "", // Nếu có trường ý kiến phản hồi thì truyền vào
    repliedBy: receiverInfo?.staff?.fullName || "",
    attachment2:
      fileList && fileList.length ? fileList.map((f) => f.name).join(", ") : "",
  };

  const canEditRecord = (record: RFI) => {
    if (!record) return false;
    return checkEditPermissionByCreator(
      userStore.info.id,
      record.createdBy?.id,
      haveEditPermission,
      haveViewAllPermission
    );
  };

  return (
    <div className="app-container">
      <PageTitle
        back
        className="mr-[114px] relative z-10"
        breadcrumbs={[
          { label: "Báo cáo" },
          {
            label: "RFIs",
            href: `/report/${PermissionNames.rfisList}`,
          },
          { label: pageTitle },
        ]}
        title={pageTitle}
        extra={
          status === "update" && (
            <div className="">
              <Popconfirm
                title="Bạn có chắc chắn muốn xuất phiếu RFI không?"
                okText="Đồng ý"
                cancelText="Hủy"
                onConfirm={handlePrint}
                okButtonProps={{
                  style: {
                    background: "#223a5f",
                    borderColor: "#223a5f",
                    color: "#fff",
                  },
                }}
              >
                <CustomButton>Xuất PDF</CustomButton>
              </Popconfirm>
              <div
                style={{
                  position: "absolute",
                  top: 0,
                  left: 0,
                  width: 0,
                  height: 0,
                  overflow: "hidden",
                }}
              >
                <ExportPdfRFIs ref={printRef} data={rfiData} />
              </div>
            </div>
          )
        }
      />

      <Spin spinning={loadingFetch}>
        <Form
          form={form}
          layout="vertical"
          disabled={readonly}
          className={clsx(readonly ? "readonly" : "")}
          onFinish={handleSubmit}
          initialValues={{
            createdDate: dayjs(),
          }}
        >
          <Row gutter={24}>
            <Col span={18}>
              <Card className="content-card ">
                <Card title="Thông tin cơ bản" className="mb-0 form-card">
                  <Row gutter={16}>
                    {/* First Row */}
                    <Col span={8}>
                      <Form.Item name="code" label="Mã RFIs">
                        <Input
                          placeholder="Mã RFIs"
                          disabled={status === "update"}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item name="title" label="Tiêu đề" rules={rules}>
                        <Input placeholder="Tiêu đề" />
                      </Form.Item>
                    </Col>

                    <Col span={8}>
                      <Form.Item
                        name="rfiCategoryId"
                        label="Phân loại"
                        rules={rules}
                      >
                        <DictionarySelector
                          initQuery={{
                            type: DictionaryType.RfiCategory,
                            isActive: true,
                          }}
                        />
                      </Form.Item>
                    </Col>

                    <Col span={8}>
                      <Form.Item name="codeDraw" label="Bản vẽ">
                        <Select placeholder="Chọn bản vẽ" />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item name="specifications" label="Phần thông số">
                        <Input placeholder="Phần thông số" />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        name="status"
                        label="Trạng thái phản hồi"
                        rules={rules}
                      >
                        <Select placeholder="Chọn trạng thái">
                          <Select.Option value={RFIStatus.Draft}>
                            Nháp
                          </Select.Option>
                          <Select.Option value={RFIStatus.Open}>
                            Mở
                          </Select.Option>
                          <Select.Option value={RFIStatus.Responded}>
                            Đã phản hồi
                          </Select.Option>
                          <Select.Option value={RFIStatus.Closed}>
                            Đã đóng
                          </Select.Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        name="requestDate"
                        label="Ngày gửi"
                        rules={[
                          ...rules,
                          ({ getFieldValue }) => ({
                            validator(_, value) {
                              const dueAt = getFieldValue("dueAt");
                              if (!value || !dueAt) return Promise.resolve();
                              if (dayjs(value).isBefore(dayjs(dueAt), "day")) {
                                return Promise.resolve();
                              }
                              return Promise.reject(
                                new Error("Ngày gửi phải trước ngày đến hạn!")
                              );
                            },
                          }),
                        ]}
                      >
                        <DatePicker
                          allowClear={false}
                          placeholder="Ngày gửi"
                          format={settings.dateFormat}
                          className="w-full"
                          onChange={() => {
                            form.validateFields(["requestDate", "dueAt"]);
                          }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        name="dueAt"
                        label="Ngày đến hạn"
                        rules={[
                          ...rules,
                          ({ getFieldValue }) => ({
                            validator(_, value) {
                              const requestDate = getFieldValue("requestDate");
                              if (!value || !requestDate)
                                return Promise.resolve();
                              if (
                                dayjs(value).isAfter(dayjs(requestDate), "day")
                              ) {
                                return Promise.resolve();
                              }
                              return Promise.reject(
                                new Error("Ngày đến hạn phải sau ngày gửi!")
                              );
                            },
                          }),
                        ]}
                      >
                        <DatePicker
                          allowClear={false}
                          format={settings.dateFormat}
                          className="w-full"
                          onChange={() => {
                            form.validateFields(["requestDate", "dueAt"]);
                          }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      {/* <Form.Item
                        name="responsibilityMemberShipId"
                        label="Người phụ trách"
                      >
                        <MembershipSelector placeholder="Người phụ trách" />
                      </Form.Item> */}
                    </Col>
                  </Row>
                </Card>

                <Card
                  title="Thông tin bên gửi / nhận"
                  className="mb-0 form-card mt-[16px]"
                >
                  <Row gutter={16}>
                    {/* First Row */}
                    <Col span={8}>
                      <Form.Item
                        name="senderMemberShipId"
                        label="Người gửi"
                        rules={[
                          ...rules,
                          ({ getFieldValue }) => ({
                            validator(_, value) {
                              if (
                                !value ||
                                value !== getFieldValue("recipientMemberShipId")
                              ) {
                                return Promise.resolve();
                              }
                              return Promise.reject(
                                new Error(
                                  "Người gửi và người nhận phải khác nhau!"
                                )
                              );
                            },
                          }),
                        ]}
                      >
                        <MembershipSelector
                          placeholder="Người gửi"
                          valueIsOption
                          onChange={(memberInfo: any) => {
                            const recipientId = form.getFieldValue(
                              "recipientMemberShipId"
                            );

                            // Nếu chọn trùng với người nhận, set lỗi cho cả hai field
                            if (memberInfo && memberInfo.id === recipientId) {
                              form.setFields([
                                {
                                  name: "senderMemberShipId",
                                  errors: [
                                    "Người gửi và người nhận phải khác nhau!",
                                  ],
                                },
                                {
                                  name: "recipientMemberShipId",
                                  errors: [
                                    "Người gửi và người nhận phải khác nhau!",
                                  ],
                                },
                              ]);
                              return;
                            }

                            // Clear lỗi nếu valid lại
                            form.setFields([
                              {
                                name: "senderMemberShipId",
                                errors: [],
                              },
                              {
                                name: "recipientMemberShipId",
                                errors: [],
                              },
                            ]);

                            setSenderInfo(memberInfo || null);
                            form.setFieldsValue({
                              senderMemberShipId: memberInfo
                                ? memberInfo.id
                                : null,
                              sendCompanyId: memberInfo?.company?.id || 0,
                              senderLevelId: memberInfo?.jobTitle?.id || 0,
                              sendCompanyDisplay:
                                memberInfo?.company?.name || "",
                              senderLevelDisplay:
                                memberInfo?.jobTitle?.name || "",
                            });

                            form.validateFields([
                              "senderMemberShipId",
                              "recipientMemberShipId",
                            ]);
                          }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        name="sendCompanyDisplay"
                        label="Công ty"
                        rules={rules}
                      >
                        <Input
                          disabled
                          placeholder="Công ty"
                          key={`sender-company-${
                            senderInfo?.company?.id || "empty"
                          }`}
                          value={senderInfo?.company?.name || ""}
                        />
                      </Form.Item>
                      {/* Hidden field để lưu ID */}
                      <Form.Item name="sendCompanyId" hidden>
                        <Input />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        name="senderLevelDisplay"
                        label="Chức vụ"
                        rules={rules}
                      >
                        <Input
                          disabled
                          placeholder="Chức vụ"
                          key={`sender-level-${
                            senderInfo?.jobTitle?.id || "empty"
                          }`}
                        />
                      </Form.Item>
                      {/* Hidden field để lưu ID */}
                      <Form.Item name="senderLevelId" hidden>
                        <Input />
                      </Form.Item>
                    </Col>

                    {/* Second Row */}
                    <Col span={8}>
                      <Form.Item
                        name="recipientMemberShipId"
                        label="Người nhận"
                        rules={[
                          ...rules,
                          ({ getFieldValue }) => ({
                            validator(_, value) {
                              if (
                                !value ||
                                value !== getFieldValue("senderMemberShipId")
                              ) {
                                return Promise.resolve();
                              }
                              return Promise.reject(
                                new Error(
                                  "Người nhận và người gửi phải khác nhau!"
                                )
                              );
                            },
                          }),
                        ]}
                      >
                        <MembershipSelector
                          placeholder="Người nhận"
                          valueIsOption
                          onChange={(memberInfo: any) => {
                            const senderId =
                              form.getFieldValue("senderMemberShipId");

                            if (memberInfo && memberInfo.id === senderId) {
                              form.setFields([
                                {
                                  name: "recipientMemberShipId",
                                  errors: [
                                    "Người gửi và người nhận phải khác nhau!",
                                  ],
                                },
                                {
                                  name: "senderMemberShipId",
                                  errors: [
                                    "Người gửi và người nhận phải khác nhau!",
                                  ],
                                },
                              ]);
                              return;
                            }

                            form.setFields([
                              {
                                name: "recipientMemberShipId",
                                errors: [],
                              },
                              {
                                name: "senderMemberShipId",
                                errors: [],
                              },
                            ]);

                            setReceiverInfo(memberInfo || null);
                            form.setFieldsValue({
                              recipientMemberShipId: memberInfo
                                ? memberInfo.id
                                : null,
                              receiverCompanyId: memberInfo?.company?.id || 0,
                              receiverLevelId: memberInfo?.jobTitle?.id || 0,
                              receiverCompanyDisplay:
                                memberInfo?.company?.name || "",
                              receiverLevelDisplay:
                                memberInfo?.jobTitle?.name || "",
                            });

                            form.validateFields([
                              "senderMemberShipId",
                              "recipientMemberShipId",
                            ]);
                          }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        name="receiverCompanyDisplay"
                        label="Công ty"
                        rules={rules}
                      >
                        <Input
                          disabled
                          placeholder="Công ty"
                          key={`receiver-company-${
                            receiverInfo?.company?.id || "empty"
                          }`}
                        />
                      </Form.Item>
                      {/* Hidden field để lưu ID */}
                      <Form.Item name="receiverCompanyId" hidden>
                        <Input />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        name="receiverLevelDisplay"
                        label="Chức vụ"
                        rules={rules}
                      >
                        <Input
                          disabled
                          placeholder="Chức vụ"
                          key={`receiver-level-${
                            receiverInfo?.jobTitle?.id || "empty"
                          }`}
                        />
                      </Form.Item>
                      {/* Hidden field để lưu ID */}
                      <Form.Item name="receiverLevelId" hidden>
                        <Input />
                      </Form.Item>
                    </Col>
                  </Row>
                </Card>

                <Card
                  title="Thông tin yêu cầu"
                  className="mb-0 form-card mt-[16px]"
                >
                  <Row gutter={16}>
                    {/* First Row */}
                    <Col span={24}>
                      <Form.Item name="requestInfo" label="Thông tin yêu cầu">
                        <BMDCKEditor
                          placeholder="Nhập thông tin yêu cầu"
                          disabled={readonly}
                          inputHeight={300}
                          value={requestInfo}
                          onChange={(content) => {
                            setRequestInfo(content);
                            form.setFieldsValue({ requestInfo: content });
                          }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </Card>

                <Tabs defaultActiveKey="0" type="line" className="mt-[16px]">
                  <Tabs.TabPane tab="Tệp đính kèm" key="0">
                    <Form.Item
                      shouldUpdate={true}
                      style={{ marginBottom: 0, height: "100%" }}
                      className="form-height-full"
                    >
                      {() => {
                        return (
                          <Form.Item
                            label={""}
                            noStyle
                            style={{ marginBottom: 0 }}
                            name="files"
                            className="h-full "
                          >
                            <FileUploadMultiple2
                              hideUploadButton={readonly}
                              showSearch
                              className="h-full"
                              fileList={fileList}
                              onUploadOk={(file) => {
                                fileList.push(file);
                                setFileList([...fileList]);
                              }}
                              onDelete={(file) => {
                                const findIndex = fileList.findIndex(
                                  (e) => e.uid == file.uid
                                );

                                if (findIndex > -1) {
                                  fileList.splice(findIndex, 1);
                                  setFileList([...fileList]);
                                }
                              }}
                            />
                          </Form.Item>
                        );
                      }}
                    </Form.Item>
                  </Tabs.TabPane>
                  {selectedRFI && readonly && (
                    <>
                      <Tabs.TabPane tab="Bình luận" key="1">
                        <CommentView
                          initQuery={{ rfiId: selectedRFI!.id }}
                          refreshTrigger={commentRefreshTrigger}
                          commentConfirm={commentConfirm}
                          handleCommentConfirm={handleCommentConfirm}
                        />
                      </Tabs.TabPane>
                      <Tabs.TabPane tab="Người phản hồi" key="2">
                        <ResponderSelector
                          responders={responders}
                          setResponders={setResponders}
                          readonly={readonly}
                          headerTitle={`Người phản hồi (${responders?.length})`}
                        />
                      </Tabs.TabPane>
                    </>
                  )}
                </Tabs>

                {/* Action Buttons */}
                <div
                  className="mt-[16px]"
                  style={{
                    display: "flex",
                    justifyContent: "flex-end",
                    gap: "12px",
                  }}
                >
                  {!readonly && (
                    <CustomButton
                      variant="outline"
                      className="cta-button"
                      onClick={() => {
                        if (status == "create") {
                          navigate(`/report/${PermissionNames.rfisList}`);
                        } else {
                          setDataToForm(selectedRFI!);
                          setReadonly(true);
                        }
                      }}
                    >
                      Hủy
                    </CustomButton>
                  )}
                  <CustomButton
                    className="cta-button"
                    loading={loading}
                    onClick={() => {
                      if (!readonly) {
                        handleSubmit();
                      } else {
                        setReadonly(false);
                      }
                    }}
                    disabled={
                      status == "update" &&
                      (!selectedRFI || !canEditRecord(selectedRFI))
                    }
                  >
                    {status == "create"
                      ? "Tạo RFIs"
                      : readonly
                      ? "Chỉnh sửa"
                      : "Lưu chỉnh sửa"}
                  </CustomButton>
                </div>
              </Card>
            </Col>

            <Col span={6}>
              <ApprovalStepsCard
                steps={approvalSteps}
                loading={loadingApprove}
                onSelectStep={setApprovalSteps}
                onRemove={setRemoveApprovalList}
                onApprove={handleApproveProcess}
                onReject={handleRejectProcess}
                templateType={ApprovalTemplateType.RFI}
                editable={true}
                isShowActionButton={status == "update"}
                notRequired
              />

              <FollowerSelector
                followers={followers}
                setFollowers={setFollowers}
                readonly={readonly}
                headerTitle={`Người theo dõi` + ` (${followers?.length})`}
              />

              {(status === "create" || status === "update") && !readonly && (
                <ResponderSelector
                  responders={responders}
                  setResponders={setResponders}
                  readonly={readonly}
                  headerTitle={`Người phản hồi (${responders?.length})`}
                  required
                />
              )}
            </Col>
          </Row>
        </Form>
      </Spin>
    </div>
  );
}

export default observer(CreateOrUpdateRFIsPage);
