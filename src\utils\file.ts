import J<PERSON><PERSON><PERSON> from "jszip";
import { saveAs } from "file-saver";
import { FileAttachType } from "types/fileAttach";


export async function downloadFilesAsZip(files: { name: string; url: string }[], zipName = "files.zip") {
    const zip = new JSZip();

    const promises = files.map(async (file) => {
        const response = await fetch(file.url);
        const blob = await response.blob();
        zip.file(file.name, blob); // 
    });

    await Promise.all(promises);

    const content = await zip.generateAsync({ type: "blob" });
    saveAs(content, zipName); // 
}


export function formatFileSize(bytes: number, decimals = 2) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    const size = bytes / Math.pow(k, i);
    return `${parseFloat(size.toFixed(decimals))} ${sizes[i]}`;
}

export const getMimeTypeCategory = (mimetype: string): FileAttachType => {
  // Handle image types
  if (mimetype.startsWith('image/')) {
    return FileAttachType.Image;
  }
  
  // Handle PDF
  if (mimetype === 'application/pdf') {
    return FileAttachType.Pdf;
  }
  
  // Handle video types
  if (mimetype.startsWith('video/')) {
    return FileAttachType.Video;
  }

  // Handle folder type
  if (mimetype === 'folder') {
    return FileAttachType.Folder;
  }
  
  // Default to Other for unrecognized types
  return FileAttachType.Other;
};