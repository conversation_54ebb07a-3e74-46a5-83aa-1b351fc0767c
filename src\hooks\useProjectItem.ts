import { projectItemApi } from "api/projectItem.api";
import { useMemo, useState } from "react";
import { ProjectItem } from "types/projectItem";
import { QueryParam } from "types/query";

export interface ProjectItemQuery extends QueryParam {}

interface Props {
  initQuery: ProjectItemQuery;
}

export const useProjectItem = ({ initQuery }: Props) => {
  const [data, setData] = useState<ProjectItem[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<ProjectItemQuery>(initQuery);
  const [loading, setLoading] = useState(false);
  const [isFetched, setIsFetched] = useState(false);

  const isEmptyQuery = useMemo(
    () =>
      Object.keys(query).filter(
        (k) =>
          !["limit", "page", "queryObject", "projectId"].includes(k) &&
          query[k] !== undefined &&
          query[k] !== null &&
          query[k] !== ""
      ).length === 0,
    [query]
  );

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await projectItemApi.findAll(query);

      setData(data.projectItems);
      setTotal(data.total);
    } finally {
      setLoading(false);
      setIsFetched(true);
    }
  };

  return {
    projectItems: data,
    total,
    fetchData,
    loading,
    setQuery,
    query,
    isEmptyQuery,
    setData,
    isFetched,
  };
};
