import { Card, Spin, Button, Space, Tooltip, Modal, Tag } from "antd";
import DeleteIcon from "assets/svgs/DeleteIcon";
import PencilIcon from "assets/svgs/PencilIcon";
import CustomButton from "components/Button/CustomButton";
import CustomInput from "components/Input/CustomInput";
import PageTitle from "components/PageTitle/PageTitle";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import { Pagination } from "components/Pagination";
import { useTheme } from "context/ThemeContext";
import React, { useEffect, useState } from "react";
import { QueryParam } from "types/query";
import { useNavigate } from "react-router-dom";
import { checkRoles, filterActionColumnIfNoPermission } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { observer } from "mobx-react";
import { PermissionNames } from "types/PermissionNames";
import { getTitle } from "utils";
import { LockOutlined, UnlockOutlined } from "@ant-design/icons";
import LockButton from "components/Button/LockButton";
import EditButton from "components/Button/EditButton";

function DocumentaryPage({ title }: { title: string }) {
  const { haveAddPermission, haveBlockPermission, haveEditPermission } =
    checkRoles(
      {
        add: PermissionNames.projectDocAdd,
        edit: PermissionNames.projectDocEdit,
        block: PermissionNames.projectDocBlock,
      },
      permissionStore.permissions
    );

  const [searchValue, setSearchValue] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [total, setTotal] = useState(25);
  const [loading, setLoading] = useState(false);
  const [query, setQuery] = useState<QueryParam>({
    page: 1,
    limit: 10,
    search: "",
  });

  useEffect(() => {
    document.title = getTitle(title);
  }, []);

  // Fake data for the table
  const fakeData = [
    {
      id: "TL-001",
      documentName: "Hướng dẫn an toàn lao động",
      folder: "An toàn lao động",
      creator: "Nguyễn Văn A",
      createdDate: "2024-01-15",
      approvalLevel: "Cấp 1",
      status: "Đã duyệt",
      isActive: true,
    },
    {
      id: "TL-002",
      documentName: "Quy trình thi công móng",
      folder: "Kỹ thuật thi công",
      creator: "Trần Thị B",
      createdDate: "2024-01-20",
      approvalLevel: "Cấp 2",
      status: "Chờ duyệt",
      isActive: true,
    },
    {
      id: "TL-003",
      documentName: "Báo cáo chất lượng vật liệu",
      folder: "Báo cáo chất lượng",
      creator: "Lê Văn C",
      createdDate: "2024-01-25",
      approvalLevel: "Cấp 3",
      status: "Từ chối",
      isActive: false,
    },
    {
      id: "TL-004",
      documentName: "Kế hoạch bảo vệ môi trường",
      folder: "Môi trường",
      creator: "Phạm Thị D",
      createdDate: "2024-02-01",
      approvalLevel: "Cấp 2",
      status: "Nháp",
      isActive: true,
    },
    {
      id: "TL-005",
      documentName: "Hướng dẫn phòng chống cháy nổ",
      folder: "An toàn lao động",
      creator: "Hoàng Văn E",
      createdDate: "2024-02-05",
      approvalLevel: "Cấp 1",
      status: "Đã duyệt",
      isActive: true,
    },
    {
      id: "TL-006",
      documentName: "Quy định sử dụng thiết bị bảo hộ",
      folder: "An toàn lao động",
      creator: "Ngô Thị F",
      createdDate: "2024-02-10",
      approvalLevel: "Cấp 1",
      status: "Đã duyệt",
      isActive: true,
    },
    {
      id: "TL-007",
      documentName: "Báo cáo tiến độ thi công",
      folder: "Báo cáo tiến độ",
      creator: "Đỗ Văn G",
      createdDate: "2024-02-15",
      approvalLevel: "Cấp 3",
      status: "Chờ duyệt",
      isActive: true,
    },
    {
      id: "TL-008",
      documentName: "Hướng dẫn bảo dưỡng thiết bị",
      folder: "Kỹ thuật",
      creator: "Bùi Thị H",
      createdDate: "2024-02-20",
      approvalLevel: "Cấp 2",
      status: "Đã duyệt",
      isActive: true,
    },
    {
      id: "TL-009",
      documentName: "Quy chế quản lý nhân sự",
      folder: "Quản lý nhân sự",
      creator: "Vũ Văn I",
      createdDate: "2024-02-25",
      approvalLevel: "Cấp 2",
      status: "Nháp",
      isActive: false,
    },
    {
      id: "TL-010",
      documentName: "Hướng dẫn xử lý chất thải",
      folder: "Môi trường",
      creator: "Lý Thị K",
      createdDate: "2024-03-01",
      approvalLevel: "Cấp 1",
      status: "Đã duyệt",
      isActive: true,
    },
  ];

  const { darkMode } = useTheme();
  const navigate = useNavigate();

  const handleSearch = (value: string) => {
    setSearchValue(value);
  };

  const handleStatusChange = (value: string) => {
    setStatusFilter(value);
  };

  const handleCreateDocument = () => {
    // Navigate to create document page
    navigate("/doc-management/create-project-doc");
  };

  const pagination = {
    current: query.page,
    pageSize: query.limit,
    total: total,
    showSizeChanger: true,
  };

  const handleRowClick = (record: any) => {
    navigate(
      `/doc-management/${PermissionNames.projectDocEdit.replace(
        ":id",
        record.id
      )}`
    );
  };

  const columns: CustomizableColumn<any>[] = [
    {
      key: "id",
      title: "Mã",
      dataIndex: "id",
      width: 100,
      defaultVisible: true,
      alwaysVisible: true,
    },
    {
      key: "documentName",
      title: "Tên tài liệu",
      dataIndex: "documentName",
      width: 200,
      defaultVisible: true,
      alwaysVisible: true,
    },
    {
      key: "folder",
      title: "Thư mục",
      dataIndex: "folder",
      width: 150,
      defaultVisible: true,
      alwaysVisible: true,
    },
    {
      key: "creator",
      title: "Người tạo",
      dataIndex: "creator",
      width: 120,
      defaultVisible: true,
      alwaysVisible: true,
    },
    {
      key: "createdDate",
      title: "Ngày tạo",
      dataIndex: "createdDate",
      width: 120,
      defaultVisible: true,
      alwaysVisible: true,
    },
    {
      key: "approvalLevel",
      title: "Cấp duyệt",
      dataIndex: "approvalLevel",
      width: 100,
      defaultVisible: true,
      alwaysVisible: true,
    },
    {
      key: "status",
      title: "Trạng thái",
      dataIndex: "status",
      align: "center",
      width: 150,
      render: (_, record) => (
        <div className="flex justify-center">
          {record?.isActive ? (
            <Tag className="status-tag !mr-0" color="green">
              Hoạt động
            </Tag>
          ) : (
            <Tag className="status-tag !mr-0" color="red">
              Tạm khóa
            </Tag>
          )}
        </div>
      ),
      defaultVisible: true,
      alwaysVisible: true,
    },
    {
      key: "actions",
      title: "Xử lý",
      width: 60,
      align: "center",
      fixed: "right",
      render: (_, record) => (
        <Space size="small">
          {haveEditPermission && (
            <EditButton
              onClick={(e) => {
                e.stopPropagation();
                navigate(
                  `/doc-management/${PermissionNames.projectDocEdit.replace(
                    ":id",
                    record.id
                  )}`
                );
              }}
            />
          )}
          {haveBlockPermission && (
            <LockButton
              isActive={record.isActive}
              onAccept={() => {}}
              modalTitle={`${record.isActive ? "Khóa" : "Mở khóa"} dịch vụ: ${
                record.name
              }`}
              modalContent={
                <>
                  <div>
                    Khi {record.isActive ? "khóa" : "mở khóa"} tài liệu các
                    thông tin của tài liệu này cũng sẽ được{" "}
                    {record.isActive ? "khóa" : "mở khóa"}.
                  </div>
                  <div>
                    Bạn có chắc chắn muốn {record.isActive ? "khóa" : "mở khóa"}{" "}
                    tài liệu này?
                  </div>
                </>
              }
            />
          )}
        </Space>
      ),
    },
  ];

  return (
    <div>
      <PageTitle
        title="Tài liệu"
        breadcrumbs={["Dữ liệu nguồn", "Tài liệu"]}
        extra={
          <Space>
            {haveAddPermission && (
              <CustomButton
                size="small"
                showPlusIcon
                onClick={handleCreateDocument}
              >
                Tạo tài liệu
              </CustomButton>
            )}
          </Space>
        }
      />

      <Card>
        <div className="pb-[16px]">
          <div className="flex justify-between items-center mb-4">
            <div className="font-bold text-lg">DANH SÁCH TÀI LIỆU</div>
            <div className="flex gap-[16px] items-center w-[500px]">
              <CustomInput
                inputSearch
                placeholder="Tìm kiếm"
                value={searchValue}
                onChange={handleSearch}
              />

              <CustomInput
                type="select"
                placeholder="Tất cả trạng thái"
                value={statusFilter}
                options={[
                  {
                    label: "Tất cả trạng thái",
                    value: "",
                  },
                  {
                    label: "Đã duyệt",
                    value: "approved",
                  },
                  {
                    label: "Chờ duyệt",
                    value: "pending",
                  },
                  {
                    label: "Từ chối",
                    value: "rejected",
                  },
                  {
                    label: "Nháp",
                    value: "draft",
                  },
                ]}
                onChange={handleStatusChange}
              />
            </div>
          </div>

          <Spin spinning={loading}>
            <CustomizableTable
              columns={filterActionColumnIfNoPermission(columns, [
                haveEditPermission,
                haveBlockPermission,
              ])}
              dataSource={fakeData}
              rowKey="id"
              loading={loading}
              pagination={false}
              scroll={{ x: 1200 }}
              bordered
              displayOptions
              onRowClick={handleRowClick}
            />
          </Spin>
        </div>

        <Pagination
          currentPage={query.page}
          total={total}
          onChange={({ limit, page }) => {
            setQuery({ ...query, page, limit });
          }}
        />
      </Card>
    </div>
  );
}

export default observer(DocumentaryPage);
