import {
  Button,
  Checkbox,
  Col,
  Form,
  Image,
  Input,
  message,
  Modal,
  Row,
  Select,
  Tag,
  Upload,
  UploadProps,
} from "antd";

import React, { useEffect, useImperativeHandle, useState } from "react";
import { ModalStatus } from "types/modal";

import { useWatch } from "antd/lib/form/Form";

import { requiredRule } from "utils/validateRule";

import { $url } from "utils/url";
import { useComponent } from "hooks/useComponent";
import { CreatingVariant, Variant } from "types/variant";
import { useMaterial } from "hooks/useMaterial";
import { variantApi } from "api/variant.api";
import ChooseFileFromMenu from "components/Upload/ChooseImageFromMenu";
import { Unit } from "types/unit";
import { unitApi } from "api/unit.api";
import { stretchDirectionApi } from "api/stretchDirection.api";
import { useStretchDirection } from "hooks/useStretchDirection";
import { StretchDirection } from "types/stretchDirection";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import { FileAttach } from "types/fileAttach";

export interface UnitModal {
  handleCreate: () => void;
  handleUpdate: (stretchDirection: StretchDirection) => void;
}
interface UnitModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
}
export const CreateUnitModal = React.forwardRef(
  ({ onClose, onSubmitOk }: UnitModalProps, ref) => {
    const [form] = Form.useForm<StretchDirection>();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");
    const icon = useWatch("icon", form);
    useImperativeHandle<any, UnitModal>(
      ref,
      () => ({
        handleCreate() {
          form.resetFields();
          setVisible(true);
          setStatus("create");
        },
        handleUpdate(stretchDirection: StretchDirection) {
          form.setFieldsValue({
            ...stretchDirection,
          });

          setVisible(true);
          setStatus("update");
        },
      }),
      []
    );
    const createData = async () => {
      const data = form.getFieldsValue();
      setLoading(true);
      try {
        const res = await stretchDirectionApi.create({
          stretchDirection: { ...data },
        });
        message.success("Tạo thành công");
        handleClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const valid = await form.validateFields();
      const data = form.getFieldsValue();
      setLoading(true);
      console.log("What is in form", data);
      const { id, ...restData } = data;
      console.log("Rest data là", restData);
      try {
        console.log("Data when update", data);
        const res = await stretchDirectionApi.update(id, {
          stretchDirection: { ...data },
        });
        message.success("Cập nhật thành công");
        handleClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    const handleClose = () => {
      onClose();
      setVisible(false);
      form.resetFields();
    };
    const {
      stretchDirections,
      fetchData,
      query,
      loading: stretchDirectionLoading,
      setQuery,
      total,
    } = useStretchDirection({
      initQuery: {
        page: 1,
        limit: 10,
      },
    });

    useEffect(() => {
      fetchData();
    }, []);
    return (
      <Modal
        onCancel={() => {
          onClose?.();
          setVisible(false);
        }}
        open={visible}
        title={
          status == "create"
            ? "Tạo stretch direction"
            : "Cập nhật stretch direction"
        }
        style={{ top: 20 }}
        confirmLoading={loading}
        // onOk={() => {
        //   status == "create" ? createData() : updateData();
        // }}
        onOk={() => {
          form.submit();
        }}
      >
        <Form
          layout="vertical"
          form={form}
          onFinish={() => {
            status == "create" ? createData() : updateData();
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                style={{ marginBottom: 0 }}
                label={<div>Icon</div>}
                name="icon"
              >
                <SingleImageUpload
                  onUploadOk={(file: FileAttach) => {
                    form.setFieldsValue({
                      icon: file.path,
                    });
                  }}
                  imageUrl={form.getFieldValue("icon")}
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                label="Tên"
                name="name"
                rules={[{ required: true, message: "Bắt buộc nhập" }]}
              >
                <Input placeholder="" />
              </Form.Item>
            </Col>
            {[
              status === "update" && (
                <Col span={8}>
                  <Form.Item
                    className="hidden"
                    label="id"
                    name="id"
                    rules={[requiredRule]}
                  >
                    <Input placeholder="" />
                  </Form.Item>
                </Col>
              ),
            ]}
          </Row>
        </Form>
      </Modal>
    );
  }
);
