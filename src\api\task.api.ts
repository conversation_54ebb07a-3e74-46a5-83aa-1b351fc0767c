import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const taskApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/task",
      params,
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/task/${id}`,
    }),
  findSummary: (params?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/task/summary`,
      params,
    }),
  findSummaryMe: (params?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/task/summary/me`,
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/task",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/task/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/task/${id}`,
      method: "delete",
    }),
};
