import { approvalListApi } from "api/approvalList.api";
import { useMemo, useState } from "react";
import { ApprovalList } from "types/approvalList";
import { QueryParam } from "types/query";

export interface ApprovalListQuery extends QueryParam { }

interface UseApprovalListProps {
  initQuery: ApprovalListQuery;
}

export const useApprovalList = ({ initQuery }: UseApprovalListProps) => {
  const [data, setData] = useState<ApprovalList[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<ApprovalListQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const isEmptyQuery = useMemo(
    () =>
      Object.keys(query).filter(
        (k) => query[k] && !["limit", "page", "queryObject"].includes(k)
      ).length == 0,
    [query]
  );

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await approvalListApi.findAll(query);

      setData(data.approvalLists);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return { approvalLists: data, total, fetchData, loading, setQuery, query, isEmptyQuery };
};
