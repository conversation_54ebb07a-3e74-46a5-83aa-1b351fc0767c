import { instructionApi } from "api/instruction.api";
import { useState } from "react";
import { Instruction } from "types/instruction";
import { QueryParam } from "types/query";

export interface InstructionQuery extends QueryParam { }

interface UseInstructionProps {
  initQuery: InstructionQuery;
}

export const useInstruction = ({ initQuery }: UseInstructionProps) => {
  const [data, setData] = useState<Instruction[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<InstructionQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async (newQuery?: QueryParam) => {
    setLoading(true);
    try {
      const { data } = await instructionApi.findAll({ ...query, ...newQuery });

      setData(data.instructions);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return {
    instructions: data,
    totalInstruction: total,
    fetchInstruction: fetchData,
    loadingInstruction: loading,
    setQueryInstruction: setQuery,
    queryInstruction: query,
  };
};
