import {
  Button,
  Checkbox,
  Col,
  Form,
  Image,
  Input,
  message,
  Modal,
  Popconfirm,
  Radio,
  Row,
  Select,
  Switch,
  Table,
  Upload,
  UploadProps,
} from "antd";
import { Rule } from "antd/lib/form";
import { materialApi } from "api/material.api";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import React, {
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from "react";
import { ModalStatus } from "types/modal";
import { useWatch } from "antd/lib/form/Form";
import { FileUpload } from "components/Upload/FileUpload";
import { CiImageOn } from "react-icons/ci";
import {
  DeleteOutlined,
  EditOutlined,
  FileImageOutlined,
  LockOutlined,
  UnlockOutlined,
} from "@ant-design/icons";
import { InputNumber } from "components/Input/InputNumber";
import { requiredRule } from "utils/validateRule";
import { cloneDeep, fromPairs, update } from "lodash";
import { componentApi } from "api/component.api";
import { $url } from "utils/url";
import { useComponent } from "hooks/useComponent";
import ChooseFileFromMenu from "components/Upload/ChooseImageFromMenu";
import {
  BOM,
  MainComponent,
  Product,
  ProductCreating,
  ProductDetail,
  ProductStatus,
  ProductStyle,
  ProductStyleDetail,
  ProductType,
  ProductTypeTrans,
} from "types/product";
import { productApi } from "api/product.api";
import { IoAddCircleOutline, IoAddOutline } from "react-icons/io5";
import {
  AddBillOfMaterialModal,
  BillOfMaterialCreating,
  BillOfMaterialRef,
  BillOfMaterialUpdate,
} from "./AddBillOfMaterialModal";
import { ComponentsSelector } from "components/Selector/ComponentsSelector";
import Column from "antd/es/table/Column";
import ProductDetailSection from "./ProductDetailSection";
import {
  AddMainComponentModal,
  mainComponentRef,
} from "./AddMainComponentModal";
import { CreateStyleModal, StyleOfProduct, StyleRef } from "./CreateStyleModal";
import ImportBillOfMaterial, {
  ImportBillOfMaterialModal,
} from "components/ImportDocument/ImportBillOfMaterial";
import { removeSubstringFromKeys } from "utils/common";
import { bomApi } from "api/bom";
import { useBom } from "hooks/useBOM";
import {
  Component,
  DisplayImageTypeTrans,
  ZoomPositionTrans,
} from "types/component";
import { useMaterial } from "hooks/useMaterial";
import DraggableTable from "components/Table/DraggableTable";
import { BMDTextArea } from "components/TextArea/BMDTextArea";

export interface ComponentModal {
  handleCreate: () => void;
  handleUpdate: (product: Product) => void;
}
interface ComponentModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
  isCombo?: boolean;
  refetchData: () => void;
}
export const CreateProductModal = React.forwardRef(
  (
    { onClose, onSubmitOk, isCombo = false, refetchData }: ComponentModalProps,
    ref
  ) => {
    const [form] = Form.useForm<ProductCreating>();
    const icon = useWatch("fileAttachIcon", form);
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");
    const [idOfDeletingMainComponent, setIdOfDeletingMainComponent] =
      useState<number>();
    const [billOfMaterial, setBillOfMaterial] = useState<
      BillOfMaterialCreating[]
    >([]);
    const [currentProduct, setCurrentProduct] = useState<Product>();
    const [mainComponentArray, setMainComponentArray] = useState<
      Partial<Component>[]
    >([]);
    const [sortMainComponentArray, setSortMainComponentArray] = useState<
      Partial<Component>[]
    >([]);
    const [sortModalVisible, setSortModalVisible] = useState(false);

    const [styleOfProductArray, setStyleOfProductArray] = useState<
      StyleOfProduct[]
    >([]);
    const [mainComponentArrayOrigin, setMainComponentArrayOrigin] = useState<
      Partial<Component>[]
    >([]);

    const handleGetOneProduct = async (id: number) => {
      try {
        if (id) {
          const { data } = await productApi.findOne(id);
          console.log("Data khi get one là", data);
          return data;
        } else {
          return {};
        }
      } catch (error) {
        console.log(error);
      }
    };
    useImperativeHandle<any, ComponentModal>(
      ref,
      () => ({
        handleCreate() {
          form.resetFields();
          setVisible(true);
          setStatus("create");
          form.setFieldValue("type", isCombo ? "COMBO" : "SINGLE");
          setMainComponentArray([]);
          setBillOfMaterial([]);
          setStyleOfProductArray([]);
        },
        handleUpdate(product) {
          console.log("Original product is", product);
          handleGetOneProduct(product.id).then((res: Product) => {
            setStyleOfProductArray(
              res.productStyles.map(
                (item): StyleOfProduct => ({
                  id: item.id,
                  name: item.name,
                  fileAttach: item.fileAttach,
                  description: item.description,
                  productStyleDetails: item.productStyleDetails,
                })
              )
            );
            setMainComponentArray(
              res.mainComponents.map((item) => ({
                ...item,
              }))
            );
            setMainComponentArrayOrigin(
              res.mainComponents.map((item) => ({
                ...item,
              }))
            );
          });
          setCurrentProduct(product);
          setBillOfMaterial(
            product.boms.map((bom) => ({
              id: bom.id,
              materialCode: bom.material?.code,
              materialName: bom.material?.name,
              materialId: bom.material?.id,
              quantity: bom?.quantity,
            }))
          );

          form.setFieldsValue({
            ...product,
            boms: product.boms || [],
            mainComponents: product.mainComponents || [],
          });

          setVisible(true);
          setStatus("update");
        },
      }),
      []
    );
    const createData = async () => {
      const valid = await form.validateFields();
      const data = form.getFieldsValue();
      const { parentId, boms, mainComponents, fileAttachIcon, ...restData } =
        data;
      try {
        const res = await productApi.create({
          product: {
            ...restData,
          },
          parentId: undefined,
          fileAttachIconId: fileAttachIcon.id,
          boms:
            billOfMaterial.map((item) => ({
              id: item.id,
              materialName: item.materialName,
              materialCode: item.materialCode,
              quantity: item.quantity,
            })) || [],
          mainComponent: mainComponentArray.map((item) => ({
            name: item.name,
            layer: item.layer,
            parentId: item.parent?.id || 0,
          })),
          productStyles: styleOfProductArray,
        });
        message.success("Tạo thành công");
        handleClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };
    const updateData = async () => {
      const data = form.getFieldsValue();

      setLoading(true);

      const {
        id,
        parentId,
        boms,
        mainComponents,
        fileAttachIcon,
        ...restData
      } = data;

      try {
        // if (idOfDeletingMainComponent) {
        //   handleDeleteMainComponent();
        // }
        await changeProductStatus(data);
        const res = await productApi.update(id, {
          product: { ...restData },
          fileAttachIconId: fileAttachIcon?.id,
          boms:
            billOfMaterial?.map((item) => ({
              materialCode: item.materialCode,
              quantity: item.quantity,
            })) || [],
          parentId: data?.parentId,
          mainComponents: mainComponentArray.map((it) => ({
            id: it.id,
            position: it.position,
            name: it.name,
          })),
          // mainComponent: mainComponentArray.map((item) => {
          //   if (mainComponentArrayOrigin.find((it) => it.id == item.id)) {
          //     console.log("Vào đây không", item);
          //     const { parent, ...restData } = item;
          //     //@ts-ignore
          //     return { ...restData, parentId: item?.parent?.id || 0 };
          //   } else {
          //     console.log("Vào đây không", item);

          //     return {
          //       name: item.name,
          //       layer: item.layer,
          //       //@ts-ignore
          //       parentId: item?.parent?.id || 0,
          //       zoomPosition: item.zoomPosition,
          //     };
          //   }
          // }),
          // mainComponent: mainComponentArray,
          productStyles: styleOfProductArray.map((item: StyleOfProduct) => ({
            name: item.name,
            productStyleDetails: item.productStyleDetails?.map((item) => ({
              componentGroupId: item.componentGroup?.id,
              componentId: item.component?.id,
              variantId: item.variant?.id,
            })),
            description: item.description,
            fileAttachId: item.fileAttach?.id,
          })),
        });
        message.success("Cập nhật thành công");
        handleClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    const handleClose = () => {
      onClose();
      setVisible(false);
      form.resetFields();
      setSortMainComponentArray([]);
      setMainComponentArray([]);
      setMainComponentArrayOrigin([]);
      setStyleOfProductArray([]);
      setCurrentProduct(undefined);
      setBillOfMaterial([]);
    };
    const handleAddBOM = (item: BillOfMaterialCreating) => {
      const exists = billOfMaterial.some(
        (i) => i.materialId === item.materialId
      );

      if (!exists) {
        setBillOfMaterial((prev) => [...prev, { ...item }]);
        message.success("Thêm mới định mức nguyên vật liệu thành công");
      } else {
        message.warning(
          `Mã nguyên vật liệu ${item.materialCode} đã tồn tại trong danh sách.`
        );
      }
    };
    const handleUpdateItem = (
      updatedItem: BillOfMaterialCreating,
      prevItem: BillOfMaterialCreating
    ) => {
      // Kiểm tra xem có item khác có cùng id không (khác với chính prevItem)
      const isDuplicate = billOfMaterial.some(
        (item) =>
          item.materialId === updatedItem.materialId &&
          item.materialId !== prevItem.materialId
      );

      if (isDuplicate) {
        message.error(
          `Đã tồn tại mã nguyên vật liệu ${updatedItem?.materialCode}, không thể cập nhật.`
        );
        return;
      }

      const index = billOfMaterial.findIndex((item) => item.id === prevItem.id);
      if (index !== -1) {
        const newList = cloneDeep(billOfMaterial);
        newList[index] = updatedItem;
        setBillOfMaterial(newList);
        message.success("Cập nhật định mức nguyên vật liệu thành công");
      }
    };
    const updateDataNotCloseModal = async (
      mainComponentData: MainComponent
    ) => {
      console.log("main comppnent data nahn65 d9c la", mainComponentData);
      const { parent, ...finalData } = mainComponentData;
      setLoading(true);
      // const { id, parentId, boms, mainComponents, ...restData } = data;

      try {
        const res = await componentApi.create({
          component: mainComponentData,
          parentId: parent?.id || 0,
          isMainComponent: true,
          productMainComponentId: currentProduct?.id,
        });
        console.log("Sau khi tạo main component là", res);
        //@ts-ignore
        setMainComponentArray((prev) => [...prev, res.data]);
        message.success("Thêm mới thành phần chính thành công");
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };
    const handleAddMainComponentItem = (item: Partial<Component>) => {
      const isDuplicate = mainComponentArray.some(
        (m) => m.name?.trim().toLowerCase() === item.name?.trim().toLowerCase()
      );

      if (isDuplicate) {
        message.warning("Thành phần đã tồn tại, không thể thêm.");
        return;
      }

      const newArray = [...mainComponentArray, { ...item }];
      // setMainComponentArray(newArray);
      //@ts-ignore
      updateDataNotCloseModal(item);
    };

    // useEffect(() => {
    //   if (mainComponentArray.length > 0) {
    //     updateDataNotCloseModal();
    //   }
    // }, [mainComponentArray]);
    const handleUpdateMainComponentItem = async (
      updatedItem: Partial<Component>
    ) => {
      // debugger
      const current = mainComponentArray.find(
        (item) => item.id == updatedItem.id
      );
      if (current) {
        Object.assign(current, updatedItem);
        setMainComponentArray(cloneDeep(mainComponentArray));
        // @ts-ignore
        const { id, parent, ...dataForUpdate } = updatedItem;
        console.log("Data for update là", dataForUpdate);
        try {
          const res = await componentApi.update(updatedItem?.id!, {
            component: { ...dataForUpdate },
            parentId: parent?.id || 0,
            // isMainComponent: true,
            // productId: currentProduct?.id,
          });
          console.log("Sau khi tạo main component là", res);
          //@ts-ignore
          setMainComponentArray((prev) =>
            prev.map((item) => (item.id === updatedItem.id ? res.data : item))
          );
          message.success("Cập nhật thành phần chính thành công");
          onSubmitOk();
        } finally {
          setLoading(false);
        }
      }
      // const current = form.getFieldValue("mainComponents") || [];
      // const updated = current.map((item: any) =>
      //   item.id === updatedItem?.id ? updatedItem : item
      // );
      // form.setFieldValue("mainComponents", updated);
    };
    const handleAddStyleOfProduct = (item: StyleOfProduct) => {
      const exists = styleOfProductArray.some(
        (i) => i.name?.trim().toLowerCase() === item.name?.trim().toLowerCase()
      );

      if (exists) {
        message.warning("Style sản phẩm đã tồn tại trong danh sách.");
        return;
      }

      setStyleOfProductArray((prev) => [...prev, { ...item }]);
    };

    const handleUpdateStyleOfProduct = (updatedItem: StyleOfProduct) => {
      const isDuplicateName = styleOfProductArray.some(
        (item) =>
          item.id !== updatedItem.id &&
          item.name?.trim().toLowerCase() ===
            updatedItem.name?.trim().toLowerCase()
      );

      if (isDuplicateName) {
        message.warning("Tên Style sản phẩm đã tồn tại trong danh sách.");
        return;
      }

      const updatedArray = styleOfProductArray.map((item) =>
        item.id === updatedItem.id ? { ...item, ...updatedItem } : item
      );
      message.success("Cập nhật style sản phẩm thành công.");

      setStyleOfProductArray(updatedArray);
    };
    const {
      components,
      fetchData,
      query,
      loading: componentLoading,
      setQuery,
      total,
    } = useComponent({
      initQuery: {
        page: 1,
        limit: 10,
      },
    });
    useEffect(() => {
      fetchData();
    }, []);
    const modalRef = React.useRef<BillOfMaterialRef>(null);
    const mainComponentModalRef = React.useRef<mainComponentRef>(null);
    const createStyleModalRef = React.useRef<StyleRef>(null);

    const changeProductStatus = async (product: Product) => {
      let response;
      if (product.status == ProductStatus.Active) {
        response = await productApi.active(product.id);
      } else {
        response = await productApi.inactive(product.id);
      }
    };
    const handleDeleteMainComponent = async (productId?: number) => {
      try {
        const { data } = await componentApi.delete(productId || 0);
        message.success("Xóa thành phần chính thành công");
        return false;
      } catch (error) {
        return true;
      }
    };
    const ImportBillOfMaterialRef = useRef<ImportBillOfMaterialModal>(null);
    const handleOnUploadedBOMFile = async (excelData: any, setData: any) => {
      const { results } = excelData;
      const importData = results?.map((item: any) => {
        const refineRow = removeSubstringFromKeys(item, "(*)");
        return {
          rowNum: item.__rowNum__,
          materialCode: refineRow["Mã nguyên vật liệu"],
          quantity: refineRow["Định mức sử dụng"],
          productCode: refineRow["Mã sản phẩm"],
        };
      });
      console.log("importData", importData);
      setData(importData);
    };
    const { boms, fetchData: fetchBOMS } = useBom({
      initQuery: {
        page: 1,
        limit: 10,
      },
    });
    useEffect(() => {
      fetchBOMS();
    }, []);
    const handleDeleteBOM = async (id: number) => {
      try {
        const { data } = await bomApi.delete(id);
        message.success("Xóa định mức nguyên vật liệu thành công");
      } catch (error) {
        console.log(error);
      }
    };

    return (
      <Modal
        onCancel={() => {
          handleClose();
        }}
        open={visible}
        title={status == "create" ? "Tạo sản phẩm" : "Cập nhật sản phẩm"}
        style={{ top: 20 }}
        width={1200}
        confirmLoading={loading}
        onOk={() => {
          status == "create" ? createData() : updateData();
        }}
      >
        <Form layout="vertical" form={form}>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="Avatar"
                name="fileAttachIcon"
                rules={[requiredRule]}
              >
                <ChooseFileFromMenu
                  fileUrl={icon?.url}
                  onSelectOk={(url, file) => {
                    console.log("File lấy đc là", file);
                    form.setFieldValue("fileAttachIcon", file);
                  }}
                  ratioText="Tỉ lệ 1x1"
                  fileName={icon?.name}
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <h2>Thông tin sản phẩm</h2>
            </Col>
            <Col span={8}>
              <Form.Item label="Tên nội bộ" name="name" rules={[requiredRule]}>
                <Input placeholder="" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="Tên hiển thị"
                name="nameVi"
                rules={[requiredRule]}
              >
                <Input placeholder="" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="Mã sản phẩm" name="code" rules={[requiredRule]}>
                <Input placeholder="" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="Loại sản phẩm" name="type">
                <Select
                  disabled
                  allowClear
                  options={Object.values(ProductTypeTrans).map((item) => ({
                    label: item.label,
                    value: item.value,
                  }))}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="Giá ($)"
                name="price"
                // rules={[requiredRule]}
              >
                <InputNumber />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="Trạng thái"
                name="status"
                valuePropName="checked"
                getValueProps={(value) => ({
                  checked: value === ProductStatus.Active,
                })}
              >
                <Switch
                  checkedChildren="Hiện" // Khi switch đang bật (true)
                  unCheckedChildren="Ẩn"
                  onChange={(value) => {
                    console.log({ value });
                    if (value) {
                      form.setFieldValue("status", ProductStatus.Active);
                    } else {
                      form.setFieldValue("status", ProductStatus.Inactive);
                    }
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                label="Ghi chú"
                name="note"
                //  rules={[requiredRule]}
              >
                <BMDTextArea placeholder="Ghi chú" />
              </Form.Item>
            </Col>
            {[
              status === "update" && (
                <Col span={8}>
                  <Form.Item
                    className="hidden"
                    label="id"
                    name="id"
                    rules={[requiredRule]}
                  >
                    <Input placeholder="" />
                  </Form.Item>
                </Col>
              ),
            ]}
            <div
              className={`w-full ${status == "update" ? "block" : "hidden"}`}
            >
              {/* <Col span={24}>
                <div className="flex items-center gap-2">
                  <h2>Định mức nguyên vật liệu</h2>
                  <span
                    className="flex items-center cursor-pointer hover:text-green-500"
                    onClick={() => {
                      modalRef.current?.handleCreate();
                    }}
                  >
                    <IoAddCircleOutline className="text-[25px]" />
                  </span>
                  <span>
                    <Button
                      type="primary"
                      onClick={() => {
                        ImportBillOfMaterialRef.current?.open();
                      }}
                    >
                      Nhập file excel
                    </Button>
                  </span>
                </div>
              </Col> */}
              {/* <Col span={24}>
                <Table
                  pagination={false}
                  rowKey="id"
                  dataSource={billOfMaterial}
                  scroll={{ x: "max-content" }}
                >
                  <Column
                    title="Nguyên vật liệu"
                    dataIndex="code"
                    align="left"
                    key="code"
                    width={500}
                    render={(text, record: BillOfMaterialCreating) => {
                      return <div>{record.materialCode}</div>;
                    }}
                  />

                  <Column
                    title="Định mức sử dụng"
                    dataIndex="name"
                    align="left"
                    key="name"
                    render={(text, record: BillOfMaterialUpdate) => {
                      return <div>{record.quantity}</div>;
                    }}
                  />
                  <Column
                    fixed="right"
                    width={120}
                    title="Thao tác"
                    key="action"
                    align="center"
                    render={(text, record: BillOfMaterialCreating) => (
                      <div className="flex gap-2">
                        <Popconfirm
                          onConfirm={() => {
                            setBillOfMaterial(
                              cloneDeep(
                                billOfMaterial.filter(
                                  (item) => item.id !== record.id
                                )
                              )
                            );
                            handleDeleteBOM(record.id);
                          }}
                          title="Xác nhận xóa"
                        >
                          <Button danger>
                            <DeleteOutlined />
                          </Button>
                        </Popconfirm>
                        <Button
                          type="primary"
                          onClick={() => {
                            modalRef.current?.handleUpdate(record);
                          }}
                        >
                          <EditOutlined />
                        </Button>
                      </div>
                    )}
                  />
                </Table>
              </Col> */}
              <Col span={24}>
                <div className="flex justify-between gap-2 items-center">
                  <div className="flex items-center gap-2">
                    <h2>Thành phần chính</h2>
                    <span
                      className="flex items-center cursor-pointer hover:text-green-500"
                      onClick={() => {
                        mainComponentModalRef.current?.handleCreate(
                          currentProduct?.id!
                        );
                      }}
                    >
                      <IoAddCircleOutline className="text-[25px]" />
                    </span>
                  </div>
                  <Button
                    type="primary"
                    size="small"
                    onClick={() => {
                      setSortMainComponentArray(cloneDeep(mainComponentArray));
                      setSortModalVisible(true);
                    }}
                  >
                    Sắp xếp
                  </Button>
                </div>
              </Col>
              <Col span={24}>
                <Table
                  pagination={false}
                  rowKey="id"
                  dataSource={mainComponentArray}
                  scroll={{ x: "max-content" }}
                >
                  <Column
                    title="Tên thành phần"
                    dataIndex="name"
                    align="left"
                    key="name"
                    width={400}
                    render={(text, record: MainComponent) => {
                      return <div>{record.name}</div>;
                    }}
                  />
                  <Column
                    title="Truy xuất hình hiển thị"
                    dataIndex="displayImage"
                    align="left"
                    key="displayImage"
                    width={180}
                    render={(text, record: MainComponent) => {
                      return (
                        <div>
                          {DisplayImageTypeTrans[record.displayImage]?.label}
                        </div>
                      );
                    }}
                  />
                  <Column
                    title="Thứ tự hiển thị"
                    dataIndex="position"
                    align="right"
                    key="position"
                    render={(text, record: MainComponent) => {
                      return <div>{record.position}</div>;
                    }}
                  />
                  <Column
                    title="Ẩn bước cấu hình"
                    dataIndex="isDefaultConfig"
                    align="right"
                    key="isDefaultConfig"
                    render={(text, record: MainComponent) => {
                      return (
                        <Checkbox disabled checked={record.isDefaultConfig} />
                      );
                    }}
                  />
                  <Column
                    title="Thêu"
                    dataIndex="position"
                    align="right"
                    key="position"
                    render={(text, record: MainComponent) => {
                      return (
                        <Checkbox disabled checked={record.isEmbroidery} />
                      );
                    }}
                  />
                  <Column
                    title="Layer"
                    dataIndex="code"
                    align="right"
                    key="code"
                    render={(text, record: MainComponent) => {
                      return (
                        <div className="w-full text-right">{record.layer}</div>
                      );
                    }}
                  />

                  <Column
                    title="Vị trí zoom"
                    dataIndex="zoomPosition"
                    align="left"
                    key="zoomPosition"
                    render={(text, record: Component) => {
                      return (
                        <div>
                          {ZoomPositionTrans[record.zoomPosition]?.label}
                        </div>
                      );
                    }}
                  />
                  {/* <Column
                    title="Nhóm cha"
                    dataIndex="parent"
                    align="left"
                    key="parent"
                    width={300}
                    render={(text, record: MainComponent) => {
                      return <div>{record.parent?.name}</div>;
                    }}
                  /> */}
                  <Column
                    fixed="right"
                    width={120}
                    title="Thao tác"
                    key="action"
                    align="center"
                    render={(text, record: MainComponent) => (
                      <div className="flex gap-2">
                        <Popconfirm
                          onConfirm={async () => {
                            const isError = await handleDeleteMainComponent(
                              record.id
                            );
                            if (isError) {
                              return;
                            } else {
                              setIdOfDeletingMainComponent(record.id);
                              setMainComponentArray(
                                cloneDeep(
                                  mainComponentArray.filter(
                                    (item) => item.id !== record.id
                                  )
                                )
                              );
                            }
                          }}
                          title="Xác nhận xóa"
                        >
                          <Button danger>
                            <DeleteOutlined />
                          </Button>
                        </Popconfirm>
                        <Button
                          type="primary"
                          onClick={() => {
                            console.log("record lúc này là", record);
                            mainComponentModalRef.current?.handleUpdate(
                              record,
                              currentProduct?.id!
                            );
                          }}
                        >
                          <EditOutlined />
                        </Button>
                      </div>
                    )}
                  />
                </Table>
                {/* </Form.Item> */}
              </Col>
              <Col span={24}>
                <div className="flex items-center gap-2">
                  <h2>Cấu hình style</h2>
                  <span
                    className="flex items-center cursor-pointer hover:text-green-500"
                    onClick={() => {
                      createStyleModalRef.current?.handleCreate(
                        mainComponentArray
                      );
                    }}
                  >
                    <IoAddCircleOutline className="text-[25px]" />
                  </span>
                </div>
              </Col>
              <Col span={24}>
                <Table
                  pagination={false}
                  rowKey="id"
                  dataSource={styleOfProductArray}
                  scroll={{ x: "max-content" }}
                >
                  <Column
                    title="Tên Style"
                    dataIndex="name"
                    align="left"
                    key="name"
                    width={900}
                    render={(text, record: StyleOfProduct) => {
                      return <div>{record.name}</div>;
                    }}
                  />
                  <Column
                    fixed="right"
                    width={120}
                    title="Thao tác"
                    align="center"
                    key="action"
                    render={(text, record: any) => (
                      <div className="flex gap-2">
                        <Popconfirm
                          onConfirm={() => {
                            setStyleOfProductArray(
                              cloneDeep(
                                styleOfProductArray.filter(
                                  (item) => item.id !== record.id
                                )
                              )
                            );
                            message.success("Xóa style thành công");
                          }}
                          title="Xác nhận xóa"
                        >
                          <Button danger>
                            <DeleteOutlined />
                          </Button>
                        </Popconfirm>
                        <Button
                          type="primary"
                          onClick={() => {
                            createStyleModalRef.current?.handleUpdate(record);
                          }}
                        >
                          <EditOutlined />
                        </Button>
                      </div>
                    )}
                  />
                </Table>
                {/* </Form.Item> */}
              </Col>
            </div>
          </Row>
        </Form>
        <Modal
          width={800}
          open={sortModalVisible}
          title="Sắp xếp danh sách thành phần"
          cancelText="Đóng"
          onCancel={() => {
            setSortModalVisible(false);
          }}
          style={{ top: 20 }}
          okText="Áp dụng"
          onOk={() => {
            setMainComponentArray(
              cloneDeep(sortMainComponentArray).map((it, index) => ({
                ...it,
                position: index,
              }))
            );
            setSortModalVisible(false);
          }}
        >
          <DraggableTable
            dataSource={sortMainComponentArray}
            onChangeOrder={(newList) => {
              setSortMainComponentArray(
                cloneDeep(newList).map((it, index) => ({
                  ...it,
                  position: index,
                }))
              );
            }}
            columns={[
              {
                title: "Tên sản phẩm",
                dataIndex: "name",
                key: "name",
                render: (_, record) => <div>{record.name}</div>,
              },
              {
                title: "Thứ tự hiển thị",
                dataIndex: "name",
                key: "name",
                render: (_, record) => <div>{record.position}</div>,
              },
            ]}
          />
        </Modal>
        <AddBillOfMaterialModal
          onSubmitOk={handleAddBOM}
          onClose={() => {}}
          ref={modalRef}
          onUpdate={handleUpdateItem}
        />
        <AddMainComponentModal
          onSubmitOk={handleAddMainComponentItem}
          onClose={() => {}}
          ref={mainComponentModalRef}
          onUpdate={handleUpdateMainComponentItem}
        />
        <CreateStyleModal
          // onSubmitOk={(item: StyleOfProduct) => {
          //   console.log("Style of product nhận đc lòa", item);
          //   styleOfProductArray.push({ ...item });
          //   setStyleOfProductArray(cloneDeep(styleOfProductArray));
          // }}
          onSubmitOk={handleAddStyleOfProduct}
          onClose={() => {}}
          ref={createStyleModalRef}
          onUpdate={handleUpdateStyleOfProduct}
          productId={currentProduct?.id!}
          //@ts-ignore
          mainComponentArray={mainComponentArray}
        />
        {useMemo(
          () => (
            <ImportBillOfMaterial
              guide={[
                "Vui lòng tải và sử dụng file mẫu để import dữ liệu đúng cách",
                "Không được thay đổi tiêu đề trong file csv/excel mẫu để tránh import thiếu dữ liệu",
                "Chi tiết xem tại sheet Hướng dẫn của file import mẫu",
              ]}
              onSuccess={() => {
                // query.page = 1;
                // fetchData();
                handleGetOneProduct(currentProduct?.id!).then((res) => {
                  setBillOfMaterial(
                    res.boms.map((bom: BOM) => ({
                      id: bom.id,
                      materialCode: bom.material?.code,
                      materialName: bom.material?.name,
                      materialId: bom.material?.id,
                      quantity: bom?.quantity,
                    }))
                  );
                });
                // message.success("Nhập dữ liệu thành công.");
              }}
              ref={ImportBillOfMaterialRef}
              createApi={bomApi.create}
              onUploaded={(excelData, setData) => {
                console.log("up gì lên vậy", excelData);
                handleOnUploadedBOMFile(excelData, setData);
              }}
              productId={currentProduct?.id!}
              okText={`Nhập định mức NVL ngay`}
              demoExcel="/exportFile/Mau_nhap_dinh_muc_NVL.xlsx"
            />
          ),
          [currentProduct?.id]
        )}
      </Modal>
    );
  }
);
