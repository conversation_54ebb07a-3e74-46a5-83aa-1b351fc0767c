import { Avatar, Card, Col, Divider, Form, Input, message, Row } from "antd";
import { authApi } from "api/auth.api";
import CustomButton from "components/Button/CustomButton";
import { observer } from "mobx-react";
import { useEffect, useRef, useState } from "react";
import { userStore } from "store/userStore";
import { getTitle } from "utils";
import { $url } from "utils/url";
import { UpdateProfileModal } from "./UpdateProfileModal";

export const ProfilePage = observer(({ title }: { title: string }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const updateProfileModalRef = useRef<UpdateProfileModal>();

  useEffect(() => {
    document.title = getTitle(title);
  }, []);

  //handle submit form
  const onFinish = async (values: any) => {
    const oldPassword = form.getFieldValue("oldPassword");
    const newPassword = form.getFieldValue("newPassword");
    setLoading(true);

    try {
      const res = await authApi.passwordUpdate({
        oldPassword,
        newPassword,
      });
      form.resetFields();
      message.success("Cập nhật mật khẩu mới thành công!");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <Row gutter={16}>
        <Col className="gutter-row" span={12}>
          <Card
            title="Thông tin"
            className="!rounded-md !h-[calc(100vh-112px)]"
          >
            <div
              className="card-avatar"
              style={{ display: "flex", alignItems: "center" }}
            >
              <Avatar
                src={$url(userStore.info.avatar)}
                icon={userStore.info.fullName?.[0]}
                style={{
                  color: "#f56a00",
                  backgroundColor: "#fde3cf",
                  verticalAlign: "middle",
                  margin: "auto",
                }}
                size={100}
              />
            </div>

            <Divider orientation="left" orientationMargin="0">
              Thông tin khác
            </Divider>
            <p>
              <b>Họ và tên:</b> {userStore.info.fullName}
            </p>
            <p>
              <b>Số điện thoại:</b> {userStore.info.phone || "Chưa cập nhật"}
            </p>
            <p>
              <b>Email: </b> {userStore.info.email || "Chưa cập nhật"}
            </p>

            <CustomButton
              loading={loading}
              onClick={() =>
                updateProfileModalRef.current?.handleUpdate(userStore.info)
              }
            >
              Cập nhật thông tin
            </CustomButton>
          </Card>
        </Col>
        <Col className="gutter-row" span={12}>
          <Card
            title="Đổi mật khẩu"
            className="!rounded-md !h-[calc(100vh-112px)]"
          >
            <Form
              form={form}
              onFinish={onFinish}
              //   onFinishFailed={onFinishFailed}
              layout="vertical"
            >
              <Form.Item
                label="Mật khẩu cũ"
                name="oldPassword"
                required
                rules={[
                  {
                    required: true,
                    message: "Bắt buộc",
                  },
                ]}
              >
                <Input.Password />
              </Form.Item>

              <Form.Item
                name="newPassword"
                label="Mật khẩu mới"
                required
                rules={[
                  {
                    required: true,
                    message: "Bắt buộc",
                  },
                ]}
              >
                <Input.Password />
              </Form.Item>

              <Form.Item
                name="renewPassword"
                label="Nhập lại mật khẩu mới"
                required
                rules={[
                  {
                    required: true,
                    message: "Bắt buộc",
                  },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue("newPassword") === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(
                        new Error("Mật khẩu mới không khớp!")
                      );
                    },
                  }),
                ]}
              >
                <Input.Password />
              </Form.Item>
              <Form.Item style={{ marginTop: 16 }}>
                <CustomButton loading={loading} htmlType="submit">
                  Đổi mật khẩu
                </CustomButton>
              </Form.Item>
            </Form>
          </Card>
        </Col>
      </Row>
      <UpdateProfileModal
        ref={updateProfileModalRef}
        onClose={function (): void {
          throw new Error("Function not implemented.");
        }}
        onSubmitOk={() => userStore.getProfile()}
      />
    </div>
  );
});
