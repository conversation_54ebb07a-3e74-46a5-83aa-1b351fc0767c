import React, { useState, useEffect, useMemo } from "react";
import {
  Card,
  Row,
  Col,
  Form,
  message,
  Space,
  Spin,
  Input,
  Select,
} from "antd";
import { materialApi } from "api/material.api";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import CustomButton from "components/Button/CustomButton";
import { EMaterialType, Material } from "types/material";
import { FileAttach } from "types/fileAttach";
import { useTheme } from "context/ThemeContext";
import { useProvider } from "hooks/useProvider";
import { useUnit } from "hooks/useUnit";
import { useMaterialGroup } from "hooks/useMaterialGroup";
import { useBrand } from "hooks/useBrand";
import { MaterialUsage } from "types/materialUsage";
import { GoodsOriginTrans, ProductOrigin } from "types/productOrigin";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import PageTitle from "components/PageTitle/PageTitle";
import ActiveStatusTagSelect from "components/ActiveStatus/ActiveStatusTagSelect";
import { ModalStatus } from "types/modal";
import { isEmpty } from "lodash";
import { getTitle } from "utils";
import { Rule } from "antd/lib/form";
import { DictionarySelector } from "components/Selector/DictionarySelector";
import { DictionaryType } from "types/dictionary";
import { UnitSelector } from "components/Selector/UnitSelector";
import { ProviderSelector } from "components/Selector/ProviderSelector";
import { PermissionNames } from "types/PermissionNames";
import { BrandSelector } from "components/Selector/BrandSelector";
import clsx from "clsx";
import { TextInput } from "components/Input/TextInput";
import { InputNumber } from "components/Input/InputNumber";
import { $url } from "utils/url";
import { checkRoles } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { observer } from "mobx-react";
import { BMDTextArea } from "components/TextArea/BMDTextArea";
import { BMDCKEditor } from "components/Editor";

interface CreateMaterialPayload {
  unitId: number;
  providerId: number;
  brandId: number;
  materialGroupId: number;
  material: {
    code: string;
    name: string;
    type: string;
    description: string;
    oldSystemCode: string;
    supplierProductCode: string;
    usageForm: string;
    productOrigin: string;
    lengthMm: number;
    widthMm: number;
    heightMm: number;
    thicknessMm: number;
    diameterMm: number;
    otherDimensions: string;
    salePrice: number;
    purchasePrice: number;
    taxPercent: number;
    trackInventory: boolean;
    inventoryAccount: string;
    revenueAccount: string;
    salesReturnAccount: string;
    cogsAccount: string;
    discountAccount: string;
    minQuantity: number;
    maxQuantity: number;
    shelfLifeDays: number;
    isActive: boolean;
    avatar: string;
  };
}

interface CreateOrEditGoodsPageProps {
  title: string;
  status: ModalStatus;
  type: EMaterialType;
}

interface MaterialForm {
  id?: number;
  code: string;
  name: string;
  description: string;
  oldSystemCode?: string;
  supplierProductCode?: string;
  usageForm?: string;
  productOrigin?: string;
  lengthMm?: string;
  widthMm?: string;
  heightMm?: string;
  thicknessMm?: string;
  diameterMm?: string;
  otherDimensions?: string;
  salePrice?: string;
  purchasePrice?: string;
  taxPercent?: string;
  trackInventory?: boolean;
  inventoryAccount?: string;
  revenueAccount?: string;
  salesReturnAccount?: string;
  cogsAccount?: string;
  discountAccount?: string;
  minQuantity?: string;
  maxQuantity?: string;
  shelfLifeDays?: string;
  isActive?: boolean;
  unitId?: number;
  providerId?: number;
  brandId?: number;
  materialGroupId?: number;
  businessUnitId?: number;
  image?: string;
  type?: string;
  avatar?: string;
}

const rules: Rule[] = [{ required: true }];

export const CreateOrUpdateGoodsPage: React.FC<CreateOrEditGoodsPageProps> = ({
  title = "",
  status,
  type = EMaterialType.Product,
}) => {
  const isProduct = type === EMaterialType.Product;

  const { haveEditPermission } = checkRoles(
    {
      edit: isProduct
        ? PermissionNames.goodsEdit
        : PermissionNames.materialEdit,
    },
    permissionStore.permissions
  );

  const { darkMode } = useTheme();
  const [form] = Form.useForm<MaterialForm>();
  const [loading, setLoading] = useState(false);
  const [loadingFetch, setLoadingFetch] = useState(false);
  const [imageUrl, setImageUrl] = useState<string>("");
  const [selectedMaterial, setSelectedMaterial] = useState<Material>();
  const [readonly, setReadonly] = useState(true);
  const [searchParams, setSearchParams] = useSearchParams();
  const [descriptionContent, setDescriptionContent] = useState<string>("");
  const descriptionRules: Rule[] = [{ required: false }];

  const navigate = useNavigate();
  const params = useParams();

  const setDataToForm = (data: Material) => {
    form.setFieldsValue({
      ...data,
      unitId: data.unit?.id,
      providerId: data.provider?.id,
      brandId: data.brand?.id,
      materialGroupId: data.materialGroup?.id,
      businessUnitId: data.unit?.id,
      image: data.image,
      lengthMm: data.lengthMm?.toString(),
      widthMm: data.widthMm?.toString(),
      heightMm: data.heightMm?.toString(),
      thicknessMm: data.thicknessMm?.toString(),
      diameterMm: data.diameterMm?.toString(),
      otherDimensions: data.otherDimensions,
      purchasePrice: data.purchasePrice?.toString(),
      salePrice: data.salePrice?.toString(),
      taxPercent: data.taxPercent?.toString(),
      trackInventory: data.trackInventory,
      inventoryAccount: data.inventoryAccount,
      revenueAccount: data.revenueAccount,
      salesReturnAccount: data.salesReturnAccount,
      cogsAccount: data.cogsAccount,
      discountAccount: data.discountAccount,
      minQuantity: data.minQuantity?.toString(),
      maxQuantity: data.maxQuantity?.toString(),
      shelfLifeDays: data.shelfLifeDays?.toString(),
      isActive: data.isActive,
    });
  };

  const getOneMaterial = async (id: number) => {
    try {
      setLoadingFetch(true);
      const { data } = await materialApi.findOne(id);

      if (isEmpty(data)) {
        navigate("/404");
        return;
      }

      setSelectedMaterial(data);
      setImageUrl(data.avatar || "");
      setDataToForm(data);
    } catch (e: any) {
      message.error("Có lỗi xảy ra khi tải thông tin hàng hóa!");
    } finally {
      setLoadingFetch(false);
    }
  };

  useEffect(() => {
    document.title = getTitle(title);

    if (status === "update") {
      const materialId = params.id;
      if (materialId) {
        getOneMaterial(+materialId);
        setReadonly(searchParams.get("update") != "1");
      } else {
        navigate("/404");
      }
    } else {
      setReadonly(false);
    }
  }, [status, params.id, title]);

  const handleImageUpload = (fileAttach: FileAttach) => {
    const newImageUrl = fileAttach.path || fileAttach.url || "";
    setImageUrl(newImageUrl);
  };

  const getDataSubmit = () => {
    const {
      unitId,
      providerId,
      brandId,
      materialGroupId,
      businessUnitId,
      avatar,
      ...materialData
    } = form.getFieldsValue();

    const payload: CreateMaterialPayload = {
      unitId: unitId ?? 0,
      providerId: providerId ?? 0,
      brandId: brandId ?? 0,
      materialGroupId: materialGroupId ?? 0,
      material: {
        code: materialData.code || "",
        name: materialData.name || "",
        type: materialData.type || type,
        description: materialData.description || "",
        oldSystemCode: materialData.oldSystemCode || "",
        supplierProductCode: materialData.supplierProductCode || "",
        usageForm: materialData.usageForm || "",
        productOrigin: materialData.productOrigin || "",
        lengthMm: parseFloat(materialData.lengthMm || "0") || 0,
        widthMm: parseFloat(materialData.widthMm || "0") || 0,
        heightMm: parseFloat(materialData.heightMm || "0") || 0,
        thicknessMm: parseFloat(materialData.thicknessMm || "0") || 0,
        diameterMm: parseFloat(materialData.diameterMm || "0") || 0,
        otherDimensions: materialData.otherDimensions || "",
        salePrice: parseFloat(materialData.salePrice || "0") || 0,
        purchasePrice: parseFloat(materialData.purchasePrice || "0") || 0,
        taxPercent: parseFloat(materialData.taxPercent || "0") || 0,
        trackInventory: materialData.trackInventory !== false,
        inventoryAccount: materialData.inventoryAccount || "",
        revenueAccount: materialData.revenueAccount || "",
        salesReturnAccount: materialData.salesReturnAccount || "",
        cogsAccount: materialData.cogsAccount || "",
        discountAccount: materialData.discountAccount || "",
        minQuantity: parseFloat(materialData.minQuantity || "0") || 0,
        maxQuantity: parseFloat(materialData.maxQuantity || "0") || 0,
        shelfLifeDays: parseFloat(materialData.shelfLifeDays || "0") || 0,
        isActive:
          status === "update" && selectedMaterial
            ? selectedMaterial.isActive
            : materialData.isActive !== false,
        avatar: !!imageUrl ? $url(imageUrl) : "",
      },
    };

    return payload;
  };

  const createData = async () => {
    try {
      await form.validateFields();
      setLoading(true);

      await materialApi.create(getDataSubmit());
      if (isProduct) {
        message.success("Tạo hàng hóa thành công!");
        navigate(`/master-data/${PermissionNames.goodsList}`);
      } else {
        message.success("Tạo nguyên vật liệu thành công!");
        navigate(`/master-data/${PermissionNames.materialList}`);
      }
    } catch (error) {
      if (isProduct) {
        message.error("Có lỗi xảy ra khi tạo hàng hóa!");
      } else {
        message.error("Có lỗi xảy ra khi tạo nguyên vật liệu!");
      }
    } finally {
      setLoading(false);
    }
  };

  const updateData = async () => {
    try {
      await form.validateFields();
      setLoading(true);

      await materialApi.update(selectedMaterial!?.id || 0, getDataSubmit());
      if (isProduct) {
        message.success("Chỉnh sửa hàng hóa thành công!");
      } else {
        message.success("Chỉnh sửa nguyên vật liệu thành công!");
      }
    } catch (error) {
      if (isProduct) {
        message.error("Có lỗi xảy ra khi chỉnh sửa hàng hóa!");
      } else {
        message.error("Có lỗi xảy ra khi chỉnh sửa nguyên vật liệu!");
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = () => {
    if (status === "create") {
      createData();
    } else {
      updateData();
    }
  };

  const handleReset = () => {
    form.resetFields();
    setImageUrl("");

    // Reset specific fields that might not be cleared by resetFields
    form.setFieldsValue({
      trackInventory: true, // Reset checkbox to default value
    });
  };

  const pageTitle = useMemo(() => {
    const title = isProduct ? "hàng hóa" : "nguyên vật liệu";
    return status === "create" ? `Tạo ${title}` : `Chỉnh sửa ${title}`;
  }, [status]);

  // Create options for MaterialUsage enum
  const materialUsageOptions = Object.entries(MaterialUsage).map(
    ([key, value]) => {
      let label = key;
      if (value === MaterialUsage.Sell) label = "Bán";
      if (value === MaterialUsage.USE) label = "Sử dụng";

      return {
        label,
        value,
      };
    }
  );

  // Create options for ProductOrigin enum
  const productOriginOptions = Object.entries(ProductOrigin).map(
    ([key, value]) => {
      let label = key;
      if (value === ProductOrigin.Manufacturing) label = "Sản xuất";
      if (value === ProductOrigin.Processing) label = "Gia công";
      if (value === ProductOrigin.DomesticPurchase) label = "Nội địa";
      if (value === ProductOrigin.Import) label = "Nhập khẩu";
      if (value === ProductOrigin.Service) label = "Dịch vụ";

      return {
        label,
        value,
      };
    }
  );

  return (
    <div className="create-goods-page app-container">
      <PageTitle
        back
        breadcrumbs={[
          { label: "Dữ liệu nguồn" },
          {
            label: isProduct
              ? "Danh sách hàng hóa"
              : "Danh sách nguyên vật liệu",
            href: isProduct
              ? `/master-data/${PermissionNames.goodsList}`
              : `/master-data/${PermissionNames.materialList}`,
          },
          { label: pageTitle },
        ]}
        title={pageTitle}
        extra={
          selectedMaterial &&
          status === "update" && (
            <Space>
              <ActiveStatusTagSelect
                disabled={readonly}
                isActive={selectedMaterial?.isActive}
                onChange={(value) => {
                  setSelectedMaterial({
                    ...selectedMaterial,
                    isActive: value,
                  } as Material);
                  form.setFieldsValue({
                    isActive: value,
                  });
                }}
              />
            </Space>
          )
        }
      />

      <Card>
        <Spin spinning={loadingFetch}>
          <Form
            layout="vertical"
            form={form}
            className={clsx(readonly ? "readonly" : "")}
            disabled={readonly}
          >
            <div
              style={{
                display: "flex",
                gap: 20,
              }}
            >
              <div>
                <Form.Item
                  style={{ marginBottom: 0, height: "100%" }}
                  noStyle
                  name="image"
                  className="form-height-full"
                >
                  <SingleImageUpload
                    onUploadOk={(file: FileAttach) => {
                      const newImageUrl = file.path || file.url || "";
                      setImageUrl(newImageUrl);
                      form.setFieldsValue({
                        avatar: newImageUrl,
                      });
                    }}
                    imageUrl={imageUrl}
                    height={"100%"}
                    width={"100%"}
                    className="h-full upload-avatar"
                    hideUploadButton={readonly}
                    disabled={readonly}
                  />
                </Form.Item>
              </div>

              <div
                style={{
                  flex: 1,
                }}
              >
                {/* Row 1: Mã hàng hóa, Tên hàng hóa */}
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      label={isProduct ? "Mã hàng hóa" : "Mã nguyên vật liệu"}
                      name="code"
                    >
                      <TextInput
                        disabled={status == "update"}
                        placeholder={
                          status == "create" ? "Để trống sẽ tự sinh" : ""
                        }
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label={isProduct ? "Tên hàng hóa" : "Tên nguyên vật liệu"}
                      name="name"
                      rules={rules}
                    >
                      <Input
                        placeholder={
                          isProduct
                            ? "Nhập tên hàng hóa"
                            : "Nhập tên nguyên vật liệu"
                        }
                      />
                    </Form.Item>
                  </Col>
                </Row>

                {/* Row 2: Loại hàng hóa (nhóm hàng), Mã hệ thống cũ */}
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      label={isProduct ? "Nhóm hàng hóa" : "Nhóm hàng"}
                      name="materialGroupId"
                      rules={rules}
                    >
                      <DictionarySelector
                        initQuery={{
                          type: isProduct
                            ? DictionaryType.ProductGroup
                            : DictionaryType.MaterialGroup,
                          isActive: true,
                        }}
                        placeholder={
                          isProduct ? "Chọn nhóm hàng hóa" : "Chọn nhóm hàng"
                        }
                        allowClear
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label="Mã hệ thống cũ" name="oldSystemCode">
                      <Input placeholder="Nhập mã hệ thống cũ" />
                    </Form.Item>
                  </Col>
                </Row>

                {/* Row 3: Đơn vị tính, Thương hiệu */}
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item label="Đơn vị tính" name="unitId" rules={rules}>
                      <UnitSelector
                        initQuery={{ isActive: true }}
                        initOptionItem={selectedMaterial?.unit}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label="Thương hiệu" name="brandId">
                      <DictionarySelector
                        placeholder="Chọn thương hiệu"
                        initQuery={{
                          type: DictionaryType.Brand,
                          isActive: true,
                        }}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </div>
            </div>

            {/* Row phía dưới ảnh: Nhà cung cấp, mã SP NCC, hình thức sử dụng, nguồn gốc sản phẩm */}
            <Row gutter={16} className="mt-4">
              <Col span={6}>
                <Form.Item label="Nhà cung cấp" name="providerId">
                  <ProviderSelector
                    placeholder="Chọn nhà cung cấp"
                    initQuery={{ isActive: true }}
                    initOptionItem={selectedMaterial?.provider}
                  />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="Mã SP NCC" name="supplierProductCode">
                  <Input placeholder="Nhập mã sản phẩm NCC" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="Hình thức sử dụng" name="usageForm">
                  <Select
                    placeholder="Chọn hình thức sử dụng"
                    options={materialUsageOptions}
                  />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="Nguồn gốc sản phẩm" name="productOrigin">
                  <Select
                    placeholder="Chọn nguồn gốc sản phẩm"
                    options={Object.values(GoodsOriginTrans)}
                  />
                </Form.Item>
              </Col>
            </Row>

            {/* Row mô tả hàng hóa */}
            <Row className="mt-4">
              <Col span={24}>
                <Form.Item
                  label={isProduct ? "Mô tả hàng hóa" : "Mô tả nguyên vật liệu"}
                  name="description"
                  rules={descriptionRules}
                >
                  <BMDCKEditor
                    placeholder={
                      isProduct
                        ? "Nhập mô tả hàng hóa"
                        : "Nhập mô tả nguyên vật liệu"
                    }
                    inputHeight={300}
                    onChange={(content) => {
                      setDescriptionContent(content);
                    }}
                    value={selectedMaterial?.description}
                    disabled={readonly}
                  />
                </Form.Item>
              </Col>
            </Row>

            {/* Thuộc tính Card */}
            <Card title="Thuộc tính" className="mb-4 form-card mt-4">
              {/* Row 1: Dài, Rộng, Cao, Dày */}
              <Row gutter={16}>
                <Col span={6}>
                  <Form.Item label="Dài (mm)" name="lengthMm" rules={rules}>
                    <InputNumber placeholder="Nhập chiều dài" />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="Rộng (mm)" name="widthMm">
                    <InputNumber placeholder="Nhập chiều rộng" />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="Cao (mm)" name="heightMm">
                    <InputNumber placeholder="Nhập chiều cao" />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="Dày (mm)" name="thicknessMm">
                    <InputNumber placeholder="Nhập độ dày" />
                  </Form.Item>
                </Col>
              </Row>

              {/* Row 2: Đường kính, Kích thước khác */}
              <Row gutter={16}>
                <Col span={6}>
                  <Form.Item label="Đường kính (mm)" name="diameterMm">
                    <InputNumber placeholder="Nhập đường kính" />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="Kích thước khác" name="otherDimensions">
                    <Input placeholder="Nhập kích thước khác" />
                  </Form.Item>
                </Col>
              </Row>
            </Card>

            {/* Thông tin kinh doanh Card */}
            <Card title="Thông tin kinh doanh" className="mb-4 form-card">
              {/* Row 1: Giá mua, Giá bán, Thuế, Đơn vị tính */}
              <Row gutter={16}>
                <Col span={6}>
                  <Form.Item label="Giá mua" name="purchasePrice">
                    <InputNumber placeholder="Nhập giá mua" suffix="VNĐ" />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="Giá bán" name="salePrice">
                    <InputNumber placeholder="Nhập giá bán" suffix="VNĐ" />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="Thuế (%)" name="taxPercent" rules={rules}>
                    <InputNumber placeholder="Nhập % thuế" />
                  </Form.Item>
                </Col>
              </Row>
            </Card>

            {/* Thông tin kho Card */}
            <Card title="Thông tin kho" className="mb-4 form-card">
              {/* Row 1: Số lượng tối thiểu, Số lượng tối đa, Thời gian tồn */}
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item label="Số lượng tối thiểu" name="minQuantity">
                    <InputNumber placeholder="Nhập số lượng tối thiểu" />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label="Số lượng tối đa" name="maxQuantity">
                    <InputNumber placeholder="Nhập số lượng tối đa" />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label="Thời gian tồn (ngày)" name="shelfLifeDays">
                    <InputNumber
                      placeholder="Nhập thời gian tồn"
                      suffix="ngày"
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          </Form>

          {/* Action Buttons */}
          <div className="flex gap-[16px] justify-end mt-2">
            {!readonly && (
              <CustomButton
                variant="outline"
                className="cta-button"
                onClick={() => {
                  if (status == "create") {
                    if (isProduct) {
                      navigate(`/master-data/${PermissionNames.goodsList}`);
                    } else {
                      navigate(`/master-data/${PermissionNames.materialList}`);
                    }
                  } else {
                    setReadonly(true);
                    setDataToForm(selectedMaterial!);
                  }
                }}
              >
                Hủy
              </CustomButton>
            )}

            <CustomButton
              // variant="outline"
              className="cta-button"
              loading={loading}
              onClick={() => {
                if (!readonly) {
                  handleSubmit();
                } else {
                  setReadonly(false);
                }
              }}
              disabled={status == "update" && !haveEditPermission}
            >
              {useMemo(() => {
                const moduleName = isProduct ? "hàng hóa" : "nguyên vật liệu";

                return status == "create"
                  ? "Tạo " + moduleName
                  : readonly
                  ? "Chỉnh sửa"
                  : "Lưu chỉnh sửa";
              }, [status, isProduct, readonly])}
            </CustomButton>
          </div>
        </Spin>
      </Card>
    </div>
  );
};

export default observer(CreateOrUpdateGoodsPage);
