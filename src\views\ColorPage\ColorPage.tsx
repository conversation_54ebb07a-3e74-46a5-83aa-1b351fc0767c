import { PlusOutlined, SearchOutlined } from "@ant-design/icons";
import { Button, Input, message, Popconfirm, Space, Spin, Table } from "antd";
import { colorApi } from "api/color.api";
import { Pagination } from "components/Pagination";
import React, { useEffect, useState, useRef } from "react";
import { ModalStatus } from "types/modal";
import { QueryParam } from "types/query";
import { Color } from "types/color";
import { getTitle } from "utils";
import { $url } from "utils/url";
import { ColorModal } from "./ColorModal";
import { useColor } from "hooks/useColor";

const { ColumnGroup, Column } = Table;

export const ColorPage = ({ title = "" }) => {
  const { colors, fetchData, loading, query, setQuery, total } = useColor({
    initQuery: { limit: 10, page: 1 },
  });
  const [loadingDelete, setLoadingDelete] = useState(false);
  const modalRef = useRef<ColorModal>(null);

  useEffect(() => {
    document.title = getTitle(title);
  }, []);

  useEffect(() => {
    fetchData();
  }, [query]);

  const handleDelete = async (id: number) => {
    try {
      setLoadingDelete(true);
      await colorApi.delete(id);
      message.success("Xóa thành công");

      fetchData();
    } catch (e) {
      console.log({ e });
    } finally {
      setLoadingDelete(false);
    }
  };

  return (
    <div>
      <div className="filter-container">
        <Space>
          <div className="filter-item">
            <label htmlFor="">Tìm kiếm</label>
            <Input
              onKeyDown={(ev) => {
                if (ev.code == "Enter") {
                  query.page = 1;
                  setQuery({ ...query });
                }
              }}
              size="middle"
              onChange={(ev) => {
                query.search = ev.currentTarget.value;
                setQuery({ ...query });
              }}
              placeholder="Tìm kiếm"
            />
          </div>

          <div className="filter-item btn">
            <Button
              onClick={fetchData}
              type="primary"
              icon={<SearchOutlined />}
            >
              Tìm kiếm
            </Button>
          </div>

          <div className="filter-item btn">
            <Button
              onClick={() => {
                modalRef.current?.handleCreate();
              }}
              type="primary"
              icon={<PlusOutlined />}
            >
              Thêm mới
            </Button>
          </div>
        </Space>
      </div>

      <Spin spinning={loading}>
        <Table pagination={false} rowKey="id" dataSource={colors}>
          <Column title="Tên màu" dataIndex="name" key="name" />
          <Column
            title="Mã màu (HEX)"
            dataIndex="hex"
            key="hex"
            render={(hex, record: Color) => {
              return (
                <div className="flex items-center gap-1">
                  <div
                    style={{
                      backgroundColor: !hex.includes("#") ? `#${hex}` : hex,
                    }}
                    className="rounded-md h-[25px] w-[25px]"
                  ></div>{" "}
                  <span>{!hex.includes("#") ? `#${hex}` : hex}</span>
                </div>
              );
            }}
          />

          <Column
            width={180}
            align="center"
            title="Thao tác"
            key="action"
            render={(text, record: Color) => (
              <Space>
                <Popconfirm
                  onConfirm={() => {
                    handleDelete(record.id);
                  }}
                  title="Xác nhận xóa"
                >
                  <Button loading={loadingDelete} danger>
                    Xóa
                  </Button>
                </Popconfirm>
                <Button
                  type="primary"
                  onClick={() => {
                    modalRef.current?.handleUpdate(record);
                  }}
                >
                  Cập nhật
                </Button>
              </Space>
            )}
          />
        </Table>

        <Pagination
          defaultPageSize={query.limit}
          currentPage={query.page}
          total={total}
          onChange={({ limit, page }) => {
            query.page = page;
            query.limit = limit;
            setQuery({ ...query });
          }}
        />
      </Spin>

      <ColorModal onSubmitOk={fetchData} onClose={() => {}} ref={modalRef} />
    </div>
  );
};
