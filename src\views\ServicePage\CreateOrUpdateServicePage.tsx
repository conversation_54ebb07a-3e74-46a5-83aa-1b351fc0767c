import {
  Card,
  Col,
  Form,
  Input,
  message,
  Row,
  Select,
  Space,
  Spin,
  Tabs,
} from "antd";
import { Rule } from "antd/lib/form";
import { serviceApi } from "api/service.api";
import { ProviderSelector } from "components/Selector/ProviderSelector";
import { UnitSelector } from "components/Selector/UnitSelector";
import { FileUploadMultiple } from "components/Upload/FileUploadMultiple";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import { useEffect, useMemo, useState } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { FileAttach } from "types/fileAttach";
import { getTitle } from "utils";
import { FileAttachPayload } from "components/Upload/FileUploadItem";
import { $url } from "utils/url";
import { UploadFile } from "antd/lib";
import { useWatch } from "antd/es/form/Form";
import { Service } from "types/service";
import CustomButton from "components/Button/CustomButton";
import PageTitle from "components/PageTitle/PageTitle";
import ActiveStatusTagSelect from "components/ActiveStatus/ActiveStatusTagSelect";
import { ModalStatus } from "types/modal";
import { isEmpty } from "lodash";
import { PermissionNames } from "types/PermissionNames";
import { DictionaryType } from "types/dictionary";
import { useDictionary } from "hooks/useDictionary";
import clsx from "clsx";
import { InputNumber } from "components/Input/InputNumber";
import TextArea from "antd/es/input/TextArea";
import { TextInput } from "components/Input/TextInput";
import { FileUploadMultiple2 } from "components/Upload/FileUploadMultiple2";
import { fileAttachApi } from "api/fileAttach.api";
import { observer } from "mobx-react";
import { checkRoles } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { BMDTextArea } from "components/TextArea/BMDTextArea";
import { BMDCKEditor } from "components/Editor";

const rules: Rule[] = [{ required: true }];
const descriptionRules: Rule[] = [{ required: false }];

interface EditServicePageProps {
  title: string;
  status: ModalStatus;
}

interface ServiceForm extends Service {
  serviceTypeId: number;
  unitId: number;
  providerId: number;
}

export const CreateOrUpdateServicePage = observer(
  ({ title = "", status }: EditServicePageProps) => {
    const { haveEditPermission } = checkRoles(
      {
        edit: PermissionNames.serviceEdit,
      },
      permissionStore.permissions
    );

    const [form] = Form.useForm<ServiceForm>();
    const [loading, setLoading] = useState(false);
    const navigate = useNavigate();
    useEffect(() => {
      document.title = getTitle(title);
    }, []);
    const [fileList, setFileList] = useState<FileAttach[]>([]);
    const avatar = useWatch("avatar", form);
    const [searchParams, setSearchParams] = useSearchParams();
    const [selectedService, setSelectedService] = useState<Service>();
    const {
      dictionaries: serviceTypes,
      fetchData: fetchServiceTypes,
      setData: setServiceTypes,
    } = useDictionary({
      initQuery: { page: 1, limit: 100, type: DictionaryType.ServiceType },
    });
    const [readonly, setReadonly] = useState(true);
    const [loadingFetch, setLoadingFetch] = useState(false);
    const params = useParams();

    const setDataToForm = (data: Service) => {
      form.setFieldsValue({
        ...data,
        unitId: data.unit?.id,
        providerId: data.provider?.id,
        serviceTypeId: data.serviceType?.id,
      });

      setFileList(data.fileAttaches ? [...data.fileAttaches] : []);
    };

    const getOneService = async (id: number) => {
      try {
        setLoadingFetch(true);
        const { data } = await serviceApi.findOne(id);

        if (isEmpty(data)) {
          navigate("/404");

          return;
        }

        setSelectedService(data);
        setDataToForm(data);

        if (data.serviceType) {
          setServiceTypes([data.serviceType]);
        }

        return data as Service;
      } catch (e: any) {
      } finally {
        setLoadingFetch(false);
      }
    };

    useEffect(() => {
      document.title = getTitle(title);

      if (status == "update") {
        const serviceId = params.id;

        if (serviceId) {
          getOneService(+serviceId).then((service) => {
            fetchServiceTypes().then((serviceTypes) => {
              if (service?.serviceType) {
                const exist = serviceTypes.find(
                  (e) => e.id == service.serviceType.id
                );

                if (!exist) {
                  serviceTypes.unshift(service.serviceType);
                  setServiceTypes([...serviceTypes]);
                }
              }
            });
          });

          setReadonly(searchParams.get("update") != "1");

          // searchParams.delete("serviceId");
          // setSearchParams(searchParams);
        }
      } else {
        fetchServiceTypes();
        setReadonly(false);
      }
    }, []);

    const getDataSubmit = async () => {
      const {
        files,
        unitId,
        providerId,
        estPrice,
        workingDays,
        serviceTypeId,
        ...data
      } = form.getFieldsValue();

      const fileAttachIds: number[] = [];

      for (const file of fileList) {
        if (file.id) {
          fileAttachIds.push(file.id);
        } else if (file.originFile) {
          const { data } = await fileAttachApi.upload(file.originFile);

          const resFileAttach = await fileAttachApi.create({
            fileAttach: {
              ...file,
              url: $url(data.path),
            },
          });

          fileAttachIds.push(resFileAttach.data.id);
        }
      }

      const payload = {
        service: {
          ...data,
          estPrice: +estPrice || undefined,
          workingDays: +workingDays || undefined,
          files: typeof files === "string" ? files : JSON.stringify(files),
          isActive: selectedService?.isActive,
        },
        unitId,
        providerId,
        serviceTypeId,
        fileAttachIds,
      };

      return payload;
    };

    const createData = async () => {
      const valid = await form.validateFields();

      setLoading(true);
      try {
        const res = await serviceApi.create(await getDataSubmit());
        message.success("Tạo dịch vụ thành công!");
        navigate(`/master-data/${PermissionNames.serviceList}`);
        setFileList([]);
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const valid = await form.validateFields();

      setLoading(true);
      try {
        const res = await serviceApi.update(
          selectedService!?.id || 0,
          await getDataSubmit()
        );
        message.success("Chỉnh sửa dịch vụ thành công!");
      } finally {
        setLoading(false);
      }
    };

    const handleSubmit = () => {
      if (status == "create") {
        createData();
      } else {
        updateData();
      }
    };

    const pageTitle = useMemo(
      () => (status == "create" ? "Tạo  dịch vụ" : "Chỉnh sửa dịch vụ"),
      [status]
    );

    return (
      <div>
        <PageTitle
          back
          breadcrumbs={[
            { label: "Dữ liệu nguồn" },
            {
              label: "Danh mục dịch vụ",
              href: `/master-data/${PermissionNames.serviceList}`,
            },
            { label: pageTitle },
          ]}
          title={pageTitle}
          extra={
            selectedService &&
            status == "update" && (
              <Space>
                <ActiveStatusTagSelect
                  disabled={readonly}
                  isActive={selectedService?.isActive}
                  onChange={(value) => {
                    setSelectedService({
                      ...selectedService,
                      isActive: value,
                    } as Service);
                  }}
                />
              </Space>
            )
          }
        />
        <Card>
          <Spin spinning={loadingFetch}>
            <Form
              layout="vertical"
              form={form}
              className={clsx(readonly ? "readonly" : "")}
              disabled={readonly}
            >
              <Form.Item name="isActive" hidden />

              <div style={{ display: "flex" }}>
                {/* upload avatar */}
                <Form.Item
                  style={{
                    marginBottom: 0,
                    marginRight: 20,
                  }}
                  label={""}
                  name="avatar"
                  className="form-height-full"
                >
                  <SingleImageUpload
                    onUploadOk={(file: FileAttach) => {
                      console.log(file);
                      form.setFieldsValue({
                        avatar: file.path,
                      });
                    }}
                    imageUrl={avatar}
                    height={"100%"}
                    width={"100%"}
                    className="h-full upload-avatar"
                    hideUploadButton={readonly}
                    disabled={readonly}
                  />
                </Form.Item>

                <div className="service-info" style={{ flex: 1 }}>
                  <Row gutter={16}>
                    <Col span={8}>
                      <Form.Item label="Mã dịch vụ" name="code">
                        {/* <Input placeholder="Để trống sẽ tự sinh" /> */}

                        <TextInput
                          disabled={status == "update"}
                          placeholder={
                            status == "create" ? "Để trống sẽ tự sinh" : ""
                          }
                        />
                      </Form.Item>

                      <Form.Item label="Tên dịch vụ" name="name" rules={rules}>
                        <Input placeholder="Nhập tên dịch vụ" />
                      </Form.Item>
                      <Form.Item label="Nhà cung cấp" name="providerId">
                        <ProviderSelector
                          placeholder="Chọn nhà cung cấp"
                          initQuery={{ isActive: true }}
                          initOptionItem={selectedService?.provider}
                        />
                      </Form.Item>
                    </Col>

                    <Col span={8}>
                      <Form.Item label="Chi phí ước tính (VNĐ)" name="estPrice">
                        <InputNumber placeholder="Nhập chi phí" />
                      </Form.Item>
                      <Form.Item
                        label="Loại dịch vụ"
                        name="serviceTypeId"
                        rules={rules}
                      >
                        <Select
                          placeholder="Chọn loại dịch vụ"
                          options={serviceTypes.map((item) => ({
                            label: item.name,
                            value: item.id,
                          }))}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item name={"dateType"} hidden />
                      <Form.Item
                        label="Thời gian thực hiện (ngày)"
                        name="workingDays"
                      >
                        <InputNumber
                          placeholder="Thời gian nhập theo ngày"
                          min={0}
                        />
                      </Form.Item>
                      <Form.Item
                        label="Đơn vị tính"
                        name="unitId"
                        rules={rules}
                      >
                        <UnitSelector
                          initQuery={{ isActive: true }}
                          initOptionItem={selectedService?.unit}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </div>
              </div>

              <Row gutter={16}>
                {/* <Col span={24}>
                  <Form.Item label="Nhà cung cấp" name="providerId">
                    <ProviderSelector
                      placeholder="Chọn nhà cung cấp"
                      initQuery={{ isActive: true }}
                      initOptionItem={selectedService?.provider}
                    />
                  </Form.Item>
                </Col> */}
                <Col span={24}>
                  <Form.Item
                    label="Mô tả"
                    name="description"
                    rules={descriptionRules}
                  >
                    <BMDCKEditor
                      placeholder="Nhập mô tả"
                      value={selectedService?.description}
                      disabled={readonly}
                      inputHeight={300}
                      onChange={(content) => {
                        form.setFieldsValue({ description: content });
                      }}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Tabs defaultActiveKey="1" type="line">
                <Tabs.TabPane tab="Tệp đính kèm" key="1">
                  <Form.Item
                    shouldUpdate={true}
                    style={{ marginBottom: 0, height: "100%" }}
                    className="form-height-full"
                  >
                    {() => {
                      return (
                        <Form.Item
                          label={""}
                          noStyle
                          style={{ marginBottom: 0 }}
                          name="files"
                          className="h-full "
                        >
                          <FileUploadMultiple2
                            className="h-full"
                            fileList={fileList}
                            onUploadOk={(file) => {
                              fileList.push(file);
                              setFileList([...fileList]);
                            }}
                            onDelete={(file) => {
                              const findIndex = fileList.findIndex(
                                (e) => e.uid == file.uid
                              );

                              if (findIndex > -1) {
                                fileList.splice(findIndex, 1);
                                setFileList([...fileList]);
                              }
                            }}
                            hideUploadButton={readonly}
                          />
                        </Form.Item>
                      );
                    }}
                  </Form.Item>
                </Tabs.TabPane>

                {/* <Tabs.TabPane tab="Tab 2" key="2">Content of Tab Pane 2</Tabs.TabPane> */}
                {/* <Tabs.TabPane tab="Tab 3" key="3">Content of Tab Pane 3</Tabs.TabPane> */}
              </Tabs>
            </Form>
            <div className="flex gap-[16px] justify-end mt-2">
              {!readonly && (
                <CustomButton
                  variant="outline"
                  className="cta-button"
                  onClick={() => {
                    if (status == "create") {
                      navigate(`/master-data/${PermissionNames.serviceList}`);
                    } else {
                      setReadonly(true);
                      setDataToForm(selectedService!);
                    }
                  }}
                >
                  Hủy
                </CustomButton>
              )}

              <CustomButton
                // variant="outline"
                className="cta-button"
                loading={loading}
                onClick={() => {
                  if (!readonly) {
                    handleSubmit();
                  } else {
                    setReadonly(false);
                  }
                }}
                disabled={status == "update" && !haveEditPermission}
              >
                {status == "create"
                  ? "Tạo dịch vụ"
                  : readonly
                  ? "Chỉnh sửa"
                  : "Lưu chỉnh sửa"}
              </CustomButton>
            </div>
          </Spin>
        </Card>
      </div>
    );
  }
);
