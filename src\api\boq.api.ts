import { AxiosPromise } from "axios";
import { request } from "utils/request";

export const boqApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/boq",
      params,
    }),
  findOne: (id: number, params?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/boq/${id}`,
      params,
    }),
  findBoqDetail: (params?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/boqDetail`,
      params,
    }),
  findByProjectId: (projectId: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/boq/project/${projectId}`,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/boq",
      data,
      method: "post",
    }),
  createBoqDetail: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/boqDetail",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/boq/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/boq/${id}`,
      method: "delete",
    }),
  approve: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/boq/${id}/approve`,
      method: "patch",
      data,
    }),
  reject: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/boq/${id}/reject`,
      method: "patch",
      data,
    }),
};
