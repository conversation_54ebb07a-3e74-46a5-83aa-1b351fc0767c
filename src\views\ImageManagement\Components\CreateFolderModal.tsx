import { Form, Input, message, Modal } from "antd";
import { fileAttachApi } from "api/fileAttach.api";
import { BMDTextArea } from "components/TextArea/BMDTextArea";
import React, { forwardRef, useImperativeHandle, useState } from "react";
import { FileAttach, FileAttachType } from "types/fileAttach";
export interface CreateFolderModalRef {
  open: (
    currentFolderPath: string,
    status: "create" | "update",
    currentFolder?: FileAttach
  ) => void;
}
interface CreateFolderModalProps {
  onCreateSuccess?: (data: any) => void;
}
const CreateFolderModal = forwardRef<
  CreateFolderModalRef,
  CreateFolderModalProps
>(({ onCreateSuccess }, ref) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentFolder, setCurrentFolder] = useState<FileAttach>();
  const [status, setStatus] = useState<"create" | "update">();
  const [form] = Form.useForm();
  useImperativeHandle(ref, () => ({
    open: (currentFolderPath, status, currentFolder) => {
      setStatus(status);
      setIsModalOpen(true);
      if (status === "update" && currentFolder) {
        setCurrentFolder(currentFolder);
        form.setFieldsValue({
          name: currentFolder.name,
        });
      } else {
        form.resetFields(); // đảm bảo form sạch khi tạo mới
      }
    },
    close: () => setIsModalOpen(false),
  }));
  const handleCreateFolder = async () => {
    try {
      const values = await form.validateFields();

      const folderData: Partial<FileAttach> = {
        name: values.name,
        type: FileAttachType.Folder,
        url: "",
        // path: currentFolderPath
        //   ? `${currentFolderPath}/${values.path}`
        //   : values.path || "/",
        path: values.path || "/",
        size: 0,
        desc: values.desc || "",
      };
      const { data } = await fileAttachApi.create({ fileAttach: folderData });
      if (onCreateSuccess) {
        onCreateSuccess(data); // ✅ Gọi callback để component cha cập nhật lại dữ liệu
      }
      message.success("Tạo thư mục thành công");
      setIsModalOpen(false);
    } catch (err) {
      console.log("❌ Validation failed", err);
    } finally {
      form.resetFields();
    }
  };
  const handleUpdateFolder = async () => {
    try {
      const values = await form.validateFields();

      const folderData: Partial<FileAttach> = {
        name: values.name,
      };
      const { data } = await fileAttachApi.update(currentFolder?.id!, {
        fileAttach: folderData,
      });
      message.success("Cập nhật thư mục thành công");
      setIsModalOpen(false);
      if (onCreateSuccess) {
        onCreateSuccess(data); // ✅ Gọi callback để component cha cập nhật lại dữ liệu
      }
    } catch (err) {
      console.log("❌ Validation failed", err);
    } finally {
      form.resetFields();
    }
  };
  return (
    <Modal
      open={isModalOpen}
      title={status == "create" ? "Tạo thư mục" : "Cập nhật thư mục"}
      onOk={() => {
        if (status == "create") {
          handleCreateFolder();
        } else {
          handleUpdateFolder();
        }
      }}
      onCancel={() => {
        setIsModalOpen(false);
        form.resetFields();
      }}
    >
      <Form layout="vertical" form={form}>
        <Form.Item
          name="name"
          label="Tên thư mục"
          rules={[{ required: true, message: "Vui lòng nhập tên thư mục" }]}
        >
          <Input placeholder="Nhập tên thư mục" />
        </Form.Item>

        <Form.Item name="path" label="Đường dẫn (path)" className="hidden">
          <Input placeholder="/ hoặc /Avatar hoặc /logo/30" />
        </Form.Item>

        <Form.Item name="desc" label="Mô tả" className="hidden">
          <BMDTextArea placeholder="Thư mục này dùng để..." />
        </Form.Item>
      </Form>
    </Modal>
  );
});

export default CreateFolderModal;
