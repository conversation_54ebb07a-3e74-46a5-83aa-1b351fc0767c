import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
} from "react";
import { debounce, uniqBy } from "lodash";
import { useDepartment } from "hooks/useDepartment";
import { Department } from "types/department";
import { QueryParams2 } from "types/query";
import CustomSelect from "components/Input/CustomSelect";
import { Select } from "antd";

type CustomFormItemProps = {
  value?: number;
  initQuery?: QueryParams2;
  disabled?: boolean;
  selectedDepartment?: Department[];
  multiple?: boolean;
  onChange?: (value: any) => void;
  initOptionItem?: Department | Department[];
  valueIsOption?: boolean;
  allowClear?: boolean;
  placeholder?: string;
};

export interface DepartmentSelector {
  refresh(): void;
}

export const DepartmentSelector = forwardRef(
  (
    {
      value,
      onChange,
      initQuery,
      disabled,
      multiple = false,
      selectedDepartment,
      initOptionItem,
      valueIsOption,
      allowClear = true,
      placeholder = "Chọn phòng ban",
    }: CustomFormItemProps,
    ref
  ) => {
    const { departments, loading, fetchData, query } = useDepartment({
      initQuery: { page: 1, limit: 50, ...initQuery },
    });

    useImperativeHandle<any, DepartmentSelector>(
      ref,
      () => ({
        refresh() {
          fetchData();
        },
      }),
      []
    );

    useEffect(() => {
      fetchData();
    }, [selectedDepartment]);

    const debounceSearch = useCallback(
      debounce((keyword) => {
        query.search = keyword;
        fetchData();
      }, 300),
      [query]
    );

    const options = useMemo(() => {
      let data = [...departments];
      if (initOptionItem) {
        if ((initOptionItem as Department[])?.length) {
          data = data.concat(initOptionItem);
        } else {
          data.push(initOptionItem as Department);
        }
      }
      return uniqBy(data, (item) => item.id).map((item) => ({
        label: item.name,
        value: item.id,
        item, // lưu nguyên object nếu cần dùng sau
      }));
    }, [departments, initOptionItem]);

    const handleChange = (v: any, option: any) => {
      if (valueIsOption) {
        if (option instanceof Array) {
          onChange?.(option.map((opt) => opt.item));
        } else {
          onChange?.(option.item);
        }
      } else {
        onChange?.(v);
      }
    };

    return (
      <Select
        value={value}
        onChange={handleChange}
        disabled={disabled}
        options={options}
        mode={multiple ? "multiple" : undefined}
        allowClear={allowClear}
        placeholder={placeholder}
        onSearch={debounceSearch}
        loading={loading}
      />
    );
  }
);
