import { Company } from "./company";
import { Dictionary } from "./dictionary";
import { Permission } from "./permission";
import { Project } from "./project";
import { Role } from "./role";
import { Staff } from "./staff";

export interface MemberShip {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  code?: string;
  type: MemberShipType;
  name: string;
  phone: string; // số điện thoại sử dụng
  email: string; // email sử dụng
  staffCode: string; // code nhân vien
  isActive: boolean;
  project: Project;
  staff: Staff;
  permissions: Permission[];
  jobTitle: Dictionary;
  memberShipCategory?: Dictionary;
  role: Role; // vai trò Hiện tại
  company: Company; // công ty hiện tại
  // info create update
  createdBy: Staff;
  updatedBy: Staff;
  date: string; // ngày tham gia
}

export enum MemberShipType {
  MinhGlobal = "MINH_GLOBAL",
  Subcontractor = "SUBCONTRACTOR", // <PERSON>h<PERSON><PERSON> phụ
  Investor = "INVESTOR", // nhà đầu từ
}
