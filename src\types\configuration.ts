export enum ConfigurationParam {
  FacebookLink = "FACEBOOK_LINK",
  YoutubeLink = "YOUTUBE_LINK",
  GoogleLink = "GOOGLE_LINK",
  TiktokLink = "TIKTOK_LINK",
  Hotline = "HOTLINE",
  RejectRequestVipAfterDay = "REJECT_REQUEST_VIP_AFTER_DAY",
  CompanyName = "COMPANY_NAME",
  CompanyTaxCode = "COMPANY_TAX_CODE",
  CompanyAddress = "COMPANY_ADDRESS",
  HomeBanner1 = "HOME_BANNER_1",
  HomeBanner2 = "HOME_BANNER_2",
}

export const ConfigurationParamTrans = {
  [ConfigurationParam.FacebookLink]: {
    label: "Link facebook",
    value: ConfigurationParam.FacebookLink,
  },
  [ConfigurationParam.YoutubeLink]: {
    label: "Link youtube",
    value: ConfigurationParam.YoutubeLink,
  },
  [ConfigurationParam.GoogleLink]: {
    label: "Link google",
    value: ConfigurationParam.GoogleLink,
  },
  [ConfigurationParam.TiktokLink]: {
    label: "Link tiktok",
    value: ConfigurationParam.TiktokLink,
  },
  [ConfigurationParam.Hotline]: {
    label: "Hotline",
    value: ConfigurationParam.Hotline,
  },
  [ConfigurationParam.RejectRequestVipAfterDay]: {
    label: "Số ngày tự hủy yêu cầu đổi VIP",
    value: ConfigurationParam.RejectRequestVipAfterDay,
  },
  [ConfigurationParam.CompanyName]: {
    label: "Tên công ty",
    value: ConfigurationParam.CompanyName,
  },
  [ConfigurationParam.CompanyTaxCode]: {
    label: "Mã số thuế",
    value: ConfigurationParam.CompanyTaxCode,
  },
  [ConfigurationParam.CompanyAddress]: {
    label: "Địa chỉ công ty",
    value: ConfigurationParam.CompanyAddress,
  },
  [ConfigurationParam.HomeBanner1]: {
    label: "Banner trang chủ (trên)",
    value: ConfigurationParam.HomeBanner1,
  },
  [ConfigurationParam.HomeBanner2]: {
    label: "Banner trang chủ (dưới)",
    value: ConfigurationParam.HomeBanner2,
  },
};

export const ConfigSlugsNote = {};

export enum ConfigurationDataType {
  Number = "NUMBER",
  String = "STRING",
  Boolean = "BOOLEAN",
}

export interface Configuration {
  id: number;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  param: ConfigurationParam;
  title: string;
  image: string;
  value: string;
  description: string;
  link: string;
  buttonText: string;
  isEnable: boolean;
  dataType: ConfigurationDataType;
  textColor: string;
  buttonColor: string;
}
