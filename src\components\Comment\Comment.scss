.comment-card {
  .ant-btn {
    &:not(:disabled) {
      cursor: pointer !important;
    }
  }

  .ant-select {
    .ant-select-selector {
      cursor: pointer !important;
    }
  }

  .ant-select-dropdown {
    .ant-select-item {
      cursor: pointer !important;
    }
  }

  .comment-list {
    row-gap: 16px;

    .comment-item {
      background-color: var(--color-neutral-n1);
      padding: 8px;
      row-gap: 4px;

      .staff-name {
        color: var(--color-logo);
        font-weight: 700;
        font-size: 13px;
      }

      .content {
        font-size: 13px;
        color: var(--color-logo);
      }

      .time {
        font-size: 12px;
        color: var(--color-neutral-n5);
      }
    }
  }

  .custom-pagination {
    div:first-child {
      visibility: hidden;
    }
  }
}

.comment-item {
  .staff-name {
    font-weight: 500;
    margin-left: 8px;
  }

  .comment-content {
    margin: 4px 0px;
  }

  .time {
    color: #8c8c8c;
  }
}

/* Display File Upload */
.display-file-upload {
  .ant-progress-text {
    display: none;
  }

  .ant-image {
    width: 24px !important;
    height: 24px !important;

    .ant-image-img {
      width: 24px !important;
      height: 24px !important;
    }
  }
}

.ant-popconfirm-buttons {
  .ant-btn {
    &:not(:disabled) {
      cursor: pointer !important;
    }
  }
}
