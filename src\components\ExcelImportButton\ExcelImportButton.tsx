import { ImportOutlined } from "@ant-design/icons";
import { message, Modal } from "antd";
import * as ExcelJS from "exceljs";
import { saveAs } from "file-saver";
import { useRef } from "react";
import CustomButton from "components/Button/CustomButton";
import { ImportDeviceModal } from "components/ImportDocument/ImportDevice";
import { AxiosPromise } from "axios";
import { QueryParam } from "types/query";
import { addNewSheet } from "utils/excel";
import { removeSubstringFromKeys } from "utils/common";
import ImportDictionary from "components/ImportDocument/ImportDictionary";
import { getListNameByApi } from "hooks/useDictionary";
import { DictionaryType } from "types/dictionary";

interface ExcelColumn {
  header: string;
  key: string;
  required?: boolean;
  validation?: {
    type: "list";
    options: string[];
    sheetName: string;
  };
  dependColumn?: string;
}

interface ExcelImportButtonProps {
  columns: ExcelColumn[];
  templateFileName: string;
  templatePath?: string;
  onImportSuccess: () => void;
  onProcessImportData?: (data: any[], setData: (data: any[]) => void) => void;
  validationAPIs?: {
    [key: string]: {
      api: any;
      dataField: string;
      propField?: string;
      query?: QueryParam;
    };
  };
  guide?: string[];
  buttonText?: string;
  buttonSize?: "small" | "medium" | "large";
  sheetName?: string;
  type?: DictionaryType;
}

export const ExcelImportButton = ({
  columns,
  templateFileName,
  templatePath,
  onImportSuccess,
  onProcessImportData,
  validationAPIs = {},
  guide = [
    "Vui lòng tải và sử dụng file mẫu để import dữ liệu đúng cách",
    "Không được thay đổi tiêu đề trong file csv/excel mẫu để tránh import thiếu dữ liệu",
  ],
  buttonText = "Nhập excel",
  buttonSize = "small",
  sheetName,
  type,
}: ExcelImportButtonProps) => {
  const importModal = useRef<ImportDeviceModal>();

  const handleDownloadExcelDemoFile = async () => {
    try {
      // Lấy dữ liệu validation cho các columns
      const validationData: { [key: string]: string[] } = {};

      for (const [key, config] of Object.entries(validationAPIs)) {
        try {
          // Kiểm tra xem api có phải là function không
          if (typeof config.api === "function") {
            const data = (await getListNameByApi({
              api: config.api,
              query: config.query,
              dataKey: config.dataField,
              propKey: config.propField,
            })) as string[];
            validationData[key] = data;
          } else if (config.api && typeof config.api.get === "function") {
            // Nếu api là object có method get
            const data = (await getListNameByApi({
              api: config.api.get || config.api,
              query: config.query,
              dataKey: config.dataField,
              propKey: config.propField,
            })) as string[];
            validationData[key] = data;
          } else {
            console.warn(`API for ${key} is not a function:`, config.api);
            validationData[key] = [];
          }
        } catch (error) {
          console.error(`Error fetching data for ${key}:`, error);
          validationData[key] = [];
        }
      }

      let workbook: ExcelJS.Workbook;
      let worksheet: ExcelJS.Worksheet;

      if (templatePath) {
        // Tải file mẫu có sẵn
        const response = await fetch(templatePath);
        const arrayBuffer = await response.arrayBuffer();
        workbook = new ExcelJS.Workbook();
        await workbook.xlsx.load(arrayBuffer);
        worksheet = workbook.getWorksheet(1)!;
      } else {
        // Tạo file mới
        workbook = new ExcelJS.Workbook();
        worksheet = workbook.addWorksheet(sheetName);

        // Thêm headers
        const headers = columns.map((col) => col.header);
        worksheet.addRow(headers);

        // Style headers
        const headerRow = worksheet.getRow(1);
        headerRow.font = { bold: true };
        headerRow.fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "FFE0E0E0" },
        };
      }

      // Thêm validation sheets
      for (const [key, data] of Object.entries(validationData)) {
        if (data.length > 0) {
          addNewSheet(workbook, key, data, "veryHidden");
        }
      }

      // Thêm data validation cho các columns
      columns.forEach((column, index) => {
        if (column.validation && validationData[column.validation.sheetName]) {
          const columnLetter = String.fromCharCode(65 + index); // A, B, C, ...
          const dataLength = validationData[column.validation.sheetName].length;

          if (dataLength > 0) {
            worksheet.getCell(`${columnLetter}2`).dataValidation = {
              type: "list",
              allowBlank: true,
              formulae: [
                `${column.validation.sheetName}!$A$1:$A$${dataLength}`,
              ],
              error: "Vui lòng chọn một giá trị từ danh sách.",
              showErrorMessage: true,
            };
          }
        }

        // neu co column cha thi apply validation
        if (!!column.dependColumn) {
          // Add data validation for parent column (assuming it's column B)
          const parentColIndex = index; // 0-based index for column B
          const lastRow = 1000; // Adjust based on your needs

          for (let row = 2; row <= lastRow; row++) {
            const cell = worksheet.getRow(row).getCell(parentColIndex + 1);
            cell.dataValidation = {
              type: "list",
              allowBlank: true,
              formulae: [
                `=IF(ISBLANK(${column.dependColumn}${row}), "", OFFSET(Dictionary!$A$1, 0, 0, COUNTA(Dictionary!$A:$A)))`,
              ],
              error: "Vui lòng chọn một giá trị từ danh sách.",
              showErrorMessage: true,
            };
          }
        }
      });

      // Xuất file Excel
      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });

      saveAs(blob, templateFileName);
    } catch (error) {
      console.error("Error in handleDownloadExcelDemoFile:", error);
      message.error(
        `Có lỗi xảy ra: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  };

  const handleOnUploadedFile = async (excelData: any, setData: any) => {
    if (onProcessImportData) {
      onProcessImportData(excelData.results, setData);
    } else {
      // Default processing
      const { results } = excelData;
      const importData = results?.map((item: any) => {
        const refineRow = removeSubstringFromKeys(item, " *");
        const processedRow: any = { rowNum: item.__rowNum__ };

        columns.forEach((column) => {
          processedRow[column.key] = refineRow[column.header];
        });

        return processedRow;
      });

      setData(importData);
    }
  };

  return (
    <>
      <CustomButton
        className="border-none"
        size={buttonSize}
        icon={<ImportOutlined />}
        onClick={() => {
          importModal.current?.open();
        }}
      >
        {buttonText}
      </CustomButton>

      <ImportDictionary
        guide={guide}
        onSuccess={onImportSuccess}
        ref={importModal}
        onUploaded={handleOnUploadedFile}
        onDownloadDemoExcel={handleDownloadExcelDemoFile}
        dictionaryType={type}
      />
    </>
  );
};
