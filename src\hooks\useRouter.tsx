import React, { useEffect } from "react";
import { useLocation, useNavigate, useSearchParams } from "react-router-dom";
import { settings } from "settings";
import { appStore } from "store/appStore";
import { permissionStore } from "store/permissionStore";
import { getToken } from "utils/auth";

const whileList = ["/login", "/components"];

export const useRouter = (isLoaded: boolean) => {
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  const getCurrentRoute = () => {
    let currentRoute = null;
    let pathname = location.pathname;
    let query = location.search;

    if (pathname[pathname.length - 1] == "/") {
      pathname = pathname.substring(0, pathname.lastIndexOf("/"));
    }

    for (const route of permissionStore.accessRoutes) {
      if (!route.aliasPath) {
        continue;
      }

      if (route.children) {
        for (const childRoute of route.children) {
          if (!childRoute.aliasPath) {
            continue;
          }

          if (pathname.startsWith(childRoute.aliasPath)) {
            currentRoute = childRoute;
            break;
          }
        }
      } else {
        if (pathname.startsWith(route.aliasPath)) {
          currentRoute = route;
          break;
        }
      }
    }

    return currentRoute;
  };

  useEffect(() => {
    if (!isLoaded) {
      return;
    }

    const token = getToken();
    if (!token) {
      return navigate("/login");
    }

    if (location.pathname == "/login" && token) {
      return navigate("/");
    }

    if (whileList.includes(location.pathname)) {
      return;
    }

    if (permissionStore.accessRoutes.length) {
      if (location.pathname == "/") {
        if (token) {
          let firstRoute = permissionStore.accessRoutes.find(
            (e) => e.isAccess || !settings.checkPermission
          );

          const route = firstRoute?.children
            ? firstRoute.path + "/" + firstRoute.children[0].path
            : firstRoute?.path;
          navigate(route || "/login");
        } else {
          navigate("/login");
        }
      } else {
        if (settings.checkPermission) {
          const currentRoute = getCurrentRoute();

          if (!currentRoute) navigate("/404");
          else if (currentRoute.name?.includes("/update")) {
            if (
              !currentRoute.isPublic &&
              !currentRoute.isAccess &&
              searchParams.get("update")
            ) {
              navigate("/404");
            }
          } else if (!currentRoute.isPublic && !currentRoute.isAccess) {
            navigate("/404");
          }
        }
      }
    }

    handleScrollToTop();
  }, [location.pathname, isLoaded, appStore.currentProject]);

  const handleScrollToTop = () => {
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: "smooth",
    });
  };

  return location;
};
