import { RcFile } from "antd/es/upload";
import { Service } from "./service";
import { Staff } from "./staff";
import { TaskTemplate } from "./taskTemplate";

export enum FileAttachType {
  Image = "IMAGE",
  Pdf = "PDF",
  Video = "VIDEO",
  Folder = "FOLDER",
  Other = "OTHER",
}
export const FileAttachTypeTrans = {
  [FileAttachType.Image]: { label: "Hình ảnh", value: FileAttachType.Image },
  [FileAttachType.Pdf]: { label: "PDF", value: FileAttachType.Pdf },
  [FileAttachType.Video]: { label: "Video", value: FileAttachType.Video },
  [FileAttachType.Folder]: { label: "Thư mục", value: FileAttachType.Folder }, // ✅ Thêm dòng này
  [FileAttachType.Other]: { label: "Khác", value: FileAttachType.Other },
};

export interface FileAttach {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  mimetype: string;
  name: string;
  uid: string;
  type: FileAttachType;
  url: string;
  path: string; // origin file
  size: number;
  desc: string;
  isActive: boolean;
  totalImage: number;
  address: string;
  thumbnail: string;
  tag: string;
  // custom
  staffs: Staff[];
  taskTemplates: TaskTemplate[];
  services: Service[];
  isError: boolean;
  originFile?: RcFile;
  filename?: string;
}

export interface FileAttachUpload {
  name: string;
  type: string;
  url: string;
  path: string;
  size: number;
  desc: string;
}
export interface FileAttachUpload2 {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  type: string;
  filename: string;
  url: string;
  path: string;
  size: number;
  destination: string;
}
