import { lazy } from "react";
import { ReactComponent as InfoIcon } from "assets/svgs/info.svg";
import { ReactComponent as ManageByIcon } from "assets/svgs/manageBy.svg";
import { PermissionType } from "types/permission";
import { ReactComponent as BellIcon } from "assets/svgs/bell.svg";
const ComponentsPage = lazy(
  () => import("views/ComponentsPage/ComponentsPage")
);
const ForgotPasswordPage = lazy(
  () => import("views/ForgotPassword/ForgotPasswordPage")
);
const ProjectDetailPage = lazy(() => import("views/Project/ProjectDetailPage"));
const NotificationPage = lazy(
  () => import("views/NotificationPage/NotificationPage")
);

const ProfilePage = lazy(() =>
  import("views/Profile/ProfilePage").then((m) => ({ default: m.ProfilePage }))
);

import { dashboardRoutes } from "./dashboardRoute";
import { projectRoutes } from "./projectRoute";
import { reportRoutes } from "./reportRoute";
import { documentRoutes } from "./documentRoute";
import { progressRoutes } from "./progressRoute";
import { boqRoutes } from "./boqRoute";
import { masterDataRoutes } from "./masterDataRoute";
import { Route } from "./RouteType";
import { configRoutes } from "./configRoute";
import { AdminLayout } from "../layouts/AdminLayout";
import { LoginPage } from "views/Login/LoginPage";
import { NotFoundPage } from "views/404/NotFoundPage";
import TaskPage from "views/Task/TaskPage";
import { PermissionNames } from "types/PermissionNames";
import CreateOrUpdateTaskPage from "views/Task/CreateOrUpdateTaskPage";

export const adminRoutes: Route[] = [
  ...dashboardRoutes,
  ...projectRoutes,
  {
    title: "Thông báo",
    breadcrumb: "Thông báo",
    path: "/notification",
    name: "notification",
    aliasPath: "/notification",
    icon: <BellIcon className="w-[24px] h-[24px] object-contain" />,
    element: <NotificationPage title="Thông báo" />,
    permissionTypes: [PermissionType.List],
    // needProject: true,
    isPublic: true,
  },
  {
    title: "Thông tin dự án",
    breadcrumb: "Thông tin dự án",
    path: "/project-detail/:id",
    name: "project-detail",
    aliasPath: "/project-detail",
    icon: <InfoIcon />,
    element: <ProjectDetailPage />,
    permissionTypes: [PermissionType.List],
    needProject: true,
    isPublic: true,
  },
  ...reportRoutes,
  ...documentRoutes,
  ...progressRoutes,
  ...boqRoutes,
  ...masterDataRoutes,
  ...configRoutes,
  {
    title: "Công việc",
    icon: <InfoIcon />,
    path: `/task`,
    name: `task`,
    aliasPath: `/task`,
    // element: <TaskPage title="Công việc" />,
    permissionTypes: [PermissionType.List],
    isPublic: true,
    isCompact: true,
    children: [
      {
        title: "Công việc",
        breadcrumb: "Công việc",
        path: PermissionNames.taskList,
        name: PermissionNames.taskList,
        aliasPath: `/task/${PermissionNames.taskList}`,
        element: <TaskPage title="Công việc" />,
        permissionTypes: [PermissionType.List],

        // icon: <TbPackages />,
      },
      {
        title: "Xem tất cả",
        path: PermissionNames.taskViewAll,
        name: PermissionNames.taskViewAll,
        aliasPath: `/task/${PermissionNames.taskViewAll}`,
        hidden: true,
      },
      {
        title: "Tạo công việc",
        breadcrumb: "Công việc",
        path: PermissionNames.taskAdd,
        name: PermissionNames.taskAdd,
        aliasPath: `/task/${PermissionNames.taskAdd}`,
        element: (
          <CreateOrUpdateTaskPage title="Tạo công việc" status="create" />
        ),
        permissionTypes: [PermissionType.Add],
        isPublic: true,
        hidden: true,

        // icon: <TbPackages />,
      },
      {
        title: "Chỉnh sửa công việc",
        breadcrumb: "Công việc",
        path: PermissionNames.taskEdit,
        name: PermissionNames.taskEdit,
        aliasPath: `/task/${PermissionNames.taskEdit.replace(
          "/:id",
          ""
        )}`,
        element: (
          <CreateOrUpdateTaskPage title="Chỉnh sửa công việc" status="update" />
        ),
        permissionTypes: [PermissionType.Edit],
        isPublic: true,
        hidden: true,

        // icon: <TbPackages />,
      },
      {
        title: "Xóa công việc",
        breadcrumb: "Xóa công việc",
        path: PermissionNames.taskDelete,
        name: PermissionNames.taskDelete,
        aliasPath: `/tasks/${PermissionNames.taskDelete}`,
        hidden: true,
      },
    ],
  },
  {
    title: "Tài khoản",
    icon: <ManageByIcon />,
    path: "/profile",
    name: "profile",
    aliasPath: `/profile`,
    breadcrumb: "Tài khoản",
    element: <ProfilePage title="Tài khoản" />,
    isPublic: true,
  },
  {
    title: "Components",
    breadcrumb: "Components",
    path: "/components",
    name: "components",
    aliasPath: `/component`,
    element: <ComponentsPage />,
    hidden: true,
    isPublic: true,
  },
  // {
  //   title: "Hàng hóa",
  //   icon: <IoPersonCircleOutline />,
  //   path: "/goods",
  //   name: "/goods",
  //   breadcrumb: "Hàng hóa",
  //   element: <GoodsPage title="Hàng hóa" />,
  // },
];

const routes: Route[] = [
  {
    path: "/",
    name: "/",
    aliasPath: "/",
    children: adminRoutes,
    element: <AdminLayout />,
  },
  {
    name: "/login",
    element: <LoginPage title="Đăng nhập" />,
    path: "/login",
    aliasPath: "/login",
  },
  {
    element: <ForgotPasswordPage />,
    path: "/forgot-password",
    name: "/forgot-password",
    aliasPath: "/forgot-password",
  },

  {
    element: <NotFoundPage />,
    name: "*",
    path: "*",
    aliasPath: "*",
  },
];

export { routes };
