import { commentApi } from "api/comment.api";
import { useMemo, useState } from "react";
import { Comment } from "types/comment";
import { QueryParam } from "types/query";

export interface CommentQuery extends Partial<QueryParam> { }

interface UseCommentProps {
  initQuery: CommentQuery;
}

export const useComment = ({ initQuery }: UseCommentProps) => {
  const [data, setData] = useState<Comment[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<CommentQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async (newQuery?: CommentQuery) => {
    setLoading(true);
    try {
      const { data } = await commentApi.findAll({ ...query, ...newQuery });

      setData(data.comments);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return { comments: data, total, fetchData, loading, setQuery, query };
};
