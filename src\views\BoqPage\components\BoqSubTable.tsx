import { DownOutlined, RightOutlined } from "@ant-design/icons";
import { Button, Table } from "antd";
import clsx from "clsx";
import { CustomizableColumn } from "components/Table/CustomizableTable";
import { observer } from "mobx-react";
import { BOQDetail } from "types/boq";

interface Props {
  boqDetails: BOQDetail[];
  columns: CustomizableColumn<BOQDetail>[];
  indent: number;
}

const BoqSubTable = ({ boqDetails, columns, indent }: Props) => {
  return (
    <Table
      pagination={false}
      dataSource={boqDetails}
      columns={columns}
      showHeader={false}
      expandable={{
        childrenColumnName: "children2",
        rowExpandable: (record) => {
          return record.children && record.children.length > 0;
        },
        expandedRowRender: (record, index, indent) => {
          console.log({ indent });
          return (
            <BoqSubTable
              boqDetails={record.children}
              columns={columns}
              indent={indent}
              key={record.id}
            />
          );
        },
        expandIcon: ({ expanded, onExpand, record }) => {
          const hasChildren = record.children && record.children.length > 0;
          // if (!hasChildren) {
          //   return <></>;
          // }

          return (
            <Button
              type="text"
              size="small"
              className={clsx(!hasChildren && "invisible")}
              icon={expanded ? <DownOutlined /> : <RightOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                onExpand(record, e);
              }}
            />
          );
        },
      }}
    />
  );
};

export default observer(BoqSubTable);

// import React from "react";
// import { Button, Space, Tooltip, Table, Dropdown, Menu } from "antd";
// import { useNavigate } from "react-router-dom";
// import CustomizableTable, {
//   CustomizableColumn,
// } from "components/Table/CustomizableTable";
// import { PermissionNames } from "types/PermissionNames";
// import { BOQ, BOQStatus, BOQStatusTrans, BOQType } from "types/boq";
// import { unixToDate } from "utils/dateFormat";
// import { formatVND } from "utils";
// import { ReactComponent as Copy } from "assets/svgs/copy.svg";
// import EditButton from "components/Button/EditButton";
// import { useTheme } from "context/ThemeContext";
// import { checkRoles, filterActionColumnIfNoPermission } from "utils/auth";
// import { permissionStore } from "store/permissionStore";
// import { observer } from "mobx-react";
// import { DownOutlined } from "@ant-design/icons";

// interface BoqSubTableProps {
//   boqs: BOQ[];
// }

// export const BoqSubTable: React.FC<BoqSubTableProps> = observer(({ boqs }) => {
//   const navigate = useNavigate();
//   const { darkMode } = useTheme();
//   const { haveAddPermission, haveEditPermission } = checkRoles(
//     {
//       add: PermissionNames.boqAdd,
//       edit: PermissionNames.boqEdit,
//     },
//     permissionStore.permissions
//   );

//   const handleViewBoq = (boqId: string) => {
//     const numericId = parseInt(boqId);
//     if (isNaN(numericId)) {
//       console.error("Invalid ID:", boqId);
//       return;
//     }
//     navigate(
//       `/boq/${PermissionNames.boqEdit.replace(":id", numericId.toString())}`
//     );
//   };

//   const handleEditBoq = (boqId: string) => {
//     const numericId = parseInt(boqId);
//     if (isNaN(numericId)) {
//       console.error("Invalid ID:", boqId);
//       return;
//     }
//     navigate(
//       `/boq/${PermissionNames.boqEdit.replace(
//         ":id",
//         numericId.toString()
//       )}?update=1`
//     );
//   };

//   const handleCopyBoq = (boqRecord: BOQ) => {
//     navigate(`/boq/${PermissionNames.boqAdd}`, {
//       state: {
//         copyData: boqRecord,
//       },
//     });
//   };

//   // Dropdown menu for actions
//   const getActionMenu = (boqRecord: BOQ) => (
//     <Menu>
//       {haveAddPermission && (
//         <Menu.Item key="copy" onClick={() => handleCopyBoq(boqRecord)}>
//           <Space>
//             <Copy />
//             Tạo nhanh từ bản này
//           </Space>
//         </Menu.Item>
//       )}
//       {haveEditPermission && (
//         <Menu.Item
//           key="edit"
//           onClick={() => handleEditBoq(boqRecord.id.toString())}
//         >
//           <Space>
//             <EditButton />
//             Chỉnh sửa
//           </Space>
//         </Menu.Item>
//       )}
//     </Menu>
//   );

//   const boqColumns: CustomizableColumn<BOQ>[] = [
//     {
//       key: "id",
//       title: "",
//       dataIndex: "code",
//       width: 120,
//       render: (_, boqRecord) => (
//         <div
//           className="text-[#1677ff] cursor-pointer"
//           onClick={() => handleViewBoq(boqRecord.id.toString())}
//         >
//           {boqRecord.code}
//         </div>
//       ),
//       defaultVisible: true,
//       alwaysVisible: true,
//     },
//     {
//       key: "name",
//       title: "",
//       dataIndex: "name",
//       width: 200,
//       defaultVisible: true,
//       alwaysVisible: true,
//     },
//     {
//       key: "description",
//       title: "",
//       dataIndex: "description",
//       width: 250,
//       defaultVisible: true,
//       alwaysVisible: true,
//       render: (value: string) => value || "-",
//     },
//     {
//       key: "floor",
//       title: "",
//       dataIndex: "floor",
//       width: 100,
//       defaultVisible: true,
//       alwaysVisible: true,
//       render: (value: string) => value || "-",
//     },
//     {
//       key: "quantity",
//       title: "",
//       dataIndex: "quantity",
//       width: 120,
//       defaultVisible: true,
//       alwaysVisible: true,
//       render: (value: number) => value || 0,
//     },
//     {
//       key: "amount",
//       title: "",
//       dataIndex: "amount",
//       width: 150,
//       defaultVisible: true,
//       alwaysVisible: true,
//       render: (value: number) => formatVND(value || 0),
//     },
//     {
//       key: "remarks",
//       title: "",
//       dataIndex: "remarks",
//       width: 200,
//       defaultVisible: true,
//       alwaysVisible: true,
//       render: (value: string) => value || "-",
//     },
//     {
//       key: "isActive",
//       title: "",
//       dataIndex: "isActive",
//       align: "center",
//       width: 120,
//       defaultVisible: true,
//       alwaysVisible: true,
//       render: (value: boolean) => (
//         <span
//           className={`px-2 py-1 rounded text-xs font-medium ${
//             value ? "text-green-600 bg-green-100" : "text-red-600 bg-red-100"
//           }`}
//         >
//           {value ? "Hoạt động" : "Tạm khóa"}
//         </span>
//       ),
//     },
//     {
//       key: "actions",
//       title: "",
//       align: "center",
//       width: 100,
//       defaultVisible: true,
//       alwaysVisible: true,
//       render: (_, boqRecord) => (
//         <Dropdown overlay={getActionMenu(boqRecord)} trigger={["click"]}>
//           <Button type="text" className="flex items-center">
//             Hành động
//             <DownOutlined className="ml-2" />
//           </Button>
//         </Dropdown>
//       ),
//     },
//   ];

//   return (
//     <CustomizableTable
//       columns={filterActionColumnIfNoPermission(boqColumns, [
//         haveAddPermission,
//         haveEditPermission,
//       ])}
//       dataSource={boqs}
//       rowKey="id"
//       loading={false}
//       pagination={false}
//       bordered={false}
//       size="small"
//       showHeader={false}
//       className="boq-sub-table"
//       onRow={(record) => ({
//         onDoubleClick: () => handleViewBoq(record.id.toString()),
//       })}
//       expandable={{
//         expandedRowRender: () => <></>,
//         rowExpandable: () => false,
//       }}
//     />
//   );
// });
