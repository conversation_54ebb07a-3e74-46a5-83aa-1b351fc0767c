import { AxiosPromise } from "axios";
import { request } from "utils/request";

export const dictionaryApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dictionary",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dictionary",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/dictionary/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/dictionary/${id}`,
      method: "delete",
    }),
  import: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/dictionary/import`,
      method: "post",
      data,
    }),
};
