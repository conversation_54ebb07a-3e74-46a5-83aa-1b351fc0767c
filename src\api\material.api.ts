import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const materialApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/material",
      params,
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/material/${id}`,
      method: "get",
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/material",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/material/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/material/${id}`,
      method: "delete",
    }),
  import: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/material/import`,
      method: "post",
      data,
    }),
  inventoryImport: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/material/import/stock`,
      method: "post",
      data,
    }),
  block: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/material/${id}/block`,
      method: "patch",
      data,
    }),
};
