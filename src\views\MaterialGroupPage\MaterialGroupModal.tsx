import { DeleteOutlined } from "@ant-design/icons";
import {
  Button,
  Col,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Row,
  Space,
  Table,
} from "antd";
import Column from "antd/es/table/Column";
import { materialGroupApi } from "api/materialGroup.api";
import { InputNumber } from "components/Input/InputNumber";
import { MaterialTypeSelector } from "components/Selector/MaterialTypeSelector";
import { cloneDeep } from "lodash";
import React, { useImperativeHandle, useState } from "react";
import { IoAddCircleOutline } from "react-icons/io5";
import { MaterialGroup, MaterialGroupForm } from "types/materialGroup";
import { ModalStatus } from "types/modal";
import { requiredRule } from "utils/validateRule";

export interface MaterialGroupModal {
  handleCreate: () => void;
  handleUpdate: (materialGroup: MaterialGroup) => void;
}
interface MaterialGroupModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
}

export const MaterialGroupModal = React.forwardRef(
  ({ onClose, onSubmitOk }: MaterialGroupModalProps, ref) => {
    const [form] = Form.useForm<MaterialGroupForm>();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");
    // const [dictionaries, setDictionaries] = useState<Dictionary[]>([]);
    // const [sortDictionaries, setSortDictionaries] = useState<Dictionary[]>([]);

    // const [sortOpen, setSortOpen] = useState(false);

    // const dictionaryChooseModalRef = useRef<DictionaryChooseModal>();

    useImperativeHandle<any, MaterialGroupModal>(
      ref,
      () => ({
        handleCreate() {
          form.resetFields();
          setVisible(true);
          setStatus("create");
        },
        handleUpdate(materialGroup: MaterialGroup) {
          form.setFieldsValue({
            ...materialGroup,
            materialTypeId: materialGroup.materialType?.id,
          });
          setVisible(true);
          setStatus("update");
        },
      }),
      []
    );

    const createData = async () => {
      const valid = await form.validateFields();
      const { materialTypeId, ...finalData } = valid;
      const data = {
        materialGroup: finalData,
        materialTypeId,
        // dictionaryIds: dictionaries.map((it) => it.id),
      };

      setLoading(true);
      try {
        const res = await materialGroupApi.create(data);
        message.success("Tạo thành công!");
        handleClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const valid = await form.validateFields();
      const { materialTypeId, ...finalData } = valid;
      const data = {
        materialGroup: finalData,
        materialTypeId,
        // dictionaryIds: dictionaries.map((it) => it.id),
      };
      setLoading(true);
      try {
        const res = await materialGroupApi.update(
          data.materialGroup.id || 0,
          data
        );
        message.success("Cập nhật thành công");
        handleClose();
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    const handleClose = () => {
      setVisible(false);
      onClose();
    };

    return (
      <Modal
        onCancel={() => {
          handleClose();
        }}
        visible={visible}
        title={status == "create" ? "Tạo nhóm NVL" : "Cập nhật nhóm NVL"}
        style={{ top: 20 }}
        width={700}
        confirmLoading={loading}
        onOk={() => {
          status == "create" ? createData() : updateData();
        }}
        okText="Lưu"
      >
        <Form layout="vertical" form={form}>
          <Form.Item hidden name={"id"} />
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item label="Tên nhóm" name="name" rules={[requiredRule]}>
                <Input placeholder="" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Loại nguyên vật liệu"
                name="materialTypeId"
                rules={[requiredRule]}
              >
                <MaterialTypeSelector />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Vị trí" name="position" rules={[requiredRule]}>
                <InputNumber />
              </Form.Item>
            </Col>
            {/* <Col span={24}>
              <Form.Item shouldUpdate={true}>
                {() => {
                  return (
                    <Form.Item label="Ảnh" name="image">
                      <SingleImageUpload
                        onUploadOk={(fileAttach) => {
                          console.log("onUploadOk:", fileAttach);
                          form.setFieldsValue({
                            image: $url(fileAttach.path),
                          });
                        }}
                        imageUrl={form.getFieldValue("image")}
                      />
                    </Form.Item>
                  );
                }}
              </Form.Item>
            </Col> */}
          </Row>
        </Form>
      </Modal>
    );
  }
);
