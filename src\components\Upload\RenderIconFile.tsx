import { Image } from "antd";
import clsx from "clsx";
import { FaFileWord, FaFile, FaFileAlt } from "react-icons/fa"; // Import other icons as needed
import { FaFileExcel, FaFileImage, FaFilePdf } from "react-icons/fa6";
import { FileAttachType } from "types/fileAttach";

function getFileIcon(mimeType: string, url?: string) {
  switch (mimeType) {
    // Word documents
    case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
    case "application/msword": // for .doc files
      return <FaFileWord style={{ color: "#2B579A" }} className="text-3xl" />;

    // PDF files
    case "application/pdf":
    case FileAttachType.Pdf:
      return <FaFilePdf style={{ color: "#D0021B" }} className="text-3xl" />;

    // Excel files
    case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
    case "application/vnd.ms-excel": // for .xls files
      return <FaFileExcel style={{ color: "#217346" }} className="text-3xl" />;

    // Image files
    case "image/png":
    case "image/jpeg":
    case "image/jpg":
    case "image/gif":
    case FileAttachType.Image:
      return (
        <Image
          src={url}
          width={"100%"}
          height={"100%"}
          className="!w-full !h-full object-cover"
        />
      );

    // Text files and other document formats
    case "text/plain":
    case "application/vnd.openxmlformats-officedocument.presentationml.presentation":
    case "application/vnd.ms-powerpoint": // PowerPoint files
    case "application/json":
      return <FaFileAlt style={{ color: "#4A4A4A" }} className="text-3xl" />;

    // Default icon for unknown types
    default:
      return <FaFile className="text-3xl" />;
  }
}

const FileIcon = ({
  mimeType,
  url,
  className,
}: {
  mimeType?: string;
  url?: string;
  className?: string;
}) => {
  return (
    <div
      className={clsx(
        "relative w-[80px] h-[80px] flex justify-center items-center",
        className
      )}
    >
      {mimeType ? getFileIcon(mimeType, url) : null}
    </div>
  );
};

export default FileIcon;
