import { Image } from "antd";
import clsx from "clsx";
import { ReactComponent as FaFileWord } from "assets/svgs/word.svg";
import { ReactComponent as FaFileAlt } from "assets/svgs/contract.svg";
import { ReactComponent as FaFileExcel } from "assets/svgs/Excel.svg";
import { ReactComponent as FaFileImage } from "assets/svgs/Image.svg";
import { ReactComponent as FaFilePdf } from "assets/svgs/PDF.svg";
import { ReactComponent as FaNA } from "assets/svgs/Unknow.svg";
import { ReactComponent as FaFileText } from "assets/svgs/Text.svg";
import { ReactComponent as FaFileVideo } from "assets/svgs/Video.svg";

import { FileAttachType } from "types/fileAttach";
import { ReactComponent as FileIconSvg } from "assets/svgs/file.svg"; // Example SVG icon for default file type

function getFileIcon(mimeType: string, url?: string) {
  switch (mimeType) {
    // Word documents
    case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
    case "application/msword": // for .doc files
      return <FaFileWord style={{ color: "#2B579A" }} className="text-3xl" />;

    // PDF files
    case "application/pdf":
    case FileAttachType.Pdf:
      return <FaFilePdf style={{ color: "#D0021B" }} className="text-3xl" />;

    // Excel files
    case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
    case "application/vnd.ms-excel":
    case "application/vnd.ms-excel.template.macroEnabled.12":
      return <FaFileExcel style={{ color: "#217346" }} className="text-3xl" />;

    // Text files
    case "text/plain":
      return <FaFileText style={{ color: "#000000" }} className="text-3xl" />;

    // Video files
    case "video/mp4":
      return <FaFileVideo style={{ color: "#000000" }} className="text-3xl" />;

    // Image files
    case "image/png":
    case "image/jpeg":
    case "image/jpg":
    case "image/gif":
    case FileAttachType.Image:
      return <FaFileImage style={{ color: "#000000" }} className="text-3xl" />;

    // Text files and other document formats
    case "application/vnd.openxmlformats-officedocument.presentationml.presentation":
    case "application/vnd.ms-powerpoint": // PowerPoint files
    case "application/json":
      return <FaNA style={{ color: "#4A4A4A" }} className="text-3xl" />;

    // Default icon for unknown types
    default:
      return <FaNA className="text-xl size-[24px]" />;
  }
}

const FileIcon = ({
  mimeType,
  url,
  className,
  width = 80,
  height = 80,
}: {
  mimeType?: string;
  url?: string;
  className?: string;
  width?: number;
  height?: number;
}) => {
  return (
    <div
      className={clsx("relative flex justify-center items-center", className)}
      style={{ width, height }}
    >
      {mimeType ? getFileIcon(mimeType, url) : null}
    </div>
  );
};

export default FileIcon;
