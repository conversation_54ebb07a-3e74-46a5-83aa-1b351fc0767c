import {
  Card,
  Col,
  Row,
  Form,
  Spin,
  message,
  Input,
  Select,
  Tabs,
  InputNumber,
  Button,
} from "antd";
import PageTitle from "components/PageTitle/PageTitle";
import React, { useEffect, useMemo, useState } from "react";
import { Rule } from "antd/lib/form";
import CustomButton from "components/Button/CustomButton";
import { ModalStatus } from "types/modal";
import { PermissionNames } from "types/PermissionNames";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { get, isEmpty } from "lodash";
import { formatVND, getTitle } from "utils";
import { BOQ, BOQType, BOQDetail, BOQDetailProcessed } from "types/boq";
import { boqApi } from "api/boq.api";
import { DictionarySelector } from "components/Selector/DictionarySelector";
import { DictionaryType } from "types/dictionary";
import { ProjectSelector } from "components/Selector/ProjectSelector";
import { observer } from "mobx-react";
import { checkEditPermissionByCreator, checkRoles } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import TextArea from "antd/es/input/TextArea";
import { UnitSelector } from "components/Selector/UnitSelector";
import { FileAttach } from "types/fileAttach";
import { Staff } from "types/staff";
import {
  ApprovalStepsCard,
  StepItem,
} from "components/ApproveProcess/ApprovalStepsCard";
import { transformApproveData } from "components/ApproveProcess/approveUtil";
import { ApprovalListType } from "types/approvalList";
import { approvalListApi } from "api/approvalList.api";
import { fileAttachApi } from "api/fileAttach.api";
import clsx from "clsx";
import { CommentView } from "components/Comment/CommentView";
import { FileUploadMultiple2 } from "components/Upload/FileUploadMultiple2";
import { FollowerSelector } from "components/Follower/FollowerSelector";
import { useProjectItem } from "hooks/useProjectItem";
import { appStore } from "store/appStore";
import CustomInput from "components/Input/CustomInput";
import BoqTable from "./BoqTable";
import { requiredRule } from "utils/validateRule";
import { ProjectItem } from "types/projectItem";
import { BoqModal } from "./components/BoqModal";
import { MemberShip } from "types/memberShip";
import { ApprovalTemplateType } from "types/approvalTemplate";
import { useApprovalStep } from "hooks/useAppovalStep";
import { toJS } from "mobx";
import { userStore } from "store/userStore";
import dayjs from "dayjs";
import { ProjectItemSelector } from "components/Selector/ProjectItemSelector";

const rules: Rule[] = [{ required: true, message: "Trường này là bắt buộc" }];

interface CreateOrUpdateBoqPageProps {
  title: string;
  status: ModalStatus;
}

// Thêm enum cho loại tạo BOQ
// export enum CreateBOQType {
//   BOQ = "BOQ", // Tạo BOQ
// }
interface BoqForm extends BOQ {
  projectId: number;
  unitId: number;
  projectItemId: number;
  boqGroupId: number;
  convertUnitId: number;
  floor: string;
  taskCode: string;
  length: number;
  width: number;
  diameter: number;
  volume: number;
  unitPrice: number;
  height: number;
  title: string;
  estimatedBudget: number;
  createdDate: string;
  actualSpent: number;
  evaluation: number;
}

const BOQ_TYPE_OPTIONS = Object.values(BOQType).map((type) => ({
  label: {
    [BOQType.MATERIAL]: "Nguyên vật liệu",
    [BOQType.LABOR]: "Nhân công",
    [BOQType.OTHER]: "Khác",
  }[type],
  value: type,
}));

function CreateOrUpdateBoqPage({
  title = "",
  status,
}: CreateOrUpdateBoqPageProps) {
  const { haveEditPermission, haveViewAllPermission } = checkRoles(
    {
      edit: PermissionNames.boqEdit,
      viewAll: PermissionNames.boqViewAll,
    },
    permissionStore.permissions
  );

  const [form] = Form.useForm<any>();
  const [loading, setLoading] = useState(false);
  const [selectedBoq, setSelectedBoq] = useState<BOQ>();
  const navigate = useNavigate();
  const [readonly, setReadonly] = useState(true);
  const [loadingFetch, setLoadingFetch] = useState(false);
  const params = useParams();
  const [searchParams] = useSearchParams();
  const [fileList, setFileList] = useState<FileAttach[]>([]);
  const [removeApprovalList, setRemoveApprovalList] = useState<number[]>([]);
  const [loadingApprove, setLoadingApprove] = useState(false);
  const [commentRefreshTrigger, setCommentRefreshTrigger] = useState(0);
  const [boqDetails, setBoqDetails] = useState<BOQDetail[]>([]);
  const [projectItem, setProjectItem] = useState<ProjectItem>();
  const [totalPrice, setTotalPrice] = useState(0);

  const boqModalRef = React.useRef<BoqModal>(null);

  // Thêm state để xác định loại tạo BOQ
  // const [createType, setCreateType] = useState<CreateBOQType>(
  //   CreateBOQType.BOQ
  // );

  // Sử dụng useProjectItem để lấy data hạng mục
  // const {
  //   fetchData: fetchProjectItem,
  //   projectItems,
  //   loading: loadingProjectItem,
  //   query,
  //   setQuery,
  //   total,
  //   isEmptyQuery,
  // } = useProjectItem({
  //   initQuery: {
  //     limit: 0,
  //     page: 1,
  //     projectId: appStore?.currentProject?.id,
  //   },
  // });

  const {
    followers,
    setFollowers,
    approvalSteps,
    setApprovalSteps,
    fetchApprovalTemplate,
  } = useApprovalStep();

  // Hàm tính toán tự động các giá trị
  const calculateValues = (values: any) => {
    const materialCost = Number(values.materialCost) || 0;
    const labor = Number(values.labor) || 0;
    const total = materialCost + labor;
    const quantity = Number(values.quantity) || 0;
    const estimatedBudget = total * quantity;
    const amount = total * quantity;

    // Cập nhật các field được tính toán
    form.setFieldsValue({
      total: total,
      estimatedBudget: estimatedBudget,
      amount: amount,
    });
  };

  const setDataToForm = (data: BOQ) => {
    // Tính toán các giá trị tự động
    const materialCost = data.materialCost || 0;
    const labor = data.labor || 0;
    const total = materialCost + labor;
    const quantity = data.quantity || 0;
    const estimatedBudget = total * quantity;
    const amount = data.amount || 0;

    form.setFieldsValue({
      ...data,
      projectId: data.project?.id,
      unitId: data.unit?.id,
      projectItemId: data.projectItem?.id,
      boqGroupId: data.boqGroup?.id,
      convertUnitId: data.convertUnit?.id,
      name: data.title || (data as any).name, // Map title to name
      total: total,
      estimatedBudget: estimatedBudget,
      amount: amount,
      createdDate: data.createdAt
        ? dayjs(data.createdAt).format("YYYY-MM-DD")
        : "",
      actualSpent: 0,
    });

    // Fetch project items khi có projectId
    // if (data.project?.id) {
    //   query.projectId = data.project.id;
    //   setQuery({ ...query });
    // }
    // fetchProjectItem();

    // Set projectItem khi có data.projectItem (trường hợp update)
    if (data.projectItem) {
      setProjectItem(data.projectItem as unknown as ProjectItem);
    }

    setFileList(data.fileAttaches ? [...data.fileAttaches] : []);
    const transformedApproveData = transformApproveData(
      data.approvalLists,
      data.createdBy
    );
    setApprovalSteps(transformedApproveData);
    setFollowers(data.followMemberShips || []);
    setBoqDetails(assignLevel(data.boqDetails));
  };

  const assignLevel = (boqDetails: BOQDetail[], level = 0): BOQDetail[] => {
    return boqDetails.map((it) => {
      if (it.children) {
        return { ...it, level, children: assignLevel(it.children, level + 1) };
      } else {
        return { ...it, level };
      }
    });
  };

  function calc({ items = [] }: { items?: BOQDetail[] }): number {
    let amount = 0;

    for (const item of items) {
      amount += item.amount;

      if (item.children && item.children.length > 0) {
        const childAmount = calc({ items: item.children });

        amount += childAmount;
      }
      item.children = [];
    }

    return amount;
  }

  const getTotalPrice = (boqDetails: BOQDetail[]) => {
    const total = calc({ items: boqDetails });
    setTotalPrice(total);
    // Cập nhật amount trong form khi totalPrice thay đổi
    // form.setFieldValue("amount", total);
  };

  const getOneBoq = async (id: number) => {
    try {
      setLoadingFetch(true);
      const { data } = await boqApi.findOne(id, {
        version: selectedBoq?.version || 1,
      });

      if (isEmpty(data)) {
        navigate("/404");
        return;
      }

      setSelectedBoq(data);
      setDataToForm(data);
      getTotalPrice(data.boqDetails || []);

      return data as BOQ;
    } catch (e: any) {
      message.error("Có lỗi xảy ra khi tải dữ liệu");
    } finally {
      setLoadingFetch(false);
    }
  };

  useEffect(() => {
    document.title = getTitle(title);
    getTotalPrice(boqDetails);
    // fetchProjectItem();
    if (status === "update") {
      const boqId = params.id;
      if (boqId) {
        getOneBoq(+boqId);

        // Mặc định là readonly (chế độ xem) khi vào trang update
        // Chỉ cho phép edit khi có param update=1
        setReadonly(searchParams.get("update") !== "1");
      }
    } else {
      setReadonly(false);
      setBoqDetails([]);
      // Set ngày tạo mặc định khi tạo mới

      if (!appStore.currentProject) {
        return;
      }

      form.setFieldsValue({
        projectId: appStore.currentProject.id,
        createdDate: dayjs().format("YYYY-MM-DD"),
      });

      fetchApprovalTemplate({
        projectId: appStore.currentProject.id,
        createdStaff: toJS(userStore.info) as Staff,
        type: ApprovalTemplateType.BOQ,
      });
    }
  }, []);

  // // Theo dõi thay đổi projectId và cập nhật query cho useProjectItem
  // useEffect(() => {
  //   const projectId = form.getFieldValue("projectId");
  //   if (projectId && projectId > 0) {
  //     query.projectId = projectId;
  //     setQuery({ ...query });
  //   }
  //   fetchProjectItem();
  // }, []);

  // // Theo dõi thay đổi projectItemId và cập nhật projectItem cho floor options
  // useEffect(() => {
  //   const projectItemId = form.getFieldValue("projectItemId");
  //   if (projectItemId && projectItemId > 0) {
  //     const selectedProjectItem = projectItems.find(
  //       (item) => item.id === projectItemId
  //     );
  //     if (selectedProjectItem) {
  //       setProjectItem(selectedProjectItem);
  //     }
  //   }
  // }, [projectItems, form.getFieldValue("projectItemId")]);

  useEffect(() => {
    form.setFieldValue("amount", totalPrice);
  }, [totalPrice]);

  // // Hàm xử lý khi project thay đổi
  // const handleProjectChange = (projectId: number) => {
  //   if (projectId && projectId > 0) {
  //     queryprojectItem.projectId = projectId;
  //     setQueryprojectItem({ ...queryprojectItem });
  //     fetchprojectItem();
  //     // Reset hạng mục khi đổi project
  //     form.setFieldValue("projectItemId", undefined);
  //   }
  // };

  const getDataSubmit = async () => {
    const {
      projectId,
      unitId,
      projectItemId,
      boqGroupId,
      convertUnitId,
      floor,
      taskCode,
      length,
      width,
      diameter,
      volume,
      unitPrice,
      height,
      name,
      estimatedBudget,
      createdDate,
      actualSpent,
      evaluation,
      ...data
    } = form.getFieldsValue();

    // Cập nhật amount từ totalPrice trước khi submit
    form.setFieldValue("amount", totalPrice);

    const fileAttachIds: number[] = [];
    for (const file of fileList) {
      if (file.id) {
        fileAttachIds.push(file.id);
      } else if (file.originFile) {
        const { data } = await fileAttachApi.upload(file.originFile);
        const resFileAttach = await fileAttachApi.create({
          fileAttach: {
            ...file,
            // url: $url(data.path),
          },
        });
        fileAttachIds.push(resFileAttach.data.id);
      }
    }

    const approvalLists = approvalSteps.map((e, i) => ({
      id: e.id,
      name: e.name,
      type: ApprovalListType.BOQ,
      position: e.position,
      note: e.note,
      memberShipId: e.memberShipId,
      memberShip2Id: e.memberShip2Id,
      staffId: e.staffId,
      boqId: selectedBoq!?.id || 0,
    }));

    const payloadBoqDetails = checkBoqDetailsPayload(boqDetails);
    // Tạo payload cho BOQ theo API body mới
    const payload = {
      projectItemId: projectItemId || 0,
      boqGroupId: boqGroupId || 0,
      // boqDetails: payloadBoqDetails || [],
      boq: {
        code: data.code || "",
        name: name || "", // Sử dụng name từ form
        description: data.description || "",
        floor: floor || "",
        quantity: data.quantity || 0,
        amount: data.amount || 0,
        remarks: data.remarks || "",
        isActive: true,
        note: data.note || "",
      },
      followMemberShipIds: followers?.map((it) => it.id),
      approvalLists,
    };

    return payload;
  };

  // const checkBoqDetailsNew = (boqDetails: BOQDetail[]): BOQDetail[] => {
  //   return boqDetails.map((it) => {
  //     if (it.isNew) {
  //       const {id, ...rest} = it;
  //       return {
  //         ...rest,
  //         children: it.children ? checkBoqDetailsNew(it.children) : [],
  //       };
  //     } else {
  //       return {
  //         ...it,
  //         children: it.children ? checkBoqDetailsNew(it.children) : []
  //       };
  //     }
  //   });
  // };

  // const checkBoqDetailsPayload = (
  //   boqDetails: BOQDetail[]
  // ): BOQDetailProcessed[] => {
  //   return boqDetails.map((it) => {
  //     const processedChildren = it.children
  //       ? checkBoqDetailsPayload(it.children)
  //       : [];

  //     // Process unit: extract id if unit is an object, otherwise use unit value
  //     const unitId =
  //       it.unit && typeof it.unit === "object" && "id" in it.unit
  //         ? it.unit.id
  //         : it.unit;

  //     // Process workType: extract id if workType is an object, otherwise use workType value
  //     const workTypeId =
  //       it.workType && typeof it.workType === "object" && "id" in it.workType
  //         ? it.workType.id
  //         : it.workType;

  //     if (it.isNew) {
  //       const { id, unit, workType, ...rest } = it;
  //       return {
  //         ...rest,
  //         unitId: unitId ?? undefined,
  //         workTypeId: workTypeId ?? undefined,
  //         children: processedChildren,
  //       };
  //     } else {
  //       const { unit, workType, ...rest } = it;
  //       return {
  //         ...rest,
  //         unitId: unitId ?? undefined,
  //         workTypeId: workTypeId ?? undefined,
  //         children: processedChildren,
  //       };
  //     }
  //   });
  // };

  const checkBoqDetailsPayload = (boqDetails: BOQDetail[]) => {
    console.log(boqDetails);
  };

  const createData = async () => {
    const valid = await form.validateFields();

    setLoading(true);
    try {
      const data = await getDataSubmit();
      await boqApi.create(data);
      message.success("Tạo BOQ thành công!");
      navigate(`/boq/${PermissionNames.boqList}`);
    } finally {
      setLoading(false);
    }
  };

  const updateData = async () => {
    const valid = await form.validateFields();

    setLoading(true);
    try {
      const data = await getDataSubmit();
      const res = await boqApi.update(selectedBoq!?.id || 0, data);
      setSelectedBoq({ ...selectedBoq, ...res.data });
      message.success("Chỉnh sửa BOQ thành công!");
      await getOneBoq(selectedBoq!.id || 0);
      getTotalPrice(boqDetails);
      setReadonly(true);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = () => {
    if (status === "create") {
      createData();
    } else {
      updateData();
    }
  };

  const pageTitle = useMemo(() => {
    if (status === "create") {
      return "Tạo BOQ";
    }
    return "Chỉnh sửa BOQ";
  }, [status]);

  const handleApproveProcess = async (data: any) => {
    try {
      setLoadingApprove(true);
      await boqApi.approve(selectedBoq!.id || 0, data);
      message.success("Duyệt BOQ thành công!");
      await getOneBoq(selectedBoq!.id || 0);
      setCommentRefreshTrigger((prev) => prev + 1);
    } finally {
      setLoadingApprove(false);
    }
  };

  const handleRejectProcess = async (data: any) => {
    try {
      setLoadingApprove(true);
      await boqApi.reject(selectedBoq!.id || 0, data);
      message.success("Từ chối BOQ thành công!");
      await getOneBoq(selectedBoq!.id || 0);
      setCommentRefreshTrigger((prev) => prev + 1);
    } finally {
      setLoadingApprove(false);
    }
  };

  const canEditRecord = (record: BOQ) => {
    if (!record) return null;
    return checkEditPermissionByCreator(
      userStore.info.id,
      record.createdBy?.id,
      haveEditPermission,
      haveViewAllPermission
    );
  };

  // Render form fields dựa trên loại tạo
  const renderFormFields = () => {
    // Hiển thị form đầy đủ cho tạo BOQ hoặc chỉnh sửa
    return (
      <Row gutter={16}>
        {/* Row 1 - Thông tin cơ bản */}
        <Col span={8}>
          <Form.Item name="code" label="Mã BOQ" rules={rules}>
            <Input placeholder="Nhập mã BOQ" />
          </Form.Item>
        </Col>

        <Col span={8}>
          <CustomInput
            label="Dự án"
            type="text"
            disabled={true}
            value={appStore?.currentProject?.name || ""}
            className="mt-1"
          />
        </Col>

        <Col span={8}>
          <Form.Item name="name" label="Tiêu đề*" rules={rules}>
            <Input placeholder="Nhập tiêu đề" />
          </Form.Item>
        </Col>

        {/* Row 2 - Phân loại */}
        <Col span={8}>
          <Form.Item
            name="projectItemId"
            label="Hạng mục"
            rules={[requiredRule]}
          >
            <ProjectItemSelector
              placeholder="Chọn hạng mục"
              valueIsOption
              onChange={(selectedProjectItem) => {
                console.log({ selectedProjectItem });
                if (selectedProjectItem) {
                  setProjectItem(selectedProjectItem);
                  // Reset giá trị tầng khi thay đổi hạng mục
                  form.setFieldValue("floor", undefined);
                  form.setFieldValue("projectItemId", selectedProjectItem.id);
                } else {
                  setProjectItem(undefined);
                }
              }}
            />
            {/* <Select
              placeholder="Chọn hạng mục"
              loading={loadingProjectItem}
              showSearch
              onChange={(value) => {
                const projectItem = projectItems.find(
                  (item) => item.id === value
                );
                setProjectItem(projectItem);
              }}
              filterOption={(input, option) =>
                (option?.label ?? "")
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
              options={projectItems
                .filter((item) => item.isActive)
                .map((item) => ({
                  label: item.name,
                  value: item.id,
                }))}
            /> */}
          </Form.Item>
        </Col>

        <Col span={8}>
          <Form.Item name="boqGroupId" label="Nhóm BOQ">
            <DictionarySelector
              placeholder="Chọn nhóm"
              initQuery={{
                type: DictionaryType.BOQGroup,
              }}
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item name="floor" label="Tầng">
            {/* <Select
              placeholder={projectItem ? "Chọn tầng" : "Vui lòng chọn hạng mục trước"}
              disabled={!projectItem}
              allowClear
              options={
                projectItem?.floors
                  ? Array.from({ length: projectItem.floors }, (_, index) => ({
                      label: (index + 1).toString(),
                      value: index + 1,
                    }))
                  : []
              }
              onChange={(value) => {
                form.setFieldValue("floor", value);
              }}
            /> */}

            {readonly ? (
              <div
                style={
                  {
                    // padding: "4px 11px",
                    // border: "1px solid #d9d9d9",
                    // borderRadius: "6px",
                    // backgroundColor: "#f5f5f5",
                  }
                }
              >
                {form.getFieldValue("floor")
                  ? `Tầng ${form.getFieldValue("floor")}`
                  : "-"}
              </div>
            ) : (
              <Select
                placeholder={
                  projectItem ? "Chọn tầng" : "Vui lòng chọn hạng mục trước"
                }
                disabled={!projectItem}
                allowClear
                options={
                  projectItem?.floors
                    ? Array.from(
                        { length: projectItem.floors },
                        (_, index) => ({
                          label: `Tầng ${index + 1}`,
                          value: index + 1,
                        })
                      )
                    : []
                }
                onChange={(value) => {
                  form.setFieldValue("floor", value);
                }}
              />
            )}
          </Form.Item>
        </Col>
        <Col span={8}>
          <div style={{ marginBottom: 4 }}>
            <span className="font-bold">Tổng thành tiền</span>
          </div>
          <span>{formatVND(totalPrice)} VNĐ</span>
        </Col>
        {/* <Form.Item name="amount">
        <CustomInput
            label="Tổng thành tiền (VNĐ)"
            type="text"
            disabled={true}
            value={formatVND(totalPrice)}
            onChange={(e) => {
              form.setFieldValue("amount", e.target.value);
            }}
            className="mt-1"
          />
        </Form.Item> */}
        <Col span={8}>
          <div style={{ marginBottom: 4 }}>
            <span className="font-bold">Phiên bản</span>
          </div>
          <span>{selectedBoq?.version || 0}</span>
        </Col>
        <Col span={8}>
          {/* <CustomInput
            label="Phiên bản"
            type="text"
            placeholder="Phiên bản"
            disabled={true}
            value={selectedBoq?.version || ""}
            className="mt-1"
          /> */}
        </Col>
        {/* <Col span={8}>
          <div style={{ marginBottom: 4 }}>
            <span className="font-bold">Phiên bản</span>
          </div>
          <Select
            placeholder="Chọn version"
            options={
              selectedBoq?.version
                ? Array.from({ length: selectedBoq.version }, (_, index) => ({
                    label: `Phiên bản ${selectedBoq.version - index}`,
                    value: index + 1,
                  }))
                : []
            }
            onChange={handleVersionChange}
            style={{ width: "100%" }}
            value={selectedVersion}
          />
        </Col> */}
        {/* <Col span={8}>
          <Button
            type="primary"
            className="mt-6"
            onClick={() => {
              boqModalRef.current?.handleOpen(selectedBoq);
            }}
          >
            Xem lịch sử phiên bản
          </Button>
          <BoqModal
            onClose={() => {}}
            onSubmitOk={() => {}}
            ref={boqModalRef}
          />
        </Col> */}
      </Row>
    );
  };

  useEffect(() => {}, [readonly]);

  return (
    <div className="app-container">
      <BoqModal onClose={() => {}} onSubmitOk={() => {}} ref={boqModalRef} />
      <PageTitle
        back
        breadcrumbs={[
          { label: "Báo cáo" },
          {
            label: "Danh sách BOQ",
            href: `/boq/${PermissionNames.boqList}`,
          },
          { label: pageTitle },
        ]}
        title={pageTitle}
      />
      <Spin spinning={loadingFetch}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          disabled={readonly}
          className={clsx(readonly ? "readonly" : "")}
        >
          <Row gutter={24}>
            <Col span={18}>
              <Card className="content-card">
                <Card
                  title={
                    <div className="flex justify-between items-center">
                      <span>Thông tin BOQ</span>
                      {status === "update" && (
                        <Button
                          type="primary"
                          onClick={() => {
                            boqModalRef.current?.handleOpen(
                              selectedBoq,
                              params.id
                            );
                          }}
                          disabled={false}
                        >
                          Xem lịch sử phiên bản
                        </Button>
                      )}
                    </div>
                  }
                  className="mb-0 form-card"
                >
                  {renderFormFields()}
                </Card>

                {/* Chỉ hiển thị tabs khi không phải tạo BOQ (tiêu đề) */}
                {/* {!(
                  createType === CreateBOQType.BOQ && status === "create"
                ) && (
                  <Tabs defaultActiveKey="0" type="line" className="mt-[16px]">
                    <Tabs.TabPane tab="Bình luận" key="0">
                      <CommentView
                        initQuery={{ boqId: selectedBoq?.id }}
                        refreshTrigger={commentRefreshTrigger}
                      />
                    </Tabs.TabPane>
                    <Tabs.TabPane tab="Tệp đính kèm" key="1">
                      <Form.Item
                        shouldUpdate={true}
                        style={{ marginBottom: 0, height: "100%" }}
                        className="form-height-full"
                      >
                        {() => (
                          <Form.Item
                            label={""}
                            noStyle
                            style={{ marginBottom: 0 }}
                            name="files"
                            className="h-full"
                          >
                            <FileUploadMultiple2
                              hideUploadButton={readonly}
                              showSearch
                              className="h-full"
                              fileList={fileList}
                              onUploadOk={(file) => {
                                setFileList([...fileList, file]);
                              }}
                              onDelete={(file) => {
                                const newFileList = fileList.filter(
                                  (f) => f.uid !== file.uid
                                );
                                setFileList(newFileList);
                              }}
                            />
                          </Form.Item>
                        )}
                      </Form.Item>
                    </Tabs.TabPane>
                  </Tabs>
                )} */}

                {/* Hiển thị BoqTable khi có selectedBoq (trường hợp update) */}

                <div className="mt-[16px]">
                  <BoqTable
                    isEdit={readonly ? false : true}
                    boqDetails={boqDetails}
                    setBoqDetails={setBoqDetails}
                    // initialData={boqDetails}
                  />
                </div>

                <div className="mt-[16px] flex justify-end gap-[12px]">
                  {!readonly && (
                    <CustomButton
                      variant="outline"
                      onClick={() => {
                        if (status === "create") {
                          navigate(`/boq/${PermissionNames.boqList}`);
                        } else {
                          setDataToForm(selectedBoq!);
                          setReadonly(true);
                        }
                      }}
                    >
                      Hủy
                    </CustomButton>
                  )}
                  <CustomButton
                    loading={loading}
                    onClick={() => {
                      if (!readonly) {
                        handleSubmit();
                      } else {
                        setReadonly(false);
                      }
                    }}
                    disabled={status === "update" && !haveEditPermission}
                  >
                    {status === "create"
                      ? "Tạo BOQ"
                      : readonly
                      ? "Chỉnh sửa"
                      : "Lưu thay đổi"}
                  </CustomButton>
                </div>
              </Card>
            </Col>

            {/* Chỉ hiển thị sidebar khi không phải tạo BOQ (tiêu đề) */}
            {/* {!(createType === CreateBOQType.BOQ && status === "create") && ( */}
            <Col span={6}>
              <ApprovalStepsCard
                steps={approvalSteps}
                loading={loadingApprove}
                onSelectStep={setApprovalSteps}
                onRemove={setRemoveApprovalList}
                onApprove={handleApproveProcess}
                onReject={handleRejectProcess}
                templateType={ApprovalTemplateType.BOQ}
                editable={true}
              />

              <FollowerSelector
                followers={followers}
                setFollowers={setFollowers}
                readonly={readonly}
                headerTitle={`Người theo dõi (${followers?.length})`}
              />
            </Col>
            {/* )} */}
          </Row>
        </Form>
      </Spin>
    </div>
  );
}

export default observer(CreateOrUpdateBoqPage);
