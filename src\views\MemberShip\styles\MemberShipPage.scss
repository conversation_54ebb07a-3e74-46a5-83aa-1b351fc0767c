.expandable-membership-table {
  .ant-table-thead > tr > th {
    color: white !important;
    font-weight: 500 !important;
    border-color: #4b5563 !important;
  }

  .ant-table-tbody > tr.ant-table-row:hover > td {
    background-color: #f9fafb !important;
  }

  // .ant-table-tbody > tr {
  //   background-color: var(--color-neutral-n2) !important;
  // }

  // .ant-table-tbody > tr:nth-child(even) {
  //   background-color: var(--color-neutral-n2) !important;
  // }

  // .ant-table-tbody > tr:nth-child(odd) {
  //   background-color: var(--color-neutral-n2) !important;
  // }

  // .ant-table-tbody > tr:nth-child(even):hover > td {
  //   background-color: var(--color-neutral-n2) !important;
  // }

  // .ant-table-tbody > tr:nth-child(odd):hover > td {
  //   background-color: var(--color-neutral-n2) !important;
  // }

  .ant-table-expanded-row .ant-table-thead {
    display: none !important;
  }

  // Remove background color for expanded rows
  .ant-table-expanded-row .ant-table-tbody > tr {
    // background-color: unset !important;

    // &:hover > td {
    //   background-color: unset !important;
    // }

    // &:nth-child(even) {
    //   background-color: unset !important;
    // }

    // &:nth-child(odd) {
    //   background-color: unset !important;
    // }
  }

  // Fix width and scroll issues for expanded rows
  .ant-table-expanded-row {
    .ant-table-content {
      overflow: hidden !important;
      width: 100% !important;
    }

    .ant-table {
      width: 100% !important;
      table-layout: fixed !important; // Force table to respect width constraints
    }

    .ant-table-body {
      overflow: hidden !important;
    }

    .ant-table-container {
      overflow: hidden !important;
      width: 100% !important;
    }
  }

  .ant-table-expand-icon-col {
    width: 48px !important;
  }

  .ant-table-row-expand-icon {
    border: none !important;
    background: transparent !important;
  }
}
