import {
  CheckOutlined,
  CloseOutlined,
  DeleteOutlined,
  DownOutlined,
  ExportOutlined,
  ImportOutlined,
  LockOutlined,
  PlusOutlined,
  SearchOutlined,
  UnlockOutlined,
} from "@ant-design/icons";
import {
  Button,
  Card,
  Checkbox,
  Input,
  message,
  Popconfirm,
  Space,
  Spin,
  Table,
  TableProps,
  Tag,
} from "antd";
import Column from "antd/es/table/Column";
import { Pagination } from "components/Pagination";
import { useComponent } from "hooks/useComponent";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  Component,
  ComponentShowTypeTrans,
  DisplayImageTypeTrans,
  RelationComponent,
} from "types/component";
import { formatVND, getTitle } from "utils";
import { $url } from "utils/url";
import {
  ComponentModal,
  CreateComponentModal,
} from "./Components/CreateComponentModal";
import { componentApi } from "api/component.api";
import { handleExport, MyExcelColumn } from "utils/MyExcel";
import ImportSettingComponent, {
  ImportSettingComponentModal,
} from "components/ImportDocument/ImportSettingComponent";
import { removeSubstringFromKeys } from "utils/common";
import DropdownCell from "components/Table/DropdownCell";
import { ProductSelector } from "components/Selector/ProductSeletor";
import { debounce } from "lodash";
const exportColumns: MyExcelColumn<Component>[] = [
  {
    header: "Tên nhóm",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "componentGroup",
    columnKey: "componentGroup",
    render: (record) => record?.componentGroup?.name,
  },
  {
    header: "TP mặc định của nhóm",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "isDefault",
    columnKey: "isDefault",
    render: (record) => (record?.isDefault ? "Phải" : "Không"),
  },
  {
    header: "Mã thành phần",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "code",
    columnKey: "code",
  },
  {
    header: "Mã sản phẩm",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "productId",
    columnKey: "productId",
    render: (record) => record?.product?.code,
  },
  {
    header: "NVL",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "materialType",
    columnKey: "materialType",
    render: (record) => record?.materialType?.name,
  },
  {
    header: "Tên nội bộ",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "privateName",
    columnKey: "privateName",
  },
  {
    header: "Tên hiển thị",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "name",
    columnKey: "name",
  },
  {
    header: "Nhóm điều kiện",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "depComponents",
    columnKey: "depComponents",
    render: (record) =>
      record.depComponents
        ?.map((item: any) => item.component2?.code)
        .join(", "),
  },
  {
    header: "Nhóm loai trừ",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "excludeComponents",
    columnKey: "excludeComponents",
    render: (record) =>
      record.excludeComponents
        ?.map((item: any) => item.component2?.code)
        .join(", "),
  },
  {
    header: "Nhóm cha",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "parentId",
    columnKey: "parentId",
    render: (record) => record?.parent?.code,
  },
  {
    header: "Mô tả",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "desc",
    columnKey: "desc",
  },
  {
    header: "Truy xuất thông tin mô tả",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "featureImageShowType",
    columnKey: "featureImageShowType",
    render: (record: Component) =>
      ComponentShowTypeTrans[record?.featureImageShowType]?.label || "-",
  },
  {
    header: "Truy xuất hình hiển thị",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "displayImage",
    columnKey: "displayImage",
    render: (record: Component) =>
      DisplayImageTypeTrans[record?.displayImage]?.label || "-",
  },
  {
    header: "Giá cộng thêm ($)",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "extraPrice",
    columnKey: "extraPrice",
    render: (record) => formatVND(record.extraPrice),
  },
  {
    header: "Định mức",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "standard",
    columnKey: "standard",
  },

  {
    header: "Thêu tên",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "isEmbroidery",
    columnKey: "isEmbroidery",
    render: (record) => (record.isEmbroidery == true ? "Có" : "Không"),
  },
  {
    header: "Số ký tự tối đa",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "maxLength",
    columnKey: "maxLength",
  },
  {
    header: "Vị trí",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "position",
    columnKey: "position",
  },
  {
    header: "Trạng thái",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "isBlocked",
    columnKey: "isBlocked",
    render: (record) => (record.isBlocked == true ? "Khóa" : "Mở"),
  },
  // {
  //   header: "Hình mặt trước",
  //   headingStyle: {
  //     font: {
  //       bold: true,
  //     },
  //   },
  //   key: "avatar",
  //   columnKey: "avatar",
  //   render(record) {
  //     return $url(record.fileAttachAvatar?.url);
  //   },
  // },
  // {
  //   header: "Hình mặt sau",
  //   headingStyle: {
  //     font: {
  //       bold: true,
  //     },
  //   },
  //   key: "avatarBack",
  //   columnKey: "avatarBack",
  //   render(record) {
  //     return $url(record.fileAttachAvatarBack?.url);
  //   },
  // },

  {
    header: "Hình feature",
    headingStyle: {
      font: {
        bold: true,
      },
    },
    key: "featureImage",
    columnKey: "featureImage",
    render(record) {
      return $url(record.fileAttachFeatureImage?.url);
    },
  },
];
export const ComponentConfiguration = ({ title = "" }) => {
  const [loadingDelete, setLoadingDelete] = useState(false);
  const [selectedProductId, setSelectedProductId] = useState<number>();
  useEffect(() => {
    document.title = getTitle(title);
  }, []);
  const { components, fetchData, query, loading, setQuery, total } =
    useComponent({
      initQuery: {
        page: 1,
        limit: 10,
        search: "",
      },
    });
  const {
    components: mainComponent,
    fetchData: fetchMainComponent,
    query: mainComponentQuery,
  } = useComponent({
    initQuery: {
      page: 1,
      limit: 50,
      isMainComponent: true,
      productMainComponentId: selectedProductId,
    },
  });
  useEffect(() => {
    if (selectedProductId) {
      mainComponentQuery.productMainComponentId = selectedProductId;
      fetchMainComponent();
    }
  }, [selectedProductId, mainComponentQuery]);
  const lastFilterChanged = useRef<"name" | "code" | undefined>();
  // useEffect(() => {
  //   fetchData();
  // }, [query]);
  const modalRef = React.useRef<ComponentModal>(null);
  const tagColors = [
    "magenta",
    "volcano",
    "orange",
    "gold",
    "lime",
    "green",
    "cyan",
    "blue",
    "geekblue",
    "purple",
  ];

  const getColor = (index: number) => {
    return tagColors[index % tagColors.length];
  };
  const handleDelete = async (id: number) => {
    try {
      setLoadingDelete(true);
      await componentApi.delete(id);
      message.success("Xóa thành công");
      fetchData();
    } catch (e) {
      console.log({ e });
    } finally {
      setLoadingDelete(false);
    }
  };
  const importModal = useRef<ImportSettingComponentModal>();
  const handleOnUploadedFile = async (excelData: any, setData: any) => {
    const { results } = excelData;

    console.log("results", results);

    const importData = results?.map((item: any) => {
      const refineRow = removeSubstringFromKeys(item, " (*)");

      const name = refineRow["TÊN HIỂN THỊ"] || "";
      const materialTypeName = refineRow["NVL SỬ DỤNG"] || "";
      const isDefault = refineRow["TP MẶC ĐỊNH CỦA NHÓM"] || "";
      const privateName = refineRow["TÊN NỘI BỘ"] || "";
      const code = refineRow["MÃ THÀNH PHẦN"] || "";
      const avatar = refineRow["HÌNH MẶT TRƯỚC"] || "";
      const avatarBack = refineRow["HÌNH MẶT SAU"] || "";
      const featureImage = refineRow["HÌNH FEATURE"] || "";
      const desc = refineRow["MÔ TẢ"] || "";
      const extraPrice = refineRow["GIÁ CỘNG THÊM"] || 0;
      const isEmbroidery = refineRow["THÊU TÊN"] || false;
      const maxLength = refineRow["SỐ KÝ TỰ TỐI ĐA"] || 0;
      const componentDepCodes = refineRow["NHÓM ĐIỀU KIỆN"] || "";
      const componentExcludeCodes = refineRow["NHÓM LOẠI TRỪ"] || "";
      const parentCode = refineRow["NHÓM CHA"] || "";
      // const parentName = refineRow["TÊN NHÓM"] || "";
      const productCode = refineRow["MÃ SẢN PHẨM"] || "";
      const componentGroupName = refineRow["TÊN NHÓM"];
      // const note = refineRow["GHI CHÚ TIẾNG VIỆT (DÀNH CHO THỢ MAY)"] || "";
      const featureImageShowType = refineRow["TRUY XUẤT THÔNG TIN MÔ TẢ"];

      const standard = refineRow["ĐỊNH MỨC"] || "";
      const position = refineRow["VỊ TRÍ"] || "";

      return {
        name,
        privateName,
        code,
        avatar,
        avatarBack,
        featureImage,
        isDefault,
        desc,
        extraPrice,
        isEmbroidery,
        maxLength,
        componentDepCodes,
        componentExcludeCodes,
        parentCode,
        componentGroupName,
        // parentName,
        featureImageShowType,
        standard,
        productCode,
        position,
        rowNum: item.__rowNum__,
        materialTypeName,
      };
    });
    console.log("importData", importData);

    setData(importData);
  };
  const blockComponent = async (component: Component) => {
    await componentApi.block(component.id, {
      isBlocked: !component.isBlocked,
    });

    message.success(
      `${component.isBlocked ? "Mở khóa" : "Khóa"} thành phần thành công!`
    );
    fetchData();
  };
  useEffect(() => {
    query.productId = selectedProductId;
    if (selectedProductId) {
      fetchData();
    }
  }, [selectedProductId, query]);
  const handleProductChange = (value: number) => {
    console.log("Value là", value);
    setSelectedProductId(value);
  };
  const debounceSearch = useCallback(
    debounce(
      (keyword) => setQuery({ ...query, search: keyword, page: 1 }),
      300
    ),
    [query]
  );
  const handleFilterOfTable = (pagination: any, filters: any, sorter: any) => {
    const filterCode = filters.code?.[0];
    const filterName = filters.privateName?.[0];

    const queryObject: any[] = [];

    if (filterCode && filterName) {
      // Xử lý clear 1 trong 2 theo người dùng chọn cuối
      if (lastFilterChanged.current === "name") {
        filters.code = undefined;
      } else if (lastFilterChanged.current === "code") {
        filters.privateName = undefined;
      }
    }

    if (filters.code?.[0]) {
      queryObject.push({
        type: "sort",
        field: "component.code",
        value: filters.code?.[0],
      });
    }

    if (filters.privateName?.[0]) {
      queryObject.push({
        type: "sort",
        field: "component.privateName",
        value: filters.privateName?.[0],
      });
    }
    // Filter
    const isDefaultFilter = filters.hasOwnProperty("isDefault")
      ? filters.isDefault?.[0]
      : undefined;
    const isBlocked = filters.hasOwnProperty("isBlocked")
      ? filters.isBlocked?.[0]
      : undefined;

    if (
      filters.hasOwnProperty("isDefault") &&
      filters.isDefault?.[0] !== undefined
    ) {
      queryObject.push({
        type: "single-filter",
        field: "component.isDefault",
        value: filters.isDefault[0],
      });
    }

    if (
      filters.hasOwnProperty("isBlocked") &&
      filters.isBlocked?.[0] !== undefined
    ) {
      queryObject.push({
        type: "single-filter",
        field: "component.isBlocked",
        value: filters.isBlocked[0],
      });
    }
    if (filters.componentGroup) {
      queryObject.push({
        type: "multi-filter",
        field: "componentGroup.id",
        value: filters.componentGroup,
      });
    }
    query.queryObject = JSON.stringify(queryObject);
    setQuery({ ...query });
    fetchData();
  };

  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>();
  const [deleteManyLoading, setDeleteManyLoading] = useState<boolean>(false);

  const rowSelection: TableProps<Component>["rowSelection"] = {
    onChange: (selectedRowKeys: React.Key[], selectedRows: Component[]) => {
      setSelectedRowKeys(selectedRowKeys);
    },
  };

  const handleDeleteManyComponents = async () => {
    try {
      setDeleteManyLoading(true);

      console.log(selectedRowKeys);

      const { data } = await componentApi.deleteMany({
        componentIds: selectedRowKeys,
      });

      console.log("Xóa nhiều component res:", data);

      setSelectedRowKeys([]);
      message.success("Xóa thành công");

      fetchData();
    } catch (error) {
      console.log(error);
    } finally {
      setDeleteManyLoading(false);
    }
  };

  return (
    <Card>
      <div className="filter-container">
        <Space className="flex flex-wrap">
          <div className="filter-item">
            <label htmlFor="">Sản phẩm</label>
            <ProductSelector
              allowClear
              onChange={handleProductChange}
              isJustSingleProduct
            />
          </div>
          <div className="filter-item">
            <label htmlFor="">Tìm kiếm</label>
            <Input
              allowClear
              onKeyDown={(ev) => {
                if (ev.code == "Enter") {
                  query.page = 1;
                  setQuery({ ...query });
                }
              }}
              size="middle"
              onChange={(ev) => {
                query.search = ev.currentTarget.value;
                debounceSearch(ev.target.value);
              }}
              placeholder="Tìm kiếm (mã thành phần)"
            />
          </div>
          <div className="filter-item btn">
            <Button
              onClick={fetchData}
              type="primary"
              icon={<SearchOutlined />}
            >
              Tìm kiếm
            </Button>
          </div>

          <div className="filter-item btn">
            <Button
              disabled={!selectedProductId}
              onClick={() => {
                modalRef.current?.handleCreate(selectedProductId!);
              }}
              type="primary"
              icon={<PlusOutlined />}
            >
              Thêm mới
            </Button>
          </div>

          <div className="filter-item btn">
            <Popconfirm
              disabled={!selectedRowKeys}
              onConfirm={() => {
                handleDeleteManyComponents();
              }}
              title="Xác nhận xóa những thành phần này"
            >
              <Button
                loading={deleteManyLoading}
                disabled={
                  selectedRowKeys && selectedRowKeys?.length > 0 ? false : true
                }
                danger
                icon={<DeleteOutlined />}
              >
                Xóa nhiều
              </Button>
            </Popconfirm>
          </div>

          <div className="filter-item btn">
            <Popconfirm
              title={`Bạn có muốn xuất file excel`}
              onConfirm={() =>
                handleExport({
                  onProgress(percent) {},
                  exportColumns,
                  fileType: "xlsx",
                  dataField: "components",
                  query: query,
                  api: componentApi.findAll,
                  fileName: "Danh sách cấu hình thành phần",
                  sheetName: "Danh sách cấu hình thành phần",
                })
              }
              okText={"Xuất excel"}
              cancelText={"Huỷ"}
            >
              <Button
                type="primary"
                disabled={!selectedProductId ? true : false}
                loading={false}
                icon={<ExportOutlined />}
              >
                Xuất file excel
              </Button>
            </Popconfirm>
          </div>
          <div className="filter-item btn">
            <Button
              onClick={() => {
                importModal.current?.open();
              }}
              type="primary"
              icon={<ImportOutlined />}
            >
              Nhập excel
            </Button>
          </div>
        </Space>
      </div>
      {!selectedProductId ? (
        <div className="font-semibold text-red-500">Vui lòng chọn sản phẩm</div>
      ) : (
        <Spin spinning={loading}>
          <Table
            rowSelection={rowSelection}
            className="!my-5"
            pagination={false}
            rowKey="id"
            dataSource={components}
            scroll={{ x: "max-content" }}
            onChange={handleFilterOfTable}
          >
            <Column
              title="Mã thành phần"
              dataIndex="code"
              align="left"
              key="code"
              render={(text, record: Component) => {
                return <div>{text}</div>;
              }}
              filteredValue={(() => {
                try {
                  const obj = JSON.parse(query.queryObject || "[]");
                  const item = obj.find(
                    (o: any) => o.field === "component.code"
                  );
                  return item ? [item.value] : null;
                } catch (e) {
                  return null;
                }
              })()}
              filterDropdownProps={{
                onOpenChange: (open) => {
                  if (open) lastFilterChanged.current = "code";
                },
              }}
              filterMultiple={false}
              filters={[
                { text: "A-Z", value: "ASC" },
                { text: "Z-A", value: "DESC" },
              ]}
            />
            <Column
              title="Nhóm thành phần chính"
              dataIndex="componentGroup"
              align="left"
              key="componentGroup"
              render={(componentGroup: Component, record: Component) => {
                return <div>{componentGroup?.name}</div>;
              }}
              filteredValue={(() => {
                try {
                  const obj = JSON.parse(query.queryObject || "[]");
                  const item = obj.find(
                    (o: any) => o.field === "componentGroup.id"
                  );
                  return item ? item.value : null;
                } catch (e) {
                  return null;
                }
              })()}
              filters={mainComponent.map((item) => ({
                value: item.id,
                text: item.name,
              }))}
            />
            <Column
              title="Ảnh feature"
              dataIndex="fileAttachFeatureImage"
              key="fileAttachFeatureImage"
              align="center"
              render={(text, record: Component) => {
                return (
                  <div>
                    {record.fileAttachFeatureImage && (
                      <img
                        width={40}
                        height={40}
                        style={{ objectFit: "cover" }}
                        src={$url(record.fileAttachFeatureImage?.url)}
                        alt=""
                      />
                    )}
                  </div>
                );
              }}
            />
            <Column
              title="Thành phần mặc định của nhóm"
              dataIndex="isDefault"
              key="isDefault"
              align="center"
              filterMultiple={false}
              filters={[
                { text: "Không", value: 0 },
                { text: "Phải", value: 1 },
              ]}
              filteredValue={(() => {
                try {
                  const obj = JSON.parse(query.queryObject || "[]");
                  const item = obj.find(
                    (o: any) => o.field === "component.isDefault"
                  );
                  return item ? [item.value] : null;
                } catch (e) {
                  return null;
                }
              })()}
              render={(text, record: Component) => {
                return (
                  <div>
                    {/* {record.isDefault ? <CheckOutlined /> : <CloseOutlined />} */}
                    <Checkbox disabled checked={record.isDefault} />
                  </div>
                );
              }}
            />
            <Column
              title="NVL"
              dataIndex="name"
              key="name"
              align="left"
              render={(text, record: Component) => {
                return <div>{record.materialType?.name}</div>;
              }}
            />
            <Column
              title="Tên nội bộ"
              dataIndex="privateName"
              key="privateName"
              align="left"
              render={(text, record: Component) => {
                return <div>{text}</div>;
              }}
              filterMultiple={false}
              filteredValue={(() => {
                try {
                  const obj = JSON.parse(query.queryObject || "[]");
                  const item = obj.find(
                    (o: any) => o.field === "component.privateName"
                  );
                  return item ? [item.value] : null;
                } catch (e) {
                  return null;
                }
              })()}
              filterDropdownProps={{
                onOpenChange: (open) => {
                  if (open) lastFilterChanged.current = "name";
                },
              }}
              filters={[
                { text: "A-Z", value: "ASC" },
                { text: "Z-A", value: "DESC" },
              ]}
            />
            <Column
              title="Tên hiển thị"
              dataIndex="name"
              key="name"
              align="left"
              render={(text, record: Component) => {
                return <div>{text}</div>;
              }}
            />

            <Column
              title="Vị trí"
              dataIndex="position"
              key="position"
              align="right"
              render={(text, record: Component) => {
                return <div>{text}</div>;
              }}
            />

            <Column
              title="Nhóm cha"
              dataIndex="parent"
              key="parent"
              align="left"
              // filterSearch
              // filters={components?.map((item) => ({
              //   value: item.id,
              //   text: item.name,
              // }))}
              render={(parent: Component, record: Component) => (
                <div className="flex flex-wrap gap-1 justify-start w-full">
                  {parent && (
                    <Tag color={getColor(parent?.id)}>{parent?.code}</Tag>
                  )}
                </div>
              )}
            />
            <Column
              title="Phụ thuộc vào nhóm điều kiện"
              dataIndex="depComponents"
              key="depComponents"
              align="left"
              // filterSearch
              // filters={components?.map((item) => ({
              //   value: item.id,
              //   text: item.name,
              // }))}
              render={(
                depComponents: RelationComponent[],
                record: Component
              ) => (
                <div className="flex flex-wrap gap-1 ">
                  {depComponents.map((item, index) => (
                    <Tag color={getColor(index)} key={index}>
                      {item?.component2?.code}
                    </Tag>
                  ))}
                </div>
              )}
            />
            <Column
              title="Phụ thuộc vào nhóm loại trừ"
              dataIndex="excludeComponents"
              key="excludeComponents"
              align="left"
              // filterSearch
              // filters={components?.map((item) => ({
              //   value: item.id,
              //   text: item.name,
              // }))}
              render={(
                excludeComponents: RelationComponent[],
                record: Component
              ) => (
                <div className="flex  flex-wrap gap-1">
                  {excludeComponents.map((item, index) => (
                    <Tag color={getColor(index)} key={index}>
                      {item?.component2?.code}
                    </Tag>
                  ))}
                </div>
              )}
            />
            <Column
              title="Mô tả"
              dataIndex="desc"
              key="desc"
              align="left"
              render={(text, record: Component) => {
                return <div>{text}</div>;
              }}
            />
            <Column
              title="Truy xuất thông tin mô tả"
              dataIndex="featureImageShowType"
              key="featureImageShowType"
              align="left"
              // filters={Object.values(ComponentShowTypeTrans).map((item) => ({
              //   text: item.label,
              //   value: item.value,
              // }))}
              render={(text, record: Component) => {
                return (
                  <div>
                    {ComponentShowTypeTrans[record.featureImageShowType]?.label}
                  </div>
                );
              }}
            />
            {/* <Column
              title="Truy xuất hình hiển thị"
              dataIndex="displayImage"
              key="displayImage"
              align="left"
              // filters={Object.values(ComponentShowTypeTrans).map((item) => ({
              //   text: item.label,
              //   value: item.value,
              // }))}
              render={(text, record: Component) => {
                return (
                  <div>{DisplayImageTypeTrans[record.displayImage]?.label}</div>
                );
              }}
            /> */}
            <Column
              title="Giá cộng thêm ($)"
              dataIndex="extraPrice"
              key="extraPrice"
              align="right"
              render={(text, record: Component) => {
                return <div>{formatVND(record.extraPrice)}</div>;
              }}
              sorter={(a, b) => a.extraPrice - b.extraPrice}
            />
            <Column
              title="Định mức"
              dataIndex="name"
              key="name"
              align="right"
              render={(text, record: Component) => {
                return <div>{formatVND(record.standard)}</div>;
              }}
            />
            <Column
              title="Thêu tên"
              dataIndex="isEmbroidery"
              key="isEmbroidery"
              align="center"
              render={(isEmbroidery, record: Component) => {
                return <Checkbox disabled checked={record.isEmbroidery} />;
              }}
            />
            <Column
              title="Số ký tự tối đa"
              dataIndex="maxLength"
              key="maxLength"
              align="right"
              render={(maxLength, record: Component) => {
                return <div>{maxLength}</div>;
              }}
            />
            <Column
              title="Trạng thái"
              align="center"
              dataIndex="isBlocked"
              key="isBlocked"
              filterMultiple={false}
              filters={[
                { text: "Mở", value: 0 },
                { text: "Khóa", value: 1 },
              ]}
              filteredValue={(() => {
                try {
                  const obj = JSON.parse(query.queryObject || "[]");
                  const item = obj.find(
                    (o: any) => o.field === "component.isBlocked"
                  );
                  return item ? [item.value] : null;
                } catch (e) {
                  return null;
                }
              })()}
              render={(isBlocked, record: Component) => {
                return (
                  <Tag
                    className="w-[50px] text-center"
                    color={isBlocked ? "red" : "green"}
                  >
                    {isBlocked ? "Khóa" : "Mở"}
                  </Tag>
                );
              }}
            />
            <Column
              width={120}
              fixed="right"
              align="center"
              title=""
              key="action"
              render={(text, record: Component) => (
                <DropdownCell
                  text="Thao tác"
                  items={[
                    {
                      onClick: () => "",
                      label: (
                        <Button
                          className="w-full"
                          type="primary"
                          onClick={() => {
                            modalRef.current?.handleUpdate(
                              record,
                              selectedProductId
                            );
                          }}
                        >
                          Cập nhật
                        </Button>
                      ),
                      key: "update",
                    },
                    {
                      onClick: () => "",
                      label: (
                        <Popconfirm
                          onConfirm={() => {
                            handleDelete(record.id);
                          }}
                          title="Xác nhận xóa"
                        >
                          <Button
                            className="w-full"
                            //   onClick={() => {
                            //     modalRef.current?.handleUpdate(record);
                            //   }}
                          >
                            Xóa thành phần
                          </Button>
                        </Popconfirm>
                      ),
                    },

                    {
                      label: (
                        <Popconfirm
                          placement="topLeft"
                          title={
                            <div>
                              <h1 className="text-sm">
                                Xác nhận{" "}
                                {record.isBlocked ? "mở khóa " : "khóa "}
                                thành phần này?
                              </h1>
                            </div>
                          }
                          onConfirm={() => blockComponent(record)}
                          okText="Đồng ý"
                          cancelText="Không"
                        >
                          <Button
                            icon={
                              record.isBlocked ? (
                                <UnlockOutlined />
                              ) : (
                                <LockOutlined />
                              )
                            }
                            // type="ghost"
                            className={`w-full !text-white ${
                              record.isBlocked
                                ? "!bg-green-500"
                                : "!bg-amber-500"
                            } !font-medium`}
                          >
                            {record.isBlocked
                              ? "Mở khóa thành phần"
                              : "Khóa thành phần"}
                          </Button>
                        </Popconfirm>
                      ),
                      key: "blockStaff",
                    },
                  ]}
                  trigger={["click"]}
                >
                  <a onClick={(e) => e.preventDefault()}>
                    <Space className="bg-black">
                      Thao tác
                      <DownOutlined />
                    </Space>
                  </a>
                </DropdownCell>
              )}
            />
          </Table>

          <Pagination
            defaultPageSize={query.limit}
            currentPage={query.page}
            total={total}
            onChange={({ limit, page }) => {
              query.page = page;
              query.limit = limit;
              setQuery({ ...query });
            }}
          />
        </Spin>
      )}

      {useMemo(
        () => (
          <ImportSettingComponent
            guide={[
              "Vui lòng tải và sử dụng file mẫu để import dữ liệu đúng cách",
              "Không được thay đổi tiêu đề trong file csv/excel mẫu để tránh import thiếu dữ liệu",
              "Chi tiết xem tại sheet Hướng dẫn của file import mẫu",
            ]}
            onSuccess={() => {
              fetchData();
              // message.success("Nhập dữ liệu thành công.");
            }}
            ref={importModal}
            createApi={componentApi.create}
            onUploaded={(excelData, setData) => {
              console.log("up gì lên vậy", excelData);
              handleOnUploadedFile(excelData, setData);
            }}
            okText={`Nhập cấu hình thành phần ngay`}
            demoExcel="/exportFile/file_mau_nhap_thanh_phan.xlsx"
          />
        ),
        []
      )}
      <CreateComponentModal
        onSubmitOk={fetchData}
        onClose={() => {}}
        ref={modalRef}
      />
    </Card>
  );
};

export default ComponentConfiguration;
