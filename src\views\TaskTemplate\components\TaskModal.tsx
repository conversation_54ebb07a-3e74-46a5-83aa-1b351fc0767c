import { Col, Form, Input, message, Modal, Row } from "antd";
import { Rule } from "antd/lib/form";
import React, { useImperativeHandle, useState } from "react";
import { ModalStatus } from "types/modal";
import { Todo } from "types/todo";

const rules: Rule[] = [{ required: true }];

export interface TaskModal {
  handleCreate: () => void;
  handleUpdate: (todo: Todo) => void;
}
interface TaskModalProps {
  onSubmitOk: (todo: Todo) => void;
}

export const TaskModal = React.forwardRef(
  ({ onSubmitOk }: TaskModalProps, ref) => {
    const [formModal] = Form.useForm<Todo>();

    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");

    useImperativeHandle<any, TaskModal>(
      ref,
      () => ({
        handleCreate() {
          formModal.resetFields();
          setVisible(true);
          setStatus("create");
        },
        handleUpdate(todo: Todo) {
          formModal.setFieldsValue(todo);
          setVisible(true);
          setStatus("update");
        },
      }),
      []
    );

    const createData = async () => {
      const valid = await formModal.validateFields();
      const data = { ...formModal.getFieldsValue() };

      message.success("Tạo thành công");
      handleClose();
      onSubmitOk(data);
    };

    const updateData = async () => {
      const valid = await formModal.validateFields();
      const data = { ...formModal.getFieldsValue() };
      message.success("Cập nhật thành công");
      handleClose();
      onSubmitOk(data);
    };

    const handleClose = () => {
      setVisible(false);
      formModal.resetFields();
      setStatus("create");
    };

    return (
      <Modal
        className="footer-full"
        onCancel={() => {
          handleClose();
        }}
        open={visible}
        title={status == "create" ? `Tạo việc cần làm` : `Sửa việc cần lam`}
        style={{ top: 20 }}
        width={500}
        onOk={() => {
          status == "create" ? createData() : updateData();
        }}
        cancelButtonProps={{ style: { display: "none" } }}
      >
        <Form layout="vertical" form={formModal}>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item label="Nội dung" name="content" rules={rules}>
                <Input placeholder="" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    );
  }
);
