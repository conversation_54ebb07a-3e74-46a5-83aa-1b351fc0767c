import { useTheme } from "context/ThemeContext";
import * as React from "react";

const EmptyIcon = ({ fill = "#050505", width = 24, height = 24 }) => {
  const { darkMode } = useTheme();
  if (darkMode) {
    fill = "#ffffff";
  }
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      fill="none"
      viewBox="0 0 24 20"
    >
      <path
        fill="#050505"
        d="M21.5 20h-19A2.503 2.503 0 0 1 0 17.5v-7a.5.5 0 0 1 .044-.205l3.969-8.82A2.506 2.506 0 0 1 6.293 0h11.414c.981 0 1.876.579 2.28 1.475l3.969 8.82A.5.5 0 0 1 24 10.5v7c0 1.379-1.122 2.5-2.5 2.5ZM1 10.607V17.5c0 .827.673 1.5 1.5 1.5h19c.827 0 1.5-.673 1.5-1.5v-6.893l-3.925-8.723A1.505 1.505 0 0 0 17.707 1H6.293c-.589 0-1.126.348-1.368.885L1 10.607Z"
      />
      <path
        fill="#050505"
        d="M16.807 15H7.193a1.505 1.505 0 0 1-1.404-.973l-1.014-2.703A.5.5 0 0 0 4.307 11H.75a.5.5 0 0 1 0-1h3.557c.622 0 1.186.391 1.405.973l1.013 2.703a.502.502 0 0 0 .468.324h9.613a.5.5 0 0 0 .468-.324l1.013-2.703A1.51 1.51 0 0 1 19.693 10H23.5a.5.5 0 0 1 0 1h-3.807a.5.5 0 0 0-.468.324l-1.013 2.703a1.509 1.509 0 0 1-1.405.973Z"
      />
    </svg>
  );
};
export default EmptyIcon;
