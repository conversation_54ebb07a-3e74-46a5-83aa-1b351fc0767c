import { wardApi } from "api/ward.api";
import { useState } from "react";
import { Ward } from "types/address";
import { QueryParam } from "types/query";

export interface WardQuery extends QueryParam {}

interface UseWardProps {
  initQuery: WardQuery;
}

export const useWard = ({ initQuery }: UseWardProps) => {
  const [data, setData] = useState<Ward[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<WardQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await wardApi.findAll(query);

      setData(data.wards);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return { wards: data, total, fetchData, loading, setQuery, query };
};
