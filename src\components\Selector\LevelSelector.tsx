import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
} from "react";
import { debounce, uniqBy } from "lodash";
import { useLevel } from "hooks/useLevel";
import { Level } from "types/level";
import { QueryParams2 } from "types/query";
import CustomSelect from "components/Input/CustomSelect";

type CustomFormItemProps = {
  value?: number;
  initQuery?: QueryParams2;
  disabled?: boolean;
  selectedLevel?: Level[];
  multiple?: boolean;
  onChange?: (value: any) => void;
  initOptionItem?: Level | Level[];
  valueIsOption?: boolean;
  allowClear?: boolean;
  placeholder?: string;
};

export interface LevelSelector {
  refresh(): void;
}

export const LevelSelector = forwardRef(
  (
    {
      value,
      onChange,
      initQuery,
      disabled,
      multiple = false,
      selectedLevel,
      initOptionItem,
      valueIsOption,
      allowClear = true,
      placeholder = "Chọn cấp bậc",
    }: CustomFormItemProps,
    ref
  ) => {
    const { levels, loading, fetchData, query } = useLevel({
      initQuery: { page: 1, limit: 50, ...initQuery },
    });

    useImperativeHandle<any, LevelSelector>(
      ref,
      () => ({
        refresh() {
          fetchData();
        },
      }),
      []
    );

    useEffect(() => {
      fetchData();
    }, [selectedLevel]);

    const debounceSearch = useCallback(
      debounce((keyword) => {
        query.search = keyword;
        fetchData();
      }, 300),
      [query]
    );

    const options = useMemo(() => {
      let data = [...levels];
      if (initOptionItem) {
        if ((initOptionItem as Level[])?.length) {
          data = data.concat(initOptionItem);
        } else {
          data.push(initOptionItem as Level);
        }
      }
      return uniqBy(data, (item) => item.id).map((item) => ({
        label: item.name,
        value: item.id,
        item, // lưu nguyên object nếu cần dùng sau
      }));
    }, [levels, initOptionItem]);

    const handleChange = (v: any, option: any) => {
      if (valueIsOption) {
        if (option instanceof Array) {
          onChange?.(option.map((opt) => opt.item));
        } else {
          onChange?.(option.item);
        }
      } else {
        onChange?.(v);
      }
    };

    return (
      <CustomSelect
        value={value}
        onChange={handleChange}
        disabled={disabled}
        options={options}
        mode={multiple ? "multiple" : undefined}
        allowClear={allowClear}
        placeholder={placeholder}
        onSearch={debounceSearch}
        loading={loading}
      />
    );
  }
);
