import { ApprovalList } from "./approvalList";
import { Dictionary } from "./dictionary";
import { FileAttach } from "./fileAttach";
import { MemberShip } from "./memberShip";
import { Project } from "./project";
import { ProjectItem } from "./projectItem";
import { Staff } from "./staff";
import { Unit } from "./unit";

export enum BOQType {
  MATERIAL = "MATERIAL",
  LABOR = "LABOR",
  OTHER = "OTHER",
}

export enum BOQDetailType {
  GROUP = "GROUP",
  ITEM = "ITEM",
}

export interface BOQDetail {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  type: BOQDetailType;
  name: string;
  lengthMm: number; // Dài (mm) (*)
  widthMm: number; // Rộng (mm)
  heightMm: number; // Cao (mm)
  diameterMm?: number; // Đường kính
  mass: number; // Khối lượng
  quantity: number; // số lượng
  materialCost: number; // tiền nguyên vật liệu
  labor: number; // tiền công nhân
  price: number; // tiền đơn giá labor + materialCost
  amount: number;
  amountConvert: number; // quy đổi
  priceConvert: number; // tiền đơn giá labor + materialCost
  quantityConvert: number; // quy đổi
  noteConvert: string;
  floors: number;
  technical: string;
  note: string;
  realAmount: number;
  version: number;
  boq?: BOQ | null;
  unit: Unit | null;
  unitConvert: Unit | null; // quy đổi
  project: Project;
  projectItem: ProjectItem;
  workType: Dictionary;
  parent: BOQDetail | null;
  children: BOQDetail[];

  total?: number;
  unitPrice?: number;
  level: number;
  parentId?: number;
  isNew?: boolean;
  floor?: number;
}

export type BOQDetailProcessed = {
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  type: BOQDetailType;
  name: string;
  lengthMm: number;
  widthMm: number;
  heightMm: number;
  diameterMm: number;
  mass: number;
  quantity: number;
  materialCost: number;
  labor: number;
  total: number;
  amount: number;
  unitId?: number;
  id?: number; // optional
  children: BOQDetailProcessed[]; // recursive type
  floor?: number;
  workTypeId?: number;
  note?: string;
};

// export interface BOQ {
//   id: number;
//   createdAt: number;
//   updatedAt: number;
//   deletedAt: number;
//   isDeleted: boolean;
//   code: string; // Mã BOQ
//   description: string;
//   quantity: number;
//   materialCost: number; // tiền nguyên vật liệu
//   type: BOQType; // Loại BOQ
//   labor: number; // tiền công nhân
//   total: number; // Rate: tỉ giá
//   amount: number; // Thành tiền
//   remarks: string;
//   contractorSpec?: string; // Thông số do nhà thầu đề xuất
//   specifications?: string; // Đặc điểm kỹ thuật
//   convertQuantity: number;
//   unit: Unit;
//   project: Project;
//   boqCategory: Dictionary;
//   boqGroup: Dictionary;
//   convertUnit: Unit;
//   // info create update
//   createdBy: Staff;
//   updatedBy: Staff;
//   fileAttaches?: FileAttach[];
//   approvalLists?: ApprovalList[];
//   followStaffs?: Staff[];
// }

export interface BOQ {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  code: string; // Mã BOQ
  name: string; // Tên BOQ (thay thế title)
  description: string;
  floor: string;
  quantity: number;
  amount: number;
  remarks: string;
  isActive: boolean;
  note: string;
  boqGroup?: Dictionary | null;
  boqDetails: BOQDetail[];
  projectItem?: {
    id: number;
    createdAt: number;
    updatedAt: number;
    deletedAt: number;
    isDeleted: boolean;
    code: string;
    name: string;
    area: number;
    isActive: boolean;
    note: string;
    floors: number;
  };
  // Legacy fields for backward compatibility
  materialCost?: number;
  type?: BOQType;
  labor?: number;
  total?: number;
  contractorSpec?: string;
  specifications?: string;
  convertQuantity?: number;
  unit?: Unit;
  project?: Project;
  boqCategory?: Dictionary;
  convertUnit?: Unit;
  createdBy: Staff;
  updatedBy?: Staff;
  fileAttaches?: FileAttach[];
  approvalLists: ApprovalList[];
  followMemberShips: MemberShip[];

  followStaffs?: Staff[];
  title?: string; // Legacy field
  taskCode?: string;
  length?: number;
  width?: number;
  height?: number;
  diameter?: number;
  volume?: number;
  materialRate?: number;
  laborRate?: number;
  totalRate?: number;
  unitPrice?: number;
  actualQuantity?: number;
  status?: BOQStatus;
  version: number;
}

export enum BOQStatus {
  DRAFT = "DRAFT",
  PENDING = "PENDING",
  APPROVED = "APPROVED",
  REJECTED = "REJECTED",
  COMPLETED = "COMPLETED",
}

export const BOQStatusTrans: Record<
  BOQStatus,
  { label: string; color: string }
> = {
  [BOQStatus.DRAFT]: { label: "Nháp", color: "default" },
  [BOQStatus.PENDING]: { label: "Chờ duyệt", color: "processing" },
  [BOQStatus.APPROVED]: { label: "Đã duyệt", color: "success" },
  [BOQStatus.REJECTED]: { label: "Từ chối", color: "error" },
  [BOQStatus.COMPLETED]: { label: "Hoàn thành", color: "blue" },
};
