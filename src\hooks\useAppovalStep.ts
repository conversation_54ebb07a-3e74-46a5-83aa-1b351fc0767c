import { approvalTemplateApi } from "api/approvalTemplate.api";
import { StepItem } from "components/ApproveProcess/ApprovalStepsCard";
import { useState } from "react";
import { ApprovalListStatus } from "types/approvalList";
import {
  ApprovalTemplate,
  ApprovalTemplateName,
  ApprovalTemplateType,
} from "types/approvalTemplate";
import { MemberShip } from "types/memberShip";
import { Staff } from "types/staff";

export const useApprovalStep = () => {
  const [approvalSteps, setApprovalSteps] = useState<StepItem[]>([]); // danh sach quy trinh duyet
  const [followers, setFollowers] = useState<MemberShip[]>([]); // Danh sách người theo dõi thực tế

  const fetchApprovalTemplate = async ({
    projectId,
    type,
    createdStaff,
    notRequired,
  }: {
    projectId: number;
    type: ApprovalTemplateType;
    createdStaff: Staff;
    notRequired?: boolean;
  }) => {
    try {
      const res = await approvalTemplateApi.findAll({
        type,
        projectId,
        isActive: true,
        limit: 1,
      });

      const approvalTemplates = res.data
        ?.approvalTemplates as ApprovalTemplate[];

      if (approvalTemplates.length) {
        const template = approvalTemplates[0] as ApprovalTemplate;
        // debugger;
        const approvalSteps = template.approvalTemplateDetails.map(
          (item, idx) => {
            if (item.name == ApprovalTemplateName.Create) {
              item.staff = createdStaff;
              //   item.memberShip = createdStaff.memberShip;
            }

            return {
              ...item,
              id: undefined,
              status: ApprovalListStatus.Pending,
              approvers: item.memberShipApprovals.map((it) => ({
                id: it.id,
                role: it.role,
                memberShip: it.memberShip,
                roleId: it.role.id,
                memberShipId: it.memberShip.id,
              })),

              //   staffId: item.staff?.id,
              //   staff2Id: item.staff2?.id,
              //memberShip
              //memberShip2
              //   memberShipId: item.memberShip!?.id,
              //   memberShip2Id: item.memberShip2?.id,
            };
          }
        );

        setApprovalSteps(approvalSteps.sort((a, b) => a.position - b.position));
        setFollowers(template.followMemberShips || []);
      } else {
        setApprovalSteps([
          {
            id: undefined,
            status: ApprovalListStatus.Pending,
            name: ApprovalTemplateName.Create,
            position: 0,
            staffId: createdStaff.id,
            staff: createdStaff,
            approvers: [],
            // memberShipId: createdStaff.memberShip?.id,
            // memberShip: createdStaff.memberShip,
            note: "",
            type,
          },
          // ...(notRequired
          //   ? []
          //   : [
          //       {
          //         id: undefined,
          //         status: ApprovalListStatus.Pending,
          //         name: "",
          //         position: 1,
          //         staffId: undefined,
          //         staff: undefined,
          //         approvers: [{}],
          //         // memberShipId: undefined,
          //         // memberShip: undefined,
          //         note: "",
          //         type,
          //       },
          //     ]),
        ]);
        setFollowers([]);
      }
    } catch (error) {
      setApprovalSteps([]);
      setFollowers([]);
    }
  };

  return {
    fetchApprovalTemplate,
    setFollowers,
    followers,
    approvalSteps,
    setApprovalSteps,
  };
};
