.tox-notifications-container {
  display: none !important;
}

.text-18 {
  font-size: 18px !important;
}

img {
  vertical-align: middle;
}

.upload-avatar {
  width: 178px;
  // height: calc(100% - 16px);
  height: 178px;
  margin-top: 6px;
}

.text-header {
  font-size: 24px;
  font-weight: 700;
  line-height: 32px;
}

.upload-list-custom {
  .ant-upload-list {
    float: left !important;
  }
}

img.full {
  width: 100%;
  height: 100%;
}

.w-100 {
  width: 100%;
}

.text-blue {
  color: #00a751;
}

.text-error {
  color: red;
}

.text-success {
  color: green;
}

label {
  font-weight: 500;
}

.text-center {
  text-align: center;
}

.filter-container {
  margin-bottom: 12px;

  .filter-item {
    label {
      font-weight: 500;
      font-size: 15px;
    }

    &.btn {
      button {
        position: relative;
        top: 11px;
      }
    }
  }
}

.app-container {
  border-radius: 5px;

  .ant-card-bordered {
    border: 1px solid var(--color-neutral-n2);
    border-radius: 5px;
  }
}

.upload-video {
  .video-item {
    width: 104px;
    height: 104px;

    padding: 8px;
    border: 1px solid #d9d9d9;
    border-radius: 2px;
    cursor: pointer;
    position: relative;

    video {
      object-fit: cover;
      width: 100%;
      height: 100%;
    }

    &:hover {
      .pseudo {
        opacity: 1;
      }
    }

    .pseudo {
      transition: opacity 0.3s;
      position: absolute;
      inset: 0;
      padding: 8px;
      opacity: 0;

      .actions {
        height: 100%;
        background-color: rgba($color: #000000, $alpha: 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;

        span {
          color: white;
          font-size: 16px;
        }
      }
    }
  }
}

.text-one-line {
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  max-width: 200px;
  white-space: pre-wrap;
  overflow: hidden;
  display: -webkit-box;
}

.wheel-piece-img {
  width: 100px !important;
  height: 100px !important;
  object-fit: cover;
  object-position: center -150px;
}

.input-no-style {
  border: unset !important;
  pointer-events: none;
}

.cursor-pointer {
  cursor: pointer;
}

.text-underline-hover:hover {
  text-decoration: underline;
}

//Winwheel game

#woay-wheel {
  border-radius: 50%;
}

.winwheel {
  &-bg {
    position: fixed;
    inset: 0;
    width: 100vw;
    height: 100vh;
    object-fit: cover;
    z-index: -1;

    img {
      height: 100%;
      width: 100%;
    }
  }

  .page-title {
    img {
      width: 60%;
      max-width: 400px;
    }
  }
}

.game-actions {
  text-align: center;
  margin-top: 50px;
}

.woay-box-wheel {
  margin: auto;
  margin-top: 30px;
  margin-bottom: 30px;
  max-width: 720px;
}

.woay-box-wheel .woay-bg {
  position: relative;
  border-radius: 50%;
  padding-bottom: 100%;
}

.woay-box-wheel #woay-wheel {
  position: absolute;
  width: 92%;
  left: 4%;
  top: 4%;
}

.woay-box-wheel .woay-border {
  position: absolute;
  width: 100%;
  left: 0;
  top: 0;
}

.woay-box-wheel .btn-woay {
  position: absolute;
  width: 20%;
  padding-bottom: 20%;
  left: 40%;
  top: 40%;
  cursor: pointer;
}

.woay-box-wheel .btn-woay .woay-button-bg {
  position: absolute;
  width: 100%;
  left: 0;
  top: 0;
}

.woay-box-wheel .btn-woay .woay-button-text {
  width: 70%;
  position: absolute;
  left: 15%;
  top: 15%;
  height: 70%;
  background-image: url("");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: contain;
}

.woay-box-wheel .woay-arrow {
  position: absolute;
  width: 12%;
  left: 44%;
  top: 0;
}

.product-category-table {
  .ant-table-column-sorter {
    display: none;
  }
}

.form-height-full {
  .ant-form-item-row,
  .ant-form-item-control-input,
  .ant-form-item-control-input-content,
  .ant-form-item-has-success {
    height: 100% !important;
  }
}

.upload-file-staff {
  .ant-row:first-child {
    display: block;
    max-height: 100%;
    overflow: auto;
    //  height: 100%;
  }
}

@mixin form-grid($columns) {
  display: grid;
  grid-template-columns: repeat($columns, 1fr);
  gap: 16px;
  align-items: start;
}

.form-grid-5 {
  @include form-grid(5);
}
.form-grid-4 {
  @include form-grid(4);
}

.form-grid-7 {
  @include form-grid(7);
}

.page-enter {
  opacity: 0;
  transform: translateX(20px);
}

.page-enter-active {
  opacity: 1;
  transform: translateX(0);
  transition: all 300ms ease;
}

.page-exit {
  opacity: 1;
  transform: translateX(0);
}

.page-exit-active {
  opacity: 0;
  transform: translateX(-20px);
  transition: all 300ms ease;
}
.status-tag {
  border-radius: 5px !important;
  padding: 0 12px !important;
}
.status-tag.ant-tag-green {
  border-color: #52c41a !important;
}
.status-tag.ant-tag-red {
  border-color: #ff4d4f !important;
}
.small-scrollbar-horizontal {
  /* width */
  &::-webkit-scrollbar {
    height: 4px;
  }
}
.custom-scrollbar {
  /* width */
  &::-webkit-scrollbar {
    width: 4px;
  }

  /* Track */
  &::-webkit-scrollbar-track {
    background: black;
    border-radius: 5px;
  }

  /* Handle */
  &::-webkit-scrollbar-thumb {
    background: var(--color-primary);
  }

  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: #555555;
  }
}

.card-box {
  @apply shadow-md;
  padding: 16px;
  background-color: var(--color-neutral-n0);
  font-weight: 300;
  border-radius: 5px;
}

* {
  &:focus-visible {
    outline: none !important;
  }
}
