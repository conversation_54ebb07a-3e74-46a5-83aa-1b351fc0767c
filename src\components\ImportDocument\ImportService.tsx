import { DownloadOutlined, InboxOutlined } from "@ant-design/icons";
import { Alert, Modal, Space, Spin, Table, Upload, message } from "antd";
import { Rule } from "antd/es/form";
import { serviceApi } from "api/service.api";
import { forwardRef, useEffect, useImperativeHandle, useState } from "react";
import { Link } from "react-router-dom";
import { Service } from "types/service";
import { readerData } from "utils/excel2";
import CustomButton from "components/Button/CustomButton";
import ImportPreviewModule from "./ImportPreviewModule";
import { handleConfirmImportExcel } from "utils/function";

const rules: Rule[] = [{ required: true }];
const { Dragger } = Upload;

export interface ImportServiceModal {
  open: () => void;
  close: () => void;
}

export interface IValidate {
  index: number;
  message: string;
}

interface DataImportReturn {
  msg: string;
  rowNum: number;
  status: string;
}

export interface ServiceImport extends Service {
  rowNum: number;
  serviceTypeName?: string; // From Excel
  unitName?: string; // From Excel
  providerName?: string; // From Excel
}

interface IProps {
  onSuccess?: () => void;
  createApi?: (data: any) => any;
  onUploaded?: (excelData: any, setData: (data: any) => any) => void;
  demoExcel?: string;
  guide?: React.ReactNode[];
  uploadText?: string;
  okText?: string;
  onClose?: () => void;
  titleText?: string;
  validateMessage?: IValidate[];
  onDownloadDemoExcel?: () => void;
  loadingDownloadDemo?: boolean;
}

const ImportService = forwardRef(
  (
    {
      onSuccess,
      createApi,
      onUploaded,
      onClose,
      validateMessage,
      guide,
      demoExcel,
      uploadText = "Kéo thả hoặc click vào đây để upload file",
      okText = "Nhập dữ liệu ngay",
      titleText = "Nhập excel dữ liệu",
      onDownloadDemoExcel,
      loadingDownloadDemo,
    }: IProps,
    ref
  ) => {
    const [errorsLog, setErrorsLog] = useState<any[]>([]);
    const [dataPosts, setDataPosts] = useState<ServiceImport[]>([]);
    const [visible, setVisible] = useState<boolean>(false);
    const [loading, setLoading] = useState(false);
    const [hasValidationErrors, setHasValidationErrors] = useState(false);
    const [dataReturn, setDataReturn] = useState<{
      data: DataImportReturn[];
      successCount: number;
      errorCount: number;
    }>();

    useEffect(() => {
      if (validateMessage?.length) {
        setErrorsLog([]);
      }
    }, [validateMessage]);

    // Callback to receive validation status from ImportPreviewModule
    const handleValidationStatusChange = (hasErrors: boolean) => {
      console.log("🚨 Service validation status changed:", hasErrors);
      setHasValidationErrors(hasErrors);
    };

    const handleOnImport = async () => {
      if (!dataPosts.length) return;
      let errors: any = [];

      try {
        setLoading(true);
        await handleConfirmImportExcel();
        const { data } = await serviceApi.import({
          services: dataPosts.map((dataPost) => ({
            ...dataPost,
          })),
        });
        if (data.length) {
          const successCount = data.reduce(
            (acc: number, item: DataImportReturn) => {
              if (item.status == "ok") return acc + 1;
              return acc;
            },
            0
          );
          const errorCount = data.reduce(
            (acc: number, item: DataImportReturn) => {
              if (item.status == "error") return acc + 1;
              return acc;
            },
            0
          );
          if (errorCount == 0) {
            handleOnCancel();
          }
          setDataReturn({ data, successCount, errorCount });
          onSuccess?.();
          setDataPosts([]);
        }
      } catch (err) {
        console.log({ err });
      } finally {
        setLoading(false);
      }
    };

    const handleOnCancel = () => {
      setVisible(false);
      onClose?.();
    };

    useImperativeHandle(
      ref,
      () => ({
        open: () => setVisible(true),
        close: () => setVisible(false),
      }),
      []
    );

    // Define preview columns for the table
    const previewColumns = [
      {
        key: "rowNum",
        title: "Dòng excel",
        dataIndex: "rowNum",
        width: 100,
        render: (text: number) => <span>{text}</span>,
      },
      {
        key: "code",
        title: "Mã dịch vụ",
        dataIndex: "code",
        render: (text: string) => (
          <span className={!text ? "text-gray-400" : ""}>
            {text || "Tự sinh"}
          </span>
        ),
      },
      {
        key: "name",
        title: "Tên dịch vụ *",
        dataIndex: "name",
        render: (text: string, record: any) => (
          <span className={!text ? "text-red-500" : ""}>{text || "Thiếu"}</span>
        ),
      },
      {
        key: "serviceTypeName",
        title: "Loại dịch vụ *",
        dataIndex: "serviceTypeName",
        render: (text: string, record: any) => (
          <span className={!text ? "text-red-500" : ""}>{text || "Thiếu"}</span>
        ),
      },
      {
        key: "unitName",
        title: "Đơn vị tính *",
        dataIndex: "unitName",
        render: (text: string, record: any) => (
          <span className={!text ? "text-red-500" : ""}>{text || "Thiếu"}</span>
        ),
      },
      {
        key: "providerName",
        title: "Nhà cung cấp",
        dataIndex: "providerName",
        render: (text: string) => (
          <span className={!text ? "text-gray-400" : ""}>
            {text || "Không có"}
          </span>
        ),
      },
      {
        key: "estPrice",
        title: "Chi phí ước tính",
        dataIndex: "estPrice",
        render: (text: number) => (
          <span>{text ? `${text.toLocaleString()} VNĐ` : "Không có"}</span>
        ),
      },
      {
        key: "workingDays",
        title: "Thời gian thực hiện",
        dataIndex: "workingDays",
        render: (text: number) => (
          <span>{text ? `${text} ngày` : "Không có"}</span>
        ),
      },
      {
        key: "description",
        title: "Mô tả",
        dataIndex: "description",
        render: (text: string) => (
          <span className={!text ? "text-gray-400" : ""}>
            {text || "Không có"}
          </span>
        ),
      },
      {
        key: "errorMessage",
        title: "Lỗi",
        dataIndex: "errorMessage",
        width: 300,
        render: (text: string) => (
          <span className="text-red-500 whitespace-pre-line text-xs">
            {text}
          </span>
        ),
      },
    ];

    // Define required fields for validation - based on CreateOrUpdateServicePage
    const requiredFields: (keyof ServiceImport)[] = [
      "name", // Tên dịch vụ
      "serviceTypeName", // Loại dịch vụ (from Excel)
      "unitName", // Đơn vị tính (from Excel)
    ];

    const handleValidateData = (data: ServiceImport[]): ServiceImport[] => {
      console.log("🔍 handleValidateData called with:", data);
      return data.map((item) => {
        const additionalErrors: string[] = [];

        // Price validation (only if estPrice exists)
        if (item.estPrice !== undefined && item.estPrice !== null) {
          if (isNaN(Number(item.estPrice)) || Number(item.estPrice) < 0) {
            additionalErrors.push("Chi phí ước tính phải là số không âm");
          }
        }

        // Working days validation (only if workingDays exists)
        if (item.workingDays !== undefined && item.workingDays !== null) {
          if (isNaN(Number(item.workingDays)) || Number(item.workingDays) < 0) {
            additionalErrors.push("Thời gian thực hiện phải là số không âm");
          }
        }

        console.log(
          "🔍 Additional validation for item:",
          item,
          "additional errors:",
          additionalErrors
        );

        // Return item with additional errors (don't overwrite existing errorMessage)
        return {
          ...item,
          errorMessage:
            additionalErrors.length > 0
              ? additionalErrors.join("; ")
              : undefined,
        };
      });
    };

    return (
      <Modal
        maskClosable={false}
        width={1200} // Increase width to accommodate more columns
        style={{ top: 50 }}
        visible={visible}
        onCancel={handleOnCancel}
        destroyOnClose={true}
        afterClose={() => {
          setDataPosts([]);
          setErrorsLog([]);
          setDataReturn(undefined);
        }}
        title={titleText}
        footer={[
          <CustomButton
            key="import"
            loading={loading}
            variant="primary"
            disabled={!dataPosts.length || hasValidationErrors}
            onClick={() => {
              handleOnImport();
            }}
          >
            {okText}
          </CustomButton>,
          <CustomButton
            key="close"
            variant="outline"
            className="cta-button"
            onClick={() => {
              handleOnCancel();
            }}
          >
            Đóng
          </CustomButton>,
        ]}
      >
        <Spin spinning={false}>
          {guide && (
            <Alert
              style={{ padding: "10px", marginBottom: "10px" }}
              message={<b>Lưu ý</b>}
              type="warning"
              description={
                <ul>
                  {guide.map((text, index) => (
                    <li key={index}>
                      <p>{text}</p>
                    </li>
                  ))}
                </ul>
              }
            />
          )}
          {demoExcel && (
            <Link to={demoExcel} target="_blank" download>
              <Space className={`flex gap-2 cursor-pointer`}>
                <DownloadOutlined />
                Tải file import mẫu{" "}
              </Space>
            </Link>
          )}
          {onDownloadDemoExcel && (
            <a>
              <Space
                className={`flex gap-2 cursor-pointer`}
                onClick={() => {
                  onDownloadDemoExcel();
                }}
                style={{ pointerEvents: loadingDownloadDemo ? "none" : "auto" }}
              >
                {loadingDownloadDemo ? (
                  <Spin spinning={loadingDownloadDemo} />
                ) : (
                  <DownloadOutlined />
                )}
                Tải file import mẫu
              </Space>
            </a>
          )}

          <Dragger
            style={{ marginTop: "0.5em" }}
            maxCount={1}
            multiple={false}
            beforeUpload={async (file) => {
              //Check file type
              const isCSVFile = file.name.includes("xlsx");
              if (isCSVFile === false) {
                message.error("Bạn chỉ có thể upload file excel!");
                return Upload.LIST_IGNORE;
              }
              const excelData = await readerData(file, 0);
              setDataReturn(undefined);
              console.log("Data khi import vào là", excelData);
              onUploaded?.(excelData, setDataPosts);
              return false;
            }}
            onChange={(info) => {
              //reset data
              if (info.fileList.length == 0) {
                setErrorsLog([]);
                setDataPosts([]);
              }
            }}
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">{uploadText}</p>
          </Dragger>

          {/* Import Preview Module */}
          <ImportPreviewModule
            data={dataPosts}
            dataReturn={dataReturn}
            onValidateData={handleValidateData}
            requiredFields={requiredFields}
            duplicateCheckFields={["code", "name"]} // Kiểm tra trùng lặp mã dịch vụ và tên dịch vụ
            columns={previewColumns}
            title="Xem danh sách dịch vụ"
            previewButtonText="Xem danh sách dịch vụ"
            onValidationStatusChange={handleValidationStatusChange}
          />
        </Spin>
        <Space
          style={{ width: "100%", justifyContent: "end", marginTop: "1em" }}
        ></Space>
      </Modal>
    );
  }
);

export default ImportService;
