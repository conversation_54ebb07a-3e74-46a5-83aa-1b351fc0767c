import {
  <PERSON><PERSON>,
  <PERSON>,
  Col,
  Divider,
  Form,
  Input,
  message,
  Modal,
  Row,
  Select,
} from "antd";
import TextArea from "antd/es/input/TextArea";
import { Rule } from "antd/lib/form";
import { providerApi } from "api/provider.api";
import { projectGroupApi } from "api/projectGroup.api";
import { ProjectCategorySelector } from "components/Selector/ProjectCategorySelector";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { FileAttach } from "types/fileAttach";
import { ModalStatus } from "types/modal";
import { Provider, ProviderTypeTrans } from "types/provider";
import { ProjectGroup } from "types/projectGroup";
import { getTitle } from "utils";

const rules: Rule[] = [{ required: true }];

export const EditProjectGroupPage = ({ title = "" }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const [selectedProjectGroup, setSelectedProjectGroup] =
    useState<ProjectGroup>();
  const [searchParams, setSearchParams] = useSearchParams();
  const getOneProjectGroup = async (id: number) => {
    try {
      setLoading(true);
      const { data } = await projectGroupApi.findOne(id);
      setSelectedProjectGroup(data);
      form.setFieldsValue({
        ...data,
        projectCategoryId: data.projectCategory?.id || undefined,
      });
    } catch (e) {
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    document.title = getTitle(title);
    const projectGroupId = searchParams.get("projectGroupId");
    if (projectGroupId) {
      getOneProjectGroup(+projectGroupId);
      // searchParams.delete("ProjectGroupId");
      // setSearchParams(searchParams);
    }
  }, []);

  const updateData = async () => {
    const valid = await form.validateFields();
    const { projectCategoryId, ...data } = form.getFieldsValue();
    const payload = {
      projectGroup: {
        ...data,
      },
      projectCategoryId,
    };
    try {
      setLoading(true);

      const res = await projectGroupApi.update(
        selectedProjectGroup?.id || 0,
        payload
      );
      message.success("Cập nhật nhóm dự án thành công!");
      navigate("/project-group/project-group-list");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <div className="font-bold text-2xl mb-[20px]">Chỉnh sửa nhóm dự án</div>
      <Card>
        <Form layout="vertical" form={form}>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label="Mã nhóm dự án" name="code">
                <Input placeholder="Nếu không điền hệ thống sẽ tự sinh mã" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="Tên nhóm dự án" name="name" rules={rules}>
                <Input placeholder="" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="Loại nhóm dự án"
                name="projectCategoryId"
                rules={rules}
              >
                <ProjectCategorySelector />
              </Form.Item>
            </Col>
          </Row>
        </Form>
        <div className="flex gap-[16px] justify-end mt-2">
          <Button
            size="large"
            className="w-[120px]"
            onClick={() => {
              navigate("/project-group/project-group-list");
            }}
          >
            Hủy
          </Button>
          <Button
            loading={loading}
            type="primary"
            size="large"
            className="w-[180px]"
            onClick={() => {
              updateData();
            }}
          >
            Chỉnh sửa nhóm dự án
          </Button>
        </div>
      </Card>
    </div>
  );
};
