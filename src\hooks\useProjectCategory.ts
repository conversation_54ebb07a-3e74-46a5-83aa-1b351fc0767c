import { projectCategoryApi } from "api/projectCategory.api";
import { useState } from "react";
import { ProjectCategory } from "types/projectCategory";
import { QueryParam } from "types/query";

export interface ProjectCategoryQuery extends QueryParam {}

interface UseProjectCategoryProps {
  initQuery: ProjectCategoryQuery;
}

export const useProjectCategory = ({ initQuery }: UseProjectCategoryProps) => {
  const [data, setData] = useState<ProjectCategory[]>([]);
  const [total, setTotal] = useState(0);
  const [query, setQuery] = useState<ProjectCategoryQuery>(initQuery);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const { data } = await projectCategoryApi.findAll(query);

      setData(data.projectCategories);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return {
    projectCategories: data,
    total,
    fetchData,
    loading,
    setQuery,
    query,
  };
};
