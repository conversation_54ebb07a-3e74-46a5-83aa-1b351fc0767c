// This file combines Create and Edit Indicative Page into a single component
import {
  Card,
  Col,
  Row,
  Form,
  Button,
  Avatar,
  Space,
  Spin,
  Tabs,
  Select,
  message,
  Input,
  Modal,
  Tag,
  DatePicker,
  Tooltip,
} from "antd";
import PageTitle from "components/PageTitle/PageTitle";
import React, { useEffect, useMemo, useState } from "react";
import { Rule } from "antd/lib/form";
import CustomButton from "components/Button/CustomButton";
import { ModalStatus } from "types/modal";
import { PermissionNames } from "types/PermissionNames";
import {
  useNavigate,
  useParams,
  useSearchParams,
  useLocation,
} from "react-router-dom";
import { FileAttach } from "types/fileAttach";
import { isEmpty } from "lodash";
import { getTitle } from "utils";
import { FileUploadMultiple2 } from "components/Upload/FileUploadMultiple2";
import { ProjectSelector } from "components/Selector/ProjectSelector";
import { useWard } from "hooks/useWard";
import { useWatch } from "antd/es/form/Form";
import { DictionarySelector } from "components/Selector/DictionarySelector";
import { Dictionary, DictionaryType } from "types/dictionary";
import { fileAttachApi } from "api/fileAttach.api";
import { $url } from "utils/url";
import dayjs from "dayjs";
import { ProviderSelector } from "components/Selector/ProviderSelector";
import { ProviderModule } from "types/provider";
import { Staff } from "types/staff";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import { WorkStatusTrans } from "types/workStatus";
import { Pagination } from "components/Pagination";
import {
  ApprovalStepsCard,
  StepItem,
} from "components/ApproveProcess/ApprovalStepsCard";
import { FollowerSelector } from "components/Follower/FollowerSelector";
import TextArea from "antd/es/input/TextArea";
import { settings } from "settings";
import { dictionaryApi } from "api/dictionary.api";
import clsx from "clsx";
import { observer } from "mobx-react";
import { checkRoles } from "utils/auth";
import { permissionStore } from "store/permissionStore";
import { memberShipApi } from "api/memberShip.api";
import { DeviceType } from "types/device";
import { CommentView } from "components/Comment/CommentView";
import { StaffSelector } from "components/Selector/StaffSelector";
import { FileUpload } from "components/Upload/FileUpload";
import { SingleImageUpload } from "components/Upload/SingleImageUpload";
import DeleteIcon from "assets/svgs/DeleteIcon";
import { PlusOutlined } from "@ant-design/icons";
import { formatDateTime } from "utils/date";
import { ApprovalListType } from "types/approvalList";
import { approvalListApi } from "api/approvalList.api";
import { ProjectItem } from "types/projectItem";
import { projectItemApi } from "api/projectItem.api";
import { Project } from "types/project";
import { ProjectItemDetail } from "types/projectItemDetail";
import ActiveStatusTagSelect from "components/ActiveStatus/ActiveStatusTagSelect";
import { InputNumber } from "components/Input/InputNumber";
import { appStore } from "store/appStore";
import { BMDTextArea } from "components/TextArea/BMDTextArea";
import { BMDCKEditor } from "components/Editor";

const rules: Rule[] = [{ required: true, message: "Trường này là bắt buộc" }];
const descriptionRules: Rule[] = [{ required: false }];

interface EditProjectItemPageProps {
  title: string;
  status: ModalStatus;
}

interface ProjectItemForm extends ProjectItem {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  code: string;
  name: string;
  area: string;
  isActive: boolean;
  note: string;
  project: Project;
  floors: number;
  projectItemDetails: ProjectItemDetail[];
}

function CreateOrUpdateProjectItemPage({
  title = "",
  status,
}: EditProjectItemPageProps) {
  const { haveEditPermission } = checkRoles(
    {
      edit: PermissionNames.projectItemEdit,
    },
    permissionStore.permissions
  );

  const [form] = Form.useForm<ProjectItemForm>();
  const [loading, setLoading] = useState(false);
  const [selectedProjectItem, setSelectedProjectItem] = useState<ProjectItem>();
  const [fileList, setFileList] = useState<FileAttach[]>([]);
  const navigate = useNavigate();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [followers, setFollowers] = useState<Staff[]>([]);
  const [loadingProjectItems, setLoadingProjectItems] = useState(false);
  const [projectItems, setProjectItems] = useState<any[]>([]);
  const [memberShips, setMemberShips] = useState<any[]>([]);
  const [loadingMemberShips, setLoadingMemberShips] = useState(false);
  const [pdfUrl, setPdfUrl] = useState("");
  const [pdfFileAttach, setPdfFileAttach] = useState<FileAttach | null>(null); // Thêm state này
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [approvalSteps, setApprovalSteps] = useState<StepItem[]>([]); // danh sach quy trinh duyet
  const [loadingApprove, setLoadingApprove] = useState(false);
  const [removeApprovalList, setRemoveApprovalList] = useState<number[]>([]);

  const [readonly, setReadonly] = useState(true);
  const [loadingFetch, setLoadingFetch] = useState(false);
  const params = useParams();
  const [searchParams, setSearchParams] = useSearchParams();
  const type = useWatch("type", form);
  const [senderInfo, setSenderInfo] = useState<any>(null);
  const [receiverInfo, setReceiverInfo] = useState<any>(null);
  const location = useLocation();

  const setDataToForm = async (data: ProjectItem) => {
    form.setFieldsValue({
      code: data.code,
      name: data.name,
      area: data.area,
      isActive: data.isActive,
      note: data.note,
      //@ts-ignore
      project: data.project?.id,
      //@ts-ignore
      floors: data.floors,
    });

    if (data.projectItemDetails && Array.isArray(data.projectItemDetails)) {
      setItems(
        data.projectItemDetails.map((detail) => ({
          id: detail.id,
          projectItemName: data.name || "",
          floor: detail.floor || "",
          drawId: detail.draw?.id ?? 0,
        }))
      );
      setOriginalItems(
        data.projectItemDetails.map((detail) => ({
          id: detail.id,
          projectItemName: data.name || "",
          floor: detail.floor || "",
          drawId: detail.draw?.id ?? 0,
        }))
      );
    }
  };

  const getOneProjectItem = async (id: number) => {
    try {
      setLoadingFetch(true);
      const { data } = await projectItemApi.findOne(id);

      if (isEmpty(data)) {
        navigate("/404");

        return;
      }

      setSelectedProjectItem(data);
      setDataToForm(data);

      //   if (data.serviceType) {
      //     setServiceTypes([data.serviceType]);
      //   }

      return data as ProjectItem;
    } catch (e: any) {
    } finally {
      setLoadingFetch(false);
    }
  };

  useEffect(() => {
    document.title = getTitle(title);

    if (status == "update") {
      const projectItemId = params.id;

      if (projectItemId) {
        getOneProjectItem(+projectItemId);
        setReadonly(!haveEditPermission || searchParams.get("update") !== "1");
      }
    } else {
      setReadonly(false);
    }

    fetchProjectItems();
  }, [haveEditPermission]);

  const fetchProjectItems = async () => {
    try {
      setLoadingProjectItems(true);
      const { data } = await dictionaryApi.findAll({
        isActive: true,
      });
      setProjectItems(data?.dictionaries || []);
    } catch (error) {
      console.error("Error fetching Project items:", error);
      message.error("Không thể tải danh sách phân loại Project");
    } finally {
      setLoadingProjectItems(false);
    }
  };

  const getDataSubmit = async (currentItems: any[]) => {
    const { code, name, area, isActive, note, project, floors } =
      form.getFieldsValue();

    let projectId =
      typeof project === "object" && project?.id ? project.id : project;
    if (!projectId) {
      projectId = appStore.currentProject?.id || 0;
    }
    const floorsNumber =
      typeof floors === "number" ? floors : Number(floors) || 0;

    const projectItemDetails = (currentItems || []).map((item) => ({
      ...(status !== "create" && { id: item.id }),
      projectItemId: status === "create" ? 0 : selectedProjectItem?.id || 0,
      floor: item.floor || "",
      drawId: item?.drawId,
    }));

    return {
      projectId: projectId || 0,
      projectItemDetails,
      projectItem: {
        code: code || "",
        name: name || "",
        area: area || "",
        isActive: selectedProjectItem?.isActive,
        note: note || "",
        floors: floorsNumber,
      },
    };
  };

  const createData = async () => {
    const valid = await form.validateFields();
    setLoading(true);
    try {
      const res = await projectItemApi.create(await getDataSubmit(items));

      console.log("Created project item:", res.data);

      message.success("Tạo hạng mục thành công!");
      navigate(`/report/${PermissionNames.projectItemList}`);
      setFileList([]);
    } finally {
      setLoading(false);
    }
  };

  const updateData = async () => {
    const valid = await form.validateFields();
    setLoading(true);
    try {
      const res = await projectItemApi.update(
        selectedProjectItem!?.id || 0,
        await getDataSubmit(items)
      );
      setSelectedProjectItem({ ...selectedProjectItem, ...res.data });

      message.success("Chỉnh sửa hạng mục thành công!");
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = () => {
    // Kiểm tra trùng tên tầng trước khi lưu
    const floorNames = items.map((i) => i.floor?.trim()).filter(Boolean);
    const hasDuplicate = floorNames.some(
      (name, idx) => floorNames.indexOf(name) !== idx
    );
    if (hasDuplicate) {
      message.error(
        "Có tầng bị trùng tên, vui lòng kiểm tra lại tên các tầng."
      );
      return;
    }

    if (status == "create") {
      createData();
    } else {
      updateData();
    }
  };

  const pageTitle = useMemo(
    () => (status == "create" ? "Tạo hạng mục" : "Chỉnh sửa hạng mục"),
    [status]
  );

  const [items, setItems] = useState<any[]>([]);
  const [originalItems, setOriginalItems] = useState<any[]>([]);
  const [loadingItems, setLoadingItems] = useState(false);

  // Theo dõi giá trị name và floors
  const name = useWatch("name", form);
  const floors = useWatch("floors", form);

  // Columns cho table trong tab Hạng mục
  const itemColumns: CustomizableColumn<any>[] = [
    {
      key: "drawId",
      title: "Preview bản vẽ",
      dataIndex: "drawId",
      width: 270,
      defaultVisible: true,
      alwaysVisible: true,
      align: "left",

      // render: (_, record, index) => {
      //   return <div>Trang {index + 1}</div>;
      // },
    },
    {
      key: "projectItemName",
      title: "Hạng mục",
      dataIndex: "projectItemName",
      width: 140,
      defaultVisible: true,

      alwaysVisible: true,
      render: (_, record) => {
        return <div>{record.projectItemName || ""}</div>;
      },
    },
    {
      key: "floor",
      title: "Tầng",
      dataIndex: "floor",
      width: 200,
      defaultVisible: true,
      alwaysVisible: true,
      render: (value, record, index) => {
        // Kiểm tra trùng tên tầng
        const isDuplicate =
          items.filter(
            (item, idx) =>
              item.floor?.trim() &&
              item.floor.trim() === value?.trim() &&
              idx !== index
          ).length > 0;
        return (
          <div style={{ width: "100%" }}>
            <Input
              value={value}
              disabled={readonly}
              placeholder="Nhập tên tầng"
              onChange={(e) => {
                const newValue = e.target.value;
                setItems((prev) =>
                  prev.map((item, idx) =>
                    idx === index ? { ...item, floor: newValue } : item
                  )
                );
              }}
              size="small"
              status={isDuplicate ? "error" : undefined}
            />
            {isDuplicate && (
              <div style={{ color: "red", fontSize: 12 }}>
                Tên tầng đã bị trùng
              </div>
            )}
          </div>
        );
      },
    },
    {
      key: "actions",
      title: (
        <Tooltip
          title={
            readonly ? "Bạn không thể thêm số tầng mới" : "Thêm số tầng mới"
          }
        >
          <PlusOutlined
            style={{
              fontSize: 18,
              cursor: readonly ? "not-allowed" : "pointer",
            }}
            onClick={() => !readonly && handleCategoryItem()}
          />
        </Tooltip>
      ),
      width: 100,
      align: "center",
      fixed: "right",

      render: (_, record, index) => (
        <Button
          type="text"
          danger
          icon={<DeleteIcon />}
          onClick={(e) => {
            e.stopPropagation();
            Modal.confirm({
              title: `Xóa tầng "${record.floor}"`,
              getContainer: () => {
                return document.getElementById("App") as HTMLElement;
              },
              icon: null,
              content: (
                <>
                  <div>
                    Bạn sẽ không thể khôi phục dữ liệu này sau khi xóa.
                    <br />
                    Bạn có chắc chắn muốn xóa dữ liệu này?
                  </div>
                </>
              ),
              footer: (_, { OkBtn, CancelBtn }) => (
                <>
                  <CustomButton
                    variant="outline"
                    className="cta-button"
                    onClick={() => {
                      setItems((prev) => {
                        return prev.filter((item, idx) =>
                          item.id !== 0 ? item.id !== record.id : idx !== index
                        );
                      });
                      form.setFieldValue(
                        "floors",
                        items.length > 1 ? items.length - 1 : 0
                      );
                      Modal.destroyAll();
                    }}
                  >
                    Có
                  </CustomButton>
                  <CustomButton
                    onClick={() => {
                      Modal.destroyAll();
                    }}
                    className="cta-button"
                  >
                    Không
                  </CustomButton>
                </>
              ),
            });
          }}
        />
      ),
    },
  ];

  const handleCategoryItem = () => {
    if (!name || name.trim() === "") {
      message.error("Vui lòng nhập tên hạng mục trước khi thêm tầng!");
      return;
    }
    setItems((prev) => {
      const newItems = [
        ...prev,
        {
          id: 0,
          projectItemName: name || "",
          floor: "",
        },
      ];
      form.setFieldValue("floors", newItems.length);
      return newItems;
    });
  };

  // // Cập nhật items khi name hoặc floors thay đổi
  useEffect(() => {
    if (typeof floors === "number" && floors > 0) {
      // Nếu đã có items (ví dụ khi chỉnh sửa), giữ lại tên tầng đã nhập
      setItems((prev) => {
        const newItems = [];
        for (let i = 0; i < floors; i++) {
          newItems.push({
            id: prev[i]?.id || 0,
            projectItemName: name || "",
            floor: prev[i]?.floor || "",
            drawId: prev[i]?.drawId ?? 0,
          });
        }
        setOriginalItems(newItems);
        return newItems;
      });
    } else {
      setItems([]);
      setOriginalItems([]);
    }
  }, [name, floors]);

  // useEffect(() => {
  //   if (status === "update" && selectedProjectItem) {
  //     // Khi load dữ liệu từ server, set cả items và originalItems
  //     const floorOptions = projectItems.filter((f) =>
  //       (selectedProjectItem.floors || []).map((fl) => fl.id).includes(f.id)
  //     );
  //     const newItems = floorOptions.map((floor) => ({
  //       id: floor.id,
  //       projectItemName: selectedProjectItem.name || "",
  //       floorName: floor.name,
  //     }));
  //     setItems(newItems);
  //     setOriginalItems(newItems);
  //   }
  //   // Nếu là create, có thể để trống hoặc logic tương tự
  // }, [selectedProjectItem, projectItems, status]);

  useEffect(() => {
    if (
      status === "create" &&
      location.state &&
      typeof location.state === "object" &&
      "copyData" in location.state
    ) {
      const { id, code, floors, project, projectItemDetails, ...copyData } = (
        location.state as { copyData: any }
      ).copyData;

      let floorCount = 0;
      if (Array.isArray(floors)) {
        floorCount = floors.length;
      } else if (typeof floors === "number") {
        floorCount = floors;
      }

      let projectId = project;
      if (project && typeof project === "object") {
        projectId = project.id;
      }

      form.setFieldsValue({
        ...copyData,
        floors: floorCount,
        project: projectId,
      });

      // Gắn dữ liệu tầng vào bảng
      if (Array.isArray(projectItemDetails)) {
        setItems(
          projectItemDetails.map((detail) => ({
            id: detail.id,
            projectItemName: copyData.name || "",
            floor: detail.floor || "",
            drawId: detail.draw?.id ?? 0,
          }))
        );
        setOriginalItems(
          projectItemDetails.map((detail) => ({
            id: detail.id,
            projectItemName: copyData.name || "",
            floor: detail.floor || "",
            drawId: detail.draw?.id ?? 0,
          }))
        );
      }
    }
  }, [status, location.state, form]);

  return (
    <div className="app-container">
      <PageTitle
        back
        breadcrumbs={[
          { label: "Báo cáo" },
          {
            label: "Hạng mục",
            href: `/report/${PermissionNames.projectItemList}`,
          },
          { label: pageTitle },
        ]}
        title={pageTitle}
        extra={
          selectedProjectItem &&
          status == "update" && (
            <Space>
              <ActiveStatusTagSelect
                disabled={readonly}
                isActive={selectedProjectItem?.isActive}
                onChange={(value) => {
                  setSelectedProjectItem({
                    ...selectedProjectItem,
                    isActive: value,
                  } as ProjectItem);
                }}
              />
            </Space>
          )
        }
      />
      <Spin spinning={loadingFetch}>
        <Form
          form={form}
          layout="vertical"
          disabled={readonly}
          className={clsx(readonly ? "readonly" : "")}
          onFinish={handleSubmit}
          initialValues={{
            createdDate: dayjs(),
          }}
        >
          <Form.Item name="isActive" hidden />

          <Row gutter={24}>
            <Col span={24}>
              <Card className="content-card">
                <Row gutter={16}>
                  {/* First Row */}
                  <Col span={6}>
                    <Form.Item name="code" label="Mã hạng mục">
                      <Input
                        placeholder="Mã hạng mục"
                        disabled={status === "update"}
                      />
                    </Form.Item>
                  </Col>

                  <Col span={6}>
                    <Form.Item name="name" label="Tên hạng mục" rules={rules}>
                      <Input placeholder="Tên hạng mục" />
                    </Form.Item>
                  </Col>

                  <Col span={6}>
                    <Form.Item name="area" label="Diện tích (m²)">
                      <InputNumber
                        placeholder="Diện tích"
                        min={0}
                        onKeyDown={(e) => {
                          if (e.key === "-" || e.key === "+") {
                            e.preventDefault();
                          }
                        }}
                      />
                    </Form.Item>
                  </Col>

                  <Col span={6}>
                    <Form.Item name="floors" label="Số tầng">
                      <Input
                        type="number"
                        min={1}
                        placeholder="Số tầng"
                        onChange={(e) => {
                          if (!name || name.trim() === "") {
                            message.error(
                              "Vui lòng nhập tên hạng mục trước khi nhập số tầng!"
                            );
                            // Reset lại giá trị vừa nhập
                            form.setFieldValue("floors", null);
                            return;
                          }
                          const value = parseInt(e.target.value, 10) || 0;
                          form.setFieldValue("floors", value);
                        }}
                      />
                    </Form.Item>
                  </Col>

                  {/* <Col span={6}>
                    <Form.Item name="isActive" label="Trạng thái">
                      <Select placeholder="Trạng thái">
                        <Select.Option value={true}>Hoạt động</Select.Option>
                        <Select.Option value={false}>
                          Không hoạt động
                        </Select.Option>
                      </Select>
                    </Form.Item>
                  </Col> */}

                  <Col span={24}>
                    <Form.Item
                      name="note"
                      label="Ghi chú"
                      rules={descriptionRules}
                    >
                      <BMDCKEditor
                        placeholder="Ghi chú"
                        value={selectedProjectItem?.note}
                        disabled={readonly}
                        inputHeight={300}
                        onChange={(content) => {
                          form.setFieldsValue({ note: content });
                        }}
                      />
                    </Form.Item>
                  </Col>
                </Row>

                {/* Second Row */}
                <Row gutter={16}>
                  <Col span={24}>
                    <Tabs>
                      <Tabs.TabPane tab="Bản vẽ đính kèm" key="0">
                        <div>
                          <CustomizableTable
                            columns={itemColumns}
                            dataSource={items}
                            loading={loadingItems}
                            pagination={false}
                            rowKey="id"
                            scroll={{ x: 400 }}
                            size="small"
                            bordered
                          />
                        </div>
                      </Tabs.TabPane>
                    </Tabs>
                  </Col>
                  <Col span={12}>{/* Để trống */}</Col>
                </Row>

                {/* Action Buttons */}
                <Col span={24}>
                  <div
                    className="mt-[16px]"
                    style={{
                      display: "flex",
                      justifyContent: "flex-end",
                      gap: "12px",
                    }}
                  >
                    {!readonly && (
                      <CustomButton
                        variant="outline"
                        className="cta-button"
                        onClick={() => {
                          setItems(originalItems);
                          if (status == "create") {
                            navigate(
                              `/report/${PermissionNames.projectItemList}`
                            );
                          } else {
                            setDataToForm(selectedProjectItem!);
                            setReadonly(true);
                          }
                        }}
                      >
                        Hủy
                      </CustomButton>
                    )}
                    <CustomButton
                      className="cta-button"
                      loading={loading}
                      disabled={status == "update" && !haveEditPermission}
                      onClick={() => {
                        if (!readonly) {
                          handleSubmit();
                        } else {
                          setReadonly(false);
                        }
                      }}
                    >
                      {status == "create"
                        ? "Tạo hạng mục"
                        : readonly
                        ? "Chỉnh sửa"
                        : "Lưu chỉnh sửa"}
                    </CustomButton>
                  </div>
                </Col>
              </Card>
            </Col>
          </Row>
        </Form>
      </Spin>
    </div>
  );
}

export default observer(CreateOrUpdateProjectItemPage);
