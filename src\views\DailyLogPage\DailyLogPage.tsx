import { Calendar, momentLocalizer, Views } from "react-big-calendar";
import "react-big-calendar/lib/css/react-big-calendar.css";
import moment from "moment";
import { useEffect, useState } from "react";
import { useDailyLog } from "hooks/useDailyLog";
import { Card } from "antd";
import dayjs, { Dayjs } from "dayjs";
import CustomInput from "components/Input/CustomInput";
import CustomButton from "components/Button/CustomButton";
import { checkRoles } from "utils/auth";
import { PermissionNames } from "types/PermissionNames";
import { permissionStore } from "store/permissionStore";
import { getTitle } from "utils";
import PageTitle from "components/PageTitle/PageTitle";
import { observer } from "mobx-react";
import { useNavigate } from "react-router-dom";

const localizer = momentLocalizer(moment);

interface CalendarEvent {
  id: number;
  title: string;
  start: Date;
  end: Date;
  allDay: boolean;
  resource?: any;
  color?: string;
}

interface DailyLogPageProps {
  title: string;
  projectId?: number;
  hidePageTitle?: boolean;
}

function DailyLogPage({
  title,
  projectId,
  hidePageTitle = false,
}: DailyLogPageProps) {
  const { haveAddPermission } = checkRoles(
    {
      add: PermissionNames.activityLogAdd,
      edit: PermissionNames.activityLogEdit,
    },
    permissionStore.permissions
  );

  useEffect(() => {
    document.title = getTitle(title);
  }, [title]);

  // Filter states
  const [search, setSearch] = useState("");
  const [fromDate, setFromDate] = useState<Dayjs | null>(null);
  const [toDate, setToDate] = useState<Dayjs | null>(null);

  // Calendar events
  const [myEvents, setMyEvents] = useState<CalendarEvent[]>([]);

  // Hook lấy nhật ký
  const { dailyLogs, fetchData, loading } = useDailyLog({
    initQuery: {
      limit: 50,
      page: 1,
    },
  });

  // Fetch nhật ký khi mount
  useEffect(() => {
    fetchData({ limit: 50, page: 1 });
  }, []);

  // Chuyển nhật ký thành sự kiện cho lịch
  useEffect(() => {
    const transformedEvents = dailyLogs.map((log) => ({
      id: log.id,
      title: log.reportMemberShip?.name || "Chưa có người phụ trách",
      start: dayjs.unix(log.startAt).toDate(),
      end: dayjs.unix(log.endAt).endOf("day").toDate(),
      allDay: false,
      resource: log,
      color: "#0d6efd",
    }));
    setMyEvents(transformedEvents);
  }, [dailyLogs]);

  // Filter handler
  const handleApplyFilter = () => {
    const query: any = {
      limit: 50,
      page: 1,
    };
    if (search) query.reportMemberShipName = search;
    if (fromDate) query.fromDate = fromDate.format("YYYY-MM-DD");
    if (toDate) query.toDate = toDate.format("YYYY-MM-DD");
    fetchData(query);
  };

  const navigate = useNavigate();

  // Clear filter handler
  const handleClearFilter = () => {
    setSearch("");
    setFromDate(null);
    setToDate(null);
    fetchData({ limit: 50, page: 1 });
  };

  const handleCreateDailyLog = () => {
    navigate(`/progress-management/${PermissionNames.activityLogAdd}`);
  };

  return (
    <div>
      {!hidePageTitle && (
        <PageTitle
          title={title}
          breadcrumbs={["Tiến độ", title]}
          extra={
            haveAddPermission && (
              <CustomButton
                size="small"
                showPlusIcon
                onClick={handleCreateDailyLog}
              >
                Thêm nhật ký
              </CustomButton>
            )
          }
        />
      )}

      <div className={hidePageTitle ? "" : "app-container"}>
        <Card>
          <div className="flex flex-wrap gap-[16px] items-end pb-[12px]">
            <div className="w-[250px]">
              <CustomInput
                label="Người báo cáo"
                placeholder="Tìm theo tên người báo cáo"
                value={search}
                onChange={setSearch}
                onPressEnter={handleApplyFilter}
                allowClear
              />
            </div>
            {/* Nếu muốn lọc theo ngày, thêm input chọn ngày ở đây */}
            <CustomButton onClick={handleApplyFilter}>Áp dụng</CustomButton>
            {(search || fromDate || toDate) && (
              <CustomButton variant="outline" onClick={handleClearFilter}>
                Bỏ lọc
              </CustomButton>
            )}
          </div>
          <Calendar
            localizer={localizer}
            defaultDate={new Date()}
            defaultView={Views.MONTH}
            events={myEvents}
            eventPropGetter={(event: any) => ({
              style: {
                backgroundColor: event.color || "#3b82f6",
                borderRadius: "6px",
                color: "#fff",
                border: "none",
                padding: "4px 8px",
              },
            })}
            style={{ height: 1000 }}
            popup
            selectable
            onSelectEvent={(event) => {
              console.log("Selected event:", event); 

              navigate(
                `/progress-management/${PermissionNames.activityLogEdit.replace(
                  ":id",
                  event.id + ""
                )}`
              );
            }}
          />
        </Card>
      </div>
    </div>
  );
}

export default observer(DailyLogPage);
