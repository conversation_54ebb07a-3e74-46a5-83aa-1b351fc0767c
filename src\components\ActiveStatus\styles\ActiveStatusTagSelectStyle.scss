.active-status-tag-select {
  // disable border when focus
  .ant-select-focused {
    &.active {
      .ant-select-selector {
        border-color: var(--color-project-done) !important;
        background-color: color-mix(
          in srgb,
          var(--color-project-done) 20%,
          transparent
        );
        box-shadow: unset !important;
      }
    }

    &.inactive {
      .ant-select-selector {
        border-color: var(--color-task-slow) !important;
        background-color: color-mix(
          in srgb,
          var(--color-task-slow) 20%,
          transparent
        );

        box-shadow: unset !important;
      }
    }
  }

  .ant-select {
    height: 40px;
    min-width: 122px;

    .ant-select-selector {
      padding: 12px 16px;

      .ant-select-selection-item {
        font-weight: 700;
      }
    }

    &.active {
      .ant-select-selector {
        border: 2px solid var(--color-project-done) !important;
        background-color: color-mix(
          in srgb,
          var(--color-project-done) 10%,
          transparent
        );

        .ant-select-selection-item {
          color: var(--color-project-done);
        }
      }

      &:hover {
        .ant-select-selector {
          border-color: var(--color-project-done) !important;
          background-color: color-mix(
            in srgb,
            var(--color-project-done) 20%,
            transparent
          );
        }
      }
    }
    &.inactive {
      .ant-select-selector {
        border: 2px solid var(--color-task-slow) !important;
        background-color: color-mix(
          in srgb,
          var(--color-task-slow) 10%,
          transparent
        );

        .ant-select-selection-item {
          color: var(--color-task-slow);
        }
      }

      &:hover {
        .ant-select-selector {
          border-color: var(--color-task-slow) !important;
          background-color: color-mix(
            in srgb,
            var(--color-task-slow) 20%,
            transparent
          );
        }
      }
    }
  }
}
