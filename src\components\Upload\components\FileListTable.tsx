import { Button, Dropdown, message, Modal, Space, Tooltip } from "antd";
import DeleteIcon from "assets/svgs/DeleteIcon";
import CustomButton from "components/Button/CustomButton";
import CustomizableTable, {
  CustomizableColumn,
} from "components/Table/CustomizableTable";
import { FileAttach } from "types/fileAttach";
import FileIcon from "../RenderIconFile";
import { downloadFilesAsZip, formatFileSize } from "utils/file";
import { formatDateTime } from "utils/date";
import { ArrowDownIcon } from "assets/svgs/ArrowDownIcon";
import UploadFileIconImage from "assets/images/UploadFile.png";
import { UploadFileIcon } from "assets/svgs/UploadFileIcon";
import { UploadFileIcon16 } from "assets/svgs/UploadFileIcon16";
import { DownloadIcon } from "assets/svgs/DownloadIcon";
import { useEffect, useMemo, useState } from "react";
import { ColorThemes } from "utils/theme";
import { ReactComponent as SearchIcon } from "assets/svgs/search.svg";
import CustomInput from "components/Input/CustomInput";
import { TableProps } from "antd/lib";

interface Props {
  fileAttaches: FileAttach[];
  loading?: boolean;
  onDelete?: (file: FileAttach) => void;
  onUploadFromLocal?: () => void;
  showSearch?: boolean;
}

export const FileListTable = ({
  fileAttaches,
  loading,
  onDelete,
  onUploadFromLocal,
  showSearch = false,
}: Props) => {
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  const [loadingDownload, setLoadingDownload] = useState(false);
  const [searchText, setSearchText] = useState("");
  const [sortedData, setSortedData] = useState(fileAttaches);

  useEffect(() => {
    setSortedData([...fileAttaches]);
  }, [fileAttaches]);

  const dataWithSearch = useMemo(() => {
    return sortedData.filter((item) => {
      return item.name.toLowerCase().includes(searchText.toLowerCase());
    });
  }, [sortedData, searchText]);

  const columns: CustomizableColumn<FileAttach>[] = [
    {
      title: "Tên",
      dataIndex: "name",
      key: "name",
      sorter: true,
      align: "left",
      width: 300,
      render: (_, record) => {
        return (
          <Space style={{ gap: 4 }}>
            <FileIcon
              width={32}
              height={32}
              mimeType={record.mimetype}
              url={record.url}
            />
            <span
              style={{
                fontSize: 13,
                color: "var(--color-neutral-n8)",
              }}
            >
              {record.name}
            </span>
          </Space>
        );
      },
    },
    {
      title: "Dung lượng",
      dataIndex: "size",
      key: "size",
      sorter: true,
      align: "center",
      width: 120,
      render: (_, record) => {
        return <>{formatFileSize(record.size)}</>;
      },
    },
    {
      title: "Cập nhật lần cuối",
      dataIndex: "updatedAt",
      key: "updatedAt",
      sorter: true,
      align: "center",
      width: 150,
      render: (_, record) => {
        return <>{formatDateTime(record.updatedAt)}</>;
      },
    },
    {
      title: "Hành động",
      key: "actions",
      fixed: "right",
      width: 100,
      align: "center",
      alwaysVisible: true,
      render: (_, record) => (
        <Space>
          <Tooltip title={"Xóa"} mouseEnterDelay={0.3}>
            <Button
              type="text"
              danger={record.isActive}
              icon={<DeleteIcon />}
              onClick={() => {
                Modal.confirm({
                  title: `Xóa file`,
                  getContainer: () => {
                    return document.getElementById("App") as HTMLElement;
                  },
                  icon: null,
                  content: (
                    <>
                      <div>Bạn có chắc chắn muốn xóa file '{record.name}'?</div>
                    </>
                  ),
                  footer: (_, { OkBtn, CancelBtn }) => (
                    <>
                      <CustomButton
                        variant="outline"
                        className="cta-button"
                        onClick={() => {
                          Modal.destroyAll();
                          onDelete?.(record);
                        }}
                      >
                        Xóa
                      </CustomButton>
                      <CustomButton
                        onClick={() => {
                          Modal.destroyAll();
                        }}
                        className="cta-button"
                      >
                        Không
                      </CustomButton>
                    </>
                  ),
                });
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const rowSelection = {
    onChange: (selectedRowKeys: React.Key[], selectedRows: FileAttach[]) => {
      console.log(
        `selectedRowKeys: ${selectedRowKeys}`,
        "selectedRows: ",
        selectedRows
      );

      setSelectedKeys([...selectedRowKeys]);
    },
    getCheckboxProps: (record: FileAttach) => ({
      name: record.name,
    }),
  };

  const handleDownload = async () => {
    const selectedRows = fileAttaches.filter((item) =>
      selectedKeys.some((k) => k == item.uid)
    );
    console.log("handleDownload selectedRows:", selectedRows);

    setLoadingDownload(true);
    try {
      await downloadFilesAsZip(
        selectedRows.map((e) => ({
          name: e.name,
          url: e.url,
        })),
        "tep-dinh-kem.zip"
      );
    } finally {
      setLoadingDownload(false);
    }
  };

  const handleTableChange: TableProps<any>["onChange"] = (
    pagination,
    filters,
    sorter: any
  ) => {
    if (!sorter || !sorter.order) {
      setSortedData(fileAttaches); // Không sort => reset lại data gốc
      return;
    }

    const sorted = [...fileAttaches].sort((a, b) => {
      const field = sorter.field as keyof FileAttach;
      const order = sorter.order === "ascend" ? 1 : -1;

      let valueA = a[field];
      let valueB = b[field];

      if (valueA! > valueB!) return order;
      if (valueA! < valueB!) return -order;
      return 0;
    });

    setSortedData(sorted);
  };

  return (
    <>
      <div className="flex justify-between" style={{ marginBottom: 16 }}>
        <Space>
          <CustomButton
            loading={loadingDownload}
            icon={
              <DownloadIcon
                fill={selectedKeys.length == 0 ? undefined : "#fff"}
              />
            }
            disabled={selectedKeys.length == 0}
            onClick={() => {
              handleDownload();
            }}
          >
            Tải tệp
          </CustomButton>

          <Dropdown
            menu={{
              items: [
                {
                  key: "1",
                  label: "Tệp có sẵn",
                },
                {
                  key: "2",
                  label: "Từ máy tính",
                },
              ],
              onClick(info) {
                if (info.key == "1") {
                  message.warning("Tính năng đang phát triển");
                } else {
                  onUploadFromLocal?.();
                }
              },
            }}
            trigger={["click"]}
          >
            <CustomButton icon={<UploadFileIcon16 />} variant="outline">
              <span>Thêm tệp</span>

              <ArrowDownIcon />
            </CustomButton>
          </Dropdown>
        </Space>

        {showSearch && (
          <div>
            <CustomInput
              placeholder="Tìm kiếm theo tên"
              value={searchText}
              onChange={setSearchText}
              allowClear
              suffix={<SearchIcon />}
            />
          </div>
        )}
      </div>

      <CustomizableTable
        columns={columns}
        rowSelection={{
          type: "checkbox",
          ...rowSelection,
        }}
        dataSource={dataWithSearch}
        rowKey="uid"
        loading={loading}
        pagination={false}
        scroll={{ x: "max-content" }}
        bordered
        displayOptions={false}
        tableId="file-list-table"
        onChange={handleTableChange}
      />
    </>
  );
};
