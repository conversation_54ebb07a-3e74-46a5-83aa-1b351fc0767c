import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const rfiApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/rfi",
      params,
    }),

  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/rfi",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/rfi/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/rfi/${id}`,
      method: "delete",
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/rfi/${id}`,
    }),
  approve: (id: number, data?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/rfi/${id}/approve`,
      method: "patch",
      data,
    }),

  reject: (id: number, data?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/rfi/${id}/reject`,
      method: "patch",
      data,
    }),
  complete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/rfi/${id}/complete`,
      method: "patch",
    }),
};
