import { request } from "utils/request";
import { AxiosPromise } from "axios";

export const patternApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/pattern",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/pattern",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/pattern/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/pattern/${id}`,
      method: "delete",
    }),
};
