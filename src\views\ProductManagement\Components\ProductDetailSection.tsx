import {
  Button,
  Checkbox,
  Col,
  Form,
  Input,
  InputNumber,
  Popconfirm,
  Radio,
  Row,
  Select,
  Table,
} from "antd";
import Column from "antd/es/table/Column";
import React, { useRef, useState } from "react";
import { IoAddCircleOutline } from "react-icons/io5";
import {
  ProductDetail,
  ProductDetailType,
  ProductDetailTypeTrans,
  ProductTypeTrans,
} from "types/product";
import { requiredRule } from "utils/validateRule";
import { ComponentsSelector } from "components/Selector/ComponentsSelector";
import CreateProductDetailModal, {
  ProductDetailModal,
} from "./CreateProductDetailModal";
interface ProductDetailSectionProps {
  handleSubmitOk: (newItem: ProductDetail) => void;
  productDetails: ProductDetail[];
}
const ProductDetailSection = ({
  handleSubmitOk,
  productDetails,
}: ProductDetailSectionProps) => {
  const [form] = Form.useForm<ProductDetail>();
  // const [productDetails, setProductDetails] = useState<ProductDetail[]>([]);
  const dataSource = [form.getFieldsValue()]; // single row form data for table display
  const modalRef = React.useRef<ProductDetailModal>(null);
  // const handleSubmitOk = (newItem: ProductDetail) => {
  //   setProductDetails((prev) => [...prev, newItem]);
  // };
  console.log("What is product details", productDetails);
  return (
    <>
      <Form layout="vertical" form={form}>
        <Row gutter={16}>
          <Col span={24}>
            <div className="flex items-center gap-2">
              <h2>Chi tiết sản phẩm</h2>
              <span
                className="flex items-center cursor-pointer hover:text-green-500"
                onClick={() => {
                  modalRef.current?.handleCreate();
                }}
              >
                <IoAddCircleOutline className="text-[25px]" />
              </span>
            </div>
          </Col>
          <Col span={24}>
            <Table
              pagination={false}
              dataSource={productDetails || []}
              rowKey={() => "product-detail-form"}
              scroll={{ x: "max-content" }}
            >
              <Column
                title="Tên"
                key="name"
                dataIndex="name"
                render={(text) => <div>{text}</div>}
              />
              <Column
                title="Tên nhóm"
                key="groupName"
                dataIndex="groupName"
                render={(text) => <div>{text}</div>}
              />
              <Column
                title="Lớp"
                key="layer"
                dataIndex="layer"
                render={(text) => <div>{text}</div>}
              />
              <Column
                title="Loại"
                key="type"
                dataIndex="type"
                render={(text: ProductDetailType) => (
                  <div>{ProductDetailTypeTrans[text]?.label}</div>
                )}
              />
              {/* <Column
                title="Mặt trước"
                key="isFront"
                render={(text) => (
                  <div>
                    <Checkbox checked={text == true} />
                  </div>
                )}
              />
              <Column
                title="Mặt sau"
                key="isBack"
                render={(text) => (
                  <div>
                    <Checkbox checked={text == true} />
                  </div>
                )}
              /> */}
              <Column
                title="Thành phần"
                key="componentIds"
                dataIndex="componentIds"
                render={() => (
                  <Form.Item name="componentIds" noStyle>
                    <ComponentsSelector multiple />
                  </Form.Item>
                )}
              />
              <Column
                fixed="right"
                width={120}
                title="Thao tác"
                key="action"
                render={(text, record: ProductDetail) => (
                  <div className="flex gap-2">
                    <Popconfirm
                      // onConfirm={() => {
                      //   console.log("What is in xóa", record.material.id);
                      //   console.log("What is in xóa", billOfMaterial);
                      //   setBillOfMaterial((prev) =>
                      //     prev.filter(
                      //       (item) =>
                      //         item.material.id !== record.material.id
                      //     )
                      //   );
                      // }}
                      // onConfirm={() => {
                      //   const currentBoms = form.getFieldValue("boms") || [];
                      //   const newBoms = currentBoms.filter(
                      //     (item: BillOfMaterialUpdate) =>
                      //       item.material.id !== record.material.id
                      //   );
                      //   form.setFieldValue("boms", newBoms);
                      // }}
                      title="Xác nhận xóa"
                    >
                      <Button>Xóa</Button>
                    </Popconfirm>
                    <Button
                      type="primary"
                      onClick={() => {
                        modalRef.current?.handleUpdate(record);
                      }}
                    >
                      Cập nhật
                    </Button>
                  </div>
                )}
              />
            </Table>
          </Col>
        </Row>
      </Form>
      <CreateProductDetailModal
        ref={modalRef}
        onClose={() => {}}
        onSubmitOk={handleSubmitOk}
      />
    </>
  );
};

export default ProductDetailSection;
